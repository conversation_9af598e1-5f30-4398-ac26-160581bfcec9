<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>明细成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 明细成绩页面样式 */
        .detail-header {
            background: linear-gradient(135deg, var(--info-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .detail-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .detail-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .course-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .course-name {
            font-size: var(--font-size-large);
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
            margin-bottom: var(--margin-sm);
        }
        
        .course-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            text-align: center;
        }
        
        .detail-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-header i {
            color: var(--info-color);
        }
        
        .detail-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            transition: all var(--transition-base);
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-item:hover {
            background: var(--bg-tertiary);
        }
        
        .detail-header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .detail-category {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .detail-index {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-score {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            height: 30px;
            border-radius: 15px;
            font-size: var(--font-size-base);
            font-weight: 500;
            flex-shrink: 0;
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .detail-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .meta-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .meta-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .score-breakdown {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            padding: var(--padding-md);
            text-align: center;
        }
        
        .load-more-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md) var(--padding-lg);
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .load-more-button:hover {
            background: var(--primary-dark);
        }
        
        .load-more-button:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .detail-header-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .detail-meta {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <select id="bfcj_complete" style="display: none;">
        <cache:query var="codeXfjbList" region="CODE_XFJB" />
        <c:forEach items="${codeXfjbList}" var="obj">
            <option value="${obj.djcj}" xxf="${obj.xxf}" sxf="${obj.sxf}" djcj="${obj.djcj}" jds="${obj.jds}">${obj.djcj}</option>
        </c:forEach>
    </select>
    <select id="djcj_complete" style="display: none;">
        <cache:query var="codeCjdjList" region="CODE_CJDJ" />
        <c:forEach items="${codeCjdjList}" var="codeCjdj">
            <option value="${codeCjdj.djcj}">${codeCjdj.djm}</option>
        </c:forEach>
    </select>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    <input type="hidden" id="schoolId" name="schoolId" value="${schoolId}">
    <input type="hidden" id="mxcjId" name="mxcjId" value="${mxcjId}">
    <input type="hidden" id="rounding" name="rounding" value="${rounding}">
    <input type="hidden" id="ejcjCount" name="ejcjCount" value="${ejcjCount}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="window.history.go(-1);">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">明细成绩</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 明细成绩头部 -->
        <div class="detail-header">
            <div class="detail-title">明细成绩详情</div>
            <div class="detail-desc">查看课程各项成绩明细</div>
        </div>
        
        <!-- 课程信息 -->
        <div class="course-info">
            <div class="course-name">${fn:replace(fn:replace(kcm, '（', ''), '）', '')}</div>
            <div class="course-details">课程明细成绩详细信息</div>
        </div>
        
        <!-- 明细成绩列表 -->
        <div class="detail-list" id="detailList" style="display: none;">
            <div class="list-header">
                <i class="ace-icon fa fa-list"></i>
                明细成绩列表
            </div>
            
            <div id="detailItems">
                <!-- 动态加载明细成绩项 -->
            </div>
            
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <button class="load-more-button" id="loadMoreButton" onclick="loadMore();">
                    加载更多
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div class="empty-state-text">暂无明细成绩</div>
            <div class="empty-state-desc">该课程暂无明细成绩信息</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let isLoading = false;

        $(function() {
            initPage();
            search();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 查询明细成绩
        function search() {
            currentPage = 1;
            getResultsList(currentPage, true);
        }

        // 加载更多
        function loadMore() {
            if (isLoading) return;
            currentPage++;
            getResultsList(currentPage, false);
        }

        // 获取结果列表
        function getResultsList(page, isNewSearch) {
            if (isLoading) return;

            isLoading = true;
            showLoading(true);

            const mxcjId = $('#mxcjId').val();

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/subitemScore/searchMxcj",
                cache: false,
                type: "post",
                data: "mxcjId=" + mxcjId + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    totalCount = data["pageContext"].totalCount;

                    if (data["records"] != null) {
                        fillResultsTable(data["records"], !isNewSearch, page);
                    } else {
                        fillResultsTable(null, !isNewSearch, page);
                    }

                    updatePagination();
                },
                error: function(xhr) {
                    showError("获取数据失败，请重试");
                },
                complete: function() {
                    isLoading = false;
                    showLoading(false);
                }
            });
        }

        // 填充结果表格
        function fillResultsTable(data, isAppend, page) {
            let html = '';

            if (data != null && data.length > 0) {
                data.forEach(function(item, index) {
                    const tableId = isAppend ? (page - 1) * pageSize + 1 + index : index + 1;
                    html += createDetailItem(item, tableId);
                });

                if (isAppend) {
                    $('#detailItems').append(html);
                } else {
                    $('#detailItems').html(html);
                }

                $('#detailList').show();
                $('#emptyState').hide();
            } else {
                if (!isAppend) {
                    $('#detailItems').html('');
                    $('#detailList').hide();
                    $('#emptyState').show();
                }
            }
        }

        // 创建明细成绩项HTML
        function createDetailItem(item, index) {
            const schoolId = $('#schoolId').val();
            const rounding = $('#rounding').val();
            const ejcjCount = $('#ejcjCount').val();

            // 计算总成绩显示
            let totalScore = '';
            if (schoolId == "100010") {
                if (item.KCH == '58000001' || item.KCH == '58000002' ||
                    item.KCH == '58000003' || item.KCH == '58000004' || item.KCH == '58000005') {
                    totalScore = item.ZCJ == null ? "" : (rounding == "1" ? Math.round(item.ZCJ) : item.ZCJ);
                } else {
                    if (ejcjCount > 0) {
                        totalScore = item.ZCJ == null ? "" : (item.ZCJ > 19 ? "P" : "N");
                    } else {
                        const djcj = ZSCJToDJCJConvert(item.ZCJ == null ? "" : (rounding == "1" ? Math.round(item.ZCJ) : item.ZCJ));
                        totalScore = DJCJToDJMConvert(djcj);
                    }
                }
            } else {
                totalScore = item.ZCJ == null ? "" : (rounding == "1" ? Math.round(item.ZCJ) : item.ZCJ);
            }

            return `
                <div class="detail-item">
                    <div class="detail-header-row">
                        <div class="detail-info">
                            <div class="detail-category">${item.CJFL || ''}</div>
                            <div class="detail-index"># ${index}</div>
                        </div>
                        <div class="detail-score">
                            ${totalScore}
                        </div>
                    </div>

                    <div class="detail-meta">
                        <div class="meta-item">
                            <div class="meta-label">平时成绩</div>
                            <div class="meta-value">${item.BSCJ || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">期中成绩</div>
                            <div class="meta-value">${item.QZCJ || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">期末成绩</div>
                            <div class="meta-value">${item.QMQCJ || ''}</div>
                        </div>
                    </div>

                    <div class="score-breakdown">
                        平时：${item.BSCJ || ''} &nbsp;&nbsp; 期中：${item.QZCJ || ''} &nbsp;&nbsp; 期末：${item.QMQCJ || ''} &nbsp;&nbsp; 总成绩：${totalScore}
                    </div>
                </div>
            `;
        }

        // 真实成绩转等级成绩
        function ZSCJToDJCJConvert(zscj) {
            let djcj = "";
            const options = $("#bfcj_complete option");
            options.each(function(index, ele) {
                const $option = $(ele);
                const DJCJ = $option.val();
                const XXF = $option.attr("xxf");
                const SXF = $option.attr("sxf");
                if ((parseFloat(zscj) >= parseFloat(XXF)) && (parseFloat(zscj) < parseFloat(SXF))) {
                    djcj = DJCJ;
                }
            });
            return djcj;
        }

        // 根据等级成绩获取等级名
        function DJCJToDJMConvert(djcj) {
            let djm = "";
            const options = $("#djcj_complete option");
            options.each(function(index, ele) {
                const $option = $(ele);
                const DJCJ = $option.val();
                const DJM = $option.text();
                if (DJCJ == djcj) {
                    djm = DJM;
                }
            });
            return djm;
        }

        // 更新分页
        function updatePagination() {
            const hasMore = currentPage * pageSize < totalCount;

            if (hasMore) {
                $('#paginationContainer').show();
                $('#loadMoreButton').prop('disabled', false);
            } else {
                $('#paginationContainer').hide();
            }
        }

        // 刷新数据
        function refreshData() {
            search();
        }

        // 返回上一页
        function returnIndex() {
            window.location.href = "/student/integratedQuery/scoreQuery/subitemScore/index";
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight() || 0;
            const availableHeight = windowHeight - navbarHeight;

            $('.page-mobile').css('min-height', availableHeight + 'px');
        }
    </script>
</body>
</html>
