<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>转专业拟录取公示</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 转学生名单公告页面样式 */
        .announcement-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .announcement-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .announcement-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            background: var(--info-light);
            color: var(--info-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--info-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-section i {
            color: var(--info-color);
            margin-right: 8px;
        }
        
        .tabs-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            background: var(--bg-tertiary);
            padding: 0;
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
            white-space: nowrap;
        }
        
        .tabs-nav {
            display: flex;
            min-width: max-content;
        }
        
        .tab-item {
            padding: var(--padding-md);
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-base);
            white-space: nowrap;
            text-align: center;
            line-height: 1.3;
        }
        
        .tab-item.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-primary);
        }
        
        .tab-content {
            padding: var(--padding-md);
            min-height: 200px;
        }
        
        .students-section {
            margin-bottom: var(--margin-md);
        }
        
        .students-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .students-title i {
            color: var(--success-color);
        }
        
        .student-item {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--primary-color);
        }
        
        .student-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .student-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .student-basic {
            flex: 1;
        }
        
        .student-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .student-number {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .student-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 80px;
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            color: var(--text-primary);
        }
        
        .transfer-info {
            background: var(--success-light);
            color: var(--success-dark);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .announcement-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .tabs-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .student-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
            
            .student-basic {
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="clickedBatch"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">转专业拟录取公示</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 公告头部 -->
        <div class="announcement-header">
            <div class="announcement-title">转专业拟录取公示</div>
            <div class="announcement-desc">查看转专业拟录取学生名单</div>
        </div>
        
        <!-- 消息提示 -->
        <c:if test="${not empty msg}">
            <div class="notice-section">
                <i class="ace-icon fa fa-info-circle"></i>
                ${msg}
            </div>
        </c:if>
        
        <c:if test="${empty msg}">
            <!-- 选项卡内容 -->
            <div class="tabs-section">
                <div class="tabs-header">
                    <div class="tabs-nav" id="tabsNav">
                        <c:forEach items="${list}" var="l" varStatus="status">
                            <button class="tab-item ${status.index == 0 ? 'active' : ''}" 
                                    data-batch="${l[0]}" 
                                    onclick="switchBatch('${l[0]}', this);">
                                ${l[2]}<br>${l[1]}
                            </button>
                        </c:forEach>
                    </div>
                </div>
                
                <div class="tab-content">
                    <div class="students-section">
                        <div class="students-title">
                            <i class="ace-icon fa fa-users"></i>
                            拟录取学生名单
                        </div>
                        
                        <div id="studentsList">
                            <!-- 动态加载学生列表 -->
                        </div>
                        
                        <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                            <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreStudents();">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>加载更多</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-users"></i>
            <div>暂无学生数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let studentsData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let currentBatch = '';

        $(function() {
            initPage();
            initFirstTab();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化第一个选项卡
        function initFirstTab() {
            const firstTab = $('.tab-item').first();
            if (firstTab.length > 0) {
                const batchId = firstTab.data('batch');
                switchBatch(batchId, firstTab[0]);
            }
        }

        // 切换批次
        function switchBatch(batchId, element) {
            if (currentBatch === batchId) return;

            // 更新选项卡状态
            $('.tab-item').removeClass('active');
            $(element).addClass('active');

            // 设置当前批次
            currentBatch = batchId;
            $('#clickedBatch').val(batchId);

            // 重新加载数据
            loadStudents(1, true);
        }

        // 加载更多学生
        function loadMoreStudents() {
            if (hasMore) {
                loadStudents(currentPage + 1, false);
            }
        }

        // 加载学生数据
        function loadStudents(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/viewingTheListOfTransferStudentsAnnouncement/queryStudentList",
                type: "post",
                data: "zzypch=" + currentBatch + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    if (response && response.records && response.records.length > 0) {
                        if (reset) {
                            studentsData = response.records;
                        } else {
                            studentsData = studentsData.concat(response.records);
                        }

                        totalCount = response.pageContext.totalCount;
                        currentPage = page;
                        hasMore = studentsData.length < totalCount;

                        renderStudentsList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            studentsData = [];
                            renderStudentsList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染学生列表
        function renderStudentsList(reset = false) {
            const container = $('#studentsList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : studentsData.length - pageSize;
            const endIndex = studentsData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (studentsData[i]) {
                    const itemHtml = createStudentItem(studentsData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建学生项目HTML
        function createStudentItem(student, index) {
            return `
                <div class="student-item">
                    <div class="student-header">
                        <div class="student-basic">
                            <div class="student-index">${index + 1}</div>
                            <div>
                                <div class="student-name">${student.XM || ''}</div>
                                <div class="student-number">学号：${student.XH || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="student-details">
                        <div class="detail-row">
                            <span class="detail-label">原院系</span>
                            <span class="detail-value">${student.SSYX || ''}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">原专业</span>
                            <span class="detail-value">${student.SSZY || ''}</span>
                        </div>
                    </div>

                    <div class="transfer-info">
                        拟转入专业：${student.ZRZY || ''}
                    </div>
                </div>
            `;
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && studentsData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (studentsData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.tabs-section').hide();
            } else {
                $('#emptyState').hide();
                $('.tabs-section').show();
            }
        }

        // 刷新数据
        function refreshData() {
            if (currentBatch) {
                loadStudents(1, true);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
