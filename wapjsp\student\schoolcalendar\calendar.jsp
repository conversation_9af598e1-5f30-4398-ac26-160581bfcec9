<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学校校历</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 校历页面样式 */
        .calendar-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .calendar-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .calendar-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .calendar-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .calendar-year {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            text-align: center;
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--font-size-small);
        }
        
        .calendar-table th {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            padding: var(--padding-sm);
            text-align: center;
            font-weight: 500;
            border: 1px solid var(--divider-color);
        }
        
        .calendar-table td {
            padding: var(--padding-sm);
            text-align: center;
            border: 1px solid var(--divider-color);
            vertical-align: top;
            position: relative;
            min-height: 40px;
        }
        
        .month-cell {
            background: var(--info-light);
            color: var(--info-dark);
            font-weight: 500;
        }
        
        .week-cell {
            background: var(--warning-light);
            color: var(--warning-dark);
            font-weight: 500;
        }
        
        .day-cell {
            background: var(--bg-primary);
            position: relative;
        }
        
        .day-number {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .weekend {
            color: var(--error-color) !important;
        }
        
        .today {
            background: var(--warning-light) !important;
        }
        
        .event-item {
            background: var(--primary-color);
            color: white;
            padding: 2px 4px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            margin: 1px 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
        }
        
        .event-item.holiday {
            background: var(--error-color);
        }
        
        .event-item.exam {
            background: var(--warning-color);
        }
        
        .event-item.activity {
            background: var(--success-color);
        }
        
        .event-item.vacation {
            background: var(--info-color);
        }
        
        .legend-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .legend-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-title i {
            color: var(--primary-color);
        }
        
        .legend-items {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            flex-shrink: 0;
        }
        
        .legend-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .event-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            max-width: 300px;
            width: 100%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .modal-close {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--text-disabled);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .modal-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        @media (max-width: 480px) {
            .calendar-table {
                font-size: var(--font-size-mini);
            }
            
            .calendar-table th,
            .calendar-table td {
                padding: 4px;
            }
            
            .legend-items {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学校校历</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 校历头部 -->
        <div class="calendar-header">
            <div class="calendar-title">学校校历</div>
            <div class="calendar-desc">查看学年学期安排</div>
        </div>
        
        <!-- 校历容器 -->
        <div class="calendar-container">
            <div class="calendar-year" id="calendarYear">
                <!-- 动态填充学年信息 -->
            </div>
            
            <table class="calendar-table" id="calendarTable">
                <thead>
                    <tr>
                        <th style="width: 60px;">月份</th>
                        <th style="width: 60px;">周次</th>
                        <c:if test="${firstday eq '7'}">
                            <th class="weekend">周日</th>
                        </c:if>
                        <th>周一</th>
                        <th>周二</th>
                        <th>周三</th>
                        <th>周四</th>
                        <th>周五</th>
                        <th class="weekend">周六</th>
                        <c:if test="${firstday eq '1'}">
                            <th class="weekend">周日</th>
                        </c:if>
                    </tr>
                </thead>
                <tbody id="calendarBody">
                    <!-- 动态填充日历内容 -->
                </tbody>
            </table>
        </div>
        
        <!-- 图例说明 -->
        <div class="legend-container">
            <div class="legend-title">
                <i class="ace-icon fa fa-info-circle"></i>
                图例说明
            </div>
            
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--primary-color);"></div>
                    <div class="legend-text">教学活动</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--error-color);"></div>
                    <div class="legend-text">节假日</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--warning-color);"></div>
                    <div class="legend-text">考试安排</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--success-color);"></div>
                    <div class="legend-text">校园活动</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--info-color);"></div>
                    <div class="legend-text">假期安排</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: var(--warning-light);"></div>
                    <div class="legend-text">今天</div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>
    
    <!-- 事件详情模态框 -->
    <div class="event-modal" id="eventModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">事件详情</div>
                <button class="modal-close" onclick="closeModal();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body" id="modalBody">
                <!-- 动态填充内容 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let calendarData = [];
        let currentYear = '';
        let currentSemester = '';
        let weekFirst = '${firstday}' || '1';
        
        // 颜色配置
        const eventColors = {
            'teaching': 'var(--primary-color)',
            'holiday': 'var(--error-color)',
            'exam': 'var(--warning-color)',
            'activity': 'var(--success-color)',
            'vacation': 'var(--info-color)'
        };

        $(function() {
            initPage();
            loadCalendarData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 设置学年信息
            const xnxq = "${xn}";
            const xqm = "${xqm}";
            $('#calendarYear').html(`<h3>${xnxq}学年(${xqm})</h3>`);
        }

        // 加载校历数据
        function loadCalendarData() {
            showLoading(true);

            try {
                // 获取校历数据
                const cal = '${cal}';
                calendarData = cal ? eval(cal) : [];

                // 获取基本信息
                const kxrq = "${kxrq}"; // 开学日期 20160606
                const skzc = "${skzc}"; // 上课周数

                if (kxrq && skzc) {
                    buildCalendar(kxrq, skzc);
                } else {
                    showError('校历数据加载失败');
                }
            } catch (error) {
                console.error('校历数据解析失败:', error);
                showError('校历数据解析失败');
            } finally {
                showLoading(false);
            }
        }

        // 构建校历
        function buildCalendar(startDate, weekCount) {
            // 解析开学日期
            const year = parseInt(startDate.substring(0, 4));
            const month = parseInt(startDate.substring(4, 6));
            const day = parseInt(startDate.substring(6, 8));

            // 计算结束日期
            const endDate = addDate(new Date(year, month - 1, day), weekCount * 7 - 1);
            const endYear = parseInt(endDate.substring(0, 4));
            const endMonth = parseInt(endDate.substring(5, 7));
            const endDay = parseInt(endDate.substring(8, 10));

            // 计算总月数
            const totalMonths = (endYear - year) * 12 + endMonth;

            // 构建表格
            buildTable(year, month, day, totalMonths, endDay);

            // 填充事件
            fillEvents();
        }

        // 构建表格结构
        function buildTable(year, month, day, monthNum, endDay) {
            let yearNum = year;
            let weekNum = 0;

            // 计算总周数
            for (let i = month; i <= monthNum; i++) {
                let mon = i > 12 ? i % 12 : i;
                if (i > 12) yearNum = year + 1;

                const daysPerMonth = getMonthDays(yearNum);

                if (i === month) {
                    const firstWeekDay = getWeekDay(yearNum, mon - 1, day);
                    weekNum += Math.ceil((daysPerMonth[mon - 1] - day + firstWeekDay) / 7);
                } else if (i === monthNum) {
                    const firstWeekDay = getWeekDay(yearNum, mon - 1, 1);
                    weekNum += Math.ceil((endDay + firstWeekDay) / 7);
                } else {
                    const firstWeekDay = getWeekDay(yearNum, mon - 1, 1);
                    weekNum += Math.ceil((daysPerMonth[mon - 1] + firstWeekDay) / 7);
                }
            }

            // 生成表格HTML
            let tableHtml = '';
            for (let i = 1; i <= weekNum; i++) {
                tableHtml += '<tr>';
                tableHtml += `<td id="${i}_m" class="month-cell"></td>`;
                tableHtml += `<td id="${i}_w" class="week-cell"></td>`;

                if (weekFirst === '7') {
                    tableHtml += `<td id="${i}-7" class="day-cell weekend"></td>`;
                }

                for (let j = 1; j <= (weekFirst === '7' ? 6 : 7); j++) {
                    const dayClass = j === 6 || j === 7 ? 'day-cell weekend' : 'day-cell';
                    tableHtml += `<td id="${i}-${j}" class="${dayClass}"></td>`;
                }

                if (weekFirst === '1') {
                    tableHtml += `<td id="${i}-7" class="day-cell weekend"></td>`;
                }

                tableHtml += '</tr>';
            }

            $('#calendarBody').html(tableHtml);

            // 填充日期
            fillDates(year, month, day, monthNum, endDay, weekNum);
        }

        // 填充日期
        function fillDates(year, month, day, monthNum, endDay, weekNum) {
            let weekIndex = 1;
            let weekNumber = 1;
            let monthStart = {};

            for (let i = month; i <= monthNum; i++) {
                let currentYear = i > 12 ? year + 1 : year;
                let currentMonth = i > 12 ? i % 12 : i;
                if (currentMonth === 0) currentMonth = 12;

                const daysPerMonth = getMonthDays(currentYear);
                const startDay = i === month ? day : 1;
                const endDayOfMonth = i === monthNum ? endDay : daysPerMonth[currentMonth - 1];

                // 记录月份开始位置
                if (!monthStart[currentMonth]) {
                    monthStart[currentMonth] = weekIndex;
                }

                for (let j = startDay; j <= endDayOfMonth; j++) {
                    const date = new Date(currentYear, currentMonth - 1, j);
                    const dayOfWeek = date.getDay();
                    const adjustedDayOfWeek = weekFirst === '7' ? dayOfWeek : (dayOfWeek === 0 ? 7 : dayOfWeek);
                    const dateString = formatDate(date);

                    // 填充日期
                    const cellId = `${weekIndex}-${adjustedDayOfWeek}`;
                    const dayHtml = `<div class="day-number" id="${dateString}">${j}</div>`;
                    $(`#${cellId}`).html(dayHtml);

                    // 标记今天
                    if (isToday(date)) {
                        $(`#${cellId}`).addClass('today');
                    }

                    // 填充周次
                    const endWeek = weekFirst === '1' ? 7 : 6;
                    if (adjustedDayOfWeek === endWeek) {
                        $(`#${weekIndex}_w`).html(`第${weekNumber}教学周`);
                        weekNumber++;
                    }

                    // 换行
                    if (adjustedDayOfWeek === endWeek || j === daysPerMonth[currentMonth - 1]) {
                        weekIndex++;
                    }
                }

                // 填充月份
                const monthRowSpan = weekIndex - monthStart[currentMonth];
                if (monthRowSpan > 0) {
                    $(`#${monthStart[currentMonth]}_m`).html(currentMonth).attr('rowspan', monthRowSpan);
                    for (let k = monthStart[currentMonth] + 1; k < weekIndex; k++) {
                        $(`#${k}_m`).remove();
                    }
                }
            }
        }

        // 填充事件
        function fillEvents() {
            if (!calendarData || calendarData.length === 0) return;

            calendarData.forEach((event, index) => {
                const startDate = event.ksrq;
                const endDate = event.jsrq;
                const content = event.nr;
                const colorIndex = index % 7;
                const color = getEventColor(content);

                // 填充开始日期
                addEventToDate(startDate, content, color);

                // 填充期间的所有日期
                if (startDate !== endDate) {
                    const daysDiff = getDaysDiff(startDate, endDate);
                    for (let i = 1; i <= daysDiff; i++) {
                        const currentDate = addDays(startDate, i);
                        addEventToDate(currentDate, content, color);
                    }
                }
            });
        }

        // 添加事件到指定日期
        function addEventToDate(dateString, content, color) {
            const dateElement = $(`#${dateString}`);
            if (dateElement.length > 0) {
                const eventHtml = `<div class="event-item" style="background-color: ${color};" onclick="showEventDetail('${content}')" title="${content}">${content}</div>`;
                dateElement.after(eventHtml);
            }
        }

        // 获取事件颜色
        function getEventColor(content) {
            if (content.includes('假') || content.includes('节')) return eventColors.holiday;
            if (content.includes('考试') || content.includes('考核')) return eventColors.exam;
            if (content.includes('活动') || content.includes('比赛')) return eventColors.activity;
            if (content.includes('放假') || content.includes('休息')) return eventColors.vacation;
            return eventColors.teaching;
        }

        // 显示事件详情
        function showEventDetail(content) {
            $('#modalTitle').text('事件详情');
            $('#modalBody').text(content);
            $('#eventModal').show();
        }

        // 关闭模态框
        function closeModal() {
            $('#eventModal').hide();
        }

        // 工具函数
        function getMonthDays(year) {
            if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) {
                return [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            } else {
                return [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            }
        }

        function getWeekDay(year, month, day) {
            const week = new Date(year, month, day).getDay();
            if (weekFirst === '7') {
                return week;
            } else {
                return week === 0 ? 7 : week - 1;
            }
        }

        function addDate(date, days = 1) {
            const newDate = new Date(date);
            newDate.setDate(newDate.getDate() + days);
            const month = newDate.getMonth() + 1;
            const day = newDate.getDate();
            return `${newDate.getFullYear()}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        }

        function addDays(dateString, days) {
            const date = new Date(dateString.replace(/-/g, '/'));
            return addDate(date, days);
        }

        function formatDate(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        function isToday(date) {
            const today = new Date();
            return date.toDateString() === today.toDateString();
        }

        function getDaysDiff(startDate, endDate) {
            const start = new Date(startDate.replace(/-/g, '/'));
            const end = new Date(endDate.replace(/-/g, '/'));
            return Math.abs(Math.ceil((end - start) / (1000 * 60 * 60 * 24)));
        }

        // 刷新数据
        function refreshData() {
            loadCalendarData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            alert(message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 点击模态框背景关闭
        $('#eventModal').on('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
