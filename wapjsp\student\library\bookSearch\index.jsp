<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>图书查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 图书查询页面样式 */
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-input-group {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .search-input {
            flex: 1;
            min-height: 44px;
            padding: 12px 16px;
            border: 1px solid var(--border-primary);
            border-radius: 22px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .btn-search {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 22px;
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .search-filters {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
        }
        
        .filter-select {
            flex: 1;
            min-width: 120px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
        }
        
        .book-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .book-available {
            border-left-color: var(--success-color);
        }
        
        .book-unavailable {
            border-left-color: var(--error-color);
        }
        
        .book-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .book-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .book-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-unavailable {
            background: var(--error-color);
            color: white;
        }
        
        .status-reserved {
            background: var(--warning-color);
            color: white;
        }
        
        .book-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .book-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .book-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--margin-sm);
        }
        
        .book-location {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }
        
        .book-location i {
            margin-right: 4px;
        }
        
        .btn-book {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-reserve {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-detail {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .btn-book:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .search-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn-clear-history {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            background: none;
            border: none;
            cursor: pointer;
        }
        
        .history-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .history-tag {
            padding: 6px 12px;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border-radius: 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .history-tag:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .hot-books {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .hot-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .hot-title i {
            margin-right: var(--margin-xs);
            color: var(--error-color);
        }
        
        .hot-book-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .hot-book-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .hot-book-item:hover {
            background: var(--bg-color-active);
        }
        
        .hot-book-rank {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-right: var(--margin-sm);
        }
        
        .hot-book-rank.top3 {
            background: var(--warning-color);
        }
        
        .hot-book-info {
            flex: 1;
        }
        
        .hot-book-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .hot-book-author {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: var(--margin-lg) var(--margin-md);
            gap: var(--spacing-sm);
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            background: var(--bg-primary);
            color: var(--text-primary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .page-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .page-btn:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .page-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">图书查询</div>
            <div class="navbar-action" onclick="showAdvancedSearch();">
                <i class="ace-icon fa fa-filter"></i>
            </div>
        </nav>
        
        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-input-group">
                <input type="text" class="search-input" id="searchKeyword" placeholder="输入书名、作者或ISBN">
                <button class="btn-search" onclick="searchBooks();">
                    <i class="ace-icon fa fa-search"></i>
                </button>
            </div>
            
            <div class="search-filters">
                <select class="filter-select" id="categoryFilter">
                    <option value="">全部分类</option>
                    <option value="literature">文学</option>
                    <option value="science">科学技术</option>
                    <option value="history">历史</option>
                    <option value="philosophy">哲学</option>
                    <option value="art">艺术</option>
                </select>
                
                <select class="filter-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="available">可借阅</option>
                    <option value="unavailable">已借出</option>
                </select>
                
                <select class="filter-select" id="locationFilter">
                    <option value="">全部馆藏</option>
                    <option value="main">主馆</option>
                    <option value="branch1">分馆一</option>
                    <option value="branch2">分馆二</option>
                </select>
            </div>
        </div>
        
        <!-- 搜索历史 -->
        <div class="search-history" id="searchHistory" style="display: none;">
            <div class="history-title">
                <span>搜索历史</span>
                <button class="btn-clear-history" onclick="clearHistory();">清空</button>
            </div>
            <div class="history-tags" id="historyTags">
                <!-- 历史搜索标签 -->
            </div>
        </div>
        
        <!-- 热门图书 -->
        <div class="hot-books" id="hotBooks">
            <div class="hot-title">
                <i class="ace-icon fa fa-fire"></i>
                <span>热门图书</span>
            </div>
            <div class="hot-book-list" id="hotBookList">
                <!-- 热门图书列表 -->
            </div>
        </div>
        
        <!-- 搜索结果 -->
        <div class="container-mobile">
            <div id="bookList" style="display: none;">
                <!-- 图书列表将通过JavaScript动态填充 -->
            </div>
            
            <!-- 分页 -->
            <div class="pagination" id="pagination" style="display: none;">
                <button class="page-btn" id="prevBtn" onclick="prevPage();" disabled>
                    <i class="ace-icon fa fa-chevron-left"></i>
                </button>
                <span class="page-info" id="pageInfo">第1页 / 共1页</span>
                <button class="page-btn" id="nextBtn" onclick="nextPage();" disabled>
                    <i class="ace-icon fa fa-chevron-right"></i>
                </button>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-book"></i>
                <div>未找到相关图书</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>搜索中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let searchResults = [];
        let searchHistory = [];
        let hotBooks = [];

        $(function() {
            initPage();
            loadSearchHistory();
            loadHotBooks();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            
            // 绑定回车搜索
            $('#searchKeyword').on('keypress', function(e) {
                if (e.which === 13) {
                    searchBooks();
                }
            });
        }

        // 搜索图书
        function searchBooks(page = 1) {
            const keyword = $('#searchKeyword').val().trim();
            if (!keyword) {
                showError('请输入搜索关键词');
                return;
            }
            
            // 添加到搜索历史
            addToHistory(keyword);
            
            const searchParams = {
                keyword: keyword,
                category: $('#categoryFilter').val(),
                status: $('#statusFilter').val(),
                location: $('#locationFilter').val(),
                page: page,
                pageSize: 10
            };
            
            showLoading(true);
            
            $.ajax({
                url: "/student/library/bookSearch/search",
                type: "post",
                data: searchParams,
                dataType: "json",
                success: function(data) {
                    searchResults = data.books || [];
                    currentPage = data.currentPage || 1;
                    totalPages = data.totalPages || 1;
                    
                    renderSearchResults();
                    updatePagination();
                    
                    // 隐藏热门图书，显示搜索结果
                    $('#hotBooks').hide();
                    $('#searchHistory').hide();
                    $('#bookList').show();
                    
                    showLoading(false);
                },
                error: function() {
                    showError('搜索失败，请重试');
                    showLoading(false);
                }
            });
        }

        // 渲染搜索结果
        function renderSearchResults() {
            const container = $('#bookList');
            container.empty();
            
            if (searchResults.length === 0) {
                $('#emptyState').show();
                $('#pagination').hide();
                return;
            } else {
                $('#emptyState').hide();
                $('#pagination').show();
            }
            
            searchResults.forEach(book => {
                const bookHtml = createBookItem(book);
                container.append(bookHtml);
            });
        }

        // 创建图书项HTML
        function createBookItem(book) {
            const statusClass = book.available ? 'book-available' : 'book-unavailable';
            const statusBadgeClass = book.available ? 'status-available' : 'status-unavailable';
            const statusText = book.available ? '可借阅' : '已借出';
            
            let actionButton = '';
            if (book.available) {
                actionButton = `<button class="btn-book btn-reserve" onclick="reserveBook('${book.id}')">预约</button>`;
            } else {
                actionButton = `<button class="btn-book" disabled>不可借</button>`;
            }
            
            return `
                <div class="book-item ${statusClass}">
                    <div class="book-header">
                        <div class="book-title">${book.title}</div>
                        <div class="book-status ${statusBadgeClass}">${statusText}</div>
                    </div>
                    <div class="book-details">
                        <div class="book-detail-item">
                            <span>作者:</span>
                            <span>${book.author}</span>
                        </div>
                        <div class="book-detail-item">
                            <span>出版社:</span>
                            <span>${book.publisher}</span>
                        </div>
                        <div class="book-detail-item">
                            <span>ISBN:</span>
                            <span>${book.isbn}</span>
                        </div>
                        <div class="book-detail-item">
                            <span>分类:</span>
                            <span>${book.category}</span>
                        </div>
                    </div>
                    <div class="book-actions">
                        <div class="book-location">
                            <i class="ace-icon fa fa-map-marker"></i>
                            <span>${book.location}</span>
                        </div>
                        <div>
                            <button class="btn-book btn-detail" onclick="showBookDetail('${book.id}')">详情</button>
                            ${actionButton}
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示图书详情
        function showBookDetail(bookId) {
            const book = searchResults.find(b => b.id === bookId);
            if (!book) return;
            
            let message = `书名：${book.title}\n`;
            message += `作者：${book.author}\n`;
            message += `出版社：${book.publisher}\n`;
            message += `ISBN：${book.isbn}\n`;
            message += `分类：${book.category}\n`;
            message += `馆藏位置：${book.location}\n`;
            message += `状态：${book.available ? '可借阅' : '已借出'}\n`;
            
            if (book.description) {
                message += `\n简介：${book.description}`;
            }
            
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 预约图书
        function reserveBook(bookId) {
            const book = searchResults.find(b => b.id === bookId);
            if (!book) return;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要预约《${book.title}》吗？`, function(confirmed) {
                    if (confirmed) {
                        doReserveBook(bookId);
                    }
                });
            } else {
                if (confirm(`确定要预约《${book.title}》吗？`)) {
                    doReserveBook(bookId);
                }
            }
        }

        // 执行预约
        function doReserveBook(bookId) {
            $.ajax({
                url: "/student/library/bookSearch/reserve",
                type: "post",
                data: { bookId: bookId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('预约成功');
                        searchBooks(currentPage); // 重新搜索当前页
                    } else {
                        showError(data.message || '预约失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 加载搜索历史
        function loadSearchHistory() {
            const history = localStorage.getItem('bookSearchHistory');
            if (history) {
                searchHistory = JSON.parse(history);
                renderSearchHistory();
            }
        }

        // 渲染搜索历史
        function renderSearchHistory() {
            if (searchHistory.length === 0) {
                $('#searchHistory').hide();
                return;
            }
            
            const container = $('#historyTags');
            container.empty();
            
            searchHistory.forEach(keyword => {
                const tagHtml = `<div class="history-tag" onclick="searchFromHistory('${keyword}')">${keyword}</div>`;
                container.append(tagHtml);
            });
            
            $('#searchHistory').show();
        }

        // 添加到搜索历史
        function addToHistory(keyword) {
            if (searchHistory.includes(keyword)) {
                searchHistory = searchHistory.filter(k => k !== keyword);
            }
            
            searchHistory.unshift(keyword);
            
            // 最多保存10个历史记录
            if (searchHistory.length > 10) {
                searchHistory = searchHistory.slice(0, 10);
            }
            
            localStorage.setItem('bookSearchHistory', JSON.stringify(searchHistory));
            renderSearchHistory();
        }

        // 从历史记录搜索
        function searchFromHistory(keyword) {
            $('#searchKeyword').val(keyword);
            searchBooks();
        }

        // 清空搜索历史
        function clearHistory() {
            searchHistory = [];
            localStorage.removeItem('bookSearchHistory');
            $('#searchHistory').hide();
        }

        // 加载热门图书
        function loadHotBooks() {
            $.ajax({
                url: "/student/library/bookSearch/getHotBooks",
                type: "post",
                dataType: "json",
                success: function(data) {
                    hotBooks = data || [];
                    renderHotBooks();
                },
                error: function() {
                    console.log('加载热门图书失败');
                }
            });
        }

        // 渲染热门图书
        function renderHotBooks() {
            const container = $('#hotBookList');
            container.empty();
            
            hotBooks.forEach((book, index) => {
                const rankClass = index < 3 ? 'top3' : '';
                const bookHtml = `
                    <div class="hot-book-item" onclick="searchFromHot('${book.title}')">
                        <div class="hot-book-rank ${rankClass}">${index + 1}</div>
                        <div class="hot-book-info">
                            <div class="hot-book-title">${book.title}</div>
                            <div class="hot-book-author">${book.author}</div>
                        </div>
                    </div>
                `;
                container.append(bookHtml);
            });
        }

        // 从热门图书搜索
        function searchFromHot(title) {
            $('#searchKeyword').val(title);
            searchBooks();
        }

        // 更新分页
        function updatePagination() {
            $('#pageInfo').text(`第${currentPage}页 / 共${totalPages}页`);
            $('#prevBtn').prop('disabled', currentPage <= 1);
            $('#nextBtn').prop('disabled', currentPage >= totalPages);
        }

        // 上一页
        function prevPage() {
            if (currentPage > 1) {
                searchBooks(currentPage - 1);
            }
        }

        // 下一页
        function nextPage() {
            if (currentPage < totalPages) {
                searchBooks(currentPage + 1);
            }
        }

        // 显示高级搜索
        function showAdvancedSearch() {
            // 可以实现高级搜索功能
            showSuccess('高级搜索功能开发中');
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
