<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>选课结果</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 选课结果页面样式 */
        .result-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .summary-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .course-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .course-success {
            border-left-color: var(--success-color);
        }
        
        .course-failed {
            border-left-color: var(--error-color);
        }
        
        .course-pending {
            border-left-color: var(--warning-color);
        }
        
        .course-conflict {
            border-left-color: var(--error-color);
            background: rgba(255, 77, 79, 0.05);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .course-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-success {
            background: var(--success-color);
            color: white;
        }
        
        .status-failed {
            background: var(--error-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-conflict {
            background: var(--error-color);
            color: white;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .course-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .course-detail-label {
            color: var(--text-secondary);
        }
        
        .course-detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .conflict-warning {
            background: rgba(255, 77, 79, 0.1);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--error-color);
        }
        
        .conflict-title {
            font-weight: 500;
            margin-bottom: var(--margin-xs);
            display: flex;
            align-items: center;
        }
        
        .conflict-title i {
            margin-right: var(--margin-xs);
        }
        
        .conflict-list {
            font-size: var(--font-size-mini);
            line-height: var(--line-height-base);
        }
        
        .course-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--margin-sm);
        }
        
        .course-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .btn-action {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-withdraw {
            background: var(--error-color);
            color: white;
        }
        
        .btn-resolve {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-detail {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .semester-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .conflict-alert {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--error-color);
        }
        
        .alert-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--error-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .alert-title i {
            margin-right: var(--margin-xs);
        }
        
        .alert-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .btn-resolve-all {
            background: var(--warning-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            margin-top: var(--margin-sm);
            transition: all var(--transition-base);
        }
        
        .timetable-preview {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .preview-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .mini-timetable {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            font-size: var(--font-size-mini);
        }
        
        .time-slot {
            background: var(--bg-tertiary);
            padding: 4px 2px;
            text-align: center;
            border-radius: 2px;
            min-height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .time-slot.has-course {
            background: var(--primary-color);
            color: white;
        }
        
        .time-slot.has-conflict {
            background: var(--error-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">选课结果</div>
            <div class="navbar-action" onclick="refreshResults();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学期选择 -->
        <div class="semester-selector">
            <div class="selector-title">选择学期</div>
            <select class="form-control" id="semesterSelect" onchange="loadResults();">
                <option value="">请选择学期</option>
                <c:forEach items="${semesters}" var="semester">
                    <option value="${semester.id}" <c:if test="${semester.isCurrent}">selected</c:if>>${semester.name}</option>
                </c:forEach>
            </select>
        </div>
        
        <!-- 选课统计 -->
        <div class="result-summary">
            <div class="summary-title">选课统计</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalCourses">0</div>
                    <div class="stat-label">总课程</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="successCourses">0</div>
                    <div class="stat-label">选课成功</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalCredits">0</div>
                    <div class="stat-label">总学分</div>
                </div>
            </div>
        </div>
        
        <!-- 冲突提醒 -->
        <div class="conflict-alert" id="conflictAlert" style="display: none;">
            <div class="alert-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>时间冲突提醒</span>
            </div>
            <div class="alert-content" id="conflictContent">
                检测到课程时间冲突，请及时处理以免影响正常上课。
            </div>
            <button class="btn-resolve-all" onclick="resolveAllConflicts();">一键解决冲突</button>
        </div>
        
        <!-- 课程表预览 -->
        <div class="timetable-preview" id="timetablePreview" style="display: none;">
            <div class="preview-title">课程表预览</div>
            <div class="mini-timetable" id="miniTimetable">
                <!-- 课程表预览内容 -->
            </div>
        </div>
        
        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterCourses('all')">全部</button>
                <button class="filter-chip" onclick="filterCourses('success')">选课成功</button>
                <button class="filter-chip" onclick="filterCourses('failed')">选课失败</button>
                <button class="filter-chip" onclick="filterCourses('conflict')">时间冲突</button>
            </div>
        </div>
        
        <!-- 选课结果列表 -->
        <div class="container-mobile">
            <div id="courseList">
                <!-- 课程项将通过JavaScript动态填充 -->
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-calendar-check-o"></i>
                <div>暂无选课记录</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allCourses = [];
        let filteredCourses = [];
        let currentFilter = 'all';
        let currentSemester = '';

        $(function() {
            initPage();
            loadResults();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            currentSemester = $('#semesterSelect').val();
        }

        // 加载选课结果
        function loadResults() {
            const semester = $('#semesterSelect').val();
            if (!semester) return;
            
            currentSemester = semester;
            showLoading(true);
            
            $.ajax({
                url: "/student/courseSelectManagement/courseSelectResult/getResults",
                type: "post",
                data: { semester: semester },
                dataType: "json",
                success: function(data) {
                    allCourses = data.courses || [];
                    updateStatistics(data.statistics);
                    updateConflictAlert(data.conflicts);
                    generateTimetablePreview();
                    applyFilter();
                    showLoading(false);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                    showLoading(false);
                }
            });
        }

        // 渲染课程列表
        function renderCourses() {
            const container = $('#courseList');
            container.empty();
            
            if (filteredCourses.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            filteredCourses.forEach(function(course, index) {
                const courseHtml = createCourseItem(course, index);
                container.append(courseHtml);
            });
        }

        // 创建课程项HTML
        function createCourseItem(course, index) {
            const status = getCourseStatus(course);
            const statusClass = getStatusClass(status);
            const itemClass = getItemClass(status);
            
            let conflictHtml = '';
            if (course.conflicts && course.conflicts.length > 0) {
                conflictHtml = `
                    <div class="conflict-warning">
                        <div class="conflict-title">
                            <i class="ace-icon fa fa-warning"></i>
                            <span>时间冲突</span>
                        </div>
                        <div class="conflict-list">
                            ${course.conflicts.map(conflict => `与《${conflict.courseName}》时间冲突`).join('<br>')}
                        </div>
                    </div>
                `;
            }
            
            let actionButtons = '';
            if (status === 'success') {
                actionButtons = `
                    <button class="btn-action btn-detail" onclick="showCourseDetail('${course.id}')">详情</button>
                    <button class="btn-action btn-withdraw" onclick="withdrawCourse('${course.id}')">退选</button>
                `;
            } else if (status === 'conflict') {
                actionButtons = `
                    <button class="btn-action btn-resolve" onclick="resolveConflict('${course.id}')">解决冲突</button>
                    <button class="btn-action btn-withdraw" onclick="withdrawCourse('${course.id}')">退选</button>
                `;
            } else if (status === 'failed') {
                actionButtons = `
                    <button class="btn-action btn-detail" onclick="showCourseDetail('${course.id}')">查看原因</button>
                `;
            }
            
            return `
                <div class="course-item ${itemClass}">
                    <div class="course-header">
                        <div class="course-name">${course.courseName}</div>
                        <div class="course-status ${statusClass}">${getStatusText(status)}</div>
                    </div>
                    <div class="course-details">
                        <div class="course-detail-item">
                            <span class="course-detail-label">课程号:</span>
                            <span class="course-detail-value">${course.courseCode}</span>
                        </div>
                        <div class="course-detail-item">
                            <span class="course-detail-label">学分:</span>
                            <span class="course-detail-value">${course.credits}</span>
                        </div>
                        <div class="course-detail-item">
                            <span class="course-detail-label">教师:</span>
                            <span class="course-detail-value">${course.teacherName}</span>
                        </div>
                        <div class="course-detail-item">
                            <span class="course-detail-label">课程类型:</span>
                            <span class="course-detail-value">${course.courseType}</span>
                        </div>
                    </div>
                    ${conflictHtml}
                    <div class="course-actions">
                        <div class="course-time">${course.timeSlot} @ ${course.location}</div>
                        <div>
                            ${actionButtons}
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取课程状态
        function getCourseStatus(course) {
            if (course.conflicts && course.conflicts.length > 0) {
                return 'conflict';
            } else if (course.isSelected) {
                return 'success';
            } else if (course.selectResult === 'failed') {
                return 'failed';
            } else {
                return 'pending';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'success': return 'status-success';
                case 'failed': return 'status-failed';
                case 'conflict': return 'status-conflict';
                case 'pending': return 'status-pending';
                default: return 'status-pending';
            }
        }

        // 获取项目样式类
        function getItemClass(status) {
            switch(status) {
                case 'success': return 'course-success';
                case 'failed': return 'course-failed';
                case 'conflict': return 'course-conflict';
                case 'pending': return 'course-pending';
                default: return '';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'success': return '选课成功';
                case 'failed': return '选课失败';
                case 'conflict': return '时间冲突';
                case 'pending': return '待处理';
                default: return '未知';
            }
        }

        // 退选课程
        function withdrawCourse(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要退选《${course.courseName}》吗？`, function(confirmed) {
                    if (confirmed) {
                        doWithdrawCourse(courseId);
                    }
                });
            } else {
                if (confirm(`确定要退选《${course.courseName}》吗？`)) {
                    doWithdrawCourse(courseId);
                }
            }
        }

        // 执行退选
        function doWithdrawCourse(courseId) {
            $.ajax({
                url: "/student/courseSelectManagement/courseSelectResult/withdrawCourse",
                type: "post",
                data: { courseId: courseId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('退选成功');
                        loadResults(); // 重新加载数据
                    } else {
                        showError(data.message || '退选失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 解决冲突
        function resolveConflict(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;
            
            // 这里可以显示冲突解决选项
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert('请选择要保留的课程，系统将自动退选冲突的课程。');
            } else {
                alert('请选择要保留的课程，系统将自动退选冲突的课程。');
            }
        }

        // 解决所有冲突
        function resolveAllConflicts() {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('系统将自动解决所有时间冲突，是否继续？', function(confirmed) {
                    if (confirmed) {
                        doResolveAllConflicts();
                    }
                });
            } else {
                if (confirm('系统将自动解决所有时间冲突，是否继续？')) {
                    doResolveAllConflicts();
                }
            }
        }

        // 执行解决所有冲突
        function doResolveAllConflicts() {
            $.ajax({
                url: "/student/courseSelectManagement/courseSelectResult/resolveConflicts",
                type: "post",
                data: { semester: currentSemester },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('冲突解决成功');
                        loadResults(); // 重新加载数据
                    } else {
                        showError(data.message || '冲突解决失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示课程详情
        function showCourseDetail(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;
            
            let message = `课程：${course.courseName}\n`;
            message += `教师：${course.teacherName}\n`;
            message += `时间：${course.timeSlot}\n`;
            message += `地点：${course.location}\n`;
            
            if (course.selectResult === 'failed' && course.failReason) {
                message += `\n失败原因：${course.failReason}`;
            }
            
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'success':
                    filteredCourses = allCourses.filter(course => getCourseStatus(course) === 'success');
                    break;
                case 'failed':
                    filteredCourses = allCourses.filter(course => getCourseStatus(course) === 'failed');
                    break;
                case 'conflict':
                    filteredCourses = allCourses.filter(course => getCourseStatus(course) === 'conflict');
                    break;
                default:
                    filteredCourses = allCourses;
            }
            
            renderCourses();
        }

        // 筛选课程
        function filterCourses(filter) {
            currentFilter = filter;
            
            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');
            
            applyFilter();
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;
            
            $('#totalCourses').text(statistics.totalCourses || 0);
            $('#successCourses').text(statistics.successCourses || 0);
            $('#totalCredits').text(statistics.totalCredits || 0);
        }

        // 更新冲突提醒
        function updateConflictAlert(conflicts) {
            if (conflicts && conflicts.length > 0) {
                $('#conflictContent').text(`检测到${conflicts.length}个时间冲突，请及时处理以免影响正常上课。`);
                $('#conflictAlert').show();
            } else {
                $('#conflictAlert').hide();
            }
        }

        // 生成课程表预览
        function generateTimetablePreview() {
            const successCourses = allCourses.filter(course => getCourseStatus(course) === 'success');
            if (successCourses.length > 0) {
                // 这里可以生成简化的课程表预览
                $('#timetablePreview').show();
                // 具体实现可以根据需要添加
            }
        }

        // 刷新结果
        function refreshResults() {
            loadResults();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#courseList, .result-summary, .filter-section').hide();
            } else {
                $('#loadingState').hide();
                $('#courseList, .result-summary, .filter-section').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
