<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教学评价</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教学评价页面样式 */
        .evaluation-progress {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }

        .progress-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }

        .progress-percentage {
            font-size: var(--font-size-h3);
            font-weight: 600;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }

        .progress-fill {
            background: white;
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .course-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }

        .course-evaluated {
            border-left-color: var(--success-color);
            opacity: 0.8;
        }

        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }

        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }

        .evaluation-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }

        .status-pending {
            background: var(--warning-color);
            color: white;
        }

        .status-completed {
            background: var(--success-color);
            color: white;
        }

        .status-expired {
            background: var(--error-color);
            color: white;
        }

        .course-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }

        .course-info-item {
            display: flex;
            justify-content: space-between;
        }

        .course-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .evaluation-deadline {
            font-size: var(--font-size-small);
            color: var(--error-color);
        }

        .btn-evaluate {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .btn-evaluate:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }

        .btn-view {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .evaluation-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            margin-bottom: var(--margin-lg);
        }

        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }

        .question-item {
            margin-bottom: var(--margin-md);
        }

        .question-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            line-height: var(--line-height-base);
        }

        .rating-options {
            display: flex;
            justify-content: space-between;
            gap: var(--spacing-xs);
        }

        .rating-option {
            flex: 1;
            text-align: center;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
            font-size: var(--font-size-small);
        }

        .rating-option.selected {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .rating-labels {
            display: flex;
            justify-content: space-between;
            margin-top: var(--margin-xs);
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }

        .comment-section {
            margin-top: var(--margin-md);
        }

        .comment-textarea {
            width: 100%;
            min-height: 80px;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            resize: vertical;
        }

        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
        }

        .btn-submit {
            background: var(--success-color);
            color: white;
        }

        .btn-save {
            background: var(--warning-color);
            color: white;
        }

        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }

        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教学评价</div>
            <div class="navbar-action" onclick="refreshEvaluations();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 评价进度 -->
        <div class="evaluation-progress" id="evaluationProgress">
            <div class="progress-header">
                <div class="progress-title">评价进度</div>
                <div class="progress-percentage" id="progressPercentage">0%</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
            </div>
            <div style="margin-top: var(--margin-sm); font-size: var(--font-size-small);">
                已完成 <span id="completedCount">0</span> / <span id="totalCount">0</span> 门课程
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterCourses('all')">全部</button>
                <button class="filter-chip" onclick="filterCourses('pending')">待评价</button>
                <button class="filter-chip" onclick="filterCourses('completed')">已完成</button>
                <button class="filter-chip" onclick="filterCourses('expired')">已过期</button>
            </div>
        </div>

        <!-- 课程列表 -->
        <div class="container-mobile">
            <div id="courseList">
                <!-- 课程项将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-star-o"></i>
                <div>暂无需要评价的课程</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>

        <!-- 评价表单 -->
        <div class="evaluation-form" id="evaluationForm" style="display: none;">
            <div class="form-section">
                <div class="section-title" id="formTitle">课程评价</div>

                <!-- 评价问题 -->
                <div id="questionList">
                    <!-- 问题将通过JavaScript动态填充 -->
                </div>

                <!-- 评价意见 -->
                <div class="comment-section">
                    <label class="form-label">评价意见</label>
                    <textarea class="comment-textarea" id="evaluationComment"
                              placeholder="请输入您对本课程的意见和建议..."></textarea>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button class="btn-mobile btn-secondary flex-1" onclick="cancelEvaluation();">取消</button>
                    <button class="btn-mobile btn-save flex-1" onclick="saveEvaluation();">保存</button>
                    <button class="btn-mobile btn-submit flex-1" onclick="submitEvaluation();">提交</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 评价详情模态框 -->
    <div class="modal fade" id="evaluationDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                    <h4 class="modal-title" id="evaluationDetailTitle">评价详情</h4>
                </div>
                <div class="modal-body" id="evaluationDetailBody">
                    <!-- 评价详情内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allCourses = [];
        let filteredCourses = [];
        let currentFilter = 'all';
        let currentCourse = null;
        let evaluationQuestions = [];
        let evaluationAnswers = {};

        $(function() {
            initPage();
            loadCourses();
            loadEvaluationQuestions();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载课程列表
        function loadCourses() {
            showLoading(true);

            $.ajax({
                url: "/student/teachingEvaluation/courseEvaluation/getCourses",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allCourses = data || [];
                    updateProgress();
                    applyFilter();
                    showLoading(false);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                    showLoading(false);
                }
            });
        }

        // 加载评价问题
        function loadEvaluationQuestions() {
            $.ajax({
                url: "/student/teachingEvaluation/courseEvaluation/getQuestions",
                type: "post",
                dataType: "json",
                success: function(data) {
                    evaluationQuestions = data || [];
                },
                error: function() {
                    console.log('加载评价问题失败');
                }
            });
        }

        // 渲染课程列表
        function renderCourses() {
            const container = $('#courseList');
            container.empty();

            if (filteredCourses.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            filteredCourses.forEach(function(course, index) {
                const courseHtml = createCourseItem(course, index);
                container.append(courseHtml);
            });
        }

        // 创建课程项HTML
        function createCourseItem(course, index) {
            const status = getEvaluationStatus(course);
            const statusClass = getStatusClass(status);
            const evaluatedClass = status === 'completed' ? 'course-evaluated' : '';

            let actionButton = '';
            switch(status) {
                case 'pending':
                    actionButton = `<button class="btn-evaluate" onclick="startEvaluation('${course.id}')">开始评价</button>`;
                    break;
                case 'completed':
                    actionButton = `<button class="btn-evaluate btn-view" onclick="viewEvaluation('${course.id}')">查看评价</button>`;
                    break;
                case 'expired':
                    actionButton = `<button class="btn-evaluate" disabled>已过期</button>`;
                    break;
            }

            return `
                <div class="course-item ${evaluatedClass}">
                    <div class="course-header">
                        <div class="course-name">${course.courseName}</div>
                        <div class="evaluation-status ${statusClass}">${getStatusText(status)}</div>
                    </div>
                    <div class="course-info">
                        <div class="course-info-item">
                            <span>授课教师:</span>
                            <span>${course.teacherName}</span>
                        </div>
                        <div class="course-info-item">
                            <span>课程号:</span>
                            <span>${course.courseCode}</span>
                        </div>
                        <div class="course-info-item">
                            <span>学分:</span>
                            <span>${course.credits}</span>
                        </div>
                        <div class="course-info-item">
                            <span>课程类型:</span>
                            <span>${course.courseType}</span>
                        </div>
                    </div>
                    <div class="course-actions">
                        <div class="evaluation-deadline">
                            截止时间: ${course.deadline}
                        </div>
                        ${actionButton}
                    </div>
                </div>
            `;
        }

        // 获取评价状态
        function getEvaluationStatus(course) {
            if (course.isEvaluated) {
                return 'completed';
            }

            const now = new Date();
            const deadline = new Date(course.deadline);

            if (now > deadline) {
                return 'expired';
            } else {
                return 'pending';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'status-pending';
                case 'completed': return 'status-completed';
                case 'expired': return 'status-expired';
                default: return 'status-pending';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待评价';
                case 'completed': return '已完成';
                case 'expired': return '已过期';
                default: return '待评价';
            }
        }

        // 开始评价
        function startEvaluation(courseId) {
            currentCourse = allCourses.find(c => c.id === courseId);
            if (!currentCourse) return;

            // 加载已保存的评价数据
            loadSavedEvaluation(courseId);

            // 显示评价表单
            showEvaluationForm();
        }

        // 显示评价表单
        function showEvaluationForm() {
            $('#formTitle').text(currentCourse.courseName + ' - 教学评价');
            renderQuestions();
            $('#evaluationForm').show();
            $('#courseList, .filter-section, #evaluationProgress').hide();

            // 滚动到顶部
            $('html, body').animate({scrollTop: 0}, 300);
        }

        // 渲染评价问题
        function renderQuestions() {
            const container = $('#questionList');
            container.empty();

            evaluationQuestions.forEach(function(question, index) {
                const questionHtml = createQuestionItem(question, index);
                container.append(questionHtml);
            });
        }

        // 创建问题项HTML
        function createQuestionItem(question, index) {
            const savedAnswer = evaluationAnswers[question.id] || '';

            return `
                <div class="question-item">
                    <div class="question-text">${index + 1}. ${question.text}</div>
                    <div class="rating-options" data-question-id="${question.id}">
                        <div class="rating-option ${savedAnswer === '5' ? 'selected' : ''}" data-value="5">5</div>
                        <div class="rating-option ${savedAnswer === '4' ? 'selected' : ''}" data-value="4">4</div>
                        <div class="rating-option ${savedAnswer === '3' ? 'selected' : ''}" data-value="3">3</div>
                        <div class="rating-option ${savedAnswer === '2' ? 'selected' : ''}" data-value="2">2</div>
                        <div class="rating-option ${savedAnswer === '1' ? 'selected' : ''}" data-value="1">1</div>
                    </div>
                    <div class="rating-labels">
                        <span>非常满意</span>
                        <span>满意</span>
                        <span>一般</span>
                        <span>不满意</span>
                        <span>非常不满意</span>
                    </div>
                </div>
            `;
        }

        // 绑定评分选择事件
        $(document).on('click', '.rating-option', function() {
            const $this = $(this);
            const questionId = $this.parent().data('question-id');
            const value = $this.data('value');

            // 更新选中状态
            $this.siblings().removeClass('selected');
            $this.addClass('selected');

            // 保存答案
            evaluationAnswers[questionId] = value;
        });

        // 加载已保存的评价
        function loadSavedEvaluation(courseId) {
            $.ajax({
                url: "/student/teachingEvaluation/courseEvaluation/getSavedEvaluation",
                type: "post",
                data: { courseId: courseId },
                dataType: "json",
                success: function(data) {
                    if (data && data.answers) {
                        evaluationAnswers = data.answers;
                        $('#evaluationComment').val(data.comment || '');
                    } else {
                        evaluationAnswers = {};
                        $('#evaluationComment').val('');
                    }
                },
                error: function() {
                    evaluationAnswers = {};
                    $('#evaluationComment').val('');
                }
            });
        }

        // 保存评价
        function saveEvaluation() {
            if (!validateEvaluation()) return;

            const evaluationData = {
                courseId: currentCourse.id,
                answers: evaluationAnswers,
                comment: $('#evaluationComment').val(),
                isDraft: true
            };

            $.ajax({
                url: "/student/teachingEvaluation/courseEvaluation/saveEvaluation",
                type: "post",
                data: JSON.stringify(evaluationData),
                contentType: "application/json",
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('评价已保存');
                    } else {
                        showError(data.message || '保存失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 提交评价
        function submitEvaluation() {
            if (!validateEvaluation()) return;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要提交评价吗？提交后将无法修改。', function(confirmed) {
                    if (confirmed) {
                        doSubmitEvaluation();
                    }
                });
            } else {
                if (confirm('确定要提交评价吗？提交后将无法修改。')) {
                    doSubmitEvaluation();
                }
            }
        }

        // 执行提交评价
        function doSubmitEvaluation() {
            const evaluationData = {
                courseId: currentCourse.id,
                answers: evaluationAnswers,
                comment: $('#evaluationComment').val(),
                isDraft: false
            };

            $.ajax({
                url: "/student/teachingEvaluation/courseEvaluation/submitEvaluation",
                type: "post",
                data: JSON.stringify(evaluationData),
                contentType: "application/json",
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('评价提交成功');
                        cancelEvaluation();
                        loadCourses(); // 重新加载课程列表
                    } else {
                        showError(data.message || '提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 验证评价
        function validateEvaluation() {
            // 检查是否所有问题都已回答
            for (let question of evaluationQuestions) {
                if (!evaluationAnswers[question.id]) {
                    showError('请完成所有评价项目');
                    return false;
                }
            }
            return true;
        }

        // 取消评价
        function cancelEvaluation() {
            $('#evaluationForm').hide();
            $('#courseList, .filter-section, #evaluationProgress').show();
            currentCourse = null;
            evaluationAnswers = {};
        }

        // 查看评价
        function viewEvaluation(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;

            $.ajax({
                url: "/student/teachingEvaluation/courseEvaluation/getEvaluationDetail",
                type: "post",
                data: { courseId: courseId },
                dataType: "json",
                success: function(data) {
                    if (data && data.evaluation) {
                        showEvaluationDetail(course, data.evaluation);
                    } else {
                        showError('获取评价详情失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示评价详情
        function showEvaluationDetail(course, evaluation) {
            let detailHtml = `
                <div class="detail-item">
                    <div class="detail-label">课程名称</div>
                    <div class="detail-value">${course.courseName}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">授课教师</div>
                    <div class="detail-value">${course.teacherName}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">评价时间</div>
                    <div class="detail-value">${evaluation.submitTime}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">评价得分</div>
                    <div class="detail-value">${evaluation.averageScore} 分</div>
                </div>
            `;

            if (evaluation.comment) {
                detailHtml += `
                    <div class="detail-item">
                        <div class="detail-label">评价意见</div>
                        <div class="detail-value">${evaluation.comment}</div>
                    </div>
                `;
            }

            $('#evaluationDetailTitle').text(course.courseName + ' - 评价详情');
            $('#evaluationDetailBody').html(detailHtml);
            $('#evaluationDetailModal').modal('show');
        }

        // 筛选课程
        function filterCourses(filter) {
            currentFilter = filter;

            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');

            applyFilter();
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'pending':
                    filteredCourses = allCourses.filter(course => getEvaluationStatus(course) === 'pending');
                    break;
                case 'completed':
                    filteredCourses = allCourses.filter(course => getEvaluationStatus(course) === 'completed');
                    break;
                case 'expired':
                    filteredCourses = allCourses.filter(course => getEvaluationStatus(course) === 'expired');
                    break;
                default:
                    filteredCourses = allCourses;
            }

            renderCourses();
        }

        // 更新进度
        function updateProgress() {
            const total = allCourses.length;
            const completed = allCourses.filter(course => getEvaluationStatus(course) === 'completed').length;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            $('#totalCount').text(total);
            $('#completedCount').text(completed);
            $('#progressPercentage').text(percentage + '%');
            $('#progressFill').css('width', percentage + '%');
        }

        // 刷新评价列表
        function refreshEvaluations() {
            loadCourses();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#courseList, #evaluationProgress, .filter-section').hide();
            } else {
                $('#loadingState').hide();
                $('#courseList, #evaluationProgress, .filter-section').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>