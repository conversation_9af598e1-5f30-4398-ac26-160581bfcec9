<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>转专业申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 转专业申请页面样式 */
        .transfer-header {
            background: linear-gradient(135deg, var(--warning-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .transfer-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .transfer-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .application-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .container-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-title i {
            color: var(--warning-color);
        }
        
        .btn-add-application {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .batch-tabs {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-sm);
            overflow-x: auto;
        }
        
        .batch-tab {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            white-space: nowrap;
            color: var(--text-secondary);
        }
        
        .batch-tab:hover {
            background: var(--warning-light);
            border-color: var(--warning-color);
            color: var(--warning-dark);
        }
        
        .batch-tab.active {
            background: var(--warning-color);
            border-color: var(--warning-color);
            color: white;
        }
        
        .application-content {
            padding: var(--padding-md);
        }
        
        .application-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .info-row {
            display: flex;
            margin-bottom: var(--margin-sm);
            align-items: flex-start;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            min-width: 80px;
            margin-right: var(--margin-sm);
        }
        
        .info-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            flex: 1;
            word-break: break-all;
        }
        
        .major-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: var(--margin-md);
            font-size: var(--font-size-small);
        }
        
        .major-table th,
        .major-table td {
            border: 1px solid var(--border-primary);
            padding: 8px;
            text-align: left;
        }
        
        .major-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .major-table td {
            color: var(--text-secondary);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-draft {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-pending {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-completed {
            background: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .action-btn.edit {
            color: var(--warning-color);
            border-color: var(--warning-color);
        }
        
        .action-btn.edit:hover {
            background: var(--warning-light);
        }
        
        .action-btn.submit {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .action-btn.submit:hover {
            background: var(--primary-light);
        }
        
        .action-btn.delete {
            color: var(--error-color);
            border-color: var(--error-color);
        }
        
        .action-btn.delete:hover {
            background: var(--error-light);
        }
        
        .action-btn.print {
            color: var(--info-color);
            border-color: var(--info-color);
        }
        
        .action-btn.print:hover {
            background: var(--info-light);
        }
        
        .approval-process {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .process-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .process-title i {
            color: var(--primary-color);
        }
        
        .process-steps {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .process-step {
            background: var(--primary-color);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: var(--font-size-small);
            position: relative;
        }
        
        .process-step::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid var(--primary-color);
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }
        
        .process-step:last-child::after {
            display: none;
        }
        
        .empty-notice {
            background: var(--success-light);
            border: 1px solid var(--success-color);
            border-radius: 8px;
            padding: var(--padding-lg);
            margin: var(--margin-sm) var(--margin-md);
            text-align: center;
            color: var(--success-dark);
        }
        
        .notice-icon {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--warning-color);
        }
        
        .notice-text {
            font-size: var(--font-size-base);
            margin-bottom: var(--margin-sm);
        }
        
        .notice-link {
            color: var(--primary-color);
            text-decoration: underline;
            cursor: pointer;
        }
        
        .approval-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--margin-md);
            font-size: var(--font-size-small);
        }
        
        .approval-table th,
        .approval-table td {
            border: 1px solid var(--border-primary);
            padding: 8px;
            text-align: left;
        }
        
        .approval-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .approval-table td {
            color: var(--text-secondary);
        }
        
        @media (max-width: 480px) {
            .batch-tabs {
                padding: var(--padding-xs);
            }
            
            .batch-tab {
                padding: 6px 12px;
                font-size: var(--font-size-mini);
            }
            
            .info-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-label {
                margin-bottom: 4px;
                margin-right: 0;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .process-steps {
                gap: var(--spacing-xs);
            }
            
            .process-step {
                padding: 6px 12px;
                font-size: var(--font-size-mini);
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">转专业申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 转专业申请头部 -->
        <div class="transfer-header">
            <div class="transfer-title">转专业申请</div>
            <div class="transfer-desc">管理您的转专业申请</div>
        </div>
        
        <!-- 申请信息容器 -->
        <c:if test="${not empty sqpcs && fn:length(sqpcs) > 0}">
            <div class="application-container">
                <div class="container-header">
                    <div class="container-title">
                        <i class="ace-icon fa fa-list"></i>
                        申请信息
                    </div>
                    <c:if test="${show}">
                        <button class="btn-add-application" onclick="addApplication();">
                            <i class="ace-icon fa fa-plus"></i>
                            申请
                        </button>
                    </c:if>
                </div>
                
                <!-- 批次标签页 -->
                <div class="batch-tabs" id="batchTabs">
                    <c:forEach var="sqbh" items="${sqpcs}" varStatus="sqbhsta">
                        <div class="batch-tab ${sqbhsta.first ? 'active' : ''}" 
                             data-sqbh="${sqbh[0]}" 
                             onclick="switchBatch('${sqbh[0]}', this);">
                            批次:${sqbh[2]}
                        </div>
                    </c:forEach>
                </div>
                
                <!-- 申请内容 -->
                <div class="application-content" id="applicationContent">
                    <!-- 动态加载申请详情 -->
                </div>
            </div>
            
            <!-- 审批流程 -->
            <div class="approval-process">
                <div class="process-title">
                    <i class="ace-icon fa fa-sitemap"></i>
                    审批流程
                </div>
                
                <div class="process-steps">
                    <c:if test="${zcshf == '1'}">
                        <div class="process-step">
                            1.转出时，<c:if test="${zcshbm == 'JWC'}">${jwcname}</c:if><c:if test="${zcshbm == 'YX'}">转出院系</c:if>审核
                        </div>
                        <div class="process-step">2.建议录取</div>
                        <c:if test="${zrxyfsf == '1'}">
                            <div class="process-step">3.转入院系审批</div>
                            <div class="process-step">
                                4.<c:if test="${zsspbm == 'JWC'}">${jwcname}</c:if><c:if test="${zsspbm == 'YX'}">院系</c:if>复审，修改学籍信息
                            </div>
                        </c:if>
                        <c:if test="${zrxyfsf == '0'}">
                            <div class="process-step">3.院系审批，修改学籍信息</div>
                        </c:if>
                    </c:if>
                    <c:if test="${zcshf == '0'}">
                        <div class="process-step">1.建议录取</div>
                        <c:if test="${zrxyfsf == '1'}">
                            <div class="process-step">2.转入院系审批</div>
                            <div class="process-step">
                                3.<c:if test="${zsspbm == 'JWC'}">${jwcname}</c:if><c:if test="${zsspbm == 'YX'}">院系</c:if>复审，修改学籍信息
                            </div>
                        </c:if>
                        <c:if test="${zrxyfsf == '0'}">
                            <div class="process-step">2.院系审批，修改学籍信息</div>
                        </c:if>
                    </c:if>
                </div>
            </div>
        </c:if>
        
        <!-- 空状态提示 -->
        <c:if test="${empty sqpcs || fn:length(sqpcs) < 1}">
            <div class="empty-notice">
                <i class="ace-icon fa fa-exclamation-circle notice-icon"></i>
                <div class="notice-text">您还未申请转专业</div>
                <div>
                    请点击<span class="notice-link" onclick="addApplication();">这里</span>查看可转入院系专业信息
                </div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentBatch = '';
        let yxckpm = ${yxckpm};

        $(function() {
            initPage();
            
            // 默认加载第一个批次
            const firstTab = $('.batch-tab.active').first();
            if (firstTab.length > 0) {
                const sqbh = firstTab.data('sqbh');
                loadApplicationInfo(sqbh);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换批次
        function switchBatch(sqbh, element) {
            // 更新标签状态
            $('.batch-tab').removeClass('active');
            $(element).addClass('active');

            // 加载对应批次的申请信息
            loadApplicationInfo(sqbh);
        }

        // 加载申请信息
        function loadApplicationInfo(sqbh) {
            currentBatch = sqbh;
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/studentChange/specialtiesApply/" + sqbh,
                type: "post",
                dataType: "json",
                success: function(response) {
                    if (response && response.data) {
                        renderApplicationInfo(response.data);
                    } else {
                        showError("获取申请信息失败");
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请信息
        function renderApplicationInfo(data) {
            const container = $('#applicationContent');

            let contentHtml = `
                <div class="application-info">
                    <div class="info-row">
                        <div class="info-label">申请批次:</div>
                        <div class="info-value">${data.zzypcm || '未设置'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">学号:</div>
                        <div class="info-value">${data.xh || '未设置'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">姓名:</div>
                        <div class="info-value">${data.xm || '未设置'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">绩点:</div>
                        <div class="info-value">${data.xsZzySqb.gpa || '未设置'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">联系方式:</div>
                        <div class="info-value">${data.xsZzySqb.lxfs || '未设置'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">申请理由:</div>
                        <div class="info-value">${data.xsZzySqb.sqly || '未设置'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">个人专长:</div>
                        <div class="info-value">${data.xsZzySqb.xstc || '未设置'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">个人学年综合评价:</div>
                        <div class="info-value">${data.xsZzySqb.xsgrpj || '未设置'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">审批状态:</div>
                        <div class="info-value">
                            ${getStatusBadge(data.xsZzySqb.sqztdm)}
                            ${data.xsZzySqb.sqztdm === '00' ? '<div style="color: var(--warning-color); font-size: var(--font-size-mini); margin-top: 4px;">当前申请处于暂存状态，需提交之后才能生效！</div>' : ''}
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">备注:</div>
                        <div class="info-value">${data.xsZzySqb.bz || '无'}</div>
                    </div>
                </div>

                <div class="info-row">
                    <div class="info-label">申请转入专业:</div>
                    <div class="info-value">
                        <table class="major-table">
                            <thead>
                                <tr>
                                    <th>志愿序号</th>
                                    <th>转入年级</th>
                                    <th>转入院系号</th>
                                    <th>转入院系</th>
                                    <th>转入专业</th>
                                    <th>转入专业方向</th>
                                    <th>计划接收人数</th>
                                    <th>已选人数</th>
                                    ${yxckpm ? '<th>绩点排名</th>' : ''}
                                </tr>
                            </thead>
                            <tbody>
                                ${renderMajorRows(data.xsZzyZybs)}
                            </tbody>
                        </table>
                    </div>
                </div>

                ${renderActionButtons(data)}

                ${data.xsckkz === 1 ? renderApprovalTable(data.xsZzyspbs) : ''}
            `;

            container.html(contentHtml);
        }

        // 渲染专业行
        function renderMajorRows(majors) {
            if (!majors || majors.length === 0) {
                return '<tr><td colspan="9" style="text-align: center; color: var(--text-disabled);">暂无专业信息</td></tr>';
            }

            let rowsHtml = '';
            majors.forEach(major => {
                rowsHtml += `
                    <tr>
                        <td>${major[9] || ''}</td>
                        <td>${major[0] || ''}</td>
                        <td>${major[1] || ''}</td>
                        <td>${major[2] || ''}</td>
                        <td>${major[4] || ''}</td>
                        <td>${major[11] || ''}</td>
                        <td>${major[5] || ''}</td>
                        <td>${major[6] || ''}</td>
                        ${yxckpm ? `<td>${major[8] || ''}</td>` : ''}
                    </tr>
                `;
            });

            return rowsHtml;
        }

        // 渲染操作按钮
        function renderActionButtons(data) {
            const sqbh = currentBatch;
            const status = data.xsZzySqb.sqztdm;
            const zzypch = data.xsZzySqb.zzypch;

            let buttonsHtml = '<div class="action-buttons">';

            if (status === '00') {
                // 暂存状态
                buttonsHtml += `
                    <button class="action-btn edit" onclick="updateApplication('${sqbh}');">
                        <i class="ace-icon fa fa-edit"></i>
                        修改
                    </button>
                    <button class="action-btn submit" onclick="submitApplication('${sqbh}', '${zzypch}');">
                        <i class="ace-icon fa fa-check"></i>
                        提交
                    </button>
                    <button class="action-btn delete" onclick="deleteApplication('${sqbh}');">
                        <i class="ace-icon fa fa-trash"></i>
                        撤销
                    </button>
                `;
            } else {
                // 其他状态
                if (data.xsckkz === 1) {
                    buttonsHtml += `
                        <button class="action-btn print" onclick="printApplication('${sqbh}');">
                            <i class="ace-icon fa fa-print"></i>
                            打印
                        </button>
                    `;
                }
            }

            buttonsHtml += '</div>';

            return buttonsHtml;
        }

        // 渲染审批表格
        function renderApprovalTable(approvals) {
            if (!approvals || approvals.length === 0) {
                return '';
            }

            let tableHtml = `
                <table class="approval-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>审批环节</th>
                            <th>审批院系</th>
                            <th>审批人</th>
                            <th>审批结果</th>
                            <th>审批意见</th>
                            <th>审批时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            approvals.forEach((approval, index) => {
                tableHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${approval[0] || ''}</td>
                        <td>${approval[2] || ''}</td>
                        <td>${approval[4] || ''}</td>
                        <td>${approval[8] || ''}</td>
                        <td title="${approval[7] || ''}">${approval[7] || ''}</td>
                        <td>${approval[5] || ''}</td>
                    </tr>
                `;
            });

            tableHtml += '</tbody></table>';

            return tableHtml;
        }

        // 获取状态徽章
        function getStatusBadge(status) {
            const statusMap = {
                '00': { text: '暂存', class: 'status-draft' },
                '01': { text: '待审批', class: 'status-pending' },
                '02': { text: '同意转出', class: 'status-approved' },
                '03': { text: '面试', class: 'status-pending' },
                '04': { text: '建议录取', class: 'status-approved' },
                '05': { text: '拟录取', class: 'status-approved' },
                '06': { text: '完成', class: 'status-completed' },
                '99': { text: '终止', class: 'status-rejected' },
                '100': { text: '作废', class: 'status-rejected' }
            };

            const statusInfo = statusMap[status] || { text: '未知', class: 'status-pending' };
            return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
        }

        // 添加申请
        function addApplication() {
            const url = "/student/personalManagement/studentChange/specialtiesInfo";

            if (parent && parent.addTab) {
                parent.addTab('查看可转入专业', url);
            } else {
                window.location.href = url;
            }
        }

        // 修改申请
        function updateApplication(sqbh) {
            const url = `/student/personalManagement/studentChange/specialtiesInfo?sqbh=${sqbh}`;

            if (parent && parent.addTab) {
                parent.addTab('修改申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 提交申请
        function submitApplication(sqbh, zzypch) {
            if (confirm('提交后不可修改，是否确认提交？')) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/studentChange/addSpecialtiesInfo/edit",
                    type: "post",
                    data: {
                        sqbh: sqbh,
                        zzypch: zzypch,
                        flag: '01',
                        tokenValue: $('#tokenValue').val()
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response && response.data) {
                            $('#tokenValue').val(response.data.token);

                            if (response.data.result.indexOf("/") !== -1) {
                                window.location.href = response.data.result;
                            } else if (response.data.result === "ok") {
                                showSuccess("提交成功！");
                                loadApplicationInfo(sqbh);
                            } else {
                                showError(response.data.result);
                            }
                        } else {
                            showError(response.msg || "提交失败");
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:提交失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 删除申请
        function deleteApplication(sqbh) {
            if (confirm('确定要撤回这条记录？')) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/studentChange/specialtiesApply/deleteSpecialties",
                    type: "post",
                    data: {
                        sqbh: sqbh,
                        tokenValue: $('#tokenValue').val()
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response && response.data) {
                            $('#tokenValue').val(response.data.token);

                            if (response.data.result.indexOf("/") !== -1) {
                                window.location.href = response.data.result;
                            } else if (response.data.result === "ok") {
                                showSuccess("申请撤销成功！");
                                window.location.reload();
                            } else {
                                showError(response.data.result);
                            }
                        } else {
                            showError(response.msg || "删除失败");
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:删除失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 打印申请
        function printApplication(sqbh) {
            const url = `/student/personalManagement/studentChange/printSpecialtiesInfo/${sqbh}`;

            if (parent && parent.addTab) {
                parent.addTab('打印申请表', url);
            } else {
                window.open(url, '_blank');
            }
        }

        // 刷新数据
        function refreshData() {
            if (currentBatch) {
                loadApplicationInfo(currentBatch);
            } else {
                window.location.reload();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
