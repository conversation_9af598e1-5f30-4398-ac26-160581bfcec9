<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证办理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证办理页面样式 */
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .card-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .status-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .current-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-h3);
            margin-right: var(--margin-md);
        }
        
        .card-info {
            flex: 1;
        }
        
        .card-number {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .card-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .card-status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-valid {
            background: var(--success-color);
            color: white;
        }
        
        .status-expired {
            background: var(--error-color);
            color: white;
        }
        
        .status-lost {
            background: var(--warning-color);
            color: white;
        }
        
        .service-menu {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .menu-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .menu-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .service-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .service-item:last-child {
            border-bottom: none;
        }
        
        .service-item:active {
            background: var(--bg-color-active);
        }
        
        .service-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-md);
        }
        
        .service-icon.new {
            background: var(--success-color);
        }
        
        .service-icon.replace {
            background: var(--warning-color);
        }
        
        .service-icon.report {
            background: var(--error-color);
        }
        
        .service-icon.renew {
            background: var(--info-color);
        }
        
        .service-content {
            flex: 1;
        }
        
        .service-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .service-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .service-arrow {
            color: var(--text-disabled);
            font-size: var(--font-size-base);
        }
        
        .application-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .history-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .history-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .application-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .application-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证办理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="card-header">
            <div class="header-title">学生证办理</div>
            <div class="header-subtitle">办理学生证相关业务</div>
        </div>

        <!-- 学生证状态 -->
        <div class="card-status">
            <div class="status-title">
                <i class="ace-icon fa fa-id-card"></i>
                <span>当前学生证</span>
            </div>

            <div class="current-card" id="currentCard">
                <!-- 学生证信息将动态填充 -->
            </div>
        </div>

        <!-- 服务菜单 -->
        <div class="service-menu">
            <div class="menu-header">
                <i class="ace-icon fa fa-cogs"></i>
                <span>办理服务</span>
            </div>

            <div class="service-item" onclick="showApplicationForm('new')">
                <div class="service-icon new">
                    <i class="ace-icon fa fa-plus"></i>
                </div>
                <div class="service-content">
                    <div class="service-name">新办学生证</div>
                    <div class="service-desc">首次申请办理学生证</div>
                </div>
                <div class="service-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="service-item" onclick="showApplicationForm('replace')">
                <div class="service-icon replace">
                    <i class="ace-icon fa fa-exchange"></i>
                </div>
                <div class="service-content">
                    <div class="service-name">补办学生证</div>
                    <div class="service-desc">学生证丢失或损坏补办</div>
                </div>
                <div class="service-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="service-item" onclick="showApplicationForm('report')">
                <div class="service-icon report">
                    <i class="ace-icon fa fa-exclamation-triangle"></i>
                </div>
                <div class="service-content">
                    <div class="service-name">挂失学生证</div>
                    <div class="service-desc">学生证丢失挂失处理</div>
                </div>
                <div class="service-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="service-item" onclick="showApplicationForm('renew')">
                <div class="service-icon renew">
                    <i class="ace-icon fa fa-refresh"></i>
                </div>
                <div class="service-content">
                    <div class="service-name">续期学生证</div>
                    <div class="service-desc">学生证到期续期办理</div>
                </div>
                <div class="service-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>
        </div>

        <!-- 申请历史 -->
        <div class="application-history">
            <div class="history-header">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>申请历史</span>
                </div>
                <div class="history-count" id="historyCount">0</div>
            </div>

            <div id="historyItems">
                <!-- 申请历史将动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 申请表单 -->
    <div class="application-form" id="applicationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeApplicationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">学生证申请</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业</div>
                    <input type="text" class="form-input" id="major" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">班级</div>
                    <input type="text" class="form-input" id="className" readonly>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">申请类型</div>
                    <select class="form-input" id="applicationType">
                        <option value="">请选择申请类型</option>
                        <option value="new">新办学生证</option>
                        <option value="replace">补办学生证</option>
                        <option value="report">挂失学生证</option>
                        <option value="renew">续期学生证</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">申请原因</div>
                    <textarea class="form-input form-textarea" id="reason" placeholder="请详细说明申请原因"></textarea>
                </div>

                <div class="form-group" id="lostDateGroup" style="display: none;">
                    <div class="form-label">丢失日期</div>
                    <input type="date" class="form-input" id="lostDate">
                </div>

                <div class="form-group" id="lostLocationGroup" style="display: none;">
                    <div class="form-label">丢失地点</div>
                    <input type="text" class="form-input" id="lostLocation" placeholder="请输入丢失地点">
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">备注</div>
                    <textarea class="form-input form-textarea" id="note" placeholder="其他需要说明的情况（选填）"></textarea>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeApplicationForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let currentCardData = {};
        let applicationHistory = [];
        let currentApplicationType = '';

        $(function() {
            initPage();
            loadCardData();
            loadApplicationHistory();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 申请类型变化事件
            $('#applicationType').change(function() {
                const type = $(this).val();
                toggleFormFields(type);
            });
        }

        // 加载学生证数据
        function loadCardData() {
            showLoading(true);

            $.ajax({
                url: "/student/service/studentCard/getCardData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    currentCardData = data.cardData || {};
                    renderCurrentCard();
                    showLoading(false);
                },
                error: function() {
                    showError('加载学生证信息失败');
                    showLoading(false);
                }
            });
        }

        // 渲染当前学生证
        function renderCurrentCard() {
            let cardHtml = '';

            if (currentCardData.cardNumber) {
                const status = getCardStatus(currentCardData);
                const statusClass = getCardStatusClass(status);
                const statusText = getCardStatusText(status);

                cardHtml = `
                    <div class="card-icon">
                        <i class="ace-icon fa fa-id-card"></i>
                    </div>
                    <div class="card-info">
                        <div class="card-number">学生证号：${currentCardData.cardNumber}</div>
                        <div class="card-meta">
                            发证日期：${formatDate(currentCardData.issueDate)}<br>
                            有效期至：${formatDate(currentCardData.expireDate)}<br>
                            发证机构：${currentCardData.issueOrg || '学生处'}
                        </div>
                    </div>
                    <div class="card-status-badge ${statusClass}">${statusText}</div>
                `;
            } else {
                cardHtml = `
                    <div class="card-icon">
                        <i class="ace-icon fa fa-id-card-o"></i>
                    </div>
                    <div class="card-info">
                        <div class="card-number">暂无学生证</div>
                        <div class="card-meta">
                            您还没有学生证，请申请新办学生证
                        </div>
                    </div>
                    <div class="card-status-badge status-expired">未办理</div>
                `;
            }

            $('#currentCard').html(cardHtml);
        }

        // 获取学生证状态
        function getCardStatus(cardData) {
            if (!cardData.cardNumber) return 'none';
            if (cardData.isLost) return 'lost';

            const now = new Date();
            const expireDate = new Date(cardData.expireDate);

            if (expireDate < now) return 'expired';
            return 'valid';
        }

        // 获取状态样式类
        function getCardStatusClass(status) {
            switch(status) {
                case 'valid': return 'status-valid';
                case 'expired': return 'status-expired';
                case 'lost': return 'status-lost';
                default: return 'status-expired';
            }
        }

        // 获取状态文本
        function getCardStatusText(status) {
            switch(status) {
                case 'valid': return '有效';
                case 'expired': return '已过期';
                case 'lost': return '已挂失';
                default: return '未办理';
            }
        }

        // 加载申请历史
        function loadApplicationHistory() {
            $.ajax({
                url: "/student/service/studentCard/getApplicationHistory",
                type: "post",
                dataType: "json",
                success: function(data) {
                    applicationHistory = data.applications || [];
                    renderApplicationHistory();
                },
                error: function() {
                    console.log('加载申请历史失败');
                }
            });
        }

        // 渲染申请历史
        function renderApplicationHistory() {
            $('#historyCount').text(applicationHistory.length);

            const container = $('#historyItems');
            container.empty();

            if (applicationHistory.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无申请记录
                    </div>
                `);
                return;
            }

            applicationHistory.forEach(application => {
                const historyHtml = createHistoryItem(application);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(application) {
            const status = application.status || 'pending';
            const statusClass = getApplicationStatusClass(status);
            const statusText = getApplicationStatusText(status);

            return `
                <div class="history-item ${status}" onclick="showApplicationDetail('${application.id}')">
                    <div class="history-basic">
                        <div class="history-type">${getApplicationTypeText(application.type)}</div>
                        <div class="history-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(application.applyTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>处理时间:</span>
                            <span>${formatDate(application.processTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>申请原因:</span>
                            <span>${application.reason || '-'}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>联系电话:</span>
                            <span>${application.phone || '-'}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取申请状态样式类
        function getApplicationStatusClass(status) {
            return `status-${status}`;
        }

        // 获取申请状态文本
        function getApplicationStatusText(status) {
            switch(status) {
                case 'pending': return '审核中';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                case 'completed': return '已完成';
                default: return '未知';
            }
        }

        // 获取申请类型文本
        function getApplicationTypeText(type) {
            switch(type) {
                case 'new': return '新办学生证';
                case 'replace': return '补办学生证';
                case 'report': return '挂失学生证';
                case 'renew': return '续期学生证';
                default: return '其他申请';
            }
        }

        // 显示申请表单
        function showApplicationForm(type) {
            currentApplicationType = type;

            // 设置表单标题
            $('#formTitle').text(getApplicationTypeText(type));

            // 设置申请类型
            $('#applicationType').val(type);

            // 切换表单字段
            toggleFormFields(type);

            // 填充基本信息
            fillBasicInfo();

            // 显示表单
            $('#applicationForm').addClass('show');
        }

        // 切换表单字段
        function toggleFormFields(type) {
            // 隐藏所有条件字段
            $('#lostDateGroup, #lostLocationGroup').hide();

            // 根据类型显示相应字段
            if (type === 'replace' || type === 'report') {
                $('#lostDateGroup, #lostLocationGroup').show();
            }
        }

        // 填充基本信息
        function fillBasicInfo() {
            // 这里应该从用户信息中获取，暂时使用模拟数据
            $('#studentId').val('2021001001');
            $('#studentName').val('张三');
            $('#major').val('计算机科学与技术');
            $('#className').val('计科2021-1班');
        }

        // 关闭申请表单
        function closeApplicationForm() {
            $('#applicationForm').removeClass('show');
            clearForm();
        }

        // 清空表单
        function clearForm() {
            $('#applicationType').val('');
            $('#reason').val('');
            $('#lostDate').val('');
            $('#lostLocation').val('');
            $('#phone').val('');
            $('#note').val('');
            toggleFormFields('');
        }

        // 提交申请
        function submitApplication() {
            const formData = {
                type: $('#applicationType').val(),
                reason: $('#reason').val().trim(),
                lostDate: $('#lostDate').val(),
                lostLocation: $('#lostLocation').val().trim(),
                phone: $('#phone').val().trim(),
                note: $('#note').val().trim()
            };

            // 验证表单
            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要提交${getApplicationTypeText(formData.type)}申请吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitApplication(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitApplication(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.type) {
                showError('请选择申请类型');
                return false;
            }

            if (!formData.reason) {
                showError('请填写申请原因');
                return false;
            }

            if (!formData.phone) {
                showError('请填写联系电话');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            // 如果是补办或挂失，需要填写丢失信息
            if ((formData.type === 'replace' || formData.type === 'report') && !formData.lostDate) {
                showError('请选择丢失日期');
                return false;
            }

            return true;
        }

        // 执行提交申请
        function doSubmitApplication(formData) {
            $.ajax({
                url: "/student/service/studentCard/submitApplication",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('申请提交成功，请等待审核');
                        closeApplicationForm();
                        loadApplicationHistory(); // 重新加载申请历史
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示申请详情
        function showApplicationDetail(applicationId) {
            const application = applicationHistory.find(app => app.id === applicationId);
            if (!application) return;

            let message = `申请详情\n\n`;
            message += `申请类型：${getApplicationTypeText(application.type)}\n`;
            message += `申请状态：${getApplicationStatusText(application.status)}\n`;
            message += `申请时间：${formatDate(application.applyTime)}\n`;
            message += `申请原因：${application.reason}\n`;
            message += `联系电话：${application.phone}\n`;

            if (application.lostDate) {
                message += `丢失日期：${formatDate(application.lostDate)}\n`;
            }

            if (application.lostLocation) {
                message += `丢失地点：${application.lostLocation}\n`;
            }

            if (application.processTime) {
                message += `处理时间：${formatDate(application.processTime)}\n`;
            }

            if (application.processComment) {
                message += `处理意见：${application.processComment}\n`;
            }

            if (application.note) {
                message += `备注：${application.note}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadCardData();
            loadApplicationHistory();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
