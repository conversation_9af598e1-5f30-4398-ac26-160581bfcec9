<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>修改个人信息</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 个人信息修改页面样式 */
        .form-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .section-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            display: block;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-control {
            width: 100%;
            min-height: 44px;
            padding: 12px 16px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color var(--transition-base);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-control:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .form-control.readonly {
            background: var(--bg-secondary);
            border-color: var(--border-secondary);
            color: var(--text-secondary);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-row {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .form-help {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            margin-top: var(--margin-xs);
            line-height: var(--line-height-base);
        }
        
        .form-error {
            font-size: var(--font-size-mini);
            color: var(--error-color);
            margin-top: var(--margin-xs);
            display: none;
        }
        
        .form-control.error {
            border-color: var(--error-color);
        }
        
        .form-control.error + .form-error {
            display: block;
        }
        
        .photo-upload {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .photo-preview {
            width: 80px;
            height: 100px;
            border-radius: 6px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-disabled);
            font-size: var(--font-size-small);
            text-align: center;
            border: 1px solid var(--border-primary);
            overflow: hidden;
        }
        
        .photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .photo-actions {
            flex: 1;
        }
        
        .btn-upload {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            margin-bottom: var(--margin-xs);
            display: block;
            width: 100%;
            transition: all var(--transition-base);
        }
        
        .btn-upload:hover {
            background: var(--primary-dark);
        }
        
        .upload-help {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            line-height: var(--line-height-base);
        }
        
        .action-buttons {
            padding: var(--padding-lg) var(--padding-md);
            display: flex;
            gap: var(--spacing-md);
            background: var(--bg-secondary);
            position: sticky;
            bottom: 0;
            z-index: 100;
        }
        
        .btn-save {
            background: var(--success-color);
            color: white;
        }
        
        .btn-reset {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-primary);
        }
        
        .validation-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--error-color);
            display: none;
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--error-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
        }
        
        .summary-list {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .summary-list li {
            margin-bottom: var(--margin-xs);
        }
        
        .progress-indicator {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .progress-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        
        .progress-step {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            position: relative;
            z-index: 2;
        }
        
        .progress-step.active {
            background: var(--primary-color);
            color: white;
        }
        
        .progress-step.completed {
            background: var(--success-color);
            color: white;
        }
        
        .progress-line {
            position: absolute;
            top: 50%;
            left: 16px;
            right: 16px;
            height: 2px;
            background: var(--bg-tertiary);
            z-index: 1;
        }
        
        .progress-line::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }
        
        .progress-line.step-1::after { width: 0%; }
        .progress-line.step-2::after { width: 50%; }
        .progress-line.step-3::after { width: 100%; }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">修改个人信息</div>
            <div class="navbar-action" onclick="showHelp();">
                <i class="ace-icon fa fa-question-circle"></i>
            </div>
        </nav>

        <!-- 进度指示器 -->
        <div class="progress-indicator">
            <div class="progress-title">信息完善进度</div>
            <div class="progress-steps">
                <div class="progress-line step-1" id="progressLine"></div>
                <div class="progress-step completed" id="step1">1</div>
                <div class="progress-step active" id="step2">2</div>
                <div class="progress-step" id="step3">3</div>
            </div>
        </div>

        <!-- 验证错误汇总 -->
        <div class="validation-summary" id="validationSummary">
            <div class="summary-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>请修正以下错误</span>
            </div>
            <ul class="summary-list" id="errorList">
                <!-- 错误列表 -->
            </ul>
        </div>

        <!-- 基本信息 -->
        <div class="form-section">
            <div class="section-header">
                <i class="ace-icon fa fa-user"></i>
                <span>基本信息</span>
            </div>
            <div class="form-content">
                <div class="form-group">
                    <label class="form-label">姓名</label>
                    <input type="text" class="form-control readonly" id="studentName" readonly>
                    <div class="form-help">姓名信息不可修改，如需修改请联系教务处</div>
                </div>

                <div class="form-group">
                    <label class="form-label">学号</label>
                    <input type="text" class="form-control readonly" id="studentId" readonly>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">性别</label>
                        <select class="form-control" id="gender" disabled>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                        <div class="form-help">性别信息不可修改</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">出生日期</label>
                        <input type="date" class="form-control readonly" id="birthday" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">身份证号</label>
                    <input type="text" class="form-control readonly" id="idCard" readonly>
                    <div class="form-help">身份证号不可修改，如有错误请联系教务处</div>
                </div>
            </div>
        </div>

        <!-- 联系信息 -->
        <div class="form-section">
            <div class="section-header">
                <i class="ace-icon fa fa-phone"></i>
                <span>联系信息</span>
            </div>
            <div class="form-content">
                <div class="form-group">
                    <label class="form-label required">手机号码</label>
                    <input type="tel" class="form-control" id="mobile" placeholder="请输入11位手机号码">
                    <div class="form-error">请输入正确的手机号码</div>
                    <div class="form-help">用于接收重要通知，请确保号码正确</div>
                </div>

                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" class="form-control" id="email" placeholder="请输入邮箱地址">
                    <div class="form-error">请输入正确的邮箱地址</div>
                    <div class="form-help">用于接收学校邮件通知</div>
                </div>

                <div class="form-group">
                    <label class="form-label">QQ号码</label>
                    <input type="text" class="form-control" id="qq" placeholder="请输入QQ号码">
                    <div class="form-error">请输入正确的QQ号码</div>
                </div>

                <div class="form-group">
                    <label class="form-label">微信号</label>
                    <input type="text" class="form-control" id="wechat" placeholder="请输入微信号">
                </div>
            </div>
        </div>

        <!-- 家庭信息 -->
        <div class="form-section">
            <div class="section-header">
                <i class="ace-icon fa fa-home"></i>
                <span>家庭信息</span>
            </div>
            <div class="form-content">
                <div class="form-group">
                    <label class="form-label">家庭住址</label>
                    <textarea class="form-control form-textarea" id="homeAddress" placeholder="请输入详细的家庭住址"></textarea>
                    <div class="form-help">请填写详细地址，包括省市区县及门牌号</div>
                </div>

                <div class="form-group">
                    <label class="form-label">邮政编码</label>
                    <input type="text" class="form-control" id="zipCode" placeholder="请输入邮政编码">
                    <div class="form-error">请输入正确的邮政编码</div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">家长姓名</label>
                        <input type="text" class="form-control" id="parentName" placeholder="请输入家长姓名">
                    </div>

                    <div class="form-group">
                        <label class="form-label">家长电话</label>
                        <input type="tel" class="form-control" id="parentPhone" placeholder="请输入家长电话">
                        <div class="form-error">请输入正确的电话号码</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">紧急联系人</label>
                    <input type="text" class="form-control" id="emergencyContact" placeholder="请输入紧急联系人姓名">
                </div>

                <div class="form-group">
                    <label class="form-label">紧急联系电话</label>
                    <input type="tel" class="form-control" id="emergencyPhone" placeholder="请输入紧急联系电话">
                    <div class="form-error">请输入正确的电话号码</div>
                </div>
            </div>
        </div>

        <!-- 照片信息 -->
        <div class="form-section">
            <div class="section-header">
                <i class="ace-icon fa fa-camera"></i>
                <span>照片信息</span>
            </div>
            <div class="form-content">
                <div class="form-group">
                    <label class="form-label">学籍照片</label>
                    <div class="photo-upload">
                        <div class="photo-preview" id="photoPreview">
                            <span>暂无照片</span>
                        </div>
                        <div class="photo-actions">
                            <button class="btn-upload" onclick="uploadPhoto();">上传照片</button>
                            <div class="upload-help">
                                支持JPG、PNG格式<br>
                                文件大小不超过2MB<br>
                                建议尺寸：295×413像素
                            </div>
                        </div>
                    </div>
                    <input type="file" id="photoFile" accept="image/*" style="display: none;" onchange="handlePhotoSelect(event);">
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn-mobile btn-cancel flex-1" onclick="goBack();">取消</button>
            <button class="btn-mobile btn-reset flex-1" onclick="resetForm();">重置</button>
            <button class="btn-mobile btn-save flex-1" onclick="saveInfo();">保存</button>
        </div>
    </div>

    <script>
        // 全局变量
        let originalData = {};
        let currentData = {};
        let hasChanges = false;

        $(function() {
            initPage();
            loadStudentInfo();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 监听表单变化
            $('.form-control').on('input change', function() {
                checkFormChanges();
                validateField($(this));
            });

            // 阻止页面刷新时丢失数据
            $(window).on('beforeunload', function(e) {
                if (hasChanges) {
                    const message = '您有未保存的修改，确定要离开吗？';
                    e.returnValue = message;
                    return message;
                }
            });
        }

        // 加载学生信息
        function loadStudentInfo() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/personalInfoUpdate/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data && data.data) {
                        originalData = data.data;
                        currentData = JSON.parse(JSON.stringify(originalData));
                        populateForm(originalData);
                        updateProgress();
                    } else {
                        showError("获取学生信息失败");
                    }
                },
                error: function(xhr) {
                    showError("网络错误，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 填充表单
        function populateForm(data) {
            $('#studentName').val(data.xm || '');
            $('#studentId').val(data.xh || '');
            $('#gender').val(data.xbm || '');
            $('#birthday').val(data.csrq || '');
            $('#idCard').val(data.sfzh || '');

            $('#mobile').val(data.sjhm || '');
            $('#email').val(data.dzyx || '');
            $('#qq').val(data.qq || '');
            $('#wechat').val(data.wx || '');

            $('#homeAddress').val(data.jtdz || '');
            $('#zipCode').val(data.yzbm || '');
            $('#parentName').val(data.jzxm || '');
            $('#parentPhone').val(data.jzdh || '');
            $('#emergencyContact').val(data.jjlxr || '');
            $('#emergencyPhone').val(data.jjlxdh || '');

            // 加载照片
            if (data.zp) {
                loadPhoto(data.xh);
            }
        }

        // 加载照片
        function loadPhoto(studentId) {
            const photoUrl = `/student/personalManagement/personalInfoUpdate/getPhoto?xh=${studentId}`;
            const img = new Image();

            img.onload = function() {
                $('#photoPreview').html(`<img src="${photoUrl}" alt="学籍照片">`);
            };

            img.onerror = function() {
                $('#photoPreview').html('<span>暂无照片</span>');
            };

            img.src = photoUrl;
        }

        // 检查表单变化
        function checkFormChanges() {
            const formData = getFormData();
            hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);

            // 更新进度
            updateProgress();
        }

        // 获取表单数据
        function getFormData() {
            return {
                sjhm: $('#mobile').val(),
                dzyx: $('#email').val(),
                qq: $('#qq').val(),
                wx: $('#wechat').val(),
                jtdz: $('#homeAddress').val(),
                yzbm: $('#zipCode').val(),
                jzxm: $('#parentName').val(),
                jzdh: $('#parentPhone').val(),
                jjlxr: $('#emergencyContact').val(),
                jjlxdh: $('#emergencyPhone').val()
            };
        }

        // 验证字段
        function validateField($field) {
            const fieldId = $field.attr('id');
            const value = $field.val().trim();
            let isValid = true;

            // 清除之前的错误状态
            $field.removeClass('error');

            switch(fieldId) {
                case 'mobile':
                    if (value && !/^1[3-9]\d{9}$/.test(value)) {
                        isValid = false;
                    }
                    break;
                case 'email':
                    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                        isValid = false;
                    }
                    break;
                case 'qq':
                    if (value && !/^\d{5,11}$/.test(value)) {
                        isValid = false;
                    }
                    break;
                case 'zipCode':
                    if (value && !/^\d{6}$/.test(value)) {
                        isValid = false;
                    }
                    break;
                case 'parentPhone':
                case 'emergencyPhone':
                    if (value && !/^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/.test(value)) {
                        isValid = false;
                    }
                    break;
            }

            if (!isValid) {
                $field.addClass('error');
            }

            return isValid;
        }

        // 验证整个表单
        function validateForm() {
            let isValid = true;
            const errors = [];

            // 验证必填字段
            const mobile = $('#mobile').val().trim();
            if (!mobile) {
                errors.push('手机号码不能为空');
                $('#mobile').addClass('error');
                isValid = false;
            } else if (!validateField($('#mobile'))) {
                errors.push('手机号码格式不正确');
                isValid = false;
            }

            // 验证其他字段
            $('.form-control').each(function() {
                if (!validateField($(this))) {
                    isValid = false;
                }
            });

            // 显示错误汇总
            if (errors.length > 0) {
                showValidationErrors(errors);
            } else {
                hideValidationErrors();
            }

            return isValid;
        }

        // 显示验证错误
        function showValidationErrors(errors) {
            const errorList = $('#errorList');
            errorList.empty();

            errors.forEach(error => {
                errorList.append(`<li>${error}</li>`);
            });

            $('#validationSummary').show();

            // 滚动到错误提示
            $('html, body').animate({
                scrollTop: $('#validationSummary').offset().top - 70
            }, 300);
        }

        // 隐藏验证错误
        function hideValidationErrors() {
            $('#validationSummary').hide();
        }

        // 更新进度
        function updateProgress() {
            const formData = getFormData();
            let completedFields = 0;
            let totalFields = 0;

            // 计算完成度
            Object.keys(formData).forEach(key => {
                totalFields++;
                if (formData[key] && formData[key].trim()) {
                    completedFields++;
                }
            });

            const progress = Math.round((completedFields / totalFields) * 100);
            let step = 1;

            if (progress >= 30) step = 2;
            if (progress >= 70) step = 3;

            // 更新进度条
            $('#progressLine').removeClass('step-1 step-2 step-3').addClass(`step-${step}`);

            // 更新步骤状态
            $('.progress-step').removeClass('active completed');
            for (let i = 1; i <= 3; i++) {
                const $step = $(`#step${i}`);
                if (i < step) {
                    $step.addClass('completed');
                } else if (i === step) {
                    $step.addClass('active');
                }
            }
        }

        // 上传照片
        function uploadPhoto() {
            $('#photoFile').click();
        }

        // 处理照片选择
        function handlePhotoSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 验证文件类型
            if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
                showError('请选择JPG或PNG格式的图片');
                return;
            }

            // 验证文件大小
            if (file.size > 2 * 1024 * 1024) {
                showError('图片大小不能超过2MB');
                return;
            }

            // 预览图片
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#photoPreview').html(`<img src="${e.target.result}" alt="学籍照片">`);
                hasChanges = true;
            };
            reader.readAsDataURL(file);
        }

        // 保存信息
        function saveInfo() {
            if (!validateForm()) {
                return;
            }

            if (!hasChanges) {
                showSuccess('没有需要保存的修改');
                return;
            }

            const formData = getFormData();
            const photoFile = $('#photoFile')[0].files[0];

            // 创建FormData对象
            const submitData = new FormData();
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            if (photoFile) {
                submitData.append('photo', photoFile);
            }

            // 显示保存中状态
            const $saveBtn = $('.btn-save');
            const originalText = $saveBtn.text();
            $saveBtn.text('保存中...').prop('disabled', true);

            $.ajax({
                url: "/student/personalManagement/personalInfoUpdate/saveInfo",
                type: "post",
                data: submitData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('保存成功');
                        originalData = JSON.parse(JSON.stringify(formData));
                        hasChanges = false;
                        updateProgress();

                        // 可选择性返回上一页
                        setTimeout(() => {
                            if (typeof urp !== 'undefined' && urp.confirm) {
                                urp.confirm('信息保存成功，是否返回上一页？', function(confirmed) {
                                    if (confirmed) {
                                        goBack();
                                    }
                                });
                            }
                        }, 1000);
                    } else {
                        showError(data.message || '保存失败');
                    }
                },
                error: function(xhr) {
                    showError('网络错误，请重试');
                },
                complete: function() {
                    $saveBtn.text(originalText).prop('disabled', false);
                }
            });
        }

        // 重置表单
        function resetForm() {
            if (hasChanges) {
                if (typeof urp !== 'undefined' && urp.confirm) {
                    urp.confirm('确定要重置所有修改吗？', function(confirmed) {
                        if (confirmed) {
                            doResetForm();
                        }
                    });
                } else {
                    if (confirm('确定要重置所有修改吗？')) {
                        doResetForm();
                    }
                }
            } else {
                showSuccess('没有需要重置的修改');
            }
        }

        // 执行重置
        function doResetForm() {
            populateForm(originalData);
            hasChanges = false;
            hideValidationErrors();
            $('.form-control').removeClass('error');
            updateProgress();
            showSuccess('已重置为原始数据');
        }

        // 返回上一页
        function goBack() {
            if (hasChanges) {
                if (typeof urp !== 'undefined' && urp.confirm) {
                    urp.confirm('您有未保存的修改，确定要离开吗？', function(confirmed) {
                        if (confirmed) {
                            doGoBack();
                        }
                    });
                } else {
                    if (confirm('您有未保存的修改，确定要离开吗？')) {
                        doGoBack();
                    }
                }
            } else {
                doGoBack();
            }
        }

        // 执行返回
        function doGoBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else if (history.length > 1) {
                history.back();
            } else {
                window.location.href = '/student/personalManagement/rollInfo/index';
            }
        }

        // 显示帮助
        function showHelp() {
            const helpText = `
个人信息修改说明：

1. 基本信息（姓名、学号、性别、出生日期、身份证号）不可修改，如有错误请联系教务处。

2. 联系信息中的手机号码为必填项，用于接收重要通知。

3. 照片要求：
   - 支持JPG、PNG格式
   - 文件大小不超过2MB
   - 建议尺寸：295×413像素

4. 修改后请及时保存，避免数据丢失。

5. 如有疑问，请联系教务处或学生事务中心。
            `;

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(helpText);
            } else {
                alert(helpText);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                // 可以添加加载遮罩
            } else {
                // 隐藏加载遮罩
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const actionHeight = $('.action-buttons').outerHeight();
            const containerHeight = windowHeight - navbarHeight - actionHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
