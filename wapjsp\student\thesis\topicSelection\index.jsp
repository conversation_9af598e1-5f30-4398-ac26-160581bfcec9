<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>论文选题</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 论文选题页面样式 */
        .thesis-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .selection-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .status-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .current-selection {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
        }
        
        .selection-info {
            display: flex;
            align-items: center;
        }
        
        .selection-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-h4);
            margin-right: var(--margin-md);
        }
        
        .selection-content {
            flex: 1;
        }
        
        .selection-topic {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .selection-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .selection-status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-selected {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-none {
            background: var(--text-disabled);
            color: white;
        }
        
        .topic-filter {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .filter-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .filter-row {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .filter-input {
            flex: 1;
            min-height: 36px;
            padding: 6px 10px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .filter-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .btn-search {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
        }
        
        .filter-tags {
            display: flex;
            gap: var(--spacing-xs);
            flex-wrap: wrap;
        }
        
        .filter-tag {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 16px;
            padding: 4px 12px;
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-tag:hover {
            background: var(--primary-light);
        }
        
        .filter-tag.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .topic-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .topic-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .topic-item:last-child {
            border-bottom: none;
        }
        
        .topic-item:active {
            background: var(--bg-color-active);
        }
        
        .topic-item.available {
            border-left: 4px solid var(--success-color);
        }
        
        .topic-item.selected {
            border-left: 4px solid var(--primary-color);
        }
        
        .topic-item.unavailable {
            border-left: 4px solid var(--text-disabled);
            opacity: 0.6;
        }
        
        .topic-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .topic-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .topic-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-unavailable {
            background: var(--text-disabled);
            color: white;
        }
        
        .topic-teacher {
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 6px;
            padding: var(--padding-xs) var(--padding-sm);
            font-size: var(--font-size-small);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            display: inline-block;
        }
        
        .topic-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .topic-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .topic-description {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .topic-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-select {
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .topic-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .topic-detail-modal.show {
            display: flex;
        }
        
        .topic-detail-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .topic-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .topic-detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .topic-detail-close {
            color: var(--text-secondary);
            cursor: pointer;
            font-size: var(--font-size-h4);
        }
        
        .topic-detail-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .detail-section {
            margin-bottom: var(--margin-md);
        }
        
        .detail-section:last-child {
            margin-bottom: 0;
        }
        
        .detail-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">论文选题</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="thesis-header">
            <div class="header-title">论文选题</div>
            <div class="header-subtitle">选择毕业论文题目</div>
        </div>

        <!-- 选题状态 -->
        <div class="selection-status">
            <div class="status-title">
                <i class="ace-icon fa fa-check-circle"></i>
                <span>选题状态</span>
            </div>

            <div class="current-selection" id="currentSelection">
                <!-- 当前选题信息将动态填充 -->
            </div>
        </div>

        <!-- 题目筛选 -->
        <div class="topic-filter">
            <div class="filter-title">
                <i class="ace-icon fa fa-filter"></i>
                <span>题目筛选</span>
            </div>

            <div class="filter-row">
                <input type="text" class="filter-input" id="searchKeyword" placeholder="搜索题目关键词">
                <input type="text" class="filter-input" id="searchTeacher" placeholder="搜索指导教师">
                <button class="btn-search" onclick="searchTopics();">
                    <i class="ace-icon fa fa-search"></i>
                </button>
            </div>

            <div class="filter-tags">
                <div class="filter-tag active" data-type="all" onclick="filterByType('all')">全部题目</div>
                <div class="filter-tag" data-type="available" onclick="filterByType('available')">可选题目</div>
                <div class="filter-tag" data-type="selected" onclick="filterByType('selected')">已选题目</div>
                <div class="filter-tag" data-type="theory" onclick="filterByType('theory')">理论研究</div>
                <div class="filter-tag" data-type="practice" onclick="filterByType('practice')">实践应用</div>
                <div class="filter-tag" data-type="design" onclick="filterByType('design')">设计开发</div>
            </div>
        </div>

        <!-- 题目列表 -->
        <div class="topic-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span>论文题目</span>
                </div>
                <div class="list-count" id="topicCount">0</div>
            </div>

            <div id="topicItems">
                <!-- 题目列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div id="emptyMessage">暂无题目</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 题目详情模态框 -->
    <div class="topic-detail-modal" id="topicDetailModal">
        <div class="topic-detail-content">
            <div class="topic-detail-header">
                <div class="topic-detail-title" id="topicDetailTitle">题目详情</div>
                <div class="topic-detail-close" onclick="closeTopicDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="topic-detail-body" id="topicDetailBody">
                <!-- 题目详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allTopics = [];
        let filteredTopics = [];
        let currentSelection = null;
        let currentFilter = 'all';

        $(function() {
            initPage();
            loadSelectionStatus();
            loadTopics();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载选题状态
        function loadSelectionStatus() {
            $.ajax({
                url: "/student/thesis/topicSelection/getSelectionStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    currentSelection = data.selection;
                    renderSelectionStatus();
                },
                error: function() {
                    console.log('加载选题状态失败');
                }
            });
        }

        // 渲染选题状态
        function renderSelectionStatus() {
            let statusHtml = '';

            if (currentSelection && currentSelection.topicId) {
                const status = currentSelection.status || 'pending';
                const statusClass = getSelectionStatusClass(status);
                const statusText = getSelectionStatusText(status);

                statusHtml = `
                    <div class="selection-info">
                        <div class="selection-icon">
                            <i class="ace-icon fa fa-file-text"></i>
                        </div>
                        <div class="selection-content">
                            <div class="selection-topic">${currentSelection.topicTitle}</div>
                            <div class="selection-meta">
                                指导教师：${currentSelection.teacherName}<br>
                                选题时间：${formatDate(currentSelection.selectionTime)}<br>
                                题目类型：${getTopicTypeText(currentSelection.topicType)}
                            </div>
                        </div>
                        <div class="selection-status-badge ${statusClass}">${statusText}</div>
                    </div>
                `;
            } else {
                statusHtml = `
                    <div class="selection-info">
                        <div class="selection-icon">
                            <i class="ace-icon fa fa-file-text-o"></i>
                        </div>
                        <div class="selection-content">
                            <div class="selection-topic">尚未选题</div>
                            <div class="selection-meta">
                                请从下方题目列表中选择合适的论文题目
                            </div>
                        </div>
                        <div class="selection-status-badge status-none">未选择</div>
                    </div>
                `;
            }

            $('#currentSelection').html(statusHtml);
        }

        // 获取选题状态样式类
        function getSelectionStatusClass(status) {
            switch(status) {
                case 'confirmed': return 'status-selected';
                case 'pending': return 'status-pending';
                default: return 'status-none';
            }
        }

        // 获取选题状态文本
        function getSelectionStatusText(status) {
            switch(status) {
                case 'confirmed': return '已确认';
                case 'pending': return '待确认';
                default: return '未选择';
            }
        }

        // 获取题目类型文本
        function getTopicTypeText(type) {
            switch(type) {
                case 'theory': return '理论研究';
                case 'practice': return '实践应用';
                case 'design': return '设计开发';
                default: return '其他';
            }
        }

        // 加载题目列表
        function loadTopics() {
            showLoading(true);

            $.ajax({
                url: "/student/thesis/topicSelection/getTopics",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allTopics = data.topics || [];
                    filteredTopics = [...allTopics];

                    renderTopics();
                    showLoading(false);
                },
                error: function() {
                    showError('加载题目列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染题目列表
        function renderTopics() {
            $('#topicCount').text(filteredTopics.length);

            const container = $('#topicItems');
            container.empty();

            if (filteredTopics.length === 0) {
                showEmptyState('暂无符合条件的题目');
                return;
            } else {
                hideEmptyState();
            }

            filteredTopics.forEach(topic => {
                const topicHtml = createTopicItem(topic);
                container.append(topicHtml);
            });
        }

        // 创建题目项
        function createTopicItem(topic) {
            const isAvailable = topic.isAvailable !== false;
            const isSelected = currentSelection && currentSelection.topicId === topic.id;
            const statusClass = isSelected ? 'selected' : (isAvailable ? 'available' : 'unavailable');
            const statusText = isSelected ? '已选择' : (isAvailable ? '可选择' : '不可选');
            const statusBadgeClass = isSelected ? 'status-selected' : (isAvailable ? 'status-available' : 'status-unavailable');

            return `
                <div class="topic-item ${statusClass}" onclick="showTopicDetail('${topic.id}')">
                    <div class="topic-basic">
                        <div class="topic-title">${topic.title}</div>
                        <div class="topic-status ${statusBadgeClass}">${statusText}</div>
                    </div>
                    <div class="topic-teacher">指导教师：${topic.teacherName}</div>
                    <div class="topic-details">
                        <div class="topic-detail-item">
                            <span>题目类型:</span>
                            <span>${getTopicTypeText(topic.type)}</span>
                        </div>
                        <div class="topic-detail-item">
                            <span>研究方向:</span>
                            <span>${topic.direction || '-'}</span>
                        </div>
                        <div class="topic-detail-item">
                            <span>难度等级:</span>
                            <span>${getDifficultyText(topic.difficulty)}</span>
                        </div>
                        <div class="topic-detail-item">
                            <span>预计工作量:</span>
                            <span>${topic.workload || '-'}</span>
                        </div>
                    </div>
                    ${topic.description ? `<div class="topic-description">${topic.description}</div>` : ''}
                    <div class="topic-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showTopicDetail('${topic.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${isSelected ? `
                            <button class="btn-mobile btn-cancel" onclick="event.stopPropagation(); cancelSelection('${topic.id}');">
                                <i class="ace-icon fa fa-times"></i>
                                <span>取消选择</span>
                            </button>
                        ` : isAvailable ? `
                            <button class="btn-mobile btn-select" onclick="event.stopPropagation(); selectTopic('${topic.id}');">
                                <i class="ace-icon fa fa-check"></i>
                                <span>选择</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-ban"></i>
                                <span>不可选</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取难度等级文本
        function getDifficultyText(difficulty) {
            switch(difficulty) {
                case 'easy': return '简单';
                case 'medium': return '中等';
                case 'hard': return '困难';
                default: return '中等';
            }
        }

        // 搜索题目
        function searchTopics() {
            const keyword = $('#searchKeyword').val().trim();
            const teacher = $('#searchTeacher').val().trim();

            filteredTopics = allTopics.filter(topic => {
                let match = true;

                if (keyword) {
                    match = match && (
                        topic.title.toLowerCase().includes(keyword.toLowerCase()) ||
                        (topic.description && topic.description.toLowerCase().includes(keyword.toLowerCase()))
                    );
                }

                if (teacher) {
                    match = match && topic.teacherName.toLowerCase().includes(teacher.toLowerCase());
                }

                return match;
            });

            renderTopics();
        }

        // 按类型筛选
        function filterByType(type) {
            currentFilter = type;

            // 更新标签状态
            $('.filter-tag').removeClass('active');
            $(`.filter-tag[data-type="${type}"]`).addClass('active');

            // 应用筛选
            if (type === 'all') {
                filteredTopics = [...allTopics];
            } else if (type === 'available') {
                filteredTopics = allTopics.filter(topic => topic.isAvailable !== false);
            } else if (type === 'selected') {
                filteredTopics = allTopics.filter(topic =>
                    currentSelection && currentSelection.topicId === topic.id
                );
            } else {
                filteredTopics = allTopics.filter(topic => topic.type === type);
            }

            renderTopics();
        }

        // 显示题目详情
        function showTopicDetail(topicId) {
            const topic = allTopics.find(t => t.id === topicId);
            if (!topic) return;

            renderTopicDetail(topic);
        }

        // 渲染题目详情
        function renderTopicDetail(topic) {
            let detailHtml = `
                <div class="detail-section">
                    <div class="detail-section-title">基本信息</div>
                    <div class="detail-item">
                        <span class="detail-label">题目名称:</span>
                        <span class="detail-value">${topic.title}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">指导教师:</span>
                        <span class="detail-value">${topic.teacherName}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">题目类型:</span>
                        <span class="detail-value">${getTopicTypeText(topic.type)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">研究方向:</span>
                        <span class="detail-value">${topic.direction || '-'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">难度等级:</span>
                        <span class="detail-value">${getDifficultyText(topic.difficulty)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">预计工作量:</span>
                        <span class="detail-value">${topic.workload || '-'}</span>
                    </div>
                </div>
            `;

            if (topic.description) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">题目描述</div>
                        <div style="line-height: 1.6;">${topic.description}</div>
                    </div>
                `;
            }

            if (topic.requirements) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">基本要求</div>
                        <div style="line-height: 1.6;">${topic.requirements}</div>
                    </div>
                `;
            }

            if (topic.references) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">参考文献</div>
                        <div style="line-height: 1.6;">${topic.references}</div>
                    </div>
                `;
            }

            if (topic.teacherInfo) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">指导教师信息</div>
                        <div class="detail-item">
                            <span class="detail-label">职称:</span>
                            <span class="detail-value">${topic.teacherInfo.title || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">研究领域:</span>
                            <span class="detail-value">${topic.teacherInfo.field || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">联系方式:</span>
                            <span class="detail-value">${topic.teacherInfo.contact || '-'}</span>
                        </div>
                    </div>
                `;
            }

            $('#topicDetailTitle').text(`${topic.title} - 详情`);
            $('#topicDetailBody').html(detailHtml);
            $('#topicDetailModal').addClass('show');
        }

        // 关闭题目详情
        function closeTopicDetail() {
            $('#topicDetailModal').removeClass('show');
        }

        // 选择题目
        function selectTopic(topicId) {
            const topic = allTopics.find(t => t.id === topicId);
            if (!topic) return;

            if (!topic.isAvailable) {
                showError('该题目不可选择');
                return;
            }

            const message = `确定要选择"${topic.title}"作为论文题目吗？\n\n指导教师：${topic.teacherName}\n题目类型：${getTopicTypeText(topic.type)}`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSelectTopic(topicId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSelectTopic(topicId);
                }
            }
        }

        // 执行选择题目
        function doSelectTopic(topicId) {
            $.ajax({
                url: "/student/thesis/topicSelection/selectTopic",
                type: "post",
                data: { topicId: topicId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('题目选择成功');
                        loadSelectionStatus(); // 重新加载选题状态
                        loadTopics(); // 重新加载题目列表
                    } else {
                        showError(data.message || '题目选择失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 取消选择
        function cancelSelection(topicId) {
            const topic = allTopics.find(t => t.id === topicId);
            if (!topic) return;

            const message = `确定要取消选择"${topic.title}"吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doCancelSelection(topicId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doCancelSelection(topicId);
                }
            }
        }

        // 执行取消选择
        function doCancelSelection(topicId) {
            $.ajax({
                url: "/student/thesis/topicSelection/cancelSelection",
                type: "post",
                data: { topicId: topicId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('取消选择成功');
                        loadSelectionStatus(); // 重新加载选题状态
                        loadTopics(); // 重新加载题目列表
                    } else {
                        showError(data.message || '取消选择失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadSelectionStatus();
            loadTopics();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#topicDetailModal').click(function(e) {
            if (e.target === this) {
                closeTopicDetail();
            }
        });
    </script>
</body>
</html>
