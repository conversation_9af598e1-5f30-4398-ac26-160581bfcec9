<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>退课学分列表</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 退课学分列表页面样式 */
        .drop-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .drop-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .summary-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .summary-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .summary-number.total {
            color: var(--primary-color);
        }
        
        .summary-number.credits {
            color: var(--error-color);
        }
        
        .summary-number.refund {
            color: var(--success-color);
        }
        
        .summary-number.penalty {
            color: var(--warning-color);
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .filter-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .filter-row {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .filter-input {
            flex: 1;
            min-height: 36px;
            padding: 6px 10px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .filter-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .btn-search {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
        }
        
        .drop-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .drop-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .drop-item:last-child {
            border-bottom: none;
        }
        
        .drop-item:active {
            background: var(--bg-color-active);
        }
        
        .drop-item.refunded {
            border-left: 4px solid var(--success-color);
        }
        
        .drop-item.penalty {
            border-left: 4px solid var(--warning-color);
        }
        
        .drop-item.processing {
            border-left: 4px solid var(--info-color);
        }
        
        .drop-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .drop-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .drop-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-refunded {
            background: var(--success-color);
            color: white;
        }
        
        .status-penalty {
            background: var(--warning-color);
            color: white;
        }
        
        .status-processing {
            background: var(--info-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .drop-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .drop-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .drop-financial {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .financial-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .financial-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .financial-item {
            display: flex;
            justify-content: space-between;
        }
        
        .amount-positive {
            color: var(--success-color);
            font-weight: 500;
        }
        
        .amount-negative {
            color: var(--error-color);
            font-weight: 500;
        }
        
        .drop-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-appeal {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .semester-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-xs);
            overflow-x: auto;
        }
        
        .semester-tab {
            flex-shrink: 0;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
            white-space: nowrap;
        }
        
        .semester-tab.active {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">退课学分列表</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="drop-header">
            <div class="header-title">退课学分列表</div>
            <div class="header-subtitle">查看退课学分情况和费用明细</div>
        </div>
        
        <!-- 退课汇总 -->
        <div class="drop-summary">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>退课汇总</span>
            </div>
            
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-number total" id="totalCount">0</div>
                    <div class="summary-label">退课次数</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number credits" id="totalCredits">0</div>
                    <div class="summary-label">退课学分</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number refund" id="totalRefund">¥0</div>
                    <div class="summary-label">退费金额</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number penalty" id="totalPenalty">¥0</div>
                    <div class="summary-label">违约金</div>
                </div>
            </div>
        </div>
        
        <!-- 学期选择 -->
        <div class="semester-tabs" id="semesterTabs">
            <!-- 学期标签将动态填充 -->
        </div>
        
        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="ace-icon fa fa-filter"></i>
                <span>筛选条件</span>
            </div>
            
            <div class="filter-row">
                <input type="text" class="filter-input" id="courseFilter" placeholder="课程名称或代码">
                <select class="filter-input" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="refunded">已退费</option>
                    <option value="penalty">有违约金</option>
                    <option value="processing">处理中</option>
                    <option value="rejected">已拒绝</option>
                </select>
                <button class="btn-search" onclick="filterDropList();">
                    <i class="ace-icon fa fa-search"></i>
                </button>
            </div>
        </div>
        
        <!-- 退课列表 -->
        <div class="drop-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span id="listTitle">退课记录</span>
                </div>
                <div class="list-count" id="dropCount">0</div>
            </div>
            
            <div id="dropItems">
                <!-- 退课列表将动态填充 -->
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-list"></i>
            <div id="emptyMessage">暂无退课记录</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let allDropRecords = [];
        let filteredDropRecords = [];
        let semesterData = [];
        let currentSemester = '';
        let summaryData = {};

        $(function() {
            initPage();
            loadSummaryData();
            loadSemesterData();
            loadDropRecords();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载汇总数据
        function loadSummaryData() {
            $.ajax({
                url: "/student/dropCourseCreditList/getSummaryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    summaryData = data.summary || {};
                    renderSummaryData();
                },
                error: function() {
                    console.log('加载汇总数据失败');
                }
            });
        }

        // 渲染汇总数据
        function renderSummaryData() {
            $('#totalCount').text(summaryData.totalCount || 0);
            $('#totalCredits').text(summaryData.totalCredits || 0);
            $('#totalRefund').text('¥' + (summaryData.totalRefund || 0).toLocaleString());
            $('#totalPenalty').text('¥' + (summaryData.totalPenalty || 0).toLocaleString());
        }

        // 加载学期数据
        function loadSemesterData() {
            $.ajax({
                url: "/student/dropCourseCreditList/getSemesterData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    semesterData = data.semesters || [];
                    renderSemesterTabs();
                    
                    // 默认选择第一个学期
                    if (semesterData.length > 0) {
                        selectSemester(semesterData[0].code);
                    }
                },
                error: function() {
                    console.log('加载学期数据失败');
                }
            });
        }

        // 渲染学期标签
        function renderSemesterTabs() {
            const container = $('#semesterTabs');
            container.empty();
            
            // 添加全部学期选项
            container.append(`
                <div class="semester-tab active" data-semester="all" onclick="selectSemester('all')">
                    全部学期
                </div>
            `);
            
            semesterData.forEach(semester => {
                const tabHtml = `
                    <div class="semester-tab" data-semester="${semester.code}" onclick="selectSemester('${semester.code}')">
                        ${semester.name}
                    </div>
                `;
                container.append(tabHtml);
            });
        }

        // 选择学期
        function selectSemester(semesterCode) {
            currentSemester = semesterCode;
            
            // 更新标签状态
            $('.semester-tab').removeClass('active');
            $(`.semester-tab[data-semester="${semesterCode}"]`).addClass('active');
            
            // 更新列表标题
            const semesterName = semesterCode === 'all' ? '全部学期' : 
                (semesterData.find(s => s.code === semesterCode)?.name || '');
            $('#listTitle').text(`${semesterName} 退课记录`);
            
            // 过滤数据
            filterDropList();
        }

        // 加载退课记录
        function loadDropRecords() {
            showLoading(true);
            
            $.ajax({
                url: "/student/dropCourseCreditList/getDropRecords",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allDropRecords = data.records || [];
                    filteredDropRecords = [...allDropRecords];
                    renderDropList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载退课记录失败');
                    showLoading(false);
                }
            });
        }

        // 过滤退课列表
        function filterDropList() {
            const courseKeyword = $('#courseFilter').val().trim().toLowerCase();
            const statusFilter = $('#statusFilter').val();
            
            filteredDropRecords = allDropRecords.filter(record => {
                // 学期过滤
                if (currentSemester !== 'all' && record.semester !== currentSemester) {
                    return false;
                }
                
                // 课程名称过滤
                if (courseKeyword && 
                    !record.courseName.toLowerCase().includes(courseKeyword) &&
                    !record.courseCode.toLowerCase().includes(courseKeyword)) {
                    return false;
                }
                
                // 状态过滤
                if (statusFilter && record.status !== statusFilter) {
                    return false;
                }
                
                return true;
            });
            
            renderDropList();
        }

        // 渲染退课列表
        function renderDropList() {
            $('#dropCount').text(filteredDropRecords.length);
            
            const container = $('#dropItems');
            container.empty();
            
            if (filteredDropRecords.length === 0) {
                showEmptyState('暂无符合条件的退课记录');
                return;
            } else {
                hideEmptyState();
            }
            
            filteredDropRecords.forEach(record => {
                const recordHtml = createDropItem(record);
                container.append(recordHtml);
            });
        }

        // 创建退课项
        function createDropItem(record) {
            const status = record.status || 'processing';
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);
            
            return `
                <div class="drop-item ${status}" onclick="showDropDetail('${record.id}')">
                    <div class="drop-basic">
                        <div class="drop-course">${record.courseName}</div>
                        <div class="drop-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="drop-details">
                        <div class="drop-detail-item">
                            <span>课程代码:</span>
                            <span>${record.courseCode}</span>
                        </div>
                        <div class="drop-detail-item">
                            <span>学分:</span>
                            <span>${record.credits}</span>
                        </div>
                        <div class="drop-detail-item">
                            <span>退课时间:</span>
                            <span>${formatDate(record.dropTime)}</span>
                        </div>
                        <div class="drop-detail-item">
                            <span>学期:</span>
                            <span>${getSemesterName(record.semester)}</span>
                        </div>
                    </div>
                    <div class="drop-financial">
                        <div class="financial-title">费用明细</div>
                        <div class="financial-details">
                            <div class="financial-item">
                                <span>原学费:</span>
                                <span>¥${record.originalFee.toLocaleString()}</span>
                            </div>
                            <div class="financial-item">
                                <span>退费金额:</span>
                                <span class="${record.refundAmount > 0 ? 'amount-positive' : ''}">¥${record.refundAmount.toLocaleString()}</span>
                            </div>
                            <div class="financial-item">
                                <span>违约金:</span>
                                <span class="${record.penaltyAmount > 0 ? 'amount-negative' : ''}">¥${record.penaltyAmount.toLocaleString()}</span>
                            </div>
                            <div class="financial-item">
                                <span>实际退费:</span>
                                <span class="${record.actualRefund > 0 ? 'amount-positive' : record.actualRefund < 0 ? 'amount-negative' : ''}">¥${record.actualRefund.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                    <div class="drop-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showDropDetail('${record.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'rejected' ? `
                            <button class="btn-mobile btn-appeal" onclick="event.stopPropagation(); appealDrop('${record.id}');">
                                <i class="ace-icon fa fa-gavel"></i>
                                <span>申诉</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-check"></i>
                                <span>已处理</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'refunded': return '已退费';
                case 'penalty': return '有违约金';
                case 'processing': return '处理中';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 获取学期名称
        function getSemesterName(semesterCode) {
            const semester = semesterData.find(s => s.code === semesterCode);
            return semester ? semester.name : semesterCode;
        }

        // 显示退课详情
        function showDropDetail(recordId) {
            const record = allDropRecords.find(r => r.id === recordId);
            if (!record) return;
            
            let message = `退课详情\n\n`;
            message += `课程名称：${record.courseName}\n`;
            message += `课程代码：${record.courseCode}\n`;
            message += `学分：${record.credits}\n`;
            message += `学期：${getSemesterName(record.semester)}\n`;
            message += `退课时间：${formatDate(record.dropTime)}\n`;
            message += `状态：${getStatusText(record.status)}\n\n`;
            
            message += `费用明细：\n`;
            message += `原学费：¥${record.originalFee.toLocaleString()}\n`;
            message += `退费金额：¥${record.refundAmount.toLocaleString()}\n`;
            message += `违约金：¥${record.penaltyAmount.toLocaleString()}\n`;
            message += `实际退费：¥${record.actualRefund.toLocaleString()}\n`;
            
            if (record.reason) {
                message += `\n退课原因：${record.reason}\n`;
            }
            
            if (record.processComment) {
                message += `处理意见：${record.processComment}\n`;
            }
            
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 申诉退课
        function appealDrop(recordId) {
            const record = allDropRecords.find(r => r.id === recordId);
            if (!record) return;
            
            const reason = prompt(`请输入申诉理由：`);
            if (reason && reason.trim()) {
                $.ajax({
                    url: "/student/dropCourseCreditList/appealDrop",
                    type: "post",
                    data: { 
                        recordId: recordId,
                        reason: reason.trim()
                    },
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            showSuccess('申诉提交成功，请等待处理');
                            loadDropRecords(); // 重新加载数据
                        } else {
                            showError(data.message || '申诉提交失败');
                        }
                    },
                    error: function() {
                        showError('网络错误，请重试');
                    }
                });
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadSummaryData();
            loadSemesterData();
            loadDropRecords();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
