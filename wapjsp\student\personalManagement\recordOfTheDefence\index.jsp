<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>答辩记录</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 答辩记录页面样式 */
        .defence-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .defence-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .defence-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--primary-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .btn-add {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .records-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .records-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .records-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .records-title i {
            color: var(--success-color);
        }
        
        .record-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .record-item:last-child {
            border-bottom: none;
        }
        
        .record-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .record-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .record-content {
            flex: 1;
        }
        
        .record-title-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .record-student {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .record-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .file-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .file-link:hover {
            text-decoration: underline;
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-operation {
            flex: 1;
            min-width: 60px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-download {
            background: var(--success-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .file-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .file-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .file-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .file-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .file-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .file-body {
            height: calc(80vh - 60px);
            overflow: hidden;
        }
        
        .file-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        @media (max-width: 480px) {
            .search-actions {
                flex-direction: column;
            }
            
            .record-details {
                grid-template-columns: 1fr;
            }
            
            .operation-buttons {
                flex-direction: column;
            }
            
            .file-content {
                width: 95%;
                height: 90vh;
            }
            
            .file-body {
                height: calc(90vh - 60px);
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">答辩记录</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 答辩记录头部 -->
        <div class="defence-header">
            <div class="defence-title">答辩记录</div>
            <div class="defence-desc">管理答辩记录信息</div>
        </div>
        
        <!-- 查询条件 -->
        <div class="search-section">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form id="queryInfo" name="queryInfo" class="search-form">
                <div class="form-group">
                    <label class="form-label">批次</label>
                    <select name="pch" id="pch" class="form-select">
                        <option value="">全部</option>
                        <cache:query var="pcbs" fields="pch,pcmc" region="lw_pcb" orderby="zxjxjhh desc ,pch" time="100"/>
                        <c:forEach items="${pcbs}" var="pcb" varStatus="xnSta">
                            <option value="${pcb.pch}" <c:if test="${pcb.pch eq xnxq}">selected</c:if>>${pcb.pcmc}</option>
                        </c:forEach>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">题目名称</label>
                    <input type="text" name="tmmc" id="tmmc" class="form-input" placeholder="请输入题目名称">
                </div>
            </form>
            
            <div class="search-actions">
                <button class="btn-mobile btn-search" onclick="searchRecords();">
                    <i class="ace-icon fa fa-search"></i>
                    <span>查询</span>
                </button>
                <button class="btn-mobile btn-add" onclick="addRecord();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>新增</span>
                </button>
            </div>
        </div>
        
        <!-- 答辩记录列表 -->
        <div class="records-section">
            <div class="records-header">
                <div class="records-title">
                    <i class="ace-icon fa fa-list"></i>
                    答辩记录列表
                </div>
            </div>
            
            <div id="recordsList">
                <!-- 动态加载记录列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreRecords();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无答辩记录数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 文件查看模态框 -->
        <div class="file-modal" id="fileModal">
            <div class="file-content">
                <div class="file-header">
                    <div class="file-title">查看文件</div>
                    <button class="file-close" onclick="closeFileModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="file-body">
                    <iframe id="fileIframe" class="file-iframe"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let recordData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let searchParams = '';

        $(function() {
            initPage();
            loadRecords(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索记录
        function searchRecords() {
            loadRecords(1, true);
        }

        // 加载更多记录
        function loadMoreRecords() {
            if (hasMore) {
                loadRecords(currentPage + 1, false);
            }
        }

        // 加载记录数据
        function loadRecords(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
                searchParams = $('#queryInfo').serialize();
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/processManagement/recordOfTheDefence/getApplyList",
                type: "post",
                data: searchParams + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            recordData = data.records;
                        } else {
                            recordData = recordData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = recordData.length < totalCount;

                        renderRecordsList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            recordData = [];
                            renderRecordsList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染记录列表
        function renderRecordsList(reset = false) {
            const container = $('#recordsList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : recordData.length - pageSize;
            const endIndex = recordData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (recordData[i]) {
                    const itemHtml = createRecordItem(recordData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建记录项目HTML
        function createRecordItem(item, index) {
            // 获取状态信息
            const statusInfo = getStatusInfo(item.SHZT);

            // 构建操作按钮
            let operationButtons = '';
            if (item.WJMC) {
                operationButtons += `
                    <button class="btn-operation btn-view" onclick="viewFile('${item.WJID}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                    <button class="btn-operation btn-download" onclick="downloadFile('${item.WJID}');">
                        <i class="ace-icon fa fa-download"></i>
                        <span>下载</span>
                    </button>
                `;
            }

            if (item.SHZT === "待审核") {
                operationButtons += `
                    <button class="btn-operation btn-delete" onclick="deleteRecord('${item.WJID}');">
                        <i class="ace-icon fa fa-trash"></i>
                        <span>删除</span>
                    </button>
                `;
            }

            return `
                <div class="record-item">
                    <div class="record-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="record-index">${index + 1}</div>
                            <div class="record-content">
                                <div class="record-title-text">${item.TMMC || ''}</div>
                                <div class="record-student">${item.XM || ''}（${item.XH || ''}）</div>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                        </div>
                    </div>

                    <div class="record-details">
                        <div class="detail-item">
                            <span class="detail-label">批次</span>
                            <span>${item.PC || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">题目编号</span>
                            <span>${item.TMBH || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">文件名称</span>
                            <span>
                                ${item.WJMC ? `<a href="#" class="file-link" onclick="viewFile('${item.WJID}');">${item.WJMC}</a>` : ''}
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">上传人</span>
                            <span>${item.SCR || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">上传时间</span>
                            <span>${item.SCSJ || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">审核人</span>
                            <span>${item.SHR || ''}</span>
                        </div>
                        <div class="detail-item" style="grid-column: 1 / -1;">
                            <span class="detail-label">审核意见</span>
                            <span title="${item.SHYJ || ''}">${item.SHYJ || ''}</span>
                        </div>
                    </div>

                    ${operationButtons ? `
                    <div class="operation-buttons">
                        ${operationButtons}
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(shzt) {
            switch (shzt) {
                case "待审核": return { text: '待审核', class: 'status-pending' };
                case "审核通过": return { text: '审核通过', class: 'status-approved' };
                case "审核不通过": return { text: '审核不通过', class: 'status-rejected' };
                default: return { text: shzt || '', class: 'status-pending' };
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && recordData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (recordData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.records-section').hide();
            } else {
                $('#emptyState').hide();
                $('.records-section').show();
            }
        }

        // 新增记录
        function addRecord() {
            if (parent && parent.addTab) {
                parent.addTab('新增答辩记录', '/student/personalManagement/processManagement/recordOfTheDefence/addApply');
            } else {
                location.href = "/student/personalManagement/processManagement/recordOfTheDefence/addApply";
            }
        }

        // 查看文件
        function viewFile(wjid) {
            $('#fileIframe').attr('src', '/student/personalManagement/processManagement/recordOfTheDefence/view/' + wjid);
            $('#fileModal').fadeIn(300);
        }

        // 关闭文件查看模态框
        function closeFileModal() {
            $('#fileModal').fadeOut(300);
            $('#fileIframe').attr('src', '');
        }

        // 下载文件
        function downloadFile(wjid) {
            window.location.href = "/student/personalManagement/processManagement/recordOfTheDefence/downLoad/" + wjid;
        }

        // 删除记录
        function deleteRecord(wjid) {
            if (confirm("确定要删除记录？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/processManagement/recordOfTheDefence/revokeApply",
                    type: "post",
                    data: "id=" + wjid + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        if (response.status != 200) {
                            showError(response.msg);
                        } else {
                            const data = response.data;
                            $("#tokenValue").val(data.token);

                            if (data.result.indexOf("/logout") != -1) {
                                showError("页面已过期，请刷新页面！");
                            } else if (data.result === "ok") {
                                showSuccess("删除成功！", function() {
                                    loadRecords(1, true);
                                });
                            }
                        }
                    },
                    error: function(xhr) {
                        showError("撤销失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 刷新数据
        function refreshData() {
            loadRecords(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.file-modal', function(e) {
            if (e.target === this) {
                closeFileModal();
            }
        });
    </script>
</body>
</html>
