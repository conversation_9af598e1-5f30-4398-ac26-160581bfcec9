<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>消息展示</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 消息展示页面样式 */
        .message-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .message-filters {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-tabs {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .filter-tab {
            flex: 1;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            background: var(--bg-primary);
            color: var(--text-secondary);
        }
        
        .filter-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .message-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
        }
        
        .stat-item {
            text-align: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            background: var(--bg-tertiary);
        }
        
        .stat-number {
            font-size: var(--font-size-h5);
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: var(--margin-xs);
        }
        
        .stat-label {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .message-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .message-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            position: relative;
        }
        
        .message-item:last-child {
            border-bottom: none;
        }
        
        .message-item:active {
            background: var(--bg-color-active);
        }
        
        .message-item.unread {
            background: #f0f5ff;
            border-left: 3px solid var(--primary-color);
        }
        
        .message-item.important {
            background: #fff7e6;
            border-left: 3px solid var(--warning-color);
        }
        
        .message-item.urgent {
            background: #fff2f0;
            border-left: 3px solid var(--error-color);
        }
        
        .message-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .message-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: 1.4;
        }
        
        .message-time {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            white-space: nowrap;
        }
        
        .message-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: var(--margin-sm);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .message-sender {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .message-badges {
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .message-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .badge-unread {
            background: var(--primary-color);
            color: white;
        }
        
        .badge-important {
            background: var(--warning-color);
            color: white;
        }
        
        .badge-urgent {
            background: var(--error-color);
            color: white;
        }
        
        .badge-system {
            background: var(--info-color);
            color: white;
        }
        
        .message-actions {
            position: absolute;
            top: var(--padding-md);
            right: var(--padding-md);
            display: none;
            gap: var(--spacing-xs);
        }
        
        .message-item:hover .message-actions {
            display: flex;
        }
        
        .action-btn {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .action-read {
            background: var(--success-color);
            color: white;
        }
        
        .action-delete {
            background: var(--error-color);
            color: white;
        }
        
        .load-more {
            text-align: center;
            padding: var(--padding-md);
            color: var(--primary-color);
            cursor: pointer;
            border-top: 1px solid var(--divider-color);
        }
        
        .load-more:active {
            background: var(--bg-color-active);
        }
        
        @media (max-width: 480px) {
            .filter-tabs {
                flex-wrap: wrap;
            }
            
            .filter-tab {
                min-width: 80px;
            }
            
            .message-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .message-actions {
                position: static;
                display: flex;
                margin-top: var(--margin-sm);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">消息展示</div>
            <div class="navbar-action" onclick="refreshMessages();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="message-header">
            <div class="header-title">消息中心</div>
            <div class="header-subtitle">查看系统消息和通知</div>
        </div>
        
        <!-- 消息筛选 -->
        <div class="message-filters">
            <div class="filter-tabs">
                <div class="filter-tab active" data-type="all">全部</div>
                <div class="filter-tab" data-type="unread">未读</div>
                <div class="filter-tab" data-type="important">重要</div>
                <div class="filter-tab" data-type="system">系统</div>
            </div>
            
            <div class="message-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalCount">0</div>
                    <div class="stat-label">总消息</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="unreadCount">0</div>
                    <div class="stat-label">未读</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="importantCount">0</div>
                    <div class="stat-label">重要</div>
                </div>
            </div>
        </div>
        
        <!-- 消息列表 -->
        <div class="message-list" id="messageList">
            <!-- 消息项将动态填充 -->
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-envelope-o"></i>
            <div>暂无消息</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let messages = [];
        let currentFilter = 'all';
        let currentPage = 1;
        let pageSize = 20;
        let hasMore = true;

        $(function() {
            initPage();
            loadMessages();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 筛选标签点击
            $('.filter-tab').click(function() {
                const type = $(this).data('type');
                if (type !== currentFilter) {
                    $('.filter-tab').removeClass('active');
                    $(this).addClass('active');
                    currentFilter = type;
                    currentPage = 1;
                    hasMore = true;
                    loadMessages();
                }
            });
        }

        // 加载消息
        function loadMessages() {
            showLoading(true);
            
            $.ajax({
                url: "/student/messageDisplay/getMessages",
                type: "post",
                data: {
                    type: currentFilter,
                    page: currentPage,
                    pageSize: pageSize
                },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        if (currentPage === 1) {
                            messages = data.messages || [];
                        } else {
                            messages = messages.concat(data.messages || []);
                        }
                        
                        hasMore = data.hasMore || false;
                        updateStats(data.stats);
                        renderMessages();
                        showEmptyState(messages.length === 0);
                    } else {
                        showError(data.message || '加载消息失败');
                        showEmptyState(true);
                    }
                },
                error: function() {
                    showError('网络请求失败');
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染消息列表
        function renderMessages() {
            const container = $('#messageList');
            
            if (currentPage === 1) {
                container.empty();
            }
            
            messages.forEach(function(message, index) {
                if (index >= (currentPage - 1) * pageSize) {
                    const messageHtml = createMessageItem(message);
                    container.append(messageHtml);
                }
            });
            
            // 添加加载更多按钮
            if (hasMore) {
                const loadMoreHtml = `
                    <div class="load-more" onclick="loadMore();">
                        <i class="ace-icon fa fa-chevron-down"></i>
                        <span>加载更多</span>
                    </div>
                `;
                container.append(loadMoreHtml);
            }
        }

        // 创建消息项
        function createMessageItem(message) {
            const classes = ['message-item'];
            if (!message.isRead) classes.push('unread');
            if (message.priority === 'important') classes.push('important');
            if (message.priority === 'urgent') classes.push('urgent');
            
            const badges = [];
            if (!message.isRead) badges.push('<span class="message-badge badge-unread">未读</span>');
            if (message.priority === 'important') badges.push('<span class="message-badge badge-important">重要</span>');
            if (message.priority === 'urgent') badges.push('<span class="message-badge badge-urgent">紧急</span>');
            if (message.type === 'system') badges.push('<span class="message-badge badge-system">系统</span>');
            
            return `
                <div class="${classes.join(' ')}" onclick="viewMessage('${message.id}')">
                    <div class="message-header-info">
                        <div class="message-title">${message.title}</div>
                        <div class="message-time">${formatTime(message.createTime)}</div>
                    </div>
                    <div class="message-content">${message.content}</div>
                    <div class="message-meta">
                        <div class="message-sender">发送者：${message.sender}</div>
                        <div class="message-badges">${badges.join('')}</div>
                    </div>
                    <div class="message-actions">
                        <button class="action-btn action-read" onclick="markAsRead('${message.id}', event)" title="标记已读">
                            <i class="ace-icon fa fa-check"></i>
                        </button>
                        <button class="action-btn action-delete" onclick="deleteMessage('${message.id}', event)" title="删除">
                            <i class="ace-icon fa fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }

        // 更新统计信息
        function updateStats(stats) {
            $('#totalCount').text(stats.total || 0);
            $('#unreadCount').text(stats.unread || 0);
            $('#importantCount').text(stats.important || 0);
        }

        // 查看消息
        function viewMessage(messageId) {
            // 实现消息详情查看逻辑
            console.log('View message:', messageId);
        }

        // 标记已读
        function markAsRead(messageId, event) {
            event.stopPropagation();
            // 实现标记已读逻辑
            console.log('Mark as read:', messageId);
        }

        // 删除消息
        function deleteMessage(messageId, event) {
            event.stopPropagation();
            // 实现删除消息逻辑
            console.log('Delete message:', messageId);
        }

        // 加载更多
        function loadMore() {
            if (hasMore) {
                currentPage++;
                loadMessages();
            }
        }

        // 刷新消息
        function refreshMessages() {
            currentPage = 1;
            hasMore = true;
            loadMessages();
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) {
                return '刚刚';
            } else if (diff < 3600000) {
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) {
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#messageList').hide();
            } else {
                $('#emptyState').hide();
                $('#messageList').show();
            }
        }

        // 显示错误消息
        function showError(message) {
            console.error('Error: ' + message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
        }
    </script>
</body>
</html>
