<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>推免结果确认</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 推免结果确认页面样式 */
        .confirm-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .confirm-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .confirm-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            background: var(--info-light);
            color: var(--info-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--info-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-section i {
            color: var(--info-color);
            margin-right: 8px;
        }
        
        .student-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .student-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .student-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .student-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-confirm {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-abandon {
            background: var(--error-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .student-content {
            padding: var(--padding-md);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-secondary);
            border-radius: 6px;
        }
        
        .info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .info-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
            flex: 1;
            margin-left: var(--margin-sm);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .confirm-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .confirm-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 400px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .confirm-modal-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .confirm-modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .confirm-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .confirm-body {
            padding: var(--padding-md);
        }
        
        .confirm-message {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            text-align: center;
            margin-bottom: var(--margin-md);
        }
        
        .file-upload-section {
            margin-bottom: var(--margin-md);
        }
        
        .file-upload-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        .file-upload-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .file-upload-hint {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            margin-top: 4px;
        }
        
        .confirm-footer {
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-modal-confirm {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .btn-modal-cancel {
            flex: 1;
            background: var(--text-disabled);
            color: white;
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .confirm-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">推免结果确认</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 推免结果确认头部 -->
        <div class="confirm-header">
            <div class="confirm-title">推免结果确认</div>
            <div class="confirm-desc">确认推免结果信息</div>
        </div>
        
        <!-- 消息提示 -->
        <c:if test="${not empty msg}">
            <div class="notice-section">
                <i class="ace-icon fa fa-hand-o-right"></i>
                ${msg}
            </div>
        </c:if>
        
        <c:if test="${empty msg}">
            <!-- 学生信息 -->
            <div class="student-info">
                <div class="student-header">
                    <div class="student-title">
                        <i class="ace-icon fa fa-user"></i>
                        推免信息确认
                    </div>
                    <c:if test="${qrflag}">
                        <div class="action-buttons">
                            <button class="btn-confirm" onclick="confirmResult(1);">
                                <i class="ace-icon fa fa-check"></i>
                                <span>确定推免</span>
                            </button>
                            <button class="btn-abandon" onclick="confirmResult(0);">
                                <i class="ace-icon fa fa-times"></i>
                                <span>放弃推免</span>
                            </button>
                        </div>
                    </c:if>
                </div>
                
                <div class="student-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">学号</div>
                            <div class="info-value">${xh}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">姓名</div>
                            <div class="info-value">${xm}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">年级</div>
                            <div class="info-value">${njmc}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">学院</div>
                            <div class="info-value">${xsm}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">专业</div>
                            <div class="info-value">${zym}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">推免状态</div>
                            <div class="info-value">
                                <span class="status-badge ${getStatusClass(sqztmc)}">${sqztmc}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 确认模态框 -->
        <div class="confirm-modal" id="confirmModal">
            <div class="confirm-content">
                <div class="confirm-modal-header">
                    <div class="confirm-modal-title" id="modalTitle">确认操作</div>
                    <button class="confirm-close" onclick="closeConfirmModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="confirm-body">
                    <div class="confirm-message" id="confirmMessage">
                        <!-- 动态设置确认消息 -->
                    </div>
                    
                    <div class="file-upload-section" id="fileUploadSection" style="display: none;">
                        <label class="file-upload-label">放弃声明</label>
                        <input type="file" id="abandonFile" class="file-upload-input" accept=".pdf,.jpg,.jpeg,.bmp">
                        <div class="file-upload-hint">支持格式：PDF、JPG、JPEG、BMP，最大5MB</div>
                    </div>
                </div>
                <div class="confirm-footer">
                    <button class="btn-mobile btn-modal-confirm" id="modalConfirmBtn">
                        <i class="ace-icon fa fa-check"></i>
                        <span>确定</span>
                    </button>
                    <button class="btn-mobile btn-modal-cancel" onclick="closeConfirmModal();">
                        <i class="ace-icon fa fa-times"></i>
                        <span>取消</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentAction = null;
        const maxFileSize = 5 * 1024 * 1024; // 5MB

        $(function() {
            initPage();
            initFileUpload();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化文件上传
        function initFileUpload() {
            const fileInput = $('#abandonFile');

            fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    // 检查文件大小
                    if (file.size > maxFileSize) {
                        showError('附件大小超过限制的5MB，请重新选择。');
                        this.value = '';
                        return;
                    }

                    // 检查文件格式
                    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/bmp'];
                    const fileType = file.type.toLowerCase();
                    const fileName = file.name.toLowerCase();
                    const isValidType = allowedTypes.includes(fileType) ||
                                       fileName.endsWith('.pdf') ||
                                       fileName.endsWith('.jpg') ||
                                       fileName.endsWith('.jpeg') ||
                                       fileName.endsWith('.bmp');

                    if (!isValidType) {
                        showError('文件格式上传不正确，请重新上传！');
                        this.value = '';
                        return;
                    }
                }
            });
        }

        // 确认结果
        function confirmResult(qrzt) {
            currentAction = qrzt;

            if (qrzt == 1) {
                // 确定推免
                $('#modalTitle').text('确认推免');
                $('#confirmMessage').text('是否确认推免？');
                $('#fileUploadSection').hide();
                $('#modalConfirmBtn').off('click').on('click', function() {
                    closeConfirmModal();
                    submitConfirmation(qrzt, null);
                });
            } else {
                // 放弃推免
                $('#modalTitle').text('放弃推免');
                $('#confirmMessage').text('请上传放弃声明并确认自愿放弃推免');
                $('#fileUploadSection').show();
                $('#abandonFile').val('');
                $('#modalConfirmBtn').off('click').on('click', function() {
                    const fileInput = $('#abandonFile')[0];
                    const file = fileInput.files[0];

                    if (!file) {
                        showError('请先上传放弃声明！');
                        return;
                    }

                    closeConfirmModal();
                    setTimeout(function() {
                        showFinalConfirm(qrzt, file);
                    }, 300);
                });
            }

            $('#confirmModal').fadeIn(300);
        }

        // 显示最终确认（放弃推免）
        function showFinalConfirm(qrzt, file) {
            $('#modalTitle').text('最终确认');
            $('#confirmMessage').text('是否确认自愿放弃推免？');
            $('#fileUploadSection').hide();
            $('#modalConfirmBtn').off('click').on('click', function() {
                closeConfirmModal();
                submitConfirmation(qrzt, file);
            });

            $('#confirmModal').fadeIn(300);
        }

        // 提交确认
        function submitConfirmation(qrzt, file) {
            showLoading(true);

            const formData = new FormData();
            formData.append("tokenValue", $("#tokenValue").val());
            formData.append("sqbh", "${sqbh}");
            formData.append("qrzt", qrzt);
            if (file) {
                formData.append("fqsm", file);
            }

            $.ajax({
                url: "/student/personalManagement/pushexemptionmgt/resultconfirmation/update/confirm",
                type: "post",
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    $("#tokenValue").val(data.token);

                    if (data.result.indexOf("/") != -1) {
                        showError("页面已过期，请刷新页面！");
                    } else if (data.result === "error") {
                        showError(data.msg);
                    } else if (data.result === "success") {
                        showSuccess("保存成功！", function() {
                            location.reload();
                        });
                    } else {
                        showError(data.msg);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 关闭确认模态框
        function closeConfirmModal() {
            $('#confirmModal').fadeOut(300);
        }

        // 获取状态样式类
        function getStatusClass(status) {
            if (status && status.includes('通过')) {
                return 'status-approved';
            } else if (status && status.includes('不通过')) {
                return 'status-rejected';
            } else {
                return 'status-pending';
            }
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.confirm-modal', function(e) {
            if (e.target === this) {
                closeConfirmModal();
            }
        });
    </script>
</body>
</html>
