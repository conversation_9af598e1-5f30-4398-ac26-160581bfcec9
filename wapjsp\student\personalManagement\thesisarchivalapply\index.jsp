<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>最终文档存档提交</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 论文归档申请页面样式 */
        .archival-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .archival-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .archival-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-info {
            background: var(--info-light);
            color: var(--info-dark);
            border-left: 4px solid var(--info-color);
        }
        
        .notice-warning {
            background: var(--error-light);
            color: var(--error-dark);
            border-left: 4px solid var(--error-color);
            text-align: center;
            font-weight: 600;
        }
        
        .notice-section i {
            margin-right: 8px;
        }
        
        .status-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-title i {
            color: var(--info-color);
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: var(--font-size-small);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .form-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-title i {
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-label.required::before {
            content: "*";
            color: var(--error-color);
            margin-right: 4px;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-hint {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            margin-top: 4px;
        }
        
        .form-value {
            padding: var(--padding-sm);
            background: var(--bg-secondary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        .file-upload-group {
            margin-bottom: var(--margin-md);
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-secondary);
        }
        
        .file-upload-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .file-upload-title.required::before {
            content: "*";
            color: var(--error-color);
            margin-right: 4px;
        }
        
        .file-upload-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .file-existing {
            background: var(--warning-light);
            color: var(--warning-dark);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
            font-size: var(--font-size-small);
        }
        
        .file-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .file-link:hover {
            text-decoration: underline;
        }
        
        .file-hint {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            line-height: 1.4;
        }
        
        .submit-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
            border: none;
            padding: var(--padding-md) var(--padding-xl);
            border-radius: 8px;
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-submit:hover {
            background: var(--success-dark);
            transform: translateY(-2px);
        }
        
        .btn-submit:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            color: var(--error-color);
            font-size: var(--font-size-mini);
            margin-top: 4px;
            display: none;
        }
        
        @media (max-width: 480px) {
            .archival-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .form-section, .status-section, .submit-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">最终文档存档提交</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 论文归档申请头部 -->
        <div class="archival-header">
            <div class="archival-title">最终文档存档提交</div>
            <div class="archival-desc">提交论文归档文件</div>
        </div>
        
        <!-- 消息提示 -->
        <c:if test="${not empty msg}">
            <div class="notice-section notice-info">
                <i class="ace-icon fa fa-hand-o-right"></i>
                ${msg}
            </div>
        </c:if>
        
        <c:if test="${empty msg}">
            <!-- 重要提示 -->
            <div class="notice-section notice-warning">
                如果题目变化，请完成更题后再提交存档文件！
            </div>
            
            <!-- 审核状态 -->
            <c:if test="${shzt eq '1'}">
                <div class="status-section">
                    <div class="status-title">
                        <i class="ace-icon fa fa-check-circle"></i>
                        审核结果
                    </div>
                    <c:if test="${shjg eq 'Y'}">
                        <div class="status-badge status-approved">
                            <i class="ace-icon fa fa-check"></i>
                            已通过审核
                        </div>
                    </c:if>
                    <c:if test="${shjg eq 'N'}">
                        <div class="status-badge status-rejected">
                            <i class="ace-icon fa fa-times"></i>
                            不通过：${shyj}
                        </div>
                    </c:if>
                </div>
            </c:if>
            
            <!-- 表单内容 -->
            <div class="form-section">
                <div class="form-title">
                    <i class="ace-icon fa fa-edit"></i>
                    论文信息
                </div>
                
                <form id="cd_from" name="cd_from">
                    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
                    <input type="hidden" name="sqbh" value="${sqbh}"/>
                    
                    <div class="form-group">
                        <label class="form-label">题目</label>
                        <div class="form-value">${tmmc}</div>
                        <div class="form-hint">如有变化请申请更题</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label required">研究方向</label>
                        <input type="text" id="yjfx" name="yjfx" value="${yjfx}" class="form-input" placeholder="请输入研究方向" maxlength="40">
                        <div class="form-hint">请以分号分隔</div>
                        <div class="error-message" id="yjfx-error"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">指导教师</label>
                        <div class="form-value">${jsm}</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label required">关键词</label>
                        <input type="text" id="gjc" name="gjc" value="${gjc}" class="form-input" placeholder="请输入关键词" maxlength="200">
                        <div class="form-hint">请以分号分隔</div>
                        <div class="error-message" id="gjc-error"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label required">毕业设计/论文</label>
                        <select id="lwlx" name="lwlx" class="form-select">
                            <option value="">--请选择--</option>
                            <option value="LW" <c:if test="${lwlx eq 'LW'}">selected</c:if>>毕业论文</option>
                            <option value="BS" <c:if test="${lwlx eq 'BS'}">selected</c:if>>毕业设计</option>
                        </select>
                        <div class="error-message" id="lwlx-error"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label required">撰写语种</label>
                        <select id="zxyz" name="zxyz" class="form-select">
                            <option value="">--请选择--</option>
                            <cache:query var="zxyzbs" fields="zxyzdm,zxyzmc" region="lw_code_zxyzb" where=" inuse = '1' " orderby="xssx asc"/>
                            <c:forEach items="${zxyzbs}" var="zxyzb">
                                <option value="${zxyzb.zxyzdm}" <c:if test="${zxyz eq zxyzb.zxyzdm}">selected</c:if>>${zxyzb.zxyzmc}</option>
                            </c:forEach>
                        </select>
                        <div class="error-message" id="zxyz-error"></div>
                    </div>
                    
                    <!-- 文件上传 -->
                    <c:forEach items="${wjcdxmbs}" var="wjcdxmb">
                        <div class="file-upload-group" data-cdxmdm="${wjcdxmb.cdxmdm}">
                            <div class="file-upload-title ${wjcdxmb.xmxx eq '1' ? 'required' : ''}">${wjcdxmb.cdxmmc}</div>
                            
                            <c:choose>
                                <c:when test="${not empty cdwjs && not empty cdwjs[wjcdxmb.cdxmdm]}">
                                    <c:forEach var="wjb" items="${cdwjs[wjcdxmb.cdxmdm]}" varStatus="wjSta">
                                        <div class="file-existing">
                                            已有文件：<a href="#" class="file-link" onclick="downLwFjs('${wjb.wjUrl}','${wjb.wjmc}')">${wjb.wjmc}</a>
                                            <input type="hidden" class="wjmc" value="${wjb.wjmc}"/>
                                            <input type="hidden" class="wjxh" value="${wjb.id.wjxh}"/>
                                        </div>
                                        <input type="file" class="file-upload-input" id="cd_file_${wjcdxmb.cdxmdm}_${wjb.id.wjxh}" accept="${getAcceptTypes(wjcdxmb.wjlxmc)}">
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <input type="file" class="file-upload-input" id="cd_file_${wjcdxmb.cdxmdm}" accept="${getAcceptTypes(wjcdxmb.wjlxmc)}" ${wjcdxmb.xmxx eq '1' ? 'required' : ''}>
                                </c:otherwise>
                            </c:choose>
                            
                            <div class="file-hint">
                                可上传文件格式：${wjcdxmb.wjlxmc}，文件最大可上传大小：${wjcdxmb.wjdxsx}M
                                <c:if test="${not empty wjcdxmb.bz}">，${wjcdxmb.bz}</c:if>
                            </div>
                        </div>
                    </c:forEach>
                </form>
            </div>
            
            <!-- 提交按钮 -->
            <c:if test="${shzt eq '0' || (shzt eq '1' && shjg eq 'N')}">
                <div class="submit-section">
                    <button type="button" class="btn-submit" onclick="doSaveArchivalData();">
                        <i class="ace-icon fa fa-check"></i>
                        <span>提交</span>
                    </button>
                </div>
            </c:if>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>提交中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        const maxFileSizes = {};
        const allowedExtensions = {};

        $(function() {
            initPage();
            initFileValidation();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化文件验证
        function initFileValidation() {
            <c:forEach items="${wjcdxmbs}" var="wjcdxmb">
                maxFileSizes['${wjcdxmb.cdxmdm}'] = ${wjcdxmb.wjdxsx} * 1024 * 1024;
                <c:if test="${wjcdxmb.wjlxmc ne '*'}">
                    allowedExtensions['${wjcdxmb.cdxmdm}'] = '${wjcdxmb.wjlxmc}'.split(',');
                </c:if>
            </c:forEach>

            // 绑定文件选择事件
            $('.file-upload-input').on('change', function() {
                validateFile(this);
            });
        }

        // 验证文件
        function validateFile(input) {
            const file = input.files[0];
            if (!file) return true;

            const cdxmdm = $(input).closest('.file-upload-group').data('cdxmdm');
            const maxSize = maxFileSizes[cdxmdm];
            const allowedExts = allowedExtensions[cdxmdm];

            // 检查文件大小
            if (file.size > maxSize) {
                showError(`附件大小超过限制的${maxSize / (1024 * 1024)}MB，请重新选择。`);
                input.value = '';
                return false;
            }

            // 检查文件格式
            if (allowedExts) {
                const fileName = file.name.toLowerCase();
                const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1);
                const isValidExt = allowedExts.some(ext => ext.toLowerCase() === fileExt);

                if (!isValidExt) {
                    showError('文件格式上传不正确，请重新上传！');
                    input.value = '';
                    return false;
                }
            }

            return true;
        }

        // 获取文件类型
        function getAcceptTypes(wjlxmc) {
            if (wjlxmc === '*') return '';

            const extensions = wjlxmc.split(',');
            return extensions.map(ext => '.' + ext.toLowerCase()).join(',');
        }

        // 表单验证
        function validateForm() {
            let isValid = true;

            // 清除之前的错误信息
            $('.error-message').hide();

            // 验证研究方向
            const yjfx = $('#yjfx').val().trim();
            if (!yjfx) {
                $('#yjfx-error').text('请输入研究方向').show();
                isValid = false;
            }

            // 验证关键词
            const gjc = $('#gjc').val().trim();
            if (!gjc) {
                $('#gjc-error').text('请输入关键词').show();
                isValid = false;
            }

            // 验证毕业设计/论文
            const lwlx = $('#lwlx').val();
            if (!lwlx) {
                $('#lwlx-error').text('请选择毕业设计/论文类型').show();
                isValid = false;
            }

            // 验证撰写语种
            const zxyz = $('#zxyz').val();
            if (!zxyz) {
                $('#zxyz-error').text('请选择撰写语种').show();
                isValid = false;
            }

            // 验证必需文件
            $('.file-upload-group').each(function() {
                const $group = $(this);
                const isRequired = $group.find('.file-upload-title').hasClass('required');
                const hasExistingFile = $group.find('.wjmc').length > 0;
                const hasNewFile = $group.find('.file-upload-input')[0].files.length > 0;

                if (isRequired && !hasExistingFile && !hasNewFile) {
                    const title = $group.find('.file-upload-title').text();
                    showError(`请上传${title}`);
                    isValid = false;
                }
            });

            return isValid;
        }

        // 提交归档数据
        function doSaveArchivalData() {
            if (!validateForm()) {
                showError("信息维护不正确，请维护完整之后再次提交！");
                return false;
            }

            if (confirm("是否确认提交？")) {
                showLoading(true);

                const formData = new FormData();

                // 添加基本表单数据
                formData.append("tokenValue", $("#tokenValue").val());
                formData.append("sqbh", $("input[name='sqbh']").val());
                formData.append("yjfx", $("#yjfx").val());
                formData.append("gjc", $("#gjc").val());
                formData.append("lwlx", $("#lwlx").val());
                formData.append("zxyz", $("#zxyz").val());

                // 添加文件数据
                let fileIndex = 0;
                $('.file-upload-group').each(function() {
                    const $group = $(this);
                    const cdxmdm = $group.data('cdxmdm');
                    const fileInput = $group.find('.file-upload-input')[0];
                    const file = fileInput.files[0];

                    formData.append(`cdFileObjs[${fileIndex}].cdxmdm`, cdxmdm);

                    if (file) {
                        formData.append(`cdFileObjs[${fileIndex}].file`, file);
                    }

                    const wjmc = $group.find('.wjmc').val();
                    if (wjmc) {
                        formData.append(`cdFileObjs[${fileIndex}].wjmc`, wjmc);
                    }

                    const wjxh = $group.find('.wjxh').val();
                    if (wjxh) {
                        formData.append(`cdFileObjs[${fileIndex}].wjxh`, wjxh);
                    }

                    fileIndex++;
                });

                $.ajax({
                    url: "/student/personalManagement/thesisArchivalApplication/save",
                    type: "post",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/logout") != -1) {
                            showError("页面已过期，请刷新页面！");
                        } else if (data.result === "success") {
                            showSuccess("保存成功！", function() {
                                location.href = "/student/personalManagement/thesisArchivalApplication/index";
                            });
                        } else if (data.result === "err") {
                            showError(data.msg);
                        }
                    },
                    error: function(xhr) {
                        showError("保存失败，请重新添加！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 下载文件
        function downLwFjs(filekey, filename) {
            $("#cdf_down_iframe_div").remove();
            const encodedFilename = btoa(unescape(encodeURIComponent(filename)));
            const url = "/student/personalManagement/thesisArchivalApplication/down?fileid=" + filekey + "&filename=" + encodedFilename;
            $('<div style="display:none;" id="cdf_down_iframe_div"><iframe src="' + url + '" frameborder="0" scrolling="no"></iframe></div>').appendTo('body');
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.btn-submit').prop('disabled', true);
            } else {
                $('#loadingState').hide();
                $('.btn-submit').prop('disabled', false);
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
