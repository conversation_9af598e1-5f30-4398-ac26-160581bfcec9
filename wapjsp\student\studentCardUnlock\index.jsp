<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证解挂</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证解挂页面样式 */
        .unlock-header {
            background: linear-gradient(135deg, var(--success-color), #73d13d);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .unlock-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .status-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .status-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
        }
        
        .status-icon.normal {
            background: var(--success-color);
        }
        
        .status-icon.locked {
            background: var(--error-color);
        }
        
        .status-icon.frozen {
            background: var(--warning-color);
        }
        
        .status-content {
            flex: 1;
        }
        
        .status-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .status-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .unlock-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .form-title i {
            margin-right: var(--margin-xs);
            color: var(--success-color);
        }
        
        .form-section {
            margin-bottom: var(--margin-lg);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-xs);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .unlock-info {
            background: var(--success-light);
            border: 1px solid var(--success-color);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-lg);
        }
        
        .info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--success-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .info-title i {
            margin-right: var(--margin-xs);
        }
        
        .info-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .info-item {
            font-size: var(--font-size-small);
            color: var(--success-color);
            margin-bottom: var(--margin-xs);
            display: flex;
            align-items: flex-start;
        }
        
        .info-item i {
            margin-right: var(--margin-xs);
            margin-top: 2px;
            flex-shrink: 0;
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
        }
        
        .btn-unlock {
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .unlock-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-toggle {
            color: var(--text-secondary);
            transition: transform var(--transition-base);
        }
        
        .history-toggle.expanded {
            transform: rotate(180deg);
        }
        
        .history-content {
            display: none;
        }
        
        .history-content.show {
            display: block;
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-item.unlock {
            border-left: 4px solid var(--success-color);
        }
        
        .history-item.lock {
            border-left: 4px solid var(--error-color);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-processing {
            background: var(--warning-color);
            color: white;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证解挂</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="unlock-header">
            <div class="header-title">学生证解挂</div>
            <div class="header-subtitle">解除学生证挂失状态</div>
        </div>

        <!-- 解挂状态 -->
        <div class="unlock-status">
            <div class="status-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>当前状态</span>
            </div>

            <div class="status-card" id="unlockStatusCard">
                <!-- 状态信息将动态填充 -->
            </div>
        </div>

        <!-- 解挂表单 -->
        <div class="unlock-form" id="unlockForm">
            <div class="form-title">
                <i class="ace-icon fa fa-unlock"></i>
                <span>学生证解挂申请</span>
            </div>

            <!-- 解挂说明 -->
            <div class="unlock-info">
                <div class="info-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    <span>解挂说明</span>
                </div>
                <ul class="info-list">
                    <li class="info-item">
                        <i class="ace-icon fa fa-circle"></i>
                        <span>解挂后学生证将恢复正常使用</span>
                    </li>
                    <li class="info-item">
                        <i class="ace-icon fa fa-circle"></i>
                        <span>请确认已找回学生证或确认安全</span>
                    </li>
                    <li class="info-item">
                        <i class="ace-icon fa fa-circle"></i>
                        <span>解挂操作需要验证身份信息</span>
                    </li>
                    <li class="info-item">
                        <i class="ace-icon fa fa-circle"></i>
                        <span>如有疑问请联系相关部门</span>
                    </li>
                </ul>
            </div>

            <div class="form-section">
                <div class="section-title">基本信息</div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">卡号</div>
                    <input type="text" class="form-input" id="cardNumber" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">挂失时间</div>
                    <input type="text" class="form-input" id="lockTime" disabled>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">解挂信息</div>

                <div class="form-group">
                    <div class="form-label">解挂原因</div>
                    <select class="form-input" id="unlockReason">
                        <option value="">请选择解挂原因</option>
                        <option value="found">已找回学生证</option>
                        <option value="mistake">误操作挂失</option>
                        <option value="security">确认安全</option>
                        <option value="other">其他原因</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">详细说明</div>
                    <textarea class="form-input" id="unlockDescription" placeholder="请详细说明解挂原因" style="min-height: 80px; resize: vertical;"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">身份证号</div>
                    <input type="text" class="form-input" id="idCard" placeholder="请输入身份证号进行身份验证">
                </div>
            </div>

            <div class="form-actions">
                <button class="btn-mobile btn-cancel flex-1" onclick="resetForm();">重置</button>
                <button class="btn-mobile btn-unlock flex-1" onclick="submitUnlock();">确认解挂</button>
            </div>
        </div>

        <!-- 解挂记录 -->
        <div class="unlock-history">
            <div class="history-header" onclick="toggleHistory();">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>操作记录</span>
                </div>
                <div class="history-toggle" id="historyToggle">
                    <i class="ace-icon fa fa-chevron-down"></i>
                </div>
            </div>

            <div class="history-content" id="historyContent">
                <div id="historyItems">
                    <!-- 操作记录将动态填充 -->
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let cardInfo = {};
        let unlockStatus = {};
        let historyData = [];

        $(function() {
            initPage();
            loadCardInfo();
            loadUnlockStatus();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载卡片信息
        function loadCardInfo() {
            $.ajax({
                url: "/student/studentCardUnlock/getCardInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    cardInfo = data.card || {};
                    fillCardInfo();
                },
                error: function() {
                    // 使用模拟数据
                    cardInfo = {
                        studentId: '2021001001',
                        studentName: '张三',
                        cardNumber: '2021001001',
                        lockTime: '2024-01-15 10:30:00'
                    };
                    fillCardInfo();
                }
            });
        }

        // 填充卡片信息
        function fillCardInfo() {
            $('#studentId').val(cardInfo.studentId || '2021001001');
            $('#studentName').val(cardInfo.studentName || '张三');
            $('#cardNumber').val(cardInfo.cardNumber || '2021001001');
            $('#lockTime').val(cardInfo.lockTime || '2024-01-15 10:30:00');
        }

        // 加载解挂状态
        function loadUnlockStatus() {
            showLoading(true);

            $.ajax({
                url: "/student/studentCardUnlock/getUnlockStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    unlockStatus = data.status || {};
                    renderUnlockStatus();
                    updateFormState();
                    showLoading(false);
                },
                error: function() {
                    // 使用模拟数据
                    unlockStatus = {
                        status: 'locked',
                        description: '学生证已挂失，可申请解挂'
                    };
                    renderUnlockStatus();
                    updateFormState();
                    showLoading(false);
                }
            });
        }

        // 渲染解挂状态
        function renderUnlockStatus() {
            const status = unlockStatus.status || 'locked';
            const iconClass = getStatusIconClass(status);
            const statusText = getStatusText(status);
            const metaText = unlockStatus.description || '状态正常';

            const statusHtml = `
                <div class="status-icon ${status}">
                    <i class="ace-icon fa ${iconClass}"></i>
                </div>
                <div class="status-content">
                    <div class="status-text">${statusText}</div>
                    <div class="status-meta">${metaText}</div>
                </div>
            `;

            $('#unlockStatusCard').html(statusHtml);
        }

        // 获取状态图标类
        function getStatusIconClass(status) {
            switch(status) {
                case 'normal': return 'fa-check';
                case 'locked': return 'fa-lock';
                case 'frozen': return 'fa-pause';
                default: return 'fa-question';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'normal': return '正常状态';
                case 'locked': return '已挂失';
                case 'frozen': return '已冻结';
                default: return '未知状态';
            }
        }

        // 更新表单状态
        function updateFormState() {
            const status = unlockStatus.status || 'locked';

            if (status === 'locked') {
                // 已挂失状态，显示解挂表单
                $('#unlockForm').show();
            } else {
                // 其他状态，隐藏表单
                $('#unlockForm').hide();
            }
        }

        // 重置表单
        function resetForm() {
            $('#unlockReason').val('');
            $('#unlockDescription').val('');
            $('#contactPhone').val('');
            $('#idCard').val('');
        }

        // 提交解挂申请
        function submitUnlock() {
            const formData = {
                reason: $('#unlockReason').val(),
                description: $('#unlockDescription').val().trim(),
                phone: $('#contactPhone').val().trim(),
                idCard: $('#idCard').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要解除学生证挂失吗？\n\n解挂后学生证将恢复正常使用。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitUnlock(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitUnlock(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.reason) {
                showError('请选择解挂原因');
                return false;
            }

            if (!formData.description) {
                showError('请填写详细说明');
                return false;
            }

            if (!formData.phone) {
                showError('请输入联系电话');
                return false;
            }

            if (!formData.idCard) {
                showError('请输入身份证号');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            // 验证身份证号格式
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idCardRegex.test(formData.idCard)) {
                showError('请输入正确的身份证号');
                return false;
            }

            return true;
        }

        // 执行提交解挂申请
        function doSubmitUnlock(formData) {
            $.ajax({
                url: "/student/studentCardUnlock/submitUnlock",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('解挂申请提交成功，学生证已恢复正常');
                        resetForm();
                        loadUnlockStatus(); // 重新加载状态
                    } else {
                        showError(data.message || '解挂申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 切换历史记录显示
        function toggleHistory() {
            const content = $('#historyContent');
            const toggle = $('#historyToggle');

            if (content.hasClass('show')) {
                content.removeClass('show');
                toggle.removeClass('expanded');
            } else {
                content.addClass('show');
                toggle.addClass('expanded');

                // 如果还没有加载历史数据，则加载
                if (historyData.length === 0) {
                    loadHistoryData();
                }
            }
        }

        // 加载历史数据
        function loadHistoryData() {
            $.ajax({
                url: "/student/studentCardUnlock/getHistoryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    historyData = data.history || [];
                    renderHistoryData();
                },
                error: function() {
                    // 使用模拟数据
                    historyData = [
                        {
                            id: '1',
                            type: 'lock',
                            status: 'completed',
                            createTime: '2024-01-15 10:30:00',
                            processTime: '2024-01-15 10:31:00',
                            reason: '学生证丢失'
                        },
                        {
                            id: '2',
                            type: 'unlock',
                            status: 'completed',
                            createTime: '2024-01-20 14:20:00',
                            processTime: '2024-01-20 14:21:00',
                            reason: '已找回学生证'
                        }
                    ];
                    renderHistoryData();
                }
            });
        }

        // 渲染历史数据
        function renderHistoryData() {
            const container = $('#historyItems');
            container.empty();

            if (historyData.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无操作记录
                    </div>
                `);
                return;
            }

            historyData.forEach(item => {
                const historyHtml = createHistoryItem(item);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(item) {
            const type = item.type || 'unlock';
            const status = item.status || 'processing';
            const statusClass = getHistoryStatusClass(status);
            const statusText = getHistoryStatusText(status);
            const typeText = getHistoryTypeText(type);

            return `
                <div class="history-item ${type}" onclick="showHistoryDetail('${item.id}')">
                    <div class="history-basic">
                        <div class="history-type">${typeText}</div>
                        <div class="history-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>申请时间:</span>
                            <span>${formatDateTime(item.createTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>处理时间:</span>
                            <span>${formatDateTime(item.processTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>原因:</span>
                            <span>${item.reason || '-'}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>状态:</span>
                            <span>${statusText}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取历史状态样式类
        function getHistoryStatusClass(status) {
            return `status-${status}`;
        }

        // 获取历史状态文本
        function getHistoryStatusText(status) {
            switch(status) {
                case 'completed': return '已完成';
                case 'processing': return '处理中';
                default: return '未知';
            }
        }

        // 获取历史类型文本
        function getHistoryTypeText(type) {
            switch(type) {
                case 'lock': return '挂失申请';
                case 'unlock': return '解挂申请';
                default: return '其他';
            }
        }

        // 显示历史详情
        function showHistoryDetail(historyId) {
            const item = historyData.find(h => h.id === historyId);
            if (!item) return;

            let message = `操作详情\n\n`;
            message += `类型：${getHistoryTypeText(item.type)}\n`;
            message += `状态：${getHistoryStatusText(item.status)}\n`;
            message += `申请时间：${item.createTime}\n`;
            message += `处理时间：${item.processTime || '-'}\n`;
            message += `原因：${item.reason || '-'}\n`;

            if (item.comment) {
                message += `处理意见：${item.comment}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString();
        }

        // 刷新数据
        function refreshData() {
            loadCardInfo();
            loadUnlockStatus();
            if ($('#historyContent').hasClass('show')) {
                loadHistoryData();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
