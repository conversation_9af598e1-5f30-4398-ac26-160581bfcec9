package educationalAdministration.student.personalApplication.curriculumReplacement.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONObject;

import com.urpSoft.core.service.IBaseService;

import educationalAdministration.student.personalApplication.curriculumReplacement.entity.CjKctdSqkcb;
import educationalAdministration.student.personalManagement.entity.JhFajhkcbView;
import educationalAdministration.student.personalManagement.entity.JhFakzsView;

public interface CurriculumReplacementService extends IBaseService {

	String querySqbh();

	void doDeleteAllCurriculum(String sqbh);
	
	void doDeleteSpjlb(String sqbh);
	
	List<Object[]> queryTdkchList(String sqbh);
	
	List<Object[]> queryKchList(String sqbh);
	
	long queryCount(String xh,String kchs);
	
	List<Object[]> queryKctdSpjlb(String sqbh);
	
	String queryParamValueById();
	
	Object[] queryCjKctdSqb(String sqbh);
	
	List<Object[]> queryAllPyfa(String studentId);
	
	List<JhFakzsView> findJhFakzsList(String fajhh,String fkzh);
	
	List<JhFajhkcbView> findJhFajhkcList(String fajhh,String fakzh);
	
	List<Object[]> queryRootNode(String fajhh);
	
	List<Object[]> queryKzNode(String fajhh,String fkzh);
	
	List<Object[]> queryKcNode(String fajhh,String fakzh,String xh);

	List<Object[]> queryfajhkcb(String fajhh, String fakzh, String xh, String kch, String kcm);
	
	List<Object[]> queryStudentScore(String xh,String kch,String kcm);
	
	List<CjKctdSqkcb> findCjKctdSqkcbList(String sqbh);
	
	Map<String,Object> saveCurriculumInfo(Map<String,Object> map,HttpServletRequest request,String state,List<JSONObject> list);
	
	String automaticApproval(String sqbh);
	
	String revoke(HttpServletRequest request,String sqbh,String cxyy);
	
	Map<String,Object> queryApprovers(HttpServletRequest request,Object[] obj,String tdlx,String kcList,String tdkcList);
	
	void doRevoke(HttpServletRequest request,String sqbh,String cxyy);

}