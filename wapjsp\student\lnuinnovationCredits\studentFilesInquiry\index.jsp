<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生档案查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生档案查询页面样式 */
        .tabs-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            display: flex;
            background: var(--bg-tertiary);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .tabs-header::-webkit-scrollbar {
            display: none;
        }
        
        .tab-button {
            flex: 0 0 auto;
            padding: var(--padding-md);
            background: transparent;
            border: none;
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
            white-space: nowrap;
            min-width: 120px;
            text-align: center;
        }
        
        .tab-button.active {
            background: var(--bg-primary);
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .application-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .application-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
            margin-right: var(--margin-sm);
        }
        
        .application-index {
            position: absolute;
            top: var(--padding-md);
            right: var(--padding-md);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .status-submitted {
            background: var(--info-color);
            color: white;
        }
        
        .status-reviewing {
            background: var(--primary-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-cancelled {
            background: var(--text-disabled);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
            padding-top: var(--padding-sm);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-action {
            flex: 1;
            min-height: 36px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .dynamic-columns {
            display: grid;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .dynamic-columns.cols-1 {
            grid-template-columns: 1fr;
        }
        
        .dynamic-columns.cols-2 {
            grid-template-columns: 1fr 1fr;
        }
        
        .dynamic-columns.cols-3 {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .dynamic-columns.cols-4 {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .dynamic-columns.cols-5 {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .dynamic-columns.cols-6 {
            grid-template-columns: repeat(2, 1fr);
        }
        
        @media (max-width: 480px) {
            .dynamic-columns {
                grid-template-columns: 1fr !important;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生档案查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 选项卡 -->
        <div class="tabs-container">
            <div class="tabs-header" id="tabsHeader">
                <!-- 动态加载选项卡 -->
            </div>
            
            <div class="tab-content">
                <div id="applicationsList">
                    <!-- 动态加载申请列表 -->
                </div>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无可查看的数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let applicationData = [];
        let currentTab = '';
        let currentPage = 1;
        let hasMore = true;
        let tableHeaders = [];

        $(function() {
            initPage();
            loadTabs();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载选项卡
        function loadTabs() {
            // 获取成果类型选项卡
            const tabsData = [
                <cache:query var="cxxdmbs" region="chx_code_cxxdmb" fields="cxxdm,cxxmc" />
                <c:forEach items="${cxxdmbs}" var="cxxdmb" varStatus="status">
                    {
                        code: '${cxxdmb.cxxdm}',
                        name: '${cxxdmb.cxxmc}',
                        active: ${status.index == 1}
                    }<c:if test="${!status.last}">,</c:if>
                </c:forEach>
            ];
            
            renderTabs(tabsData);
            
            // 激活第二个选项卡（索引为1）
            if (tabsData.length > 1) {
                switchTab(tabsData[1].code);
            }
        }

        // 渲染选项卡
        function renderTabs(tabs) {
            const container = $('#tabsHeader');
            container.empty();
            
            tabs.forEach(function(tab, index) {
                const tabHtml = `
                    <button class="tab-button ${tab.active ? 'active' : ''}" 
                            data-tab="${tab.code}" 
                            onclick="switchTab('${tab.code}');">
                        <i class="ace-icon fa fa-home"></i>
                        ${tab.name}
                    </button>
                `;
                container.append(tabHtml);
            });
        }

        // 切换选项卡
        function switchTab(tabCode) {
            currentTab = tabCode;
            
            // 更新按钮状态
            $('.tab-button').removeClass('active');
            $(`.tab-button[data-tab="${tabCode}"]`).addClass('active');
            
            // 加载对应数据
            loadData(1, true);
        }

        // 加载数据
        function loadData(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            $.ajax({
                url: "/student/lnuinnovationCredits/studentFilesInquiry/queryPageInfo",
                type: "post",
                data: "pageNum=" + page + "&pageSize=20&cxxdm=" + currentTab,
                dataType: "json",
                success: function(data) {
                    if (data.data && data.data.records && data.data.records.length > 0) {
                        if (reset) {
                            applicationData = data.data.records;
                            tableHeaders = data.titlearr || [];
                        } else {
                            applicationData = applicationData.concat(data.data.records);
                        }

                        hasMore = applicationData.length < data.data.pageContext.totalCount;
                        renderApplicationList();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            applicationData = [];
                            tableHeaders = data.titlearr || [];
                            renderApplicationList();
                        }
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationList() {
            const container = $('#applicationsList');
            container.empty();

            applicationData.forEach(function(item, index) {
                const itemHtml = createApplicationItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建申请项目HTML
        function createApplicationItem(item, index) {
            // 获取固定字段
            const id = item.ID;
            const status = getStatusInfo(item.SQZT);
            const semester = item.ZXJXJHM || '-';
            const projectName = item.XMMC || '申请项目';

            // 获取动态字段
            const dynamicFields = [];
            Object.keys(item).forEach(function(key) {
                if (key !== 'ID' && key !== 'SQZT' && key !== 'ZXJXJHM' && key !== 'XMMC') {
                    const headerIndex = Object.keys(item).indexOf(key) - 4; // 减去固定字段数量
                    const headerName = tableHeaders[headerIndex] || key;
                    dynamicFields.push({
                        label: headerName,
                        value: item[key] || '-'
                    });
                }
            });

            // 计算动态字段的列数
            const colsClass = dynamicFields.length <= 6 ? `cols-${Math.min(dynamicFields.length, 2)}` : 'cols-2';

            return `
                <div class="application-item">
                    <div class="application-index">#${index + 1}</div>

                    <div class="application-header">
                        <div class="application-title">${projectName}</div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">申请状态</span>
                            <span class="detail-value">
                                <span class="status-badge ${status.class}">${status.text}</span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">学年学期</span>
                            <span class="detail-value">${semester}</span>
                        </div>
                    </div>

                    ${dynamicFields.length > 0 ? `
                        <div class="dynamic-columns ${colsClass}">
                            ${dynamicFields.map(field => `
                                <div class="detail-item">
                                    <span class="detail-label">${field.label}</span>
                                    <span class="detail-value">${field.value}</span>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    <div class="action-buttons">
                        <button class="btn-action btn-view" onclick="viewApplication('${id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看详情</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(status) {
            const statusMap = {
                '-1': { text: '撤销', class: 'status-cancelled' },
                '0': { text: '待提交', class: 'status-draft' },
                '1': { text: '已提交', class: 'status-submitted' },
                '2': { text: '审批中', class: 'status-reviewing' },
                '3': { text: '审批结束', class: 'status-approved' }
            };
            return statusMap[status] || { text: '未知', class: 'status-draft' };
        }

        // 查看申请详情
        function viewApplication(id) {
            if (parent && parent.addTab) {
                parent.addTab('申请详情', '/student/application/index/seeInfo?applyId=' + id + '&applyType=10021');
            } else {
                window.location.href = '/student/application/index/seeInfo?applyId=' + id + '&applyType=10021';
            }
        }

        // 刷新数据
        function refreshData() {
            if (currentTab) {
                loadData(1, true);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#applicationsList').hide();
            } else {
                $('#emptyState').hide();
                $('#applicationsList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 无限滚动加载
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    currentPage++;
                    loadData(currentPage, false);
                }
            }
        });
    </script>
</body>
</html>
