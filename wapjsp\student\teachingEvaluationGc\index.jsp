<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教学评估</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教学评估页面样式 */
        .info-notice {
            background: var(--info-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .evaluation-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .evaluation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .evaluation-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
            margin-right: var(--margin-sm);
        }
        
        .evaluation-index {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .evaluation-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--text-disabled);
            color: white;
        }
        
        .evaluation-type {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .type-process {
            background: var(--primary-color);
            color: white;
        }
        
        .type-performance {
            background: var(--warning-color);
            color: white;
        }
        
        .type-vote {
            background: var(--info-color);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
            padding-top: var(--padding-sm);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-action {
            flex: 1;
            min-height: 36px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-evaluate {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--success-color);
            color: white;
        }
        
        .time-range {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .time-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        @media (max-width: 480px) {
            .evaluation-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教学评估</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 提示信息 -->
        <c:if test="${not empty msg}">
            <div class="info-notice">
                <i class="ace-icon fa fa-hand-o-right"></i>
                ${msg}
            </div>
        </c:if>
        
        <c:if test="${empty msg}">
            <!-- 评估列表 -->
            <div id="evaluationList">
                <!-- 动态加载内容 -->
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-list-alt"></i>
                <div>暂无评估任务</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </c:if>
    </div>

    <script>
        // 全局变量
        let evaluationData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let tabType = "ktjs"; // 默认课堂教师
        let schoolId = '${schoolId}';

        $(function() {
            initPage();
            if ('${empty msg}' === 'true') {
                loadData(1, true);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            
            // 显示学校特定提示信息
            if (schoolId === "100006" && '${showmsg}' === "1") {
                showSchoolTips();
            }
        }

        // 显示学校提示信息
        function showSchoolTips() {
            const message = "无论是课堂及时评教还是期末评教，同学们的操作都是匿名的！匿名的！匿名的！请放心给出真心的评价，如实评价每一位老师和课程。" +
                          "评教是一件辛苦的事情，需要花费你一点儿时间。你的评价将用于改进教学，让川大越来越好，期待你的真心话和客观评价！";
            
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, '【温馨提示】');
            } else {
                alert(message);
            }
        }

        // 加载数据
        function loadData(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }
            
            showLoading(true);
            
            $.ajax({
                url: "/student/teachingEvaluationGc/evaluation/queryAll",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize + "&flag=" + tabType,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            evaluationData = data.records;
                        } else {
                            evaluationData = evaluationData.concat(data.records);
                        }
                        
                        totalCount = data.pageContext.totalCount;
                        hasMore = evaluationData.length < totalCount;
                        renderEvaluationList();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            evaluationData = [];
                            renderEvaluationList();
                        }
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染评估列表
        function renderEvaluationList() {
            const container = $('#evaluationList');
            container.empty();
            
            evaluationData.forEach(function(item, index) {
                const itemHtml = createEvaluationItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建评估项目HTML
        function createEvaluationItem(item, index) {
            const evaluationType = getEvaluationType(item.PGLXDM);
            const statusInfo = getStatusInfo(item.SFPG);
            const timeRange = formatTimeRange(item.PGKSSJ, item.PGJSSJ);
            
            return `
                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-title">${item.WJMC || '问卷名称'}</div>
                        <div class="evaluation-index">#${index + 1}</div>
                    </div>
                    
                    <div class="evaluation-details">
                        <div class="detail-item">
                            <span class="detail-label">被评人</span>
                            <span class="detail-value">${item.JSM || item.LSRXM || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">评估内容</span>
                            <span class="detail-value">${item.KCM || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">课程号</span>
                            <span class="detail-value">${item.KCH || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">课序号</span>
                            <span class="detail-value">${item.KXH || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">评估类型</span>
                            <span class="detail-value">
                                <span class="evaluation-type ${evaluationType.class}">${evaluationType.text}</span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">是否已评估</span>
                            <span class="detail-value">
                                <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                            </span>
                        </div>
                    </div>
                    
                    ${timeRange ? `
                        <div class="time-range">
                            <div class="time-label">评估时间：</div>
                            <div>${timeRange}</div>
                        </div>
                    ` : ''}
                    
                    <div class="action-buttons">
                        ${getActionButtons(item)}
                    </div>
                </div>
            `;
        }

        // 获取评估类型信息
        function getEvaluationType(pglxdm) {
            const typeMap = {
                '01': { text: '过程评估', class: 'type-process' },
                '02': { text: '绩效评估', class: 'type-performance' },
                '03': { text: '投票评估', class: 'type-vote' }
            };
            return typeMap[pglxdm] || { text: '未知类型', class: 'type-process' };
        }

        // 获取状态信息
        function getStatusInfo(sfpg) {
            return sfpg === "1" ?
                { text: '是', class: 'status-completed' } :
                { text: '否', class: 'status-pending' };
        }

        // 格式化时间范围
        function formatTimeRange(startTime, endTime) {
            if (!startTime || !endTime) return '';

            const formatTime = (timeStr) => {
                if (timeStr.length < 14) return timeStr;
                return timeStr.substring(0, 4) + "-" + timeStr.substring(4, 6) + "-" +
                       timeStr.substring(6, 8) + " " + timeStr.substring(8, 10) + ":" +
                       timeStr.substring(10, 12) + ":" + timeStr.substring(12, 14);
            };

            return formatTime(startTime) + " ~ " + formatTime(endTime);
        }

        // 获取操作按钮
        function getActionButtons(item) {
            const pglxdm = item.PGLXDM;
            const sfpg = item.SFPG;
            const yxdcpg = item.YXDCPG;
            const ktid = item.KTID;
            const pgid = item.PGID;
            const wjbm = item.WJBM;
            const kch = item.KCH;
            const kxh = item.KXH;
            const jkkg = item.JKKG;
            const jkpgts = item.JKPGTS;
            const jksj = item.JKRQ;

            let buttons = '';

            if (pglxdm === "02") { // 绩效评估
                if (yxdcpg === "1") { // 允许多次评估
                    buttons += `
                        <button class="btn-action btn-evaluate" onclick="performanceEvaluation('${ktid}');">
                            <i class="ace-icon fa fa-edit"></i>
                            <span>评估</span>
                        </button>
                    `;
                    if (sfpg === "1") {
                        buttons += `
                            <button class="btn-action btn-view" onclick="viewEvaluationResult('${ktid}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看</span>
                            </button>
                        `;
                    }
                } else { // 一次评估
                    if (sfpg === "1") {
                        buttons += `
                            <button class="btn-action btn-view" onclick="viewEvaluationResult2('${ktid}', '${pgid}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看</span>
                            </button>
                        `;
                    } else {
                        buttons += `
                            <button class="btn-action btn-evaluate" onclick="performanceEvaluation3('${ktid}', '${jkkg}', '${jkpgts}', '${jksj}');">
                                <i class="ace-icon fa fa-edit"></i>
                                <span>评估</span>
                            </button>
                        `;
                    }
                }
            } else if (pglxdm === "01") { // 过程评估
                if (sfpg === "1") {
                    buttons += `
                        <button class="btn-action btn-edit" onclick="editEvaluationResult('${ktid}');">
                            <i class="ace-icon fa fa-pencil"></i>
                            <span>修改</span>
                        </button>
                        <button class="btn-action btn-view" onclick="viewEvaluationResult('${ktid}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                    `;
                } else {
                    buttons += `
                        <button class="btn-action btn-evaluate" onclick="processEvaluation('${ktid}', '${wjbm}', '${kch}', '${kxh}');">
                            <i class="ace-icon fa fa-edit"></i>
                            <span>评估</span>
                        </button>
                    `;
                }
            }

            return buttons;
        }

        // 绩效评估（多次）
        function performanceEvaluation(ktid) {
            if (checkCanEvaluate('qmkg', ktid)) {
                if (parent && parent.addTab) {
                    parent.addTab('绩效评估', '/student/teachingEvaluationGc/newEvaluation/evaluation/' + ktid);
                } else {
                    window.location.href = '/student/teachingEvaluationGc/newEvaluation/evaluation/' + ktid;
                }
            }
        }

        // 绩效评估（一次）
        function performanceEvaluation3(ktid, jkkg, jkpgts, jksj) {
            if (checkCanEvaluate('qmkg', ktid)) {
                if (parent && parent.addTab) {
                    parent.addTab('绩效评估', '/student/teachingEvaluationGc/newEvaluation/evaluation/' + ktid +
                                 '?jkkg=' + jkkg + '&jkpgts=' + jkpgts + '&jksj=' + jksj);
                } else {
                    window.location.href = '/student/teachingEvaluationGc/newEvaluation/evaluation/' + ktid +
                                          '?jkkg=' + jkkg + '&jkpgts=' + jkpgts + '&jksj=' + jksj;
                }
            }
        }

        // 过程评估
        function processEvaluation(ktid, wjbm, kch, kxh) {
            if (parent && parent.addTab) {
                parent.addTab('过程评估', '/student/teachingEvaluationGc/newEvaluation/evaluation2/' + ktid +
                             '?wjbm=' + wjbm + '&kch=' + kch + '&kxh=' + kxh);
            } else {
                window.location.href = '/student/teachingEvaluationGc/newEvaluation/evaluation2/' + ktid +
                                      '?wjbm=' + wjbm + '&kch=' + kch + '&kxh=' + kxh;
            }
        }

        // 查看评估结果
        function viewEvaluationResult(ktid) {
            if (parent && parent.addTab) {
                parent.addTab('评估结果', '/student/teachingEvaluationGc/newEvaluation/lookEvaluation?ktid=' + ktid);
            } else {
                window.location.href = '/student/teachingEvaluationGc/newEvaluation/lookEvaluation?ktid=' + ktid;
            }
        }

        // 查看评估结果2
        function viewEvaluationResult2(ktid, pgid) {
            if (parent && parent.addTab) {
                parent.addTab('评估结果', '/student/teachingEvaluationGc/newEvaluation/lookEvaluation?ktid=' + ktid + '&pgid=' + pgid);
            } else {
                window.location.href = '/student/teachingEvaluationGc/newEvaluation/lookEvaluation?ktid=' + ktid + '&pgid=' + pgid;
            }
        }

        // 修改评估结果
        function editEvaluationResult(ktid) {
            if (parent && parent.addTab) {
                parent.addTab('修改评估', '/student/teachingEvaluationGc/newEvaluation/editEvaluationResult?ktid=' + ktid);
            } else {
                window.location.href = '/student/teachingEvaluationGc/newEvaluation/editEvaluationResult?ktid=' + ktid;
            }
        }

        // 检查是否可以评估
        function checkCanEvaluate(mark, ktid) {
            // 这里可以添加具体的检查逻辑
            return true;
        }

        // 刷新数据
        function refreshData() {
            loadData(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#evaluationList').hide();
            } else {
                $('#emptyState').hide();
                $('#evaluationList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 无限滚动加载
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    currentPage++;
                    loadData(currentPage, false);
                }
            }
        });
    </script>
</body>
</html>
