<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!-- 缓存 -->
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>新增/修改申请</title>
    <style type="text/css">
        .ace-file-input {
            width: calc(100% - 40px);
            display: inline-block;
            vertical-align: middle;
        }
    </style>
    <script src="/assets/js/jquery.validate.min.js"></script>
    <script src="/assets/js/jquery.validate.methods.js"></script>
    <script src="/js/jQuery-validation/messages_zh.js"></script>
    <script type="text/javascript">
        $(function () {
            initFile();

            $("#addTeacherInfoForm").validate({
                ignore: "",
                errorElement: 'div',
                errorClass: 'help-block',
                focusInvalid: true,
                errorPlacement: function (error, element) {
                    error.appendTo($(element).closest('div.profile-info-value'));
                }
            });
        });

        function saveInfo(apply_status) {
            var kckx = $("#kxcc_xs").html();
            if (kckx.trim() == "") {
                urp.alert("请选择课程课序！");
                return;
            }
            if ($("#addTeacherInfoForm").valid()) {
                var fj = "";
                <c:if test="${fjkz[0] == '1'}">
                fj = $("#fjarea #sqfjs a");
                if (fj.length == 0) {
                    fj = $("#fjarea input");
                    var num = 0;
                    $.each(fj, function (i, e) {
                        if ($(e).val() != "") {
                            num++;
                            return false;
                        }
                    });
                    if (num == 0) {
                        urp.alert("请上传附件！");
                        return;
                    }
                }
                </c:if>

                var exts = "${fjkz[3]}";

                fj = $("#fjarea input");
                for (var i = 0; i < fj.length; i++) {
                    var fname = $(fj[i]).val();
                    if (fname != "") {
                        var fileExt = fname.substring(fname.lastIndexOf('.') + 1);
                        var extArr = exts.split(",");
                        var pass = false;
                        for (var j = 0; j < extArr.length; j++) {
                            if (fileExt.toLowerCase() == extArr[j].toLowerCase()) {
                                pass = true;
                                break;
                            }
                        }
                        if (!pass) {
                            urp.alert("请检查文件格式！");
                            return false;
                        }

                        var fsize = $(fj[i])[0].files[0].size;
                        if ("${fjkz[2]}" != "" && fsize > parseFloat("${fjkz[2]}") * 1024 * 1024) {
                            urp.alert("请检查文件大小！");
                            return false;
                        }
                    }
                }


                var formData = new FormData($("#addTeacherInfoForm")[0]);
                formData.append("tokenValue", $("#tokenValue").val());
                formData.append("apply_status", apply_status);
                var yxfj = $("#fjarea #sqfjs a");
                var yxfjids = "";
                if (yxfj.length > 0) {
                    $.each(yxfj, function (i, e) {
                        yxfjids += i == 0 ? "" : ",";
                        yxfjids += $(e).attr("fjid");
                    });
                }
                formData.append("fjids", yxfjids);
                var spjsh = $("#spjsh").val();

                if (apply_status == 1 && checkSpgzlZdspr() == 1 && spjsh == "") {
                    var url = "/student/application/gradeChange/selectApprover?&sqbh=${sqbh}";
                    var modal = urp.addModelHt("60%", "add_model");
                    modal.modal({
                        backdrop: 'static',
                        keyboard: false,
                        remote: url
                    }).on('hide.bs.modal', function () {
                        modal.next().remove();
                        modal.remove();
                        return false;
                    });
                    modal.css("z-index", "1070");
                    modal.next().css("z-index", "1060");
                } else {
                    var index;
                    $.ajax({
                        url: "/student/application/gradeChange/saveInfo",
                        type: 'post',
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        beforeSend: function () {
                            index = layer.load(0, {
                                shade: [0.2, "#000"]
                            });
                        },
                        complete: function () {
                            layer.close(index);
                        },
                        success: function (d) {
                            var data = d.data;
                            if (data["result"].indexOf("/") != -1) {
                                layer.alert("页面已过期，请刷新页面！", {skin: "layui-layer-molv", closeBtn: 0});
                            } else {
                                $("#tokenValue").val(data["token"]);
                                if (data["result"] == "ok") {
                                    urp.alert(apply_status == "0" ? "暂存成功！" : "提交成功！", function () {
                                        getApplysList(1, '30_sl', true);
                                        $("#closeZdspr").click();
                                        $("#close_add").click();
                                    });
                                } else {
                                    urp.alert(data["result"]);
                                }
                            }
                        },
                        error: function (returndata) {
                            urp.alert("操作失败！");
                        }
                    });
                }
            }
        }

        function checkSpgzlZdspr() {
            var index;
            var result = 0;
            $.ajax({
                url: "/student/personalManagement/individualApplication/index/checkSpgzlZdspr",
                cache: false,
                async: false,
                type: "post",
                data: "apply_type=10045",
                dataType: "json",
                success: function (d) {
                    result = d.data;
                },
                error: function (xhr) {
                    urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                beforeSend: function () {
                    index = layer.load(0, {
                        shade: [0.2, "#000"]
                    });
                },
                complete: function () {
                    layer.close(index);
                }
            });
            return result;
        }

        function initFile() {
            $("[id^='id-input-file-']").ace_file_input({
                no_file: '未选择文件 ...',
                btn_choose: '选择',
                btn_change: '重选',
                droppable: false,
                onchange: null,
                thumbnail: false
            });
        }

        var fjIndex = 1;

        function addFj() {
            $("#fjarea").append("<div>\
						<i class='fa fa-trash-o idanger bigger-120' title='删除附件' onclick='$(this).parent().remove();'></i>\
						<input type='file' name='sqfj' id='id-input-file-" + fjIndex + "'/></div>");
            initFile();
            fjIndex++;
        }

        function searchScore() {
            var url = "/student/application/gradeChange/selectGrade/page";
            var modal = urp.addModelHt("60%", "selectCoursePage");
            $("#selectCoursePage").css("z-index", "1070");
            modal.modal({
                remote: url
            }).on('hide.bs.modal', function () {
                modal.next().remove();
                modal.remove();
                return false;
            });
            $("#selectCoursePage").next().css("z-index", "1060");
        }
    </script>
</head>
<body>
<form name="addTeacherInfoForm" id="addTeacherInfoForm" method="post" enctype="multipart/form-data">
    <div class="modal-header no-padding">
        <div class="table-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true" id="close_add">
                <span class="white">×</span>
            </button>
            <i class="fa fa-info-circle"></i>
            <c:if test="${empty sqxx}">新增申请</c:if>
            <c:if test="${not empty sqxx}">修改申请</c:if>
        </div>
    </div>

    <div class="modal-body" style="max-height: calc(100vh - 160px); overflow: auto;">
        <input type="hidden" name="sqbh" value="${empty sqxx ? zxjxjhh : sqxx[0]}"/>
        <input type="hidden" name="spjsh" id="spjsh"/>
        <div class=" profile-user-info profile-user-info-striped self">
            <div class="profile-info-row">
                <div class="profile-info-name">课程课序</div>
                <div class="profile-info-value">
                    <input type="hidden" id="kch" name="kch" value="${empty sqxx ? '' : sqxx[3]}"/>
                    <input type="hidden" id="kxh" name="kxh" value="${empty sqxx ? '' : sqxx[5]}"/>
                    <span id="kxcc_xs"> <c:if test="${not empty sqxx}">【${sqxx[3]}_${sqxx[5]}】${sqxx[4]}</c:if></span>
                    <c:if test="${empty sqxx}">
                        <i title="查询成绩记录" class="fa fa-search iinfo" onclick="searchScore();"></i>
                    </c:if>
                </div>
            </div>
            <div class="profile-info-row">
                <div class="profile-info-name" style="width: 150px;">成绩学年学期</div>
                <div class="profile-info-value">
                    <input type="hidden" id="zxjxjhh" name="zxjxjhh" value="${empty sqxx ? '' : sqxx[1]}"/>
                    <span id="zxjxjhh_xs"><c:if test="${not empty sqxx}">${sqxx[2]}</c:if></span>
                </div>
            </div>
            <c:forEach items="${sysColConfigList }" var="sysColConfig" varStatus="i">
                <div class="profile-info-row">
                    <div class="profile-info-name">${sysColConfig.colname }<c:if
                            test="${sysColConfig.colnotnull == 1 }"><font color="red">*</font> </c:if></div>
                    <div class="profile-info-value">
                        <c:if test="${!empty sysColConfig.colguide}">
                            <span class="red">填写说明：${sysColConfig.colguide}</span>
                        </c:if>
                        <c:if test="${sysColConfig.coltype == 'radio' }">
                            <c:forEach items="${sysColConfig.colexp }" var="colexps">
                                <label>
                                    <input name="${sysColConfig.colid }" type="radio" class="ace"
                                           <c:if test="${sysColConfig.colnotnull == 1 }">required</c:if>
                                           value="${colexps }"
                                           <c:if test="${(sysColConfig.colid == 'c1' ? sqxx[9] : (sysColConfig.colid == 'c2' ? sqxx[10] : (sysColConfig.colid == 'c3' ? sqxx[11] :
											(sysColConfig.colid == 'c4' ? sqxx[12] : (sysColConfig.colid == 'c5' ? sqxx[13] : ''))))) == colexps}">checked</c:if>>
                                    <span class="lbl"> ${colexps }</span>
                                </label>
                            </c:forEach>
                        </c:if>
                        <c:if test="${sysColConfig.coltype == 'check' }">
                            <c:forEach items="${sysColConfig.colexp }" var="colexps">
                                <label>
                                    <input name="${sysColConfig.colid }" type="checkbox" class="ace"
                                           <c:if test="${sysColConfig.colnotnull == 1 }">required</c:if>
                                           value="${colexps }"
                                    <c:forEach var="colids" items="${(sysColConfig.colid == 'c1' ? sqxx[9] : (sysColConfig.colid == 'c2' ? sqxx[10] : (sysColConfig.colid == 'c3' ?
												sqxx[11] : (sysColConfig.colid == 'c4' ? sqxx[12] : (sysColConfig.colid == 'c5' ? sqxx[13] : '')))))}">
                                           <c:if test="${colids == colexps}">checked</c:if>
                                    </c:forEach>>
                                    <span class="lbl"> ${colexps }</span>
                                </label>
                            </c:forEach>
                        </c:if>
                        <c:if test="${sysColConfig.coltype == 'input' }">
                            <input name="${sysColConfig.colid }" type="text"
                                   <c:if test="${sysColConfig.colnotnull == 1 }">required</c:if> value="${(sysColConfig.colid == 'c1' ? sqxx[9]
									: (sysColConfig.colid == 'c2' ? sqxx[10] : (sysColConfig.colid == 'c3' ? sqxx[11] : (sysColConfig.colid == 'c4' ? sqxx[12] : (sysColConfig.colid == 'c5' ? sqxx[13] : '')))))}"
                                   diyCheck="${sysColConfig.regname }不合法" diyRule="${sysColConfig.regexp }"
                                   stringMaxLength="200">
                        </c:if>
                    </div>
                </div>
            </c:forEach>

            <div class="profile-info-row">
                <div class="profile-info-name"> 申请原因<span style="color: red;">*</span></div>
                <div class="profile-info-value">
                    <textarea name="sqyy" id="edit_sqyy" required stringMaxLength="500"
                              style="width: 90%; height: 50px;">${sqxx[6]}</textarea>
                </div>
            </div>
            <div class="profile-info-row">
                <div class="profile-info-name"> 上传附件说明</div>
                <div class="profile-info-value">${fjkz[1]}</div>
            </div>
            <div class="profile-info-row">
                <div class="profile-info-name"> 上传附件类型</div>
                <div class="profile-info-value">${fjkz[3]}</div>
            </div>
            <div class="profile-info-row">
                <div class="profile-info-name"> 上传附件大小</div>
                <div class="profile-info-value">${fjkz[2]}M</div>
            </div>
            <div class="profile-info-row">
                <div class="profile-info-name">
                    附件<c:if test="${fjkz[0] == '1'}"><span style="color: red;">*</span></c:if>
                    <i class="fa fa-plus isuccess" title="增加附件" onclick="addFj();"></i>
                </div>
                <div class="profile-info-value" id="fjarea">
                    <div id="sqfjs">
                        <c:forEach items="${fjs}" var="fj">
									<span><a target="_blank" fjid="${fj[0]}"
                                             href="/student/application/scoreCheck/downLoad/${fj[0]}">${fj[1]}</a>
									<i title="删除附件" style="margin-left: 10px;" class='fa fa-trash-o idanger'
                                       onclick="$(this).parent().next().remove(); $(this).parent().remove();"></i></span><br>
                        </c:forEach>
                    </div>
                    <div>
                        <i class="fa fa-trash-o idanger bigger-120" title="删除附件"
                           onclick="$(this).parent().remove();"></i>
                        <input type="file" name="sqfj" id="id-input-file-0"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" title="暂存" class="btn btn-purple btn-xs btn-round" onclick="saveInfo('0'); return false;">
            <i class="ace-icon fa fa-floppy-o bigger-120"></i> 暂存
        </button>
        <button type="button" class="btn btn-purple btn-xs btn-round" onclick="saveInfo('1');">
            <i class="ace-icon fa fa-check bigger-130"></i> 提交
        </button>
    </div>
</form>
</body>
</html>
