<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>监护人信息采集</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 信息采集页面样式 */
        .collection-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .collection-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .collection-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .info-notice {
            background: var(--info-light);
            color: var(--info-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--info-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .info-notice i {
            color: var(--info-color);
            margin-right: 8px;
        }
        
        .form-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .section-title i {
            color: var(--primary-color);
        }
        
        .guardian-number {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 600;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .required {
            color: var(--error-color);
            margin-right: 4px;
        }
        
        .form-input, .form-select {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-input.error, .form-select.error {
            border-color: var(--error-color);
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
        }
        
        .error-message {
            color: var(--error-color);
            font-size: var(--font-size-mini);
            margin-top: 4px;
        }
        
        .form-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-save {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .btn-reset {
            flex: 1;
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            flex: 1;
            background: var(--text-disabled);
            color: white;
        }
        
        .validation-tips {
            background: var(--warning-light);
            color: var(--warning-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .validation-tips ul {
            margin: 8px 0 0 0;
            padding-left: 20px;
        }
        
        .validation-tips li {
            margin-bottom: 4px;
        }
        
        @media (max-width: 480px) {
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">监护人信息采集</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 信息采集头部 -->
        <div class="collection-header">
            <div class="collection-title">监护人信息采集</div>
            <div class="collection-desc">填写和管理监护人信息</div>
        </div>
        
        <!-- 提示信息 -->
        <div class="info-notice">
            <i class="ace-icon fa fa-info-circle"></i>
            请至少填写一位监护人的完整信息，包括称谓、姓名、证件类型和证件号码。
        </div>
        
        <!-- 验证提示 -->
        <div class="validation-tips">
            <strong>填写要求：</strong>
            <ul>
                <li>监护人称谓：最多5个字符</li>
                <li>监护人姓名：最多50个字符</li>
                <li>证件类型码：最多3个字符</li>
                <li>证件号码：最多20个字符</li>
                <li>身份证号码将进行格式验证</li>
                <li>港澳台居民居住证号将进行格式验证</li>
            </ul>
        </div>
        
        <form id="frm1" name="frm1">
            <!-- 监护人1信息 -->
            <div class="form-section">
                <div class="section-title">
                    <div class="guardian-number">1</div>
                    <i class="ace-icon fa fa-user"></i>
                    监护人1信息
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">监护人称谓</label>
                        <input type="text" id="jhrcw1" name="jhrcw1" class="form-input" 
                               placeholder="请填写监护人称谓" maxlength="5" value="${xsjhrsj.jhrcw1}">
                        <input type="hidden" id="jhrcw11" name="jhrcw11" value="${xsjhrsj.jhrcw11}">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">监护人姓名</label>
                        <input type="text" id="jhrxm1" name="jhrxm1" class="form-input" 
                               placeholder="请输入监护人姓名" maxlength="50" value="${xsjhrsj.jhrxm1}">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">证件类型</label>
                        <select id="zjlxm1" name="zjlxm1" class="form-select">
                            <option value="">请选择证件类型</option>
                            <cache:query var="zjlxb" fields="zjlxm,zjlx" region="code_zjlxb" orderby="zjlxm asc"/>
                            <c:forEach items="${zjlxb}" var="zjlx">
                                <option value="${zjlx.zjlxm}" <c:if test="${xsjhrsj.zjlxm1 == zjlx.zjlxm}">selected</c:if>>${zjlx.zjlx}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">证件号码</label>
                        <input type="text" id="zjhm1" name="zjhm1" class="form-input" 
                               placeholder="请输入证件号码" maxlength="20" value="${xsjhrsj.zjhm1}">
                    </div>
                </div>
            </div>
            
            <!-- 监护人2信息 -->
            <div class="form-section">
                <div class="section-title">
                    <div class="guardian-number">2</div>
                    <i class="ace-icon fa fa-user"></i>
                    监护人2信息
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">监护人称谓</label>
                        <input type="text" id="jhrcw2" name="jhrcw2" class="form-input" 
                               placeholder="请填写监护人称谓" maxlength="5" value="${xsjhrsj.jhrcw2}">
                        <input type="hidden" id="jhrcw22" name="jhrcw22" value="${xsjhrsj.jhrcw22}">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">监护人姓名</label>
                        <input type="text" id="jhrxm2" name="jhrxm2" class="form-input" 
                               placeholder="请输入监护人姓名" maxlength="50" value="${xsjhrsj.jhrxm2}">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">证件类型</label>
                        <select id="zjlxm2" name="zjlxm2" class="form-select">
                            <option value="">请选择证件类型</option>
                            <c:forEach items="${zjlxb}" var="zjlx">
                                <option value="${zjlx.zjlxm}" <c:if test="${xsjhrsj.zjlxm2 == zjlx.zjlxm}">selected</c:if>>${zjlx.zjlx}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">证件号码</label>
                        <input type="text" id="zjhm2" name="zjhm2" class="form-input" 
                               placeholder="请输入证件号码" maxlength="20" value="${xsjhrsj.zjhm2}">
                    </div>
                </div>
            </div>
            
            <!-- 底部留白，避免被固定按钮遮挡 -->
            <div style="height: 80px;"></div>
        </form>
        
        <!-- 固定底部操作按钮 -->
        <div class="form-actions">
            <button type="button" class="btn-mobile btn-save" onclick="doSave();">
                <i class="ace-icon fa fa-save"></i>
                <span>保存</span>
            </button>
            <button type="button" class="btn-mobile btn-reset" onclick="resettj();">
                <i class="ace-icon fa fa-refresh"></i>
                <span>重置</span>
            </button>
            <button type="button" class="btn-mobile btn-cancel" onclick="goBack();">
                <i class="ace-icon fa fa-times"></i>
                <span>取消</span>
            </button>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
            initValidation();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化验证
        function initValidation() {
            // 实时验证
            $('.form-input, .form-select').on('blur', function() {
                validateField($(this));
            });
        }

        // 字符串去空格
        function trim(str) {
            return str.replace(/(^\s*)|(\s*$)/g, "");
        }

        // 验证单个字段
        function validateField($field) {
            const fieldId = $field.attr('id');
            const value = trim($field.val());
            const maxLength = $field.attr('maxlength');
            let isValid = true;
            let errorMsg = '';

            // 清除之前的错误状态
            $field.removeClass('error');
            $field.siblings('.error-message').remove();

            // 长度验证
            if (maxLength && value.length > parseInt(maxLength)) {
                isValid = false;
                errorMsg = `最大长度不能超过${maxLength}个字符`;
            }

            // 身份证验证
            if (fieldId === 'zjhm1' || fieldId === 'zjhm2') {
                const zjlxmId = fieldId === 'zjhm1' ? 'zjlxm1' : 'zjlxm2';
                const zjlxm = $('#' + zjlxmId).val();

                if (value && zjlxm === '1') {
                    if (!IdentityCodeValid(value)) {
                        isValid = false;
                        errorMsg = '身份证号格式不正确';
                    }
                } else if (value && zjlxm === 'C') {
                    if (!LMSCodeValid(value)) {
                        isValid = false;
                        errorMsg = '港澳台居民居住证号格式不正确';
                    }
                }
            }

            if (!isValid) {
                $field.addClass('error');
                $field.after(`<div class="error-message">${errorMsg}</div>`);
            }

            return isValid;
        }

        // 验证表单
        function verify() {
            let isValid = true;

            // 验证所有字段
            $('.form-input, .form-select').each(function() {
                if (!validateField($(this))) {
                    isValid = false;
                }
            });

            // 检查是否至少填写了一位监护人的完整信息
            const jhrcw1 = trim($('#jhrcw1').val());
            const jhrcw11 = trim($('#jhrcw11').val());
            const jhrxm1 = trim($('#jhrxm1').val());
            const zjlxm1 = trim($('#zjlxm1').val());
            const zjhm1 = trim($('#zjhm1').val());

            const jhrcw2 = trim($('#jhrcw2').val());
            const jhrcw22 = trim($('#jhrcw22').val());
            const jhrxm2 = trim($('#jhrxm2').val());
            const zjlxm2 = trim($('#zjlxm2').val());
            const zjhm2 = trim($('#zjhm2').val());

            const guardian1Complete = ((jhrcw1 || jhrcw11) && jhrxm1 && zjlxm1 && zjhm1);
            const guardian2Complete = ((jhrcw2 || jhrcw22) && jhrxm2 && zjlxm2 && zjhm2);

            if (!guardian1Complete && !guardian2Complete) {
                showError("请您至少填写一位监护人完整信息!");
                return false;
            }

            // 身份证验证
            if (guardian1Complete && zjlxm1 === '1' && zjhm1) {
                if (!IdentityCodeValid(zjhm1)) {
                    showError(`您填写的${jhrxm1}（先生/女士）身份证号验证失败！`);
                    return false;
                }
            } else if (guardian1Complete && zjlxm1 === 'C' && zjhm1) {
                if (!LMSCodeValid(zjhm1)) {
                    showError(`您填写的${jhrxm1}（先生/女士）港澳台居民居住证号验证失败！`);
                    return false;
                }
            }

            if (guardian2Complete && zjlxm2 === '1' && zjhm2) {
                if (!IdentityCodeValid(zjhm2)) {
                    showError(`您填写的${jhrxm2}（先生/女士）身份证号验证失败！`);
                    return false;
                }
            } else if (guardian2Complete && zjlxm2 === 'C' && zjhm2) {
                if (!LMSCodeValid(zjhm2)) {
                    showError(`您填写的${jhrxm2}（先生/女士）港澳台居民居住证号验证失败！`);
                    return false;
                }
            }

            return isValid;
        }

        // 身份证验证
        function IdentityCodeValid(code) {
            const city = {11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外"};
            let pass = true;

            if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
                pass = false;
            } else if (!city[code.substr(0,2)]) {
                pass = false;
            } else {
                // 18位身份证需要验证最后一位校验位
                if (code.length == 18) {
                    code = code.split('');
                    // 加权因子
                    const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
                    // 校验位
                    const parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
                    let sum = 0;
                    for (let i = 0; i < 17; i++) {
                        sum += code[i] * factor[i];
                    }
                    if (parity[sum % 11] != code[17]) {
                        pass = false;
                    }
                }
            }
            return pass;
        }

        // 港澳台居民居住证验证
        function LMSCodeValid(code) {
            let pass = true;
            if (!code || !/^(81|82|83)?0{4}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
                pass = false;
            }
            return pass;
        }

        // 保存数据
        function doSave() {
            if (!verify()) {
                return;
            }

            // 处理称谓字段
            const jhrcw11 = trim($('#jhrcw11').val());
            if (jhrcw11 === '请填写监护人称谓') {
                $('#jhrcw11').val("");
            }
            const jhrcw22 = trim($('#jhrcw22').val());
            if (jhrcw22 === '请填写监护人称谓') {
                $('#jhrcw22').val("");
            }

            showLoading(true);

            const formData = $('#frm1').serialize() + "&tokenValue=" + $('#tokenValue').val();

            $.ajax({
                url: "/student/rollManagement/informationCollection/update",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    $('#tokenValue').val(data.token);

                    if (data.result === "no") {
                        showError("保存失败！");
                    } else if (data.result === "yes") {
                        showSuccess("保存成功！");
                    } else {
                        window.location.href = response;
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:保存失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 重置表单
        function resettj() {
            if (confirm("重置后是否保存？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/rollManagement/informationCollection/resetAll",
                    type: "post",
                    data: "tokenValue=" + $('#tokenValue').val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $('#tokenValue').val(data.token);

                        if (data.result === "no") {
                            showError("保存失败！");
                        } else if (data.result === "yes") {
                            showSuccess("保存成功！", function() {
                                // 清空表单
                                $('#jhrxm1, #zjhm1, #jhrxm2, #zjhm2').val('');
                                $('#zjlxm1, #zjlxm2').val('');
                            });
                        } else {
                            window.location.href = response;
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:重置失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 返回
        function goBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                history.back();
            }
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
