<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>荣誉学分证明</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 荣誉学分证明页面样式 */
        .certificate-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .certificate-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .certificate-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .certificate-index {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .certificate-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
            padding-top: var(--padding-sm);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-action {
            flex: 1;
            min-height: 36px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-download {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .fab-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            z-index: 100;
            transition: all var(--transition-base);
        }
        
        .fab-button:active {
            transform: scale(0.95);
        }
        
        .certificate-preview {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            display: none;
            flex-direction: column;
        }
        
        .preview-header {
            background: var(--bg-primary);
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--divider-color);
        }
        
        .preview-title {
            font-size: var(--font-size-h4);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .preview-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-preview-action {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-print {
            background: var(--info-color);
            color: white;
        }
        
        .btn-close {
            background: var(--text-disabled);
            color: white;
        }
        
        .preview-content {
            flex: 1;
            background: white;
            overflow: auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">荣誉学分证明</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 证明列表 -->
        <div id="certificateList">
            <c:forEach items="${zmlist}" var="zm" varStatus="z">
                <div class="certificate-item">
                    <div class="certificate-header">
                        <div class="certificate-title">荣誉学分证明</div>
                        <div class="certificate-index">#${z.count}</div>
                    </div>
                    
                    <div class="certificate-details">
                        <div class="detail-item">
                            <span class="detail-label">生成时间</span>
                            <span class="detail-value">${zm[1]}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">生成IP</span>
                            <span class="detail-value">${zm[2]}</span>
                        </div>
                        <c:if test="${not empty zm[3]}">
                            <div class="detail-item full-width">
                                <span class="detail-label">备注</span>
                                <span class="detail-value">${zm[3]}</span>
                            </div>
                        </c:if>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn-action btn-view" onclick="previewCertificate('${zm[1]}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>预览</span>
                        </button>
                        <button class="btn-action btn-download" onclick="downloadCertificate('${zm[1]}');">
                            <i class="ace-icon fa fa-download"></i>
                            <span>下载</span>
                        </button>
                    </div>
                </div>
            </c:forEach>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: ${empty zmlist ? 'block' : 'none'};">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无证明记录</div>
            <button class="btn-mobile btn-primary" onclick="generateCertificate();" style="margin-top: 16px;">
                <i class="ace-icon fa fa-plus"></i>
                <span>选择学分生成</span>
            </button>
        </div>
        
        <!-- 浮动添加按钮 -->
        <c:if test="${not empty zmlist}">
            <button class="fab-button" onclick="generateCertificate();" id="fabButton">
                <i class="ace-icon fa fa-plus"></i>
            </button>
        </c:if>
        
        <!-- 证明预览 -->
        <div class="certificate-preview" id="certificatePreview">
            <div class="preview-header">
                <div class="preview-title">荣誉学分证明</div>
                <div class="preview-actions">
                    <button class="btn-preview-action btn-print" onclick="printCertificate();">
                        <i class="ace-icon fa fa-print"></i>
                        <span>打印</span>
                    </button>
                    <button class="btn-preview-action btn-close" onclick="closePreview();">
                        <i class="ace-icon fa fa-times"></i>
                        <span>关闭</span>
                    </button>
                </div>
            </div>
            <div class="preview-content">
                <img class="preview-image" id="previewImage" src="" alt="证明预览">
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentCertificateTime = '';

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 生成证明
        function generateCertificate() {
            if (parent && parent.addTab) {
                parent.addTab('选择学分生成', '/student/cxcyryxf/zmsq/xzxfsc');
            } else {
                window.location.href = '/student/cxcyryxf/zmsq/xzxfsc';
            }
        }

        // 预览证明
        function previewCertificate(scsj) {
            currentCertificateTime = scsj;
            const imageUrl = '/student/innovationCredits/innovationProject/showCjd?scsj=' + scsj + '&dt=' + new Date().getTime();
            
            $('#previewImage').attr('src', imageUrl);
            $('#certificatePreview').show();
        }

        // 下载证明
        function downloadCertificate(scsj) {
            const imageUrl = '/student/innovationCredits/innovationProject/showCjd?scsj=' + scsj + '&dt=' + new Date().getTime();
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = '荣誉学分证明_' + scsj + '.png';
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 打印证明
        function printCertificate() {
            if (currentCertificateTime) {
                const printWindow = window.open('', '', 'width=800,height=600');
                const imageUrl = '/student/innovationCredits/innovationProject/showCjd?scsj=' + currentCertificateTime + '&dt=' + new Date().getTime();
                
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>荣誉学分证明</title>
                            <style>
                                body { margin: 0; padding: 20px; text-align: center; }
                                img { max-width: 100%; height: auto; }
                            </style>
                        </head>
                        <body>
                            <img src="${imageUrl}" alt="荣誉学分证明">
                            <script>
                                window.onload = function() {
                                    window.print();
                                    window.close();
                                }
                            </script>
                        </body>
                    </html>
                `);
                printWindow.document.close();
            }
        }

        // 关闭预览
        function closePreview() {
            $('#certificatePreview').hide();
            currentCertificateTime = '';
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击预览背景关闭
        $('#certificatePreview').click(function(e) {
            if (e.target === this) {
                closePreview();
            }
        });
    </script>
</body>
</html>
