<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>转专业申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 转专业申请页面样式 */
        .transfer-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .transfer-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .transfer-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            background: var(--warning-light);
            color: var(--warning-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-section i {
            color: var(--warning-color);
            margin-right: 8px;
        }
        
        .actions-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .actions-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-apply {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .btn-return {
            flex: 1;
            background: var(--text-disabled);
            color: white;
        }
        
        .process-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .process-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .process-title i {
            color: var(--info-color);
        }
        
        .process-steps {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .process-step {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-sm) var(--padding-md);
            border-radius: 6px;
            font-size: var(--font-size-small);
            position: relative;
        }
        
        .process-step::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid var(--primary-color);
        }
        
        .process-step:last-child::after {
            display: none;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-title i {
            color: var(--primary-color);
        }
        
        .batch-tabs {
            display: flex;
            overflow-x: auto;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .batch-tab {
            flex: 0 0 auto;
            padding: var(--padding-sm) var(--padding-md);
            background: var(--bg-secondary);
            color: var(--text-secondary);
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-size: var(--font-size-small);
            white-space: nowrap;
            transition: all var(--transition-base);
        }
        
        .batch-tab.active {
            background: var(--bg-primary);
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .application-detail {
            padding: var(--padding-md);
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .detail-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        .majors-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-mini);
        }
        
        .majors-table th,
        .majors-table td {
            padding: 4px 2px;
            text-align: left;
            border-bottom: 1px solid var(--divider-color);
        }
        
        .majors-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .majors-table td {
            color: var(--text-primary);
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-operation {
            flex: 1;
            min-width: 80px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-print {
            background: var(--info-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-link {
            color: var(--primary-color);
            text-decoration: underline;
            cursor: pointer;
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .operation-buttons {
                flex-direction: column;
            }
            
            .process-steps {
                gap: var(--spacing-xs);
            }
            
            .majors-table {
                font-size: var(--font-size-mini);
            }
            
            .majors-table th,
            .majors-table td {
                padding: 2px 1px;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">转专业申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 转专业申请头部 -->
        <div class="transfer-header">
            <div class="transfer-title">转专业申请</div>
            <div class="transfer-desc">申请和管理转专业</div>
        </div>
        
        <!-- 提示信息 -->
        <c:if test="${not empty msg}">
            <div class="notice-section">
                <i class="ace-icon fa fa-exclamation-circle"></i>
                ${msg}
            </div>
        </c:if>
        
        <!-- 操作按钮 -->
        <div class="actions-section">
            <div class="actions-title">
                <i class="ace-icon fa fa-cogs"></i>
                操作
            </div>
            <div class="action-buttons">
                <c:if test="${show}">
                    <button class="btn-mobile btn-apply" onclick="addSpecialties();">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>申请</span>
                    </button>
                </c:if>
                <button class="btn-mobile btn-return" onclick="returnIndex();">
                    <i class="ace-icon fa fa-reply"></i>
                    <span>返回</span>
                </button>
            </div>
        </div>
        
        <!-- 申请信息 -->
        <c:choose>
            <c:when test="${not empty sqpcs && fn:length(sqpcs) > 0}">
                <div class="applications-section">
                    <div class="applications-header">
                        <div class="applications-title">
                            <i class="ace-icon fa fa-list"></i>
                            申请信息
                        </div>
                    </div>
                    
                    <!-- 批次选项卡 -->
                    <div class="batch-tabs" id="batchTabs">
                        <c:forEach var="sqbh" items="${sqpcs}" varStatus="sqbhsta">
                            <button class="batch-tab <c:if test='${sqbhsta.first}'>active</c:if>" 
                                    data-sqbh="${sqbh[0]}" data-index="${sqbhsta.index}"
                                    onclick="switchBatch(${sqbhsta.index}, '${sqbh[0]}');">
                                批次:${sqbh[2]}
                            </button>
                        </c:forEach>
                    </div>
                    
                    <!-- 申请详情 -->
                    <div class="application-detail" id="applicationDetail">
                        <!-- 动态加载申请详情 -->
                    </div>
                </div>
                
                <!-- 审批流程 -->
                <div class="process-section">
                    <div class="process-title">
                        <i class="ace-icon fa fa-sitemap"></i>
                        审批流程
                    </div>
                    <div class="process-steps" id="processSteps">
                        <!-- 动态加载审批流程 -->
                    </div>
                </div>
            </c:when>
            <c:otherwise>
                <c:if test="${show}">
                    <div class="applications-section">
                        <div class="empty-state">
                            <i class="ace-icon fa fa-file-text-o"></i>
                            <div>您还未申请转专业，请点击<span class="empty-link" onclick="addSpecialties();">这里</span>查看可转入院系专业信息</div>
                        </div>
                    </div>
                </c:if>
            </c:otherwise>
        </c:choose>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentBatchIndex = 0;
        let applicationData = null;

        $(function() {
            initPage();
            initProcessSteps();

            // 如果有申请数据，加载第一个批次
            if ($('.batch-tab').length > 0) {
                const firstTab = $('.batch-tab').first();
                const sqbh = firstTab.data('sqbh');
                switchBatch(0, sqbh);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化审批流程
        function initProcessSteps() {
            const schoolId = '${schoolId}';
            const processSteps = $('#processSteps');
            let stepsHtml = '';

            if (schoolId === '100053') {
                stepsHtml = `
                    <div class="process-step">1.转出学院审核报名资格</div>
                    <div class="process-step">2.教务处复审报名资格</div>
                    <div class="process-step">3.学生参加考核</div>
                    <div class="process-step">4.转入学院录取</div>
                    <div class="process-step">5.教务处复审，修改学籍信息</div>
                `;
            } else {
                const zcshf = '${zcshf}';
                const zcshbm = '${zcshbm}';
                const zrxyfsf = '${zrxyfsf}';
                const zsspbm = '${zsspbm}';
                const jwcname = '${jwcname}';

                if (zcshf === '1') {
                    if (zcshbm === 'JWC' || zcshbm === 'YX') {
                        if (zcshbm === 'JWC') {
                            stepsHtml += '<div class="process-step">1.转出时，' + jwcname + '审核</div>';
                        } else {
                            stepsHtml += '<div class="process-step">1.转出时，转出院系审核</div>';
                        }
                        stepsHtml += '<div class="process-step">2.转入院系拟录取</div>';
                        if (zrxyfsf === '1') {
                            const reviewDept = zsspbm === 'JWC' ? jwcname : '院系';
                            stepsHtml += '<div class="process-step">3.' + reviewDept + '复审，修改学籍信息</div>';
                        }
                    } else if (zcshbm === 'YXJWC' || zcshbm === 'JWCYX') {
                        if (zcshbm === 'YXJWC') {
                            stepsHtml += '<div class="process-step">1.转出院系预审</div>';
                            stepsHtml += '<div class="process-step">2.' + jwcname + '转出审批</div>';
                        } else {
                            stepsHtml += '<div class="process-step">1.' + jwcname + '预审</div>';
                            stepsHtml += '<div class="process-step">2.转出院系转出审批</div>';
                        }
                        stepsHtml += '<div class="process-step">3.转入院系拟录取</div>';
                        if (zrxyfsf === '1') {
                            const reviewDept = zsspbm === 'JWC' ? jwcname : '院系';
                            stepsHtml += '<div class="process-step">4.' + reviewDept + '复审，修改学籍信息</div>';
                        }
                    }
                } else {
                    stepsHtml += '<div class="process-step">1.转入院系拟录取</div>';
                    if (zrxyfsf === '1') {
                        const reviewDept = zsspbm === 'JWC' ? jwcname : '院系';
                        stepsHtml += '<div class="process-step">2.' + reviewDept + '复审，修改学籍信息</div>';
                    }
                }
            }

            processSteps.html(stepsHtml);
        }

        // 切换批次
        function switchBatch(index, sqbh) {
            // 更新选中状态
            $('.batch-tab').removeClass('active');
            $(`.batch-tab[data-index="${index}"]`).addClass('active');

            currentBatchIndex = index;
            loadApplicationDetail(sqbh);
        }

        // 加载申请详情
        function loadApplicationDetail(sqbh) {
            showLoading(true);

            $.ajax({
                url: "/student/ordertransfer/specialty/application/index/" + sqbh,
                type: "post",
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    applicationData = data;
                    renderApplicationDetail(data, sqbh);
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请详情
        function renderApplicationDetail(data, sqbh) {
            const container = $('#applicationDetail');
            const schoolId = '${schoolId}';

            // 构建转入专业表格
            let majorsTableHtml = '';
            if (data.xsZzyZybs && data.xsZzyZybs.length > 0) {
                majorsTableHtml = `
                    <table class="majors-table">
                        <thead>
                            <tr>
                                <th>志愿序号</th>
                                <th>转入年级</th>
                                <th>转入院系</th>
                                <th>转入专业</th>
                                ${schoolId === '100008' ? '<th>文理分类</th>' : ''}
                                <th>计划接收人数</th>
                                ${(schoolId !== '100036' && schoolId !== '100053') ? '<th>已选人数</th>' : ''}
                                ${data.yxckpm ? '<th>' + (schoolId === '100060' ? 'WAVG排名' : '绩点排名') + '</th>' : ''}
                                ${(schoolId !== '100036' && schoolId !== '100053') ? '<th>笔试成绩</th><th>面试成绩</th><th>合成成绩</th>' : ''}
                                ${(schoolId === '100053' || schoolId === '100060') ? '<th>面试时间</th><th>面试地点</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
                `;

                data.xsZzyZybs.forEach(function(item) {
                    majorsTableHtml += `
                        <tr>
                            <td>${item[12] || ''}</td>
                            <td>${item[1] || ''}</td>
                            <td>${item[3] || ''}（${item[2] || ''}）</td>
                            <td>${item[5] || ''}（${item[4] || ''}）</td>
                            ${schoolId === '100008' ? `<td>${item[13] || ''}</td>` : ''}
                            <td>${item[8] || ''}</td>
                            ${(schoolId !== '100036' && schoolId !== '100053') ? `<td>${item[9] || ''}</td>` : ''}
                            ${data.yxckpm ? `<td>${item[11] || ''}</td>` : ''}
                            ${(schoolId !== '100036' && schoolId !== '100053') ? `<td>${item[14] || ''}</td><td>${item[15] || ''}</td><td>${item[16] || ''}</td>` : ''}
                            ${(schoolId === '100053' || schoolId === '100060') ? `<td>${item[17] || ''}</td><td>${item[18] || ''}</td>` : ''}
                        </tr>
                    `;
                });

                majorsTableHtml += '</tbody></table>';
            }

            // 构建审批信息表格
            let approvalTableHtml = '';
            if (data.xsZzyspbs && data.xsZzyspbs.length > 0) {
                approvalTableHtml = `
                    <table class="majors-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>审批环节</th>
                                <th>审批院系</th>
                                <th>审批人</th>
                                <th>审批结果</th>
                                <th>审批意见</th>
                                <th>审批时间</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                data.xsZzyspbs.forEach(function(item, index) {
                    approvalTableHtml += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item[0] || ''}</td>
                            <td>${item[2] || ''}</td>
                            <td>${item[4] || ''}</td>
                            <td>${item[8] || ''}</td>
                            <td title="${item[7] || ''}">${item[7] || ''}</td>
                            <td>${item[5] || ''}</td>
                        </tr>
                    `;
                });

                approvalTableHtml += '</tbody></table>';
            } else {
                approvalTableHtml = '<div class="empty-state"><i class="ace-icon fa fa-clock-o"></i><div>暂无审批信息</div></div>';
            }

            // 构建录取结果表格
            let resultTableHtml = '';
            if (data.xsZzylqjgbs && data.xsZzylqjgbs.length > 0) {
                resultTableHtml = `
                    <table class="majors-table">
                        <thead>
                            <tr>
                                <th>年级</th>
                                <th>院系</th>
                                <th>专业</th>
                                ${schoolId === '100018' ? '<th>专业方向</th>' : ''}
                                <th>班级</th>
                                ${schoolId !== '100036' ? '<th>校区</th>' : ''}
                                <th>培养方案</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                data.xsZzylqjgbs.forEach(function(item) {
                    resultTableHtml += `
                        <tr>
                            <td>${item[0] || ''}</td>
                            <td>${item[1] || ''}</td>
                            <td>${item[2] || ''}</td>
                            ${schoolId === '100018' ? `<td>${item[3] || ''}</td>` : ''}
                            <td>${item[4] || ''}</td>
                            ${schoolId !== '100036' ? `<td>${item[5] || ''}</td>` : ''}
                            <td>${item[6] || ''}</td>
                        </tr>
                    `;
                });

                resultTableHtml += '</tbody></table>';
            } else {
                resultTableHtml = '<div class="empty-state"><i class="ace-icon fa fa-clock-o"></i><div>暂无录取结果</div></div>';
            }

            // 构建操作按钮
            let operationButtons = '';
            if (data.xsZzySqb.sqztdm === '00') {
                operationButtons = `
                    <button class="btn-operation btn-edit" onclick="updateSpecialties('${sqbh}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                    <button class="btn-operation btn-submit" onclick="doSpecialties('${sqbh}', '${data.xsZzySqb.zzypch}');">
                        <i class="ace-icon fa fa-check"></i>
                        <span>提交</span>
                    </button>
                    <button class="btn-operation btn-delete" onclick="deleteSpecialties('${sqbh}');">
                        <i class="ace-icon fa fa-trash"></i>
                        <span>撤销</span>
                    </button>
                `;
            } else {
                operationButtons = `
                    <button class="btn-operation btn-print" onclick="printSpecialties('${sqbh}');">
                        <i class="ace-icon fa fa-print"></i>
                        <span>打印</span>
                    </button>
                `;
            }

            const detailHtml = `
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">申请批次</div>
                        <div class="detail-value">${data.zzypcm || ''}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">学号</div>
                        <div class="detail-value">${data.xh || ''}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">姓名</div>
                        <div class="detail-value">${data.xm || ''}</div>
                    </div>

                    ${schoolId !== '100053' ? `
                    <div class="detail-item">
                        <div class="detail-label">${schoolId === '100060' ? 'WAVG' : '绩点'}</div>
                        <div class="detail-value">${data.xsZzySqb.gpa || ''}</div>
                    </div>
                    ` : ''}

                    <div class="detail-item">
                        <div class="detail-label">申请转入专业</div>
                        <div class="detail-value">${majorsTableHtml}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">联系方式</div>
                        <div class="detail-value">${data.xsZzySqb.lxfs || ''}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">申请理由</div>
                        <div class="detail-value">${data.xsZzySqb.sqly || ''}</div>
                    </div>

                    ${schoolId !== '100053' ? `
                    <div class="detail-item">
                        <div class="detail-label">个人专长</div>
                        <div class="detail-value">${data.xsZzySqb.xstc || ''}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">个人学年综合评价</div>
                        <div class="detail-value">${data.xsZzySqb.xsgrpj || ''}</div>
                    </div>
                    ` : ''}

                    ${data.sqscfj === 1 ? `
                    <div class="detail-item">
                        <div class="detail-label">附件</div>
                        <div class="detail-value">${data.fjxx || ''}</div>
                    </div>
                    ` : ''}

                    <div class="detail-item">
                        <div class="detail-label">备注</div>
                        <div class="detail-value">${data.xsZzySqb.bz || ''}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">操作</div>
                        <div class="detail-value">
                            <div class="operation-buttons">
                                ${operationButtons}
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">转专业审批</div>
                        <div class="detail-value">${approvalTableHtml}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">录取结果</div>
                        <div class="detail-value">${resultTableHtml}</div>
                    </div>
                </div>
            `;

            container.html(detailHtml);
        }

        // 查询可申请转专业数据
        function addSpecialties() {
            if (parent && parent.addTab) {
                parent.addTab('申请转专业', '/student/personalManagement/ordertransfer/index/specialtiesInfo');
            } else {
                location.href = "/student/personalManagement/ordertransfer/index/specialtiesInfo";
            }
        }

        // 修改申请
        function updateSpecialties(sqbh) {
            if (parent && parent.addTab) {
                parent.addTab('修改申请', '/student/personalManagement/ordertransfer/index/specialtiesInfo?sqbh=' + sqbh);
            } else {
                location.href = "/student/personalManagement/ordertransfer/index/specialtiesInfo?sqbh=" + sqbh;
            }
        }

        // 提交申请
        function doSpecialties(sqbh, zzypch) {
            if (confirm("提交后不可修改，是否确认提交？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/studentChange/addSpecialtiesInfo/edit",
                    type: "post",
                    data: "sqbh=" + sqbh + "&zzypch=" + zzypch + "&flag=02&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/") != -1) {
                            showError("页面已过期，请刷新页面！");
                        } else if (data.result === "ok") {
                            showSuccess("提交成功！", function() {
                                refreshData();
                            });
                        } else {
                            showError(data.msg);
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 撤销申请
        function deleteSpecialties(sqbh) {
            if (confirm("确定要撤回这条记录？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/ordertransfer/index/deleteSpecialties",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/") != -1) {
                            showError("页面已过期，请刷新页面！");
                        } else if (data.result === "ok") {
                            showSuccess("申请撤销成功！", function() {
                                location.reload();
                            });
                        } else {
                            showError(data.msg);
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 打印申请单
        function printSpecialties(sqbh) {
            if (parent && parent.addTab) {
                parent.addTab('打印申请单', '/student/personalManagement/ordertransfer/index/printSpecialtiesInfo/' + sqbh);
            } else {
                window.open("/student/personalManagement/ordertransfer/index/printSpecialtiesInfo/" + sqbh);
            }
        }

        // 返回首页
        function returnIndex() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                location.href = "/student/application/index";
            }
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
