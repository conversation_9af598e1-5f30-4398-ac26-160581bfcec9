<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学籍变动申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学籍变动申请页面样式 */
        .application-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .type-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .type-item:last-child {
            border-bottom: none;
        }
        
        .type-item:active {
            background: var(--bg-color-active);
        }
        
        .type-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .type-icon.suspend {
            background: var(--warning-color);
        }
        
        .type-icon.resume {
            background: var(--success-color);
        }
        
        .type-icon.transfer {
            background: var(--info-color);
        }
        
        .type-icon.withdraw {
            background: var(--error-color);
        }
        
        .type-content {
            flex: 1;
        }
        
        .type-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .type-arrow {
            color: var(--text-disabled);
            font-size: var(--font-size-small);
        }
        
        .application-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .form-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-lg);
            text-align: center;
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            display: block;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-control {
            width: 100%;
            min-height: 44px;
            padding: 12px 16px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color var(--transition-base);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-help {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            margin-top: var(--margin-xs);
            line-height: var(--line-height-base);
        }
        
        .file-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .file-upload:hover {
            border-color: var(--primary-color);
            background: rgba(24, 144, 255, 0.05);
        }
        
        .file-upload.dragover {
            border-color: var(--primary-color);
            background: rgba(24, 144, 255, 0.1);
        }
        
        .upload-icon {
            font-size: 32px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
        }
        
        .upload-hint {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .file-list {
            margin-top: var(--margin-md);
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }
        
        .file-icon {
            color: var(--primary-color);
            margin-right: var(--margin-sm);
        }
        
        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
            padding: 4px;
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-back {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-primary);
        }
        
        .application-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .list-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .app-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .app-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .status-draft {
            background: var(--text-disabled);
            color: white;
        }
        
        .app-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .app-time {
            margin-top: var(--margin-xs);
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .notice-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--info-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            margin-right: var(--margin-xs);
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学籍变动申请</div>
            <div class="navbar-action" onclick="refreshApplications();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 重要提示 -->
        <div class="notice-section">
            <div class="notice-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>重要提示</span>
            </div>
            <div class="notice-content">
                学籍变动申请需要经过严格审核，请确保提供的材料真实有效。申请提交后不可撤销，请谨慎操作。如有疑问，请联系学生事务中心。
            </div>
        </div>

        <!-- 申请类型选择 -->
        <div class="application-types" id="applicationTypes">
            <div class="type-item" onclick="showApplicationForm('suspend')">
                <div class="type-icon suspend">
                    <i class="ace-icon fa fa-pause"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">休学申请</div>
                    <div class="type-desc">因病、因事等原因需要暂停学业</div>
                </div>
                <div class="type-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="type-item" onclick="showApplicationForm('resume')">
                <div class="type-icon resume">
                    <i class="ace-icon fa fa-play"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">复学申请</div>
                    <div class="type-desc">休学期满，申请恢复学业</div>
                </div>
                <div class="type-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="type-item" onclick="showApplicationForm('transfer')">
                <div class="type-icon transfer">
                    <i class="ace-icon fa fa-exchange"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">转专业申请</div>
                    <div class="type-desc">申请转入其他专业学习</div>
                </div>
                <div class="type-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="type-item" onclick="showApplicationForm('withdraw')">
                <div class="type-icon withdraw">
                    <i class="ace-icon fa fa-sign-out"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">退学申请</div>
                    <div class="type-desc">申请退出学校，终止学业</div>
                </div>
                <div class="type-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>
        </div>

        <!-- 申请表单 -->
        <div class="application-form" id="applicationForm">
            <div class="form-title" id="formTitle">申请表单</div>

            <div class="form-group">
                <label class="form-label required">申请类型</label>
                <input type="text" class="form-control" id="applicationType" readonly>
            </div>

            <div class="form-group">
                <label class="form-label required">申请原因</label>
                <select class="form-control" id="applicationReason">
                    <option value="">请选择申请原因</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label required">详细说明</label>
                <textarea class="form-control form-textarea" id="applicationDetail"
                          placeholder="请详细说明申请的具体原因和情况..."></textarea>
                <div class="form-help">请如实填写申请的详细原因，以便审核</div>
            </div>

            <div class="form-group" id="transferMajorGroup" style="display: none;">
                <label class="form-label required">目标专业</label>
                <select class="form-control" id="targetMajor">
                    <option value="">请选择目标专业</option>
                </select>
            </div>

            <div class="form-group" id="dateGroup" style="display: none;">
                <label class="form-label required" id="dateLabel">申请日期</label>
                <input type="date" class="form-control" id="applicationDate">
            </div>

            <div class="form-group">
                <label class="form-label">联系电话</label>
                <input type="tel" class="form-control" id="contactPhone" placeholder="请输入联系电话">
                <div class="form-help">用于审核过程中的联系</div>
            </div>

            <div class="form-group">
                <label class="form-label">家长联系方式</label>
                <input type="tel" class="form-control" id="parentPhone" placeholder="请输入家长联系电话">
            </div>

            <div class="form-group">
                <label class="form-label">附件材料</label>
                <div class="file-upload" id="fileUpload" onclick="selectFiles()">
                    <div class="upload-icon">
                        <i class="ace-icon fa fa-cloud-upload"></i>
                    </div>
                    <div class="upload-text">点击上传或拖拽文件到此处</div>
                    <div class="upload-hint">支持PDF、JPG、PNG格式，单个文件不超过5MB</div>
                </div>
                <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                <div class="file-list" id="fileList"></div>
                <div class="form-help">请上传相关证明材料，如医院证明、家长同意书等</div>
            </div>

            <div class="form-actions">
                <button class="btn-mobile btn-back flex-1" onclick="hideApplicationForm();">返回</button>
                <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
                <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
            </div>
        </div>

        <!-- 申请记录 -->
        <div class="application-list">
            <div class="list-header">
                <i class="ace-icon fa fa-list"></i>
                <span>申请记录</span>
            </div>
            <div id="applicationListContent">
                <!-- 申请记录将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentApplicationType = '';
        let uploadedFiles = [];
        let applications = [];

        // 申请原因配置
        const reasonOptions = {
            suspend: [
                { value: 'illness', text: '因病休学' },
                { value: 'family', text: '家庭原因' },
                { value: 'financial', text: '经济困难' },
                { value: 'personal', text: '个人原因' },
                { value: 'other', text: '其他原因' }
            ],
            resume: [
                { value: 'recovery', text: '病情康复' },
                { value: 'resolved', text: '问题解决' },
                { value: 'ready', text: '准备就绪' }
            ],
            transfer: [
                { value: 'interest', text: '专业兴趣' },
                { value: 'career', text: '职业规划' },
                { value: 'academic', text: '学术发展' },
                { value: 'other', text: '其他原因' }
            ],
            withdraw: [
                { value: 'illness', text: '身体原因' },
                { value: 'family', text: '家庭原因' },
                { value: 'career', text: '就业需要' },
                { value: 'other', text: '其他原因' }
            ]
        };

        $(function() {
            initPage();
            loadApplications();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 文件拖拽事件
            const fileUpload = document.getElementById('fileUpload');

            fileUpload.addEventListener('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            fileUpload.addEventListener('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            fileUpload.addEventListener('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                handleFiles(e.dataTransfer.files);
            });

            // 文件选择事件
            $('#fileInput').on('change', function(e) {
                handleFiles(e.target.files);
            });
        }

        // 显示申请表单
        function showApplicationForm(type) {
            currentApplicationType = type;

            // 设置表单标题和类型
            const titles = {
                suspend: '休学申请',
                resume: '复学申请',
                transfer: '转专业申请',
                withdraw: '退学申请'
            };

            $('#formTitle').text(titles[type]);
            $('#applicationType').val(titles[type]);

            // 设置申请原因选项
            const reasonSelect = $('#applicationReason');
            reasonSelect.empty().append('<option value="">请选择申请原因</option>');

            reasonOptions[type].forEach(option => {
                reasonSelect.append(`<option value="${option.value}">${option.text}</option>`);
            });

            // 显示/隐藏特定字段
            if (type === 'transfer') {
                $('#transferMajorGroup').show();
                loadMajors();
            } else {
                $('#transferMajorGroup').hide();
            }

            if (type === 'suspend' || type === 'resume') {
                $('#dateGroup').show();
                $('#dateLabel').text(type === 'suspend' ? '休学开始日期' : '复学日期');
            } else {
                $('#dateGroup').hide();
            }

            // 重置表单
            resetForm();

            // 显示表单，隐藏类型选择
            $('#applicationTypes').hide();
            $('#applicationForm').show();

            // 滚动到顶部
            $('html, body').animate({scrollTop: 0}, 300);
        }

        // 隐藏申请表单
        function hideApplicationForm() {
            $('#applicationForm').hide();
            $('#applicationTypes').show();
            currentApplicationType = '';
            uploadedFiles = [];
        }

        // 重置表单
        function resetForm() {
            $('#applicationReason').val('');
            $('#applicationDetail').val('');
            $('#targetMajor').val('');
            $('#applicationDate').val('');
            $('#contactPhone').val('');
            $('#parentPhone').val('');
            $('#fileList').empty();
            uploadedFiles = [];
        }

        // 加载专业列表
        function loadMajors() {
            $.ajax({
                url: "/student/personalManagement/statusChange/getMajors",
                type: "post",
                dataType: "json",
                success: function(data) {
                    const majorSelect = $('#targetMajor');
                    majorSelect.empty().append('<option value="">请选择目标专业</option>');

                    if (data && data.length > 0) {
                        data.forEach(major => {
                            majorSelect.append(`<option value="${major.id}">${major.name}</option>`);
                        });
                    }
                },
                error: function() {
                    console.log('加载专业列表失败');
                }
            });
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                // 验证文件类型
                const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
                if (!allowedTypes.includes(file.type)) {
                    showError(`文件 ${file.name} 格式不支持，请选择PDF、JPG或PNG格式`);
                    return;
                }

                // 验证文件大小
                if (file.size > 5 * 1024 * 1024) {
                    showError(`文件 ${file.name} 大小超过5MB限制`);
                    return;
                }

                // 添加到文件列表
                uploadedFiles.push(file);
                addFileToList(file);
            });
        }

        // 添加文件到列表
        function addFileToList(file) {
            const fileId = 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const fileHtml = `
                <div class="file-item" id="${fileId}">
                    <i class="ace-icon fa fa-file file-icon"></i>
                    <span class="file-name">${file.name}</span>
                    <i class="ace-icon fa fa-times file-remove" onclick="removeFile('${fileId}', '${file.name}')"></i>
                </div>
            `;
            $('#fileList').append(fileHtml);
        }

        // 移除文件
        function removeFile(fileId, fileName) {
            $(`#${fileId}`).remove();
            uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
        }

        // 验证表单
        function validateForm() {
            const errors = [];

            if (!$('#applicationReason').val()) {
                errors.push('请选择申请原因');
            }

            if (!$('#applicationDetail').val().trim()) {
                errors.push('请填写详细说明');
            }

            if (currentApplicationType === 'transfer' && !$('#targetMajor').val()) {
                errors.push('请选择目标专业');
            }

            if ((currentApplicationType === 'suspend' || currentApplicationType === 'resume') && !$('#applicationDate').val()) {
                errors.push('请选择日期');
            }

            if (!$('#contactPhone').val().trim()) {
                errors.push('请填写联系电话');
            }

            if (errors.length > 0) {
                showError(errors.join('\n'));
                return false;
            }

            return true;
        }

        // 获取表单数据
        function getFormData() {
            return {
                applicationType: currentApplicationType,
                applicationReason: $('#applicationReason').val(),
                applicationDetail: $('#applicationDetail').val(),
                targetMajor: $('#targetMajor').val(),
                applicationDate: $('#applicationDate').val(),
                contactPhone: $('#contactPhone').val(),
                parentPhone: $('#parentPhone').val()
            };
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            const submitData = new FormData();

            // 添加表单数据
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            // 添加文件
            uploadedFiles.forEach((file, index) => {
                submitData.append(`file_${index}`, file);
            });

            $.ajax({
                url: "/student/personalManagement/statusChange/submitApplication",
                type: "post",
                data: submitData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        hideApplicationForm();
                        loadApplications(); // 重新加载申请记录
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 保存草稿
        function saveDraft() {
            if (!validateForm()) return;

            const formData = getFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交申请
        function submitApplication() {
            if (!validateForm()) return;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要提交申请吗？提交后将无法修改。', function(confirmed) {
                    if (confirmed) {
                        doSubmitApplication();
                    }
                });
            } else {
                if (confirm('确定要提交申请吗？提交后将无法修改。')) {
                    doSubmitApplication();
                }
            }
        }

        // 执行提交申请
        function doSubmitApplication() {
            const formData = getFormData();
            formData.isDraft = false;

            submitFormData(formData, '申请提交成功，请等待审核');
        }

        // 加载申请记录
        function loadApplications() {
            $.ajax({
                url: "/student/personalManagement/statusChange/getApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    applications = data || [];
                    renderApplications();
                },
                error: function() {
                    console.log('加载申请记录失败');
                }
            });
        }

        // 渲染申请记录
        function renderApplications() {
            const container = $('#applicationListContent');
            container.empty();

            if (applications.length === 0) {
                container.html(`
                    <div class="empty-state" style="padding: var(--padding-lg); text-align: center;">
                        <i class="ace-icon fa fa-file-text-o" style="font-size: 32px; color: var(--text-disabled); margin-bottom: var(--margin-sm);"></i>
                        <div style="color: var(--text-secondary);">暂无申请记录</div>
                    </div>
                `);
                return;
            }

            applications.forEach(app => {
                const appHtml = createApplicationItem(app);
                container.append(appHtml);
            });
        }

        // 创建申请记录项
        function createApplicationItem(app) {
            const statusClass = getStatusClass(app.status);
            const statusText = getStatusText(app.status);

            return `
                <div class="application-item" onclick="showApplicationDetail('${app.id}')">
                    <div class="app-header">
                        <div class="app-type">${app.typeName}</div>
                        <div class="app-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="app-details">
                        申请原因：${app.reasonText}<br>
                        ${app.status === 'rejected' && app.rejectReason ? '驳回原因：' + app.rejectReason : ''}
                    </div>
                    <div class="app-time">申请时间：${app.createTime}</div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'status-pending';
                case 'approved': return 'status-approved';
                case 'rejected': return 'status-rejected';
                case 'draft': return 'status-draft';
                default: return 'status-pending';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'pending': return '审核中';
                case 'approved': return '已通过';
                case 'rejected': return '已驳回';
                case 'draft': return '草稿';
                default: return '未知';
            }
        }

        // 显示申请详情
        function showApplicationDetail(appId) {
            const app = applications.find(a => a.id === appId);
            if (!app) return;

            let message = `申请类型：${app.typeName}\n`;
            message += `申请原因：${app.reasonText}\n`;
            message += `详细说明：${app.detail}\n`;
            message += `申请时间：${app.createTime}\n`;
            message += `当前状态：${getStatusText(app.status)}\n`;

            if (app.status === 'rejected' && app.rejectReason) {
                message += `驳回原因：${app.rejectReason}\n`;
            }

            if (app.approveTime) {
                message += `审核时间：${app.approveTime}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 刷新申请记录
        function refreshApplications() {
            loadApplications();
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
