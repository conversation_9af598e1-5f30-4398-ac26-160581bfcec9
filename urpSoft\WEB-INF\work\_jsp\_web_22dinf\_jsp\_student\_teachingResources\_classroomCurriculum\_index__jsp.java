/*
 * JSP generated by Resin Professional 4.0.55 (built Wed, 29 Nov 2017 03:07:06 PST)
 */

package _jsp._web_22dinf._jsp._student._teachingResources._classroomCurriculum;
import javax.servlet.*;
import javax.servlet.jsp.*;
import javax.servlet.http.*;

public class _index__jsp extends com.caucho.jsp.JavaPage
{
  private static final java.util.HashMap<String,java.lang.reflect.Method> _jsp_functionMap = new java.util.HashMap<String,java.lang.reflect.Method>();
  private boolean _caucho_isDead;
  private boolean _caucho_isNotModified;
  private com.caucho.jsp.PageManager _jsp_pageManager;
  
  public void
  _jspService(javax.servlet.http.HttpServletRequest request,
              javax.servlet.http.HttpServletResponse response)
    throws java.io.IOException, javax.servlet.ServletException
  {
    javax.servlet.http.HttpSession session = request.getSession(true);
    com.caucho.server.webapp.WebApp _jsp_application = _caucho_getApplication();
    com.caucho.jsp.PageContextImpl pageContext = _jsp_pageManager.allocatePageContext(this, _jsp_application, request, response, null, session, 8192, true, false);

    TagState _jsp_state = new TagState();

    try {
      _jspService(request, response, pageContext, _jsp_application, session, _jsp_state);
    } catch (java.lang.Throwable _jsp_e) {
      pageContext.handlePageException(_jsp_e);
    } finally {
      _jsp_state.release();
      _jsp_pageManager.freePageContext(pageContext);
    }
  }
  
  private void
  _jspService(javax.servlet.http.HttpServletRequest request,
              javax.servlet.http.HttpServletResponse response,
              com.caucho.jsp.PageContextImpl pageContext,
              javax.servlet.ServletContext application,
              javax.servlet.http.HttpSession session,
              TagState _jsp_state)
    throws Throwable
  {
    javax.servlet.jsp.JspWriter out = pageContext.getOut();
    final javax.el.ELContext _jsp_env = pageContext.getELContext();
    javax.servlet.ServletConfig config = getServletConfig();
    javax.servlet.Servlet page = this;
    javax.servlet.jsp.tagext.JspTag _jsp_parent_tag = null;
    com.caucho.jsp.PageContextImpl _jsp_parentContext = pageContext;
    response.setContentType("text/html; charset=UTF-8");
    com.caucho.jsp.IteratorLoopSupportTag _jsp_loop_1 = null;
    com.urpSoft.core.cache.redis.RedisCacheQueryTag _jsp_RedisCacheQueryTag_2 = null;
    com.urpSoft.core.pagination.pageTag.PaginationTag _jsp_PaginationTag_3 = null;

    out.write(_jsp_string0, 0, _jsp_string0.length);
    out.print((request.getContextPath()));
    out.write(_jsp_string1, 0, _jsp_string1.length);
    out.print((request.getContextPath()));
    out.write(_jsp_string2, 0, _jsp_string2.length);
    _caucho_expr_0.print(out, _jsp_env, false);
    out.write(_jsp_string3, 0, _jsp_string3.length);
    _caucho_expr_0.print(out, _jsp_env, false);
    out.write(_jsp_string4, 0, _jsp_string4.length);
    _caucho_expr_1.print(out, _jsp_env, false);
    out.write(_jsp_string5, 0, _jsp_string5.length);
    _caucho_expr_2.print(out, _jsp_env, false);
    out.write(_jsp_string6, 0, _jsp_string6.length);
    out.write(_jsp_string7, 0, _jsp_string7.length);
    _caucho_expr_1.print(out, _jsp_env, false);
    out.write(_jsp_string8, 0, _jsp_string8.length);
    out.write(_jsp_string9, 0, _jsp_string9.length);
    if (_caucho_expr_3.evalBoolean(_jsp_env)) {
      out.write(_jsp_string10, 0, _jsp_string10.length);
    }
    out.write(_jsp_string11, 0, _jsp_string11.length);
    if (_caucho_expr_4.evalBoolean(_jsp_env)) {
      out.write(_jsp_string12, 0, _jsp_string12.length);
      _caucho_expr_5.print(out, _jsp_env, false);
      out.write(_jsp_string13, 0, _jsp_string13.length);
      _jsp_loop_1 = _jsp_state.get_jsp_loop_1(pageContext, _jsp_parent_tag);
      java.lang.Object _jsp_items_4 = _caucho_expr_6.evalObject(_jsp_env);
      java.util.Iterator _jsp_iter_4 = com.caucho.jstl.rt.CoreForEachTag.getIterator(_jsp_items_4);
      _jsp_loop_1.init(0, Integer.MAX_VALUE, 1, false, false, false);
      while (_jsp_iter_4.hasNext()) {
        Object _jsp_i_4 = _jsp_iter_4.next();
        _jsp_loop_1.setCurrent(_jsp_i_4, _jsp_iter_4.hasNext());
        pageContext.setAttribute("semester", _jsp_i_4);
        out.write(_jsp_string14, 0, _jsp_string14.length);
        if (_caucho_expr_7.evalBoolean(_jsp_env)) {
          out.write(_jsp_string15, 0, _jsp_string15.length);
          _caucho_expr_8.print(out, _jsp_env, false);
          out.write(_jsp_string16, 0, _jsp_string16.length);
          _caucho_expr_9.print(out, _jsp_env, false);
          out.write(_jsp_string17, 0, _jsp_string17.length);
        }
        out.write(_jsp_string14, 0, _jsp_string14.length);
        if (_caucho_expr_10.evalBoolean(_jsp_env)) {
          out.write(_jsp_string15, 0, _jsp_string15.length);
          _caucho_expr_8.print(out, _jsp_env, false);
          out.write(_jsp_string18, 0, _jsp_string18.length);
          _caucho_expr_9.print(out, _jsp_env, false);
          out.write(_jsp_string17, 0, _jsp_string17.length);
        }
        out.write(_jsp_string19, 0, _jsp_string19.length);
      }
      pageContext.pageSetOrRemove("semester", null);
      out.write(_jsp_string20, 0, _jsp_string20.length);
      _jsp_RedisCacheQueryTag_2 = new com.urpSoft.core.cache.redis.RedisCacheQueryTag();
      _jsp_RedisCacheQueryTag_2.setJspContext(pageContext);
      _jsp_RedisCacheQueryTag_2.setVar("xaqb");
      _jsp_RedisCacheQueryTag_2.setRegion("CODE_XAQB");
      _jsp_RedisCacheQueryTag_2.setWhere("");
      _jsp_RedisCacheQueryTag_2.doTag();
      out.write(_jsp_string19, 0, _jsp_string19.length);
      _jsp_loop_1 = _jsp_state.get_jsp_loop_1(pageContext, _jsp_parent_tag);
      java.lang.Object _jsp_items_5 = _caucho_expr_11.evalObject(_jsp_env);
      java.util.Iterator _jsp_iter_5 = com.caucho.jstl.rt.CoreForEachTag.getIterator(_jsp_items_5);
      _jsp_loop_1.init(0, Integer.MAX_VALUE, 1, false, false, false);
      while (_jsp_iter_5.hasNext()) {
        Object _jsp_i_5 = _jsp_iter_5.next();
        _jsp_loop_1.setCurrent(_jsp_i_5, _jsp_iter_5.hasNext());
        pageContext.setAttribute("campus", _jsp_i_5);
        out.write(_jsp_string14, 0, _jsp_string14.length);
        if (_caucho_expr_12.evalBoolean(_jsp_env)) {
          out.write(_jsp_string15, 0, _jsp_string15.length);
          _caucho_expr_13.print(out, _jsp_env, false);
          out.write(_jsp_string16, 0, _jsp_string16.length);
          _caucho_expr_14.print(out, _jsp_env, false);
          out.write(_jsp_string17, 0, _jsp_string17.length);
        }
        out.write(_jsp_string14, 0, _jsp_string14.length);
        if (_caucho_expr_15.evalBoolean(_jsp_env)) {
          out.write(_jsp_string15, 0, _jsp_string15.length);
          _caucho_expr_13.print(out, _jsp_env, false);
          out.write(_jsp_string18, 0, _jsp_string18.length);
          _caucho_expr_14.print(out, _jsp_env, false);
          out.write(_jsp_string17, 0, _jsp_string17.length);
        }
        out.write(_jsp_string19, 0, _jsp_string19.length);
      }
      pageContext.pageSetOrRemove("campus", null);
      out.write(_jsp_string21, 0, _jsp_string21.length);
      if (_caucho_expr_16.evalBoolean(_jsp_env)) {
        out.write(_jsp_string14, 0, _jsp_string14.length);
        _jsp_loop_1 = _jsp_state.get_jsp_loop_1(pageContext, _jsp_parent_tag);
        java.lang.Object _jsp_items_6 = _caucho_expr_17.evalObject(_jsp_env);
        java.util.Iterator _jsp_iter_6 = com.caucho.jstl.rt.CoreForEachTag.getIterator(_jsp_items_6);
        _jsp_loop_1.init(0, Integer.MAX_VALUE, 1, false, false, false);
        while (_jsp_iter_6.hasNext()) {
          Object _jsp_i_6 = _jsp_iter_6.next();
          _jsp_loop_1.setCurrent(_jsp_i_6, _jsp_iter_6.hasNext());
          pageContext.setAttribute("teachingBuilding", _jsp_i_6);
          out.write(_jsp_string22, 0, _jsp_string22.length);
          if (_caucho_expr_18.evalBoolean(_jsp_env)) {
            out.write(_jsp_string23, 0, _jsp_string23.length);
            _caucho_expr_19.print(out, _jsp_env, false);
            out.write(_jsp_string24, 0, _jsp_string24.length);
            _caucho_expr_20.print(out, _jsp_env, false);
            out.write(_jsp_string25, 0, _jsp_string25.length);
          }
          out.write(_jsp_string22, 0, _jsp_string22.length);
          if (_caucho_expr_21.evalBoolean(_jsp_env)) {
            out.write(_jsp_string23, 0, _jsp_string23.length);
            _caucho_expr_19.print(out, _jsp_env, false);
            out.write(_jsp_string26, 0, _jsp_string26.length);
            _caucho_expr_20.print(out, _jsp_env, false);
            out.write(_jsp_string25, 0, _jsp_string25.length);
          }
          out.write(_jsp_string14, 0, _jsp_string14.length);
        }
        pageContext.pageSetOrRemove("teachingBuilding", null);
        out.write(_jsp_string19, 0, _jsp_string19.length);
      }
      out.write(_jsp_string27, 0, _jsp_string27.length);
      if (_caucho_expr_22.evalBoolean(_jsp_env)) {
        out.write(_jsp_string14, 0, _jsp_string14.length);
        _jsp_loop_1 = _jsp_state.get_jsp_loop_1(pageContext, _jsp_parent_tag);
        java.lang.Object _jsp_items_7 = _caucho_expr_23.evalObject(_jsp_env);
        java.util.Iterator _jsp_iter_7 = com.caucho.jstl.rt.CoreForEachTag.getIterator(_jsp_items_7);
        _jsp_loop_1.init(0, Integer.MAX_VALUE, 1, false, false, false);
        while (_jsp_iter_7.hasNext()) {
          Object _jsp_i_7 = _jsp_iter_7.next();
          _jsp_loop_1.setCurrent(_jsp_i_7, _jsp_iter_7.hasNext());
          pageContext.setAttribute("classroom", _jsp_i_7);
          out.write(_jsp_string22, 0, _jsp_string22.length);
          if (_caucho_expr_24.evalBoolean(_jsp_env)) {
            out.write(_jsp_string23, 0, _jsp_string23.length);
            _caucho_expr_25.print(out, _jsp_env, false);
            out.write(_jsp_string28, 0, _jsp_string28.length);
            _caucho_expr_26.print(out, _jsp_env, false);
            out.write(_jsp_string25, 0, _jsp_string25.length);
          }
          out.write(_jsp_string22, 0, _jsp_string22.length);
          if (_caucho_expr_27.evalBoolean(_jsp_env)) {
            out.write(_jsp_string23, 0, _jsp_string23.length);
            _caucho_expr_25.print(out, _jsp_env, false);
            out.write(_jsp_string26, 0, _jsp_string26.length);
            _caucho_expr_26.print(out, _jsp_env, false);
            out.write(_jsp_string25, 0, _jsp_string25.length);
          }
          out.write(_jsp_string14, 0, _jsp_string14.length);
        }
        pageContext.pageSetOrRemove("classroom", null);
        out.write(_jsp_string19, 0, _jsp_string19.length);
      }
      out.write(_jsp_string29, 0, _jsp_string29.length);
      _jsp_PaginationTag_3 = _jsp_state.get_jsp_PaginationTag_3(pageContext, _jsp_parent_tag);
      _jsp_PaginationTag_3.doStartTag();
      out.write(_jsp_string30, 0, _jsp_string30.length);
    }
    out.write(_jsp_string31, 0, _jsp_string31.length);
  }

  private com.caucho.make.DependencyContainer _caucho_depends
    = new com.caucho.make.DependencyContainer();

  public java.util.ArrayList<com.caucho.vfs.Dependency> _caucho_getDependList()
  {
    return _caucho_depends.getDependencies();
  }

  public void _caucho_addDepend(com.caucho.vfs.PersistentDependency depend)
  {
    super._caucho_addDepend(depend);
    _caucho_depends.add(depend);
  }

  protected void _caucho_setNeverModified(boolean isNotModified)
  {
    _caucho_isNotModified = true;
  }

  public boolean _caucho_isModified()
  {
    if (_caucho_isDead)
      return true;

    if (_caucho_isNotModified)
      return false;

    if (com.caucho.server.util.CauchoSystem.getVersionId() != -7019056920836842200L)
      return true;

    return _caucho_depends.isModified();
  }

  public long _caucho_lastModified()
  {
    return 0;
  }

  public void destroy()
  {
      _caucho_isDead = true;
      super.destroy();
    TagState tagState;
  }

  public void init(com.caucho.vfs.Path appDir)
    throws javax.servlet.ServletException
  {
    com.caucho.vfs.Path resinHome = com.caucho.server.util.CauchoSystem.getResinHome();
    com.caucho.vfs.MergePath mergePath = new com.caucho.vfs.MergePath();
    mergePath.addMergePath(appDir);
    mergePath.addMergePath(resinHome);
    com.caucho.loader.DynamicClassLoader loader;
    loader = (com.caucho.loader.DynamicClassLoader) getClass().getClassLoader();
    String resourcePath = loader.getResourcePathSpecificFirst();
    mergePath.addClassPath(resourcePath);
    com.caucho.vfs.Depend depend;
    depend = new com.caucho.vfs.Depend(appDir.lookup("WEB-INF/jsp/student/teachingResources/classroomCurriculum/index.jsp"), -7279537116964673791L, true);
    _caucho_depends.add(depend);
    loader.addDependency(depend);
    depend = new com.caucho.vfs.Depend(appDir.lookup("WEB-INF/tld/cache.tld"), -5372381982850635951L, true);
    _caucho_depends.add(depend);
    loader.addDependency(depend);
    depend = new com.caucho.vfs.Depend(appDir.lookup("WEB-INF/tld/pagination.tld"), -7527331385242438147L, true);
    _caucho_depends.add(depend);
    loader.addDependency(depend);
    _caucho_depends.add(new com.caucho.make.ClassDependency("com.urpSoft.core.pagination.pageTag.PaginationTag", -6335182701469123054L));
  }

  static {
    try {
    } catch (Exception e) {
      e.printStackTrace();
      throw new RuntimeException(e);
    }
  }

  final static class TagState {
    private com.caucho.jsp.IteratorLoopSupportTag _jsp_loop_1;

    final com.caucho.jsp.IteratorLoopSupportTag get_jsp_loop_1(PageContext pageContext, javax.servlet.jsp.tagext.JspTag _jsp_parent_tag) throws Throwable
    {
      if (_jsp_loop_1 == null) {
        _jsp_loop_1 = new com.caucho.jsp.IteratorLoopSupportTag();
        _jsp_loop_1.setParent((javax.servlet.jsp.tagext.Tag) null);
      }

      return _jsp_loop_1;
    }
    private com.urpSoft.core.pagination.pageTag.PaginationTag _jsp_PaginationTag_3;

    final com.urpSoft.core.pagination.pageTag.PaginationTag get_jsp_PaginationTag_3(PageContext pageContext, javax.servlet.jsp.tagext.JspTag _jsp_parent_tag) throws Throwable
    {
      if (_jsp_PaginationTag_3 == null) {
        _jsp_PaginationTag_3 = new com.urpSoft.core.pagination.pageTag.PaginationTag();
        _jsp_PaginationTag_3.setPageContext(pageContext);
        if (_jsp_parent_tag instanceof javax.servlet.jsp.tagext.Tag)
          _jsp_PaginationTag_3.setParent((javax.servlet.jsp.tagext.Tag) _jsp_parent_tag);
        else if (_jsp_parent_tag instanceof javax.servlet.jsp.tagext.SimpleTag)
          _jsp_PaginationTag_3.setParent(new javax.servlet.jsp.tagext.TagAdapter((javax.servlet.jsp.tagext.SimpleTag) _jsp_parent_tag));
        else
          _jsp_PaginationTag_3.setParent((javax.servlet.jsp.tagext.Tag) null);
        _jsp_PaginationTag_3.setBean("pageData");
        _jsp_PaginationTag_3.setUrl("/student/teachingResources/classroomCurriculum/search");
      }

      return _jsp_PaginationTag_3;
    }

    void release()
    {
      if (_jsp_PaginationTag_3 != null) {
        _jsp_PaginationTag_3.release();
        _jsp_PaginationTag_3 = null;
      }
    }
  }

  public java.util.HashMap<String,java.lang.reflect.Method> _caucho_getFunctionMap()
  {
    return _jsp_functionMap;
  }

  public void caucho_init(ServletConfig config)
  {
    try {
      com.caucho.server.webapp.WebApp webApp
        = (com.caucho.server.webapp.WebApp) config.getServletContext();
      init(config);
      if (com.caucho.jsp.JspManager.getCheckInterval() >= 0)
        _caucho_depends.setCheckInterval(com.caucho.jsp.JspManager.getCheckInterval());
      _jsp_pageManager = webApp.getJspApplicationContext().getPageManager();
      com.caucho.jsp.TaglibManager manager = webApp.getJspApplicationContext().getTaglibManager();
      manager.addTaglibFunctions(_jsp_functionMap, "c", "http://java.sun.com/jsp/jstl/core");
      manager.addTaglibFunctions(_jsp_functionMap, "form", "http://www.springframework.org/tags/form");
      manager.addTaglibFunctions(_jsp_functionMap, "pager", "http://www.urpSoft.com/pagination");
      manager.addTaglibFunctions(_jsp_functionMap, "cache", "http://www.urpSoft.com/cache");
      com.caucho.jsp.PageContextImpl pageContext = new com.caucho.jsp.InitPageContextImpl(webApp, this);
      _caucho_expr_0 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${xqzs}");
      _caucho_expr_1 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${onOff}");
      _caucho_expr_2 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${isFromIndex}");
      _caucho_expr_3 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${onOff == '0'}");
      _caucho_expr_4 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${onOff == '1'}");
      _caucho_expr_5 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${param_value }");
      _caucho_expr_6 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${academicSemesterList}");
      _caucho_expr_7 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${semester.executiveEducationPlanNumber == planCode}");
      _caucho_expr_8 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${semester.executiveEducationPlanNumber}");
      _caucho_expr_9 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${semester.executiveEducationPlanName}");
      _caucho_expr_10 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${semester.executiveEducationPlanNumber != planCode}");
      _caucho_expr_11 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${xaqb}");
      _caucho_expr_12 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${campus.xqh == campusCode}");
      _caucho_expr_13 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${campus.xqh}");
      _caucho_expr_14 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${campus.xqm}");
      _caucho_expr_15 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${campus.xqh != campusCode}");
      _caucho_expr_16 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${teachingBuildingList != null && teachingBuildingList.size() > 0}");
      _caucho_expr_17 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${teachingBuildingList}");
      _caucho_expr_18 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${teachingBuilding.id.teachingBuildingNumber == teachingBuildingCode}");
      _caucho_expr_19 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${teachingBuilding.id.teachingBuildingNumber}");
      _caucho_expr_20 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${teachingBuilding.teachingBuildingName}");
      _caucho_expr_21 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${teachingBuilding.id.teachingBuildingNumber != teachingBuildingCode}");
      _caucho_expr_22 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${classroomList != null && classroomList.size() > 0}");
      _caucho_expr_23 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${classroomList}");
      _caucho_expr_24 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${classroom.id.classroomNumber == classroomCode}");
      _caucho_expr_25 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${classroom.id.classroomNumber}");
      _caucho_expr_26 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${classroom.classroomName}");
      _caucho_expr_27 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${classroom.id.classroomNumber != classroomCode}");
    } catch (Exception e) {
      throw com.caucho.config.ConfigException.create(e);
    }
  }
  private static com.caucho.el.Expr _caucho_expr_0;
  private static com.caucho.el.Expr _caucho_expr_1;
  private static com.caucho.el.Expr _caucho_expr_2;
  private static com.caucho.el.Expr _caucho_expr_3;
  private static com.caucho.el.Expr _caucho_expr_4;
  private static com.caucho.el.Expr _caucho_expr_5;
  private static com.caucho.el.Expr _caucho_expr_6;
  private static com.caucho.el.Expr _caucho_expr_7;
  private static com.caucho.el.Expr _caucho_expr_8;
  private static com.caucho.el.Expr _caucho_expr_9;
  private static com.caucho.el.Expr _caucho_expr_10;
  private static com.caucho.el.Expr _caucho_expr_11;
  private static com.caucho.el.Expr _caucho_expr_12;
  private static com.caucho.el.Expr _caucho_expr_13;
  private static com.caucho.el.Expr _caucho_expr_14;
  private static com.caucho.el.Expr _caucho_expr_15;
  private static com.caucho.el.Expr _caucho_expr_16;
  private static com.caucho.el.Expr _caucho_expr_17;
  private static com.caucho.el.Expr _caucho_expr_18;
  private static com.caucho.el.Expr _caucho_expr_19;
  private static com.caucho.el.Expr _caucho_expr_20;
  private static com.caucho.el.Expr _caucho_expr_21;
  private static com.caucho.el.Expr _caucho_expr_22;
  private static com.caucho.el.Expr _caucho_expr_23;
  private static com.caucho.el.Expr _caucho_expr_24;
  private static com.caucho.el.Expr _caucho_expr_25;
  private static com.caucho.el.Expr _caucho_expr_26;
  private static com.caucho.el.Expr _caucho_expr_27;

  private final static char []_jsp_string5;
  private final static char []_jsp_string19;
  private final static char []_jsp_string13;
  private final static char []_jsp_string1;
  private final static char []_jsp_string16;
  private final static char []_jsp_string2;
  private final static char []_jsp_string29;
  private final static char []_jsp_string7;
  private final static char []_jsp_string17;
  private final static char []_jsp_string3;
  private final static char []_jsp_string6;
  private final static char []_jsp_string15;
  private final static char []_jsp_string31;
  private final static char []_jsp_string12;
  private final static char []_jsp_string4;
  private final static char []_jsp_string26;
  private final static char []_jsp_string27;
  private final static char []_jsp_string23;
  private final static char []_jsp_string20;
  private final static char []_jsp_string22;
  private final static char []_jsp_string28;
  private final static char []_jsp_string21;
  private final static char []_jsp_string24;
  private final static char []_jsp_string18;
  private final static char []_jsp_string14;
  private final static char []_jsp_string11;
  private final static char []_jsp_string8;
  private final static char []_jsp_string0;
  private final static char []_jsp_string9;
  private final static char []_jsp_string25;
  private final static char []_jsp_string10;
  private final static char []_jsp_string30;
  static {
    _jsp_string5 = "' != '0'){\r\n            var data = '".toCharArray();
    _jsp_string19 = "\r\n                            ".toCharArray();
    _jsp_string13 = "\">\r\n\r\n        <form name=\"kckbform\" method=\"POST\">\r\n            <h4 class=\"header smaller lighter grey\">\r\n                <i class=\"ace-icon fa fa-search\"></i>\u67e5\u8be2\u6761\u4ef6\r\n					<span class=\"right_top_oper\" style=\"margin-left: 0px;padding-left: 0\">\r\n						<button id=\"queryButton\" title=\"\u67e5\u8be2\" class=\"btn btn-info btn-xs btn-round\"\r\n                                onclick=\"getKtkbList();return false;\">\r\n                            <i class=\"ace-con fa fa-search white bigger-120\"></i> \u67e5\u8be2\r\n                        </button>\r\n					</span>\r\n            </h4>\r\n\r\n            <div class=\"profile-user-info profile-user-info-striped self\">\r\n                <div class=\"profile-info-row\">\r\n                    <div class=\"profile-info-name\"> \u5b66\u5e74\u5b66\u671f</div>\r\n                    <div class=\"profile-info-value\">\r\n                        <select name=\"executiveEducationPlanNumber\" class=\"select form-control value_element\">\r\n                            <option value=\"\">\u5168\u90e8</option>\r\n                            ".toCharArray();
    _jsp_string1 = "/img/icon/g.gif' id='kctzcx' style='cursor: hand;' title=''></li></ul></div>\";\r\n\r\n                        $(\"#buttonDiv\").empty();\r\n                        $(\"#buttonDiv\").html(bottonDiv);\r\n                        var iframeHtml = \"<iframe name='jasjc' id='jasjc'\" +\r\n                                \"src='/student/teachingResources/classroomCurriculum/\" + $(\"#ycd\").val() + \"/show/decoration' \" +\r\n                                \"width='100%' height='500' scrolling='0' frameborder='0' style='margin-top: -21px;'> </iframe>\";\r\n                        $(\"#iframeDiv\").empty();\r\n                        $(\"#iframeDiv\").html(iframeHtml);\r\n                    } else {\r\n                        var bottonDiv = \"<div class='tabbable'><ul class='nav nav-tabs padding-18'><li class='active'>\" +\r\n                                \"<a id='td_div' data-toggle='tab' href='#' onClick=dolook('currentWeek')>\u5355\u5468\u6b21\u6a21\u5f0f</a></li>\" +\r\n                                \"<li class=''><a id='td_div' data-toggle='tab' href='#' onclick=dolook('')>\" +\r\n                                \"\u5168\u5468\u6b21\u6a21\u5f0f</a></li>\";\r\n                        bottonDiv += \"<li class='' ><div style='margin-left:500px;'>\u672c\u6559\u5ba4\u5b89\u6392\u60c5\u51b5:<img src='".toCharArray();
    _jsp_string16 = "\" selected=\"selected\">\r\n                                            ".toCharArray();
    _jsp_string2 = "/img/icon/g.gif' id='kctzcx' style='cursor: hand;' title=''></li></ul></div>\";\r\n                        $(\"#buttonDiv\").empty();\r\n                        $(\"#buttonDiv\").html(bottonDiv);\r\n                        $(\"#mycoursetable\").empty();\r\n                        coursetable.init(\"iframeDiv\", \"\");\r\n\r\n                        //\u5468\u6b21\u6e10\u53d8\r\n                        var seccont = \"<ol id='drag-ol'>\";\r\n                        var xqzs = ".toCharArray();
    _jsp_string29 = "\r\n                        </select>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </form>\r\n        <h4 class=\"header smaller lighter grey\">\r\n            <i class=\"glyphicon glyphicon-list\" style=\"margin-left: 0px;\"></i>\r\n            \u5217\u8868\r\n        </h4>\r\n\r\n        <div class=\"widget-content\" id=\"page_div\" style=\"overflow: auto;max-height: calc(100vh - 285px);\">\r\n            <form id=\"info\" method=\"POST\" name=\"info\">\r\n                <input type=\"hidden\" name=\"planCode\" id=\"planNumber\">\r\n                <input type=\"hidden\" name=\"campusCode\" id=\"campusNumber\">\r\n                <input type=\"hidden\" name=\"teachingBuildingCode\" id=\"teachingBuildingNumber\">\r\n                <input type=\"hidden\" name=\"classroomCode\" id=\"classroomNumber\">\r\n                <input type=\"hidden\" name=\"campusName\">\r\n                <input type=\"hidden\" name=\"teachingBuildingName\">\r\n                <input type=\"hidden\" name=\"classroomName\">\r\n                <input type=\"hidden\" name=\"week\">\r\n                <input type=\"hidden\" name=\"oper\" value=\"currentWeek\">\r\n            </form>\r\n            <table class=\"table table-striped table-bordered table-hover\">\r\n                <thead>\r\n                <tr class=\"center\">\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u5e8f\u53f7</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u8bfe\u8868\u4fe1\u606f\u67e5\u770b</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u6559\u5ba4\u4fe1\u606f\u67e5\u770b</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u6821\u533a\u540d</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u6559\u5b66\u697c\u540d</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u6559\u5ba4\u53f7</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u6559\u5ba4\u540d</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u4e0a\u8bfe\u5ea7\u4f4d\u6570</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u6240\u5c5e\u9662\u7cfb</th>\r\n                    <th style=\"border-top:3px solid #ddd;background-color:transparent;\">\u6559\u5ba4\u7c7b\u578b</th>\r\n                </tr>\r\n                </thead>\r\n                <tbody id=\"ktkbtbody\" style=\"overflow: auto;\"></tbody>\r\n            </table>\r\n        </div>\r\n        <!-- \u9875\u7801\u5f00\u59cb -->\r\n        <div id=\"urppagebar\" style=\"margin: 5px 0 0 0\"></div>\r\n        <div>\r\n            <div class=\"manu-page\">\r\n                ".toCharArray();
    _jsp_string7 = "eTable\").offset().top);\r\n                if ($(v).siblings().size() > 0) {\r\n                    if ($(v).next().size() > 0) {\r\n                        $(v).css(\"left\", $(v).parent(\"td\").offset().left - $(\"#print_div_now\").offset().left + \"px\");\r\n                    } else {\r\n                        $(v).css(\"left\", $(v).parent(\"td\").offset().left - $(\"#print_div_now\").offset().left + tdWidth.substring(0, tdWidth.length - 2) / 2 + \"px\");\r\n                    }\r\n                } else {\r\n                    $(v).css(\"left\", $(v).parent(\"td\").offset().left - $(\"#print_div_now\").offset().left + \"px\");\r\n                }\r\n            });\r\n        }\r\n\r\n\r\n    </script>\r\n\r\n    <script type=\"text/javascript\">\r\n        function init() {\r\n            if('".toCharArray();
    _jsp_string17 = "\r\n                                    </option>\r\n                                ".toCharArray();
    _jsp_string3 = ";\r\n                        for (var i = 1; i <= ".toCharArray();
    _jsp_string6 = "';\r\n            if (data) {\r\n                fillTable(eval(\"(\" + data + \")\"), false, 0, 0);\r\n            } else {\r\n                getKtkbList(1, \"30_sl\", true);\r\n            }\r\n            }\r\n        });\r\n\r\n        function getKtkbList(page, pageSizeVal, conditionChanged) {\r\n            if (pageSizeVal == undefined) {\r\n                pageSizeVal = \"30_sl\";\r\n                page = \"1\";\r\n            }\r\n            var parr = (pageSizeVal + \"\").split(\"_\");\r\n            var pageSize = parseInt(parr[0]);\r\n            var pageConditions = $(document.kckbform).serialize();\r\n            var index;//queryStudent\u6309\u94aeid\r\n            $.ajax({\r\n                url: \"/student/teachingResources/classroomCurriculum/search\",\r\n                cache: false,\r\n                type: \"post\",\r\n                data: pageConditions + \"&pageNum=\" + page + \"&pageSize=\" + pageSize,\r\n                dataType: \"json\",\r\n                beforeSend: function () {\r\n                    index = layer.load(0, {\r\n                        shade: [0.2, \"#000\"] //0.1\u900f\u660e\u5ea6\u7684\u767d\u8272\u80cc\u666f\r\n                    });\r\n                },\r\n                complete: function () {\r\n                    layer.close(index);\r\n                },\r\n                success: function (d) {\r\n                    var pageData = d[0].pageData;\r\n                    var xqzs = d[0].xqzs;\r\n                    var div_id = \"page_div\";\r\n                    urp.pagebar(\"urppagebar\", pageSizeVal, page, pageData[\"pageContext\"].totalCount, getKtkbList, \"on\", div_id);\r\n                    var isScroll = (pageSizeVal + \"\").indexOf(\"_\") != -1 && page != 1 ? true : false;\r\n                    fillTable(pageData[\"records\"], isScroll, page, pageSize);\r\n                },\r\n                error: function (xhr) {\r\n                    urp.alert(\"\u9519\u8bef\u4ee3\u7801[\" + xhr.readyState + \"-\" + xhr.status + \"]:\u83b7\u53d6\u6570\u636e\u5931\u8d25\uff01\");\r\n                }\r\n            });\r\n        }\r\n\r\n        function fillTable(data, isScroll, page, pageSize) {\r\n            var tcont = \"\";\r\n            $.each(data, function (i, v) {\r\n                tcont += \"<tr>\";\r\n                var xh = \"\";\r\n                if (isScroll) {\r\n                    xh = (page - 1) * pageSize + 1 + i;\r\n                } else {\r\n                    xh = i + 1;\r\n                }\r\n                tcont += \"<td>\" + xh + \"</td>\";\r\n                tcont += \"<td ><button id='td_div' title='\u67e5\u770b' class='btn btn-info btn-xs btn-round'\" +\r\n                        \"onclick='searchCurriculumInfo(\\\"\" + v.id.executiveEducationPlanNumber + \"\\\",\\\"\" + v.id.campusNumber + \"\\\",\\\"\" + v.id.teachingBuildingNumber + \"\\\",\\\"\" + v.id.classroomNumber + \"\\\",\\\"\" + v.campusName + \"\\\",\\\"\" + v.teachingBuildingName + \"\\\",\\\"\" + v.classroomName + \"\\\",\\\"curriculum\\\");return false;'>\" +\r\n                        \"<i id='td_div' class='fa fa-eye bigger-120'></i> \u67e5\u770b\" +\r\n                        \"</button></td></td>\";\r\n                tcont += \"<td ><button id='td_div' title='\u67e5\u770b' class='btn btn-info btn-xs btn-round'\" +\r\n                        \"onclick='searchCurriculumInfo(\\\"\" + v.id.executiveEducationPlanNumber + \"\\\",\\\"\" + v.id.campusNumber + \"\\\",\\\"\" + v.id.teachingBuildingNumber + \"\\\",\\\"\" + v.id.classroomNumber + \"\\\",\\\"\" + v.campusName + \"\\\",\\\"\" + v.teachingBuildingName + \"\\\",\\\"\" + v.classroomName + \"\\\",\\\"classroom\\\");return false;'>\" +\r\n                        \"<i id='td_div' class='fa fa-eye bigger-120'></i> \u67e5\u770b\" +\r\n                        \"</button></td>\";\r\n                tcont += \"<td>\" + v.campusName + \"</td>\";\r\n                tcont += \"<td>\" + v.teachingBuildingName + \"</td>\";\r\n                tcont += \"<td>\" + v.id.classroomNumber + \"</td>\";\r\n                tcont += \"<td>\" + v.classroomName + \"</td>\";\r\n                tcont += \"<td>\" + v.classNumberOfSeats + \"</td>\";\r\n                tcont += \"<td>\" + (v.departmentName == null || v.departmentName == \"\" ? \"\u5168\u9662\" : v.departmentName) + \"</td>\";\r\n                tcont += \"<td>\" + v.classroomTypeDirections + \"</td>\";\r\n\r\n                tcont += \"</tr>\";\r\n            });\r\n            if (isScroll) {\r\n                $(\"#ktkbtbody\").append(tcont);\r\n            } else {\r\n                $(\"#ktkbtbody\").html(tcont);\r\n            }\r\n        }\r\n\r\n\r\n        function dy() {\r\n\r\n            $(\"#page-content-template\").after('<div class=\"col-xs-12\" id=\"print_div_now\">' + $(\"#print_div\").html() + '</div>');\r\n            $(\"#page-content-template\").hide();\r\n            $(\"#breadcrumbs\").hide();\r\n\r\n            $(\"#print_div_now div.class_div\").addClass(\"printDiv\");\r\n            $(\"#print_div_now #courseTable\").addClass(\"printDiv\");\r\n            $(\"#print_div_now #courseTable\").removeClass(\"table\");\r\n            $(\"#print_div_now #courseTable\").css(\"width\", $(\"#print_div_now #mycoursetable\").width());\r\n\r\n            printDivBuild();\r\n            if ($.isIE()) {\r\n                window.print();\r\n                $(\"#print_div_now\").remove();\r\n                $(\"#breadcrumbs\").show();\r\n                $(\"#page-content-template\").show();\r\n            } else {\r\n                var shareContent = document.querySelector(\"#print_div_now\");\r\n                var width = shareContent.offsetWidth;\r\n                var height = shareContent.offsetHeight;\r\n                var canvas = document.createElement(\"canvas\");\r\n                var scale = 2;\r\n\r\n                canvas.width = width * scale;\r\n                canvas.height = height * scale;\r\n                canvas.getContext(\"2d\").scale(scale, scale);\r\n                var opts = {\r\n                    scale: scale,\r\n                    canvas: canvas,\r\n                    logging: true,\r\n                    width: width,\r\n                    height: height\r\n                };\r\n\r\n                html2canvas(shareContent, opts).then(function (canvas) {\r\n\r\n                    canvas.id = \"mycanvas\";\r\n                    var dataUrl = canvas.toDataURL();\r\n                    var newImg = document.createElement(\"img\");\r\n                    newImg.src = dataUrl;\r\n                    $(\"#print_div_now\").html('<img style=\"width: 100%;\" src=\"' + newImg.src + '\" />');\r\n\r\n                    newImg.onload = function () {\r\n                        window.print();\r\n                        $(\"#print_div_now\").remove();\r\n                        $(\"#breadcrumbs\").show();\r\n                        $(\"#page-content-template\").show();\r\n                    };\r\n                });\r\n            }\r\n\r\n\r\n//            var planNumber = document.getElementById(\"planNumber\").value;\r\n//            var campusNumber = document.getElementById(\"campusNumber\").value;\r\n//            var teachingBuildingNumber = document.getElementById(\"teachingBuildingNumber\").value;\r\n//            var classroomNumber = document.getElementById(\"classroomNumber\").value;\r\n//            window.location.href = \"/student/teachingResources/classroomCurriculum/printClassroom?planNumber=\" + planNumber + \"&campusNumber=\" + campusNumber + \"&teachingBuildingNumber=\" + teachingBuildingNumber + \"&classroomNumber=\" + classroomNumber;\r\n\r\n        }\r\n\r\n        function printDivBuild() {\r\n            $(\"#print_div_now div.class_div\").removeAttr(\"style\");\r\n            $(\"#print_div_now div.class_div\").css(\"position\", \"absolute\");\r\n            var tdWidth = $(\"#print_div_now #mycoursetable td\").css(\"width\");\r\n            $(\"#print_div_now div.class_div\").each(function (i, v) {\r\n                if ($(v).siblings().size() > 0) {\r\n                    $(v).css(\"width\", tdWidth.substring(0, tdWidth.length - 2) / 2 + \"px\");\r\n                } else {\r\n                    $(v).css(\"width\", tdWidth);\r\n                }\r\n            });\r\n            var hd = 0;\r\n            $(\"#print_div_now div.class_div\").each(function (i, v) {\r\n                if ($(v).height() / $(v).attr(\"classNum\") > hd) {\r\n                    hd = $(v).height() / $(v).attr(\"classNum\");\r\n                }\r\n            });\r\n            $(\"#print_div_now #courseTableBody tr\").height(hd + \"px\");\r\n            $(\"#print_div_now div.class_div\").each(function (i, v) {\r\n                $(v).css(\"height\", $(\"#print_div_now #courseTableBody tr\").height() * $(v).attr(\"classNum\") + \"px\");\r\n                $(v).css(\"top\", $(v).parent(\"td\").offset().top - $(\"#print_div_now #cours".toCharArray();
    _jsp_string15 = "\r\n                                    <option value=\"".toCharArray();
    _jsp_string31 = "\r\n</body>\r\n</html>".toCharArray();
    _jsp_string12 = "\r\n<div class=\"row\">\r\n    <div class=\"col-xs-12 self-margin\" id=\"close\">\r\n        <input type=\"hidden\" id=\"param_value\" name=\"param_value\" value=\"".toCharArray();
    _jsp_string4 = "; i++) {\r\n                            seccont += \"<li class='border-common \";\r\n                            if (i == parseInt(d.data.week)) {\r\n                                seccont += \" current-week' \";\r\n                            }\r\n//                            if (i < parseInt(d.data.week)) {\r\n//                                seccont += \"' style='background-color: \" + colour[23 - d.data.week + i] + \";'\";\r\n//                            } else {\r\n//                                seccont += \"' \";\r\n//                            }\r\n                            seccont += \"' onclick='dolook(\\\"currentWeek\\\",\" + i + \")' >\" + i + \"</li>\";\r\n                        }\r\n                        seccont += \"</ol><div style='clear:both'></div>\";\r\n\r\n//                        if (id != \"calssRoomInfo\")\r\n                        $(\"#drag-section\").html(seccont);\r\n\r\n                    }\r\n\r\n                    $(\"#curriculumInfo-divcon\").animate({right: '-70%'});\r\n                    $(\"#calssInfo-divcon\").animate({right: '0%'});\r\n                    //\u662f\u5426\u6709\u6559\u5ba4\u4fe1\u606f\r\n                    if (d.usedTimeMap != null && d.data.usedTimeMapSize > 0) {\r\n                        fillCheckedWeekInfo(d);\r\n                    }\r\n                },\r\n                error: function () {\r\n                    urp.alert(\"\u67e5\u8be2\u5931\u8d25\uff01\");\r\n                }\r\n            });\r\n        }\r\n        //\u6559\u5ba4\u4fe1\u606f\u67e5\u770b\u6dfb\u52a0\u9009\u4e2d\u5468\u6b21\u6559\u5ba4\u4fe1\u606f\r\n        function fillCheckedWeekInfo(d) {\r\n            setTimeout(function () {\r\n                var strs = new Array();\r\n                if (d.data.oper == \"\") {\r\n                    $.each(d.usedTimeMap, function (i, v) {\r\n                        var arr = i.split(\"-\");\r\n                        var str = v.split(\",\");\r\n                        for (var j = 0; j < str.length; j++) {\r\n                            var doc = document.getElementById('jasjc').contentDocument || document.frames['jasjc'].document;\r\n                            $(doc).find(\"#allWeekTable td[id='\" + arr[0] + \"_\" + arr[1] + \"']\").find(\"td[id='\" + str[j] + \"']\").css({\r\n                                \"color\": \"#ff0011\",\r\n                                \"background\": \"#00CC33\"\r\n                            });\r\n                        }\r\n                    });\r\n                } else {\r\n                    $.each(d.usedTimeMap, function (i, v) {\r\n                        var str = v.split(\",\");\r\n                        for (var j = 0; j < str.length; j++) {\r\n                            if (d.data.week == str[j])\r\n                                strs = strs.concat(i);\r\n                        }\r\n                    });\r\n                    for (var i = 0; i < strs.length; i++) {\r\n                        var arr = strs[i].split(\"-\");\r\n                        $(\"#courseTableBody td[id='\" + arr[1] + \"_\" + arr[0] + \"']\").css({\r\n                            \"color\": \"#ff0011\",\r\n                            \"background\": \"rgb(175, 212, 62)\"\r\n                        });\r\n                    }\r\n                }\r\n            }, 1000);\r\n\r\n        }\r\n\r\n        //\u67e5\u770b\u65b9\u6cd5\r\n        function searchCurriculumInfo(planCode, campusCode, teachingBuildingCode,\r\n                                      classroomCode, campusName, teachingBuildingName, classroomName,\r\n                                      oper) {\r\n            colorClass.initArrays();\r\n            var frm = document.forms[\"info\"];\r\n            frm.planCode.value = planCode;\r\n            frm.campusCode.value = campusCode;\r\n            frm.teachingBuildingCode.value = teachingBuildingCode;\r\n            frm.classroomCode.value = classroomCode;\r\n            frm.campusName.value = campusName;\r\n            frm.teachingBuildingName.value = teachingBuildingName;\r\n            frm.classroomName.value = classroomName;\r\n            if (oper == \"classroom\") {\r\n                //\u6559\u5ba4\u4fe1\u606f\u67e5\u770b\r\n                searchCalssRoomInfo(\"info\");\r\n\r\n            } else {//\u8bfe\u8868\u4fe1\u606f\r\n                $.ajax({\r\n                    url: \"/student/teachingResources/classroomCurriculum/searchClassroomUesedInfoLo\",\r\n                    type: \"post\",\r\n                    data: $(\"#info\").serialize(),\r\n                    dataType: \"json\",\r\n                    success: function (d) {\r\n                        $(\"#title\").html(d.data.campusName + \"&nbsp;&nbsp;\" + d.data.teachingBuildingName + \"&nbsp;&nbsp;\" + d.data.classroomName + \"&nbsp;&nbsp;\" + d.data.planName + \"&nbsp;&nbsp;\" + \"\u8bfe\u8868\u4fe1\u606f\");\r\n                        $(\"#table-title\").html(\"<i class=\\\"fa fa-info-circle\\\"></i>\" + d.data.campusName + \"&nbsp;&nbsp;\" + d.data.teachingBuildingName + \"&nbsp;&nbsp;\" + d.data.classroomName + \"&nbsp;&nbsp;\" + d.data.planName + \"&nbsp;&nbsp;\" + \"\u8bfe\u7a0b\u4fe1\u606f\");\r\n                        init();\r\n\r\n                        $(\"#calssInfo-divcon\").animate({right: '-70%'});\r\n                        $(\"#curriculumInfo-divcon\").animate({right: '0%'});\r\n                    },\r\n                    error: function () {\r\n                        urp.alert(\"\u67e5\u8be2\u5931\u8d25\uff01\");\r\n                    }\r\n                });\r\n            }\r\n        }\r\n\r\n\r\n        $(function () {\r\n            if('".toCharArray();
    _jsp_string26 = "\">\r\n                                                ".toCharArray();
    _jsp_string27 = "\r\n                        </select>\r\n                    </div>\r\n\r\n                    <div class=\"profile-info-name\"> \u6559\u5ba4\u540d\u79f0</div>\r\n                    <div class=\"profile-info-value\">\r\n                        <select id=\"classRoom\" name=\"classRoomNumber\" class=\"form-control value_element\">\r\n                            <option value=\"\">\u5168\u90e8</option>\r\n                            ".toCharArray();
    _jsp_string23 = "\r\n                                        <option value=\"".toCharArray();
    _jsp_string20 = "\r\n                        </select>\r\n                    </div>\r\n\r\n                    <div class=\"profile-info-name\"> \u6821\u533a</div>\r\n                    <div class=\"profile-info-value\">\r\n                        <select name=\"campusNumber\" id=\"campus\" class=\"form-control value_element\"\r\n                                onchange=\"toChangeTeachingBuilding(this);\">\r\n                            <option value=\"\">\u5168\u90e8</option>\r\n                            ".toCharArray();
    _jsp_string22 = "\r\n                                    ".toCharArray();
    _jsp_string28 = "\" selected=\"selected\">\r\n                                                ".toCharArray();
    _jsp_string21 = "\r\n                        </select>\r\n                    </div>\r\n                    <div class=\"profile-info-name\"> \u6559\u5b66\u697c</div>\r\n                    <div class=\"profile-info-value\">\r\n                        <select id=\"teachingBuilding\" name=\"teachingBuildingNumber\"\r\n                                class=\"form-control value_element\" onchange=\"toChangeClassroom(this);\">\r\n                            <option value=\"\">\u5168\u90e8</option>\r\n                            ".toCharArray();
    _jsp_string24 = "\"\r\n                                                selected=\"selected\">\r\n                                                ".toCharArray();
    _jsp_string18 = "\">\r\n                                            ".toCharArray();
    _jsp_string14 = "\r\n                                ".toCharArray();
    _jsp_string11 = "\r\n".toCharArray();
    _jsp_string8 = "' != '0'){\r\n	            $(\"#iframeDiv\").empty();\r\n	            coursetable.init(\"mycoursetable\", \"\");\r\n	            getCourseInfo();\r\n            }\r\n        }\r\n        ;\r\n\r\n        function getCourseInfo() {\r\n            var frm = document.forms[\"info\"];\r\n\r\n            url = \"/student/teachingResources/classroomCurriculum/searchCurriculum/callback\";\r\n            $.get(url, {\r\n                        planNumber: frm.planCode.value,\r\n                        campusNumber: frm.campusCode.value,\r\n                        teachingBuildingNumber: frm.teachingBuildingCode.value,\r\n                        classroomNumber: frm.classroomCode.value\r\n                    },\r\n                    function (courseInfo) {\r\n                        var infoArr = JSON.parse(JSON.stringify(courseInfo));\r\n                        var curriculumInfoList = infoArr[0];\r\n                        //setTimeout(function(){fillCourseTable(curriculumInfoList);},400);\r\n                        fillCourseTable(curriculumInfoList);\r\n                        divBuild();\r\n                    }\r\n            );\r\n        }\r\n\r\n        function fillCourseTable(cil) {\r\n            $('#course-tbody').html('');\r\n            $('#other-course').css(\"display\", \"none\");\r\n            $.each(cil, function (index, item) {\r\n                var id = item.id.skxq + \"_\" + (item.id.skjc);\r\n                if ($(\"#\" + id + \" div\").size() < 2) {\r\n                    var c_names = colorClass.getDivColorClass(item.id.kch + \"_\" + item.id.kxh).split(\",\");\r\n                    var endJc = item.id.skjc + item.cxjc - 1;\r\n                    //var divh = 35*item.cxjc+\"px\";\r\n                    var cont = \"<div style='margin:0;' class='class_div box_font \" + c_names[0] + \"' classNum = '\" + item.cxjc + \"'>\"\r\n                            + \"<p class='\" + c_names[1] + \"'>\" + item.kcm + \"_\" + item.id.kxh + \"</p>\"\r\n                            + \"<p class='kcb_p_gray'>\" + item.jsm + \"</p>\"\r\n                            + \"<p class='kcb_p_gray'>\" + item.zcsm + \"</p>\"\r\n                            + \"<p class='kcb_p_gray'>\" + item.id.skjc + \"-\" + endJc + \"\u8282</p>\"\r\n                            + \"<p class='\" + c_names[2] + \"'>\" + (item.xqm == null ? \"\" : item.xqm) + (item.jxlm == null ? \"\" : item.jxlm) + (item.jasm == null ? \"\" : item.jasm) + \"</p>\"\r\n                            + \"</div>\";\r\n                    if ($(\"#\" + id).html() == \"\") {\r\n                        $(\"#\" + id).html(cont);\r\n                    } else {\r\n                        $(\"#\" + id).append(cont);\r\n                    }\r\n                } else {\r\n                    $('#other-course').removeAttr(\"style\");\r\n                    var trHt = \"<tr><td>\" + item.id.kch + \"</td><td>\" + item.kcm + \"</td><td>\" + item.id.kxh + \"</td><td>\" + item.jsm + \"</td><td>\" + item.zcsm + \"</td><td>\" + urp.translationWeek(item.id.skxq) + \"</td><td>\" + urp.mergeSession(item.id.skjc, item.cxjc) + \"</td><td>\" + item.xqm + \"</td><td>\" + item.jxlm + \"</td><td>\" + item.jasm + \"</td></tr>\";\r\n                    $('#course-tbody').append(trHt);\r\n                }\r\n\r\n\r\n            });\r\n            c_kch.length = 0;\r\n        }\r\n        $(window).resize(function () {\r\n            divBuild();\r\n        });\r\n        function divBuild() {\r\n            if ($(\"#print_div_now\").size() < 1) {\r\n                $(\"div.class_div\").removeAttr(\"style\");\r\n                $(\"div.class_div\").css(\"position\", \"absolute\");\r\n                var tdWidth = $(\"#mycoursetable td\").css(\"width\");\r\n                $(\"div.class_div\").each(function (i, v) {\r\n                    if ($(v).siblings().size() > 0) {\r\n                        $(v).css(\"width\", tdWidth.substring(0, tdWidth.length - 2) / 2 + \"px\");\r\n                    } else {\r\n                        $(v).css(\"width\", tdWidth);\r\n                    }\r\n                });\r\n                var hd = 0;\r\n                $(\"#courseTableBody tr\").each(function(i,v){\r\n                    if($(v).height() > hd){\r\n                        hd = $(v).height();\r\n                    }\r\n                });\r\n                $(\"div.class_div\").each(function (i, v) {\r\n                    if ($(v).height() / $(v).attr(\"classNum\") > hd) {\r\n                        hd = $(v).height() / $(v).attr(\"classNum\");\r\n                    }\r\n                });\r\n                $(\"#courseTableBody tr\").height(hd + \"px\");\r\n                $(\"div.class_div\").each(function (i, v) {\r\n                    $(v).css(\"height\", $(\"#courseTableBody tr\").height() * $(v).attr(\"classNum\") + \"px\");\r\n                    $(v).css(\"top\", $(v).parent(\"td\").offset().top - $('#courseTable').offset().top);\r\n                    if ($(v).siblings().size() > 0) {\r\n                        if ($(v).next().size() > 0) {\r\n                            $(v).css(\"left\", $(v).parent(\"td\").offset().left - $('#curriculumInfo-divcon').offset().left + \"px\");\r\n                        } else {\r\n                            $(v).css(\"left\", $(v).parent(\"td\").offset().left - $('#curriculumInfo-divcon').offset().left + tdWidth.substring(0, tdWidth.length - 2) / 2 + \"px\");\r\n                        }\r\n                    } else {\r\n                        $(v).css(\"left\", $(v).parent(\"td\").offset().left - $('#curriculumInfo-divcon').offset().left + \"px\");\r\n                    }\r\n                });\r\n            }\r\n        }\r\n    </script>\r\n\r\n    \r\n    <script type=\"text/javascript\">\r\n        function initialization() {\r\n            var ycd = document.getElementById(\"ycd\").value;\r\n            for (var j = 1; j <= ycd; j++) {\r\n                var zc = document.getElementById(\"zc\" + j + \"\").value;\r\n                var jc = document.getElementById(\"xqjc\" + j + \"\").value;\r\n                window.frames[\"jasjc\"].tzqjc(zc, jc);\r\n            }\r\n        }\r\n\r\n        function dolook(oper, p) {\r\n            $('#drag-ol .current-week').removeClass('current-week');\r\n            $('#drag-ol .border-common:eq(' + (p - 1) + ') ').addClass('current-week');\r\n\r\n            var frm = window.document.forms[2];\r\n            if (oper == \"\") {\r\n                $(\"#drag-section\").empty();\r\n            }\r\n            $(\"#oper\").val(oper);\r\n            $(\"#week\").val(p);\r\n            var frm = window.document.forms[2];\r\n            frm.action = \"/student/teachingResources/classroomCurriculum/searchClassroomUesedInfo\";\r\n            searchCalssRoomInfo(\"calssRoomInfo\");\r\n        }\r\n\r\n        function dyniframesize(down) {\r\n            var pTar = null;\r\n            if (document.getElementById) {\r\n                pTar = document.getElementById(down);\r\n            }\r\n            else {\r\n                eval('pTar = ' + down + ';');\r\n            }\r\n            if (pTar && !window.opera) {\r\n                //begin resizing iframe\r\n                pTar.style.display = \"block\";\r\n                if (pTar.contentDocument && pTar.contentDocument.body.offsetHeight) {\r\n                    //ns6 syntax\r\n                    pTar.height = pTar.contentDocument.body.offsetHeight + 20;\r\n                    pTar.width = pTar.contentDocument.body.scrollWidth + 20;\r\n                }\r\n                else if (pTar.Document && pTar.Document.body.scrollHeight) {\r\n                    //ie5+ syntax\r\n                    pTar.height = pTar.Document.body.scrollHeight;\r\n                    pTar.width = pTar.Document.body.scrollWidth;\r\n                }\r\n            }\r\n        }\r\n\r\n        $(function () {\r\n            $(\"#close\").click(function (e) {\r\n                var id = $(e.target).attr(\"id\");\r\n                if (id == \"td_div\") {\r\n\r\n                } else {\r\n                    $('#calssInfo-divcon').animate({right: '-70%'});\r\n                    $('#curriculumInfo-divcon').animate({right: '-70%'});\r\n                }\r\n            });\r\n        });\r\n\r\n\r\n        function dc() {\r\n            $(\"#page-content-template\").after('<div class=\"col-xs-12\" id=\"print_div_now\">' + $(\"#print_div\").html() + '</div>');\r\n            $(\"#page-content-template\").hide();\r\n            $(\"#breadcrumbs\").hide();\r\n\r\n            $(\"#print_div_now div.class_div\").addClass(\"printDiv\");\r\n            $(\"#print_div_now #courseTable\").addClass(\"printDiv\");\r\n            $(\"#print_div_now #courseTable\").removeCla".toCharArray();
    _jsp_string0 = "\r\n\r\n\r\n\r\n\r\n<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\">\r\n<html>\r\n<head>\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\r\n    <title>\u6559\u5ba4\u8bfe\u8868</title>\r\n    <link rel=\"stylesheet\" href=\"/css/commons/kcbcolor.css\" type=\"text/css\"/>\r\n\r\n    <style type=\"text/css\">\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n        }\r\n\r\n        .jc-back {\r\n            background-color: #f0f0f0\r\n        }\r\n\r\n        #drag-section {\r\n            padding-bottom: 20px;\r\n        }\r\n\r\n        #drag-ol {\r\n            list-style-type: none;\r\n            margin: 0px;\r\n            padding: 0;\r\n        }\r\n\r\n        #drag-ol .ui-selecting {\r\n            background: #FECA40;\r\n        }\r\n\r\n        #drag-ol .ui-selected {\r\n            background: #F39814;\r\n            color: white;\r\n        }\r\n\r\n        #drag-ol .border-common {\r\n            cursor: pointer;\r\n            float: left;\r\n            text-align: center;\r\n            margin: 0;\r\n            padding: 0;\r\n            width: 35px;\r\n            height: 30px;\r\n            line-height: 30px;\r\n            border: 1px solid #aaa;\r\n        }\r\n\r\n        #drag-ol .current-week {\r\n            background-color: #CCCC66 !important;\r\n        }\r\n\r\n        .drag-border-right {\r\n            border-right: 1px solid #aaa !important;\r\n        }\r\n\r\n        #curriculumInfo-divcon, #calssInfo-divcon {\r\n            width: 0;\r\n            height: 95%;\r\n            background: #fff;\r\n            /*position: absolute;*/\r\n            top: 35px;;\r\n            right: 0;\r\n            /*display:none;*/\r\n            box-shadow: #a9a9a9 0px 0px 10px;\r\n            -webkit-box-shadow: #a9a9a9 0px 0px 10px;\r\n            -moz-box-shadow: #a9a9a9 0px 0px 10px;\r\n            overflow: auto;\r\n            position: fixed;\r\n            z-index: 999;\r\n        }\r\n\r\n        .div-title {\r\n            width: 100%;\r\n            position: relative;\r\n            border-bottom: #eeece8 solid 1px;\r\n        }\r\n\r\n        .div-title h3 {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-size: 16px;\r\n            line-height: 55px;\r\n            color: #999;\r\n            text-indent: 23px;\r\n            font-weight: normal;\r\n        }\r\n\r\n        .div-title span {\r\n            margin: 0;\r\n            padding: 0;\r\n            text-align: center;\r\n            margin-right: 10px;\r\n            display: inline-block;\r\n            position: absolute;\r\n            right: 0px;\r\n            top: 15px;\r\n            width: 50px;\r\n            height: 25px;\r\n            border-left: #dbdbdb solid 1px;\r\n        }\r\n\r\n        /*\u9ed8\u8ba4\u6eda\u52a8\u5206\u9875*/\r\n    </style>\r\n\r\n    <script type=\"text/javascript\" src=\"/js/json/json2.js\"></script>\r\n    <script type=\"text/javascript\" src=\"/js/customjs/kcbcolor.js\"></script>\r\n    <script type=\"text/javascript\" src=\"/js/customjs/coursetable.js\"></script>\r\n    <script type=\"text/javascript\" src=\"/js/html2canvas/html2canvas.js\"></script>\r\n    <script type=\"text/javascript\" src=\"/js/customjs/browser.js\"></script>\r\n\r\n    <script src=\"/js/bluebird.js\"></script>\r\n    <script src=\"/js/tableExport/libs/pdfmake/pdfmake.min.js\"></script>\r\n    <script src=\"/js/tableExport/libs/pdfmake/vfs_fonts.js\"></script>\r\n    <script type=\"text/javascript\">\r\n        $(function () {\r\n            $('.select').chosen({allow_single_deselect: true});\r\n            $(window).off('resize.chosen').on('resize.chosen', function () {\r\n                $('.select').each(function () {\r\n                    var $this = $(this);\r\n                    $this.next().css({'width': 200});\r\n                });\r\n            }).trigger('resize.chosen');\r\n        });\r\n        function toChangeTeachingBuilding(o) {\r\n            var campusNumber = o.value;\r\n            if (campusNumber == '' || campusNumber == null) {\r\n                $(\"#teachingBuilding\").html(\"<option value = ''>\u5168\u90e8</option>\");\r\n                $(\"#classRoom\").html(\"<option value = ''>\u5168\u90e8</option>\");\r\n                return false;\r\n            }\r\n            $.get(\"/student/teachingResources/classroomCurriculum/\" + campusNumber\r\n                    + \"/teachingBuildingJson\", function (data) {\r\n                var htmlString = \"<option value = ''>\u5168\u90e8</option>\";\r\n                if (data != null && data != \"\") {\r\n                    for (var i = 0; i < data.length; i++) {\r\n                        var name = data[i].teachingBuildingName;\r\n                        htmlString += \"<option value = '\" + data[i].id.teachingBuildingNumber + \"'>\" + name\r\n                                + \"</option>\";\r\n                    }\r\n                }\r\n\r\n                $(\"#teachingBuilding\").html(htmlString);\r\n\r\n                $(\"#classRoom\").html(\"<option value = ''>\u5168\u90e8</option>\");\r\n\r\n            });\r\n\r\n        }\r\n\r\n        function toChangeClassroom(o) {\r\n            var campusNumber = $(\"#campus\").val();\r\n            var teachingBuildingName = o.value;\r\n            if (teachingBuildingName == '' || teachingBuildingName == null) {\r\n                $(\"#classRoom\").html(\"<option value = ''>\u5168\u90e8</option>\");\r\n                return false;\r\n            }\r\n            $.get(\"/student/teachingResources/classroomCurriculum/\" + campusNumber + \"/\"\r\n                    + teachingBuildingName + \"/classroomJson\", function (data) {\r\n                var htmlString = \"<option value = ''>\u5168\u90e8</option>\";\r\n                if (data != null && data != \"\") {\r\n                    for (var i = 0; i < data.length; i++) {\r\n                        var name = data[i].classroomName;\r\n                        htmlString += \"<option value = '\" + data[i].id.classroomNumber + \"'>\" + name\r\n                                + \"</option>\";\r\n                    }\r\n                }\r\n\r\n                $(\"#classRoom\").html(htmlString);\r\n\r\n            });\r\n\r\n        }\r\n        //			curriculumInfo-offcanvas classroomInfo-offcanvas\r\n        var colour = [\"#000000\", \"#080808\", \"#121212\", \"#1c1c1c\", \"#232323\", \"#2f2f2f\", \"#3c3c3c\", \"#464646\", \"#525252\", \"#606060\", \"#5d5d5d\", \"#6d6d6d\",\r\n            \"#797979\", \"#898989\", \"#949494\", \"#a2a2a2\", \"#bcbcbc\", \"#c8c8c8\", \"#d4d4d4\", \"#d4d4d4\", \"#e8e8e8\", \"#f0f0f0\", \"#f8f8f8\", \"#ffffff\"];\r\n\r\n        function searchCalssRoomInfo(id) {\r\n            $.ajax({\r\n                url: \"/student/teachingResources/classroomCurriculum/searchClassroomUesedInfoLo\",\r\n                type: \"post\",\r\n                data: $(\"#\" + id).serialize(),\r\n                /*dataType: \"json\",*/\r\n                success: function (d) {\r\n                    $(\"#title2\").html(d.data.planName + \"&nbsp;\" + d.data.campusName + \"&nbsp;&nbsp;\" + d.data.teachingBuildingName + \"&nbsp;&nbsp;\" + d.data.classroomName + \"&nbsp;&nbsp;\u6559\u5ba4\u4fe1\u606f&nbsp;&nbsp;\u65f6\u95f4\u4e00\u89c8\u8868\");\r\n\r\n                    $(\"#ycd\").val(d.data.planNumber);\r\n                    $(\"#week\").val(d.data.week);\r\n\r\n                    $(\"#planCode\").val(d.data.planNumber);\r\n                    $(\"#campusCode\").val(d.data.campusCode);\r\n                    $(\"#teachingBuildingCode\").val(d.data.teachingBuildingCode);\r\n                    $(\"#classroomCode\").val(d.data.classroomCode);\r\n\r\n                    $(\"#campusName\").val(d.data.campusName);\r\n                    $(\"#teachingBuildingNamec\").val(d.data.teachingBuildingName);\r\n                    $(\"#classroomNamec\").val(d.data.classroomName);\r\n                    $(\"#oper\").val(d.data.oper);\r\n\r\n\r\n                    if (d.data.oper == \"\") {\r\n                        var bottonDiv = \"<div class='tabbable'><ul class='nav nav-tabs padding-18'><li class=''>\" +\r\n                                \"<a id='td_div' data-toggle='tab' href='#' onClick=dolook('currentWeek')>\u5355\u5468\u6b21\u6a21\u5f0f</a></li>\" +\r\n                                \"<li class='active'><a id='td_div' data-toggle='tab' href='#' onclick=dolook('')>\" +\r\n                                \"\u5168\u5468\u6b21\u6a21\u5f0f</a></li>\";\r\n                        bottonDiv += \"<li class='' ><div style='margin-left:500px;'>\u672c\u6559\u5ba4\u5b89\u6392\u60c5\u51b5:<img src='".toCharArray();
    _jsp_string9 = "ss(\"table\");\r\n            $(\"#print_div_now #courseTable\").css(\"width\", $(\"#print_div_now #mycoursetable\").width());\r\n\r\n            printDivBuild();\r\n            var index = layer.load(0, {\r\n                shade: [0.2, '#000'] //0.1\u900f\u660e\u5ea6\u7684\u767d\u8272\u80cc\u666f\r\n            });\r\n            var shareContent = document.querySelector(\"#print_div_now\");\r\n            var width = shareContent.offsetWidth;\r\n            var height = shareContent.offsetHeight;\r\n            var canvas = document.createElement(\"canvas\");\r\n            var scale = 2;\r\n\r\n            canvas.width = width * scale;\r\n            canvas.height = height * scale;\r\n            canvas.getContext(\"2d\").scale(scale, scale);\r\n            var opts = {\r\n                scale: scale,\r\n                canvas: canvas,\r\n                logging: true,\r\n                width: width,\r\n                height: height\r\n            };\r\n\r\n            html2canvas(shareContent, opts).then(function (canvas) {\r\n                canvas.id = \"mycanvas\";\r\n                var dataUrl = canvas.toDataURL();\r\n                var dd = {\r\n                    pageSize: 'A4',\r\n                    content: [\r\n                        {text: '\u6559\u5ba4\u8bfe\u8868', fontSize: 12, margin: [250, 0, 0, 0]},\r\n                        {image: dataUrl, width: 520, margin: [0, 10, 0, 0]}\r\n                    ],\r\n                    defaultStyle: {\r\n                        font: 'simsun'\r\n                    }\r\n                };\r\n                pdfMake.fonts = {\r\n                    simsun: {\r\n                        normal: 'simsun.ttf',\r\n                        bold: 'simsun.ttf',\r\n                        italics: 'simsun.ttf',\r\n                        bolditalics: 'simsun.ttf'\r\n                    }\r\n                };\r\n                pdfMake.createPdf(dd).download(\"\u6559\u5ba4\u8bfe\u8868.pdf\");\r\n                $(\"#print_div_now\").remove();\r\n                $(\"#breadcrumbs\").show();\r\n                $(\"#page-content-template\").show();\r\n                layer.close(index);\r\n            });\r\n\r\n        }\r\n\r\n    </script>\r\n\r\n\r\n</head>\r\n<body>\r\n".toCharArray();
    _jsp_string25 = "\r\n                                        </option>\r\n                                    ".toCharArray();
    _jsp_string10 = "\r\n    <div class=\"alert alert-block alert-success\">\r\n        <button type=\"button\" class=\"close\" data-dismiss=\"alert\">\r\n            <i class=\"ace-icon fa fa-times\"></i>\r\n        </button>\r\n\r\n        <i class=\"ace-icon fa fa-exclamation-circle orange\"></i>\r\n        &nbsp;\r\n        \u6ca1\u6709\u5b8c\u6210\u8bc4\u4f30,\u4e0d\u80fd\u67e5\u770b\u8bfe\u8868\uff01\r\n    </div>\r\n".toCharArray();
    _jsp_string30 = "\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!-- \u9875\u7801\u7ed3\u675f -->\r\n    \r\n    <div id=\"curriculumInfo-divcon\" class=\"td_div\" style=\"width: 70%;right: -70%;\">\r\n        <div class=\"div-title\">\r\n            <h3 id=\"title\"></h3>\r\n				<span class='right_top_oper' style=\"width: 170px;\">\r\n					<button type=\"button\" title=\"\u6253\u5370\" class='btn btn-info btn-xs btn-round' onclick=\"dy();\"\r\n                            style=\"float: left;\">\r\n                        <i class='ace-icon fa fa-print bigger-120'></i> \u6253\u5370\u8bfe\u8868\r\n                    </button>\r\n					<button type=\"button\" title=\"\u5bfc\u51fa\" class='btn btn-info btn-xs btn-round' onclick=\"dc();\"\r\n                            style=\"float: right;\">\r\n                        <i class='ace-icon fa  fa-cloud-download bigger-120'></i> \u5bfc\u51fa\u8bfe\u8868\r\n                    </button>\r\n				</span>\r\n        </div>\r\n        <div class=\"row\" style=\"margin-right: 0px; margin-left: 0px;\">\r\n            <div class=\"col-xs-12\" id=\"print_div\">\r\n\r\n                <div class=\"widget-content\">\r\n                    <div id=\"mycoursetable\"></div>\r\n                </div>\r\n                <div id=\"other-course\">\r\n                    <h4 class=\"header smaller lighter grey\" id=\"table-title\">\r\n                        <i class=\"fa fa-info-circle\"></i> \u8bfe\u7a0b\u4fe1\u606f\r\n                    </h4>\r\n\r\n                    <div class=\"tab-pane active\" id=\"tab2155\">\r\n                        <table class=\"table table-striped table-bordered\">\r\n                            <thead>\r\n                            <tr>\r\n                                <td style=\"font-weight: bold;\">\u8bfe\u7a0b\u53f7</td>\r\n                                <td style=\"font-weight: bold;\">\u8bfe\u7a0b\u540d</td>\r\n                                <td style=\"font-weight: bold;\">\u8bfe\u5e8f\u53f7</td>\r\n                                <td style=\"font-weight: bold;\">\u6559\u5e08</td>\r\n                                <td style=\"font-weight: bold;\">\u5468\u6b21</td>\r\n                                <td style=\"font-weight: bold;\">\u661f\u671f</td>\r\n                                <td style=\"font-weight: bold;\">\u8282\u6b21</td>\r\n                                <td style=\"font-weight: bold;\">\u6821\u533a</td>\r\n                                <td style=\"font-weight: bold;\">\u6559\u5b66\u697c</td>\r\n                                <td style=\"font-weight: bold;\">\u6559\u5ba4</td>\r\n                            </tr>\r\n                            </thead>\r\n                            <tbody id=\"course-tbody\">\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    \r\n    <div id=\"calssInfo-divcon\" class=\"td_div\" style=\"width: 70%;right: -70%\">\r\n        <div class=\"div-title\" style=\"border-bottom: #eeece8 solid 0px;\">\r\n            <h3 id=\"title2\"></h3>\r\n        </div>\r\n        <div class=\"row\" style=\"margin-left: 0px; margin-right: 0px;\">\r\n            <div class=\"col-xs-12\">\r\n                <form name=\"frm\" method=\"POST\" id=\"calssRoomInfo\">\r\n                    <input type=\"hidden\" name=\"planCode\" id=\"planCode\">\r\n                    <input type=\"hidden\" name=\"campusCode\" id=\"campusCode\">\r\n                    <input type=\"hidden\" name=\"teachingBuildingCode\" id=\"teachingBuildingCode\">\r\n                    <input type=\"hidden\" name=\"classroomCode\" id=\"classroomCode\">\r\n                    <input type=\"hidden\" name=\"campusName\" id=\"campusName\">\r\n                    <input type=\"hidden\" name=\"teachingBuildingName\" id=\"teachingBuildingNamec\">\r\n                    <input type=\"hidden\" name=\"classroomName\" id=\"classroomNamec\">\r\n                    <input type=\"hidden\" name=\"week\" id=\"week\">\r\n                    <input type=\"hidden\" name=\"oper\" id=\"oper\">\r\n                    <!-- \u8868\u5355\u7ed3\u675f -->\r\n                    <div id=\"buttonDiv\" style=\"margin-bottom: 10px;\"></div>\r\n                </form>\r\n                <div class=\"widget-content\">\r\n                    <div id=\"drag-section\"></div>\r\n                    <form name=\"frm\" method=\"GET\">\r\n                        <table id=\"frm-table\" class=\"table table-bordered\" border=\"0\"\r\n                               cellpadding=\"0\" cellspacing=\"1\" bgcolor=\"#d7d7d7\" width=\"100%\">\r\n                            <input type=\"hidden\" id=\"ycd\" name=\"ycd\"/>\r\n                        </table>\r\n                    </form>\r\n\r\n                    <div class=\"widget-content\">\r\n                        <div id=\"iframeDiv\"></div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n".toCharArray();
  }
}
