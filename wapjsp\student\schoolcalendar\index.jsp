<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学校校历</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 校历页面样式 */
        .calendar-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            text-align: center;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .calendar-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .calendar-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .calendar-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .calendar-nav {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--divider-color);
        }
        
        .nav-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .nav-button:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .current-month {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .calendar-table th,
        .calendar-table td {
            border: 1px solid var(--divider-color);
            text-align: center;
            vertical-align: top;
            position: relative;
        }
        
        .calendar-table th {
            background: var(--info-color);
            color: white;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        .week-header {
            background: var(--secondary-color);
            color: white;
            width: 50px;
        }
        
        .month-header {
            background: var(--warning-color);
            color: white;
            width: 40px;
        }
        
        .calendar-cell {
            height: 60px;
            min-height: 60px;
            padding: 2px;
            position: relative;
        }
        
        .day-number {
            position: absolute;
            top: 2px;
            right: 4px;
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            z-index: 2;
        }
        
        .weekend {
            color: var(--error-color) !important;
        }
        
        .today {
            background: #fff3cd !important;
        }
        
        .other-month {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .calendar-event {
            position: absolute;
            bottom: 2px;
            left: 2px;
            right: 2px;
            background: var(--primary-color);
            color: white;
            font-size: var(--font-size-mini);
            padding: 1px 4px;
            border-radius: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            z-index: 1;
            cursor: pointer;
        }
        
        .event-color-1 { background: #1890ff; }
        .event-color-2 { background: #52c41a; }
        .event-color-3 { background: #fa8c16; }
        .event-color-4 { background: #eb2f96; }
        .event-color-5 { background: #722ed1; }
        .event-color-6 { background: #13c2c2; }
        .event-color-7 { background: #f5222d; }
        
        .legend-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .legend-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-title i {
            color: var(--primary-color);
        }
        
        .legend-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: var(--font-size-small);
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            flex-shrink: 0;
        }
        
        .event-detail-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .event-detail-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            max-width: 400px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .event-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .event-detail-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .event-detail-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .event-detail-info {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        @media (max-width: 480px) {
            .calendar-cell {
                height: 50px;
                min-height: 50px;
            }
            
            .day-number {
                font-size: calc(var(--font-size-small) - 1px);
            }
            
            .calendar-event {
                font-size: calc(var(--font-size-mini) - 1px);
                padding: 1px 2px;
            }
            
            .week-header {
                width: 40px;
            }
            
            .month-header {
                width: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学校校历</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 校历标题 -->
        <div class="calendar-header">
            <div class="calendar-title" id="calendarTitle">学校校历</div>
            <div class="calendar-subtitle" id="calendarSubtitle">学年学期</div>
        </div>
        
        <!-- 校历容器 -->
        <div class="calendar-container">
            <div class="calendar-nav">
                <button class="nav-button" id="prevButton" onclick="changeMonth(-1);">
                    <i class="ace-icon fa fa-chevron-left"></i>
                    <span>上月</span>
                </button>
                <div class="current-month" id="currentMonth">当前月份</div>
                <button class="nav-button" id="nextButton" onclick="changeMonth(1);">
                    <span>下月</span>
                    <i class="ace-icon fa fa-chevron-right"></i>
                </button>
            </div>
            
            <table class="calendar-table" id="calendarTable">
                <thead>
                    <tr>
                        <th class="month-header">月</th>
                        <th class="week-header">周</th>
                        <th>周一</th>
                        <th>周二</th>
                        <th>周三</th>
                        <th>周四</th>
                        <th>周五</th>
                        <th class="weekend">周六</th>
                        <th class="weekend">周日</th>
                    </tr>
                </thead>
                <tbody id="calendarBody">
                    <!-- 动态生成校历内容 -->
                </tbody>
            </table>
        </div>
        
        <!-- 图例 -->
        <div class="legend-container" id="legendContainer" style="display: none;">
            <div class="legend-title">
                <i class="ace-icon fa fa-info-circle"></i>
                事件图例
            </div>
            <div class="legend-items" id="legendItems">
                <!-- 动态生成图例 -->
            </div>
        </div>
        
        <!-- 事件详情面板 -->
        <div class="event-detail-panel" id="eventDetailPanel">
            <div class="event-detail-content">
                <div class="event-detail-header">
                    <div class="event-detail-title" id="eventDetailTitle">事件详情</div>
                    <button class="event-detail-close" onclick="closeEventDetail();">×</button>
                </div>
                <div class="event-detail-info" id="eventDetailInfo">
                    <!-- 动态加载事件详情 -->
                </div>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-calendar"></i>
            <div>暂无校历数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let calendarData = [];
        let currentYear = new Date().getFullYear();
        let currentMonth = new Date().getMonth() + 1;
        let startDate = '';
        let endDate = '';
        let semesterInfo = {};
        let eventColors = {};
        let colorIndex = 0;

        $(function() {
            initPage();
            loadCalendarData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载校历数据
        function loadCalendarData() {
            showLoading(true);

            // 获取学期信息和校历数据
            $.ajax({
                url: "/student/schoolcalendar/calendar",
                type: "get",
                dataType: "html",
                success: function(data) {
                    try {
                        // 从返回的HTML中提取JavaScript变量
                        const calMatch = data.match(/var cal = '(.+?)';/);
                        const xnMatch = data.match(/var xnxq = "(.+?)";/);
                        const xqmMatch = data.match(/var xqm = "(.+?)";/);
                        const kxrqMatch = data.match(/var rq = "(.+?)";/);
                        const skzcMatch = data.match(/var skzc = "(.+?)";/);

                        if (calMatch && calMatch[1]) {
                            calendarData = JSON.parse(calMatch[1].replace(/'/g, '"'));
                        }

                        if (xnMatch && xnMatch[1]) {
                            semesterInfo.xnxq = xnMatch[1];
                        }

                        if (xqmMatch && xqmMatch[1]) {
                            semesterInfo.xqm = xqmMatch[1];
                        }

                        if (kxrqMatch && kxrqMatch[1]) {
                            semesterInfo.kxrq = kxrqMatch[1];
                        }

                        if (skzcMatch && skzcMatch[1]) {
                            semesterInfo.skzc = parseInt(skzcMatch[1]);
                        }

                        initCalendar();
                        renderCalendar();
                        showEmptyState(false);
                    } catch (error) {
                        console.error('解析校历数据失败:', error);
                        showError('加载校历数据失败');
                        showEmptyState(true);
                    }
                },
                error: function() {
                    showError('网络请求失败');
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 初始化校历
        function initCalendar() {
            // 设置标题
            $('#calendarTitle').text('学校校历');
            $('#calendarSubtitle').text(semesterInfo.xnxq + '学年(' + semesterInfo.xqm + ')');

            // 计算学期开始和结束日期
            if (semesterInfo.kxrq && semesterInfo.skzc) {
                const year = parseInt(semesterInfo.kxrq.substring(0, 4));
                const month = parseInt(semesterInfo.kxrq.substring(4, 6));
                const day = parseInt(semesterInfo.kxrq.substring(6, 8));

                startDate = new Date(year, month - 1, day);
                endDate = new Date(startDate.getTime() + (semesterInfo.skzc * 7 - 1) * 24 * 60 * 60 * 1000);

                // 设置当前显示月份为开学月份
                currentYear = year;
                currentMonth = month;
            }

            // 生成事件颜色映射
            generateEventColors();

            // 生成图例
            generateLegend();
        }

        // 生成事件颜色映射
        function generateEventColors() {
            eventColors = {};
            colorIndex = 0;

            calendarData.forEach(function(event) {
                if (!eventColors[event.nr]) {
                    eventColors[event.nr] = (colorIndex % 7) + 1;
                    colorIndex++;
                }
            });
        }

        // 生成图例
        function generateLegend() {
            if (Object.keys(eventColors).length === 0) return;

            const legendItems = $('#legendItems');
            legendItems.empty();

            Object.keys(eventColors).forEach(function(eventName) {
                const colorClass = eventColors[eventName];
                const itemHtml = `
                    <div class="legend-item">
                        <div class="legend-color event-color-${colorClass}"></div>
                        <span>${eventName}</span>
                    </div>
                `;
                legendItems.append(itemHtml);
            });

            $('#legendContainer').show();
        }

        // 渲染校历
        function renderCalendar() {
            updateMonthDisplay();
            generateCalendarTable();
            fillCalendarEvents();
        }

        // 更新月份显示
        function updateMonthDisplay() {
            $('#currentMonth').text(currentYear + '年' + currentMonth + '月');

            // 更新导航按钮状态
            const isFirstMonth = (currentYear === startDate.getFullYear() && currentMonth === startDate.getMonth() + 1);
            const isLastMonth = (currentYear === endDate.getFullYear() && currentMonth === endDate.getMonth() + 1);

            $('#prevButton').prop('disabled', isFirstMonth);
            $('#nextButton').prop('disabled', isLastMonth);
        }

        // 生成校历表格
        function generateCalendarTable() {
            const tbody = $('#calendarBody');
            tbody.empty();

            const firstDay = new Date(currentYear, currentMonth - 1, 1);
            const lastDay = new Date(currentYear, currentMonth, 0);
            const daysInMonth = lastDay.getDate();
            const startWeekDay = firstDay.getDay() === 0 ? 7 : firstDay.getDay(); // 周一为1，周日为7

            let currentWeek = 1;
            let currentRow = '';
            let cellsInRow = 0;

            // 生成日历行
            for (let week = 1; week <= 6; week++) {
                if (week === 1) {
                    currentRow = '<tr>';

                    // 月份列
                    currentRow += `<td class="month-header" rowspan="6">${currentMonth}</td>`;

                    // 周次列
                    currentRow += `<td class="week-header">第${currentWeek}周</td>`;

                    // 填充第一周的空白天数
                    for (let i = 1; i < startWeekDay; i++) {
                        currentRow += '<td class="calendar-cell other-month"></td>';
                        cellsInRow++;
                    }

                    // 填充第一周的日期
                    for (let day = 1; day <= 7 - startWeekDay + 1 && day <= daysInMonth; day++) {
                        currentRow += createDayCell(day);
                        cellsInRow++;
                    }

                    currentRow += '</tr>';
                    tbody.append(currentRow);
                    currentWeek++;
                    cellsInRow = 0;
                } else {
                    currentRow = '<tr>';

                    // 周次列
                    currentRow += `<td class="week-header">第${currentWeek}周</td>`;

                    // 计算这一周的起始日期
                    const weekStartDay = (week - 1) * 7 - startWeekDay + 2;

                    // 填充这一周的日期
                    for (let i = 0; i < 7; i++) {
                        const day = weekStartDay + i;
                        if (day > 0 && day <= daysInMonth) {
                            currentRow += createDayCell(day);
                        } else {
                            currentRow += '<td class="calendar-cell other-month"></td>';
                        }
                    }

                    currentRow += '</tr>';
                    tbody.append(currentRow);
                    currentWeek++;

                    // 如果已经显示完所有日期，跳出循环
                    if (weekStartDay + 6 >= daysInMonth) {
                        break;
                    }
                }
            }
        }

        // 创建日期单元格
        function createDayCell(day) {
            const dateStr = formatDate(currentYear, currentMonth, day);
            const isToday = isDateToday(currentYear, currentMonth, day);
            const isWeekend = isDateWeekend(currentYear, currentMonth, day);

            let cellClass = 'calendar-cell';
            if (isToday) cellClass += ' today';

            let dayClass = '';
            if (isWeekend) dayClass = 'weekend';

            return `
                <td class="${cellClass}" id="cell_${dateStr}">
                    <div class="day-number ${dayClass}">${day}</div>
                </td>
            `;
        }

        // 填充校历事件
        function fillCalendarEvents() {
            calendarData.forEach(function(event) {
                const startDate = parseDate(event.ksrq);
                const endDate = parseDate(event.jsrq);
                const eventName = event.nr;
                const colorClass = eventColors[eventName];

                // 计算事件持续天数
                const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

                // 为每一天添加事件
                for (let i = 0; i <= daysDiff; i++) {
                    const eventDate = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);

                    // 只显示当前月份的事件
                    if (eventDate.getFullYear() === currentYear && eventDate.getMonth() + 1 === currentMonth) {
                        const dateStr = formatDate(eventDate.getFullYear(), eventDate.getMonth() + 1, eventDate.getDate());
                        const cell = $(`#cell_${dateStr}`);

                        if (cell.length > 0) {
                            const eventHtml = `
                                <div class="calendar-event event-color-${colorClass}"
                                     onclick="showEventDetail('${eventName}', '${event.ksrq}', '${event.jsrq}');"
                                     title="${eventName}">
                                    ${eventName}
                                </div>
                            `;
                            cell.append(eventHtml);
                        }
                    }
                }
            });
        }

        // 显示事件详情
        function showEventDetail(eventName, startDate, endDate) {
            $('#eventDetailTitle').text(eventName);

            const startDateFormatted = formatDisplayDate(parseDate(startDate));
            const endDateFormatted = formatDisplayDate(parseDate(endDate));

            const infoHtml = `
                <p><strong>事件名称：</strong>${eventName}</p>
                <p><strong>开始日期：</strong>${startDateFormatted}</p>
                <p><strong>结束日期：</strong>${endDateFormatted}</p>
                <p><strong>持续时间：</strong>${calculateDuration(startDate, endDate)}</p>
            `;

            $('#eventDetailInfo').html(infoHtml);
            $('#eventDetailPanel').show();
        }

        // 关闭事件详情
        function closeEventDetail() {
            $('#eventDetailPanel').hide();
        }

        // 切换月份
        function changeMonth(direction) {
            currentMonth += direction;

            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            } else if (currentMonth < 1) {
                currentMonth = 12;
                currentYear--;
            }

            renderCalendar();
        }

        // 工具函数
        function formatDate(year, month, day) {
            return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);
        }

        function parseDate(dateStr) {
            if (dateStr.length === 8) {
                // YYYYMMDD格式
                const year = parseInt(dateStr.substring(0, 4));
                const month = parseInt(dateStr.substring(4, 6)) - 1;
                const day = parseInt(dateStr.substring(6, 8));
                return new Date(year, month, day);
            } else {
                // YYYY-MM-DD格式
                return new Date(dateStr);
            }
        }

        function formatDisplayDate(date) {
            return date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
        }

        function calculateDuration(startDate, endDate) {
            const start = parseDate(startDate);
            const end = parseDate(endDate);
            const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
            return days + '天';
        }

        function isDateToday(year, month, day) {
            const today = new Date();
            return year === today.getFullYear() &&
                   month === today.getMonth() + 1 &&
                   day === today.getDate();
        }

        function isDateWeekend(year, month, day) {
            const date = new Date(year, month - 1, day);
            const dayOfWeek = date.getDay();
            return dayOfWeek === 0 || dayOfWeek === 6;
        }

        // 刷新数据
        function refreshData() {
            loadCalendarData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.calendar-container').hide();
                $('.calendar-header').hide();
                $('#legendContainer').hide();
            } else {
                $('#emptyState').hide();
                $('.calendar-container').show();
                $('.calendar-header').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击面板背景关闭
        $('#eventDetailPanel').click(function(e) {
            if (e.target === this) {
                closeEventDetail();
            }
        });
    </script>
</body>
</html>
