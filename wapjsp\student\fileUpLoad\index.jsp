<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>文件上传</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 文件上传页面样式 */
        .upload-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .upload-area {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .upload-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .upload-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .upload-zone {
            border: 2px dashed var(--border-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            background: var(--bg-tertiary);
        }
        
        .upload-zone:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .upload-zone.dragover {
            border-color: var(--primary-color);
            background: var(--primary-light);
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 48px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-md);
        }
        
        .upload-text {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            font-weight: 500;
        }
        
        .upload-hint {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .upload-limits {
            background: var(--warning-light);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-md);
            font-size: var(--font-size-small);
            color: var(--warning-color);
        }
        
        .limits-title {
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .limits-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .limits-list li {
            margin-bottom: 2px;
        }
        
        .file-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-clear {
            background: var(--error-color);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            border: none;
            cursor: pointer;
        }
        
        .btn-upload {
            background: var(--success-color);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            border: none;
            cursor: pointer;
        }
        
        .file-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-item.uploading {
            background: var(--info-light);
        }
        
        .file-item.success {
            background: var(--success-light);
        }
        
        .file-item.error {
            background: var(--error-light);
        }
        
        .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-md);
            font-size: var(--font-size-base);
        }
        
        .file-icon.pdf {
            background: var(--error-color);
        }
        
        .file-icon.image {
            background: var(--success-color);
        }
        
        .file-icon.document {
            background: var(--info-color);
        }
        
        .file-icon.other {
            background: var(--text-disabled);
        }
        
        .file-info {
            flex: 1;
            min-width: 0;
        }
        
        .file-name {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .file-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-size {
            white-space: nowrap;
        }
        
        .file-status {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-waiting {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-uploading {
            background: var(--info-color);
            color: white;
        }
        
        .status-success {
            background: var(--success-color);
            color: white;
        }
        
        .status-error {
            background: var(--error-color);
            color: white;
        }
        
        .file-progress {
            width: 100%;
            height: 4px;
            background: var(--bg-tertiary);
            border-radius: 2px;
            overflow: hidden;
            margin-top: var(--margin-xs);
        }
        
        .progress-bar {
            height: 100%;
            background: var(--primary-color);
            border-radius: 2px;
            transition: width var(--transition-base);
            width: 0%;
        }
        
        .file-actions {
            display: flex;
            gap: var(--spacing-xs);
            margin-left: var(--margin-sm);
        }
        
        .btn-retry {
            width: 28px;
            height: 28px;
            border-radius: 4px;
            background: var(--warning-color);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: var(--font-size-small);
        }
        
        .btn-delete {
            width: 28px;
            height: 28px;
            border-radius: 4px;
            background: var(--error-color);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: var(--font-size-small);
        }
        
        .upload-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }
        
        .summary-stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .stat-number.total {
            color: var(--primary-color);
        }
        
        .stat-number.success {
            color: var(--success-color);
        }
        
        .stat-number.error {
            color: var(--error-color);
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .category-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .selector-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .category-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .category-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">文件上传</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="upload-header">
            <div class="header-title">文件上传</div>
            <div class="header-subtitle">上传和管理文件</div>
        </div>

        <!-- 文件分类选择 -->
        <div class="category-selector">
            <div class="selector-title">
                <i class="ace-icon fa fa-folder"></i>
                <span>文件分类</span>
            </div>

            <select class="category-input" id="fileCategory">
                <option value="">请选择文件分类</option>
                <option value="assignment">作业提交</option>
                <option value="report">实验报告</option>
                <option value="thesis">论文相关</option>
                <option value="certificate">证书证明</option>
                <option value="photo">照片图片</option>
                <option value="other">其他文件</option>
            </select>
        </div>

        <!-- 上传区域 -->
        <div class="upload-area">
            <div class="upload-title">
                <i class="ace-icon fa fa-cloud-upload"></i>
                <span>选择文件</span>
            </div>

            <div class="upload-zone" id="uploadZone" onclick="selectFiles();">
                <div class="upload-icon">
                    <i class="ace-icon fa fa-cloud-upload"></i>
                </div>
                <div class="upload-text">点击或拖拽文件到此处上传</div>
                <div class="upload-hint">
                    支持多文件同时上传<br>
                    可上传图片、文档、PDF等格式文件
                </div>
            </div>

            <div class="upload-limits">
                <div class="limits-title">上传限制：</div>
                <ul class="limits-list">
                    <li>• 单个文件大小不超过 10MB</li>
                    <li>• 支持格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、JPG、PNG、GIF</li>
                    <li>• 单次最多上传 20 个文件</li>
                </ul>
            </div>

            <input type="file" id="fileInput" multiple style="display: none;">
        </div>

        <!-- 上传统计 -->
        <div class="upload-summary">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>上传统计</span>
            </div>

            <div class="summary-stats">
                <div class="summary-stat">
                    <div class="stat-number total" id="totalFiles">0</div>
                    <div class="stat-label">总文件数</div>
                </div>
                <div class="summary-stat">
                    <div class="stat-number success" id="successFiles">0</div>
                    <div class="stat-label">上传成功</div>
                </div>
                <div class="summary-stat">
                    <div class="stat-number error" id="errorFiles">0</div>
                    <div class="stat-label">上传失败</div>
                </div>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="file-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span>文件列表</span>
                </div>
                <div class="list-actions">
                    <button class="btn-clear" onclick="clearAllFiles();" id="clearBtn" style="display: none;">
                        <i class="ace-icon fa fa-trash"></i>
                        <span>清空</span>
                    </button>
                    <button class="btn-upload" onclick="uploadAllFiles();" id="uploadBtn" style="display: none;">
                        <i class="ace-icon fa fa-upload"></i>
                        <span>上传</span>
                    </button>
                </div>
            </div>

            <div id="fileItems">
                <!-- 文件列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState">
            <i class="ace-icon fa fa-cloud-upload"></i>
            <div>请选择要上传的文件</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>上传中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let fileList = [];
        let uploadQueue = [];
        let isUploading = false;
        let uploadStats = {
            total: 0,
            success: 0,
            error: 0
        };

        $(function() {
            initPage();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            updateStats();
        }

        // 绑定事件
        function bindEvents() {
            // 文件选择事件
            $('#fileInput').change(function() {
                handleFileSelect(this.files);
            });

            // 拖拽事件
            const uploadZone = document.getElementById('uploadZone');

            uploadZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            uploadZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            uploadZone.addEventListener('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                handleFileSelect(e.dataTransfer.files);
            });
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件选择
        function handleFileSelect(files) {
            if (files.length === 0) return;

            // 检查文件数量限制
            if (fileList.length + files.length > 20) {
                showError('单次最多上传20个文件');
                return;
            }

            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // 检查文件大小
                if (file.size > 10 * 1024 * 1024) {
                    showError(`文件"${file.name}"超过10MB限制`);
                    continue;
                }

                // 检查文件类型
                if (!isValidFileType(file)) {
                    showError(`文件"${file.name}"格式不支持`);
                    continue;
                }

                // 检查重复文件
                if (fileList.some(f => f.name === file.name && f.size === file.size)) {
                    showError(`文件"${file.name}"已存在`);
                    continue;
                }

                // 添加到文件列表
                const fileItem = {
                    id: generateFileId(),
                    file: file,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    status: 'waiting',
                    progress: 0,
                    error: null
                };

                fileList.push(fileItem);
            }

            renderFileList();
            updateStats();
            updateButtons();
        }

        // 验证文件类型
        function isValidFileType(file) {
            const allowedTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'image/jpeg',
                'image/png',
                'image/gif'
            ];

            return allowedTypes.includes(file.type);
        }

        // 生成文件ID
        function generateFileId() {
            return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 渲染文件列表
        function renderFileList() {
            const container = $('#fileItems');
            container.empty();

            if (fileList.length === 0) {
                $('#emptyState').show();
                $('.file-list').hide();
                return;
            } else {
                $('#emptyState').hide();
                $('.file-list').show();
            }

            fileList.forEach(fileItem => {
                const fileHtml = createFileItem(fileItem);
                container.append(fileHtml);
            });
        }

        // 创建文件项
        function createFileItem(fileItem) {
            const fileType = getFileType(fileItem.type);
            const statusClass = getStatusClass(fileItem.status);
            const statusText = getStatusText(fileItem.status);

            return `
                <div class="file-item ${fileItem.status}" id="file-${fileItem.id}">
                    <div class="file-icon ${fileType}">
                        <i class="ace-icon fa ${getFileIcon(fileItem.type)}"></i>
                    </div>
                    <div class="file-info">
                        <div class="file-name" title="${fileItem.name}">${fileItem.name}</div>
                        <div class="file-meta">
                            <span class="file-size">${formatFileSize(fileItem.size)}</span>
                            <span class="file-status ${statusClass}">${statusText}</span>
                        </div>
                        ${fileItem.status === 'uploading' ? `
                            <div class="file-progress">
                                <div class="progress-bar" style="width: ${fileItem.progress}%"></div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="file-actions">
                        ${fileItem.status === 'error' ? `
                            <button class="btn-retry" onclick="retryUpload('${fileItem.id}');" title="重试">
                                <i class="ace-icon fa fa-refresh"></i>
                            </button>
                        ` : ''}
                        ${fileItem.status !== 'uploading' ? `
                            <button class="btn-delete" onclick="removeFile('${fileItem.id}');" title="删除">
                                <i class="ace-icon fa fa-times"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 获取文件类型
        function getFileType(mimeType) {
            if (mimeType === 'application/pdf') {
                return 'pdf';
            } else if (mimeType.startsWith('image/')) {
                return 'image';
            } else if (mimeType.includes('word') || mimeType.includes('excel') || mimeType.includes('powerpoint')) {
                return 'document';
            } else {
                return 'other';
            }
        }

        // 获取文件图标
        function getFileIcon(mimeType) {
            if (mimeType === 'application/pdf') {
                return 'fa-file-pdf-o';
            } else if (mimeType.startsWith('image/')) {
                return 'fa-file-image-o';
            } else if (mimeType.includes('word')) {
                return 'fa-file-word-o';
            } else if (mimeType.includes('excel')) {
                return 'fa-file-excel-o';
            } else if (mimeType.includes('powerpoint')) {
                return 'fa-file-powerpoint-o';
            } else {
                return 'fa-file-o';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'waiting': return '等待上传';
                case 'uploading': return '上传中';
                case 'success': return '上传成功';
                case 'error': return '上传失败';
                default: return '未知';
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 移除文件
        function removeFile(fileId) {
            const index = fileList.findIndex(f => f.id === fileId);
            if (index !== -1) {
                fileList.splice(index, 1);
                renderFileList();
                updateStats();
                updateButtons();
            }
        }

        // 重试上传
        function retryUpload(fileId) {
            const fileItem = fileList.find(f => f.id === fileId);
            if (fileItem) {
                fileItem.status = 'waiting';
                fileItem.progress = 0;
                fileItem.error = null;
                renderFileList();
                updateStats();
            }
        }

        // 清空所有文件
        function clearAllFiles() {
            if (isUploading) {
                showError('正在上传中，无法清空文件列表');
                return;
            }

            if (fileList.length === 0) return;

            const message = '确定要清空所有文件吗？';

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doClearAllFiles();
                    }
                });
            } else {
                if (confirm(message)) {
                    doClearAllFiles();
                }
            }
        }

        // 执行清空所有文件
        function doClearAllFiles() {
            fileList = [];
            uploadQueue = [];
            renderFileList();
            updateStats();
            updateButtons();
        }

        // 上传所有文件
        function uploadAllFiles() {
            const category = $('#fileCategory').val();
            if (!category) {
                showError('请先选择文件分类');
                return;
            }

            const waitingFiles = fileList.filter(f => f.status === 'waiting');
            if (waitingFiles.length === 0) {
                showError('没有需要上传的文件');
                return;
            }

            uploadQueue = [...waitingFiles];
            isUploading = true;
            updateButtons();

            processUploadQueue();
        }

        // 处理上传队列
        function processUploadQueue() {
            if (uploadQueue.length === 0) {
                isUploading = false;
                updateButtons();
                showSuccess('所有文件上传完成');
                return;
            }

            const fileItem = uploadQueue.shift();
            uploadFile(fileItem);
        }

        // 上传单个文件
        function uploadFile(fileItem) {
            fileItem.status = 'uploading';
            fileItem.progress = 0;
            renderFileList();
            updateStats();

            const formData = new FormData();
            formData.append('file', fileItem.file);
            formData.append('category', $('#fileCategory').val());
            formData.append('fileName', fileItem.name);

            $.ajax({
                url: '/student/fileUpLoad/upload',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            const percentComplete = (e.loaded / e.total) * 100;
                            fileItem.progress = Math.round(percentComplete);
                            updateFileProgress(fileItem.id, fileItem.progress);
                        }
                    }, false);
                    return xhr;
                },
                success: function(data) {
                    if (data.success) {
                        fileItem.status = 'success';
                        fileItem.progress = 100;
                    } else {
                        fileItem.status = 'error';
                        fileItem.error = data.message || '上传失败';
                    }
                    renderFileList();
                    updateStats();

                    // 继续处理下一个文件
                    setTimeout(() => {
                        processUploadQueue();
                    }, 500);
                },
                error: function() {
                    fileItem.status = 'error';
                    fileItem.error = '网络错误';
                    renderFileList();
                    updateStats();

                    // 继续处理下一个文件
                    setTimeout(() => {
                        processUploadQueue();
                    }, 500);
                }
            });
        }

        // 更新文件进度
        function updateFileProgress(fileId, progress) {
            $(`#file-${fileId} .progress-bar`).css('width', progress + '%');
        }

        // 更新统计信息
        function updateStats() {
            uploadStats.total = fileList.length;
            uploadStats.success = fileList.filter(f => f.status === 'success').length;
            uploadStats.error = fileList.filter(f => f.status === 'error').length;

            $('#totalFiles').text(uploadStats.total);
            $('#successFiles').text(uploadStats.success);
            $('#errorFiles').text(uploadStats.error);
        }

        // 更新按钮状态
        function updateButtons() {
            const hasFiles = fileList.length > 0;
            const hasWaitingFiles = fileList.some(f => f.status === 'waiting');

            if (hasFiles) {
                $('#clearBtn').show();
            } else {
                $('#clearBtn').hide();
            }

            if (hasWaitingFiles && !isUploading) {
                $('#uploadBtn').show();
            } else {
                $('#uploadBtn').hide();
            }
        }

        // 刷新数据
        function refreshData() {
            // 重置所有状态
            fileList = [];
            uploadQueue = [];
            isUploading = false;
            renderFileList();
            updateStats();
            updateButtons();
            $('#fileCategory').val('');
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
