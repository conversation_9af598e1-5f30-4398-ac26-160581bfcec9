<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>可信成绩单</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 可信成绩单页面样式 */
        .report-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .report-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .summary-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .summary-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .summary-number.gpa {
            color: var(--primary-color);
        }
        
        .summary-number.credits {
            color: var(--success-color);
        }
        
        .summary-number.courses {
            color: var(--info-color);
        }
        
        .summary-number.rank {
            color: var(--warning-color);
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .report-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .actions-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .action-btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .action-btn:active {
            background: var(--bg-color-active);
        }
        
        .action-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .action-btn.success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }
        
        .action-btn.info {
            background: var(--info-color);
            color: white;
            border-color: var(--info-color);
        }
        
        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-base);
        }
        
        .action-icon.primary {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .action-icon.success {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .action-icon.info {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .action-icon.default {
            background: var(--primary-color);
            color: white;
        }
        
        .action-content {
            flex: 1;
        }
        
        .action-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .action-desc {
            font-size: var(--font-size-small);
            opacity: 0.8;
        }
        
        .action-arrow {
            color: rgba(255, 255, 255, 0.6);
            font-size: var(--font-size-base);
        }
        
        .action-arrow.default {
            color: var(--text-disabled);
        }
        
        .report-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-item.completed {
            border-left: 4px solid var(--success-color);
        }
        
        .history-item.processing {
            border-left: 4px solid var(--warning-color);
        }
        
        .history-item.failed {
            border-left: 4px solid var(--error-color);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-processing {
            background: var(--warning-color);
            color: white;
        }
        
        .status-failed {
            background: var(--error-color);
            color: white;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .history-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-download {
            background: var(--success-color);
            color: white;
        }
        
        .btn-verify {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .application-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .application-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .form-checkbox input {
            margin-right: var(--margin-xs);
        }
        
        .form-checkbox label {
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">可信成绩单</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="report-header">
            <div class="header-title">可信成绩单</div>
            <div class="header-subtitle">申请和查看可信成绩单</div>
        </div>

        <!-- 成绩汇总 -->
        <div class="report-summary">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>成绩汇总</span>
            </div>

            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-number gpa" id="gpaValue">0.00</div>
                    <div class="summary-label">平均绩点</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number credits" id="creditsValue">0</div>
                    <div class="summary-label">总学分</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number courses" id="coursesValue">0</div>
                    <div class="summary-label">课程数</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number rank" id="rankValue">-</div>
                    <div class="summary-label">专业排名</div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="report-actions">
            <div class="actions-title">
                <i class="ace-icon fa fa-cogs"></i>
                <span>操作功能</span>
            </div>

            <div class="action-buttons">
                <div class="action-btn primary" onclick="showApplicationForm('official')">
                    <div class="action-icon primary">
                        <i class="ace-icon fa fa-certificate"></i>
                    </div>
                    <div class="action-content">
                        <div class="action-name">申请官方成绩单</div>
                        <div class="action-desc">申请带有学校公章的官方成绩单</div>
                    </div>
                    <div class="action-arrow">
                        <i class="ace-icon fa fa-chevron-right"></i>
                    </div>
                </div>

                <div class="action-btn success" onclick="showApplicationForm('electronic')">
                    <div class="action-icon success">
                        <i class="ace-icon fa fa-file-pdf-o"></i>
                    </div>
                    <div class="action-content">
                        <div class="action-name">申请电子成绩单</div>
                        <div class="action-desc">申请可验证的电子版成绩单</div>
                    </div>
                    <div class="action-arrow">
                        <i class="ace-icon fa fa-chevron-right"></i>
                    </div>
                </div>

                <div class="action-btn info" onclick="showApplicationForm('english')">
                    <div class="action-icon info">
                        <i class="ace-icon fa fa-globe"></i>
                    </div>
                    <div class="action-content">
                        <div class="action-name">申请英文成绩单</div>
                        <div class="action-desc">申请英文版本的成绩单</div>
                    </div>
                    <div class="action-arrow">
                        <i class="ace-icon fa fa-chevron-right"></i>
                    </div>
                </div>

                <div class="action-btn" onclick="verifyReportCard()">
                    <div class="action-icon default">
                        <i class="ace-icon fa fa-shield"></i>
                    </div>
                    <div class="action-content">
                        <div class="action-name">验证成绩单</div>
                        <div class="action-desc">验证成绩单的真实性</div>
                    </div>
                    <div class="action-arrow default">
                        <i class="ace-icon fa fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申请历史 -->
        <div class="report-history">
            <div class="history-header">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>申请历史</span>
                </div>
                <div class="history-count" id="historyCount">0</div>
            </div>

            <div id="historyItems">
                <!-- 申请历史将动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 申请表单 -->
    <div class="application-form" id="applicationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeApplicationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">成绩单申请</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">专业</div>
                    <input type="text" class="form-input" id="major" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">班级</div>
                    <input type="text" class="form-input" id="className" disabled>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">成绩单类型</div>
                    <select class="form-input" id="reportType">
                        <option value="">请选择成绩单类型</option>
                        <option value="official">官方成绩单</option>
                        <option value="electronic">电子成绩单</option>
                        <option value="english">英文成绩单</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">申请用途</div>
                    <input type="text" class="form-input" id="purpose" placeholder="请输入申请用途">
                </div>

                <div class="form-group">
                    <div class="form-label">申请份数</div>
                    <select class="form-input" id="copies">
                        <option value="1">1份</option>
                        <option value="2">2份</option>
                        <option value="3">3份</option>
                        <option value="4">4份</option>
                        <option value="5">5份</option>
                    </select>
                </div>

                <div class="form-group" id="languageGroup" style="display: none;">
                    <div class="form-label">语言版本</div>
                    <div class="form-checkbox">
                        <input type="checkbox" id="chineseLang" checked>
                        <label for="chineseLang">中文版</label>
                    </div>
                    <div class="form-checkbox">
                        <input type="checkbox" id="englishLang">
                        <label for="englishLang">英文版</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮寄地址</div>
                    <textarea class="form-input form-textarea" id="address" placeholder="请输入邮寄地址（选填）"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">备注说明</div>
                    <textarea class="form-input form-textarea" id="note" placeholder="其他需要说明的情况（选填）"></textarea>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeApplicationForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let summaryData = {};
        let applicationHistory = [];
        let currentApplicationType = '';

        $(function() {
            initPage();
            loadSummaryData();
            loadApplicationHistory();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 成绩单类型变化事件
            $('#reportType').change(function() {
                const type = $(this).val();
                toggleLanguageGroup(type);
            });
        }

        // 切换语言选项组
        function toggleLanguageGroup(type) {
            if (type === 'english') {
                $('#languageGroup').show();
                $('#englishLang').prop('checked', true);
                $('#chineseLang').prop('checked', false);
            } else {
                $('#languageGroup').hide();
                $('#chineseLang').prop('checked', true);
                $('#englishLang').prop('checked', false);
            }
        }

        // 加载汇总数据
        function loadSummaryData() {
            showLoading(true);

            $.ajax({
                url: "/student/credibleReportCard/getSummaryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    summaryData = data.summary || {};
                    renderSummaryData();
                    showLoading(false);
                },
                error: function() {
                    showError('加载成绩汇总失败');
                    showLoading(false);
                }
            });
        }

        // 渲染汇总数据
        function renderSummaryData() {
            $('#gpaValue').text(summaryData.gpa || '0.00');
            $('#creditsValue').text(summaryData.totalCredits || '0');
            $('#coursesValue').text(summaryData.courseCount || '0');
            $('#rankValue').text(summaryData.rank || '-');
        }

        // 加载申请历史
        function loadApplicationHistory() {
            $.ajax({
                url: "/student/credibleReportCard/getApplicationHistory",
                type: "post",
                dataType: "json",
                success: function(data) {
                    applicationHistory = data.history || [];
                    renderApplicationHistory();
                },
                error: function() {
                    console.log('加载申请历史失败');
                }
            });
        }

        // 渲染申请历史
        function renderApplicationHistory() {
            $('#historyCount').text(applicationHistory.length);

            const container = $('#historyItems');
            container.empty();

            if (applicationHistory.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无申请记录
                    </div>
                `);
                return;
            }

            applicationHistory.forEach(item => {
                const historyHtml = createHistoryItem(item);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(item) {
            const status = item.status || 'processing';
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);

            return `
                <div class="history-item ${status}" onclick="showHistoryDetail('${item.id}')">
                    <div class="history-basic">
                        <div class="history-type">${getReportTypeText(item.type)}</div>
                        <div class="history-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(item.applyTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>申请用途:</span>
                            <span>${item.purpose || '-'}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>申请份数:</span>
                            <span>${item.copies}份</span>
                        </div>
                        <div class="history-detail-item">
                            <span>处理时间:</span>
                            <span>${formatDate(item.processTime)}</span>
                        </div>
                    </div>
                    <div class="history-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showHistoryDetail('${item.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'completed' ? `
                            <button class="btn-mobile btn-download" onclick="event.stopPropagation(); downloadReportCard('${item.id}');">
                                <i class="ace-icon fa fa-download"></i>
                                <span>下载</span>
                            </button>
                            <button class="btn-mobile btn-verify" onclick="event.stopPropagation(); verifyReportCard('${item.id}');">
                                <i class="ace-icon fa fa-shield"></i>
                                <span>验证</span>
                            </button>
                        ` : status === 'failed' ? `
                            <button class="btn-mobile btn-view" onclick="event.stopPropagation(); reapplyReportCard('${item.id}');">
                                <i class="ace-icon fa fa-refresh"></i>
                                <span>重新申请</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-clock-o"></i>
                                <span>处理中</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'completed': return '已完成';
                case 'processing': return '处理中';
                case 'failed': return '失败';
                default: return '未知';
            }
        }

        // 获取成绩单类型文本
        function getReportTypeText(type) {
            switch(type) {
                case 'official': return '官方成绩单';
                case 'electronic': return '电子成绩单';
                case 'english': return '英文成绩单';
                default: return '其他成绩单';
            }
        }

        // 显示申请表单
        function showApplicationForm(type) {
            currentApplicationType = type;

            // 设置表单标题
            $('#formTitle').text(`${getReportTypeText(type)}申请`);

            // 设置成绩单类型
            $('#reportType').val(type);

            // 切换语言选项
            toggleLanguageGroup(type);

            // 填充基本信息
            fillBasicInfo();

            // 显示表单
            $('#applicationForm').addClass('show');
        }

        // 填充基本信息
        function fillBasicInfo() {
            // 这里应该从用户信息中获取，暂时使用模拟数据
            $('#studentId').val('2021001001');
            $('#studentName').val('张三');
            $('#major').val('计算机科学与技术');
            $('#className').val('计科2021-1班');
        }

        // 关闭申请表单
        function closeApplicationForm() {
            $('#applicationForm').removeClass('show');
            clearForm();
        }

        // 清空表单
        function clearForm() {
            $('#reportType').val('');
            $('#purpose').val('');
            $('#copies').val('1');
            $('#chineseLang').prop('checked', true);
            $('#englishLang').prop('checked', false);
            $('#phone').val('');
            $('#address').val('');
            $('#note').val('');
            $('#languageGroup').hide();
        }

        // 提交申请
        function submitApplication() {
            const formData = {
                type: $('#reportType').val(),
                purpose: $('#purpose').val().trim(),
                copies: $('#copies').val(),
                chineseLang: $('#chineseLang').is(':checked'),
                englishLang: $('#englishLang').is(':checked'),
                phone: $('#phone').val().trim(),
                address: $('#address').val().trim(),
                note: $('#note').val().trim()
            };

            // 验证表单
            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要申请${getReportTypeText(formData.type)}吗？\n\n申请用途：${formData.purpose}\n申请份数：${formData.copies}份`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitApplication(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitApplication(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.type) {
                showError('请选择成绩单类型');
                return false;
            }

            if (!formData.purpose) {
                showError('请填写申请用途');
                return false;
            }

            if (!formData.phone) {
                showError('请填写联系电话');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            if (!formData.chineseLang && !formData.englishLang) {
                showError('请至少选择一种语言版本');
                return false;
            }

            return true;
        }

        // 执行提交申请
        function doSubmitApplication(formData) {
            $.ajax({
                url: "/student/credibleReportCard/submitApplication",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('申请提交成功，请等待处理');
                        closeApplicationForm();
                        loadApplicationHistory(); // 重新加载申请历史
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示历史详情
        function showHistoryDetail(historyId) {
            const item = applicationHistory.find(h => h.id === historyId);
            if (!item) return;

            let message = `申请详情\n\n`;
            message += `成绩单类型：${getReportTypeText(item.type)}\n`;
            message += `申请状态：${getStatusText(item.status)}\n`;
            message += `申请时间：${formatDate(item.applyTime)}\n`;
            message += `申请用途：${item.purpose}\n`;
            message += `申请份数：${item.copies}份\n`;
            message += `联系电话：${item.phone}\n`;

            if (item.address) {
                message += `邮寄地址：${item.address}\n`;
            }

            if (item.processTime) {
                message += `处理时间：${formatDate(item.processTime)}\n`;
            }

            if (item.processComment) {
                message += `处理意见：${item.processComment}\n`;
            }

            if (item.note) {
                message += `备注：${item.note}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 下载成绩单
        function downloadReportCard(historyId) {
            const item = applicationHistory.find(h => h.id === historyId);
            if (!item) return;

            // 创建下载链接
            const link = document.createElement('a');
            link.href = `/student/credibleReportCard/download?id=${historyId}`;
            link.download = `${getReportTypeText(item.type)}.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showSuccess('成绩单下载成功');
        }

        // 验证成绩单
        function verifyReportCard(historyId) {
            if (historyId) {
                // 验证特定成绩单
                const item = applicationHistory.find(h => h.id === historyId);
                if (!item) return;

                showSuccess(`${getReportTypeText(item.type)}验证通过，该成绩单真实有效`);
            } else {
                // 通用验证功能
                const verifyCode = prompt('请输入成绩单验证码：');
                if (verifyCode) {
                    // 这里应该调用验证接口
                    showSuccess('验证通过，该成绩单真实有效');
                }
            }
        }

        // 重新申请
        function reapplyReportCard(historyId) {
            const item = applicationHistory.find(h => h.id === historyId);
            if (!item) return;

            const message = `确定要重新申请${getReportTypeText(item.type)}吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        showApplicationForm(item.type);
                    }
                });
            } else {
                if (confirm(message)) {
                    showApplicationForm(item.type);
                }
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadSummaryData();
            loadApplicationHistory();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
