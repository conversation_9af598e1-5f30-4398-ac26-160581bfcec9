<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证充值</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证充值页面样式 */
        .recharge-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .balance-card {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 12px;
            padding: var(--padding-lg);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }
        
        .card-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--success-light), var(--success-color));
            opacity: 0.1;
            border-radius: 12px;
        }
        
        .card-content {
            position: relative;
            z-index: 1;
            text-align: center;
        }
        
        .balance-title {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .balance-amount {
            font-size: var(--font-size-h1);
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: var(--margin-sm);
        }
        
        .balance-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .recharge-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .form-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .amount-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--margin-lg);
        }
        
        .amount-option {
            background: var(--bg-tertiary);
            border: 2px solid var(--border-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            cursor: pointer;
            transition: all var(--transition-base);
            text-align: center;
        }
        
        .amount-option:active {
            background: var(--bg-color-active);
        }
        
        .amount-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .option-amount {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .option-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .custom-amount {
            margin-bottom: var(--margin-lg);
        }
        
        .amount-input {
            width: 100%;
            min-height: 50px;
            padding: 12px 16px;
            border: 2px solid var(--border-primary);
            border-radius: 8px;
            font-size: var(--font-size-h4);
            font-weight: 500;
            text-align: center;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .amount-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .amount-input::placeholder {
            color: var(--text-disabled);
        }
        
        .payment-methods {
            margin-bottom: var(--margin-lg);
        }
        
        .methods-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
        }
        
        .method-options {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .method-option {
            background: var(--bg-tertiary);
            border: 2px solid var(--border-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .method-option:active {
            background: var(--bg-color-active);
        }
        
        .method-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .method-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-base);
            color: white;
        }
        
        .method-icon.wechat {
            background: #1aad19;
        }
        
        .method-icon.alipay {
            background: #1677ff;
        }
        
        .method-icon.bank {
            background: #fa8c16;
        }
        
        .method-info {
            flex: 1;
        }
        
        .method-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .method-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .recharge-summary {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-lg);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--margin-sm);
            font-size: var(--font-size-small);
        }
        
        .summary-item:last-child {
            margin-bottom: 0;
            font-size: var(--font-size-base);
            font-weight: 500;
            padding-top: var(--padding-sm);
            border-top: 1px solid var(--divider-color);
        }
        
        .summary-label {
            color: var(--text-secondary);
        }
        
        .summary-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .recharge-actions {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-recharge {
            background: var(--success-color);
            color: white;
        }
        
        .btn-reset {
            background: var(--text-disabled);
            color: white;
        }
        
        .recharge-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-toggle {
            color: var(--text-secondary);
            transition: transform var(--transition-base);
        }
        
        .history-toggle.expanded {
            transform: rotate(180deg);
        }
        
        .history-content {
            display: none;
        }
        
        .history-content.show {
            display: block;
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-item.success {
            border-left: 4px solid var(--success-color);
        }
        
        .history-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .history-item.failed {
            border-left: 4px solid var(--error-color);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-amount {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-success {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-failed {
            background: var(--error-color);
            color: white;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证充值</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="recharge-header">
            <div class="header-title">学生证充值</div>
            <div class="header-subtitle">为学生证账户充值</div>
        </div>

        <!-- 余额卡片 -->
        <div class="balance-card">
            <div class="card-bg"></div>
            <div class="card-content">
                <div class="balance-title">当前余额</div>
                <div class="balance-amount" id="currentBalance">¥0.00</div>
                <div class="balance-meta">
                    <span>卡号: <span id="cardNumber">2021001001</span></span>
                    <span>更新时间: <span id="updateTime">--</span></span>
                </div>
            </div>
        </div>

        <!-- 充值表单 -->
        <div class="recharge-form">
            <div class="form-title">
                <i class="ace-icon fa fa-credit-card"></i>
                <span>选择充值金额</span>
            </div>

            <!-- 快捷金额选择 -->
            <div class="amount-options">
                <div class="amount-option" data-amount="10" onclick="selectAmount(10)">
                    <div class="option-amount">¥10</div>
                    <div class="option-label">餐费</div>
                </div>
                <div class="amount-option" data-amount="20" onclick="selectAmount(20)">
                    <div class="option-amount">¥20</div>
                    <div class="option-label">日常</div>
                </div>
                <div class="amount-option" data-amount="50" onclick="selectAmount(50)">
                    <div class="option-amount">¥50</div>
                    <div class="option-label">周用</div>
                </div>
                <div class="amount-option" data-amount="100" onclick="selectAmount(100)">
                    <div class="option-amount">¥100</div>
                    <div class="option-label">月用</div>
                </div>
                <div class="amount-option" data-amount="200" onclick="selectAmount(200)">
                    <div class="option-amount">¥200</div>
                    <div class="option-label">学期</div>
                </div>
                <div class="amount-option" data-amount="500" onclick="selectAmount(500)">
                    <div class="option-amount">¥500</div>
                    <div class="option-label">年用</div>
                </div>
            </div>

            <!-- 自定义金额 -->
            <div class="custom-amount">
                <input type="number" class="amount-input" id="customAmount" placeholder="或输入自定义金额" min="1" max="1000" step="0.01" oninput="selectCustomAmount()">
            </div>

            <!-- 支付方式 -->
            <div class="payment-methods">
                <div class="methods-title">选择支付方式</div>
                <div class="method-options">
                    <div class="method-option" data-method="wechat" onclick="selectPaymentMethod('wechat')">
                        <div class="method-icon wechat">
                            <i class="ace-icon fa fa-wechat"></i>
                        </div>
                        <div class="method-info">
                            <div class="method-name">微信支付</div>
                            <div class="method-desc">使用微信扫码支付</div>
                        </div>
                    </div>
                    <div class="method-option" data-method="alipay" onclick="selectPaymentMethod('alipay')">
                        <div class="method-icon alipay">
                            <i class="ace-icon fa fa-alipay"></i>
                        </div>
                        <div class="method-info">
                            <div class="method-name">支付宝</div>
                            <div class="method-desc">使用支付宝扫码支付</div>
                        </div>
                    </div>
                    <div class="method-option" data-method="bank" onclick="selectPaymentMethod('bank')">
                        <div class="method-icon bank">
                            <i class="ace-icon fa fa-bank"></i>
                        </div>
                        <div class="method-info">
                            <div class="method-name">银行卡</div>
                            <div class="method-desc">使用绑定的银行卡支付</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 充值汇总 -->
            <div class="recharge-summary">
                <div class="summary-title">充值汇总</div>
                <div class="summary-item">
                    <span class="summary-label">充值金额:</span>
                    <span class="summary-value" id="summaryAmount">¥0.00</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">手续费:</span>
                    <span class="summary-value" id="summaryFee">¥0.00</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">实付金额:</span>
                    <span class="summary-value" id="summaryTotal">¥0.00</span>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="recharge-actions">
                <button class="btn-mobile btn-reset flex-1" onclick="resetForm();">重置</button>
                <button class="btn-mobile btn-recharge flex-1" onclick="submitRecharge();">立即充值</button>
            </div>
        </div>

        <!-- 充值记录 -->
        <div class="recharge-history">
            <div class="history-header" onclick="toggleHistory();">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>充值记录</span>
                </div>
                <div class="history-toggle" id="historyToggle">
                    <i class="ace-icon fa fa-chevron-down"></i>
                </div>
            </div>

            <div class="history-content" id="historyContent">
                <div id="historyItems">
                    <!-- 充值记录将动态填充 -->
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let balanceInfo = {};
        let historyData = [];
        let selectedAmount = 0;
        let selectedPaymentMethod = '';

        $(function() {
            initPage();
            loadBalanceInfo();
            loadHistoryData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载余额信息
        function loadBalanceInfo() {
            showLoading(true);

            $.ajax({
                url: "/student/studentCardRecharge/getBalanceInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    balanceInfo = data.balance || {};
                    renderBalanceInfo();
                    showLoading(false);
                },
                error: function() {
                    // 使用模拟数据
                    balanceInfo = {
                        balance: 156.78,
                        cardNumber: '2021001001',
                        updateTime: new Date().toLocaleString()
                    };
                    renderBalanceInfo();
                    showLoading(false);
                }
            });
        }

        // 渲染余额信息
        function renderBalanceInfo() {
            $('#currentBalance').text('¥' + (balanceInfo.balance || 0).toFixed(2));
            $('#cardNumber').text(balanceInfo.cardNumber || '2021001001');
            $('#updateTime').text(balanceInfo.updateTime || new Date().toLocaleString());
        }

        // 选择金额
        function selectAmount(amount) {
            selectedAmount = amount;

            // 更新UI状态
            $('.amount-option').removeClass('selected');
            $(`.amount-option[data-amount="${amount}"]`).addClass('selected');

            // 清空自定义金额
            $('#customAmount').val('');

            updateSummary();
        }

        // 选择自定义金额
        function selectCustomAmount() {
            const customAmount = parseFloat($('#customAmount').val()) || 0;

            if (customAmount > 0) {
                selectedAmount = customAmount;

                // 清除快捷选择
                $('.amount-option').removeClass('selected');

                updateSummary();
            } else {
                selectedAmount = 0;
                updateSummary();
            }
        }

        // 选择支付方式
        function selectPaymentMethod(method) {
            selectedPaymentMethod = method;

            // 更新UI状态
            $('.method-option').removeClass('selected');
            $(`.method-option[data-method="${method}"]`).addClass('selected');

            updateSummary();
        }

        // 更新汇总信息
        function updateSummary() {
            const amount = selectedAmount;
            const fee = calculateFee(amount, selectedPaymentMethod);
            const total = amount + fee;

            $('#summaryAmount').text('¥' + amount.toFixed(2));
            $('#summaryFee').text('¥' + fee.toFixed(2));
            $('#summaryTotal').text('¥' + total.toFixed(2));
        }

        // 计算手续费
        function calculateFee(amount, method) {
            if (amount <= 0) return 0;

            // 不同支付方式的手续费率
            const feeRates = {
                'wechat': 0.001,  // 0.1%
                'alipay': 0.001,  // 0.1%
                'bank': 0.002     // 0.2%
            };

            const rate = feeRates[method] || 0;
            return Math.max(amount * rate, 0); // 最低手续费0元
        }

        // 重置表单
        function resetForm() {
            selectedAmount = 0;
            selectedPaymentMethod = '';

            $('.amount-option').removeClass('selected');
            $('.method-option').removeClass('selected');
            $('#customAmount').val('');

            updateSummary();
        }

        // 提交充值
        function submitRecharge() {
            if (!validateRecharge()) {
                return;
            }

            const amount = selectedAmount;
            const fee = calculateFee(amount, selectedPaymentMethod);
            const total = amount + fee;

            const message = `确定要充值 ¥${amount.toFixed(2)} 吗？\n\n手续费：¥${fee.toFixed(2)}\n实付金额：¥${total.toFixed(2)}`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitRecharge();
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitRecharge();
                }
            }
        }

        // 验证充值信息
        function validateRecharge() {
            if (selectedAmount <= 0) {
                showError('请选择充值金额');
                return false;
            }

            if (selectedAmount > 1000) {
                showError('单次充值金额不能超过1000元');
                return false;
            }

            if (!selectedPaymentMethod) {
                showError('请选择支付方式');
                return false;
            }

            return true;
        }

        // 执行提交充值
        function doSubmitRecharge() {
            const rechargeData = {
                amount: selectedAmount,
                paymentMethod: selectedPaymentMethod,
                fee: calculateFee(selectedAmount, selectedPaymentMethod)
            };

            $.ajax({
                url: "/student/studentCardRecharge/submitRecharge",
                type: "post",
                data: rechargeData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        if (data.paymentUrl) {
                            // 跳转到支付页面
                            window.location.href = data.paymentUrl;
                        } else {
                            showSuccess('充值申请提交成功，请等待处理');
                            resetForm();
                            loadBalanceInfo(); // 重新加载余额
                            loadHistoryData(); // 重新加载历史记录
                        }
                    } else {
                        showError(data.message || '充值申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 切换历史记录显示
        function toggleHistory() {
            const content = $('#historyContent');
            const toggle = $('#historyToggle');

            if (content.hasClass('show')) {
                content.removeClass('show');
                toggle.removeClass('expanded');
            } else {
                content.addClass('show');
                toggle.addClass('expanded');

                // 如果还没有加载历史数据，则加载
                if (historyData.length === 0) {
                    loadHistoryData();
                }
            }
        }

        // 加载历史数据
        function loadHistoryData() {
            $.ajax({
                url: "/student/studentCardRecharge/getHistoryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    historyData = data.history || [];
                    renderHistoryData();
                },
                error: function() {
                    // 使用模拟数据
                    historyData = [
                        {
                            id: '1',
                            amount: 50.00,
                            fee: 0.05,
                            paymentMethod: 'wechat',
                            status: 'success',
                            createTime: '2024-01-15 10:30:00',
                            completeTime: '2024-01-15 10:31:00'
                        },
                        {
                            id: '2',
                            amount: 100.00,
                            fee: 0.10,
                            paymentMethod: 'alipay',
                            status: 'pending',
                            createTime: '2024-01-20 14:20:00',
                            completeTime: null
                        }
                    ];
                    renderHistoryData();
                }
            });
        }

        // 渲染历史数据
        function renderHistoryData() {
            const container = $('#historyItems');
            container.empty();

            if (historyData.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无充值记录
                    </div>
                `);
                return;
            }

            historyData.forEach(item => {
                const historyHtml = createHistoryItem(item);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(item) {
            const status = item.status || 'pending';
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);
            const methodText = getPaymentMethodText(item.paymentMethod);

            return `
                <div class="history-item ${status}" onclick="showHistoryDetail('${item.id}')">
                    <div class="history-basic">
                        <div class="history-amount">¥${item.amount.toFixed(2)}</div>
                        <div class="history-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>支付方式:</span>
                            <span>${methodText}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>手续费:</span>
                            <span>¥${item.fee.toFixed(2)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>创建时间:</span>
                            <span>${formatDateTime(item.createTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>完成时间:</span>
                            <span>${formatDateTime(item.completeTime)}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'success': return '成功';
                case 'pending': return '处理中';
                case 'failed': return '失败';
                default: return '未知';
            }
        }

        // 获取支付方式文本
        function getPaymentMethodText(method) {
            switch(method) {
                case 'wechat': return '微信支付';
                case 'alipay': return '支付宝';
                case 'bank': return '银行卡';
                default: return '其他';
            }
        }

        // 显示历史详情
        function showHistoryDetail(historyId) {
            const item = historyData.find(h => h.id === historyId);
            if (!item) return;

            let message = `充值详情\n\n`;
            message += `充值金额：¥${item.amount.toFixed(2)}\n`;
            message += `手续费：¥${item.fee.toFixed(2)}\n`;
            message += `实付金额：¥${(item.amount + item.fee).toFixed(2)}\n`;
            message += `支付方式：${getPaymentMethodText(item.paymentMethod)}\n`;
            message += `状态：${getStatusText(item.status)}\n`;
            message += `创建时间：${item.createTime}\n`;
            message += `完成时间：${item.completeTime || '-'}\n`;

            if (item.transactionId) {
                message += `交易号：${item.transactionId}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString();
        }

        // 刷新数据
        function refreshData() {
            loadBalanceInfo();
            if ($('#historyContent').hasClass('show')) {
                loadHistoryData();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
