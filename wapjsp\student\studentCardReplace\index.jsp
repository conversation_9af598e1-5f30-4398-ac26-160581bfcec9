<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证补办</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证补办页面样式 */
        .replace-header {
            background: linear-gradient(135deg, var(--warning-color), #ffa940);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .replace-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .status-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .status-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
        }
        
        .status-icon.available {
            background: var(--success-color);
        }
        
        .status-icon.processing {
            background: var(--warning-color);
        }
        
        .status-icon.unavailable {
            background: var(--error-color);
        }
        
        .status-content {
            flex: 1;
        }
        
        .status-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .status-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .replace-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .form-title i {
            margin-right: var(--margin-xs);
            color: var(--warning-color);
        }
        
        .form-section {
            margin-bottom: var(--margin-lg);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-xs);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .photo-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            background: var(--bg-tertiary);
        }
        
        .photo-upload:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .photo-preview {
            width: 120px;
            height: 150px;
            border-radius: 6px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-disabled);
            font-size: var(--font-size-h3);
            margin: 0 auto var(--margin-md);
            border: 2px solid var(--border-primary);
            overflow: hidden;
        }
        
        .upload-text {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            font-weight: 500;
        }
        
        .upload-hint {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .fee-info {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-lg);
        }
        
        .fee-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--info-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .fee-title i {
            margin-right: var(--margin-xs);
        }
        
        .fee-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .fee-item {
            font-size: var(--font-size-small);
            color: var(--info-color);
            margin-bottom: var(--margin-xs);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .fee-item:last-child {
            margin-bottom: 0;
            font-weight: 500;
            font-size: var(--font-size-base);
            padding-top: var(--padding-xs);
            border-top: 1px solid var(--info-color);
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
        }
        
        .btn-replace {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-reset {
            background: var(--text-disabled);
            color: white;
        }
        
        .replace-guide {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .guide-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .guide-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .guide-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .guide-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: var(--margin-md);
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .guide-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            margin-right: var(--margin-sm);
            flex-shrink: 0;
        }
        
        .guide-content {
            flex: 1;
        }
        
        .guide-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: var(--line-height-base);
        }
        
        .replace-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-toggle {
            color: var(--text-secondary);
            transition: transform var(--transition-base);
        }
        
        .history-toggle.expanded {
            transform: rotate(180deg);
        }
        
        .history-content {
            display: none;
        }
        
        .history-content.show {
            display: block;
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-item.completed {
            border-left: 4px solid var(--success-color);
        }
        
        .history-item.processing {
            border-left: 4px solid var(--warning-color);
        }
        
        .history-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-processing {
            background: var(--warning-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证补办</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="replace-header">
            <div class="header-title">学生证补办</div>
            <div class="header-subtitle">申请补办新的学生证</div>
        </div>

        <!-- 补办状态 -->
        <div class="replace-status">
            <div class="status-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>补办状态</span>
            </div>

            <div class="status-card" id="replaceStatusCard">
                <!-- 状态信息将动态填充 -->
            </div>
        </div>

        <!-- 补办表单 -->
        <div class="replace-form" id="replaceForm">
            <div class="form-title">
                <i class="ace-icon fa fa-credit-card"></i>
                <span>学生证补办申请</span>
            </div>

            <div class="form-section">
                <div class="section-title">基本信息</div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">专业</div>
                    <input type="text" class="form-input" id="major" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">班级</div>
                    <input type="text" class="form-input" id="className" disabled>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">补办信息</div>

                <div class="form-group">
                    <div class="form-label">补办原因</div>
                    <select class="form-input" id="replaceReason">
                        <option value="">请选择补办原因</option>
                        <option value="lost">学生证丢失</option>
                        <option value="stolen">学生证被盗</option>
                        <option value="damaged">学生证损坏</option>
                        <option value="expired">学生证过期</option>
                        <option value="other">其他原因</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">详细说明</div>
                    <textarea class="form-input" id="replaceDescription" placeholder="请详细说明补办原因" style="min-height: 80px; resize: vertical;"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮寄地址</div>
                    <textarea class="form-input" id="mailAddress" placeholder="请输入邮寄地址（选填）" style="min-height: 60px; resize: vertical;"></textarea>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">照片上传</div>

                <div class="form-group">
                    <div class="form-label">学生证照片</div>
                    <div class="photo-upload" onclick="selectPhoto();">
                        <div class="photo-preview" id="photoPreview">
                            <i class="ace-icon fa fa-camera"></i>
                        </div>
                        <div class="upload-text">点击上传照片</div>
                        <div class="upload-hint">
                            请上传近期免冠照片<br>
                            格式：JPG、PNG<br>
                            大小：不超过2MB<br>
                            尺寸：建议295×413像素
                        </div>
                    </div>
                    <input type="file" id="photoInput" accept="image/*" style="display: none;">
                </div>
            </div>

            <!-- 费用信息 -->
            <div class="fee-info">
                <div class="fee-title">
                    <i class="ace-icon fa fa-money"></i>
                    <span>费用说明</span>
                </div>
                <ul class="fee-list">
                    <li class="fee-item">
                        <span>工本费:</span>
                        <span>¥20.00</span>
                    </li>
                    <li class="fee-item">
                        <span>制卡费:</span>
                        <span>¥5.00</span>
                    </li>
                    <li class="fee-item">
                        <span>邮寄费:</span>
                        <span>¥3.00</span>
                    </li>
                    <li class="fee-item">
                        <span>总计:</span>
                        <span>¥28.00</span>
                    </li>
                </ul>
            </div>

            <div class="form-actions">
                <button class="btn-mobile btn-reset flex-1" onclick="resetForm();">重置</button>
                <button class="btn-mobile btn-replace flex-1" onclick="submitReplace();">提交申请</button>
            </div>
        </div>

        <!-- 补办指南 -->
        <div class="replace-guide">
            <div class="guide-title">
                <i class="ace-icon fa fa-question-circle"></i>
                <span>补办指南</span>
            </div>

            <ul class="guide-list">
                <li class="guide-item">
                    <div class="guide-number">1</div>
                    <div class="guide-content">
                        <div class="guide-text">填写完整的补办申请信息，确保信息准确无误</div>
                    </div>
                </li>
                <li class="guide-item">
                    <div class="guide-number">2</div>
                    <div class="guide-content">
                        <div class="guide-text">上传符合要求的证件照片，照片将用于制作新学生证</div>
                    </div>
                </li>
                <li class="guide-item">
                    <div class="guide-number">3</div>
                    <div class="guide-content">
                        <div class="guide-text">提交申请后，缴纳相关费用</div>
                    </div>
                </li>
                <li class="guide-item">
                    <div class="guide-number">4</div>
                    <div class="guide-content">
                        <div class="guide-text">等待审核通过，制卡完成后可到指定地点领取或选择邮寄</div>
                    </div>
                </li>
            </ul>
        </div>

        <!-- 补办记录 -->
        <div class="replace-history">
            <div class="history-header" onclick="toggleHistory();">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>补办记录</span>
                </div>
                <div class="history-toggle" id="historyToggle">
                    <i class="ace-icon fa fa-chevron-down"></i>
                </div>
            </div>

            <div class="history-content" id="historyContent">
                <div id="historyItems">
                    <!-- 补办记录将动态填充 -->
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let studentInfo = {};
        let replaceStatus = {};
        let historyData = [];
        let selectedPhoto = null;

        $(function() {
            initPage();
            loadStudentInfo();
            loadReplaceStatus();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 照片选择事件
            $('#photoInput').change(function() {
                handlePhotoSelect(this.files[0]);
            });
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/studentCardReplace/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data.student || {};
                    fillStudentInfo();
                },
                error: function() {
                    // 使用模拟数据
                    studentInfo = {
                        studentId: '2021001001',
                        name: '张三',
                        major: '计算机科学与技术',
                        className: '计科2021-1班'
                    };
                    fillStudentInfo();
                }
            });
        }

        // 填充学生信息
        function fillStudentInfo() {
            $('#studentId').val(studentInfo.studentId || '2021001001');
            $('#studentName').val(studentInfo.name || '张三');
            $('#major').val(studentInfo.major || '计算机科学与技术');
            $('#className').val(studentInfo.className || '计科2021-1班');
        }

        // 加载补办状态
        function loadReplaceStatus() {
            showLoading(true);

            $.ajax({
                url: "/student/studentCardReplace/getReplaceStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    replaceStatus = data.status || {};
                    renderReplaceStatus();
                    updateFormVisibility();
                    showLoading(false);
                },
                error: function() {
                    // 使用模拟数据
                    replaceStatus = {
                        status: 'available',
                        description: '可以申请补办学生证'
                    };
                    renderReplaceStatus();
                    updateFormVisibility();
                    showLoading(false);
                }
            });
        }

        // 渲染补办状态
        function renderReplaceStatus() {
            const status = replaceStatus.status || 'available';
            const iconClass = getStatusIconClass(status);
            const statusText = getStatusText(status);
            const metaText = replaceStatus.description || '状态正常';

            const statusHtml = `
                <div class="status-icon ${status}">
                    <i class="ace-icon fa ${iconClass}"></i>
                </div>
                <div class="status-content">
                    <div class="status-text">${statusText}</div>
                    <div class="status-meta">${metaText}</div>
                </div>
            `;

            $('#replaceStatusCard').html(statusHtml);
        }

        // 获取状态图标类
        function getStatusIconClass(status) {
            switch(status) {
                case 'available': return 'fa-check';
                case 'processing': return 'fa-clock-o';
                case 'unavailable': return 'fa-times';
                default: return 'fa-question';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'available': return '可以补办';
                case 'processing': return '补办中';
                case 'unavailable': return '暂不可补办';
                default: return '未知状态';
            }
        }

        // 更新表单可见性
        function updateFormVisibility() {
            const status = replaceStatus.status || 'available';

            if (status === 'available') {
                $('#replaceForm').show();
            } else {
                $('#replaceForm').hide();
            }
        }

        // 选择照片
        function selectPhoto() {
            $('#photoInput').click();
        }

        // 处理照片选择
        function handlePhotoSelect(file) {
            if (!file) return;

            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showError('请选择图片文件');
                return;
            }

            // 检查文件大小
            if (file.size > 2 * 1024 * 1024) {
                showError('照片大小不能超过2MB');
                return;
            }

            selectedPhoto = file;

            // 预览照片
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#photoPreview').html(`<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover;">`);
            };
            reader.readAsDataURL(file);
        }

        // 重置表单
        function resetForm() {
            $('#replaceReason').val('');
            $('#replaceDescription').val('');
            $('#contactPhone').val('');
            $('#mailAddress').val('');

            // 重置照片
            selectedPhoto = null;
            $('#photoPreview').html('<i class="ace-icon fa fa-camera"></i>');
            $('#photoInput').val('');
        }

        // 提交补办申请
        function submitReplace() {
            const formData = {
                reason: $('#replaceReason').val(),
                description: $('#replaceDescription').val().trim(),
                phone: $('#contactPhone').val().trim(),
                address: $('#mailAddress').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要提交学生证补办申请吗？\n\n补办费用：¥28.00\n包含工本费、制卡费和邮寄费`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitReplace(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitReplace(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.reason) {
                showError('请选择补办原因');
                return false;
            }

            if (!formData.description) {
                showError('请填写详细说明');
                return false;
            }

            if (!formData.phone) {
                showError('请输入联系电话');
                return false;
            }

            if (!selectedPhoto) {
                showError('请上传学生证照片');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            return true;
        }

        // 执行提交补办申请
        function doSubmitReplace(formData) {
            const submitData = new FormData();

            // 添加表单数据
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            // 添加照片
            if (selectedPhoto) {
                submitData.append('photo', selectedPhoto);
            }

            $.ajax({
                url: "/student/studentCardReplace/submitReplace",
                type: "post",
                data: submitData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('补办申请提交成功，请等待审核并缴费');
                        resetForm();
                        loadReplaceStatus(); // 重新加载状态
                    } else {
                        showError(data.message || '补办申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 切换历史记录显示
        function toggleHistory() {
            const content = $('#historyContent');
            const toggle = $('#historyToggle');

            if (content.hasClass('show')) {
                content.removeClass('show');
                toggle.removeClass('expanded');
            } else {
                content.addClass('show');
                toggle.addClass('expanded');

                // 如果还没有加载历史数据，则加载
                if (historyData.length === 0) {
                    loadHistoryData();
                }
            }
        }

        // 加载历史数据
        function loadHistoryData() {
            $.ajax({
                url: "/student/studentCardReplace/getHistoryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    historyData = data.history || [];
                    renderHistoryData();
                },
                error: function() {
                    // 使用模拟数据
                    historyData = [
                        {
                            id: '1',
                            type: '学生证补办',
                            status: 'completed',
                            createTime: '2024-01-15 10:30:00',
                            processTime: '2024-01-20 14:20:00',
                            reason: '学生证丢失'
                        }
                    ];
                    renderHistoryData();
                }
            });
        }

        // 渲染历史数据
        function renderHistoryData() {
            const container = $('#historyItems');
            container.empty();

            if (historyData.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无补办记录
                    </div>
                `);
                return;
            }

            historyData.forEach(item => {
                const historyHtml = createHistoryItem(item);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(item) {
            const status = item.status || 'processing';
            const statusClass = getHistoryStatusClass(status);
            const statusText = getHistoryStatusText(status);

            return `
                <div class="history-item ${status}" onclick="showHistoryDetail('${item.id}')">
                    <div class="history-basic">
                        <div class="history-type">${item.type}</div>
                        <div class="history-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>申请时间:</span>
                            <span>${formatDateTime(item.createTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>处理时间:</span>
                            <span>${formatDateTime(item.processTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>补办原因:</span>
                            <span>${item.reason || '-'}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>费用:</span>
                            <span>¥28.00</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取历史状态样式类
        function getHistoryStatusClass(status) {
            return `status-${status}`;
        }

        // 获取历史状态文本
        function getHistoryStatusText(status) {
            switch(status) {
                case 'completed': return '已完成';
                case 'processing': return '处理中';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 显示历史详情
        function showHistoryDetail(historyId) {
            const item = historyData.find(h => h.id === historyId);
            if (!item) return;

            let message = `补办详情\n\n`;
            message += `类型：${item.type}\n`;
            message += `状态：${getHistoryStatusText(item.status)}\n`;
            message += `申请时间：${item.createTime}\n`;
            message += `处理时间：${item.processTime || '-'}\n`;
            message += `补办原因：${item.reason || '-'}\n`;
            message += `费用：¥28.00\n`;

            if (item.comment) {
                message += `处理意见：${item.comment}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString();
        }

        // 刷新数据
        function refreshData() {
            loadStudentInfo();
            loadReplaceStatus();
            if ($('#historyContent').hasClass('show')) {
                loadHistoryData();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
