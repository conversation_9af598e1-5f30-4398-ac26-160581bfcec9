<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>劳动教育</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 劳动教育页面样式 */
        .labor-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .labor-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .summary-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .summary-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .summary-number.total {
            color: var(--primary-color);
        }
        
        .summary-number.completed {
            color: var(--success-color);
        }
        
        .summary-number.hours {
            color: var(--info-color);
        }
        
        .summary-number.pending {
            color: var(--warning-color);
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .labor-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .labor-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
        }
        
        .labor-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .labor-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .labor-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .labor-item:last-child {
            border-bottom: none;
        }
        
        .labor-item:active {
            background: var(--bg-color-active);
        }
        
        .labor-item.available {
            border-left: 4px solid var(--success-color);
        }
        
        .labor-item.registered {
            border-left: 4px solid var(--info-color);
        }
        
        .labor-item.completed {
            border-left: 4px solid var(--primary-color);
        }
        
        .labor-item.expired {
            border-left: 4px solid var(--error-color);
        }
        
        .labor-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .labor-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .labor-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-registered {
            background: var(--info-color);
            color: white;
        }
        
        .status-completed {
            background: var(--primary-color);
            color: white;
        }
        
        .status-expired {
            background: var(--error-color);
            color: white;
        }
        
        .labor-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .labor-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .labor-description {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
        }
        
        .labor-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-register {
            background: var(--success-color);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .registration-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .registration-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel-form {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit-form {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">劳动教育</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="labor-header">
            <div class="header-title">劳动教育</div>
            <div class="header-subtitle">参与劳动实践，培养劳动精神</div>
        </div>

        <!-- 劳动汇总 -->
        <div class="labor-summary">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>劳动统计</span>
            </div>

            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-number total" id="totalActivities">0</div>
                    <div class="summary-label">总活动数</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number completed" id="completedActivities">0</div>
                    <div class="summary-label">已完成</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number hours" id="totalHours">0</div>
                    <div class="summary-label">总时长(小时)</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number pending" id="pendingActivities">0</div>
                    <div class="summary-label">待参与</div>
                </div>
            </div>
        </div>

        <!-- 劳动标签 -->
        <div class="labor-tabs">
            <div class="labor-tab active" data-tab="available" onclick="switchTab('available')">可报名</div>
            <div class="labor-tab" data-tab="registered" onclick="switchTab('registered')">已报名</div>
            <div class="labor-tab" data-tab="completed" onclick="switchTab('completed')">已完成</div>
        </div>

        <!-- 劳动列表 -->
        <div class="labor-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span id="listTitle">可报名活动</span>
                </div>
                <div class="list-count" id="laborCount">0</div>
            </div>

            <div id="laborItems">
                <!-- 劳动列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-hand-paper-o"></i>
            <div id="emptyMessage">暂无劳动活动</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 报名表单 -->
    <div class="registration-form" id="registrationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeRegistrationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">劳动活动报名</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">班级</div>
                    <input type="text" class="form-input" id="className" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-hand-paper-o"></i>
                    <span>活动信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">活动名称</div>
                    <input type="text" class="form-input" id="activityName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">活动时间</div>
                    <input type="text" class="form-input" id="activityTime" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">活动地点</div>
                    <input type="text" class="form-input" id="activityLocation" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">劳动时长</div>
                    <input type="text" class="form-input" id="activityHours" disabled>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>报名信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">报名理由</div>
                    <textarea class="form-input" id="reason" placeholder="请说明参与该劳动活动的理由" style="min-height: 80px; resize: vertical;"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">特殊说明</div>
                    <textarea class="form-input" id="note" placeholder="其他需要说明的情况（选填）" style="min-height: 60px; resize: vertical;"></textarea>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel-form flex-1" onclick="closeRegistrationForm();">取消</button>
            <button class="btn-mobile btn-submit-form flex-1" onclick="submitRegistration();">确认报名</button>
        </div>
    </div>

    <script>
        // 全局变量
        let allActivities = [];
        let currentTab = 'available';
        let summaryData = {};
        let currentActivity = null;

        $(function() {
            initPage();
            loadSummaryData();
            loadLaborData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载汇总数据
        function loadSummaryData() {
            $.ajax({
                url: "/student/laborEducation/getSummaryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    summaryData = data.summary || {};
                    renderSummaryData();
                },
                error: function() {
                    console.log('加载汇总数据失败');
                }
            });
        }

        // 渲染汇总数据
        function renderSummaryData() {
            $('#totalActivities').text(summaryData.totalActivities || 0);
            $('#completedActivities').text(summaryData.completedActivities || 0);
            $('#totalHours').text(summaryData.totalHours || 0);
            $('#pendingActivities').text(summaryData.pendingActivities || 0);
        }

        // 加载劳动数据
        function loadLaborData() {
            showLoading(true);

            $.ajax({
                url: "/student/laborEducation/getLaborData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allActivities = data.activities || [];
                    renderLaborList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载劳动活动数据失败');
                    showLoading(false);
                }
            });
        }

        // 切换标签
        function switchTab(tab) {
            currentTab = tab;

            // 更新标签状态
            $('.labor-tab').removeClass('active');
            $(`.labor-tab[data-tab="${tab}"]`).addClass('active');

            // 更新列表标题
            const titles = {
                'available': '可报名活动',
                'registered': '已报名活动',
                'completed': '已完成活动'
            };
            $('#listTitle').text(titles[tab]);

            renderLaborList();
        }

        // 渲染劳动列表
        function renderLaborList() {
            const filteredActivities = allActivities.filter(activity => {
                const status = getActivityStatus(activity);
                return currentTab === 'available' ? status === 'available' :
                       currentTab === 'registered' ? status === 'registered' :
                       status === 'completed';
            });

            $('#laborCount').text(filteredActivities.length);

            const container = $('#laborItems');
            container.empty();

            if (filteredActivities.length === 0) {
                const messages = {
                    'available': '暂无可报名的劳动活动',
                    'registered': '暂无已报名的活动',
                    'completed': '暂无已完成的活动'
                };
                showEmptyState(messages[currentTab]);
                return;
            } else {
                hideEmptyState();
            }

            filteredActivities.forEach(activity => {
                const activityHtml = createLaborItem(activity);
                container.append(activityHtml);
            });
        }

        // 创建劳动项
        function createLaborItem(activity) {
            const status = getActivityStatus(activity);
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);

            return `
                <div class="labor-item ${status}" onclick="showActivityDetail('${activity.id}')">
                    <div class="labor-basic">
                        <div class="labor-name">${activity.name}</div>
                        <div class="labor-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="labor-details">
                        <div class="labor-detail-item">
                            <span>活动类型:</span>
                            <span>${getActivityTypeText(activity.type)}</span>
                        </div>
                        <div class="labor-detail-item">
                            <span>劳动时长:</span>
                            <span>${activity.hours}小时</span>
                        </div>
                        <div class="labor-detail-item">
                            <span>活动时间:</span>
                            <span>${formatDate(activity.startTime)}</span>
                        </div>
                        <div class="labor-detail-item">
                            <span>报名截止:</span>
                            <span>${formatDate(activity.deadline)}</span>
                        </div>
                        <div class="labor-detail-item">
                            <span>活动地点:</span>
                            <span>${activity.location || '待定'}</span>
                        </div>
                        <div class="labor-detail-item">
                            <span>参与人数:</span>
                            <span>${activity.currentCount}/${activity.maxCount}</span>
                        </div>
                    </div>
                    ${activity.description ? `
                        <div class="labor-description">${activity.description}</div>
                    ` : ''}
                    <div class="labor-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showActivityDetail('${activity.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'available' ? `
                            <button class="btn-mobile btn-register" onclick="event.stopPropagation(); showRegistrationForm('${activity.id}');">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>报名</span>
                            </button>
                        ` : status === 'registered' ? `
                            <button class="btn-mobile btn-submit" onclick="event.stopPropagation(); submitWork('${activity.id}');">
                                <i class="ace-icon fa fa-upload"></i>
                                <span>提交</span>
                            </button>
                            <button class="btn-mobile btn-cancel" onclick="event.stopPropagation(); cancelRegistration('${activity.id}');">
                                <i class="ace-icon fa fa-times"></i>
                                <span>取消</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-check"></i>
                                <span>已完成</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取活动状态
        function getActivityStatus(activity) {
            if (activity.isCompleted) {
                return 'completed';
            } else if (activity.isRegistered) {
                return 'registered';
            } else {
                const now = new Date();
                const deadline = new Date(activity.deadline);
                return deadline > now ? 'available' : 'expired';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'available': return '可报名';
                case 'registered': return '已报名';
                case 'completed': return '已完成';
                case 'expired': return '已截止';
                default: return '未知';
            }
        }

        // 获取活动类型文本
        function getActivityTypeText(type) {
            switch(type) {
                case 'campus': return '校园劳动';
                case 'community': return '社区服务';
                case 'volunteer': return '志愿服务';
                case 'practice': return '实践活动';
                default: return '其他';
            }
        }

        // 显示活动详情
        function showActivityDetail(activityId) {
            const activity = allActivities.find(a => a.id === activityId);
            if (!activity) return;

            let message = `活动详情\n\n`;
            message += `活动名称：${activity.name}\n`;
            message += `活动类型：${getActivityTypeText(activity.type)}\n`;
            message += `劳动时长：${activity.hours}小时\n`;
            message += `活动时间：${formatDate(activity.startTime)}\n`;
            message += `报名截止：${formatDate(activity.deadline)}\n`;
            message += `活动地点：${activity.location || '待定'}\n`;
            message += `参与人数：${activity.currentCount}/${activity.maxCount}\n`;
            message += `状态：${getStatusText(getActivityStatus(activity))}\n`;

            if (activity.requirements) {
                message += `\n活动要求：${activity.requirements}\n`;
            }

            if (activity.description) {
                message += `活动说明：${activity.description}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示报名表单
        function showRegistrationForm(activityId) {
            const activity = allActivities.find(a => a.id === activityId);
            if (!activity) return;

            currentActivity = activity;

            // 填充基本信息
            $('#studentId').val('2021001001'); // 模拟数据
            $('#studentName').val('张三'); // 模拟数据
            $('#className').val('计科2021-1班'); // 模拟数据

            // 填充活动信息
            $('#activityName').val(activity.name);
            $('#activityTime').val(formatDate(activity.startTime));
            $('#activityLocation').val(activity.location || '待定');
            $('#activityHours').val(activity.hours + '小时');

            // 清空表单
            $('#phone').val('');
            $('#reason').val('');
            $('#note').val('');

            $('#formTitle').text(`${activity.name} - 报名`);
            $('#registrationForm').addClass('show');
        }

        // 关闭报名表单
        function closeRegistrationForm() {
            $('#registrationForm').removeClass('show');
            currentActivity = null;
        }

        // 提交报名
        function submitRegistration() {
            if (!currentActivity) return;

            const formData = {
                activityId: currentActivity.id,
                phone: $('#phone').val().trim(),
                reason: $('#reason').val().trim(),
                note: $('#note').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要报名"${currentActivity.name}"吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitRegistration(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitRegistration(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.phone) {
                showError('请填写联系电话');
                return false;
            }

            if (!formData.reason) {
                showError('请填写报名理由');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            return true;
        }

        // 执行提交报名
        function doSubmitRegistration(formData) {
            $.ajax({
                url: "/student/laborEducation/submitRegistration",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('报名成功');
                        closeRegistrationForm();
                        loadLaborData(); // 重新加载数据
                    } else {
                        showError(data.message || '报名失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 提交作业
        function submitWork(activityId) {
            const activity = allActivities.find(a => a.id === activityId);
            if (!activity) return;

            const message = `确定要提交"${activity.name}"的劳动成果吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        // 这里应该跳转到作业提交页面
                        showSuccess('跳转到作业提交页面');
                    }
                });
            } else {
                if (confirm(message)) {
                    showSuccess('跳转到作业提交页面');
                }
            }
        }

        // 取消报名
        function cancelRegistration(activityId) {
            const activity = allActivities.find(a => a.id === activityId);
            if (!activity) return;

            const message = `确定要取消报名"${activity.name}"吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doCancelRegistration(activityId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doCancelRegistration(activityId);
                }
            }
        }

        // 执行取消报名
        function doCancelRegistration(activityId) {
            $.ajax({
                url: "/student/laborEducation/cancelRegistration",
                type: "post",
                data: { activityId: activityId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('取消报名成功');
                        loadLaborData(); // 重新加载数据
                    } else {
                        showError(data.message || '取消报名失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadSummaryData();
            loadLaborData();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
