<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>交流生成绩认定申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 成绩认定申请页面样式 */
        .achievement-header {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .achievement-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .achievement-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--success-color);
        }
        
        .search-form {
            display: flex;
            gap: var(--spacing-md);
            align-items: flex-end;
        }
        
        .form-group {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-select {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-select:focus {
            outline: none;
            border-color: var(--success-color);
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
        
        .btn-search {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-sm) var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
            white-space: nowrap;
        }
        
        .btn-search:hover {
            background: var(--success-dark);
        }
        
        .records-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-header i {
            color: var(--success-color);
        }
        
        .records-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .record-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .record-item:last-child {
            border-bottom: none;
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .record-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--success-light);
            color: var(--success-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .record-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .record-id {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .record-type {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .record-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .record-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 8px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .detail-item i {
            color: var(--success-color);
            width: 12px;
        }
        
        .record-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .action-btn.apply {
            color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .action-btn.apply:hover {
            background: var(--success-light);
        }
        
        .action-btn.view {
            color: var(--info-color);
            border-color: var(--info-color);
        }
        
        .action-btn.view:hover {
            background: var(--info-light);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--success-light);
            border-color: var(--success-color);
            color: var(--success-dark);
        }
        
        .btn-page.active {
            background: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .search-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .record-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .record-details {
                grid-template-columns: 1fr;
            }
            
            .record-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">交流生成绩认定申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 成绩认定申请头部 -->
        <div class="achievement-header">
            <div class="achievement-title">交流生成绩认定申请</div>
            <div class="achievement-desc">管理您的成绩认定申请</div>
        </div>
        
        <!-- 搜索条件 -->
        <div class="search-container">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label class="form-label">项目类型</label>
                    <select class="form-select" name="xmlx" id="xmlx">
                        <option value="">全部</option>
                        <option value="long">长期项目</option>
                        <option value="short">短期项目</option>
                    </select>
                </div>
                
                <button type="button" class="btn-search" onclick="searchRecords();">
                    <i class="ace-icon fa fa-search"></i>
                    查询
                </button>
            </form>
        </div>
        
        <!-- 成绩单信息列表 -->
        <div class="records-container">
            <div class="container-header">
                <i class="ace-icon fa fa-list"></i>
                出国交流成绩单信息
            </div>
            
            <div class="records-list" id="recordsList">
                <!-- 动态加载成绩单列表 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;

        $(function() {
            initPage();
            loadRecords();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载成绩单记录
        function loadRecords(page = 1) {
            currentPage = page;
            showLoading(true);

            const formData = $('#searchForm').serialize();

            $.ajax({
                url: "/student/personalManagement/achievementDetermination/index/getCjdList",
                type: "post",
                data: formData + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    if (response && response.data) {
                        renderRecords(response.data.records);
                        renderPagination(response.data.pageContext);
                    } else {
                        showEmptyState('recordsList', '暂无成绩单记录');
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染成绩单记录
        function renderRecords(records) {
            const container = $('#recordsList');

            if (!records || records.length === 0) {
                showEmptyState('recordsList', '暂无成绩单记录');
                return;
            }

            let recordsHtml = '';

            records.forEach((record, index) => {
                const serialNumber = (currentPage - 1) * pageSize + 1 + index;
                const statusInfo = getStatusInfo(record.BSQZT);
                const projectType = record.XMLX === 'long' ? '长期项目' : '短期项目';
                const recognitionMode = getRecognitionMode(record.CJRDMS);

                recordsHtml += `
                    <div class="record-item">
                        <div class="record-header">
                            <div class="record-number">${serialNumber}</div>
                            <div class="record-info">
                                <div class="record-id">成绩单ID：${record.CJD_ID || '未设置'}</div>
                                <div class="record-type">${projectType}</div>
                            </div>
                            <div class="record-status ${statusInfo.class}">${statusInfo.text}</div>
                        </div>

                        <div class="record-details">
                            <div class="detail-item">
                                <i class="ace-icon fa fa-upload"></i>
                                <span>上传时间：${record.SCSJ || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-user"></i>
                                <span>上传人：${record.SCRM || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-check"></i>
                                <span>审核时间：${record.SHSJ || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-user-md"></i>
                                <span>审核人：${record.SHRM || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-cog"></i>
                                <span>认定模式：${recognitionMode}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-info-circle"></i>
                                <span>审批状态：${statusInfo.text}</span>
                            </div>
                        </div>

                        <div class="record-actions">
                            ${record.SQZT === '1' ? `
                                <button class="action-btn apply" onclick="queryApplyInfo('${record.CJD_ID}');">
                                    <i class="ace-icon fa fa-pencil-square-o"></i>
                                    申请
                                </button>
                            ` : `
                                <button class="action-btn view" onclick="showApplyInfo('${record.SQBH}', '${record.XMLX}');">
                                    <i class="ace-icon fa fa-eye"></i>
                                    查看
                                </button>
                            `}
                        </div>
                    </div>
                `;
            });

            container.html(recordsHtml);
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case '0':
                    return { text: "待审批", class: "status-pending" };
                case '1':
                    return { text: "通过", class: "status-approved" };
                case '2':
                    return { text: "拒绝", class: "status-rejected" };
                default:
                    return { text: "未知", class: "status-pending" };
            }
        }

        // 获取认定模式
        function getRecognitionMode(mode) {
            switch(mode) {
                case '1':
                    return '整体认定';
                case '2':
                    return '按学分认定';
                default:
                    return '未设置';
            }
        }

        // 显示空状态
        function showEmptyState(containerId, message) {
            const container = $('#' + containerId);
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <div class="empty-state-text">${message}</div>
                    <div class="empty-state-desc">请调整搜索条件后重试</div>
                </div>
            `);
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            if (!pageContext || pageContext.totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="loadRecords(${currentPage - 1});">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<button class="btn-page" onclick="loadRecords(1);">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="loadRecords(${i});">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                paginationHtml += `<button class="btn-page" onclick="loadRecords(${totalPages});">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="loadRecords(${currentPage + 1});">下一页</button>`;

            container.html(paginationHtml);
            $('#paginationContainer').show();
        }

        // 搜索记录
        function searchRecords() {
            loadRecords(1);
        }

        // 申请成绩认定
        function queryApplyInfo(cjdId) {
            const url = `/student/personalManagement/achievementDetermination/index/queryApplyInfo?cjd_id=${cjdId}`;

            if (parent && parent.addTab) {
                parent.addTab('申请成绩认定', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看申请信息
        function showApplyInfo(sqbh, xmlx) {
            const url = `/student/personalManagement/achievementDetermination/index/showApplyInfo?sqbh=${sqbh}&xmlx=${xmlx}`;

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 刷新数据
        function refreshData() {
            loadRecords(currentPage);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
