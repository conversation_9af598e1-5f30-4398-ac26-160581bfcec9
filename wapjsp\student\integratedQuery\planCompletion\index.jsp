<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>方案完成情况</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 方案完成情况页面样式 */
        .tabs-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            display: flex;
            background: var(--bg-tertiary);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .tabs-header::-webkit-scrollbar {
            display: none;
        }
        
        .tab-button {
            flex: 0 0 auto;
            padding: var(--padding-md);
            background: transparent;
            border: none;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
            white-space: nowrap;
            min-width: 100px;
            text-align: center;
        }
        
        .tab-button.active {
            background: var(--bg-primary);
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .plan-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--margin-lg);
        }
        
        .stats-card {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
            position: relative;
        }
        
        .stats-icon {
            font-size: 24px;
            margin-bottom: var(--margin-sm);
        }
        
        .stats-number {
            font-size: var(--font-size-h2);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stats-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .stats-card.green .stats-icon { color: var(--success-color); }
        .stats-card.blue .stats-icon { color: var(--info-color); }
        .stats-card.pink .stats-icon { color: var(--error-color); }
        .stats-card.orange .stats-icon { color: var(--warning-color); }
        .stats-card.red .stats-icon { color: var(--error-color); }
        
        .progress-section {
            margin: var(--margin-lg) 0;
        }
        
        .progress-item {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .progress-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .progress-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .progress-bar-container {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            border-radius: 10px;
            transition: width 0.6s ease;
            position: relative;
        }
        
        .progress-bar.success {
            background: linear-gradient(90deg, var(--success-color), #4caf50);
        }
        
        .progress-bar.danger {
            background: linear-gradient(90deg, var(--error-color), #f44336);
        }
        
        .progress-bar.warning {
            background: linear-gradient(90deg, var(--warning-color), #ff9800);
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        .course-tree {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-top: var(--margin-md);
        }
        
        .tree-controls {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .btn-tree {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-expand {
            background: var(--info-color);
            color: white;
        }
        
        .btn-collapse {
            background: var(--secondary-color);
            color: white;
        }
        
        .semester-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--margin-md);
        }
        
        .semester-table th,
        .semester-table td {
            padding: var(--padding-sm);
            text-align: center;
            border: 1px solid var(--border-primary);
            font-size: var(--font-size-small);
        }
        
        .semester-table th {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .semester-table td {
            background: var(--bg-primary);
        }
        
        .warning-notice {
            background: var(--error-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .download-section {
            text-align: center;
            margin: var(--margin-md) 0;
        }
        
        .btn-download {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md) var(--padding-lg);
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-base);
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
            font-size: var(--font-size-small);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .legend-icon {
            font-size: 16px;
        }
        
        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .semester-table {
                font-size: var(--font-size-mini);
            }
            
            .semester-table th,
            .semester-table td {
                padding: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">方案完成情况</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <c:if test="${fajhh==null || fajhh==''}">
            <!-- 无方案提示 -->
            <div class="empty-state">
                <i class="ace-icon fa fa-exclamation-circle"></i>
                <div>对不起，没有查询到您的主修方案！</div>
            </div>
        </c:if>
        
        <c:if test="${fajhh!=null && fajhh!=''}">
            <!-- 选项卡容器 -->
            <div class="tabs-container">
                <div class="tabs-header">
                    <button class="tab-button active" onclick="switchTab('overview')">总体情况</button>
                    <button class="tab-button" onclick="switchTab('courses')">课程修读</button>
                    <button class="tab-button" onclick="switchTab('semester')">学期完成</button>
                </div>
                
                <!-- 总体完成情况 -->
                <div class="tab-content" id="overviewTab">
                    <div class="plan-title">${jhFajhb.famc}</div>
                    
                    <!-- 基础统计 -->
                    <div class="stats-grid">
                        <div class="stats-card green">
                            <div class="stats-icon">
                                <i class="ace-icon fa fa-book"></i>
                            </div>
                            <div class="stats-number">${qbcj.fajhzms}</div>
                            <div class="stats-label">总门数</div>
                        </div>
                        
                        <div class="stats-card blue">
                            <div class="stats-icon">
                                <i class="ace-icon fa fa-graduation-cap"></i>
                            </div>
                            <div class="stats-number">${qbcj.fajhzxf}</div>
                            <div class="stats-label">总学分</div>
                        </div>
                        
                        <div class="stats-card pink">
                            <div class="stats-icon">
                                <i class="ace-icon fa fa-clock-o"></i>
                            </div>
                            <div class="stats-number">${qbcj.fajhzxs}</div>
                            <div class="stats-label">总课时</div>
                        </div>
                        
                        <div class="stats-card orange">
                            <div class="stats-icon">
                                <i class="ace-icon fa fa-check-circle"></i>
                            </div>
                            <div class="stats-number">${qbcj.fajhnkcms}</div>
                            <div class="stats-label">方案内修读</div>
                        </div>
                        
                        <div class="stats-card red">
                            <div class="stats-icon">
                                <i class="ace-icon fa fa-external-link"></i>
                            </div>
                            <div class="stats-number">${qbcj.fajhwkcms}</div>
                            <div class="stats-label">方案外修读</div>
                        </div>
                    </div>
                    
                    <!-- 进度统计 -->
                    <div class="progress-section">
                        <div class="progress-item">
                            <div class="progress-header">
                                <div class="progress-title">课程及格率</div>
                                <div class="progress-value">
                                    及格${qbcj.tgms}门，未及格${qbcj.wtgms}门
                                </div>
                            </div>
                            <div class="progress-bar-container">
                                <c:set var="passRate" value="${qbcj.tgms/(qbcj.tgms+qbcj.wtgms)*100}" />
                                <div class="progress-bar ${passRate >= 80 ? 'success' : (passRate >= 60 ? 'warning' : 'danger')}" 
                                     style="width: ${passRate > 100 ? 100 : passRate}%">
                                    <div class="progress-text">
                                        <fmt:formatNumber type="percent" minFractionDigits="0" value="${qbcj.tgms/(qbcj.tgms+qbcj.wtgms)}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="progress-item">
                            <div class="progress-header">
                                <div class="progress-title">未完成课组最低进度</div>
                                <div class="progress-value">
                                    完成${wckz}个，未完成${wwckz}个课组
                                </div>
                            </div>
                            <div class="progress-bar-container">
                                <div class="progress-bar ${temp >= 80 ? 'success' : (temp >= 60 ? 'warning' : 'danger')}" 
                                     style="width: ${temp == 'NaN' ? 0 : temp}%">
                                    <div class="progress-text">${temp == 'NaN' ? 0 : temp}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 下载培养方案书（特定学校） -->
                    <c:if test="${schoolCode eq '100033'}">
                        <div class="download-section">
                            <button class="btn-download" onclick="downFasFile('${fjid}');">
                                <i class="ace-icon fa fa-download"></i>
                                <span>下载我的培养方案书</span>
                            </button>
                        </div>
                    </c:if>
                </div>
                
                <!-- 课程修读情况 -->
                <div class="tab-content" id="coursesTab" style="display: none;">
                    <div class="legend">
                        <div class="legend-item">
                            <i class="ace-icon fa fa-check-square-o legend-icon" style="color: var(--success-color);"></i>
                            <span>已完成课组</span>
                        </div>
                        <div class="legend-item">
                            <i class="ace-icon fa fa-square-o legend-icon" style="color: var(--info-color);"></i>
                            <span>未完成课组</span>
                        </div>
                        <div class="legend-item">
                            <i class="ace-icon fa fa-smile-o legend-icon" style="color: var(--success-color);"></i>
                            <span>已修读及格</span>
                        </div>
                        <div class="legend-item">
                            <i class="ace-icon fa fa-frown-o legend-icon" style="color: var(--error-color);"></i>
                            <span>已修读未及格</span>
                        </div>
                        <div class="legend-item">
                            <i class="ace-icon fa fa-meh-o legend-icon" style="color: var(--text-disabled);"></i>
                            <span>尚未修读</span>
                        </div>
                    </div>
                    
                    <div class="tree-controls">
                        <button class="btn-tree btn-expand" onclick="expandAllNodes();">
                            <i class="ace-icon fa fa-folder-open-o"></i>
                            <span>打开课组</span>
                        </button>
                        <button class="btn-tree btn-collapse" onclick="collapseAllNodes();">
                            <i class="ace-icon fa fa-folder-o"></i>
                            <span>关闭课组</span>
                        </button>
                    </div>
                    
                    <div class="course-tree">
                        <div id="courseTreeContainer">
                            <!-- 课程树将通过JavaScript渲染 -->
                        </div>
                    </div>
                </div>
                
                <!-- 学期完成情况 -->
                <div class="tab-content" id="semesterTab" style="display: none;">
                    <table class="semester-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>学期学年</th>
                                <th>要求最低学分</th>
                                <th>已修读学分</th>
                                <th>已及格门数</th>
                                <th>未及格门数</th>
                                <th>学分完成率</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach items="${qbCjList}" var="gradeCj" varStatus="s">
                                <tr>
                                    <td>${s.count}</td>
                                    <td>${gradeCj.cjlx}</td>
                                    <td>${gradeCj.yqxf}</td>
                                    <td>${gradeCj.zxf}</td>
                                    <td>${gradeCj.tgms}</td>
                                    <td>${gradeCj.wtgms}</td>
                                    <td>
                                        <c:if test="${gradeCj.yqxf != '' && gradeCj.yqxf != null}">
                                            <c:set var="completionRate" value="${(gradeCj.zxf)/(gradeCj.yqxf)*100}" />
                                            <div class="progress-bar-container" style="height: 16px;">
                                                <div class="progress-bar ${gradeCj.yqxf <= gradeCj.zxf ? 'success' : 'danger'}" 
                                                     style="width: ${completionRate > 100 ? 100 : completionRate}%">
                                                    <div class="progress-text" style="font-size: 10px;">
                                                        <fmt:formatNumber type="percent" minFractionDigits="0" value="${(gradeCj.zxf)/(gradeCj.yqxf)}"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </c:if>
                                        <c:if test="${gradeCj.yqxf == '' || gradeCj.yqxf == null}">
                                            <div class="progress-bar-container" style="height: 16px;">
                                                <div class="progress-bar success" style="width: 100%">
                                                    <div class="progress-text" style="font-size: 10px;">100%</div>
                                                </div>
                                            </div>
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 特别提示（川大） -->
            <c:if test="${school_id == '100006'}">
                <div class="warning-notice">
                    <i class="ace-icon fa fa-warning"></i>
                    <strong>特别提示：</strong>根据《四川大学本科生学业预警管理办法》（川大教〔2020〕13号）文件规定，在校学习期间，不及格必修课程（含缺考）累计超过30学分（已经重修并且及格的课程不再计入），且已获得总学分未达到所在专业毕业规定总学分的75%者，作退学处理。此管理办法自2019级起执行，请各位同学认真学习，避免达到退学红线。
                </div>
            </c:if>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'overview';
        let treeData = [];
        let treeInitialized = false;

        $(function() {
            initPage();
            initTreeData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化树形数据
        function initTreeData() {
            <%
            String treeList = ((String)request.getAttribute("treeNodeList"));
            if (treeList != null) {
            %>
            try {
                treeData = <%=treeList%>;
                console.log('Tree data loaded:', treeData);
            } catch (e) {
                console.error('Failed to parse tree data:', e);
                treeData = [];
            }
            <% } else { %>
            treeData = [];
            <% } %>
        }

        // 切换选项卡
        function switchTab(tabName) {
            // 更新按钮状态
            $('.tab-button').removeClass('active');
            $(event.target).addClass('active');

            // 隐藏所有内容
            $('.tab-content').hide();

            // 显示对应内容
            switch(tabName) {
                case 'overview':
                    $('#overviewTab').show();
                    break;
                case 'courses':
                    $('#coursesTab').show();
                    if (!treeInitialized) {
                        renderCourseTree();
                        treeInitialized = true;
                    }
                    break;
                case 'semester':
                    $('#semesterTab').show();
                    break;
            }

            currentTab = tabName;
        }

        // 渲染课程树
        function renderCourseTree() {
            const container = $('#courseTreeContainer');
            container.empty();

            if (!treeData || treeData.length === 0) {
                container.html('<div class="empty-state"><i class="ace-icon fa fa-tree"></i><div>暂无课程树数据</div></div>');
                return;
            }

            // 构建树形结构
            const treeHtml = buildTreeHtml(treeData);
            container.html(treeHtml);
        }

        // 构建树形HTML
        function buildTreeHtml(nodes) {
            if (!nodes || nodes.length === 0) return '';

            let html = '<ul class="tree-list">';

            nodes.forEach(function(node) {
                html += '<li class="tree-node">';

                // 节点图标和展开/折叠按钮
                if (node.children && node.children.length > 0) {
                    html += '<span class="tree-toggle" onclick="toggleNode(this);">';
                    html += '<i class="ace-icon fa fa-plus-square-o"></i>';
                    html += '</span>';
                } else {
                    html += '<span class="tree-spacer"></span>';
                }

                // 节点内容
                html += '<span class="tree-content">';

                // 根据节点状态添加图标
                if (node.highlight) {
                    html += '<i class="ace-icon fa fa-check-square-o" style="color: var(--success-color);"></i>';
                } else if (node.isGroup) {
                    html += '<i class="ace-icon fa fa-square-o" style="color: var(--info-color);"></i>';
                } else if (node.passed) {
                    html += '<i class="ace-icon fa fa-smile-o" style="color: var(--success-color);"></i>';
                } else if (node.failed) {
                    html += '<i class="ace-icon fa fa-frown-o" style="color: var(--error-color);"></i>';
                } else {
                    html += '<i class="ace-icon fa fa-meh-o" style="color: var(--text-disabled);"></i>';
                }

                html += ' ' + (node.name || '');
                html += '</span>';

                // 子节点
                if (node.children && node.children.length > 0) {
                    html += '<div class="tree-children" style="display: none;">';
                    html += buildTreeHtml(node.children);
                    html += '</div>';
                }

                html += '</li>';
            });

            html += '</ul>';
            return html;
        }

        // 切换节点展开/折叠
        function toggleNode(element) {
            const $toggle = $(element);
            const $children = $toggle.siblings('.tree-children');
            const $icon = $toggle.find('i');

            if ($children.is(':visible')) {
                $children.slideUp(200);
                $icon.removeClass('fa-minus-square-o').addClass('fa-plus-square-o');
            } else {
                $children.slideDown(200);
                $icon.removeClass('fa-plus-square-o').addClass('fa-minus-square-o');
            }
        }

        // 展开所有节点
        function expandAllNodes() {
            $('.tree-children').slideDown(200);
            $('.tree-toggle i').removeClass('fa-plus-square-o').addClass('fa-minus-square-o');
        }

        // 折叠所有节点
        function collapseAllNodes() {
            $('.tree-children').slideUp(200);
            $('.tree-toggle i').removeClass('fa-minus-square-o').addClass('fa-plus-square-o');
        }

        // 下载培养方案文件
        function downFasFile(fjid) {
            if (!fjid) {
                showError('文件ID不存在');
                return;
            }

            const url = '/student/integratedQuery/planCompletion/downloadFile?fjid=' + fjid;
            window.open(url, '_blank');
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>

    <style>
        /* 树形结构样式 */
        .tree-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tree-node {
            margin: 4px 0;
            position: relative;
        }

        .tree-toggle {
            cursor: pointer;
            display: inline-block;
            width: 20px;
            text-align: center;
            color: var(--text-secondary);
        }

        .tree-spacer {
            display: inline-block;
            width: 20px;
        }

        .tree-content {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: 1.4;
            margin-left: 4px;
        }

        .tree-children {
            margin-left: 20px;
            border-left: 1px dashed var(--border-primary);
            padding-left: 10px;
        }

        .tree-children .tree-node {
            position: relative;
        }

        .tree-children .tree-node:before {
            content: '';
            position: absolute;
            left: -10px;
            top: 10px;
            width: 8px;
            height: 1px;
            border-top: 1px dashed var(--border-primary);
        }
    </style>
</body>
</html>
