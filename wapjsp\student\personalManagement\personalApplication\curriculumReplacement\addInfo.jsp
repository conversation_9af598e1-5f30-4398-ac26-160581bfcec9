<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>添加课程替代申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 添加课程替代申请页面样式 */
        .add-header {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .add-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .add-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .form-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-section {
            margin-bottom: var(--margin-lg);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            color: var(--success-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .required {
            color: var(--error-color);
        }
        
        .replacement-types {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .type-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .type-option:hover {
            background: var(--bg-tertiary);
        }
        
        .type-option.selected {
            background: var(--success-light);
            border-color: var(--success-color);
        }
        
        .type-radio {
            margin: 0;
        }
        
        .type-label {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            flex: 1;
        }
        
        .course-selection {
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-md);
            background: var(--bg-tertiary);
            min-height: 80px;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .course-placeholder {
            color: var(--text-disabled);
            font-style: italic;
            text-align: center;
            padding: var(--padding-lg);
        }
        
        .course-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .course-item {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .course-name {
            flex: 1;
        }
        
        .course-remove {
            color: var(--error-color);
            cursor: pointer;
            padding: 4px;
        }
        
        .btn-select-course {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-sm) var(--padding-md);
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
            align-self: flex-start;
        }
        
        .btn-select-course:hover {
            background: var(--primary-dark);
        }
        
        .form-textarea {
            width: 100%;
            min-height: 100px;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            resize: vertical;
            box-sizing: border-box;
        }
        
        .form-textarea:focus {
            outline: none;
            border-color: var(--success-color);
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
        
        .form-select {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-select:focus {
            outline: none;
            border-color: var(--success-color);
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
        
        .form-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            flex: 1;
        }
        
        .btn-submit:hover {
            background: var(--success-dark);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            flex: 1;
        }
        
        .btn-cancel:hover {
            background: var(--text-secondary);
        }
        
        .warning-notice {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }
        
        .warning-icon {
            color: var(--warning-color);
            font-size: var(--font-size-large);
            margin-top: 2px;
        }
        
        .warning-content {
            flex: 1;
        }
        
        .warning-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--warning-dark);
            margin-bottom: 4px;
        }
        
        .warning-text {
            font-size: var(--font-size-small);
            color: var(--warning-dark);
            line-height: 1.4;
        }
        
        @media (max-width: 480px) {
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    <input type="hidden" id="cjtdyy" value="${cjtdyy}">
    <input type="hidden" id="assigning" value="${assigning}">
    <input type="hidden" id="schoolCode" value="${schoolCode}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">添加课程替代申请</div>
            <div class="navbar-action">
                <!-- 空白占位 -->
            </div>
        </nav>
        
        <!-- 添加申请头部 -->
        <div class="add-header">
            <div class="add-title">添加课程替代申请</div>
            <div class="add-desc">填写课程替代申请信息</div>
        </div>
        
        <!-- 警告提示 -->
        <c:if test="${ywhdkzb.sfxyd == '1'}">
            <div class="form-container">
                <div class="warning-notice">
                    <i class="ace-icon fa fa-exclamation-triangle warning-icon"></i>
                    <div class="warning-content">
                        <div class="warning-title">重要提示</div>
                        <div class="warning-text">请注意相关规定和要求，确保申请信息准确无误。</div>
                    </div>
                </div>
            </div>
        </c:if>
        
        <!-- 表单容器 -->
        <div class="form-container">
            <!-- 替代类型选择 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-list"></i>
                    替代类型
                </div>
                
                <div class="form-group">
                    <div class="form-label">
                        <span class="required">*</span>
                        <span>请选择替代类型</span>
                    </div>
                    
                    <div class="replacement-types" id="replacementTypes">
                        <!-- 动态加载替代类型 -->
                    </div>
                </div>
            </div>
            
            <!-- 课程选择 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-book"></i>
                    课程选择
                </div>
                
                <div class="form-group">
                    <div class="form-label">
                        <span class="required">*</span>
                        <span>替代课程</span>
                    </div>
                    
                    <div class="course-selection" id="courseSelection">
                        <div class="course-placeholder">请选择替代课程</div>
                        <button class="btn-select-course" onclick="selectCourses('jg_kc');">
                            <i class="ace-icon fa fa-plus"></i>
                            选择课程
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">
                        <span class="required">*</span>
                        <span>被替代课程</span>
                    </div>
                    
                    <div class="course-selection" id="replacedCourseSelection">
                        <div class="course-placeholder">请选择被替代课程</div>
                        <button class="btn-select-course" onclick="selectCourses('fa_kc');">
                            <i class="ace-icon fa fa-plus"></i>
                            选择课程
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 申请原因 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    申请原因
                </div>
                
                <c:if test="${cjtdyy == '1'}">
                    <div class="form-group">
                        <div class="form-label">
                            <span class="required">*</span>
                            <span>替代原因</span>
                        </div>
                        
                        <select class="form-select" name="tdyym" id="tdyym" onchange="changeTdyy();">
                            <option value="">--请选择--</option>
                            <!-- 动态加载替代原因选项 -->
                        </select>
                    </div>
                </c:if>
                
                <div class="form-group" id="sqyyGroup" style="${cjtdyy == '1' ? 'display: none;' : ''}">
                    <div class="form-label">
                        <span class="required">*</span>
                        <span>申请原因</span>
                    </div>
                    
                    <textarea class="form-textarea" name="sqyy" id="sqyy" placeholder="请输入申请原因"></textarea>
                </div>
            </div>
        </div>
        
        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-cancel" onclick="goBack();">
                <i class="ace-icon fa fa-times"></i>
                取消
            </button>
            <button class="btn-submit" onclick="submitApplication();">
                <i class="ace-icon fa fa-check"></i>
                提交申请
            </button>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedCourses = [];
        let selectedReplacedCourses = [];
        let replacementTypes = [];
        let cjtdyy = '${cjtdyy}';
        let assigning = '${assigning}';
        let schoolCode = '${schoolCode}';

        $(function() {
            initPage();
            loadReplacementTypes();
            
            if (cjtdyy === '1') {
                loadReasonOptions();
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载替代类型
        function loadReplacementTypes() {
            // 模拟替代类型数据，实际应该从后端获取
            replacementTypes = [
                { value: '01', text: '一替一', description: '一门课程替代一门课程' },
                { value: '02', text: '多替一', description: '多门课程替代一门课程' },
                { value: '03', text: '一替多', description: '一门课程替代多门课程' },
                { value: '04', text: '主辅修学分认定', description: '主辅修学分认定' }
            ];

            renderReplacementTypes();
        }

        // 渲染替代类型
        function renderReplacementTypes() {
            const container = $('#replacementTypes');
            let typesHtml = '';

            replacementTypes.forEach((type, index) => {
                typesHtml += `
                    <div class="type-option" onclick="selectReplacementType('${type.value}');">
                        <input type="radio" name="tdlx" value="${type.value}" class="type-radio" id="type_${type.value}">
                        <label for="type_${type.value}" class="type-label">
                            <strong>${type.text}</strong>
                            <div style="font-size: var(--font-size-small); color: var(--text-secondary); margin-top: 4px;">
                                ${type.description}
                            </div>
                        </label>
                    </div>
                `;
            });

            container.html(typesHtml);
        }

        // 选择替代类型
        function selectReplacementType(value) {
            // 清除之前的选择
            $('.type-option').removeClass('selected');

            // 设置当前选择
            $(`input[name="tdlx"][value="${value}"]`).prop('checked', true);
            $(`input[name="tdlx"][value="${value}"]`).closest('.type-option').addClass('selected');

            // 清空课程选择
            selectedCourses = [];
            selectedReplacedCourses = [];
            updateCourseDisplay();
            updateReplacedCourseDisplay();
        }

        // 加载原因选项
        function loadReasonOptions() {
            // 模拟原因选项数据，实际应该从后端获取
            const reasonOptions = [
                { value: '01', text: '课程内容相似', sgtx: '0' },
                { value: '02', text: '学分要求', sgtx: '0' },
                { value: '03', text: '其他原因', sgtx: '1' }
            ];

            const select = $('#tdyym');
            reasonOptions.forEach(option => {
                select.append(`<option value="${option.value}" sgtx="${option.sgtx}">${option.text}</option>`);
            });
        }

        // 替代原因改变
        function changeTdyy() {
            const selectedOption = $('#tdyym option:selected');
            const sgtx = selectedOption.attr('sgtx');

            if (sgtx === '1') {
                $('#sqyyGroup').show();
            } else {
                $('#sqyyGroup').hide();
                $('#sqyy').val('');
            }
        }

        // 选择课程
        function selectCourses(type) {
            const tdlx = $('input[name="tdlx"]:checked').val();

            if (!tdlx) {
                showError('请先选择替代类型！');
                return;
            }

            // 这里应该打开课程选择页面
            // 由于移动端限制，我们使用简化的选择方式
            showCourseSelectionModal(type, tdlx);
        }

        // 显示课程选择模态框
        function showCourseSelectionModal(type, tdlx) {
            const title = type === 'jg_kc' ? '选择替代课程' : '选择被替代课程';
            const courses = getMockCourses(type);

            let modalHtml = `
                <div class="modal-overlay" id="courseModal" onclick="closeCourseModal(event);">
                    <div class="modal-content" style="max-width: 90%; max-height: 80%; background: var(--bg-primary); border-radius: 8px; padding: var(--padding-md);">
                        <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--margin-md); padding-bottom: var(--padding-sm); border-bottom: 1px solid var(--divider-color);">
                            <h3 style="margin: 0; font-size: var(--font-size-large);">${title}</h3>
                            <button onclick="closeCourseModal();" style="background: none; border: none; font-size: var(--font-size-large); color: var(--text-secondary); cursor: pointer;">
                                <i class="ace-icon fa fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body" style="max-height: 400px; overflow-y: auto;">
                            <div class="course-options" id="courseOptions">
                                ${renderCourseOptions(courses, type)}
                            </div>
                        </div>
                        <div class="modal-footer" style="display: flex; gap: var(--spacing-md); margin-top: var(--margin-md); padding-top: var(--padding-sm); border-top: 1px solid var(--divider-color);">
                            <button onclick="closeCourseModal();" style="flex: 1; background: var(--text-disabled); color: white; border: none; border-radius: 6px; padding: var(--padding-sm);">取消</button>
                            <button onclick="confirmCourseSelection('${type}', '${tdlx}');" style="flex: 1; background: var(--success-color); color: white; border: none; border-radius: 6px; padding: var(--padding-sm);">确定</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
        }

        // 渲染课程选项
        function renderCourseOptions(courses, type) {
            let optionsHtml = '';

            courses.forEach(course => {
                optionsHtml += `
                    <div class="course-option" style="display: flex; align-items: center; gap: 8px; padding: var(--padding-sm); border: 1px solid var(--border-primary); border-radius: 4px; margin-bottom: 8px; cursor: pointer;" onclick="toggleCourseOption(this);">
                        <input type="checkbox" value="${course.kch}" data-name="${course.kcm}" data-score="${course.cj || ''}" style="margin: 0;">
                        <div style="flex: 1;">
                            <div style="font-weight: 500;">${course.kcm}</div>
                            <div style="font-size: var(--font-size-small); color: var(--text-secondary);">
                                课程号：${course.kch} | 成绩：${course.cj || '无'}
                            </div>
                        </div>
                    </div>
                `;
            });

            return optionsHtml;
        }

        // 切换课程选项
        function toggleCourseOption(element) {
            const checkbox = $(element).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked'));

            if (checkbox.prop('checked')) {
                $(element).css('background', 'var(--success-light)');
            } else {
                $(element).css('background', 'transparent');
            }
        }

        // 确认课程选择
        function confirmCourseSelection(type, tdlx) {
            const selectedOptions = $('#courseOptions input[type="checkbox"]:checked');
            const selectedList = [];

            selectedOptions.each(function() {
                selectedList.push({
                    kch: $(this).val(),
                    kcm: $(this).data('name'),
                    cj: $(this).data('score')
                });
            });

            // 验证选择数量
            if (!validateCourseSelection(selectedList, type, tdlx)) {
                return;
            }

            if (type === 'jg_kc') {
                selectedCourses = selectedList;
                updateCourseDisplay();
            } else {
                selectedReplacedCourses = selectedList;
                updateReplacedCourseDisplay();
            }

            closeCourseModal();
        }

        // 验证课程选择
        function validateCourseSelection(selectedList, type, tdlx) {
            const tdlxText = replacementTypes.find(t => t.value === tdlx)?.text || '';

            if (selectedList.length === 0) {
                showError('请选择课程！');
                return false;
            }

            if (type === 'jg_kc') {
                // 替代课程验证
                if (tdlx === '03') {
                    if (selectedList.length < 2) {
                        showError(`当前替代类型为：${tdlxText}，请至少选择两门课程！`);
                        return false;
                    }
                } else {
                    if (selectedList.length > 1) {
                        showError(`当前替代类型为：${tdlxText}，只能选择一门课程！`);
                        return false;
                    }
                }
            } else {
                // 被替代课程验证
                if (tdlx === '02') {
                    if (selectedList.length < 2) {
                        showError(`当前替代类型为：${tdlxText}，请至少选择两门被替代课程！`);
                        return false;
                    }
                } else {
                    if (selectedList.length > 1) {
                        showError(`当前替代类型为：${tdlxText}，只能选择一门被替代课程！`);
                        return false;
                    }
                }
            }

            return true;
        }

        // 关闭课程选择模态框
        function closeCourseModal(event) {
            if (event && event.target !== event.currentTarget) {
                return;
            }
            $('#courseModal').remove();
        }

        // 更新课程显示
        function updateCourseDisplay() {
            const container = $('#courseSelection');

            if (selectedCourses.length === 0) {
                container.html(`
                    <div class="course-placeholder">请选择替代课程</div>
                    <button class="btn-select-course" onclick="selectCourses('jg_kc');">
                        <i class="ace-icon fa fa-plus"></i>
                        选择课程
                    </button>
                `);
            } else {
                let coursesHtml = '<div class="course-list">';

                selectedCourses.forEach((course, index) => {
                    coursesHtml += `
                        <div class="course-item">
                            <div class="course-name">${course.kcm} (${course.kch})</div>
                            <i class="ace-icon fa fa-times course-remove" onclick="removeCourse(${index}, 'jg_kc');"></i>
                        </div>
                    `;
                });

                coursesHtml += '</div>';
                coursesHtml += `
                    <button class="btn-select-course" onclick="selectCourses('jg_kc');">
                        <i class="ace-icon fa fa-edit"></i>
                        重新选择
                    </button>
                `;

                container.html(coursesHtml);
            }
        }

        // 更新被替代课程显示
        function updateReplacedCourseDisplay() {
            const container = $('#replacedCourseSelection');

            if (selectedReplacedCourses.length === 0) {
                container.html(`
                    <div class="course-placeholder">请选择被替代课程</div>
                    <button class="btn-select-course" onclick="selectCourses('fa_kc');">
                        <i class="ace-icon fa fa-plus"></i>
                        选择课程
                    </button>
                `);
            } else {
                let coursesHtml = '<div class="course-list">';

                selectedReplacedCourses.forEach((course, index) => {
                    coursesHtml += `
                        <div class="course-item">
                            <div class="course-name">${course.kcm} (${course.kch})</div>
                            <i class="ace-icon fa fa-times course-remove" onclick="removeCourse(${index}, 'fa_kc');"></i>
                        </div>
                    `;
                });

                coursesHtml += '</div>';
                coursesHtml += `
                    <button class="btn-select-course" onclick="selectCourses('fa_kc');">
                        <i class="ace-icon fa fa-edit"></i>
                        重新选择
                    </button>
                `;

                container.html(coursesHtml);
            }
        }

        // 移除课程
        function removeCourse(index, type) {
            if (type === 'jg_kc') {
                selectedCourses.splice(index, 1);
                updateCourseDisplay();
            } else {
                selectedReplacedCourses.splice(index, 1);
                updateReplacedCourseDisplay();
            }
        }

        // 获取模拟课程数据
        function getMockCourses(type) {
            if (type === 'jg_kc') {
                return [
                    { kch: 'CS001', kcm: '计算机程序设计基础', cj: '85' },
                    { kch: 'CS002', kcm: '数据结构与算法', cj: '90' },
                    { kch: 'CS003', kcm: '计算机网络', cj: '88' },
                    { kch: 'CS004', kcm: '操作系统', cj: '92' }
                ];
            } else {
                return [
                    { kch: 'FA001', kcm: '高等数学A', cj: '78' },
                    { kch: 'FA002', kcm: '线性代数', cj: '82' },
                    { kch: 'FA003', kcm: '概率论与数理统计', cj: '85' },
                    { kch: 'FA004', kcm: '大学物理', cj: '80' }
                ];
            }
        }

        // 提交申请
        function submitApplication() {
            // 验证表单
            if (!validateForm()) {
                return;
            }

            const formData = collectFormData();

            if (confirm('提交后只能删除此项申请，不能修改课程的替代关系。是否确认提交?')) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/personalApplication/curriculumReplacement/saveCurriculum",
                    type: "post",
                    data: JSON.stringify(formData),
                    contentType: "application/json",
                    dataType: "json",
                    success: function(response) {
                        if (response.status === 200) {
                            showSuccess("申请提交成功！");
                            setTimeout(() => {
                                goBack();
                            }, 1500);
                        } else {
                            showError(response.msg || "提交失败！");
                        }
                        $('#tokenValue').val(response.token);
                    },
                    error: function() {
                        showError("提交失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 验证表单
        function validateForm() {
            const tdlx = $('input[name="tdlx"]:checked').val();

            if (!tdlx) {
                showError('请选择替代类型！');
                return false;
            }

            if (selectedCourses.length === 0) {
                showError('请选择替代课程！');
                return false;
            }

            if (selectedReplacedCourses.length === 0) {
                showError('请选择被替代课程！');
                return false;
            }

            if (cjtdyy === '1') {
                const tdyym = $('#tdyym').val();
                if (!tdyym) {
                    showError('请选择替代原因！');
                    return false;
                }

                const sgtx = $('#tdyym option:selected').attr('sgtx');
                if (sgtx === '1') {
                    const sqyy = $('#sqyy').val().trim();
                    if (!sqyy) {
                        showError('申请原因不能为空！');
                        return false;
                    }
                }
            } else {
                const sqyy = $('#sqyy').val().trim();
                if (!sqyy) {
                    showError('申请原因不能为空！');
                    return false;
                }
            }

            return true;
        }

        // 收集表单数据
        function collectFormData() {
            const tdlx = $('input[name="tdlx"]:checked').val();
            const kch = selectedCourses.map(c => c.kch).join(',');
            const tdkch = selectedReplacedCourses.map(c => c.kch).join(',');
            const tdyym = cjtdyy === '1' ? $('#tdyym').val() : '';
            const sqyy = $('#sqyy').val().trim();

            return [{
                tdlx: tdlx,
                kch: kch,
                tdkch: tdkch,
                tdyym: tdyym,
                sqyy: sqyy,
                ealUser: '' // 审批人信息，移动端暂不处理
            }];
        }

        // 返回上一页
        function goBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                history.back();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 添加模态框样式
        $('<style>').text(`
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                padding: var(--padding-md);
            }

            .course-option:hover {
                background: var(--bg-tertiary) !important;
            }
        `).appendTo('head');
    </script>
</body>
</html>
