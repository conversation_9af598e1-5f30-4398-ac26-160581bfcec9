<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证信息</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证信息维护页面样式 */
        .studentcard-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .add-application {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .add-btn {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md) var(--padding-lg);
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .add-btn:active {
            background: var(--success-dark);
            transform: scale(0.98);
        }
        
        .applications-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .application-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .application-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .application-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .application-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .application-reason {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-sm);
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .action-btn {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .action-btn:active {
            transform: scale(0.95);
        }
        
        .application-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
        }
        
        .application-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .form-back {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .form-back:active {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .form-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
            height: calc(100vh - 120px);
            overflow-y: auto;
        }
        
        .form-group {
            margin-bottom: var(--margin-lg);
        }
        
        .form-label {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .form-label .required {
            color: var(--error-color);
            margin-right: var(--margin-xs);
        }
        
        .form-input {
            width: 100%;
            min-height: 44px;
            padding: 10px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .form-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-save {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .limit-hint {
            font-size: var(--font-size-small);
            color: var(--error-color);
            margin-top: var(--margin-xs);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证信息</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="studentcard-header">
            <div class="header-title">学生证信息</div>
            <div class="header-subtitle">我的申请信息</div>
        </div>

        <!-- 添加申请 -->
        <div class="add-application">
            <button class="add-btn" onclick="showApplicationForm();">
                <i class="ace-icon fa fa-plus"></i>
                <span>增加新的申请</span>
            </button>
        </div>

        <!-- 申请列表 -->
        <div class="applications-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span>申请记录</span>
                </div>
                <div class="list-count" id="applicationCount">0</div>
            </div>

            <div id="applicationItems">
                <!-- 申请记录将动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 申请表单 -->
    <div class="application-form" id="applicationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeApplicationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">添加申请</div>
        </div>

        <div class="form-content">
            <div class="form-group">
                <div class="form-label">
                    <span class="required">*</span>
                    <span>申请事项</span>
                </div>
                <select class="form-input" id="sqsx" onchange="changeSqsx();">
                    <option value="">--请选择--</option>
                    <option value="01">补办学生证</option>
                    <option value="02">更换磁条</option>
                    <option value="03">录入磁条信息</option>
                    <option value="04">修改乘车区间</option>
                </select>
                <div class="limit-hint" id="limitHint"></div>
            </div>

            <div class="form-group">
                <div class="form-label">
                    <span class="required">*</span>
                    <span>申请原因</span>
                </div>
                <textarea class="form-input" id="sqyy" placeholder="请输入申请原因" style="min-height: 100px; resize: vertical;"></textarea>
            </div>

            <div class="form-group" id="ccqjGroup" style="display: none;">
                <div class="form-label">
                    <span class="required">*</span>
                    <span>乘车区间</span>
                </div>
                <input type="text" class="form-input" id="ccqjInput" placeholder="请输入目的地车站名称">
                <input type="hidden" id="ccqjValue">
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeApplicationForm();">关闭</button>
            <button class="btn-mobile btn-save flex-1" onclick="saveApplication();">保存</button>
        </div>
    </div>

    <script>
        // 全局变量
        let applicationData = [];
        let currentEditData = null;
        let isEditMode = false;
        let schoolCode = '${schoolCode}' || '';
        let bbxszCount = parseInt('${csb.csz}') || 2;
        let ccqjCount = parseInt('${csb.csza}') || 2;

        $(function() {
            initPage();
            loadApplicationData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载申请数据
        function loadApplicationData() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/studentIdCard/search",
                type: "post",
                data: "pageNum=1&pageSize=100",
                dataType: "json",
                success: function(data) {
                    applicationData = data.records || [];
                    renderApplicationData();
                    showLoading(false);
                },
                error: function() {
                    // 使用模拟数据
                    applicationData = [
                        {
                            SQSX: '01',
                            SQYY: '学生证丢失，需要补办',
                            SQRQ: '20240115103000',
                            SPZT: 0,
                            SPYJ: ''
                        },
                        {
                            SQSX: '02',
                            SQYY: '磁条损坏，无法正常使用',
                            SQRQ: '20240110143000',
                            SPZT: 2,
                            SPYJ: '同意更换磁条'
                        }
                    ];
                    renderApplicationData();
                    showLoading(false);
                }
            });
        }

        // 渲染申请数据
        function renderApplicationData() {
            $('#applicationCount').text(applicationData.length);

            const container = $('#applicationItems');
            container.empty();

            if (applicationData.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无申请记录
                    </div>
                `);
                return;
            }

            applicationData.forEach(item => {
                const applicationHtml = createApplicationItem(item);
                container.append(applicationHtml);
            });
        }

        // 创建申请项
        function createApplicationItem(item) {
            const status = parseInt(item.SPZT);
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);
            const typeText = getApplicationTypeText(item.SQSX);
            const dateText = formatDateTime(item.SQRQ);

            let actionsHtml = '';
            if (status === 0) {
                // 待审批状态可以修改和删除
                actionsHtml = `
                    <button class="action-btn btn-edit" onclick="editApplication('${item.SQSX}', '${item.SQRQ}');">
                        <i class="ace-icon fa fa-edit"></i> 修改
                    </button>
                    <button class="action-btn btn-delete" onclick="deleteApplication('${item.SQSX}', '${item.SQRQ}');">
                        <i class="ace-icon fa fa-trash"></i> 删除
                    </button>
                `;
            } else {
                // 其他状态只能查看
                actionsHtml = `
                    <button class="action-btn btn-view" onclick="viewApplication('${item.SQSX}', '${item.SQRQ}');">
                        <i class="ace-icon fa fa-eye"></i> 查看
                    </button>
                `;
            }

            return `
                <div class="application-item ${statusClass}">
                    <div class="application-header">
                        <div class="application-type">${typeText}</div>
                        <div class="application-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="application-details">申请时间：${dateText}</div>
                    <div class="application-reason">${item.SQYY}</div>
                    ${item.SPYJ ? `<div class="application-details">审批意见：${item.SPYJ}</div>` : ''}
                    <div class="application-actions">
                        ${actionsHtml}
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case -1: return 'rejected';
                case 2: return 'approved';
                default: return 'pending';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case -1: return '不批准';
                case 2: return '已批准';
                default: return '待审批';
            }
        }

        // 获取申请类型文本
        function getApplicationTypeText(sqsx) {
            switch(sqsx) {
                case '01': return '补办学生证';
                case '02': return '更换磁条';
                case '03': return '录入磁条信息';
                case '04': return '修改乘车区间';
                default: return '未知类型';
            }
        }

        // 显示申请表单
        function showApplicationForm() {
            isEditMode = false;
            currentEditData = null;
            $('#formTitle').text('添加申请');
            resetForm();
            $('#applicationForm').addClass('show');
        }

        // 关闭申请表单
        function closeApplicationForm() {
            $('#applicationForm').removeClass('show');
            resetForm();
        }

        // 重置表单
        function resetForm() {
            $('#sqsx').val('');
            $('#sqyy').val('');
            $('#ccqjInput').val('');
            $('#ccqjValue').val('');
            $('#ccqjGroup').hide();
            $('#limitHint').text('');
        }

        // 申请事项变化
        function changeSqsx() {
            const sqsx = $('#sqsx').val();
            $('#limitHint').text('');

            if (sqsx === '04') {
                $('#ccqjGroup').show();
                if (schoolCode === '100007') {
                    $('#limitHint').text(`在校期间只能申请${ccqjCount - 1}次`);
                }
            } else {
                $('#ccqjGroup').hide();
                if (sqsx === '01') {
                    $('#limitHint').text(`在校期间只能申请${bbxszCount}次`);
                }
            }
        }

        // 保存申请
        function saveApplication() {
            const sqsx = $('#sqsx').val();
            const sqyy = $('#sqyy').val().trim();
            const ccqj = $('#ccqjValue').val() || $('#ccqjInput').val().trim();

            if (!validateForm(sqsx, sqyy, ccqj)) {
                return;
            }

            const url = isEditMode ?
                "/student/personalManagement/studentIdCard/doEdit" :
                "/student/personalManagement/studentIdCard/doSave";

            let data = `sqsx=${sqsx}&sqyy=${encodeURIComponent(sqyy)}&ccqj=${encodeURIComponent(ccqj)}&tokenValue=${getTokenValue()}`;

            if (isEditMode && currentEditData) {
                data += `&sqrq=${currentEditData.SQRQ}`;
            }

            $.ajax({
                url: url,
                type: "post",
                data: data,
                dataType: "json",
                success: function(data) {
                    if (data.result && data.result.indexOf("/") !== -1) {
                        window.location.href = data.result;
                    } else if (data.result === "ok") {
                        showSuccess('保存成功！');
                        closeApplicationForm();
                        loadApplicationData();
                    } else if (data.result === "error") {
                        showError('当前申请事项已存在，请勿重复申请！');
                    } else {
                        showError('当前申请事项已达上限，不能申请！');
                    }

                    if (data.token) {
                        updateTokenValue(data.token);
                    }
                },
                error: function() {
                    showError('保存失败，请重新添加！');
                }
            });
        }

        // 验证表单
        function validateForm(sqsx, sqyy, ccqj) {
            if (!sqsx) {
                showError('请选择申请事项！');
                return false;
            }

            if (!sqyy) {
                showError('申请原因不能为空！');
                return false;
            }

            if (getStringLength(sqyy) > 100) {
                showError('申请原因字符长度不能超过100！');
                return false;
            }

            if (schoolCode === '100007' && sqsx === '04') {
                if (!ccqj) {
                    showError('请完整填写乘车区间！');
                    return false;
                }
            }

            return true;
        }

        // 编辑申请
        function editApplication(sqsx, sqrq) {
            const item = applicationData.find(app => app.SQSX === sqsx && app.SQRQ === sqrq);
            if (!item) return;

            isEditMode = true;
            currentEditData = item;
            $('#formTitle').text('修改申请');

            $('#sqsx').val(item.SQSX).prop('disabled', true);
            $('#sqyy').val(item.SQYY);

            if (item.SQSX === '04' && item.CCQJ) {
                $('#ccqjGroup').show();
                $('#ccqjValue').val(item.CCQJ);
                $('#ccqjInput').val(item.CCQJ); // 简化处理，实际应该显示城市名称
            }

            changeSqsx();
            $('#applicationForm').addClass('show');
        }

        // 删除申请
        function deleteApplication(sqsx, sqrq) {
            const message = '确定要删除申请？';

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doDeleteApplication(sqsx, sqrq);
                    }
                });
            } else {
                if (confirm(message)) {
                    doDeleteApplication(sqsx, sqrq);
                }
            }
        }

        // 执行删除申请
        function doDeleteApplication(sqsx, sqrq) {
            $.ajax({
                url: "/student/personalManagement/studentIdCard/doDel",
                type: "post",
                data: `sqsx=${sqsx}&sqrq=${sqrq}&tokenValue=${getTokenValue()}`,
                dataType: "json",
                success: function(data) {
                    if (data.result && data.result.indexOf("/") !== -1) {
                        window.location.href = data.result;
                    } else if (data.result === "ok") {
                        showSuccess('删除成功！');
                        loadApplicationData();
                    } else {
                        showError('删除失败！');
                    }

                    if (data.token) {
                        updateTokenValue(data.token);
                    }
                },
                error: function() {
                    showError('删除失败！');
                }
            });
        }

        // 查看申请
        function viewApplication(sqsx, sqrq) {
            const item = applicationData.find(app => app.SQSX === sqsx && app.SQRQ === sqrq);
            if (!item) return;

            let message = `申请详情\n\n`;
            message += `申请事项：${getApplicationTypeText(item.SQSX)}\n`;
            message += `申请时间：${formatDateTime(item.SQRQ)}\n`;
            message += `申请原因：${item.SQYY}\n`;
            message += `审批状态：${getStatusText(parseInt(item.SPZT))}\n`;

            if (item.SPYJ) {
                message += `审批意见：${item.SPYJ}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr || dateTimeStr.length < 14) return '';

            const year = dateTimeStr.substring(0, 4);
            const month = dateTimeStr.substring(4, 6);
            const day = dateTimeStr.substring(6, 8);
            const hour = dateTimeStr.substring(8, 10);
            const minute = dateTimeStr.substring(10, 12);
            const second = dateTimeStr.substring(12, 14);

            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        }

        // 获取字符串长度（中文算2个字符）
        function getStringLength(str) {
            let length = 0;
            for (let i = 0; i < str.length; i++) {
                if ((str.charCodeAt(i) & 0xff00) !== 0) {
                    length++;
                }
                length++;
            }
            return length;
        }

        // 获取Token值
        function getTokenValue() {
            return $('#tokenValue').val() || '${token_in_session}' || '';
        }

        // 更新Token值
        function updateTokenValue(token) {
            if ($('#tokenValue').length === 0) {
                $('body').append(`<input type="hidden" id="tokenValue" value="${token}">`);
            } else {
                $('#tokenValue').val(token);
            }
        }

        // 刷新数据
        function refreshData() {
            loadApplicationData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>

    <!-- 隐藏的Token输入框 -->
    <input type="hidden" id="tokenValue" value="${token_in_session}">
</body>
</html>
