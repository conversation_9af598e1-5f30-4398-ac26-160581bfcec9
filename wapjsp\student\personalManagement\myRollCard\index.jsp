<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学籍卡</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学籍卡页面样式 */
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .card-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .card-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .card-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .card-toolbar {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .toolbar-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .toolbar-title i {
            color: var(--primary-color);
        }
        
        .toolbar-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-action {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-action:hover {
            background: var(--info-dark);
        }
        
        .btn-action:active {
            transform: translateY(1px);
        }
        
        .card-viewer {
            position: relative;
            min-height: 400px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card-embed {
            width: 100%;
            height: 400px;
            border: none;
            background: white;
        }
        
        .loading-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
            color: var(--text-disabled);
            padding: var(--padding-xl);
        }
        
        .loading-placeholder i {
            font-size: 48px;
            color: var(--text-disabled);
        }
        
        .loading-placeholder-text {
            font-size: var(--font-size-base);
            text-align: center;
        }
        
        .error-container {
            background: var(--error-light);
            border: 1px solid var(--error-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--error-dark);
        }
        
        .error-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .error-title i {
            color: var(--error-color);
        }
        
        .info-tips {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
        }
        
        .tips-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tips-title i {
            color: var(--info-color);
        }
        
        .tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .tips-list li {
            padding: 4px 0;
            font-size: var(--font-size-small);
            position: relative;
            padding-left: 16px;
        }
        
        .tips-list li::before {
            content: "•";
            color: var(--info-color);
            position: absolute;
            left: 0;
        }
        
        .action-buttons {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .buttons-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .buttons-title i {
            color: var(--primary-color);
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .action-button:hover {
            background: var(--primary-dark);
        }
        
        .action-button:active {
            transform: translateY(1px);
        }
        
        .action-button.secondary {
            background: var(--success-color);
        }
        
        .action-button.secondary:hover {
            background: var(--success-dark);
        }
        
        @media (max-width: 480px) {
            .toolbar-actions {
                flex-direction: column;
                gap: 4px;
            }
            
            .btn-action {
                font-size: var(--font-size-mini);
                padding: 4px 8px;
            }
            
            .button-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学籍卡</div>
            <div class="navbar-action" onclick="refreshCard();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学籍卡头部 -->
        <div class="card-header">
            <div class="card-title">我的学籍卡</div>
            <div class="card-desc">查看个人学籍信息</div>
        </div>
        
        <!-- 使用提示 -->
        <div class="info-tips">
            <div class="tips-title">
                <i class="ace-icon fa fa-info-circle"></i>
                使用说明
            </div>
            <ul class="tips-list">
                <li>学籍卡包含您的基本学籍信息</li>
                <li>如信息有误，请点击"完善我的信息"进行修改</li>
                <li>学籍卡可用于各种证明材料</li>
                <li>如有问题请联系教务处</li>
            </ul>
        </div>
        
        <!-- 学籍卡容器 -->
        <div class="card-container">
            <div class="card-toolbar">
                <div class="toolbar-title">
                    <i class="ace-icon fa fa-credit-card"></i>
                    学籍卡片
                </div>
                <div class="toolbar-actions">
                    <button class="btn-action" onclick="fixedMyInfo();">
                        <i class="ace-icon fa fa-edit"></i>
                        完善信息
                    </button>
                </div>
            </div>
            
            <div class="card-viewer" id="cardViewer">
                <div class="loading-placeholder" id="loadingPlaceholder">
                    <i class="ace-icon fa fa-file-o"></i>
                    <div class="loading-placeholder-text">正在加载学籍卡...</div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <div class="buttons-title">
                <i class="ace-icon fa fa-cogs"></i>
                相关操作
            </div>
            
            <div class="button-grid">
                <button class="action-button" onclick="fixedMyInfo();">
                    <i class="ace-icon fa fa-edit"></i>
                    完善信息
                </button>
                <button class="action-button secondary" onclick="printCard();">
                    <i class="ace-icon fa fa-print"></i>
                    打印学籍卡
                </button>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        var urlPre = "/student/rollManagement/myRollCard";

        $(function() {
            initPage();
            loadCard();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学籍卡
        function loadCard() {
            try {
                const embedHtml = `<embed src="${urlPre}/report" class="card-embed" type="application/pdf">`;
                $('#cardViewer').html(embedHtml);
                
                // 延迟隐藏加载提示
                setTimeout(function() {
                    $('#loadingPlaceholder').hide();
                }, 2000);
            } catch (error) {
                console.error('加载学籍卡失败:', error);
                showError();
            }
        }

        // 显示错误信息
        function showError() {
            const errorHtml = `
                <div class="loading-placeholder">
                    <i class="ace-icon fa fa-exclamation-triangle" style="color: var(--error-color);"></i>
                    <div class="loading-placeholder-text" style="color: var(--error-color);">
                        学籍卡加载失败<br>
                        <small>请检查网络连接或联系管理员</small>
                    </div>
                </div>
            `;
            $('#cardViewer').html(errorHtml);
        }

        // 完善个人信息
        function fixedMyInfo() {
            const url = "/student/rollManagement/myRollCard/myInfoEdit";
            if (parent && parent.addTab) {
                parent.addTab('完善个人信息', url);
            } else {
                window.location.href = url;
            }
        }

        // 打印学籍卡
        function printCard() {
            try {
                window.open(urlPre + "/report", '_blank');
            } catch (error) {
                alert('打印功能暂时不可用，请稍后再试');
            }
        }

        // 刷新学籍卡
        function refreshCard() {
            $('#loadingPlaceholder').show();
            loadCard();
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
