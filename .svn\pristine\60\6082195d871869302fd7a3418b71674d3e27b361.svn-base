<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
    <title>课程替代</title>
    <c:if test="${mobile}">
	    <style type="text/css">
		    .phone-profile-info-value {
			    padding: 8px 6px;
			}
			
		    .fa {
			    width: 18px !important;
	    		height: 14px !important;
			}
		
	    </style>  
    </c:if> 
</head>
<body>
<div class="row">
    <div class="self-margin col-xs-12">
    	<input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
		<c:if test="${mobile}">
			<h5 class="phone-header smaller lighter grey">
				<i class="ace-icon fa fa-times bigger-130 phone-header-left" onclick="returnIndex();"></i>
				<span class="phone-header-center">课程替代</span>
			</h5>
			<div class="widget-toolbar phone-no-border">		
				<h5 class="smaller lighter phone-header-font" style="color: black;font-weight: bold;">	
					我申请的课程替代
					<span class="right_top_oper">
		                <c:if test="${pxcsb == '1' }">
		                    <button title="增加" class="btn btn-success btn-xs btn-round" onclick="addInfo();return false;">
		                        <i class="ace-icon fa fa-plus bigger-120"></i>增加新的申请
		                    </button>
		                </c:if>
		                <button type="button" class="btn btn-xs btn-round" title="返回" onclick="returnIndex();return false;">
		                    <i class="fa fa-reply bigger-120"></i> 返回
		                </button>
	            	</span>
				</h5>
				
				<div id="kctd_scroll" style="max-height: calc(100vh - 175px);overflow: auto;">
					<div id="kctdtbody"></div>
				</div>
				<div id="urppagebar"></div>
			</div>			
		</c:if>    
		<c:if test="${!mobile}">
	        <h4 class="header smaller lighter grey">
	            <i class="glyphicon glyphicon-list"></i> 我申请的课程替代
	            <span class="right_top_oper">
	                <c:if test="${pxcsb == '1' }">
	                    <button title="增加" class="btn btn-success btn-xs btn-round" onclick="addInfo();return false;">
	                        <i class="ace-icon fa fa-plus bigger-120"></i>增加新的申请
	                    </button>
	                </c:if>
	                <button type="button" class="btn btn-xs btn-round" title="返回" onclick="returnIndex();return false;">
	                    <i class="fa fa-reply bigger-120"></i> 返回
	                </button>
	            </span>
	        </h4>
	        <div class="widget-content widget-box" id="kctd_scroll" style="max-height: calc(100vh - 175px);overflow: auto;">
	            <table class="table table-striped table-bordered" id="kctdTable">
	                <thead>
	                <tr>
	                    <th>操作</th>
	                    <c:if test="${approvalProcess=='1' }">
	                        <th>申请单号</th>
	                    </c:if>
	                    <th>申请状态</th>
	                    <th>申请日期</th>
	                    <th>替代类型</th>
	                    <th style="color: green;">课程</th>
	                    <th style="color: #00000047;text-decoration-line: line-through;">被替代课程</th>
	                </tr>
	                </thead>
	                <tbody id="kctdtbody"></tbody>
	            </table>
	        </div>
	        <div id="urppagebar"></div>
        </c:if>
    </div>
</div>
<script type="text/javascript">
    var page_size = "30_sl";
    var mobile = ${mobile};
    if(mobile){
        page_size = "100000000_sl";
    }

    $(function(){
        getCurriculumList("1",page_size,true);
    });

    //分页查询
    function getCurriculumList(page,pageSizeVal,conditionChanged){
        if(pageSizeVal == undefined){
            pageSizeVal = page_size;
            page = "1";
        }
        var parr = (pageSizeVal+"").split("_");
        var pageSize = parseInt(parr[0]);
        var url = "/student/personalManagement/personalApplication/curriculumReplacement/index/getPage";
        $.ajax({
            url : url,
            cache : false,
            type : "post",
            data : "pageNum=" + page + "&pageSize=" + pageSize,
            dataType : "json",
            success : function(d){
                var data = d.data;
                urp.pagebar("urppagebar", pageSizeVal, page,data["pageContext"].totalCount, getCurriculumList,"on", "kctd_scroll");
                var isScroll = (pageSizeVal+"").indexOf("_")!=-1 && page!=1?true:false;
                if(data["records"] != null && data["records"].length != 0){
                    fillPyfaTable(data["records"],isScroll,page,pageSize);
                }else{
                    fillPyfaTable(null,isScroll,page,pageSize);
                }
            },
            error : function(xhr){
                urp.alert("错误代码["+xhr.readyState+"-"+xhr.status+"]:获取数据失败！");
            }
        });
    }

    //数据显示
    function fillPyfaTable(data,isScroll,page,pageSize){
        var tcont = "";
        if(data != null){
            $.each(data,function(i,v){
                var tableId = "";
                if(isScroll){
                    tableId = (page-1)*pageSize+1+i;
                }else{
                    tableId = i+1;
                }
                if(mobile){
                	var status = "";
                	if("${approvalProcess}"=="1"){
                        if(v.SQZT == -1){
                            status = "拒绝<";
                        }else if(v.SQZT == -2){
                            status = "取消[终审后]";
                        }else if(v.SQZT == -3){
                            status = "撤回[终审前]";
                        }else if(v.SQZT == 0){
                            status = "暂存";
                        }else if(v.SQZT == 1){
                            status = "待审批";
                        }else if(v.SQZT == 2){
                            status = "审批中";
                        }else if(v.SQZT == 3){
                            status = "批准";
                        }               	
                	}else{
                        //申请状态(-1不批准0待审批1审批中2已批准)
                        if(v.SQZT == -1){
                            status = "不批准";
                        }else if(v.SQZT == -2){
                            status = "已撤回";
                        }else if(v.SQZT == 0){
                            status = "待审批";
                        }else if(v.SQZT == 1){
                            status = "审批中";
                        }else if(v.SQZT == 2){
                            status = "已批准";
                        }               	
                	}
                    tcont += "<div class=\"phone-message-item\">";
 		            tcont += "<h5 style=\"margin-top: 0px; border-bottom: 1px solid white;\"><font>#"+tableId+"</font><span style=\"float: right; position: relative; bottom: 5px; border-radius: 20px; background: #3eabe1;\" class=\"label\">";
				    tcont += status+"</span></h5>";                    
                    tcont += "<p><i class=\"ace-icon fa fa-barcode bigger-110\"></i>&nbsp;" + (v.SQBH == null ? "" : v.SQBH) + " </p> ";
                    tcont += "<p class=\"inline-blocks\"><i class=\"ace-icon fa fa-check bigger-110 inline-block\"></i>&nbsp;<span class=\"inline-block\">" + (v.KCM == null ? "" : v.KCM.replace(/;/g, "<br>")) + "</span></p> ";
                    tcont += "<p class=\"inline-blocks\"><i class=\"ace-icon fa fa-mortar-board bigger-110 inline-block\"></i>&nbsp;<span class=\"inline-block\">" + (v.TDKCM == null ? "" : v.TDKCM.replace(/;/g, "<br>")) + "</span></p> ";
                    tcont += "<p><i class=\"ace-icon fa fa-clock-o bigger-120\"></i>&nbsp;" + (v.SQRQ == null ? "" : v.SQRQ) + " </p> ";
                    tcont += "<p><i class=\"ace-icon fa fa-file-text-o bigger-110\"></i>&nbsp;" + (v.SQYY == null ? (v.TDYY == null ? "" : v.TDYY) : v.SQYY) + " </p> ";
                    tcont += " <div class=\"hr hr8 hr-dotted white\" style='border-top: 1px solid #FFF;'></div>";
                    tcont += " <span style=\"margin: 0px;height: 17px; display: block;\"> ";
                    tcont += " <span class=\"label label-lg label-yellow arrowed-in-right arrowed-in\">" + (v.TDLXSM == null ? "" : v.TDLXSM) + "</span>";
                    if("${approvalProcess}"=="1"){
                        if('${pxcsb}' == "1"){
                            if(v.SQZT == 0){
                                tcont += "<button class='btn btn-xs btn-danger btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                                    "border-bottom-width: 1px;border: 1px;' onclick='revokeInfo(\""+v.SQBH+"\");'>删除</button>";
                            }else if("${schoolCode}"!="100027"&&"${schoolCode}"!="100053"&&(v.SQZT == 1||v.SQZT == 2)){
                                tcont += "<button class='btn btn-xs btn-danger btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                                    "border-bottom-width: 1px;border: 1px;' onclick='doRevoke(\""+v.SQBH+"\",\"撤回\");'>撤回</button>";
                            }else if("${schoolCode}"!="100027"&&"${schoolCode}"!="100053"&&v.SQZT == 3){
                                tcont += "<button class='btn btn-xs btn-danger btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                                    "border-bottom-width: 1px;border: 1px;' onclick='doRevoke(\""+v.SQBH+"\",\"取消\");'>取消</button>";
                            }
                        }
                        tcont += "<button class='btn btn-xs btn-white btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                            "border-bottom-width: 1px;border: 1px;' onclick='seeInfo(\""+v.SQBH+"\");'>查看</button>";
                    }else{
                        if('${pxcsb}' == "1"){
                            if(v.SQZT == 0){
                                tcont += "<button class='btn btn-xs btn-danger btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                                    "border-bottom-width: 1px;border: 1px;' onclick='revokeInfo(\""+v.SQBH+"\");'>删除</button>";
                            }else if("${schoolCode}"!="100027"&&"${schoolCode}"!="100053"&&v.SQZT == 2){
                                tcont += "<button class='btn btn-xs btn-danger btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                                    "border-bottom-width: 1px;border: 1px;' onclick='doRevoke(\""+v.SQBH+"\",\"撤回\");'>撤回</button>";
                            }
                        }
                        if(v.SQZT == 1 || v.SQZT == 2 || v.SQZT == -1|| v.SQZT == -2){
                            tcont += "<button class='btn btn-xs btn-white btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                                "border-bottom-width: 1px;border: 1px;' onclick='seeInfo(\""+v.SQBH+"\");'>查看</button>";
                        }

                    }
                    tcont += " </span>";
                    tcont += " </div>";

                }else{
                    tcont += "<tr>";
                    var status = "";
                    if("${approvalProcess}"=="1"){
                        tcont += "<td>";
                        tcont += "<a style='cursor:pointer;' class='blue' title='查看' onclick='seeInfo(\""+v.SQBH+"\");'><i class='ace-icon fa fa-eye bigger-130'></i></a>&nbsp;&nbsp;";
                        if('${pxcsb}' == "1"){
                            if(v.SQZT == 0){
                                //tcont += "<a style='color:green;cursor:pointer;' title='修改' onclick='updateInfo(\""+v.SQBH+"\");'><i class='ace-icon fa fa-pencil-square-o purple bigger-130'></i></a>&nbsp;";
                                tcont += "<a style='cursor:pointer;' class='red' title='删除' onclick='revokeInfo(\""+v.SQBH+"\");'><i class='ace-icon fa fa-trash-o bigger-130'></i></a>&nbsp;&nbsp;";
                                //tcont += "<button style='cursor:pointer;' class='btn btn-purple btn-xs btn-round' title='提交审批' onclick='doSave(\""+v.SQBH+"\");'><i class='ace-icon fa fa-check bigger-120'></i> 提交</button>&nbsp;";
                            }else if("${schoolCode}"!="100027"&&"${schoolCode}"!="100053"&&(v.SQZT == 1||v.SQZT == 2)){
                                tcont += "<a style='cursor:pointer;' class='red' title='撤回' onclick='doRevoke(\""+v.SQBH+"\",\"撤回\");'><i class='ace-icon fa fa-reply bigger-130'></i></a>";
                            }else if("${schoolCode}"!="100027"&&"${schoolCode}"!="100053"&&v.SQZT == 3){
                                tcont += "<a style='cursor:pointer;' class='red' title='取消' onclick='doRevoke(\""+v.SQBH+"\",\"取消\");'><i class='ace-icon fa fa-reply bigger-130'></i></a>";
                            }
                        }
                        tcont += "</td>";
                        tcont += "<td>"+(v.SQBH == null ? "" : v.SQBH)+"</td>";
                        //申请状态(-2取消[终审后]-3撤回[终审前]-1拒绝0暂存1待审批2审批中3批准)
                        if(v.SQZT == -1){
                            status = "<span class='label label-lg label-grey arrowed'>拒绝</span>";
                        }else if(v.SQZT == -2){
                            status = "<span class='label label-lg label-grey arrowed'>取消[终审后]</span>";
                        }else if(v.SQZT == -3){
                            status = "<span class='label label-lg label-grey arrowed'>撤回[终审前]</span>";
                        }else if(v.SQZT == 0){
                            status = "<span class='label label-xlg label-yellow arrowed arrowed-right'>暂存</span>";
                        }else if(v.SQZT == 1){
                            status = "<span class='label label-xlg label-yellow arrowed arrowed-right'>待审批</span>";
                        }else if(v.SQZT == 2){
                            status = "<span class='label label-xlg label-primary arrowed arrowed-right'>审批中</span>";
                        }else if(v.SQZT == 3){
                            status = "<span class='label label-lg label-success arrowed'>批准</span>";
                        }
                    }else{
                        tcont += "<td>";
                        if(v.SQZT == 1 || v.SQZT == 2 || v.SQZT == -1|| v.SQZT == -2){
                            tcont += "<a style='cursor:pointer;' class='blue' title='查看' onclick='seeInfo(\""+v.SQBH+"\");'><i class='ace-icon fa fa-eye bigger-130'></i></a>";
                        }
                        if('${pxcsb}' == "1"){
                            if(v.SQZT == 0){
                                //tcont += "<a style='color:green;cursor:pointer;' title='修改' onclick='updateInfo(\""+v.SQBH+"\");'><i class='ace-icon fa fa-pencil-square-o purple bigger-130'></i></a>&nbsp;";
                                tcont += "<a style='cursor:pointer;' class='red' title='删除' onclick='revokeInfo(\""+v.SQBH+"\");'><i class='ace-icon fa fa-trash-o bigger-130'></i></a>";
                                //tcont += "<button style='cursor:pointer;' class='btn btn-purple btn-xs btn-round' title='提交审批' onclick='doSave(\""+v.SQBH+"\");'><i class='ace-icon fa fa-check bigger-120'></i> 提交</button>&nbsp;";
                            }else if("${schoolCode}"!="100027"&&"${schoolCode}"!="100053"&&v.SQZT == 2){
                                tcont += "&nbsp;&nbsp;<a style='cursor:pointer;' class='red' title='撤回' onclick='doRevoke(\""+v.SQBH+"\",\"撤回\");'><i class='ace-icon fa fa-reply bigger-130'></i></a>";
                            }
                        }
                        tcont += "</td>";
                        //申请状态(-1不批准0待审批1审批中2已批准)
                        if(v.SQZT == -1){
                            status = "<span class='label label-lg label-grey arrowed'>不批准</span>";
                        }
                        if(v.SQZT == -2){
                            status = "<span class='label label-lg label-grey arrowed'>已撤回</span>";
                        }
                        if(v.SQZT == 0){
                            status = "<span class='label label-xlg label-yellow arrowed arrowed-right'>待审批</span>";
                        }
                        if(v.SQZT == 1){
                            status = "<span class='label label-xlg label-primary arrowed arrowed-right'>审批中</span>";
                        }
                        if(v.SQZT == 2){
                            status = "<span class='label label-lg label-success arrowed'>已批准</span>";
                        }
                    }

                    tcont += "<td>"+status+"</td>";
                    tcont += "<td>"+(v.SQRQ == null ? "" : v.SQRQ)+"</td>";
                    tcont += "<td>"+(v.TDLXSM == null ? "" : v.TDLXSM)+"</td>";
                    tcont += "<td>"+v.KCM.replace(/;/g, "<br>")+"</td>";
                    tcont += "<td>"+v.TDKCM.replace(/;/g, "<br>")+"</td>";
                    tcont += "</tr>";
                }



            });
        }
        if(isScroll){
            $("#kctdtbody").append(tcont);
        }else{
            $("#kctdtbody").html(tcont);
        }
    }

    function addslidersModel(id, width) {
        var modal = '<div id="' + id + '" class="modal right fade" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" tabindex="-1">\
			            <div class="modal-dialog">\
			                <div class="modal-content">\
			                    <div class="center">\
		                            <img src="/img/icon/pageloading.gif" style="width:28px;height:28px;">\
		                        </div>\
			                </div>\
			            </div>\
			        </div>';
        var modal = $(modal).appendTo('body');
        $(".modal-dialog").css("width", width);
        return modal;
    }

    //添加数据
    function addInfo() {
        if('${pxcsb}' == "1"){
            location.href = "/student/personalManagement/personalApplication/curriculumReplacement/addInfo";
        }else{
            urp.alert("课程替代申请开关已关闭,请打开后申请!");
        }

    }

    //修改数据
    function updateInfo(sqbh) {
        if('${pxcsb}' == "1"){
            location.href = "/student/personalManagement/personalApplication/curriculumReplacement/updateInfo?sqbh=" + sqbh;
        }else{
            urp.alert("课程替代申请开关已关闭,请打开后修改!");
        }
    }

    //提交申请
    function doSave(sqbh){
        urp.confirm("提交后不可修改或删除，是否确认提交?",callback);
        function callback(f){
            if(!f){
                return false;
            }else{
                $.ajax({
                    url: "/student/personalManagement/personalApplication/curriculumReplacement/doSave",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function (data) {
                        if(data.status != 200){
                            layer.alert(data.msg, {icon: 5,time: 3000});
                        } else {
                            if(data.data["result"].indexOf("/")!=-1){
                                window.location.href = data.data["result"];
                            }else{
                                if(data.data["result"] == "ok"){
                                    urp.alert("提交成功！");
                                    getCurriculumList(1,page_size,true);
                                }
                            }
                            $("#tokenValue").val(data.data["token"]);
                        }
                    },
                    error: function () {
                        urp.alert("提交失败！");
                    }
                });
            }
        }
    }

    function revokeInfo(sqbh){
        urp.confirm("确定要删除当前申请？",callback);
        function callback(f){
            if(!f){
                return false;
            }else{
                $.ajax({
                    url: "/student/personalManagement/personalApplication/curriculumReplacement/revokeInfo",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    beforeSend: function () {
                        $("#loading-btn").attr("data-loading-text", "正在删除...");
                        $("#loading-btn").button('loading');
                    },
                    complete: function () {
                        $("#loading-btn").removeAttr("data-loading-text");
                        $("#loading-btn").button('reset');
                    },
                    success: function (data) {
                        if(data.status != 200){
                            layer.alert(data.msg, {icon: 5,time: 3000});
                        } else {
                            if(data.data["result"].indexOf("/")!=-1){
                                window.location.href = data.data["result"];
                            }else{
                                if(data.data["result"] == "ok"){
                                    urp.alert("删除成功！");
                                    getCurriculumList(1,page_size,true);
                                }else{
                                    urp.alert(data.data["result"]);
                                }
                            }
                            $("#tokenValue").val(data.data["token"]);
                        }
                    },
                    error: function () {
                        urp.alert("删除失败！");
                    }
                });
            }
        }
    }

    //撤回
    function doRevoke(sqbh,sm){
        urp.confirm("是否确认"+sm+"当前申请？",callback);
        function callback(f){
            if(!f){
                return false;
            }else{
                var index;
                index=layer.open({
                    type : 1,
                    title : sm+"原因",
                    shadeClose : true,
                    skin : 'yourclass',
                    content : "<textarea maxlength='150' id='cxyy' style='height: 300px;' class='autosize-transition form-control'></textarea>",
                    btnAlign : 'c',
                    area : [ '500px', '400px' ],
                    btn : [ '确定', '关闭' ],
                    btn1 : function(index, layero) {
                        var cxyy = $("#cxyy").val();
                        if (!cxyy) {
                            urp.alert(sm+"原因不能为空！");
                        } else {
                            layer.close(index);
                            $.ajax({
                                url : "/student/personalManagement/personalApplication/curriculumReplacement/doRevoke",
                                type : "post",
                                data : "sqbh=" + sqbh+ "&cxyy=" + cxyy+ "&tokenValue=" + $("#tokenValue").val(),
                                dataType : "json",
                                beforeSend: function () {
                                    index = layer.load(0, {
                                        shade: [0.2, "#000"] //0.1透明度的白色背景
                                    });
                                },
                                complete: function () {
                                    layer.close(index);
                                },
                                success : function(data) {
                                    if (data["result"].indexOf("/") != -1) {
                                        window.location.href = data["result"];
                                    } else {
                                        if (data["result"] == "0") {
                                            urp.alert("操作成功！");
                                            getCurriculumList(1,page_size,true);
                                        }else{
                                            urp.alert(data["result"]);
                                        }
                                    }
                                    $("#tokenValue").val(data["token"]);
                                },
                                error : function() {
                                    urp.alert("操作失败！");
                                }
                            });
                        }
                    }
                });

            }
        }
    }


    //查看数据
    function seeInfo(applyId) {
        if("${approvalProcess}"=="1"){
	        if(mobile){
	        	location.href = "/phone/student/application/index/seeInfo?applyId=" + applyId+"&applyType=10001";
	        }else{
	            var url = "/student/application/index/seeInfo?applyId=" + applyId+"&applyType=10001";
	            var modal = addslidersModel("look_model", "60%");
	            modal.modal({
	                remote: url
	            }).on('hide.bs.modal', function () {
	                modal.remove();
	            });
	        }
        }else{
            location.href = "/student/personalManagement/personalApplication/curriculumReplacement/seeInfo?sqbh=" + applyId;
        }
    }

    function returnIndex(){
        window.location.href = "/student/application/index";
    }
</script>
</body>
</html>