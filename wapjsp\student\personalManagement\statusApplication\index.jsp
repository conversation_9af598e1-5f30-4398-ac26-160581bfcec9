<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学籍异动申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学籍异动申请页面样式 */
        .application-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .application-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .type-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .type-item:last-child {
            border-bottom: none;
        }
        
        .type-item:active {
            background: var(--bg-color-active);
        }
        
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .type-icon.suspend {
            background: var(--warning-color);
        }
        
        .type-icon.resume {
            background: var(--success-color);
        }
        
        .type-icon.transfer {
            background: var(--info-color);
        }
        
        .type-icon.withdraw {
            background: var(--error-color);
        }
        
        .type-icon.change {
            background: var(--primary-color);
        }
        
        .type-content {
            flex: 1;
        }
        
        .type-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .type-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-restricted {
            background: var(--warning-color);
            color: white;
        }
        
        .status-unavailable {
            background: var(--text-disabled);
            color: white;
        }
        
        .my-applications {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .applications-title {
            display: flex;
            align-items: center;
        }
        
        .applications-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .applications-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .application-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .application-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .application-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .application-item.processing {
            border-left: 4px solid var(--info-color);
        }
        
        .application-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .status-processing {
            background: var(--info-color);
            color: white;
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .application-progress {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
        }
        
        .progress-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .progress-text {
            color: var(--text-secondary);
        }
        
        .application-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .application-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .form-upload:hover {
            border-color: var(--primary-color);
            background: var(--bg-tertiary);
        }
        
        .upload-icon {
            font-size: var(--font-size-h2);
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-list {
            margin-top: var(--margin-sm);
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }
        
        .file-item:last-child {
            margin-bottom: 0;
        }
        
        .file-icon {
            margin-right: var(--margin-sm);
            color: var(--primary-color);
        }
        
        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学籍异动申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="application-header">
            <div class="header-title">学籍异动申请</div>
            <div class="header-subtitle">办理休学、复学、转专业等学籍变更</div>
        </div>

        <!-- 申请类型 -->
        <div class="application-types">
            <div class="type-item" onclick="showApplicationForm('suspend')">
                <div class="type-icon suspend">
                    <i class="ace-icon fa fa-pause"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">休学申请</div>
                    <div class="type-desc">因病、因事等原因暂停学业</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showApplicationForm('resume')">
                <div class="type-icon resume">
                    <i class="ace-icon fa fa-play"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">复学申请</div>
                    <div class="type-desc">休学期满恢复学业</div>
                </div>
                <div class="type-status status-restricted">有条件</div>
            </div>

            <div class="type-item" onclick="showApplicationForm('transfer')">
                <div class="type-icon transfer">
                    <i class="ace-icon fa fa-exchange"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">转专业申请</div>
                    <div class="type-desc">转入其他专业学习</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showApplicationForm('withdraw')">
                <div class="type-icon withdraw">
                    <i class="ace-icon fa fa-sign-out"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">退学申请</div>
                    <div class="type-desc">主动申请退学离校</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showApplicationForm('change')">
                <div class="type-icon change">
                    <i class="ace-icon fa fa-edit"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">学籍信息变更</div>
                    <div class="type-desc">修改姓名、身份证等信息</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>
        </div>

        <!-- 我的申请 -->
        <div class="my-applications">
            <div class="applications-header">
                <div class="applications-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的申请</span>
                </div>
                <div class="applications-count" id="applicationsCount">0</div>
            </div>

            <div id="applicationsList">
                <!-- 申请列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-o"></i>
            <div id="emptyMessage">暂无申请记录</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 申请表单 -->
    <div class="application-form" id="applicationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeApplicationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">学籍异动申请</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">申请类型</div>
                    <input type="text" class="form-input" id="applicationType" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业班级</div>
                    <input type="text" class="form-input" id="majorClass" readonly>
                </div>
            </div>

            <!-- 申请信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-group" id="reasonGroup">
                    <div class="form-label required">申请原因</div>
                    <select class="form-input" id="applicationReason">
                        <option value="">请选择申请原因</option>
                    </select>
                </div>

                <div class="form-group" id="targetMajorGroup" style="display: none;">
                    <div class="form-label required">目标专业</div>
                    <select class="form-input" id="targetMajor">
                        <option value="">请选择目标专业</option>
                    </select>
                </div>

                <div class="form-group" id="effectiveDateGroup">
                    <div class="form-label required">生效日期</div>
                    <input type="date" class="form-input" id="effectiveDate">
                </div>

                <div class="form-group">
                    <div class="form-label required">详细说明</div>
                    <textarea class="form-input form-textarea" id="detailDescription"
                              placeholder="请详细说明申请原因和具体情况..."></textarea>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">家长联系电话</div>
                    <input type="tel" class="form-input" id="parentPhone" placeholder="请输入家长联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">联系地址</div>
                    <textarea class="form-input" id="contactAddress" rows="2"
                              placeholder="请输入详细联系地址"></textarea>
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-paperclip"></i>
                    <span>附件材料</span>
                </div>

                <div class="form-group">
                    <div class="form-label">上传附件</div>
                    <div class="form-upload" onclick="selectFiles()">
                        <div class="upload-icon">
                            <i class="ace-icon fa fa-cloud-upload"></i>
                        </div>
                        <div class="upload-text">点击上传相关证明材料</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                    <div class="file-list" id="fileList">
                        <!-- 文件列表将动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeApplicationForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let myApplications = [];
        let currentApplicationType = '';
        let uploadedFiles = [];
        let studentInfo = {};

        $(function() {
            initPage();
            loadStudentInfo();
            loadMyApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFileUpload();
        }

        // 绑定文件上传
        function bindFileUpload() {
            $('#fileInput').change(function() {
                handleFileSelect(this.files);
            });
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/personalManagement/statusApplication/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 加载我的申请
        function loadMyApplications() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/statusApplication/getMyApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myApplications = data.applications || [];
                    renderApplicationsList();
                    updateApplicationsCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载申请列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationsList() {
            const container = $('#applicationsList');
            container.empty();

            if (myApplications.length === 0) {
                showEmptyState('暂无申请记录');
                return;
            } else {
                hideEmptyState();
            }

            myApplications.forEach(application => {
                const applicationHtml = createApplicationItem(application);
                container.append(applicationHtml);
            });
        }

        // 创建申请项
        function createApplicationItem(application) {
            const statusClass = getStatusClass(application.status);
            const statusText = getStatusText(application.status);

            return `
                <div class="application-item ${statusClass}" onclick="showApplicationDetail('${application.id}')">
                    <div class="application-basic">
                        <div class="application-title">${getApplicationTypeText(application.type)}</div>
                        <div class="application-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="application-details">
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(application.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>生效日期:</span>
                            <span>${formatDate(application.effectiveDate)}</span>
                        </div>
                        <div class="detail-item">
                            <span>申请原因:</span>
                            <span>${application.reason}</span>
                        </div>
                        <div class="detail-item">
                            <span>当前状态:</span>
                            <span>${statusText}</span>
                        </div>
                    </div>
                    <div class="application-progress">
                        <div class="progress-title">处理进度</div>
                        <div class="progress-text">${application.progressText || '等待处理'}</div>
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'pending';
                case 'processing': return 'processing';
                case 'approved': return 'approved';
                case 'rejected': return 'rejected';
                default: return 'pending';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'processing': return '审核中';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 获取申请类型文本
        function getApplicationTypeText(type) {
            switch(type) {
                case 'suspend': return '休学申请';
                case 'resume': return '复学申请';
                case 'transfer': return '转专业申请';
                case 'withdraw': return '退学申请';
                case 'change': return '学籍信息变更';
                default: return '学籍异动申请';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示申请表单
        function showApplicationForm(type) {
            currentApplicationType = type;

            // 设置表单标题
            $('#formTitle').text(getApplicationTypeText(type));
            $('#applicationType').val(getApplicationTypeText(type));

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 加载申请原因选项
            loadApplicationReasons(type);

            // 根据类型显示/隐藏特定字段
            toggleFormFields(type);

            // 清空表单
            resetForm();

            // 显示表单
            $('#applicationForm').addClass('show');
        }

        // 加载申请原因选项
        function loadApplicationReasons(type) {
            $.ajax({
                url: "/student/personalManagement/statusApplication/getApplicationReasons",
                type: "post",
                data: { applicationType: type },
                dataType: "json",
                success: function(data) {
                    const reasons = data.reasons || [];
                    renderReasonOptions(reasons);
                },
                error: function() {
                    console.log('加载申请原因失败');
                }
            });
        }

        // 渲染原因选项
        function renderReasonOptions(reasons) {
            const select = $('#applicationReason');
            select.find('option:not(:first)').remove();

            reasons.forEach(reason => {
                select.append(`<option value="${reason.code}">${reason.name}</option>`);
            });
        }

        // 切换表单字段
        function toggleFormFields(type) {
            // 隐藏所有特殊字段
            $('#targetMajorGroup').hide();

            // 根据类型显示特定字段
            if (type === 'transfer') {
                $('#targetMajorGroup').show();
                loadTargetMajors();
            }
        }

        // 加载目标专业
        function loadTargetMajors() {
            $.ajax({
                url: "/student/personalManagement/statusApplication/getTargetMajors",
                type: "post",
                dataType: "json",
                success: function(data) {
                    const majors = data.majors || [];
                    renderMajorOptions(majors);
                },
                error: function() {
                    console.log('加载专业列表失败');
                }
            });
        }

        // 渲染专业选项
        function renderMajorOptions(majors) {
            const select = $('#targetMajor');
            select.find('option:not(:first)').remove();

            majors.forEach(major => {
                select.append(`<option value="${major.id}">${major.name}</option>`);
            });
        }

        // 重置表单
        function resetForm() {
            $('#applicationReason').val('');
            $('#targetMajor').val('');
            $('#effectiveDate').val('');
            $('#detailDescription').val('');
            $('#contactPhone').val('');
            $('#parentPhone').val('');
            $('#contactAddress').val('');
            uploadedFiles = [];
            renderFileList();
        }

        // 关闭申请表单
        function closeApplicationForm() {
            $('#applicationForm').removeClass('show');
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件选择
        function handleFileSelect(files) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (validateFile(file)) {
                    uploadedFiles.push({
                        id: Date.now() + i,
                        file: file,
                        name: file.name,
                        size: file.size
                    });
                }
            }
            renderFileList();
        }

        // 验证文件
        function validateFile(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['application/pdf', 'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'image/jpeg', 'image/jpg', 'image/png'];

            if (file.size > maxSize) {
                showError('文件大小不能超过10MB');
                return false;
            }

            if (!allowedTypes.includes(file.type)) {
                showError('只支持PDF、Word文档和图片格式');
                return false;
            }

            return true;
        }

        // 渲染文件列表
        function renderFileList() {
            const container = $('#fileList');
            container.empty();

            uploadedFiles.forEach(fileItem => {
                const fileHtml = `
                    <div class="file-item">
                        <i class="file-icon ace-icon fa fa-file"></i>
                        <span class="file-name">${fileItem.name}</span>
                        <i class="file-remove ace-icon fa fa-times" onclick="removeFile('${fileItem.id}')"></i>
                    </div>
                `;
                container.append(fileHtml);
            });
        }

        // 移除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id != fileId);
            renderFileList();
        }

        // 保存草稿
        function saveDraft() {
            if (!validateForm(false)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交申请
        function submitApplication() {
            if (!validateForm(true)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = false;

            const message = `确定要提交${getApplicationTypeText(currentApplicationType)}吗？\n\n提交后将进入审核流程，请确保信息准确无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitFormData(formData, '申请提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    submitFormData(formData, '申请提交成功');
                }
            }
        }

        // 收集表单数据
        function collectFormData() {
            return {
                applicationType: currentApplicationType,
                reason: $('#applicationReason').val(),
                targetMajor: $('#targetMajor').val(),
                effectiveDate: $('#effectiveDate').val(),
                detailDescription: $('#detailDescription').val(),
                contactPhone: $('#contactPhone').val(),
                parentPhone: $('#parentPhone').val(),
                contactAddress: $('#contactAddress').val(),
                attachments: uploadedFiles
            };
        }

        // 验证表单
        function validateForm(isSubmit) {
            if (!$('#applicationReason').val()) {
                showError('请选择申请原因');
                return false;
            }

            if (currentApplicationType === 'transfer' && !$('#targetMajor').val()) {
                showError('请选择目标专业');
                return false;
            }

            if (!$('#effectiveDate').val()) {
                showError('请选择生效日期');
                return false;
            }

            if (!$('#detailDescription').val().trim()) {
                showError('请填写详细说明');
                return false;
            }

            if (!$('#contactPhone').val().trim()) {
                showError('请填写联系电话');
                return false;
            }

            if (isSubmit && uploadedFiles.length === 0) {
                showError('请上传相关证明材料');
                return false;
            }

            return true;
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            $.ajax({
                url: "/student/personalManagement/statusApplication/submitApplication",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        closeApplicationForm();
                        loadMyApplications();
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示申请详情
        function showApplicationDetail(applicationId) {
            const application = myApplications.find(app => app.id === applicationId);
            if (!application) return;

            let message = `申请详情\n\n`;
            message += `申请类型：${getApplicationTypeText(application.type)}\n`;
            message += `申请时间：${formatDate(application.createTime)}\n`;
            message += `生效日期：${formatDate(application.effectiveDate)}\n`;
            message += `申请原因：${application.reason}\n`;
            message += `详细说明：${application.description}\n`;
            message += `当前状态：${getStatusText(application.status)}\n`;

            if (application.reviewComment) {
                message += `审核意见：${application.reviewComment}\n`;
            }

            if (application.reviewTime) {
                message += `审核时间：${formatDate(application.reviewTime)}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 更新申请数量
        function updateApplicationsCount() {
            $('#applicationsCount').text(myApplications.length);
        }

        // 刷新数据
        function refreshData() {
            loadMyApplications();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.my-applications').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.my-applications').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.my-applications').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#applicationForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#applicationForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeApplicationForm();
            }
        });

        $('#applicationForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
