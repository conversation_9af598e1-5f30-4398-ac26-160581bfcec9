<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学科竞赛信息</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学科竞赛信息页面样式 */
        .competition-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .competition-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .competition-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
            margin-right: var(--margin-sm);
        }
        
        .competition-index {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .competition-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .award-level {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .award-level.national {
            background: var(--error-color);
            color: white;
        }
        
        .award-level.provincial {
            background: var(--warning-color);
            color: white;
        }
        
        .award-level.municipal {
            background: var(--info-color);
            color: white;
        }
        
        .award-level.school {
            background: var(--success-color);
            color: white;
        }
        
        .award-level.other {
            background: var(--text-disabled);
            color: white;
        }
        
        .award-grade {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .award-grade.first {
            background: var(--error-color);
            color: white;
        }
        
        .award-grade.second {
            background: var(--warning-color);
            color: white;
        }
        
        .award-grade.third {
            background: var(--info-color);
            color: white;
        }
        
        .award-grade.other {
            background: var(--success-color);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
            padding-top: var(--padding-sm);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-action {
            flex: 1;
            min-height: 36px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-view-certificate {
            background: var(--primary-color);
            color: white;
        }
        
        .participants-info {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .participants-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .certificate-preview {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            display: none;
            flex-direction: column;
        }
        
        .preview-header {
            background: var(--bg-primary);
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--divider-color);
        }
        
        .preview-title {
            font-size: var(--font-size-h4);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .btn-close {
            background: var(--text-disabled);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .preview-content {
            flex: 1;
            background: white;
            overflow: auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-embed {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        @media (max-width: 480px) {
            .competition-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学科竞赛信息</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 竞赛列表 -->
        <div id="competitionList">
            <!-- 动态加载内容 -->
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-trophy"></i>
            <div>暂无学科竞赛记录</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 证书预览 -->
        <div class="certificate-preview" id="certificatePreview">
            <div class="preview-header">
                <div class="preview-title">获奖证书</div>
                <button class="btn-close" onclick="closeCertificatePreview();">
                    <i class="ace-icon fa fa-times"></i>
                    <span>关闭</span>
                </button>
            </div>
            <div class="preview-content">
                <embed class="preview-embed" id="previewEmbed" src="" type="application/pdf">
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let competitionData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;

        $(function() {
            initPage();
            loadData(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载数据
        function loadData(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            $.ajax({
                url: "/student/subjectCompetition/queryPageList",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize + "&choose=2",
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            competitionData = data.records;
                        } else {
                            competitionData = competitionData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        hasMore = competitionData.length < totalCount;
                        renderCompetitionList();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            competitionData = [];
                            renderCompetitionList();
                        }
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染竞赛列表
        function renderCompetitionList() {
            const container = $('#competitionList');
            container.empty();

            competitionData.forEach(function(item, index) {
                const itemHtml = createCompetitionItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建竞赛项目HTML
        function createCompetitionItem(item, index) {
            const awardLevelClass = getAwardLevelClass(item.HJJB);
            const awardGradeClass = getAwardGradeClass(item.HJDJ);

            return `
                <div class="competition-item">
                    <div class="competition-header">
                        <div class="competition-title">${item.JSMC || '竞赛名称'}</div>
                        <div class="competition-index">#${index + 1}</div>
                    </div>

                    <div class="competition-details">
                        <div class="detail-item">
                            <span class="detail-label">所属赛事</span>
                            <span class="detail-value">${item.SSMC || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">获奖时间</span>
                            <span class="detail-value">${item.HJSJ || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">获奖等级</span>
                            <span class="detail-value">
                                <span class="award-grade ${awardGradeClass}">${item.HJDJ || '-'}</span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">获奖级别</span>
                            <span class="detail-value">
                                <span class="award-level ${awardLevelClass}">${item.HJJB || '-'}</span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">申请人</span>
                            <span class="detail-value">${item.SQR || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">指导教师</span>
                            <span class="detail-value">${item.ZDJS || '-'}</span>
                        </div>
                    </div>

                    ${item.HJXS ? `
                        <div class="participants-info">
                            <div class="participants-label">获奖学生：</div>
                            <div>${item.HJXS}</div>
                        </div>
                    ` : ''}

                    <div class="action-buttons">
                        <button class="btn-action btn-view-certificate" onclick="showCertificate('${item.SQID}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看获奖证书</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 获取获奖级别样式类
        function getAwardLevelClass(level) {
            if (!level) return 'other';
            const levelStr = level.toString().toLowerCase();
            if (levelStr.includes('国家') || levelStr.includes('全国')) return 'national';
            if (levelStr.includes('省') || levelStr.includes('自治区') || levelStr.includes('直辖市')) return 'provincial';
            if (levelStr.includes('市') || levelStr.includes('地区')) return 'municipal';
            if (levelStr.includes('校') || levelStr.includes('院')) return 'school';
            return 'other';
        }

        // 获取获奖等级样式类
        function getAwardGradeClass(grade) {
            if (!grade) return 'other';
            const gradeStr = grade.toString().toLowerCase();
            if (gradeStr.includes('一等') || gradeStr.includes('第一') || gradeStr.includes('金奖')) return 'first';
            if (gradeStr.includes('二等') || gradeStr.includes('第二') || gradeStr.includes('银奖')) return 'second';
            if (gradeStr.includes('三等') || gradeStr.includes('第三') || gradeStr.includes('铜奖')) return 'third';
            return 'other';
        }

        // 查看获奖证书
        function showCertificate(id) {
            const url = '/student/subjectCompetition/showFj?id=' + id + '&timestamp=' + new Date().getTime();
            $('#previewEmbed').attr('src', url);
            $('#certificatePreview').show();
        }

        // 关闭证书预览
        function closeCertificatePreview() {
            $('#certificatePreview').hide();
            $('#previewEmbed').attr('src', '');
        }

        // 刷新数据
        function refreshData() {
            loadData(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#competitionList').hide();
            } else {
                $('#emptyState').hide();
                $('#competitionList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 无限滚动加载
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    currentPage++;
                    loadData(currentPage, false);
                }
            }
        });

        // 点击预览背景关闭
        $('#certificatePreview').click(function(e) {
            if (e.target === this) {
                closeCertificatePreview();
            }
        });
    </script>
</body>
</html>
