<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>专业认证课程评估</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 专业认证课程评估页面样式 */
        .certification-header {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .certification-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .certification-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .evaluation-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--info-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .evaluation-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--success-color);
            position: relative;
        }
        
        .evaluation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .evaluation-index {
            background: var(--success-color);
            color: white;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 600;
        }
        
        .evaluation-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .evaluation-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 var(--margin-sm);
            line-height: 1.4;
        }
        
        .evaluation-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            padding: 4px 0;
        }
        
        .detail-label {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .detail-value {
            color: var(--text-secondary);
        }
        
        .evaluation-time {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin: var(--margin-sm) 0;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .time-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .evaluation-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
            margin-top: var(--margin-sm);
        }
        
        .btn-action {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-evaluate {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-evaluate:hover {
            background: var(--primary-dark);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-view:hover {
            background: var(--info-dark);
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .evaluation-details {
                grid-template-columns: 1fr;
            }
            
            .evaluation-header {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-sm);
            }
            
            .evaluation-actions {
                flex-wrap: wrap;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">专业认证课程评估</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 专业认证头部 -->
        <div class="certification-header">
            <div class="certification-title">专业认证课程评估</div>
            <div class="certification-desc">参与专业认证课程评估问卷</div>
        </div>
        
        <!-- 评估统计 -->
        <div class="evaluation-stats">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                评估统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalEvaluations">0</div>
                    <div class="stat-label">总评估数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedEvaluations">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="pendingEvaluations">0</div>
                    <div class="stat-label">待评估</div>
                </div>
            </div>
        </div>
        
        <!-- 评估记录列表 -->
        <div id="evaluationList">
            <!-- 动态加载评估记录 -->
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text"></i>
            <div>暂无评估记录</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let evaluationData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;

        $(function() {
            initPage();
            loadEvaluationList(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载评估列表
        function loadEvaluationList(page, reset) {
            if (reset) {
                currentPage = 1;
                evaluationData = [];
                hasMore = true;
            }

            if (!hasMore) return;

            showLoading(true);

            $.ajax({
                url: "/student/professionalCertification/courseEvaluation/search",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    if (data && data.records) {
                        const records = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (reset) {
                            evaluationData = records;
                        } else {
                            evaluationData = evaluationData.concat(records);
                        }

                        hasMore = evaluationData.length < totalCount;
                        currentPage = page;

                        renderEvaluationList();
                        updateStats();
                    } else {
                        if (reset) {
                            showEmptyState();
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染评估列表
        function renderEvaluationList() {
            const container = $('#evaluationList');
            container.empty();

            if (evaluationData.length === 0) {
                showEmptyState();
                return;
            }

            hideEmptyState();

            evaluationData.forEach(function(item, index) {
                const evaluationHtml = `
                    <div class="evaluation-item">
                        <div class="evaluation-header">
                            <div class="evaluation-index">${index + 1}</div>
                            <div class="evaluation-title">${item.WJMC || '未知问卷'}</div>
                            <div class="evaluation-status ${item.SFYPG == '1' ? 'status-completed' : 'status-pending'}">
                                ${item.SFYPG == '1' ? '已评估' : '待评估'}
                            </div>
                        </div>

                        <div class="evaluation-details">
                            <div class="detail-item">
                                <span class="detail-label">课程名称</span>
                                <span class="detail-value">${item.KCM || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">课程号</span>
                                <span class="detail-value">${item.KCH || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">课序号</span>
                                <span class="detail-value">${item.KXH || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">评估状态</span>
                                <span class="detail-value">${item.SFYPG == '1' ? '已完成' : '未完成'}</span>
                            </div>
                        </div>

                        ${item.PGSJ ? `
                            <div class="evaluation-time">
                                <div class="time-label">评估时间</div>
                                <div>${item.PGSJ}</div>
                            </div>
                        ` : ''}

                        <div class="evaluation-actions">
                            ${getActionButtons(item)}
                        </div>
                    </div>
                `;
                container.append(evaluationHtml);
            });
        }

        // 获取操作按钮
        function getActionButtons(item) {
            const kg = "${kg}"; // 从后端获取开关状态

            if (item.SFYPG == '1' || kg != '1') {
                // 已评估或评估关闭，只能查看
                return `
                    <button class="btn-action btn-view" onclick="seeInfo('look', '${item.KTID}', '${item.PGID}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `;
            } else {
                // 未评估且评估开放，可以评估
                return `
                    <button class="btn-action btn-evaluate" onclick="seeInfo('pg', '${item.KTID}', '');">
                        <i class="ace-icon fa fa-pencil-square-o"></i>
                        <span>评估</span>
                    </button>
                `;
            }
        }

        // 查看或评估
        function seeInfo(type, ktid, pgid) {
            let url = "/student/professionalCertification/courseEvaluation/evaluation?type=" + type + "&ktid=" + ktid;
            if (type === "look" && pgid) {
                url += "&pgid=" + pgid;
            }

            if (parent && parent.addTab) {
                const title = type === "look" ? "查看评估" : "课程评估";
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 更新统计信息
        function updateStats() {
            const total = evaluationData.length;
            const completed = evaluationData.filter(item => item.SFYPG == '1').length;
            const pending = total - completed;

            $('#totalEvaluations').text(total);
            $('#completedEvaluations').text(completed);
            $('#pendingEvaluations').text(pending);
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
            $('#evaluationList').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('#evaluationList').show();
        }

        // 刷新数据
        function refreshData() {
            loadEvaluationList(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 滚动加载更多
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    loadEvaluationList(currentPage + 1, false);
                }
            }
        });
    </script>
</body>
</html>
