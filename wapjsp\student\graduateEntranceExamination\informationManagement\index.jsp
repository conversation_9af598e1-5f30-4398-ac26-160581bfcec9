<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>考研信息填报</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 考研信息填报页面样式 */
        .graduate-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .graduate-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .graduate-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .status-alert {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            font-size: var(--font-size-small);
            text-align: center;
        }
        
        .status-alert.error {
            background: var(--error-light);
            border-color: var(--error-color);
            color: var(--error-dark);
        }
        
        .status-alert.success {
            background: var(--success-light);
            border-color: var(--success-color);
            color: var(--success-dark);
        }
        
        .form-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .form-title i {
            color: var(--primary-color);
            margin-right: 8px;
        }
        
        .btn-save {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-save:hover {
            background: var(--success-dark);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-md);
        }
        
        .form-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-label.required::before {
            content: '*';
            color: var(--error-color);
            margin-right: 4px;
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
            font-family: inherit;
        }
        
        .switch-container {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--text-disabled);
            transition: var(--transition-base);
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: var(--transition-base);
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--success-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .switch-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .switch-label.active {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .error-message {
            color: var(--error-color);
            font-size: var(--font-size-mini);
            margin-top: 4px;
        }
        
        .readonly-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .readonly-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .readonly-title i {
            color: var(--info-color);
        }
        
        .readonly-row {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-md);
        }
        
        .readonly-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .readonly-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .readonly-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
            flex: 1;
            margin-left: var(--margin-sm);
        }
        
        .time-notice {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
            font-size: var(--font-size-small);
            text-align: center;
        }
        
        @media (max-width: 480px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .readonly-row {
                grid-template-columns: 1fr;
            }
            
            .readonly-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }
            
            .readonly-value {
                text-align: left;
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">考研信息填报</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 考研信息填报头部 -->
        <div class="graduate-header">
            <div class="graduate-title">考研信息填报</div>
            <div class="graduate-desc">填写和管理研究生入学考试信息</div>
        </div>
        
        <!-- 状态提示 -->
        <c:if test="${flag == 'nonparametric'}">
            <div class="status-alert error">申请参数未维护，请联系管理员处理</div>
        </c:if>
        <c:if test="${flag == 'notenabled'}">
            <div class="status-alert error">申请未启用，请联系管理员处理</div>
        </c:if>
        <c:if test="${flag == 'nottime'}">
            <div class="status-alert error">不在申请时间范围或申请开关关闭</div>
        </c:if>
        <c:if test="${flag == 'noGrade'}">
            <div class="status-alert error">没有维护面向年级</div>
        </c:if>
        <c:if test="${flag == 'gradeDiscrepancy'}">
            <div class="status-alert error">年级与面向年级不一致</div>
        </c:if>
        
        <!-- 申请时间提示 -->
        <c:if test="${flag == 'showAdd'}">
            <div class="time-notice">
                允许申请时间：${fn:substring(ywsqkzb.kssj, 0, 4)}-${fn:substring(ywsqkzb.kssj, 4, 6)}-${fn:substring(ywsqkzb.kssj, 6, 8)} ${fn:substring(ywsqkzb.kssj, 8, 10)}:${fn:substring(ywsqkzb.kssj, 10, 12)}:${fn:substring(ywsqkzb.kssj, 12, 14)}~${fn:substring(ywsqkzb.jssj, 0, 4)}-${fn:substring(ywsqkzb.jssj, 4, 6)}-${fn:substring(ywsqkzb.jssj, 6, 8)} ${fn:substring(ywsqkzb.jssj, 8, 10)}:${fn:substring(ywsqkzb.jssj, 10, 12)}:${fn:substring(ywsqkzb.jssj, 12, 14)}
            </div>
        </c:if>
        
        <!-- 表单容器 -->
        <c:if test="${flag == 'showAdd'}">
            <div class="form-container">
                <div class="form-title">
                    <div>
                        <i class="ace-icon fa fa-edit"></i>
                        考研信息填报
                    </div>
                    <button class="btn-save" onclick="doSave();">
                        <i class="ace-icon fa fa-check"></i>
                        保存
                    </button>
                </div>
                
                <form id="graduateForm">
                    <!-- 基本信息 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">报考学校</label>
                            <select class="form-input form-select" id="bkxx" name="bkxx" required>
                                <option value="">--请选择--</option>
                                <cache:query var="yzdwbs" fields="dwdm,dwmc" region="code_yzdwb" orderby="dwdm asc"/>
                                <c:forEach items="${yzdwbs}" var="yzdwb">
                                    <option value="${yzdwb.dwdm}" <c:if test="${xtbb.bkxx == yzdwb.dwdm}">selected</c:if>>${yzdwb.dwmc}</option>
                                </c:forEach>
                            </select>
                        </div>
                        
                        <div class="form-item">
                            <label class="form-label required">报考专业</label>
                            <select class="form-input form-select" id="bkzy" name="bkzy" required>
                                <option value="">--请选择--</option>
                                <cache:query var="yzzybs" fields="zydm,zymc" region="CODE_YZZYB" orderby="zydm asc"/>
                                <c:forEach items="${yzzybs}" var="yzzyb">
                                    <option value="${yzzyb.zydm}" <c:if test="${xtbb.bkzy == yzzyb.zydm}">selected</c:if>>${yzzyb.zymc}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 成绩信息 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">初试成绩</label>
                            <input type="number" class="form-input" id="cscj" name="cscj" value="${xtbb.cscj}" min="0" max="500" step="0.1">
                        </div>
                        
                        <div class="form-item">
                            <label class="form-label">是否进入面试</label>
                            <div class="switch-container">
                                <label class="switch">
                                    <input type="checkbox" id="sfjrms" name="sfjrms" ${xtbb.sfjrms == '1' ? 'checked' : ''} onchange="toggleSwitch('sfjrms')">
                                    <span class="slider"></span>
                                </label>
                                <span class="switch-label" id="sfjrmsLabel">${xtbb.sfjrms == '1' ? '是' : '否'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 国家线和复试 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">是否超过国家线</label>
                            <div class="switch-container">
                                <label class="switch">
                                    <input type="checkbox" id="sfcggjx" name="sfcggjx" ${xtbb.sfcggjx == '1' ? 'checked' : ''} onchange="toggleSwitch('sfcggjx')">
                                    <span class="slider"></span>
                                </label>
                                <span class="switch-label" id="sfcggjxLabel">${xtbb.sfcggjx == '1' ? '是' : '否'}</span>
                            </div>
                        </div>
                        
                        <div class="form-item">
                            <label class="form-label">复试成绩</label>
                            <input type="number" class="form-input" id="fscj" name="fscj" value="${xtbb.fscj}" step="0.1">
                        </div>
                    </div>
                    
                    <!-- 调剂和录取 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">是否调剂</label>
                            <div class="switch-container">
                                <label class="switch">
                                    <input type="checkbox" id="sftj" name="sftj" ${xtbb.sftj == '1' ? 'checked' : ''} onchange="toggleSwitch('sftj')">
                                    <span class="slider"></span>
                                </label>
                                <span class="switch-label" id="sftjLabel">${xtbb.sftj == '1' ? '是' : '否'}</span>
                            </div>
                        </div>
                        
                        <div class="form-item">
                            <label class="form-label">录取学校</label>
                            <select class="form-input form-select" id="lqxx" name="lqxx">
                                <option value="">--请选择--</option>
                                <cache:query var="yzdwbs" fields="dwdm,dwmc" region="code_yzdwb" orderby="dwdm asc"/>
                                <c:forEach items="${yzdwbs}" var="yzdwb">
                                    <option value="${yzdwb.dwdm}" <c:if test="${xtbb.lqxx == yzdwb.dwdm}">selected</c:if>>${yzdwb.dwmc}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 录取专业 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">录取专业</label>
                            <select class="form-input form-select" id="lqzy" name="lqzy">
                                <option value="">--请选择--</option>
                                <cache:query var="yzzybs" fields="zydm,zymc" region="CODE_YZZYB" orderby="zydm asc"/>
                                <c:forEach items="${yzzybs}" var="yzzyb">
                                    <option value="${yzzyb.zydm}" <c:if test="${xtbb.lqzy == yzzyb.zydm}">selected</c:if>>${yzzyb.zymc}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 备注 -->
                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <textarea class="form-input form-textarea" id="bz" name="bz" maxlength="500" placeholder="请输入备注信息...">${xtbb.bz}</textarea>
                    </div>
                </form>
            </div>
        </c:if>
        
        <!-- 只读显示 -->
        <c:if test="${flag != 'showAdd'}">
            <div class="readonly-container">
                <div class="readonly-title">
                    <i class="ace-icon fa fa-eye"></i>
                    考研信息查看
                </div>
                
                <!-- 基本信息 -->
                <div class="readonly-row">
                    <div class="readonly-item">
                        <span class="readonly-label">报考学校</span>
                        <span class="readonly-value">
                            <cache:query var="yzdwbs" fields="dwdm,dwmc" region="code_yzdwb" orderby="dwdm asc"/>
                            <c:forEach items="${yzdwbs}" var="yzdwb">
                                <c:if test="${xtbb.bkxx == yzdwb.dwdm}">${yzdwb.dwmc}</c:if>
                            </c:forEach>
                        </span>
                    </div>
                    
                    <div class="readonly-item">
                        <span class="readonly-label">报考专业</span>
                        <span class="readonly-value">
                            <cache:query var="yzzybs" fields="zydm,zymc" region="CODE_YZZYB" orderby="zydm asc"/>
                            <c:forEach items="${yzzybs}" var="yzzyb">
                                <c:if test="${xtbb.bkzy == yzzyb.zydm}">${yzzyb.zymc}</c:if>
                            </c:forEach>
                        </span>
                    </div>
                </div>
                
                <!-- 成绩信息 -->
                <div class="readonly-row">
                    <div class="readonly-item">
                        <span class="readonly-label">初试成绩</span>
                        <span class="readonly-value">${xtbb.cscj}</span>
                    </div>
                    
                    <div class="readonly-item">
                        <span class="readonly-label">是否进入面试</span>
                        <span class="readonly-value">${xtbb.sfjrms == '1' ? '是' : '否'}</span>
                    </div>
                </div>
                
                <!-- 国家线和复试 -->
                <div class="readonly-row">
                    <div class="readonly-item">
                        <span class="readonly-label">是否超过国家线</span>
                        <span class="readonly-value">${xtbb.sfcggjx == '1' ? '是' : '否'}</span>
                    </div>
                    
                    <div class="readonly-item">
                        <span class="readonly-label">复试成绩</span>
                        <span class="readonly-value">${xtbb.fscj}</span>
                    </div>
                </div>
                
                <!-- 调剂和录取 -->
                <div class="readonly-row">
                    <div class="readonly-item">
                        <span class="readonly-label">是否调剂</span>
                        <span class="readonly-value">${xtbb.sftj == '1' ? '是' : '否'}</span>
                    </div>
                    
                    <div class="readonly-item">
                        <span class="readonly-label">录取学校</span>
                        <span class="readonly-value">
                            <cache:query var="yzdwbs" fields="dwdm,dwmc" region="code_yzdwb" orderby="dwdm asc"/>
                            <c:forEach items="${yzdwbs}" var="yzdwb">
                                <c:if test="${xtbb.lqxx == yzdwb.dwdm}">${yzdwb.dwmc}</c:if>
                            </c:forEach>
                        </span>
                    </div>
                </div>
                
                <!-- 录取专业 -->
                <div class="readonly-row">
                    <div class="readonly-item">
                        <span class="readonly-label">录取专业</span>
                        <span class="readonly-value">
                            <cache:query var="yzzybs" fields="zydm,zymc" region="CODE_YZZYB" orderby="zydm asc"/>
                            <c:forEach items="${yzzybs}" var="yzzyb">
                                <c:if test="${xtbb.lqzy == yzzyb.zydm}">${yzzyb.zymc}</c:if>
                            </c:forEach>
                        </span>
                    </div>
                </div>
                
                <!-- 备注 -->
                <c:if test="${not empty xtbb.bz}">
                    <div class="readonly-row">
                        <div class="readonly-item" style="flex-direction: column; align-items: flex-start;">
                            <span class="readonly-label">备注</span>
                            <span class="readonly-value" style="margin-left: 0; margin-top: 8px; text-align: left;">${xtbb.bz}</span>
                        </div>
                    </div>
                </c:if>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            initFormValidation();
        }

        // 切换开关状态
        function toggleSwitch(id) {
            const checkbox = document.getElementById(id);
            const label = document.getElementById(id + 'Label');

            if (checkbox.checked) {
                label.textContent = '是';
                label.classList.add('active');
            } else {
                label.textContent = '否';
                label.classList.remove('active');
            }
        }

        // 初始化表单验证
        function initFormValidation() {
            // 自定义验证规则：字符串最大长度（中文算2个字符）
            $.validator.addMethod("stringMaxLength", function(value, element, params) {
                let length = value.length;
                for (let i = 0; i < value.length; i++) {
                    if (value.charCodeAt(i) > 19967) {
                        length++;
                    }
                }
                return length <= params;
            }, "最大长度不能超过{0}个字符，一个汉字为两个字符");

            // 自定义验证规则：小数验证
            $.validator.addMethod("decimal", function(value, element) {
                return this.optional(element) || /^(\d{1,9}|\d{1,7}\.\d{1,3})$/.test(value);
            }, "保留3位小数，长度为9位！");

            // 表单验证配置
            $("#graduateForm").validate({
                ignore: "",
                errorElement: 'div',
                errorClass: 'error-message',
                focusInvalid: true,
                rules: {
                    bkxx: {
                        required: true
                    },
                    bkzy: {
                        required: true
                    },
                    cscj: {
                        decimal: true
                    },
                    fscj: {
                        decimal: true
                    },
                    bz: {
                        stringMaxLength: 500
                    }
                },
                messages: {
                    bkxx: {
                        required: "请选择报考学校"
                    },
                    bkzy: {
                        required: "请选择报考专业"
                    },
                    cscj: {
                        decimal: "请输入有效的成绩"
                    },
                    fscj: {
                        decimal: "请输入有效的成绩"
                    }
                },
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                }
            });
        }

        // 保存数据
        function doSave() {
            // 验证进入面试的学生必须填写初试成绩
            const sfjrms = $('#sfjrms').is(':checked');
            const cscj = $('#cscj').val().trim();

            if (sfjrms && (cscj === '' || cscj === null)) {
                showError("进入面试的同学需要填写初试成绩！");
                return;
            }

            if (!$("#graduateForm").valid()) {
                return;
            }

            showLoading(true);

            // 准备表单数据
            const formData = {
                bkxx: $('#bkxx').val(),
                bkzy: $('#bkzy').val(),
                cscj: $('#cscj').val(),
                sfjrms: $('#sfjrms').is(':checked') ? '1' : '0',
                sfcggjx: $('#sfcggjx').is(':checked') ? '1' : '0',
                fscj: $('#fscj').val(),
                sftj: $('#sftj').is(':checked') ? '1' : '0',
                lqxx: $('#lqxx').val(),
                lqzy: $('#lqzy').val(),
                bz: $('#bz').val(),
                tokenValue: $('#tokenValue').val()
            };

            $.ajax({
                url: "/student/graduateEntranceExamination/informationManagement/InformationFilling/doSave",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    $('#tokenValue').val(data.token);

                    if (data.result.indexOf("/") !== -1) {
                        showError('页面已过期，请刷新页面！');
                    } else if (data.result === "ok") {
                        showSuccess("操作成功！");
                    } else if (data.result === "nonparametric") {
                        showError("申请参数未维护，请联系管理员处理！");
                    } else if (data.result === "notenabled") {
                        showError("申请未启用，请联系管理员处理");
                    } else if (data.result === "nottime") {
                        showError("不在申请时间范围或申请开关关闭");
                    } else {
                        showError(data.result);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
