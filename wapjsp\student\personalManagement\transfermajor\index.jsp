<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>转专业申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 转专业申请页面样式 */
        .transfer-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .transfer-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .transfer-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .action-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .action-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-title i {
            color: var(--primary-color);
        }
        
        .apply-button {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .apply-button:hover {
            background: var(--success-dark);
        }
        
        .apply-button:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .notice-container {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--warning-dark);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .notice-text {
            font-size: var(--font-size-small);
            color: var(--warning-dark);
            line-height: 1.4;
        }
        
        .application-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-header i {
            color: var(--primary-color);
        }
        
        .tab-container {
            display: flex;
            flex-direction: column;
        }
        
        .tab-header {
            display: flex;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .tab-header::-webkit-scrollbar {
            display: none;
        }
        
        .tab-item {
            flex: 0 0 auto;
            padding: var(--padding-md);
            text-align: center;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
            border-bottom: 2px solid transparent;
            white-space: nowrap;
            min-width: 120px;
        }
        
        .tab-item.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-primary);
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .application-info {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .info-row {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .info-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            word-break: break-word;
        }
        
        .major-table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--font-size-small);
            margin: var(--margin-sm) 0;
        }
        
        .major-table th,
        .major-table td {
            border: 1px solid var(--divider-color);
            padding: var(--padding-sm);
            text-align: left;
        }
        
        .major-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .approval-table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--font-size-small);
            margin: var(--margin-sm) 0;
        }
        
        .approval-table th,
        .approval-table td {
            border: 1px solid var(--divider-color);
            padding: var(--padding-sm);
            text-align: left;
        }
        
        .approval-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            display: inline-block;
        }
        
        .status-draft {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-submitted {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-edit:hover {
            background: var(--primary-dark);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-submit:hover {
            background: var(--success-dark);
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-delete:hover {
            background: var(--error-dark);
        }
        
        .btn-print {
            background: var(--info-color);
            color: white;
        }
        
        .btn-print:hover {
            background: var(--info-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        @media (max-width: 480px) {
            .major-table,
            .approval-table {
                font-size: var(--font-size-mini);
            }
            
            .major-table th,
            .major-table td,
            .approval-table th,
            .approval-table td {
                padding: 4px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">转专业申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 转专业申请头部 -->
        <div class="transfer-header">
            <div class="transfer-title">转专业申请</div>
            <div class="transfer-desc">申请信息</div>
        </div>
        
        <!-- 申请操作 -->
        <c:if test="${show}">
            <div class="action-container">
                <div class="action-title">
                    <i class="ace-icon fa fa-plus"></i>
                    申请转专业
                </div>
                
                <button class="apply-button" onclick="addSpecialties();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>申请</span>
                </button>
            </div>
        </c:if>
        
        <!-- 提示信息 -->
        <c:if test="${!sqshow}">
            <div class="notice-container">
                <div class="notice-title">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    提示信息
                </div>
                <div class="notice-text">
                    您还未申请转专业。
                    <c:if test="${show}">
                        请点击上方按钮查看可转入院系专业信息。
                    </c:if>
                    <c:if test="${!show}">
                        目前没有可申请转专业的批次。
                    </c:if>
                </div>
            </div>
        </c:if>
        
        <!-- 申请列表 -->
        <c:if test="${not empty xsCauZzySqbQus && fn:length(xsCauZzySqbQus) > 0}">
            <div class="application-list">
                <div class="list-header">
                    <i class="ace-icon fa fa-list"></i>
                    申请记录
                </div>
                
                <div class="tab-container">
                    <div class="tab-header">
                        <c:forEach var="xsCauZzySqbQu" items="${xsCauZzySqbQus}" varStatus="sqbhsta">
                            <div class="tab-item ${sqbhsta.first ? 'active' : ''}" data-tab="${xsCauZzySqbQu.sqbh}">
                                批次:${xsCauZzySqbQu.zzypcm}
                            </div>
                        </c:forEach>
                    </div>
                    
                    <div class="tab-content">
                        <c:forEach var="xsCauZzySqbQu" items="${xsCauZzySqbQus}" varStatus="sqbhsta">
                            <div class="tab-pane ${sqbhsta.first ? 'active' : ''}" id="tab-${xsCauZzySqbQu.sqbh}">
                                <div class="application-info">
                                    <div class="info-row">
                                        <div class="info-label">学号</div>
                                        <div class="info-value">${xsCauZzySqbQu.xh}</div>
                                    </div>
                                    
                                    <div class="info-row">
                                        <div class="info-label">姓名</div>
                                        <div class="info-value">${xsCauZzySqbQu.xm}</div>
                                    </div>
                                    
                                    <div class="info-row">
                                        <div class="info-label">申请转入专业</div>
                                        <div class="info-value">
                                            <table class="major-table">
                                                <thead>
                                                    <tr>
                                                        <th>志愿序号</th>
                                                        <th>转入年级</th>
                                                        <th>转入院系</th>
                                                        <th>转入专业</th>
                                                        <th>拟选结果</th>
                                                        <th>录取结果</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <c:forEach var="xsCauZzyZybQu" items="${xsCauZzySqbQu.xsCauZzyZybQus}">
                                                        <tr>
                                                            <td>${xsCauZzyZybQu.zybh}</td>
                                                            <td>${xsCauZzyZybQu.njmc}</td>
                                                            <td>${xsCauZzyZybQu.xsm}</td>
                                                            <td>${xsCauZzyZybQu.zym}</td>
                                                            <td>${xsCauZzyZybQu.nxjg}</td>
                                                            <td>${xsCauZzyZybQu.lqjg}</td>
                                                        </tr>
                                                    </c:forEach>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <div class="info-row">
                                        <div class="info-label">联系方式</div>
                                        <div class="info-value">${xsCauZzySqbQu.lxfs}</div>
                                    </div>
                                    
                                    <div class="info-row">
                                        <div class="info-label">申请理由</div>
                                        <div class="info-value">${xsCauZzySqbQu.sqly}</div>
                                    </div>
                                    
                                    <div class="info-row">
                                        <div class="info-label">特长</div>
                                        <div class="info-value">${xsCauZzySqbQu.xstc}</div>
                                    </div>
                                    
                                    <div class="info-row">
                                        <div class="info-label">申请状态</div>
                                        <div class="info-value">
                                            <span class="status-badge 
                                                <c:choose>
                                                    <c:when test="${xsCauZzySqbQu.sqztdm eq '00'}">status-draft</c:when>
                                                    <c:when test="${xsCauZzySqbQu.sqztdm eq '01'}">status-submitted</c:when>
                                                    <c:when test="${xsCauZzySqbQu.sqztdm eq '06'}">status-approved</c:when>
                                                    <c:otherwise>status-rejected</c:otherwise>
                                                </c:choose>
                                            ">
                                                ${xsCauZzySqbQu.sqztmc}
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="info-row">
                                        <div class="info-label">操作</div>
                                        <div class="info-value">
                                            <div class="action-buttons">
                                                <c:if test="${xsCauZzySqbQu.sqztdm eq '00'}">
                                                    <button class="action-btn btn-edit" onclick="updateSpecialties('${xsCauZzySqbQu.sqbh}');">
                                                        <i class="ace-icon fa fa-pencil-square-o"></i>
                                                        修改
                                                    </button>
                                                    <button class="action-btn btn-submit" onclick="doSpecialties('${xsCauZzySqbQu.sqbh}','${xsCauZzySqbQu.zzypch}');">
                                                        <i class="ace-icon fa fa-check-square-o"></i>
                                                        提交
                                                    </button>
                                                    <button class="action-btn btn-delete" onclick="deleteSpecialties('${xsCauZzySqbQu.sqbh}');">
                                                        <i class="ace-icon fa fa-trash-o"></i>
                                                        撤销
                                                    </button>
                                                </c:if>
                                                <c:if test="${xsCauZzySqbQu.sqztdm eq '06'}">
                                                    <button class="action-btn btn-print" onclick="printConfirm('${xsCauZzySqbQu.sqbh}');">
                                                        <i class="ace-icon fa fa-print"></i>
                                                        打印确认单
                                                    </button>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <c:if test="${xsCauZzySqbQu.sqztdm ne '00'}">
                                        <div class="info-row">
                                            <div class="info-label">转专业审批</div>
                                            <div class="info-value">
                                                <table class="approval-table">
                                                    <thead>
                                                        <tr>
                                                            <th>审批序号</th>
                                                            <th>审批院系</th>
                                                            <th>审批人</th>
                                                            <th>审批结果</th>
                                                            <th>审批意见</th>
                                                            <th>审批时间</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <c:forEach var="xsCauZzyspbQu" items="${xsCauZzySqbQu.xsCauZzyspbQus}">
                                                            <tr>
                                                                <td>${xsCauZzyspbQu.spxh}</td>
                                                                <td>${xsCauZzyspbQu.xsm}</td>
                                                                <td>${xsCauZzyspbQu.jsm}</td>
                                                                <td>${xsCauZzyspbQu.spjgmc}</td>
                                                                <td>${xsCauZzyspbQu.spyj}</td>
                                                                <td>${xsCauZzyspbQu.spsj}</td>
                                                            </tr>
                                                        </c:forEach>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </c:if>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </div>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <c:if test="${empty xsCauZzySqbQus || fn:length(xsCauZzySqbQus) == 0}">
            <div class="empty-state">
                <i class="ace-icon fa fa-exchange"></i>
                <div class="empty-state-text">暂无申请记录</div>
                <div class="empty-state-desc">
                    <c:if test="${show}">点击上方按钮开始申请</c:if>
                    <c:if test="${!show}">目前没有可申请的批次</c:if>
                </div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            initTabs();
        }

        // 初始化选项卡
        function initTabs() {
            $('.tab-item').on('click', function() {
                const tabId = $(this).data('tab');

                // 切换选项卡状态
                $('.tab-item').removeClass('active');
                $(this).addClass('active');

                // 切换内容面板
                $('.tab-pane').removeClass('active');
                $('#tab-' + tabId).addClass('active');
            });
        }

        // 查询可申请转专业数据
        function addSpecialties() {
            location.href = "/student/personalManagement/transfer/major/apply";
        }

        // 修改（确认）
        function updateSpecialties(sqbh) {
            location.href = "/student/personalManagement/transfer/major/apply?sqbh=" + sqbh;
        }

        // 撤销申请
        function deleteSpecialties(sqbh) {
            showConfirm("确定要撤回这条记录？", function() {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/transfer/major/del",
                    method: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(d) {
                        const data = d.data;
                        if (data["result"].indexOf("/") != -1) {
                            showError('页面已过期，请刷新页面！');
                        } else if (data["result"] == "ok") {
                            showSuccess("申请撤销成功！");
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        } else {
                            showError(data.msg || "撤销失败");
                        }
                        $("#tokenValue").val(d.token);
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            });
        }

        // 提交，只修改状态
        function doSpecialties(sqbh, zzypch) {
            showConfirm("提交后不可修改，是否确认提交？", function() {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/transfer/major/edit",
                    method: "post",
                    data: "sqbh=" + sqbh + "&zzypch=" + zzypch + "&flag=01&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(d) {
                        const data = d.data;
                        if (data["result"].indexOf("/") != -1) {
                            showError('页面已过期，请刷新页面！');
                        } else if (data["result"] == "ok") {
                            showSuccess("提交成功！");
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        } else {
                            showError(data.msg || "提交失败");
                        }
                        $("#tokenValue").val(d.token);
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            });
        }

        // 打印确认单
        function printConfirm(sqbh) {
            const url = "/student/personalManagement/transfer/major/print/confirm/" + sqbh;
            window.open(url, "_blank");
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }
