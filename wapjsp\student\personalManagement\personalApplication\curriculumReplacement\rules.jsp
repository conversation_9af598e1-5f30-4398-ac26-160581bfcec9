<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>替代规则</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 替代规则页面样式 */
        .rules-header {
            background: linear-gradient(135deg, var(--warning-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .rules-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .rules-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--warning-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--warning-color);
            box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
        }
        
        .form-select {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-select:focus {
            outline: none;
            border-color: var(--warning-color);
            box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
        }
        
        .btn-search {
            background: var(--warning-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-search:hover {
            background: var(--warning-dark);
        }
        
        .rules-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-header i {
            color: var(--warning-color);
        }
        
        .rules-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .rule-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .rule-item:last-child {
            border-bottom: none;
        }
        
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .rule-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--warning-light);
            color: var(--warning-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .rule-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .rule-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .rule-scope {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .rule-details {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            margin-top: var(--margin-sm);
        }
        
        .course-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .section-label {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .section-label.replacement {
            color: var(--success-color);
        }
        
        .section-label.replaced {
            color: var(--error-color);
        }
        
        .course-list {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .course-item {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
        }
        
        .course-item.replacement {
            background: var(--success-light);
            border-color: var(--success-color);
            color: var(--success-dark);
        }
        
        .course-item.replaced {
            background: var(--error-light);
            border-color: var(--error-color);
            color: var(--error-dark);
            text-decoration: line-through;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--warning-light);
            border-color: var(--warning-color);
            color: var(--warning-dark);
        }
        
        .btn-page.active {
            background: var(--warning-color);
            border-color: var(--warning-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .rule-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">替代规则</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 替代规则头部 -->
        <div class="rules-header">
            <div class="rules-title">课程替代规则</div>
            <div class="rules-desc">查看课程替代规则和适用范围</div>
        </div>
        
        <!-- 搜索条件 -->
        <div class="search-container">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form class="search-form" id="searchForm">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">课程号</label>
                        <input type="text" class="form-input" name="kch" id="kch" placeholder="请输入课程号">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">课程名</label>
                        <input type="text" class="form-input" name="kcm" id="kcm" placeholder="请输入课程名">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">替代课程号</label>
                        <input type="text" class="form-input" name="tdkch" id="tdkch" placeholder="请输入替代课程号">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">替代课程名</label>
                        <input type="text" class="form-input" name="tdkcm" id="tdkcm" placeholder="请输入替代课程名">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">替代类型</label>
                        <select class="form-select" name="tdlx" id="tdlx">
                            <cache:query var="tdlxb" region="code_tdlxb" orderby="tdlx asc" />
                            <option value="">全部</option>
                            <c:forEach items="${tdlxb}" var="tdlxb">
                                <option value="${tdlxb.tdlx}">${tdlxb.tdlxsm}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <button type="button" class="btn-search" onclick="searchRules();">
                            <i class="ace-icon fa fa-search"></i>
                            查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 替代规则列表 -->
        <div class="rules-container">
            <div class="container-header">
                <i class="ace-icon fa fa-list"></i>
                替代规则列表
            </div>
            
            <div class="rules-list" id="rulesList">
                <!-- 动态加载规则列表 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;

        $(function() {
            initPage();
            loadRules(1);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载替代规则
        function loadRules(page = 1) {
            currentPage = page;
            showLoading(true);

            const formData = $('#searchForm').serialize();

            $.ajax({
                url: "/student/personalManagement/personalApplication/curriculumReplacement/getRulesPage",
                type: "post",
                data: formData + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    if (response && response.data) {
                        renderRules(response.data.records);
                        renderPagination(response.data.pageContext);
                    } else {
                        showEmptyState('rulesList', '暂无替代规则');
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染替代规则
        function renderRules(rules) {
            const container = $('#rulesList');

            if (!rules || rules.length === 0) {
                showEmptyState('rulesList', '暂无替代规则');
                return;
            }

            let rulesHtml = '';

            rules.forEach((rule, index) => {
                const serialNumber = (currentPage - 1) * pageSize + 1 + index;
                const replacementCourses = (rule.KCM || '').split(',').filter(course => course.trim());
                const replacedCourses = (rule.TDKCM || '').split(',').filter(course => course.trim());
                const scopes = (rule.SYFW || '').split(',').filter(scope => scope.trim());

                rulesHtml += `
                    <div class="rule-item">
                        <div class="rule-header">
                            <div class="rule-number">${serialNumber}</div>
                            <div class="rule-info">
                                <div class="rule-type">${rule.GZLXSM || '未设置'}</div>
                                <div class="rule-scope">适用范围：${scopes.join('、') || '未设置'}</div>
                            </div>
                        </div>

                        <div class="rule-details">
                            <div class="course-section">
                                <div class="section-label replacement">
                                    <i class="ace-icon fa fa-check"></i>
                                    替代课程（已通过课程）
                                </div>
                                <div class="course-list">
                                    ${replacementCourses.map(course =>
                                        `<div class="course-item replacement">${course}</div>`
                                    ).join('')}
                                </div>
                            </div>

                            <div class="course-section">
                                <div class="section-label replaced">
                                    <i class="ace-icon fa fa-times"></i>
                                    被替代课程（培养方案中课程）
                                </div>
                                <div class="course-list">
                                    ${replacedCourses.map(course =>
                                        `<div class="course-item replaced">${course}</div>`
                                    ).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.html(rulesHtml);
        }

        // 显示空状态
        function showEmptyState(containerId, message) {
            const container = $('#' + containerId);
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <div class="empty-state-text">${message}</div>
                    <div class="empty-state-desc">请调整搜索条件后重试</div>
                </div>
            `);
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            if (!pageContext || pageContext.totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="loadRules(${currentPage - 1});">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<button class="btn-page" onclick="loadRules(1);">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="loadRules(${i});">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                paginationHtml += `<button class="btn-page" onclick="loadRules(${totalPages});">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="loadRules(${currentPage + 1});">下一页</button>`;

            container.html(paginationHtml);
            $('#paginationContainer').show();
        }

        // 搜索规则
        function searchRules() {
            loadRules(1);
        }

        // 刷新数据
        function refreshData() {
            loadRules(currentPage);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
