<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>等级考试成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 等级考试成绩页面样式 */
        .exam-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .summary-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .exam-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .exam-passed {
            border-left-color: var(--success-color);
        }
        
        .exam-failed {
            border-left-color: var(--error-color);
        }
        
        .exam-excellent {
            border-left-color: var(--warning-color);
        }
        
        .exam-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .exam-result {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 500;
            text-align: center;
        }
        
        .result-pass {
            background: var(--success-color);
            color: white;
        }
        
        .result-fail {
            background: var(--error-color);
            color: white;
        }
        
        .result-excellent {
            background: var(--warning-color);
            color: white;
        }
        
        .result-pending {
            background: var(--text-disabled);
            color: white;
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .exam-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-detail-label {
            color: var(--text-secondary);
        }
        
        .exam-detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .score-breakdown {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
        }
        
        .breakdown-title {
            font-weight: 500;
            margin-bottom: var(--margin-xs);
            color: var(--text-primary);
        }
        
        .breakdown-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .breakdown-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .exam-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--margin-sm);
        }
        
        .exam-date {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .btn-certificate {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-certificate:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .exam-type-group {
            margin-bottom: var(--margin-lg);
        }
        
        .type-title {
            background: var(--bg-tertiary);
            padding: var(--padding-sm) var(--padding-md);
            margin: 0 var(--margin-md) var(--margin-sm);
            border-radius: 6px;
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            position: sticky;
            top: 56px;
            z-index: 100;
        }
        
        .registration-notice {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--info-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            margin-right: var(--margin-xs);
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .btn-register {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            margin-top: var(--margin-sm);
            transition: all var(--transition-base);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">等级考试成绩</div>
            <div class="navbar-action" onclick="refreshScores();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 考试统计 -->
        <div class="exam-summary">
            <div class="summary-title">等级考试统计</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalExams">0</div>
                    <div class="stat-label">总考试</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="passedExams">0</div>
                    <div class="stat-label">已通过</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="certificates">0</div>
                    <div class="stat-label">可下载证书</div>
                </div>
            </div>
        </div>
        
        <!-- 报名通知 -->
        <div class="registration-notice" id="registrationNotice" style="display: none;">
            <div class="notice-title">
                <i class="ace-icon fa fa-bullhorn"></i>
                <span>考试报名</span>
            </div>
            <div class="notice-content" id="registrationContent">
                <!-- 报名通知内容 -->
            </div>
            <button class="btn-register" onclick="goToRegistration();">立即报名</button>
        </div>
        
        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterExams('all')">全部</button>
                <button class="filter-chip" onclick="filterExams('cet')">英语四六级</button>
                <button class="filter-chip" onclick="filterExams('computer')">计算机等级</button>
                <button class="filter-chip" onclick="filterExams('passed')">已通过</button>
            </div>
        </div>
        
        <!-- 考试成绩列表 -->
        <div class="container-mobile">
            <div id="examList">
                <!-- 考试项将通过JavaScript动态填充 -->
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-certificate"></i>
                <div>暂无等级考试记录</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allExams = [];
        let filteredExams = [];
        let currentFilter = 'all';
        let groupedExams = {};

        $(function() {
            initPage();
            loadExams();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载等级考试成绩
        function loadExams() {
            showLoading(true);
            
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/externalScores/getScores",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allExams = data.exams || [];
                    updateStatistics(data.statistics);
                    updateRegistrationNotice(data.registration);
                    applyFilter();
                    showLoading(false);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                    showLoading(false);
                }
            });
        }

        // 渲染考试列表
        function renderExams() {
            const container = $('#examList');
            container.empty();
            
            if (filteredExams.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            // 按考试类型分组
            groupedExams = groupByType(filteredExams);
            
            Object.keys(groupedExams).forEach(type => {
                // 添加类型标题
                const typeHtml = `<div class="type-title">${getTypeDisplayName(type)}</div>`;
                container.append(typeHtml);
                
                // 添加该类型的考试
                groupedExams[type].forEach(function(exam, index) {
                    const examHtml = createExamItem(exam, index);
                    container.append(examHtml);
                });
            });
        }

        // 按考试类型分组
        function groupByType(exams) {
            const grouped = {};
            exams.forEach(exam => {
                const type = exam.examType || 'other';
                if (!grouped[type]) {
                    grouped[type] = [];
                }
                grouped[type].push(exam);
            });
            return grouped;
        }

        // 获取类型显示名称
        function getTypeDisplayName(type) {
            const typeNames = {
                'cet4': '英语四级',
                'cet6': '英语六级',
                'computer1': '计算机一级',
                'computer2': '计算机二级',
                'computer3': '计算机三级',
                'computer4': '计算机四级',
                'other': '其他考试'
            };
            return typeNames[type] || type;
        }

        // 创建考试项HTML
        function createExamItem(exam, index) {
            const result = getExamResult(exam);
            const resultClass = getResultClass(result);
            const itemClass = getItemClass(result);
            
            let breakdownHtml = '';
            if (exam.scores && exam.scores.length > 0) {
                breakdownHtml = `
                    <div class="score-breakdown">
                        <div class="breakdown-title">成绩详情</div>
                        <div class="breakdown-grid">
                            ${exam.scores.map(score => `
                                <div class="breakdown-item">
                                    <span>${score.subject}</span>
                                    <span>${score.score}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
            
            let actionButton = '';
            if (result === 'passed' && exam.canDownloadCertificate) {
                actionButton = `<button class="btn-certificate" onclick="downloadCertificate('${exam.id}')">下载证书</button>`;
            } else if (result === 'passed') {
                actionButton = `<button class="btn-certificate" disabled>证书未发布</button>`;
            }
            
            return `
                <div class="exam-item ${itemClass}">
                    <div class="exam-header">
                        <div class="exam-name">${exam.examName}</div>
                        <div class="exam-result ${resultClass}">${getResultDisplay(exam)}</div>
                    </div>
                    <div class="exam-details">
                        <div class="exam-detail-item">
                            <span class="exam-detail-label">考试时间:</span>
                            <span class="exam-detail-value">${exam.examDate}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span class="exam-detail-label">准考证号:</span>
                            <span class="exam-detail-value">${exam.ticketNumber}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span class="exam-detail-label">总分:</span>
                            <span class="exam-detail-value">${exam.totalScore || '-'}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span class="exam-detail-label">等级:</span>
                            <span class="exam-detail-value">${exam.level || '-'}</span>
                        </div>
                    </div>
                    ${breakdownHtml}
                    <div class="exam-actions">
                        <div class="exam-date">成绩发布: ${exam.resultDate || '未发布'}</div>
                        ${actionButton}
                    </div>
                </div>
            `;
        }

        // 获取考试结果
        function getExamResult(exam) {
            if (!exam.totalScore && !exam.isPassed) {
                return 'pending';
            }
            
            if (exam.isPassed || exam.level === '优秀' || exam.level === '良好') {
                if (exam.level === '优秀' || (exam.totalScore && exam.totalScore >= 85)) {
                    return 'excellent';
                }
                return 'passed';
            } else {
                return 'failed';
            }
        }

        // 获取结果样式类
        function getResultClass(result) {
            switch(result) {
                case 'passed': return 'result-pass';
                case 'failed': return 'result-fail';
                case 'excellent': return 'result-excellent';
                case 'pending': return 'result-pending';
                default: return 'result-pending';
            }
        }

        // 获取项目样式类
        function getItemClass(result) {
            switch(result) {
                case 'passed': return 'exam-passed';
                case 'failed': return 'exam-failed';
                case 'excellent': return 'exam-excellent';
                default: return '';
            }
        }

        // 获取结果显示
        function getResultDisplay(exam) {
            if (exam.level) {
                return exam.level;
            } else if (exam.totalScore) {
                return exam.totalScore + '分';
            } else if (exam.isPassed) {
                return '通过';
            } else {
                return '未通过';
            }
        }

        // 下载证书
        function downloadCertificate(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要下载证书吗？', function(confirmed) {
                    if (confirmed) {
                        window.open(`/student/integratedQuery/scoreQuery/externalScores/downloadCertificate?examId=${examId}`);
                    }
                });
            } else {
                if (confirm('确定要下载证书吗？')) {
                    window.open(`/student/integratedQuery/scoreQuery/externalScores/downloadCertificate?examId=${examId}`);
                }
            }
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'cet':
                    filteredExams = allExams.filter(exam => 
                        exam.examType === 'cet4' || exam.examType === 'cet6'
                    );
                    break;
                case 'computer':
                    filteredExams = allExams.filter(exam => 
                        exam.examType && exam.examType.startsWith('computer')
                    );
                    break;
                case 'passed':
                    filteredExams = allExams.filter(exam => 
                        exam.isPassed || exam.level === '优秀' || exam.level === '良好'
                    );
                    break;
                default:
                    filteredExams = allExams;
            }
            
            renderExams();
        }

        // 筛选考试
        function filterExams(filter) {
            currentFilter = filter;
            
            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');
            
            applyFilter();
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;
            
            $('#totalExams').text(statistics.totalExams || 0);
            $('#passedExams').text(statistics.passedExams || 0);
            $('#certificates').text(statistics.certificates || 0);
        }

        // 更新报名通知
        function updateRegistrationNotice(registration) {
            if (registration && registration.isOpen) {
                $('#registrationContent').text(registration.notice);
                $('#registrationNotice').show();
            }
        }

        // 前往报名
        function goToRegistration() {
            if (parent && parent.addTab) {
                parent.addTab('考试报名', '/student/examRegistration/index');
            } else {
                window.location.href = '/student/examRegistration/index';
            }
        }

        // 刷新成绩
        function refreshScores() {
            loadExams();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#examList, .exam-summary, .filter-section').hide();
            } else {
                $('#loadingState').hide();
                $('#examList, .exam-summary, .filter-section').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
