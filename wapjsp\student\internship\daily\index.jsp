<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的实习课程</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实习日志页面样式 */
        .daily-header {
            background: linear-gradient(135deg, var(--warning-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .daily-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .daily-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .course-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--warning-color);
            position: relative;
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
            margin-right: var(--margin-sm);
        }
        
        .course-code {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        .course-index {
            background: var(--warning-color);
            color: white;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 600;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-row {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            color: var(--text-primary);
            font-weight: 500;
            min-width: 80px;
        }
        
        .detail-value {
            flex: 1;
            color: var(--text-secondary);
        }
        
        .course-location {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin: var(--margin-sm) 0;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .course-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-course-action {
            flex: 1;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-daily {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-daily:hover {
            background: var(--warning-dark);
            transform: translateY(-1px);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-view:hover {
            background: var(--info-dark);
            transform: translateY(-1px);
        }
        
        .btn-course-action:active {
            transform: translateY(0);
        }
        
        .time-selector {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin: var(--margin-sm) 0;
        }
        
        .time-selector-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .time-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .btn-time {
            background: var(--bg-primary);
            color: var(--text-primary);
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-time:hover {
            background: var(--warning-color);
            color: white;
            border-color: var(--warning-color);
        }
        
        .btn-time.current {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }
        
        .stats-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--info-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--warning-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        @media (max-width: 480px) {
            .course-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .course-index {
                align-self: flex-end;
                margin-top: var(--margin-sm);
            }
            
            .course-actions {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">我的实习课程</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 实习日志头部 -->
        <div class="daily-header">
            <div class="daily-title">我的实习课程</div>
            <div class="daily-desc">管理实习日志和课程信息</div>
        </div>
        
        <!-- 统计摘要 -->
        <div class="stats-summary">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                课程统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalCourses">0</div>
                    <div class="stat-label">总课程数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="activeCourses">0</div>
                    <div class="stat-label">进行中</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedReports">0</div>
                    <div class="stat-label">已提交日报</div>
                </div>
            </div>
        </div>
        
        <!-- 课程列表 -->
        <div id="courseList">
            <!-- 动态加载课程内容 -->
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-briefcase"></i>
            <div>暂无实习课程</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let courseData = [];
        let currentPage = 1;
        let pageSize = 100;
        let totalCount = 0;
        let currentTime = '${currentTime}';

        $(function() {
            initPage();
            loadCourseList(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载课程列表
        function loadCourseList(page, conditionChanged) {
            if (conditionChanged) {
                currentPage = 1;
            }

            showLoading(true);

            const url = "/student/internship/daily/queryPage";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    if (data && data.records) {
                        courseData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (courseData.length > 0) {
                            renderCourseList();
                            updateStats();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染课程列表
        function renderCourseList() {
            const container = $('#courseList');
            container.empty();
            $('#emptyState').hide();

            courseData.forEach(function(course, index) {
                const courseHtml = createCourseItem(course, index + 1);
                container.append(courseHtml);
            });
        }

        // 创建课程项目HTML
        function createCourseItem(course, index) {
            // 处理时间数组
            let times = [];
            if (course.TIMES) {
                times = course.TIMES.split(",");
            }

            // 检查是否可以填写今日日报
            if (course.KTB > 0 && course.YTB == 0 && times.indexOf(currentTime) == -1) {
                times.push(currentTime);
            }

            const hasReports = course.TBZS > 0;
            const location = (course.SJDD || '').replace(/,/g, "<br>").replace(/@/g, " ");

            return `
                <div class="course-item">
                    <div class="course-header">
                        <div class="course-title">
                            ${course.KCM || ''}
                            <div class="course-code">课程号：${course.KCH || ''} | 课序号：${course.KXH || ''}</div>
                        </div>
                        <div class="course-index">${index}</div>
                    </div>

                    <div class="course-details">
                        <div class="detail-row">
                            <span class="detail-label">学年学期：</span>
                            <span class="detail-value">${course.XNXQMC || ''}</span>
                        </div>
                    </div>

                    ${location ? `
                        <div class="course-location">
                            <strong>时间地点：</strong><br>
                            ${location}
                        </div>
                    ` : ''}

                    ${times.length > 0 ? createTimeSelector(course.ID, times) : ''}

                    <div class="course-actions">
                        ${createActionButtons(course.ID, times, hasReports)}
                    </div>
                </div>
            `;
        }

        // 创建时间选择器
        function createTimeSelector(courseId, times) {
            if (times.length <= 1) {
                return '';
            }

            let timeButtons = '';
            times.forEach(function(time) {
                const isCurrent = time === currentTime;
                timeButtons += `
                    <button class="btn-time ${isCurrent ? 'current' : ''}"
                            onclick="writeDailyReport('${courseId}', '${time}');">
                        ${time}
                    </button>
                `;
            });

            return `
                <div class="time-selector">
                    <div class="time-selector-title">可维护日期：</div>
                    <div class="time-buttons">
                        ${timeButtons}
                    </div>
                </div>
            `;
        }

        // 创建操作按钮
        function createActionButtons(courseId, times, hasReports) {
            let buttons = '';

            // 日报维护按钮
            if (times.length === 1) {
                buttons += `
                    <button class="btn-course-action btn-daily" onclick="writeDailyReport('${courseId}', '${times[0]}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>实习日报维护</span>
                    </button>
                `;
            } else if (times.length > 1) {
                buttons += `
                    <button class="btn-course-action btn-daily" onclick="writeDailyReport('${courseId}', '${times[0]}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>${times[0]} 日报</span>
                    </button>
                `;
            }

            // 查看日报按钮
            if (hasReports) {
                buttons += `
                    <button class="btn-course-action btn-view" onclick="viewDailyReports('${courseId}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>日报记录</span>
                    </button>
                `;
            }

            return buttons;
        }

        // 写实习日报
        function writeDailyReport(courseId, date) {
            const url = "/student/internship/daily/add?rwid=" + courseId + "&tbrq=" + date;

            if (parent && parent.addTab) {
                parent.addTab('实习日报维护', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看日报记录
        function viewDailyReports(courseId) {
            const url = "/student/internship/daily/view?rwid=" + courseId;

            if (parent && parent.addTab) {
                parent.addTab('实习日报记录', url);
            } else {
                window.location.href = url;
            }
        }

        // 更新统计信息
        function updateStats() {
            const totalCourses = courseData.length;
            let activeCourses = 0;
            let completedReports = 0;

            courseData.forEach(function(course) {
                if (course.KTB > 0 || course.YTB > 0) {
                    activeCourses++;
                }
                if (course.TBZS > 0) {
                    completedReports += parseInt(course.TBZS);
                }
            });

            $('#totalCourses').text(totalCourses);
            $('#activeCourses').text(activeCourses);
            $('#completedReports').text(completedReports);
        }

        // 显示空状态
        function showEmptyState() {
            $('#courseList').empty();
            $('#emptyState').show();

            // 重置统计
            $('#totalCourses').text(0);
            $('#activeCourses').text(0);
            $('#completedReports').text(0);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 刷新数据
        function refreshData() {
            loadCourseList(1, true);
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
