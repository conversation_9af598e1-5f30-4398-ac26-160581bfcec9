<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>论文提交</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 论文提交页面样式 */
        .submit-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .submit-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .submit-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-error {
            background: var(--error-light);
            color: var(--error-dark);
            border-left: 4px solid var(--error-color);
        }
        
        .notice-warning {
            background: var(--warning-light);
            color: var(--warning-dark);
            border-left: 4px solid var(--warning-color);
        }
        
        .notice-section i {
            margin-right: 8px;
        }
        
        .papers-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .papers-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .papers-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .papers-title i {
            color: var(--success-color);
        }
        
        .paper-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .paper-item:last-child {
            border-bottom: none;
        }
        
        .paper-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .paper-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .paper-content {
            flex: 1;
        }
        
        .paper-title-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .paper-teacher {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .paper-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-selected {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-submitted {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-failed {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-operation {
            flex: 1;
            min-width: 80px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .guidance-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .guidance-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .guidance-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .guidance-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .guidance-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .guidance-body {
            padding: var(--padding-md);
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .timeline-item {
            display: flex;
            margin-bottom: var(--margin-md);
            position: relative;
        }
        
        .timeline-indicator {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-small);
            margin-right: var(--margin-md);
            flex-shrink: 0;
        }
        
        .timeline-indicator.student {
            background: var(--success-color);
        }
        
        .timeline-indicator.teacher {
            background: var(--primary-color);
        }
        
        .timeline-content {
            flex: 1;
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-sm);
        }
        
        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .timeline-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .timeline-file {
            font-size: var(--font-size-small);
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .timeline-time {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .new-badge {
            background: var(--error-color);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: var(--font-size-mini);
            margin-left: 4px;
        }
        
        @media (max-width: 480px) {
            .paper-details {
                grid-template-columns: 1fr;
            }
            
            .operation-buttons {
                flex-direction: column;
            }
            
            .guidance-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">论文提交</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 论文提交头部 -->
        <div class="submit-header">
            <div class="submit-title">论文提交</div>
            <div class="submit-desc">管理和提交毕业论文</div>
        </div>
        
        <!-- 错误信息 -->
        <c:if test="${not empty errorMessage}">
            <div class="notice-section notice-error">
                <i class="ace-icon fa fa-exclamation-circle"></i>
                ${errorMessage}
            </div>
        </c:if>
        
        <!-- 提示信息 -->
        <c:if test="${empty errorMessage && open_message == '0'}">
            <div class="notice-section notice-warning">
                <i class="ace-icon fa fa-times"></i>
                当前不是毕业论文提交时间，请确认提交时间！
            </div>
        </c:if>
        
        <!-- 选题结果列表 -->
        <c:if test="${empty errorMessage && open_message == '1'}">
            <div class="papers-section">
                <div class="papers-header">
                    <div class="papers-title">
                        <i class="ace-icon fa fa-list"></i>
                        选题结果列表
                    </div>
                </div>
                
                <div id="papersList">
                    <!-- 动态加载论文列表 -->
                </div>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂时木有内容呀~~</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 论文指导过程模态框 -->
        <div class="guidance-modal" id="guidanceModal">
            <div class="guidance-content">
                <div class="guidance-header">
                    <div class="guidance-title" id="guidanceTitle">论文指导过程</div>
                    <button class="guidance-close" onclick="closeGuidanceModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="guidance-body" id="guidanceBody">
                    <!-- 动态加载指导过程 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let paperData = [];
        let schoolId = '${schoolid}';

        $(function() {
            initPage();

            // 如果开放提交，加载论文列表
            if ('${open_message}' === '1') {
                queryList();
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 查询论文列表
        function queryList() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/paperSubmit/query",
                type: "post",
                data: { zxjxjhh: '${zxjxjhh}' },
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.xtlist && data.xtlist.length > 0) {
                        paperData = data.xtlist;
                        renderPapersList();
                        showEmptyState(false);
                    } else {
                        paperData = [];
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("查询论文列表失败！");
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染论文列表
        function renderPapersList() {
            const container = $('#papersList');
            container.empty();

            paperData.forEach(function(item, index) {
                const itemHtml = createPaperItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建论文项目HTML
        function createPaperItem(item, index) {
            // 构建操作按钮
            let operationButtons = '';
            if (item.xtztdm === '02') {
                operationButtons += `
                    <button class="btn-operation btn-submit" onclick="submitPaper('${item.id.zxjxjhh}', '${item.id.tmbh}', '${item.id.xh}');">
                        <i class="ace-icon fa fa-pencil-square-o"></i>
                        <span>填写论文信息</span>
                    </button>
                `;
            }

            if (schoolId !== '100030' && item.submit_num > 0) {
                operationButtons += `
                    <button class="btn-operation btn-view" onclick="showRecord('${item.id.zxjxjhh}', '${item.id.tmbh}', '${item.id.xh}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看指导过程</span>
                    </button>
                `;
            }

            return `
                <div class="paper-item">
                    <div class="paper-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="paper-index">${index + 1}</div>
                            <div class="paper-content">
                                <div class="paper-title-text">${item.tmmc_xs || ''}</div>
                                <div class="paper-teacher">负责教师：${item.jsm || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="paper-details">
                        <div class="detail-item">
                            <span class="detail-label">题目编号</span>
                            <span>${item.id.tmbh || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">是否跨院系</span>
                            <span>${item.sfkyx || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">选题状态</span>
                            <span>
                                <span class="status-badge ${getSelectionStatusClass(item.xtztdm)}">
                                    ${item.xtztsm || ''}
                                </span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">论文状态</span>
                            <span>
                                <span class="status-badge ${getPaperStatusClass(item.lwtjztdm)}">
                                    ${item.lwtjztsm || ''}
                                </span>
                            </span>
                        </div>
                        ${item.wtgyy ? `
                        <div class="detail-item" style="grid-column: 1 / -1;">
                            <span class="detail-label">未通过原因</span>
                            <span style="color: var(--error-color);">${item.wtgyy}</span>
                        </div>
                        ` : ''}
                    </div>

                    ${operationButtons ? `
                    <div class="operation-buttons">
                        ${operationButtons}
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // 获取选题状态样式类
        function getSelectionStatusClass(xtztdm) {
            switch (xtztdm) {
                case '02': return 'status-selected';
                default: return 'status-submitted';
            }
        }

        // 获取论文状态样式类
        function getPaperStatusClass(lwtjztdm) {
            switch (lwtjztdm) {
                case '01': return 'status-submitted';
                case '02': return 'status-failed';
                default: return 'status-selected';
            }
        }

        // 提交论文
        function submitPaper(zxjxjhh, tmbh, xh) {
            if (parent && parent.addTab) {
                parent.addTab('填写论文信息', '/student/personalManagement/paperSubmit/viewPaper/' + tmbh);
            } else {
                location.href = "/student/personalManagement/paperSubmit/viewPaper/" + tmbh;
            }
        }

        // 显示指导记录
        function showRecord(zxjxjhh, tmbh, xh) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/paperSubmit/showRecord",
                type: "post",
                data: {
                    zxjxjhh: zxjxjhh,
                    tmbh: tmbh,
                    xh: xh
                },
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.list && data.list.length > 0) {
                        renderGuidanceRecord(data);
                        showGuidanceModal();
                    } else {
                        showInfo("暂无指导记录");
                    }
                },
                error: function(xhr) {
                    showError("查询指导记录失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染指导记录
        function renderGuidanceRecord(data) {
            $('#guidanceTitle').text(`《${data.tmmc}》论文指导过程`);

            const container = $('#guidanceBody');
            container.empty();

            let typearr = [];
            let newItems = [];
            let oldItems = [];

            data.list.forEach(function(item) {
                const isNewType = !typearr.includes(item[9]);
                if (isNewType) {
                    typearr.push(item[9]);
                }

                const itemHtml = createTimelineItem(item, isNewType);
                if (isNewType) {
                    newItems.push(itemHtml);
                } else {
                    oldItems.push(itemHtml);
                }
            });

            container.html(newItems.join('') + oldItems.join(''));
        }

        // 创建时间线项目HTML
        function createTimelineItem(item, isNew) {
            const indicatorClass = item[0] === 's' ? 'student' : 'teacher';
            const indicatorIcon = item[0] === 's' ? 'fa-male' : 'fa-user';

            return `
                <div class="timeline-item">
                    <div class="timeline-indicator ${indicatorClass}">
                        <i class="ace-icon ${indicatorIcon}"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <div class="timeline-text">${item[1] || ''}</div>
                            <div class="timeline-time">
                                <i class="ace-icon fa fa-clock-o"></i>
                                ${item[2] || ''}
                            </div>
                        </div>
                        <div>
                            <i class="ace-icon fa fa-hand-o-right"></i>
                            <a href="/student/personalManagement/paperSubmit/downloadPaper/${item[4]}/${item[5]}/${item[6]}/${item[7]}/${item[8]}"
                               class="timeline-file">
                                【${item[9]}】${item[3]}
                            </a>
                            ${isNew ? '<span class="new-badge">新</span>' : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示指导过程模态框
        function showGuidanceModal() {
            $('#guidanceModal').fadeIn(300);
        }

        // 关闭指导过程模态框
        function closeGuidanceModal() {
            $('#guidanceModal').fadeOut(300);
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.papers-section').hide();
            } else {
                $('#emptyState').hide();
                $('.papers-section').show();
            }
        }

        // 刷新数据
        function refreshData() {
            if ('${open_message}' === '1') {
                queryList();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示信息
        function showInfo(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.guidance-modal', function(e) {
            if (e.target === this) {
                closeGuidanceModal();
            }
        });
    </script>
</body>
</html>
