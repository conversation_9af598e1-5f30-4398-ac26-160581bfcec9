<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>论文管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 论文管理页面样式 */
        .thesis-header {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .thesis-icon {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            opacity: 0.9;
        }
        
        .thesis-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .thesis-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .function-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .function-card:hover::before {
            left: 100%;
        }
        
        .function-card:hover {
            border-color: var(--info-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .function-card:active {
            transform: translateY(-2px);
        }
        
        .function-icon {
            font-size: 40px;
            margin-bottom: var(--margin-md);
            color: var(--info-color);
            position: relative;
            z-index: 1;
        }
        
        .function-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .function-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
            position: relative;
            z-index: 1;
        }
        
        .function-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: var(--warning-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: var(--font-size-mini);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }
        
        .thesis-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--info-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--info-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-actions-title i {
            color: var(--success-color);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-item {
            text-align: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .action-item:hover {
            background: var(--bg-tertiary);
            transform: translateY(-2px);
        }
        
        .action-item-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--info-light);
            color: var(--info-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto var(--margin-sm);
        }
        
        .action-item-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .thesis-progress {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .progress-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .progress-title i {
            color: var(--warning-color);
        }
        
        .progress-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .progress-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .progress-item:last-child {
            margin-bottom: 0;
        }
        
        .progress-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: var(--info-light);
            color: var(--info-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-right: var(--margin-sm);
        }
        
        .progress-content {
            flex: 1;
        }
        
        .progress-item-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .progress-item-desc {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .progress-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-not-started {
            background: var(--text-disabled);
            color: white;
        }
        
        @media (max-width: 480px) {
            .function-grid {
                grid-template-columns: 1fr;
            }
            
            .action-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">论文管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 论文管理头部 -->
        <div class="thesis-header">
            <div class="thesis-icon">
                <i class="ace-icon fa fa-file-text"></i>
            </div>
            <div class="thesis-title">论文管理中心</div>
            <div class="thesis-desc">论文写作、答辩、文档管理</div>
        </div>
        
        <!-- 论文统计 -->
        <div class="thesis-stats">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                论文统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalThesis">0</div>
                    <div class="stat-label">总论文数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedStages">0</div>
                    <div class="stat-label">已完成阶段</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="pendingTasks">0</div>
                    <div class="stat-label">待办任务</div>
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="quick-actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                快速操作
            </div>
            <div class="action-grid">
                <div class="action-item" onclick="goToFunction('thesisDefenseInfo');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-microphone"></i>
                    </div>
                    <div class="action-item-title">我的答辩</div>
                </div>
                <div class="action-item" onclick="goToFunction('stageddocuments');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-upload"></i>
                    </div>
                    <div class="action-item-title">文档提交</div>
                </div>
                <div class="action-item" onclick="goToFunction('thesisProgress');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-tasks"></i>
                    </div>
                    <div class="action-item-title">进度管理</div>
                </div>
                <div class="action-item" onclick="goToFunction('thesisGuidance');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-comments"></i>
                    </div>
                    <div class="action-item-title">指导记录</div>
                </div>
            </div>
        </div>
        
        <!-- 功能模块 -->
        <div class="function-grid">
            <div class="function-card" onclick="goToFunction('thesisDefenseInfo');">
                <div class="function-badge">
                    <i class="ace-icon fa fa-star"></i>
                </div>
                <div class="function-icon">
                    <i class="ace-icon fa fa-microphone"></i>
                </div>
                <div class="function-title">我的答辩</div>
                <div class="function-desc">查看答辩安排和信息</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('stageddocuments');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-upload"></i>
                </div>
                <div class="function-title">阶段文档提交</div>
                <div class="function-desc">提交各阶段论文文档</div>
            </div>
        </div>
        
        <!-- 论文进度 -->
        <div class="thesis-progress" id="thesisProgress" style="display: none;">
            <div class="progress-title">
                <i class="ace-icon fa fa-tasks"></i>
                论文进度
            </div>
            <div id="progressList">
                <!-- 动态加载进度内容 -->
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let thesisStats = {};

        $(function() {
            initPage();
            loadThesisStats();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载论文统计
        function loadThesisStats() {
            showLoading(true);

            // 获取论文统计信息
            $.ajax({
                url: "/student/thesis/getStats",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        thesisStats = data.data;
                        updateStats();
                        loadThesisProgress();
                    } else {
                        // 设置默认统计
                        updateStats({
                            totalThesis: 0,
                            completedStages: 0,
                            pendingTasks: 0
                        });
                    }
                },
                error: function() {
                    console.log('获取论文统计失败');
                    // 设置默认统计
                    updateStats({
                        totalThesis: 0,
                        completedStages: 0,
                        pendingTasks: 0
                    });
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新统计信息
        function updateStats(stats = thesisStats) {
            $('#totalThesis').text(stats.totalThesis || 0);
            $('#completedStages').text(stats.completedStages || 0);
            $('#pendingTasks').text(stats.pendingTasks || 0);
        }

        // 加载论文进度
        function loadThesisProgress() {
            $.ajax({
                url: "/student/thesis/getProgress",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success && data.data && data.data.length > 0) {
                        renderThesisProgress(data.data);
                        $('#thesisProgress').show();
                    }
                },
                error: function() {
                    console.log('获取论文进度失败');
                }
            });
        }

        // 渲染论文进度
        function renderThesisProgress(progressData) {
            const container = $('#progressList');
            container.empty();

            progressData.forEach(function(progress) {
                const progressHtml = `
                    <div class="progress-item" onclick="handleProgress('${progress.id}', '${progress.type}');">
                        <div class="progress-icon">
                            <i class="ace-icon fa fa-${getProgressIcon(progress.type)}"></i>
                        </div>
                        <div class="progress-content">
                            <div class="progress-item-title">${progress.title}</div>
                            <div class="progress-item-desc">${progress.description}</div>
                        </div>
                        <div class="progress-status ${getProgressStatusClass(progress.status)}">
                            ${getProgressStatusText(progress.status)}
                        </div>
                    </div>
                `;
                container.append(progressHtml);
            });
        }

        // 获取进度图标
        function getProgressIcon(type) {
            switch(type) {
                case 'defense': return 'microphone';
                case 'document': return 'upload';
                case 'guidance': return 'comments';
                case 'review': return 'eye';
                default: return 'file-text';
            }
        }

        // 获取进度状态样式类
        function getProgressStatusClass(status) {
            switch(status) {
                case 'completed': return 'status-completed';
                case 'pending': return 'status-pending';
                case 'not-started': return 'status-not-started';
                default: return 'status-not-started';
            }
        }

        // 获取进度状态文本
        function getProgressStatusText(status) {
            switch(status) {
                case 'completed': return '已完成';
                case 'pending': return '进行中';
                case 'not-started': return '未开始';
                default: return '未开始';
            }
        }

        // 处理进度点击
        function handleProgress(progressId, progressType) {
            // 根据进度类型跳转到相应页面
            switch(progressType) {
                case 'defense':
                    goToFunction('thesisDefenseInfo');
                    break;
                case 'document':
                    goToFunction('stageddocuments');
                    break;
                case 'guidance':
                    goToFunction('thesisGuidance');
                    break;
                default:
                    showError('功能暂未开放');
            }
        }

        // 跳转到功能页面
        function goToFunction(functionName) {
            let url = '';
            let title = '';

            switch(functionName) {
                case 'thesisDefenseInfo':
                    url = '/student/thesis/thesisDefenseInfo/index';
                    title = '我的答辩';
                    break;
                case 'stageddocuments':
                    url = '/student/thesis/stageddocuments/uploadsub';
                    title = '阶段文档提交';
                    break;
                case 'thesisProgress':
                    showError('论文进度管理功能开发中');
                    return;
                case 'thesisGuidance':
                    showError('论文指导记录功能开发中');
                    return;
                default:
                    showError('功能暂未开放');
                    return;
            }

            if (parent && parent.addTab) {
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 刷新数据
        function refreshData() {
            loadThesisStats();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
