<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>系统概览</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 系统概览页面样式 */
        .overview-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .system-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .stats-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .stat-item {
            text-align: center;
            padding: var(--padding-md);
            border-radius: 8px;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--margin-xs);
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .module-categories {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .categories-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .categories-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .category-list {
            display: grid;
            gap: var(--spacing-sm);
        }
        
        .category-item {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .category-item:active {
            background: var(--bg-color-active);
        }
        
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .category-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }
        
        .category-icon {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .category-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .category-description {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .system-features {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .features-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .features-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .feature-list {
            display: grid;
            gap: var(--spacing-sm);
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            background: var(--bg-tertiary);
        }
        
        .feature-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: var(--success-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: 14px;
        }
        
        .feature-content {
            flex: 1;
        }
        
        .feature-name {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .feature-description {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .actions-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .action-btn {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-primary);
            text-decoration: none;
        }
        
        .action-btn:active {
            background: var(--bg-color-active);
        }
        
        .action-btn i {
            display: block;
            font-size: 24px;
            margin-bottom: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .action-btn span {
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">系统概览</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="overview-header">
            <div class="header-title">学生教务管理系统</div>
            <div class="header-subtitle">移动端完整功能概览</div>
        </div>
        
        <!-- 系统统计 -->
        <div class="system-stats">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>系统统计</span>
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">239</div>
                    <div class="stat-label">功能模块</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">252</div>
                    <div class="stat-label">页面总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15</div>
                    <div class="stat-label">功能分类</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">移动端适配</div>
                </div>
            </div>
        </div>
        
        <!-- 模块分类 -->
        <div class="module-categories">
            <div class="categories-title">
                <i class="ace-icon fa fa-th-large"></i>
                <span>功能模块分类</span>
            </div>
            <div class="category-list">
                <div class="category-item" onclick="viewCategory('academic')">
                    <div class="category-header">
                        <div class="category-name">
                            <i class="ace-icon fa fa-graduation-cap category-icon"></i>
                            学术管理类
                        </div>
                        <div class="category-count">69</div>
                    </div>
                    <div class="category-description">学分检查、选课管理、成绩查询、课表管理等核心学术功能</div>
                </div>
                
                <div class="category-item" onclick="viewCategory('practice')">
                    <div class="category-header">
                        <div class="category-name">
                            <i class="ace-icon fa fa-flask category-icon"></i>
                            实践教学类
                        </div>
                        <div class="category-count">24</div>
                    </div>
                    <div class="category-description">实验管理、实习管理、论文管理、劳动教育等实践教学功能</div>
                </div>
                
                <div class="category-item" onclick="viewCategory('personal')">
                    <div class="category-header">
                        <div class="category-name">
                            <i class="ace-icon fa fa-user category-icon"></i>
                            个人事务类
                        </div>
                        <div class="category-count">57</div>
                    </div>
                    <div class="category-description">个人信息、学籍管理、证明打印、学科竞赛等个人事务功能</div>
                </div>
                
                <div class="category-item" onclick="viewCategory('campus')">
                    <div class="category-header">
                        <div class="category-name">
                            <i class="ace-icon fa fa-university category-icon"></i>
                            校园服务类
                        </div>
                        <div class="category-count">18</div>
                    </div>
                    <div class="category-description">学生证管理、校园卡服务、证明打印等校园生活服务功能</div>
                </div>
                
                <div class="category-item" onclick="viewCategory('resources')">
                    <div class="category-header">
                        <div class="category-name">
                            <i class="ace-icon fa fa-building category-icon"></i>
                            教学资源类
                        </div>
                        <div class="category-count">9</div>
                    </div>
                    <div class="category-description">教学资源查询、空闲教室、教师课表等教学资源管理功能</div>
                </div>
                
                <div class="category-item" onclick="viewCategory('calendar')">
                    <div class="category-header">
                        <div class="category-name">
                            <i class="ace-icon fa fa-calendar category-icon"></i>
                            日历管理类
                        </div>
                        <div class="category-count">9</div>
                    </div>
                    <div class="category-description">校历查询、课程日历、作息时间、时间安排等时间管理功能</div>
                </div>
            </div>
        </div>
        
        <!-- 系统特色 -->
        <div class="system-features">
            <div class="features-title">
                <i class="ace-icon fa fa-star"></i>
                <span>系统特色</span>
            </div>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-mobile"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-name">完全响应式设计</div>
                        <div class="feature-description">适配各种屏幕尺寸，提供优秀的移动端体验</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-check"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-name">业务逻辑一致</div>
                        <div class="feature-description">与PC端保持完全一致的业务逻辑和功能特性</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-paint-brush"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-name">统一UI设计</div>
                        <div class="feature-description">统一的设计语言和交互规范，提供一致的用户体验</div>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-rocket"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-name">性能优化</div>
                        <div class="feature-description">优化的页面加载速度和流畅的操作体验</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                <span>快捷操作</span>
            </div>
            <div class="action-buttons">
                <a href="/student/weeklySchedule" class="action-btn">
                    <i class="ace-icon fa fa-table"></i>
                    <span>课程表</span>
                </a>
                <a href="/student/gradeQuery" class="action-btn">
                    <i class="ace-icon fa fa-line-chart"></i>
                    <span>成绩查询</span>
                </a>
                <a href="/student/examManagement" class="action-btn">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <span>考试管理</span>
                </a>
                <a href="/student/personalManagement" class="action-btn">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人管理</span>
                </a>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 查看分类详情
        function viewCategory(categoryType) {
            // 实现分类详情查看逻辑
            console.log('View category:', categoryType);
        }

        // 刷新数据
        function refreshData() {
            showLoading(true);
            
            setTimeout(function() {
                showLoading(false);
                showSuccess('系统概览已更新');
            }, 1000);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            // 这里可以添加成功提示的实现
            console.log('Success: ' + message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
        }
    </script>
</body>
</html>
