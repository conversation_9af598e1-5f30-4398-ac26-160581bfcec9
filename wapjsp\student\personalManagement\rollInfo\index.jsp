<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>个人信息</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 个人信息页面样式 */
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: var(--padding-lg);
            text-align: center;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--margin-md);
            font-size: 32px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .profile-name {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .profile-id {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .info-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .section-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .info-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            flex: 0 0 80px;
        }
        
        .info-value {
            color: var(--text-primary);
            font-size: var(--font-size-base);
            flex: 1;
            text-align: right;
            word-break: break-all;
        }
        
        .info-value.empty {
            color: var(--text-disabled);
            font-style: italic;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-active {
            background: var(--success-color);
            color: white;
        }
        
        .status-inactive {
            background: var(--error-color);
            color: white;
        }
        
        .status-warning {
            background: var(--warning-color);
            color: white;
        }
        
        .action-buttons {
            padding: var(--padding-lg) var(--padding-md);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline:active {
            background: var(--primary-color);
            color: white;
        }
        
        /* 照片预览 */
        .photo-preview {
            width: 100px;
            height: 120px;
            border-radius: 6px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-disabled);
            font-size: var(--font-size-small);
            text-align: center;
            border: 1px solid var(--border-primary);
        }
        
        .photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
        }
        
        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .stats-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-number {
            font-size: var(--font-size-h2);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stats-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">个人信息</div>
            <div class="navbar-action" onclick="refreshInfo();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 个人资料头部 -->
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="ace-icon fa fa-user"></i>
            </div>
            <div class="profile-name" id="studentName">加载中...</div>
            <div class="profile-id" id="studentId">学号：加载中...</div>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-grid" id="statsGrid" style="display: none;">
            <div class="stats-card">
                <div class="stats-number" id="totalCredits">0</div>
                <div class="stats-label">已获学分</div>
            </div>
            <div class="stats-card">
                <div class="stats-number" id="gpaScore">0.0</div>
                <div class="stats-label">平均绩点</div>
            </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="info-section">
            <div class="section-header">
                <i class="ace-icon fa fa-user"></i>
                <span>基本信息</span>
            </div>
            <div class="info-item">
                <div class="info-label">姓名</div>
                <div class="info-value" id="name">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">学号</div>
                <div class="info-value" id="studentNo">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">性别</div>
                <div class="info-value" id="gender">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">出生日期</div>
                <div class="info-value" id="birthday">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">身份证号</div>
                <div class="info-value" id="idCard">-</div>
            </div>
        </div>
        
        <!-- 学籍信息 -->
        <div class="info-section">
            <div class="section-header">
                <i class="ace-icon fa fa-graduation-cap"></i>
                <span>学籍信息</span>
            </div>
            <div class="info-item">
                <div class="info-label">学院</div>
                <div class="info-value" id="college">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">专业</div>
                <div class="info-value" id="major">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">班级</div>
                <div class="info-value" id="class">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">年级</div>
                <div class="info-value" id="grade">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">学制</div>
                <div class="info-value" id="duration">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">学籍状态</div>
                <div class="info-value">
                    <span class="status-badge status-active" id="status">在读</span>
                </div>
            </div>
        </div>
        
        <!-- 联系信息 -->
        <div class="info-section">
            <div class="section-header">
                <i class="ace-icon fa fa-phone"></i>
                <span>联系信息</span>
            </div>
            <div class="info-item">
                <div class="info-label">手机号</div>
                <div class="info-value" id="mobile">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">邮箱</div>
                <div class="info-value" id="email">-</div>
            </div>
            <div class="info-item">
                <div class="info-label">家庭住址</div>
                <div class="info-value" id="address">-</div>
            </div>
        </div>
        
        <!-- 照片信息 -->
        <div class="info-section">
            <div class="section-header">
                <i class="ace-icon fa fa-camera"></i>
                <span>照片信息</span>
            </div>
            <div class="info-item">
                <div class="info-label">学籍照片</div>
                <div class="info-value">
                    <div class="photo-preview" id="studentPhoto">
                        <span>暂无照片</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn-mobile btn-outline flex-1" onclick="editInfo();">
                <i class="ace-icon fa fa-edit"></i>
                <span>修改信息</span>
            </button>
            <button class="btn-mobile btn-primary flex-1" onclick="downloadInfo();">
                <i class="ace-icon fa fa-download"></i>
                <span>导出信息</span>
            </button>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let studentInfo = {};

        $(function() {
            initPage();
            loadStudentInfo();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学生信息
        function loadStudentInfo() {
            showLoading(true);
            
            // 这里使用原有的数据接口
            $.ajax({
                url: "/student/personalManagement/rollInfo/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data && data.data) {
                        studentInfo = data.data;
                        renderStudentInfo();
                        loadStatistics();
                    } else {
                        showError("获取学生信息失败");
                    }
                },
                error: function(xhr) {
                    showError("网络错误，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染学生信息
        function renderStudentInfo() {
            const info = studentInfo;
            
            // 更新头部信息
            $('#studentName').text(info.xm || '未知');
            $('#studentId').text('学号：' + (info.xh || '未知'));
            
            // 更新基本信息
            $('#name').text(info.xm || '-');
            $('#studentNo').text(info.xh || '-');
            $('#gender').text(info.xbm || '-');
            $('#birthday').text(info.csrq || '-');
            $('#idCard').text(formatIdCard(info.sfzh) || '-');
            
            // 更新学籍信息
            $('#college').text(info.xymc || '-');
            $('#major').text(info.zymc || '-');
            $('#class').text(info.bjmc || '-');
            $('#grade').text(info.nj || '-');
            $('#duration').text(info.xz || '-');
            
            // 更新联系信息
            $('#mobile').text(info.sjhm || '-');
            $('#email').text(info.dzyx || '-');
            $('#address').text(info.jtdz || '-');
            
            // 更新学籍状态
            updateStatus(info.xjzt);
            
            // 加载学籍照片
            loadStudentPhoto(info.xh);
        }

        // 格式化身份证号（隐藏中间部分）
        function formatIdCard(idCard) {
            if (!idCard || idCard.length < 10) return idCard;
            return idCard.substring(0, 6) + '****' + idCard.substring(idCard.length - 4);
        }

        // 更新学籍状态
        function updateStatus(status) {
            const statusElement = $('#status');
            statusElement.removeClass('status-active status-inactive status-warning');
            
            switch(status) {
                case '在读':
                case '正常':
                    statusElement.addClass('status-active').text('在读');
                    break;
                case '休学':
                case '暂停':
                    statusElement.addClass('status-warning').text('休学');
                    break;
                case '毕业':
                    statusElement.addClass('status-active').text('毕业');
                    break;
                default:
                    statusElement.addClass('status-inactive').text(status || '未知');
            }
        }

        // 加载学籍照片
        function loadStudentPhoto(studentNo) {
            if (!studentNo) return;
            
            const photoUrl = `/student/personalManagement/rollInfo/getStudentPhoto?xh=${studentNo}`;
            const img = new Image();
            
            img.onload = function() {
                $('#studentPhoto').html(`<img src="${photoUrl}" alt="学籍照片">`);
            };
            
            img.onerror = function() {
                $('#studentPhoto').html('<span>暂无照片</span>');
            };
            
            img.src = photoUrl;
        }

        // 加载统计信息
        function loadStatistics() {
            $.ajax({
                url: "/student/personalManagement/rollInfo/getStudentStats",
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data && data.data) {
                        $('#totalCredits').text(data.data.totalCredits || '0');
                        $('#gpaScore').text(data.data.gpa || '0.0');
                        $('#statsGrid').show();
                    }
                },
                error: function() {
                    // 统计信息加载失败不影响主要功能
                    console.log('统计信息加载失败');
                }
            });
        }

        // 修改信息
        function editInfo() {
            // 跳转到信息修改页面
            if (parent && parent.addTab) {
                parent.addTab('修改个人信息', '/student/personalManagement/personalInfoUpdate/index');
            } else {
                window.location.href = '/student/personalManagement/personalInfoUpdate/index';
            }
        }

        // 导出信息
        function downloadInfo() {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要导出个人信息吗？', function(confirmed) {
                    if (confirmed) {
                        window.open('/student/personalManagement/rollInfo/exportInfo');
                    }
                });
            } else {
                if (confirm('确定要导出个人信息吗？')) {
                    window.open('/student/personalManagement/rollInfo/exportInfo');
                }
            }
        }

        // 刷新信息
        function refreshInfo() {
            loadStudentInfo();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.info-section, .profile-header, .stats-grid, .action-buttons').hide();
            } else {
                $('#loadingState').hide();
                $('.info-section, .profile-header, .action-buttons').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
