<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="data" uri="http://www.urpSoft.com/data" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学籍信息</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学籍信息页面样式 - 统一移动端风格 */
        .info-cards-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin: var(--margin-md);
        }

        .info-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-primary);
        }

        .info-card:active {
            transform: scale(0.98);
            background: var(--bg-secondary);
        }

        .info-card-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 auto var(--margin-xs);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .info-card-icon.primary {
            background: var(--primary-color);
        }

        .info-card-icon.success {
            background: var(--success-color);
        }

        .info-card-icon.warning {
            background: var(--warning-color);
        }

        .info-card-icon.error {
            background: var(--error-color);
        }

        .info-card-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 4px;
        }

        .info-card-subtitle {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }

        .info-card-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        /* 学生照片 */
        .student-photo {
            width: 80px;
            height: 100px;
            border-radius: 6px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--margin-md);
            border: 2px solid var(--border-primary);
            overflow: hidden;
        }

        .student-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .student-photo .placeholder {
            color: var(--text-disabled);
            font-size: var(--font-size-small);
            text-align: center;
        }

        /* 详情面板 */
        .detail-panel {
            position: fixed;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: var(--bg-primary);
            z-index: 1000;
            transition: right 0.3s ease;
            overflow-y: auto;
        }

        .detail-panel.show {
            right: 0;
        }

        .detail-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .detail-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
        }

        .detail-close {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 4px;
        }

        .detail-content {
            padding: var(--padding-md);
        }

        /* 方案信息树形结构 */
        .plan-tree {
            background: var(--bg-primary);
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: var(--margin-md);
        }

        .tree-node {
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
        }

        .tree-node:last-child {
            border-bottom: none;
        }

        .tree-node-header {
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .tree-node-title {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
        }

        .tree-node-arrow {
            color: var(--text-secondary);
            transition: transform 0.3s ease;
        }

        .tree-node.expanded .tree-node-arrow {
            transform: rotate(90deg);
        }

        .tree-node-content {
            display: none;
            padding: 0 var(--padding-md) var(--padding-md);
            background: var(--bg-secondary);
        }

        .tree-node.expanded .tree-node-content {
            display: block;
        }

        /* 加载覆盖层 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: var(--bg-primary);
            padding: var(--padding-lg);
            border-radius: 8px;
            text-align: center;
            color: var(--text-primary);
        }

        .loading-spinner i {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: var(--margin-xs);
        }

        /* 可点击元素样式 */
        .clickable {
            color: var(--primary-color) !important;
            cursor: pointer;
            text-decoration: underline;
        }

        .clickable:active {
            color: var(--primary-dark) !important;
        }

        /* 分割线 */
        .divider-mobile {
            height: 1px;
            background: var(--divider-color);
            margin: var(--margin-md) 0;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学籍信息</div>
            <div class="navbar-action" onclick="refreshPage();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 评估提示 -->
        <c:if test="${onOff == '0'}">
            <div class="alert-mobile alert-warning">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>没有完成评估，不能查看学籍信息！</span>
            </div>
        </c:if>

        <c:if test="${onOff == '1'}">
        <!-- 学生基本信息头部 -->
        <div class="card-mobile">
            <div class="card-header">
                <i class="ace-icon fa fa-user"></i>
                <span>基本信息</span>
            </div>
            <div class="card-body">
                <div class="row-mobile">
                    <div class="col-mobile-4">
                        <div class="student-photo" id="studentPhoto">
                            <div class="placeholder">
                                <i class="ace-icon fa fa-user"></i>
                                <div>照片</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-mobile-8">
                        <div class="info-item-mobile">
                            <span class="label">姓名：</span>
                            <span class="value" id="studentName">${xjxx.xm}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">学号：</span>
                            <span class="value" id="studentNo">${xjxx.xh}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">院系：</span>
                            <span class="value">${xjxx.xsh}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">专业：</span>
                            <span class="value">${xjxx.zyh}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 与我相关的信息卡片 -->
        <div class="card-mobile">
            <div class="card-header">
                <i class="ace-icon fa fa-cogs"></i>
                <span>与我相关的信息</span>
            </div>
            <div class="card-body">
                <div class="info-cards-grid">
                    <!-- 主修方案 -->
                    <c:forEach items="${XsPyViewList}" var="xp" varStatus="s">
                        <c:if test="${xp.id.jhFajhb.xdlxdm=='00001'}">
                            <div class="info-card" onclick="showPlanDetail('zx', '${xp.id.jhFajhb.fajhh}');">
                                <div class="info-card-icon primary">
                                    <i class="ace-icon fa fa-graduation-cap"></i>
                                </div>
                                <div class="info-card-title">
                                    <c:choose>
                                        <c:when test="${fn:length(xp.id.jhFajhb.famc) > 8}">
                                            ${fn:substring(xp.id.jhFajhb.famc, 0, 8)}...
                                        </c:when>
                                        <c:otherwise>
                                            ${xp.id.jhFajhb.famc}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                                <div class="info-card-subtitle">主修方案</div>
                                <input type="hidden" id="zx" value="${xp.id.jhFajhb.fajhh}"/>
                            </div>
                        </c:if>
                    </c:forEach>

                    <!-- 辅修方案 -->
                    <c:forEach items="${XsPyViewList}" var="xp1" varStatus="s">
                        <c:if test="${xp1.id.jhFajhb.xdlxdm=='00002'}">
                            <div class="info-card" onclick="showPlanDetail('fx', '${xp1.id.jhFajhb.fajhh}');">
                                <div class="info-card-icon success">
                                    <i class="ace-icon fa fa-book"></i>
                                </div>
                                <div class="info-card-title">
                                    <c:choose>
                                        <c:when test="${fn:length(xp1.id.jhFajhb.famc) > 8}">
                                            ${fn:substring(xp1.id.jhFajhb.famc, 0, 8)}...
                                        </c:when>
                                        <c:otherwise>
                                            ${xp1.id.jhFajhb.famc}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                                <div class="info-card-subtitle">辅修方案</div>
                                <input type="hidden" id="fx" value="${xp1.id.jhFajhb.fajhh}"/>
                            </div>
                        </c:if>
                    </c:forEach>

                    <!-- 双学位 -->
                    <c:forEach items="${XsPyViewList}" var="xp2" varStatus="s">
                        <c:if test="${xp2.id.jhFajhb.xdlxdm=='00003'}">
                            <div class="info-card" onclick="showPlanDetail('fx', '${xp2.id.jhFajhb.fajhh}');">
                                <div class="info-card-icon success">
                                    <i class="ace-icon fa fa-certificate"></i>
                                </div>
                                <div class="info-card-title">
                                    <c:choose>
                                        <c:when test="${fn:length(xp2.id.jhFajhb.famc) > 8}">
                                            ${fn:substring(xp2.id.jhFajhb.famc, 0, 8)}...
                                        </c:when>
                                        <c:otherwise>
                                            ${xp2.id.jhFajhb.famc}
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                                <div class="info-card-subtitle">双学位</div>
                                <input type="hidden" id="fx" value="${xp2.id.jhFajhb.fajhh}"/>
                            </div>
                        </c:if>
                    </c:forEach>

                    <!-- 学籍异动 -->
                    <c:if test="${ydcount!=0}">
                        <div class="info-card" onclick="showAnomalyDetail();">
                            <div class="info-card-icon warning">
                                <i class="ace-icon fa fa-exclamation-triangle"></i>
                            </div>
                            <div class="info-card-number">${ydcount}</div>
                            <div class="info-card-subtitle">学籍异动</div>
                        </div>
                    </c:if>

                    <!-- 奖惩信息 -->
                    <c:if test="${jccount!=0}">
                        <div class="info-card" onclick="showRewardDetail();">
                            <div class="info-card-icon error">
                                <i class="ace-icon fa fa-trophy"></i>
                            </div>
                            <div class="info-card-number">${jccount}</div>
                            <div class="info-card-subtitle">奖惩信息</div>
                        </div>
                    </c:if>
                </div>
            </div>
        </div>

        <!-- 详细学籍信息 - 使用配置化显示 -->
        <c:if test="${fn:length(xxxs) > 0}">
            <!-- 使用配置化字段显示 -->
            <c:set var="fzh" value="" scope="request"/>
            <c:set var="fznum" value="1" scope="request"/>

            <c:forEach var="xxxs" items="${xxxs}" varStatus="status">
                <c:if test="${xxxs.xxfz != fzh}">
                    <c:if test="${status.count != 1}">
                        </div> <!-- 结束上一个分组 -->
                    </c:if>

                    <!-- 开始新分组 -->
                    <div class="card-mobile">
                        <div class="card-header">
                            <i class="ace-icon fa fa-info-circle"></i>
                            <span>${data:getJsonPropertyValue(fzinfojson,xxxs.xxfz)}</span>
                        </div>
                        <div class="card-body">

                    <c:set var="fzh" value="${xxxs.xxfz}" scope="request"/>
                    <c:set var="fznum" value="1" scope="request"/>
                </c:if>

                <c:if test="${xxxs.sfpk == 1}">
                    <div class="info-item-mobile">
                        <span class="label">${xxxs.fld_name}：</span>
                        <span class="value">${data:getJsonPropertyValue(xjxxjson,xxxs.fld_id)}</span>
                    </div>
                    <c:set var="fznum" value="${fznum+1}" scope="request"/>
                </c:if>

                <c:if test="${xxxs.sfpk == 0}">
                    <c:if test="${data:getJsonPropertyValue(xjxxjson,xxxs.fld_id) != null && data:getJsonPropertyValue(xjxxjson,xxxs.fld_id) != ''}">
                        <div class="info-item-mobile">
                            <span class="label">${xxxs.fld_name}：</span>
                            <span class="value">${data:getJsonPropertyValue(xjxxjson,xxxs.fld_id)}</span>
                        </div>
                        <c:set var="fznum" value="${fznum+1}" scope="request"/>
                    </c:if>
                </c:if>
            </c:forEach>
            </div> <!-- 结束最后一个分组 -->
        </c:if>

        <!-- 默认学籍信息显示（当没有配置化字段时） -->
        <c:if test="${fn:length(xxxs) <= 0}">
            <!-- 基本信息 -->
            <div class="card-mobile">
                <div class="card-header">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>
                <div class="card-body">
                    <div class="info-item-mobile">
                        <span class="label">身高：</span>
                        <span class="value">${grxx.grsg}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">体重：</span>
                        <span class="value">${grxx.grtz}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">电子邮件：</span>
                        <span class="value clickable" onclick="showSensitiveInfo('${grxx.email}', this);">
                            <c:choose>
                                <c:when test="${not empty grxx.email}">******</c:when>
                                <c:otherwise>-</c:otherwise>
                            </c:choose>
                        </span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">QQ号：</span>
                        <span class="value">${grxx.qqh}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">个人简介：</span>
                        <span class="value">${grxx.grjj}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">个人主页：</span>
                        <span class="value">${grxx.grzy}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">手机：</span>
                        <span class="value clickable" onclick="showSensitiveInfo('${grxx.brlxdh}', this);">
                            <c:choose>
                                <c:when test="${not empty grxx.brlxdh}">******</c:when>
                                <c:otherwise>-</c:otherwise>
                            </c:choose>
                        </span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">宿舍电话：</span>
                        <span class="value clickable" onclick="showSensitiveInfo('${grxx.dh}', this);">
                            <c:choose>
                                <c:when test="${not empty grxx.dh}">******</c:when>
                                <c:otherwise>-</c:otherwise>
                            </c:choose>
                        </span>
                    </div>
                </div>
            </div>

            <!-- 学籍信息 -->
            <div class="card-mobile">
                <div class="card-header">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span>学籍信息</span>
                    <div class="card-action">
                        <button class="btn-mobile btn-sm btn-primary" onclick="editStudentInfo();">
                            <i class="ace-icon fa fa-edit"></i>
                            <span>修改信息</span>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row-mobile">
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">学号：</span>
                                <span class="value">${xjxx.xh}</span>
                            </div>
                        </div>
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">姓名：</span>
                                <span class="value">${xjxx.xm}</span>
                            </div>
                        </div>
                    </div>
                    <div class="row-mobile">
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">姓名拼音：</span>
                                <span class="value">${xjxx.xmpy}</span>
                            </div>
                        </div>
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">英文姓名：</span>
                                <span class="value">${xjxx.ywxm}</span>
                            </div>
                        </div>
                    </div>
                    <div class="row-mobile">
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">证件号码：</span>
                                <span class="value clickable" onclick="showSensitiveInfo('${xjxx.sfzh}', this);">
                                    <c:choose>
                                        <c:when test="${not empty xjxx.sfzh}">******</c:when>
                                        <c:otherwise>-</c:otherwise>
                                    </c:choose>
                                </span>
                            </div>
                        </div>
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">年级：</span>
                                <span class="value">${xjxx.njdm}</span>
                            </div>
                        </div>
                    </div>
                    <div class="row-mobile">
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">院系：</span>
                                <span class="value">${xjxx.xsh}</span>
                            </div>
                        </div>
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">专业：</span>
                                <span class="value">${xjxx.zyh}</span>
                            </div>
                        </div>
                    </div>
                    <div class="row-mobile">
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">专业方向：</span>
                                <span class="value">${xjxx.zyfxh}</span>
                            </div>
                        </div>
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">班级：</span>
                                <span class="value">${xjxx.bjh}</span>
                            </div>
                        </div>
                    </div>
                    <div class="row-mobile">
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">校区：</span>
                                <span class="value">${xjxx.xqh}</span>
                            </div>
                        </div>
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">辅修专业：</span>
                                <span class="value">${xjxx.fxzyh}</span>
                            </div>
                        </div>
                    </div>
                    <div class="row-mobile">
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">第二学位专业：</span>
                                <span class="value">${xjxx.dexwzyh}</span>
                            </div>
                        </div>
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">是否有学籍：</span>
                                <span class="value">${xjxx.sfyxj}</span>
                            </div>
                        </div>
                    </div>
                    <div class="row-mobile">
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">是否有国家学籍：</span>
                                <span class="value">${xjxx.sfygjxj}</span>
                            </div>
                        </div>
                        <div class="col-mobile-6">
                            <div class="info-item-mobile">
                                <span class="label">学生类别：</span>
                                <span class="value">${xjxx.xslbmc}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </c:if>
        </c:if> <!-- 结束 onOff == '1' 判断 -->


        <!-- 方案详情面板 -->
        <div class="detail-panel" id="planDetailPanel">
            <div class="detail-header">
                <div class="detail-title" id="planDetailTitle">方案详情</div>
                <button class="detail-close" onclick="closePlanDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="detail-content" id="planDetailContent">
                <!-- 方案计划信息 -->
                <div class="card-mobile" id="planInfoCard" style="display: none;">
                    <div class="card-header">
                        <i class="ace-icon fa fa-info-circle"></i>
                        <span>方案计划信息</span>
                    </div>
                    <div class="card-body" id="planInfoBody">
                        <!-- 动态填充方案信息 -->
                    </div>
                </div>

                <!-- 方案树形结构 -->
                <div class="card-mobile">
                    <div class="card-header">
                        <i class="ace-icon fa fa-sitemap"></i>
                        <span>课程结构</span>
                        <div class="card-action">
                            <button class="btn-mobile btn-sm" onclick="expandAllNodes();">展开</button>
                            <button class="btn-mobile btn-sm" onclick="collapseAllNodes();">折叠</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="plan-tree" id="planTree">
                            <!-- 动态生成树形结构 -->
                        </div>
                    </div>
                </div>

                <!-- 课程详情 -->
                <div class="card-mobile" id="courseDetailCard" style="display: none;">
                    <div class="card-header">
                        <i class="ace-icon fa fa-book"></i>
                        <span>课程详情</span>
                    </div>
                    <div class="card-body" id="courseDetailBody">
                        <!-- 动态填充课程详情 -->
                    </div>
                </div>

                <!-- 课组详情 -->
                <div class="card-mobile" id="groupDetailCard" style="display: none;">
                    <div class="card-header">
                        <i class="ace-icon fa fa-folder"></i>
                        <span>课组详情</span>
                    </div>
                    <div class="card-body" id="groupDetailBody">
                        <!-- 动态填充课组详情 -->
                    </div>
                </div>

                <!-- 学年学期详情 -->
                <div class="card-mobile" id="semesterDetailCard" style="display: none;">
                    <div class="card-header">
                        <i class="ace-icon fa fa-calendar"></i>
                        <span>学年学期详情</span>
                    </div>
                    <div class="card-body" id="semesterDetailBody">
                        <!-- 动态填充学年学期详情 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 学籍异动详情面板 -->
        <div class="detail-panel" id="anomalyDetailPanel">
            <div class="detail-header">
                <div class="detail-title">学籍异动</div>
                <button class="detail-close" onclick="closeAnomalyDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="detail-content" id="anomalyDetailContent">
                <!-- 动态加载学籍异动信息 -->
            </div>
        </div>

        <!-- 奖惩信息详情面板 -->
        <div class="detail-panel" id="rewardDetailPanel">
            <div class="detail-header">
                <div class="detail-title">奖惩信息</div>
                <button class="detail-close" onclick="closeRewardDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
            </div>
            <div class="detail-content" id="rewardDetailContent">
                <!-- 动态加载奖惩信息 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPlanData = null;
        let currentTreeData = null;

        $(function() {
            initPage();
            loadStudentPhoto();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 绑定事件
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 阻止面板点击事件冒泡
            $('.detail-panel').on('click', function(e) {
                e.stopPropagation();
            });

            // 点击页面其他地方关闭面板
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.detail-panel').length && !$(e.target).closest('.info-card').length) {
                    closeAllPanels();
                }
            });
        }

        // 加载学生照片
        function loadStudentPhoto() {
            const photoElement = $('#studentPhoto');
            const photoUrl = '/student/rollInfo/img';

            // 创建图片对象测试是否能加载
            const img = new Image();
            img.onload = function() {
                photoElement.html(`<img src="${photoUrl}" alt="学生照片" onerror="this.parentNode.innerHTML='<div class=\\'placeholder\\'><i class=\\'ace-icon fa fa-user\\'></i><div>暂无照片</div></div>';">`);
            };
            img.onerror = function() {
                photoElement.html('<div class="placeholder"><i class="ace-icon fa fa-user"></i><div>暂无照片</div></div>');
            };
            img.src = photoUrl;
        }

        // 显示方案详情
        function showPlanDetail(type, fajhh) {
            if (!fajhh) {
                showToast('方案号不能为空');
                return;
            }

            showLoading(true);

            const url = `/student/rollManagement/project/${fajhh}/1/detail`;

            $.ajax({
                url: url,
                type: 'get',
                dataType: 'json',
                success: function(data) {
                    if (data && data.treeList) {
                        currentPlanData = data;
                        currentTreeData = data.treeList;

                        // 填充方案信息
                        fillPlanInfo(data.jhFajhb);

                        // 初始化树形结构
                        initPlanTree(data.treeList);

                        // 显示面板
                        $('#planDetailPanel').addClass('show');
                        $('#planDetailTitle').text(type === 'zx' ? '主修方案详情' : '辅修方案详情');
                    } else {
                        showToast('对不起，没有查询到您的方案信息！');
                    }
                },
                error: function(xhr) {
                    showToast('获取方案信息失败，请重试');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 填充方案信息
        function fillPlanInfo(jhFajhb) {
            if (!jhFajhb) return;

            let html = '';
            const fields = [
                {key: 'famc', label: '方案名称'},
                {key: 'jhmc', label: '计划名称'},
                {key: 'njmc', label: '年级'},
                {key: 'xsm', label: '院系'},
                {key: 'zym', label: '专业'},
                {key: 'zyfxm', label: '专业方向'},
                {key: 'xwm', label: '学位'},
                {key: 'bylxmc', label: '毕业类型'},
                {key: 'xzlxmc', label: '学制类型'},
                {key: 'xdlxmc', label: '修读类型'},
                {key: 'fajhlx', label: '方案计划类型'},
                {key: 'xnmc', label: '开始学年'},
                {key: 'xqlxm', label: '学期类型'},
                {key: 'xqm', label: '开始学期'},
                {key: 'yqzxf', label: '要求总学分'},
                {key: 'kczxf', label: '课程总学分'},
                {key: 'kczms', label: '课程总门数'},
                {key: 'kczxs', label: '课程总学时'},
                {key: 'pymb', label: '培养目标'},
                {key: 'xdyq', label: '修读要求'},
                {key: 'bz', label: '备注'}
            ];

            fields.forEach(field => {
                const value = jhFajhb[field.key];
                if (value) {
                    html += `
                        <div class="info-item-mobile">
                            <span class="label">${field.label}：</span>
                            <span class="value">${value}</span>
                        </div>
                    `;
                }
            });

            $('#planInfoBody').html(html);
            $('#planInfoCard').show();
        }

        // 格式化身份证号（隐藏中间部分）
        function formatIdCard(idCard) {
            if (!idCard || idCard.length < 10) return idCard;
            return idCard.substring(0, 6) + '****' + idCard.substring(idCard.length - 4);
        }

        // 更新学籍状态
        function updateStatus(status) {
            const statusElement = $('#status');
            statusElement.removeClass('status-active status-inactive status-warning');
            
            switch(status) {
                case '在读':
                case '正常':
                    statusElement.addClass('status-active').text('在读');
                    break;
                case '休学':
                case '暂停':
                    statusElement.addClass('status-warning').text('休学');
                    break;
                case '毕业':
                    statusElement.addClass('status-active').text('毕业');
                    break;
                default:
                    statusElement.addClass('status-inactive').text(status || '未知');
            }
        }

        // 加载学籍照片
        function loadStudentPhoto(studentNo) {
            if (!studentNo) return;
            
            const photoUrl = `/student/personalManagement/rollInfo/getStudentPhoto?xh=${studentNo}`;
            const img = new Image();
            
            img.onload = function() {
                $('#studentPhoto').html(`<img src="${photoUrl}" alt="学籍照片">`);
            };
            
            img.onerror = function() {
                $('#studentPhoto').html('<span>暂无照片</span>');
            };
            
            img.src = photoUrl;
        }

        // 加载统计信息
        function loadStatistics() {
            $.ajax({
                url: "/student/personalManagement/rollInfo/getStudentStats",
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data && data.data) {
                        $('#totalCredits').text(data.data.totalCredits || '0');
                        $('#gpaScore').text(data.data.gpa || '0.0');
                        $('#statsGrid').show();
                    }
                },
                error: function() {
                    // 统计信息加载失败不影响主要功能
                    console.log('统计信息加载失败');
                }
            });
        }

        // 修改信息
        function editInfo() {
            // 跳转到信息修改页面
            if (parent && parent.addTab) {
                parent.addTab('修改个人信息', '/student/personalManagement/personalInfoUpdate/index');
            } else {
                window.location.href = '/student/personalManagement/personalInfoUpdate/index';
            }
        }

        // 导出信息
        function downloadInfo() {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要导出个人信息吗？', function(confirmed) {
                    if (confirmed) {
                        window.open('/student/personalManagement/rollInfo/exportInfo');
                    }
                });
            } else {
                if (confirm('确定要导出个人信息吗？')) {
                    window.open('/student/personalManagement/rollInfo/exportInfo');
                }
            }
        }

        // 隐藏所有详情卡片
        function hideAllDetailCards() {
            $('#courseDetailCard, #groupDetailCard, #semesterDetailCard').hide();
        }

        // 展开所有节点
        function expandAllNodes() {
            $('.tree-node').addClass('expanded');
        }

        // 折叠所有节点
        function collapseAllNodes() {
            $('.tree-node').removeClass('expanded');
        }

        // 关闭方案详情面板
        function closePlanDetail() {
            $('#planDetailPanel').removeClass('show');
            hideAllDetailCards();
        }

        // 显示学籍异动详情
        function showAnomalyDetail() {
            showLoading(true);

            $.ajax({
                url: '/student/rollManagement/project/queryInfoById',
                type: 'get',
                dataType: 'json',
                success: function(data) {
                    // 这里应该加载学籍异动的具体数据
                    $('#anomalyDetailContent').html('<div class="card-mobile"><div class="card-body"><div class="info-item-mobile"><span class="value">学籍异动信息加载中...</span></div></div></div>');
                    $('#anomalyDetailPanel').addClass('show');
                },
                error: function() {
                    showToast('加载学籍异动信息失败');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 关闭学籍异动详情面板
        function closeAnomalyDetail() {
            $('#anomalyDetailPanel').removeClass('show');
        }

        // 显示奖惩信息详情
        function showRewardDetail() {
            showLoading(true);

            $.ajax({
                url: '/student/rollManagement/project/queryInfoById',
                type: 'get',
                dataType: 'json',
                success: function(data) {
                    // 这里应该加载奖惩信息的具体数据
                    $('#rewardDetailContent').html('<div class="card-mobile"><div class="card-body"><div class="info-item-mobile"><span class="value">奖惩信息加载中...</span></div></div></div>');
                    $('#rewardDetailPanel').addClass('show');
                },
                error: function() {
                    showToast('加载奖惩信息失败');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 关闭奖惩信息详情面板
        function closeRewardDetail() {
            $('#rewardDetailPanel').removeClass('show');
        }

        // 关闭所有面板
        function closeAllPanels() {
            $('.detail-panel').removeClass('show');
        }

        // 显示敏感信息
        function showSensitiveInfo(info, element) {
            if (!info) return;

            if (typeof urp !== 'undefined' && urp.showPlaintext) {
                urp.showPlaintext(info, element);
            } else {
                $(element).text(info);
                setTimeout(() => {
                    $(element).text('******');
                }, 3000);
            }
        }

        // 修改学生信息
        function editStudentInfo() {
            if (parent && parent.addTab) {
                parent.addTab('修改个人信息', '/student/rollManagement/personalInfoUpdate/index');
            } else {
                window.location.href = '/student/rollManagement/personalInfoUpdate/index';
            }
        }

        // 刷新页面
        function refreshPage() {
            location.reload();
        }

        // 显示提示信息
        function showToast(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                if (typeof urp !== 'undefined' && urp.showLoading) {
                    urp.showLoading();
                } else {
                    // 简单的加载提示
                    if (!$('#mobileLoading').length) {
                        $('body').append('<div id="mobileLoading" class="loading-overlay"><div class="loading-spinner"><i class="ace-icon fa fa-spinner fa-spin"></i><div>加载中...</div></div></div>');
                    }
                    $('#mobileLoading').show();
                }
            } else {
                if (typeof urp !== 'undefined' && urp.hideLoading) {
                    urp.hideLoading();
                } else {
                    $('#mobileLoading').hide();
                }
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
