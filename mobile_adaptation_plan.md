# PC端到移动端JSP适配计划

## 📋 总体策略

### 1. 适配原则
- **业务逻辑完全一致**：移动端与PC端功能完全对应
- **数据接口统一**：使用相同的Controller和Service
- **响应式设计**：遵循mobile-framework.css规范
- **用户体验优化**：针对移动端特点优化交互

### 2. 文件清理策略
- 删除wapjsp目录下与PC端不对应的文件
- 保留核心移动端特色功能（如studentCard系列）
- 统一文件命名和目录结构

## 🎯 第一阶段：核心功能模块适配 (优先级：高)

### 1.1 学生信息管理模块
```
PC端: urpSoft/WEB-INF/jsp/student/personalManagement/
移动端: wapjsp/student/personalManagement/

需要适配的文件：
- rollInfo/index.jsp                    # 学籍信息查询
- personalInfoUpdate/xjInfo.jsp         # 个人信息修改
- myRollCard/index.jsp                  # 学生证信息
- warning/index.jsp                     # 学业预警
- studentIdCard/index.jsp               # 学生证管理
- transfermajor/index.jsp               # 转专业申请
```

### 1.2 成绩查询模块
```
PC端: urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/
移动端: wapjsp/student/integratedQuery/scoreQuery/

需要适配的文件：
- allTermScores/index.jsp               # 历年成绩 ✓已适配
- thisTermScores/index.jsp              # 本学期成绩 ✓已适配
- unpassedScores/index.jsp              # 不及格成绩 ✓已适配
- externalScores/index.jsp              # 等级考试成绩 ✓已适配
- experimentScores/index.jsp            # 实验成绩 ✓已适配
- coursePropertyScores/index.jsp        # 课程属性成绩 ✓已适配
- subitemScores/index.jsp               # 分项成绩 ✓已适配
- physicalTestScore/index.jsp           # 体测成绩
- scoresCard/index.jsp                  # 成绩单
```

### 1.3 课程管理模块
```
PC端: urpSoft/WEB-INF/jsp/student/courseSelectManagement/
移动端: wapjsp/student/courseSelectManagement/

需要适配的文件：
- preCourseSelect/index.jsp             # 预选课 ✓已适配
- currentCourseListInfo.jsp             # 当前课程信息 ✓已适配
- tsxk/currentWeeklyCourse/index.jsp    # 当前周课程
- tsxk/specialCourse/index.jsp          # 特殊课程
- teachingBooks/index.jsp               # 教材管理
```

### 1.4 课程表查询模块
```
PC端: urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/
移动端: wapjsp/student/courseTableOfThisSemester/

需要适配的文件：
- index.jsp                             # 本学期课表 ✓已适配
- courseSelectResult.jsp                # 选课结果
- evaluateIndex.jsp                     # 评价入口
```

## 🎯 第二阶段：考试与评价模块适配 (优先级：高)

### 2.1 考试管理模块
```
PC端: urpSoft/WEB-INF/jsp/student/examinationManagement/
移动端: wapjsp/student/examinationManagement/

需要适配的文件：
- examPlan/index.jsp                    # 考试安排 ✓已适配
- cet/index.jsp                         # 四六级考试
- examGrade/index.jsp                   # 考试成绩
- examregistration/index.jsp            # 考试报名
- printAdmissionCertificate/index.jsp   # 准考证打印
```

### 2.2 教学评价模块
```
PC端: urpSoft/WEB-INF/jsp/student/teachingEvaluation/
移动端: wapjsp/student/teachingEvaluation/

需要适配的文件：
- newEvaluation/index.jsp               # 新版教学评价
- newEvaluation/evaluation.jsp          # 评价页面
- teachingEvaluation/index.jsp          # 传统教学评价
- comprehensiveReview/index.jsp         # 综合评价
```

## 🎯 第三阶段：实验与实习模块适配 (优先级：中)

### 3.1 实验管理模块
```
PC端: urpSoft/WEB-INF/jsp/student/experiment/
移动端: wapjsp/student/experiment/

需要适配的文件：
- choseProj/index.jsp                   # 实验项目选择 ✓已适配
- safetyExamination/index.jsp           # 安全考试 ✓已适配
- largeDeviceYy/largeDeviceYyIndex.jsp  # 大型设备预约
- subscribe/askFor/index.jsp            # 实验预约
```

### 3.2 实习管理模块
```
PC端: urpSoft/WEB-INF/jsp/student/internship/
移动端: wapjsp/student/internship/

需要适配的文件：
- daily/index.jsp                       # 实习日志 ✓已适配
- internshipExecutionPlanCompletion/index.jsp  # 实习计划完成
- sxbggl/uploadIndex.jsp                # 实习报告管理
- sxgcgl/sxgcglIndex.jsp                # 实习过程管理
```

## 🎯 第四阶段：高级功能模块适配 (优先级：中)

### 4.1 创新学分模块
```
PC端: urpSoft/WEB-INF/jsp/student/innovationCredits/
移动端: wapjsp/student/innovationCredits/

需要适配的文件：
- creditCourseReplace/index.jsp         # 学分课程替换 ✓已适配
- creditsRecognition/index.jsp          # 学分认定 ✓已适配
- innovationProject/index.jsp           # 创新项目 ✓已适配
```

### 4.2 论文管理模块
```
PC端: urpSoft/WEB-INF/jsp/student/thesis/
移动端: wapjsp/student/thesis/

需要适配的文件：
- thesisDefenseInfo/index.jsp           # 论文答辩信息 ✓已适配
- stageddocuments/uploadsub/index.jsp   # 阶段文档上传
```

## 📱 移动端适配技术规范

### 1. HTML结构规范
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>页面标题</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">页面标题</div>
            <div class="navbar-action">
                <i class="ace-icon fa fa-search"></i>
            </div>
        </nav>
        
        <!-- 内容区域 -->
        <div class="container-mobile">
            <!-- 页面内容 -->
        </div>
    </div>
</body>
</html>
```

### 2. JavaScript规范
```javascript
$(function() {
    initPage();
    loadData();
});

// 初始化页面
function initPage() {
    adjustPageHeight();
    bindEvents();
}

// 加载数据
function loadData() {
    showLoading(true);
    
    $.ajax({
        url: "相同的PC端URL",
        type: "post",
        dataType: "json",
        success: function(data) {
            renderData(data);
            showLoading(false);
        },
        error: function(xhr) {
            showError("加载失败，请重试");
            showLoading(false);
        }
    });
}
```

### 3. CSS类名规范
```css
/* 移动端专用类名前缀 */
.page-mobile          /* 页面容器 */
.navbar-mobile        /* 导航栏 */
.container-mobile     /* 内容容器 */
.card-mobile          /* 卡片组件 */
.form-mobile          /* 表单组件 */
.list-mobile          /* 列表组件 */
.modal-mobile         /* 模态框组件 */
```

## 🗂️ 文件清理计划

### 需要删除的文件（与PC端不对应）
```
wapjsp/student/advancedMobile/          # 移动端高级功能
wapjsp/student/mobileFeatures/          # 移动端特色功能
wapjsp/student/systemOverview/          # 系统概览
wapjsp/student/userExperience/          # 用户体验
wapjsp/student/messageDisplay/          # 消息显示
wapjsp/student/notificationList/        # 通知列表
wapjsp/student/timeArrangement/         # 时间安排
wapjsp/student/workAndRestTimeArrangement/  # 作息时间安排
```

### 需要保留的移动端特色功能
```
wapjsp/student/library/                 # 图书馆服务（移动端特色）
wapjsp/student/studentCard*/            # 学生卡系列（移动端特色）
wapjsp/student/finance/                 # 财务查询（移动端特色）
wapjsp/student/service/                 # 服务中心（移动端特色）
wapjsp/student/query/                   # 快速查询（移动端特色）
```

## 📅 实施时间表

### 第一周：环境准备和核心模块
- 清理不对应的文件
- 适配学生信息管理模块
- 完善成绩查询模块

### 第二周：课程和考试模块
- 适配课程管理模块
- 适配考试管理模块
- 适配教学评价模块

### 第三周：实验和实习模块
- 适配实验管理模块
- 适配实习管理模块

### 第四周：高级功能和优化
- 适配创新学分模块
- 适配论文管理模块
- 整体测试和优化

## ✅ 验收标准

1. **功能一致性**：移动端功能与PC端完全对应
2. **数据一致性**：使用相同的数据接口和业务逻辑
3. **UI适配性**：符合移动端设计规范
4. **性能要求**：页面加载时间<3秒
5. **兼容性要求**：支持iOS Safari 12+, Android Chrome 70+
