<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>体测项目预约</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 体测预约页面样式 */
        .exam-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .exam-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .exam-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .info-section {
            background: var(--info-light);
            color: var(--info-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--info-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .warning-section {
            background: var(--warning-light);
            color: var(--warning-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .warning-section i {
            color: var(--warning-color);
            margin-right: 8px;
        }
        
        .tab-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tab-header {
            display: flex;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .tab-button {
            flex: 1;
            padding: var(--padding-md);
            background: var(--bg-secondary);
            color: var(--text-secondary);
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-size: var(--font-size-small);
            transition: all var(--transition-base);
        }
        
        .tab-button.active {
            background: var(--bg-primary);
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .search-section {
            margin-bottom: var(--margin-md);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--primary-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: var(--font-size-small);
        }
        
        .checkbox-item input[type="checkbox"] {
            margin: 0;
        }
        
        .clear-button {
            background: var(--error-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            margin-left: var(--margin-sm);
        }
        
        .week-selector {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 2px;
            margin-top: 8px;
        }
        
        .week-item {
            padding: 6px 4px;
            text-align: center;
            border: 1px solid var(--border-primary);
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            font-size: var(--font-size-mini);
            transition: all var(--transition-base);
        }
        
        .week-item:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
        
        .week-item:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        
        .week-item.selected {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .list-section {
            margin-top: var(--margin-md);
        }
        
        .list-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-title i {
            color: var(--success-color);
        }
        
        .exam-item {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            position: relative;
        }
        
        .exam-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .exam-content {
            flex: 1;
        }
        
        .exam-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .exam-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .book-button {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
            width: 100%;
        }
        
        .book-button:hover {
            background: var(--success-dark);
        }
        
        .cancel-button {
            background: var(--error-color);
            color: white;
        }
        
        .cancel-button:hover {
            background: var(--error-dark);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-booked {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-confirmed {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        @media (max-width: 480px) {
            .week-selector {
                grid-template-columns: repeat(6, 1fr);
            }
            
            .exam-details {
                grid-template-columns: 1fr;
            }
            
            .search-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">体测项目预约</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 体测预约头部 -->
        <div class="exam-header">
            <div class="exam-title">体测项目预约</div>
            <div class="exam-desc">预约和管理体测项目</div>
        </div>
        
        <!-- 信息提示 -->
        <div class="info-section">
            <cache:get var="zxjxjhb" targetprop="zxjxjhm" key="${xnxq}" keyName="zxjxjhh" region="jh_zxjxjhb_view"/>
            <cache:get var="tcnjb" targetprop="tcnjmc" key="${tcnjdm}" keyName="tcnjdm" region="tc_code_tcnjb"/>
            ${zxjxjhb}，您的体测年级是：${tcnjb}
        </div>
        
        <!-- 选项卡 -->
        <div class="tab-section">
            <div class="tab-header">
                <button class="tab-button active" data-tab="zs">
                    正式测试
                    <c:if test="${flag == 'showAdd'}">
                        <div style="font-size: 10px; margin-top: 2px;">
                            ${fn:substring(kzkg.kssj, 0, 4)}-${fn:substring(kzkg.kssj, 4, 6)}-${fn:substring(kzkg.kssj, 6, 8)}~${fn:substring(kzkg.jssj, 0, 4)}-${fn:substring(kzkg.jssj, 4, 6)}-${fn:substring(kzkg.jssj, 6, 8)}
                        </div>
                    </c:if>
                </button>
                <button class="tab-button" data-tab="bc">
                    补测
                    <c:if test="${flag1 == 'showAdd'}">
                        <div style="font-size: 10px; margin-top: 2px;">
                            ${fn:substring(kzkg1.kssj, 0, 4)}-${fn:substring(kzkg1.kssj, 4, 6)}-${fn:substring(kzkg1.kssj, 6, 8)}~${fn:substring(kzkg1.jssj, 0, 4)}-${fn:substring(kzkg1.jssj, 4, 6)}-${fn:substring(kzkg1.jssj, 6, 8)}
                        </div>
                    </c:if>
                </button>
                <button class="tab-button" data-tab="wd">我的预约</button>
            </div>
            
            <div class="tab-content">
                <!-- 正式测试 -->
                <div id="zs" class="tab-pane active">
                    <c:choose>
                        <c:when test="${flag == 'nonparametric'}">
                            <div class="warning-section">
                                <i class="ace-icon fa fa-exclamation-triangle"></i>
                                申请参数未维护，请联系管理员处理
                            </div>
                        </c:when>
                        <c:when test="${flag == 'notenabled'}">
                            <div class="warning-section">
                                <i class="ace-icon fa fa-exclamation-triangle"></i>
                                申请未启用，请联系管理员处理
                            </div>
                        </c:when>
                        <c:when test="${flag == 'nottime'}">
                            <div class="warning-section">
                                <i class="ace-icon fa fa-exclamation-triangle"></i>
                                不在申请时间范围或申请开关关闭
                            </div>
                        </c:when>
                        <c:otherwise>
                            <!-- 查询条件 -->
                            <div class="search-section">
                                <div class="search-title">
                                    <i class="ace-icon fa fa-search"></i>
                                    查询条件
                                </div>
                                
                                <form id="zsform" class="search-form">
                                    <div class="form-group">
                                        <label class="form-label">体测项目</label>
                                        <div class="checkbox-group">
                                            <cache:query var="tcxms" region="tc_code_tcxmb" where="${xmwhere}" orderby="tcxmdm" />
                                            <c:forEach var="tcxm" items="${tcxms}">
                                                <label class="checkbox-item">
                                                    <input type="checkbox" name="tcxmdm" value="${tcxm.tcxmdm}"/>
                                                    <span>${tcxm.tcxmmc}</span>
                                                </label>
                                            </c:forEach>
                                            <button type="button" class="clear-button" onclick="clearCheckboxes('zsform', 'tcxmdm');">清除</button>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">周次</label>
                                        <input type="hidden" name="zc">
                                        <div class="week-selector" id="zsWeekSelector">
                                            <c:forEach var="zc" begin="1" end="24">
                                                <div class="week-item" data-week="${zc}">${zc}</div>
                                            </c:forEach>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">星期</label>
                                        <div class="checkbox-group">
                                            <c:forEach var="xq" begin="1" end="7" varStatus="in">
                                                <label class="checkbox-item">
                                                    <input type="checkbox" name="xq" value="${xq}"/>
                                                    <span>星期${fn:substring('零一二三四五六日', in.index, in.index+1)}</span>
                                                </label>
                                            </c:forEach>
                                            <button type="button" class="clear-button" onclick="clearCheckboxes('zsform', 'xq');">清除</button>
                                        </div>
                                    </div>
                                </form>
                                
                                <div class="search-actions">
                                    <button class="btn-mobile btn-search" onclick="searchExams('zs');">
                                        <i class="ace-icon fa fa-search"></i>
                                        <span>查询</span>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 正式测试列表 -->
                            <div class="list-section">
                                <div class="list-title">
                                    <i class="ace-icon fa fa-list"></i>
                                    正式测试项目列表
                                </div>
                                <div id="zsExamList">
                                    <!-- 动态加载考试列表 -->
                                </div>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>
                
                <!-- 补测 -->
                <div id="bc" class="tab-pane">
                    <!-- 补测内容与正式测试类似，这里省略以节省空间 -->
                </div>
                
                <!-- 我的预约 -->
                <div id="wd" class="tab-pane">
                    <div class="list-section">
                        <div class="list-title">
                            <i class="ace-icon fa fa-calendar"></i>
                            我的预约列表
                        </div>
                        <div id="wdExamList">
                            <!-- 动态加载我的预约列表 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'zs';
        let zsParams = '';
        let bcParams = '';
        let zsData = [];
        let bcData = [];
        let wdData = [];
        let currentPage = 1;
        let pageSize = 30;

        $(function() {
            initPage();
            initTabs();
            initWeekSelector();

            // 默认加载正式测试数据
            if ('${flag}' === 'showAdd') {
                searchExams('zs');
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化选项卡
        function initTabs() {
            $('.tab-button').click(function() {
                const tabId = $(this).data('tab');
                switchTab(tabId);
            });
        }

        // 切换选项卡
        function switchTab(tabId) {
            // 更新按钮状态
            $('.tab-button').removeClass('active');
            $(`.tab-button[data-tab="${tabId}"]`).addClass('active');

            // 更新内容区域
            $('.tab-pane').removeClass('active');
            $(`#${tabId}`).addClass('active');

            currentTab = tabId;

            // 加载对应数据
            if (tabId === 'wd') {
                searchMyBookings();
            } else if (tabId === 'bc' && '${flag1}' === 'showAdd') {
                searchExams('bc');
            }
        }

        // 初始化周次选择器
        function initWeekSelector() {
            $('.week-item').click(function() {
                const week = $(this).data('week');
                $(this).toggleClass('selected');
                updateWeekInput();
            });
        }

        // 更新周次输入值
        function updateWeekInput() {
            const selectedWeeks = [];
            $('.week-item.selected').each(function() {
                selectedWeeks.push($(this).data('week'));
            });
            $('input[name="zc"]').val(selectedWeeks.join(','));
        }

        // 清除复选框
        function clearCheckboxes(formId, name) {
            $(`#${formId} input[name="${name}"]`).prop('checked', false);
            if (name === 'zc') {
                $('.week-item').removeClass('selected');
                updateWeekInput();
            }
        }

        // 搜索考试项目
        function searchExams(tab) {
            showLoading(true);

            const formData = $(`#${tab}form`).serialize();
            if (tab === 'zs') {
                zsParams = formData;
            } else {
                bcParams = formData;
            }

            $.ajax({
                url: "/student/bodyexamination/orderexam/queryList",
                type: "post",
                data: formData + "&tab=" + tab + "&pageNum=1&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (tab === 'zs') {
                            zsData = data.records;
                        } else {
                            bcData = data.records;
                        }
                        renderExamList(data.records, tab);
                    } else {
                        renderEmptyState(tab);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    renderEmptyState(tab);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 搜索我的预约
        function searchMyBookings() {
            showLoading(true);

            $.ajax({
                url: "/student/bodyexamination/orderexam/queryWdList",
                type: "post",
                data: "pageNum=1&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        wdData = data.records;
                        renderMyBookingsList(data.records);
                    } else {
                        renderEmptyState('wd');
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    renderEmptyState('wd');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染考试列表
        function renderExamList(data, tab) {
            const container = $(`#${tab}ExamList`);
            container.empty();

            data.forEach(function(item, index) {
                const itemHtml = createExamItem(item, index, tab);
                container.append(itemHtml);
            });
        }

        // 创建考试项目HTML
        function createExamItem(item, index, tab) {
            const weekDay = '零一二三四五六日'.substring(parseInt(item.XQ), parseInt(item.XQ) + 1);

            return `
                <div class="exam-item">
                    <div class="exam-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="exam-index">${index + 1}</div>
                            <div class="exam-content">
                                <div class="exam-name">${item.TCXMMC || ''}</div>
                                <div class="exam-time">第${item.ZC || ''}周 星期${weekDay} （${item.TCRQ || ''}）第${item.JC1 || ''}节</div>
                            </div>
                        </div>
                    </div>

                    <div class="exam-details">
                        <div class="detail-item">
                            <span class="detail-label">周次</span>
                            <span>${item.ZC || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">星期</span>
                            <span>${item.XQ || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">预约容量</span>
                            <span>${item.YYRL || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">剩余容量</span>
                            <span>${item.SYRL || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">可预约年级</span>
                            <span>${item.XSNJDM || ''}</span>
                        </div>
                    </div>

                    <button class="book-button" onclick="bookExam('${tab}', '${item.XNXQ}_${item.TCXMDM}_${item.ZC}_${item.XQ}_${item.JC}', '体测项目：${item.TCXMMC}<br>体测时间：第${item.ZC}周 星期${weekDay} （${item.TCRQ}）<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;第${item.JC1}节');">
                        <i class="ace-icon fa fa-clock-o"></i>
                        <span>预约</span>
                    </button>
                </div>
            `;
        }

        // 渲染我的预约列表
        function renderMyBookingsList(data) {
            const container = $('#wdExamList');
            container.empty();

            data.forEach(function(item, index) {
                const itemHtml = createMyBookingItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建我的预约项目HTML
        function createMyBookingItem(item, index) {
            const weekDay = '零一二三四五六日'.substring(parseInt(item.XQ), parseInt(item.XQ) + 1);
            const bookingType = item.YYLXDM === '0' ? '正式测试' : '补测';
            const canCancel = item.YYZTDM === '0';

            return `
                <div class="exam-item">
                    <div class="exam-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="exam-index">${index + 1}</div>
                            <div class="exam-content">
                                <div class="exam-name">${item.TCXMMC || ''}</div>
                                <div class="exam-time">${bookingType} - 第${item.ZC || ''}周 星期${weekDay} （${item.TCRQ || ''}）第${item.JC1 || ''}节</div>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge ${item.YYZTDM === '0' ? 'status-booked' : 'status-confirmed'}">
                                ${item.YYZTDM === '0' ? '已预约' : '已确认'}
                            </span>
                        </div>
                    </div>

                    <div class="exam-details">
                        <div class="detail-item">
                            <span class="detail-label">预约类型</span>
                            <span>${bookingType}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">操作时间</span>
                            <span>${item.CZSJ || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">确认教师</span>
                            <span>${item.QRJS || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">确认时间</span>
                            <span>${item.QRSJ || ''}</span>
                        </div>
                    </div>

                    ${canCancel ? `
                    <button class="book-button cancel-button" onclick="cancelBooking('${item.SQBH}');">
                        <i class="ace-icon fa fa-undo"></i>
                        <span>取消预约</span>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        // 渲染空状态
        function renderEmptyState(tab) {
            const container = $(`#${tab}ExamList`);
            if (tab === 'wd') {
                container = $('#wdExamList');
            }

            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-calendar-times-o"></i>
                    <div>暂无数据</div>
                </div>
            `);
        }

        // 预约考试
        function bookExam(tab, examInfo, examDetail) {
            if (confirm("确定要预约这个体测项目吗？\n\n" + examDetail.replace(/<br>/g, '\n'))) {
                showLoading(true);

                $.ajax({
                    url: "/student/bodyexamination/orderexam/yytc",
                    type: "post",
                    data: "tab=" + tab + "&examInfo=" + examInfo + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/") != -1) {
                            showError("页面已过期，请刷新页面！");
                        } else if (data.result === "ok") {
                            showSuccess("预约成功！", function() {
                                searchExams(tab);
                            });
                        } else {
                            showError(data.msg || "预约失败！");
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:预约失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 取消预约
        function cancelBooking(sqbh) {
            if (confirm("确定要取消这个预约吗？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/bodyexamination/orderexam/qxyytc",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/") != -1) {
                            showError("页面已过期，请刷新页面！");
                        } else if (data.result === "ok") {
                            showSuccess("取消预约成功！", function() {
                                searchMyBookings();
                            });
                        } else {
                            showError(data.msg || "取消预约失败！");
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:取消预约失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 刷新数据
        function refreshData() {
            if (currentTab === 'wd') {
                searchMyBookings();
            } else {
                searchExams(currentTab);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
