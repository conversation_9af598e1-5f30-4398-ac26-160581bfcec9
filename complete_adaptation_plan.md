# 完整的PC端到移动端JSP适配计划

## 📊 总体统计

- **PC端文件总数**: 616
- **移动端文件总数**: 150
- **模块总数**: 37

- **已完成适配**: 74 个文件 (12.0%)
- **待适配**: 542 个文件 (88.0%)

## 📋 按模块详细适配计划

### calendarSemesterCurriculum 模块

**文件数量**: 1

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/calendarSemesterCurriculum/index.jsp` | `wapjsp/student/calendarSemesterCurriculum/index.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 1/1 (100.0%)

### certificationExam 模块

**文件数量**: 2

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/certificationExam/onlineRegistration/enroll.jsp` | `wapjsp/student/certificationExam/onlineRegistration/enroll.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/certificationExam/onlineRegistration/index.jsp` | `wapjsp/student/certificationExam/onlineRegistration/index.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 0/2 (0.0%)

### courseSelectManagement 模块

**文件数量**: 41

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/bxkc.jsp` | `wapjsp/student/courseSelectManagement/bxkc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/chargingCourse/gradeExaminationIndex.jsp` | `wapjsp/student/courseSelectManagement/chargingCourse/gradeExaminationIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/chargingCourse/index.jsp` | `wapjsp/student/courseSelectManagement/chargingCourse/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/chargingCourse/selectCourseIndex.jsp` | `wapjsp/student/courseSelectManagement/chargingCourse/selectCourseIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/courseSelectPriority.jsp` | `wapjsp/student/courseSelectManagement/courseSelectPriority.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/currentCourseListInfo.jsp` | `wapjsp/student/courseSelectManagement/currentCourseListInfo.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/cxxk.jsp` | `wapjsp/student/courseSelectManagement/cxxk.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/deleteKcList.jsp` | `wapjsp/student/courseSelectManagement/deleteKcList.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/draw.jsp` | `wapjsp/student/courseSelectManagement/draw.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/error.jsp` | `wapjsp/student/courseSelectManagement/error.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/fakc.jsp` | `wapjsp/student/courseSelectManagement/fakc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/fatjxk.jsp` | `wapjsp/student/courseSelectManagement/fatjxk.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/fxxk.jsp` | `wapjsp/student/courseSelectManagement/fxxk.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/jhkc.jsp` | `wapjsp/student/courseSelectManagement/jhkc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/kcwcmxckBody.jsp` | `wapjsp/student/courseSelectManagement/kcwcmxckBody.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/kzwcmxckBody.jsp` | `wapjsp/student/courseSelectManagement/kzwcmxckBody.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/mobile/page1.jsp` | `wapjsp/student/courseSelectManagement/mobile/page1.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/mobile/page2.jsp` | `wapjsp/student/courseSelectManagement/mobile/page2.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/preCourseSelect/index.jsp` | `wapjsp/student/courseSelectManagement/preCourseSelect/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/selectCourseNoticeDetail.jsp` | `wapjsp/student/courseSelectManagement/selectCourseNoticeDetail.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/selectFa.jsp` | `wapjsp/student/courseSelectManagement/selectFa.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/selectKc.jsp` | `wapjsp/student/courseSelectManagement/selectKc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/teachingBooks/index.jsp` | `wapjsp/student/courseSelectManagement/teachingBooks/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tjgyWaitfor.jsp` | `wapjsp/student/courseSelectManagement/tjgyWaitfor.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tkkc.jsp` | `wapjsp/student/courseSelectManagement/tkkc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/checkFajhh.jsp` | `wapjsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/checkFajhh.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/index.jsp` | `wapjsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/saveCourse.jsp` | `wapjsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/saveCourse.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/tkIndex.jsp` | `wapjsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/tkIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/xkIndex.jsp` | `wapjsp/student/courseSelectManagement/tsxk/currentWeeklyCourse/xkIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/specialCourse/checkFajhh.jsp` | `wapjsp/student/courseSelectManagement/tsxk/specialCourse/checkFajhh.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/specialCourse/error.jsp` | `wapjsp/student/courseSelectManagement/tsxk/specialCourse/error.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/specialCourse/index.jsp` | `wapjsp/student/courseSelectManagement/tsxk/specialCourse/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/specialCourse/index_faw.jsp` | `wapjsp/student/courseSelectManagement/tsxk/specialCourse/index_faw.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/specialCourse/saveCourse.jsp` | `wapjsp/student/courseSelectManagement/tsxk/specialCourse/saveCourse.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/tsxk/specialCourse/selectTab.jsp` | `wapjsp/student/courseSelectManagement/tsxk/specialCourse/selectTab.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/waitfor.jsp` | `wapjsp/student/courseSelectManagement/waitfor.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/xarxk.jsp` | `wapjsp/student/courseSelectManagement/xarxk.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/xirxk.jsp` | `wapjsp/student/courseSelectManagement/xirxk.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/xksbxx.jsp` | `wapjsp/student/courseSelectManagement/xksbxx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseSelectManagement/zyxk.jsp` | `wapjsp/student/courseSelectManagement/zyxk.jsp` | ⏳ 待适配 | 🔴 高 |

**模块完成度**: 2/41 (4.9%)

### courseTableOfThisSemester 模块

**文件数量**: 7

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/canlendar_show.jsp` | `wapjsp/student/courseTableOfThisSemester/canlendar_show.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/courseSelectResult.jsp` | `wapjsp/student/courseTableOfThisSemester/courseSelectResult.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/evaluateIndex.jsp` | `wapjsp/student/courseTableOfThisSemester/evaluateIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/fileNoExist.jsp` | `wapjsp/student/courseTableOfThisSemester/fileNoExist.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/index.jsp` | `wapjsp/student/courseTableOfThisSemester/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/mobileindex.jsp` | `wapjsp/student/courseTableOfThisSemester/mobileindex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/syllabus_show.jsp` | `wapjsp/student/courseTableOfThisSemester/syllabus_show.jsp` | ⏳ 待适配 | 🔴 高 |

**模块完成度**: 1/7 (14.3%)

### courseVoluntary 模块

**文件数量**: 2

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/courseVoluntary/index.jsp` | `wapjsp/student/courseVoluntary/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/courseVoluntary/myXkYx.jsp` | `wapjsp/student/courseVoluntary/myXkYx.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 1/2 (50.0%)

### credibleReportCard 模块

**文件数量**: 4

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/credibleReportCard/scoreCard/create/error.jsp` | `wapjsp/student/credibleReportCard/scoreCard/create/error.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/credibleReportCard/scoreCard/create/index.jsp` | `wapjsp/student/credibleReportCard/scoreCard/create/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/credibleReportCard/scoreCard/create/pay.jsp` | `wapjsp/student/credibleReportCard/scoreCard/create/pay.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/credibleReportCard/scoreCard/history/index.jsp` | `wapjsp/student/credibleReportCard/scoreCard/history/index.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 0/4 (0.0%)

### creditCheck 模块

**文件数量**: 3

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/creditCheck/index.jsp` | `wapjsp/student/creditCheck/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/creditCheck/indexAll.jsp` | `wapjsp/student/creditCheck/indexAll.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/creditCheck/yjxf.jsp` | `wapjsp/student/creditCheck/yjxf.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 1/3 (33.3%)

### creditTuition 模块

**文件数量**: 2

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/creditTuition/index.jsp` | `wapjsp/student/creditTuition/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/creditTuition/searchPaymentIndex.jsp` | `wapjsp/student/creditTuition/searchPaymentIndex.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 1/2 (50.0%)

### dropCourseCreditList 模块

**文件数量**: 1

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/dropCourseCreditList/index.jsp` | `wapjsp/student/dropCourseCreditList/index.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 1/1 (100.0%)

### exam 模块

**文件数量**: 3

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/exam/add.jsp` | `wapjsp/student/exam/add.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/exam/index.jsp` | `wapjsp/student/exam/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/exam/update.jsp` | `wapjsp/student/exam/update.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 3/3 (100.0%)

### examinationManagement 模块

**文件数量**: 20

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/cns.jsp` | `wapjsp/student/examinationManagement/cet/cns.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/dyzkz.jsp` | `wapjsp/student/examinationManagement/cet/dyzkz.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/grade.jsp` | `wapjsp/student/examinationManagement/cet/grade.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/hdxx.jsp` | `wapjsp/student/examinationManagement/cet/hdxx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/index.jsp` | `wapjsp/student/examinationManagement/cet/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/lxfs.jsp` | `wapjsp/student/examinationManagement/cet/lxfs.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/sczpq.jsp` | `wapjsp/student/examinationManagement/cet/sczpq.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/zysx.jsp` | `wapjsp/student/examinationManagement/cet/zysx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examGrade/index.jsp` | `wapjsp/student/examinationManagement/examGrade/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examPlan/dati.jsp` | `wapjsp/student/examinationManagement/examPlan/dati.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examPlan/index.jsp` | `wapjsp/student/examinationManagement/examPlan/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examSignUp/index.jsp` | `wapjsp/student/examinationManagement/examSignUp/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examSignUp/sczpq.jsp` | `wapjsp/student/examinationManagement/examSignUp/sczpq.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examSignUp/xsInforCheck.jsp` | `wapjsp/student/examinationManagement/examSignUp/xsInforCheck.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examregistration/addApply.jsp` | `wapjsp/student/examinationManagement/examregistration/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examregistration/checkinfo.jsp` | `wapjsp/student/examinationManagement/examregistration/checkinfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/examregistration/index.jsp` | `wapjsp/student/examinationManagement/examregistration/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/othersExamPlan/index.jsp` | `wapjsp/student/examinationManagement/othersExamPlan/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/printAdmissionCertificate/index.jsp` | `wapjsp/student/examinationManagement/printAdmissionCertificate/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/examinationManagement/specialReTestApply/index.jsp` | `wapjsp/student/examinationManagement/specialReTestApply/index.jsp` | ⏳ 待适配 | 🔴 高 |

**模块完成度**: 1/20 (5.0%)

### exemptsExam 模块

**文件数量**: 4

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/exemptsExam/addSq.jsp` | `wapjsp/student/exemptsExam/addSq.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/exemptsExam/fj.jsp` | `wapjsp/student/exemptsExam/fj.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/exemptsExam/sqIndex.jsp` | `wapjsp/student/exemptsExam/sqIndex.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/exemptsExam/view.jsp` | `wapjsp/student/exemptsExam/view.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 0/4 (0.0%)

### experiment 模块

**文件数量**: 31

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/experiment/choseProj/choseView.jsp` | `wapjsp/student/experiment/choseProj/choseView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/choseProj/index.jsp` | `wapjsp/student/experiment/choseProj/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/courseTableQuery/bjCourseTableIndex.jsp` | `wapjsp/student/experiment/courseTableQuery/bjCourseTableIndex.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbgxshView.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbgxshView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbgxtbList.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbgxtbList.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbgxtbView.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbgxtbView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbkfsb/addDxyqsbgxsbInfo.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbkfsb/addDxyqsbgxsbInfo.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbkfsb/dxyqsbkfsbList.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbkfsb/dxyqsbkfsbList.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbkfsb/edit.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbkfsb/edit.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbkfsb/editDxyqsbgxsbInfo.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbkfsb/editDxyqsbgxsbInfo.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbkfsh/approveView.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbkfsh/approveView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbkfsh/dxyqsbkfshView.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbkfsh/dxyqsbkfshView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbsysq/applyView.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbsysq/applyView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbsysq/dxyqsbsysqView.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbsysq/dxyqsbsysqView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/dxyqsbsysq/dxyqsbsysqshView.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/dxyqsbsysq/dxyqsbsysqshView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/editDxyqsbgxView.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/editDxyqsbgxView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/dxyqsbgxgl/editInfo.jsp` | `wapjsp/student/experiment/dxyqsbgxgl/editInfo.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/largeDeviceYy/largeDeviceYyAddInfo.jsp` | `wapjsp/student/experiment/largeDeviceYy/largeDeviceYyAddInfo.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/largeDeviceYy/largeDeviceYyEditInfo.jsp` | `wapjsp/student/experiment/largeDeviceYy/largeDeviceYyEditInfo.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/largeDeviceYy/largeDeviceYyIndex.jsp` | `wapjsp/student/experiment/largeDeviceYy/largeDeviceYyIndex.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/largeDeviceYy/largeDeviceYysqIndex.jsp` | `wapjsp/student/experiment/largeDeviceYy/largeDeviceYysqIndex.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/largeDeviceYy/largeDeviceYysqInfoGet.jsp` | `wapjsp/student/experiment/largeDeviceYy/largeDeviceYysqInfoGet.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/safetyExamination/index.jsp` | `wapjsp/student/experiment/safetyExamination/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/safetyExamination/jumpPromise.jsp` | `wapjsp/student/experiment/safetyExamination/jumpPromise.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/safetyExamination/jumpTestQuestions.jsp` | `wapjsp/student/experiment/safetyExamination/jumpTestQuestions.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/safetyExamination/queryInfo.jsp` | `wapjsp/student/experiment/safetyExamination/queryInfo.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/safetyExamination/testResults.jsp` | `wapjsp/student/experiment/safetyExamination/testResults.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/subscribe/askFor/add.jsp` | `wapjsp/student/experiment/subscribe/askFor/add.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/subscribe/askFor/addList.jsp` | `wapjsp/student/experiment/subscribe/askFor/addList.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/subscribe/askFor/index.jsp` | `wapjsp/student/experiment/subscribe/askFor/index.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/experiment/subscribe/askFor/update.jsp` | `wapjsp/student/experiment/subscribe/askFor/update.jsp` | ⏳ 待适配 | 🟡 中 |

**模块完成度**: 2/31 (6.5%)

### fileUpLoad 模块

**文件数量**: 1

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/fileUpLoad/index.jsp` | `wapjsp/student/fileUpLoad/index.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 1/1 (100.0%)

### graduateEntranceExamination 模块

**文件数量**: 1

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/graduateEntranceExamination/informationManagement/index.jsp` | `wapjsp/student/graduateEntranceExamination/informationManagement/index.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 1/1 (100.0%)

### graduatesManagement 模块

**文件数量**: 21

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesCourse/bjgcj.jsp` | `wapjsp/student/graduatesManagement/graduatesCourse/bjgcj.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesCourse/course.jsp` | `wapjsp/student/graduatesManagement/graduatesCourse/course.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesCourse/edit.jsp` | `wapjsp/student/graduatesManagement/graduatesCourse/edit.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesCourse/kxkc.jsp` | `wapjsp/student/graduatesManagement/graduatesCourse/kxkc.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesExamManagement/billUpload/doUpload.jsp` | `wapjsp/student/graduatesManagement/graduatesExamManagement/billUpload/doUpload.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesExamManagement/billUpload/downIndex.jsp` | `wapjsp/student/graduatesManagement/graduatesExamManagement/billUpload/downIndex.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesExamManagement/billUpload/index.jsp` | `wapjsp/student/graduatesManagement/graduatesExamManagement/billUpload/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/course.jsp` | `wapjsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/course.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/index.jsp` | `wapjsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/initindex.jsp` | `wapjsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/initindex.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/kxcourse.jsp` | `wapjsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/kxcourse.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/yxcourse.jsp` | `wapjsp/student/graduatesManagement/graduatesExamManagement/graduatesApply/yxcourse.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/billUpload/doUpload.jsp` | `wapjsp/student/graduatesManagement/restudy/billUpload/doUpload.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/billUpload/downIndex.jsp` | `wapjsp/student/graduatesManagement/restudy/billUpload/downIndex.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/billUpload/index.jsp` | `wapjsp/student/graduatesManagement/restudy/billUpload/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/graduatesApply/bxqkkView.jsp` | `wapjsp/student/graduatesManagement/restudy/graduatesApply/bxqkkView.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/graduatesApply/ckbjgcjView.jsp` | `wapjsp/student/graduatesManagement/restudy/graduatesApply/ckbjgcjView.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/graduatesApply/index.jsp` | `wapjsp/student/graduatesManagement/restudy/graduatesApply/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/graduatesApply/indexFawcqk.jsp` | `wapjsp/student/graduatesManagement/restudy/graduatesApply/indexFawcqk.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/graduatesApply/pfsx.jsp` | `wapjsp/student/graduatesManagement/restudy/graduatesApply/pfsx.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/graduatesManagement/restudy/graduatesApply/showPyfa.jsp` | `wapjsp/student/graduatesManagement/restudy/graduatesApply/showPyfa.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 0/21 (0.0%)

### innovationCredits 模块

**文件数量**: 13

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/innovationCredits/creditCourseReplace/add.jsp` | `wapjsp/student/innovationCredits/creditCourseReplace/add.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/creditCourseReplace/index.jsp` | `wapjsp/student/innovationCredits/creditCourseReplace/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/creditCourseReplace/view.jsp` | `wapjsp/student/innovationCredits/creditCourseReplace/view.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/creditsRecognition/add.jsp` | `wapjsp/student/innovationCredits/creditsRecognition/add.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/creditsRecognition/index.jsp` | `wapjsp/student/innovationCredits/creditsRecognition/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/creditsRecognition/view.jsp` | `wapjsp/student/innovationCredits/creditsRecognition/view.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/innovationProject/add.jsp` | `wapjsp/student/innovationCredits/innovationProject/add.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/innovationProject/add_lndx.jsp` | `wapjsp/student/innovationCredits/innovationProject/add_lndx.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/innovationProject/cjcx.jsp` | `wapjsp/student/innovationCredits/innovationProject/cjcx.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/innovationProject/edit.jsp` | `wapjsp/student/innovationCredits/innovationProject/edit.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/innovationProject/fj.jsp` | `wapjsp/student/innovationCredits/innovationProject/fj.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/innovationProject/index.jsp` | `wapjsp/student/innovationCredits/innovationProject/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/innovationCredits/innovationProject/ryxf.jsp` | `wapjsp/student/innovationCredits/innovationProject/ryxf.jsp` | ⏳ 待适配 | 🟡 中 |

**模块完成度**: 3/13 (23.1%)

### integratedQuery 模块

**文件数量**: 37

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/integratedQuery/course/courseBasicInformation/basicInf.jsp` | `wapjsp/student/integratedQuery/course/courseBasicInformation/basicInf.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/course/courseSchdule/courseDetail.jsp` | `wapjsp/student/integratedQuery/course/courseSchdule/courseDetail.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/course/courseSchdule/index.jsp` | `wapjsp/student/integratedQuery/course/courseSchdule/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/instructionPlanQuery/detail/index.jsp` | `wapjsp/student/integratedQuery/instructionPlanQuery/detail/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/instructionPlanQuery/detail/showPyfa.jsp` | `wapjsp/student/integratedQuery/instructionPlanQuery/detail/showPyfa.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/planCompletion/index.jsp` | `wapjsp/student/integratedQuery/planCompletion/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/planCompletion/pfsx.jsp` | `wapjsp/student/integratedQuery/planCompletion/pfsx.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/planCompletion/showPyfa.jsp` | `wapjsp/student/integratedQuery/planCompletion/showPyfa.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/planSearch/index.jsp` | `wapjsp/student/integratedQuery/planSearch/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/allPassingScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/allPassingScores/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/allTermScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/allTermScores/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/coursePropertyScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/coursePropertyScores/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/experimentScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/experimentScores/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/experimentScores/syfxcjIndex.jsp` | `wapjsp/student/integratedQuery/scoreQuery/experimentScores/syfxcjIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/experimentScores/syxmcjIndex.jsp` | `wapjsp/student/integratedQuery/scoreQuery/experimentScores/syxmcjIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/externalScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/externalScores/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/physicalTestScore/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/physicalTestScore/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/schemeScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/schemeScores/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/scoreGiveUp/add.jsp` | `wapjsp/student/integratedQuery/scoreQuery/scoreGiveUp/add.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/scoreGiveUp/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/scoreGiveUp/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/scoreGiveUp/search.jsp` | `wapjsp/student/integratedQuery/scoreQuery/scoreGiveUp/search.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/scoresCard/CAReportCardsindex.jsp` | `wapjsp/student/integratedQuery/scoreQuery/scoresCard/CAReportCardsindex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/scoresCard/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/scoresCard/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/subitemScores/fxcj.jsp` | `wapjsp/student/integratedQuery/scoreQuery/subitemScores/fxcj.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/subitemScores/fxcjIndex.jsp` | `wapjsp/student/integratedQuery/scoreQuery/subitemScores/fxcjIndex.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/subitemScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/subitemScores/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/subitemScores/mxcjIndex.jsp` | `wapjsp/student/integratedQuery/scoreQuery/subitemScores/mxcjIndex.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/thisTermScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/thisTermScores/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/thisTermScores/ytdx/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/thisTermScores/ytdx/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/unpassedScores/index.jsp` | `wapjsp/student/integratedQuery/scoreQuery/unpassedScores/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/teachingMaterial/ReceiveSearch/jcxx.jsp` | `wapjsp/student/integratedQuery/teachingMaterial/ReceiveSearch/jcxx.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/teachingMaterial/SelectionSearch/index.jsp` | `wapjsp/student/integratedQuery/teachingMaterial/SelectionSearch/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/teachingMaterial/classGetBook/index.jsp` | `wapjsp/student/integratedQuery/teachingMaterial/classGetBook/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/teachingMaterial/teachingMaterialQuery/classBookIndex.jsp` | `wapjsp/student/integratedQuery/teachingMaterial/teachingMaterialQuery/classBookIndex.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/teachingMaterial/teachingMaterialQuery/classBookPayIndex.jsp` | `wapjsp/student/integratedQuery/teachingMaterial/teachingMaterialQuery/classBookPayIndex.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/teachingMaterial/teachingMaterialQuery/detailExpensesIndex.jsp` | `wapjsp/student/integratedQuery/teachingMaterial/teachingMaterialQuery/detailExpensesIndex.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/integratedQuery/teachingMaterial/teachingMaterialQuery/index.jsp` | `wapjsp/student/integratedQuery/teachingMaterial/teachingMaterialQuery/index.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 14/37 (37.8%)

### internship 模块

**文件数量**: 11

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/internship/daily/add.jsp` | `wapjsp/student/internship/daily/add.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/daily/index.jsp` | `wapjsp/student/internship/daily/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/daily/view.jsp` | `wapjsp/student/internship/daily/view.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/internshipExecutionPlanCompletion/edit.jsp` | `wapjsp/student/internship/internshipExecutionPlanCompletion/edit.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/internshipExecutionPlanCompletion/index.jsp` | `wapjsp/student/internship/internshipExecutionPlanCompletion/index.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/sxbggl/sxbginfoDetails.jsp` | `wapjsp/student/internship/sxbggl/sxbginfoDetails.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/sxbggl/uploadIndex.jsp` | `wapjsp/student/internship/sxbggl/uploadIndex.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/sxbggl/uploadView.jsp` | `wapjsp/student/internship/sxbggl/uploadView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/sxgcgl/replayView.jsp` | `wapjsp/student/internship/sxgcgl/replayView.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/sxgcgl/sxgcglIndex.jsp` | `wapjsp/student/internship/sxgcgl/sxgcglIndex.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/internship/sxzbgl/sxzbglIndex.jsp` | `wapjsp/student/internship/sxzbgl/sxzbglIndex.jsp` | ⏳ 待适配 | 🟡 中 |

**模块完成度**: 1/11 (9.1%)

### laborEducation 模块

**文件数量**: 12

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/laborEducation/eventRegistration/add.jsp` | `wapjsp/student/laborEducation/eventRegistration/add.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/eventRegistration/detail.jsp` | `wapjsp/student/laborEducation/eventRegistration/detail.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/eventRegistration/index.jsp` | `wapjsp/student/laborEducation/eventRegistration/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/eventRegistration/view.jsp` | `wapjsp/student/laborEducation/eventRegistration/view.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/laborEducation/add.jsp` | `wapjsp/student/laborEducation/laborEducation/add.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/laborEducation/index.jsp` | `wapjsp/student/laborEducation/laborEducation/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/laborEducation/wap/add.jsp` | `wapjsp/student/laborEducation/laborEducation/wap/add.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/laborEducation/wap/index.jsp` | `wapjsp/student/laborEducation/laborEducation/wap/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/practicalActivity/add.jsp` | `wapjsp/student/laborEducation/practicalActivity/add.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/practicalActivity/addXdth.jsp` | `wapjsp/student/laborEducation/practicalActivity/addXdth.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/practicalActivity/index.jsp` | `wapjsp/student/laborEducation/practicalActivity/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/laborEducation/stuSummaryAchievements/index.jsp` | `wapjsp/student/laborEducation/stuSummaryAchievements/index.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 3/12 (25.0%)

### lnuinnovationCredits 模块

**文件数量**: 4

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/lnuinnovationCredits/studentFilesInquiry/index.jsp` | `wapjsp/student/lnuinnovationCredits/studentFilesInquiry/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/lnuinnovationCredits/studentScoreInquiry/index.jsp` | `wapjsp/student/lnuinnovationCredits/studentScoreInquiry/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/lnuinnovationCredits/zmsq/index.jsp` | `wapjsp/student/lnuinnovationCredits/zmsq/index.jsp` | ✅ 已完成 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/lnuinnovationCredits/zmsq/zmsq.jsp` | `wapjsp/student/lnuinnovationCredits/zmsq/zmsq.jsp` | ⏳ 待适配 | 🟡 中 |

**模块完成度**: 3/4 (75.0%)

### main 模块

**文件数量**: 4

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/main/customerServiceCenter/index.jsp` | `wapjsp/student/main/customerServiceCenter/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/main/inbox.jsp` | `wapjsp/student/main/inbox.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/main/noticeListIndex.jsp` | `wapjsp/student/main/noticeListIndex.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/main/restschedule.jsp` | `wapjsp/student/main/restschedule.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 4/4 (100.0%)

### myAttention 模块

**文件数量**: 3

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/myAttention/cultivationPlanShow.jsp` | `wapjsp/student/myAttention/cultivationPlanShow.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/myAttention/index.jsp` | `wapjsp/student/myAttention/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/myAttention/informShow.jsp` | `wapjsp/student/myAttention/informShow.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 1/3 (33.3%)

### noticeManagement 模块

**文件数量**: 4

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/noticeManagement/evaluateNoticeIndex.jsp` | `wapjsp/student/noticeManagement/evaluateNoticeIndex.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/noticeManagement/index.jsp` | `wapjsp/student/noticeManagement/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/noticeManagement/selectCourseNoticeDetail.jsp` | `wapjsp/student/noticeManagement/selectCourseNoticeDetail.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/noticeManagement/selectEvaluateNoticeDetail.jsp` | `wapjsp/student/noticeManagement/selectEvaluateNoticeDetail.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 4/4 (100.0%)

### personalManagement 模块

**文件数量**: 286

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/detail.jsp` | `wapjsp/student/personalManagement/achievementDetermination/detail.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/detail_short.jsp` | `wapjsp/student/personalManagement/achievementDetermination/detail_short.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/edit.jsp` | `wapjsp/student/personalManagement/achievementDetermination/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/edit_short.jsp` | `wapjsp/student/personalManagement/achievementDetermination/edit_short.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/index.jsp` | `wapjsp/student/personalManagement/achievementDetermination/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/queryCourses.jsp` | `wapjsp/student/personalManagement/achievementDetermination/queryCourses.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/applyTeacher/addApplyInfo.jsp` | `wapjsp/student/personalManagement/applyTeacher/addApplyInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/applyTeacher/index.jsp` | `wapjsp/student/personalManagement/applyTeacher/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/applyTeacher/seeApply.jsp` | `wapjsp/student/personalManagement/applyTeacher/seeApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/bodyexamination/orderexam/index.jsp` | `wapjsp/student/personalManagement/bodyexamination/orderexam/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/bodyexamination/scoreresultstatistics/index.jsp` | `wapjsp/student/personalManagement/bodyexamination/scoreresultstatistics/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/excellentProject/index.jsp` | `wapjsp/student/personalManagement/excellentProject/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/achievementRecognition/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/achievementRecognition/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/achievementRecognition/edit.jsp` | `wapjsp/student/personalManagement/individualApplication/achievementRecognition/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/achievementRecognition/index.jsp` | `wapjsp/student/personalManagement/individualApplication/achievementRecognition/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/achievementRecognition/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/achievementRecognition/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/approval.jsp` | `wapjsp/student/personalManagement/individualApplication/approval.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/baccalaureateapplication/edit.jsp` | `wapjsp/student/personalManagement/individualApplication/baccalaureateapplication/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/baccalaureateapplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/baccalaureateapplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/baccalaureateapplication/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/baccalaureateapplication/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/baccalaureateapplication/show.jsp` | `wapjsp/student/personalManagement/individualApplication/baccalaureateapplication/show.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/byElectionApplication/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/byElectionApplication/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/byElectionApplication/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/byElectionApplication/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/byElectionApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/byElectionApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/byElectionApplication/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/byElectionApplication/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changeCourseClassApplication/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/changeCourseClassApplication/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changeCourseClassApplication/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/changeCourseClassApplication/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changeCourseClassApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/changeCourseClassApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changeCourseClassApplication/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/changeCourseClassApplication/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changeStudentInfo/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/changeStudentInfo/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changeStudentInfo/index.jsp` | `wapjsp/student/personalManagement/individualApplication/changeStudentInfo/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changeStudentInfo/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/changeStudentInfo/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changepapertitle/addApply.jsp` | `wapjsp/student/personalManagement/individualApplication/changepapertitle/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changepapertitle/index.jsp` | `wapjsp/student/personalManagement/individualApplication/changepapertitle/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changepapertitle/seeApply.jsp` | `wapjsp/student/personalManagement/individualApplication/changepapertitle/seeApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/changepapertitle/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/changepapertitle/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/classIdentification/apply.jsp` | `wapjsp/student/personalManagement/individualApplication/classIdentification/apply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/classIdentification/index.jsp` | `wapjsp/student/personalManagement/individualApplication/classIdentification/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/classIdentification/kc.jsp` | `wapjsp/student/personalManagement/individualApplication/classIdentification/kc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/classIdentification/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/classIdentification/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/classIdentification/show.jsp` | `wapjsp/student/personalManagement/individualApplication/classIdentification/show.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/competitionExemApplication/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/competitionExemApplication/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/competitionExemApplication/addTeacher.jsp` | `wapjsp/student/personalManagement/individualApplication/competitionExemApplication/addTeacher.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/competitionExemApplication/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/competitionExemApplication/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/competitionExemApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/competitionExemApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/competitionExemApplication/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/competitionExemApplication/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/courseGroupReplace/addApply.jsp` | `wapjsp/student/personalManagement/individualApplication/courseGroupReplace/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/courseGroupReplace/index.jsp` | `wapjsp/student/personalManagement/individualApplication/courseGroupReplace/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/courseGroupReplace/seeApply.jsp` | `wapjsp/student/personalManagement/individualApplication/courseGroupReplace/seeApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/courseGroupReplace/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/courseGroupReplace/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/courseGroupReplace/selectScore.jsp` | `wapjsp/student/personalManagement/individualApplication/courseGroupReplace/selectScore.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/creditCertification/edit.jsp` | `wapjsp/student/personalManagement/individualApplication/creditCertification/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/creditCertification/index.jsp` | `wapjsp/student/personalManagement/individualApplication/creditCertification/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/creditCertification/kc.jsp` | `wapjsp/student/personalManagement/individualApplication/creditCertification/kc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/creditCertification/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/creditCertification/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/creditCertification/selectCourse.jsp` | `wapjsp/student/personalManagement/individualApplication/creditCertification/selectCourse.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/creditCertification/show.jsp` | `wapjsp/student/personalManagement/individualApplication/creditCertification/show.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/curriculumReplacement/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/curriculumReplacement/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/curriculumReplacement/editeInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/curriculumReplacement/editeInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/curriculumReplacement/index.jsp` | `wapjsp/student/personalManagement/individualApplication/curriculumReplacement/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/curriculumReplacement/updateInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/curriculumReplacement/updateInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/dropCourseApplication/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/dropCourseApplication/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/dropCourseApplication/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/dropCourseApplication/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/dropCourseApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/dropCourseApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/dropCourseApplication/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/dropCourseApplication/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/entranceRegistrationForm/index.jsp` | `wapjsp/student/personalManagement/individualApplication/entranceRegistrationForm/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/exemptionApplication/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/exemptionApplication/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/exemptionApplication/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/exemptionApplication/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/exemptionApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/exemptionApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/extensionInnovation/edit.jsp` | `wapjsp/student/personalManagement/individualApplication/extensionInnovation/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/extensionInnovation/index.jsp` | `wapjsp/student/personalManagement/individualApplication/extensionInnovation/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/extensionInnovation/xmsqIndex.jsp` | `wapjsp/student/personalManagement/individualApplication/extensionInnovation/xmsqIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/gradeChange/addApply.jsp` | `wapjsp/student/personalManagement/individualApplication/gradeChange/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/gradeChange/index.jsp` | `wapjsp/student/personalManagement/individualApplication/gradeChange/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/gradeChange/seeApply.jsp` | `wapjsp/student/personalManagement/individualApplication/gradeChange/seeApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/gradeChange/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/gradeChange/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/gradeChange/selectGrade.jsp` | `wapjsp/student/personalManagement/individualApplication/gradeChange/selectGrade.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/graduationDegreeApplication/edit.jsp` | `wapjsp/student/personalManagement/individualApplication/graduationDegreeApplication/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/graduationDegreeApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/graduationDegreeApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/joinTheArmyApplication/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/joinTheArmyApplication/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/joinTheArmyApplication/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/joinTheArmyApplication/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/joinTheArmyApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/joinTheArmyApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/joinTheArmyApplication/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/joinTheArmyApplication/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/listenFreeApplication/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/listenFreeApplication/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/listenFreeApplication/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/listenFreeApplication/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/listenFreeApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/listenFreeApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/microspecialtyMgt/microprofessionalRegistration/index.jsp` | `wapjsp/student/personalManagement/individualApplication/microspecialtyMgt/microprofessionalRegistration/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/microspecialtyMgt/microprofessionalRegistration/registrationApplication.jsp` | `wapjsp/student/personalManagement/individualApplication/microspecialtyMgt/microprofessionalRegistration/registrationApplication.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/microspecialtyMgt/microprofessionalRegistration/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/microspecialtyMgt/microprofessionalRegistration/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/projectApply/add.jsp` | `wapjsp/student/personalManagement/individualApplication/projectApply/add.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/projectApply/addTeacher.jsp` | `wapjsp/student/personalManagement/individualApplication/projectApply/addTeacher.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/projectApply/index.jsp` | `wapjsp/student/personalManagement/individualApplication/projectApply/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/projectApply/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/projectApply/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/projectApply/show.jsp` | `wapjsp/student/personalManagement/individualApplication/projectApply/show.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/projectApply/showfj.jsp` | `wapjsp/student/personalManagement/individualApplication/projectApply/showfj.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/rebuildCourseSelection/addApply.jsp` | `wapjsp/student/personalManagement/individualApplication/rebuildCourseSelection/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/rebuildCourseSelection/fj.jsp` | `wapjsp/student/personalManagement/individualApplication/rebuildCourseSelection/fj.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/rebuildCourseSelection/index.jsp` | `wapjsp/student/personalManagement/individualApplication/rebuildCourseSelection/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/rebuildCourseSelection/print.jsp` | `wapjsp/student/personalManagement/individualApplication/rebuildCourseSelection/print.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/rebuildCourseSelection/selectCourse.jsp` | `wapjsp/student/personalManagement/individualApplication/rebuildCourseSelection/selectCourse.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/reviewScoreReduction/addCurriculum.jsp` | `wapjsp/student/personalManagement/individualApplication/reviewScoreReduction/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/reviewScoreReduction/editInfo.jsp` | `wapjsp/student/personalManagement/individualApplication/reviewScoreReduction/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/reviewScoreReduction/index.jsp` | `wapjsp/student/personalManagement/individualApplication/reviewScoreReduction/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/routineWork/busSection/edit.jsp` | `wapjsp/student/personalManagement/individualApplication/routineWork/busSection/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/routineWork/busSection/index.jsp` | `wapjsp/student/personalManagement/individualApplication/routineWork/busSection/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/routineWork/busSection/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/routineWork/busSection/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scholarshipApplication/add.jsp` | `wapjsp/student/personalManagement/individualApplication/scholarshipApplication/add.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scholarshipApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/scholarshipApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scholarshipApplication/notice.jsp` | `wapjsp/student/personalManagement/individualApplication/scholarshipApplication/notice.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreCheck/addApply.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreCheck/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreCheck/index.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreCheck/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreCheck/seeApply.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreCheck/seeApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreCheck/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreCheck/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreCheck/selectScore.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreCheck/selectScore.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreRecheck/addApply.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreRecheck/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreRecheck/index.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreRecheck/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreRecheck/seeApply.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreRecheck/seeApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/scoreRecheck/selectApprover.jsp` | `wapjsp/student/personalManagement/individualApplication/scoreRecheck/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/applicationManagement/projectTitleChange/edit.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/applicationManagement/projectTitleChange/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/applicationManagement/projectTitleChange/historicalRecord.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/applicationManagement/projectTitleChange/historicalRecord.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/applicationManagement/projectTitleChange/index.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/applicationManagement/projectTitleChange/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/applicationManagement/projectTitleChange/view.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/applicationManagement/projectTitleChange/view.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/countindex.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/countindex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/detail/applydetail.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/detail/applydetail.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/detail/midsemesterdetail.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/detail/midsemesterdetail.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/detail/nodetermdetail.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/detail/nodetermdetail.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/detail/showdetail.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/detail/showdetail.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/addStudent.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/addStudent.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/addTeacher.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/addTeacher.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/addTeamMembers.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/addTeamMembers.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/applyfbinnovationadd.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/applyfbinnovationadd.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/applyfbinnovationedit.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/applyfbinnovationedit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/applyinnovation.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/applyinnovation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/budgetEdit.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/budgetEdit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/budgetIndex.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/budgetIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/changingTeamMembers.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/changingTeamMembers.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/costinnovation.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/costinnovation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/midinnovation.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/midinnovation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/edit/nodeterminnovation.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/edit/nodeterminnovation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/fj.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/fj.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/fjJscg.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/fjJscg.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/tabindex.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/tabindex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/studentsInnovation/tabxmfbindex.jsp` | `wapjsp/student/personalManagement/individualApplication/studentsInnovation/tabxmfbindex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/terminationInnovationApplication/edit.jsp` | `wapjsp/student/personalManagement/individualApplication/terminationInnovationApplication/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/terminationInnovationApplication/index.jsp` | `wapjsp/student/personalManagement/individualApplication/terminationInnovationApplication/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/terminationInnovationApplication/xmsqIndex.jsp` | `wapjsp/student/personalManagement/individualApplication/terminationInnovationApplication/xmsqIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/informationCollection/index.jsp` | `wapjsp/student/personalManagement/informationCollection/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/informationCollection/indexHbgy.jsp` | `wapjsp/student/personalManagement/informationCollection/indexHbgy.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/informationCollection/qztx.jsp` | `wapjsp/student/personalManagement/informationCollection/qztx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/informationCollection/sftytx.jsp` | `wapjsp/student/personalManagement/informationCollection/sftytx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/largeClassDiversion/choseProfession.jsp` | `wapjsp/student/personalManagement/largeClassDiversion/choseProfession.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/largeClassDiversion/index.jsp` | `wapjsp/student/personalManagement/largeClassDiversion/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/largeClassDiversion/printSpecialtiesInfo.jsp` | `wapjsp/student/personalManagement/largeClassDiversion/printSpecialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/largeClassDiversion/showInfo.jsp` | `wapjsp/student/personalManagement/largeClassDiversion/showInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/largeClassDiversion/specialtiesInfo.jsp` | `wapjsp/student/personalManagement/largeClassDiversion/specialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/lwydqr/index.jsp` | `wapjsp/student/personalManagement/lwydqr/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplit/choseProfession.jsp` | `wapjsp/student/personalManagement/majorsSplit/choseProfession.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplit/index.jsp` | `wapjsp/student/personalManagement/majorsSplit/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplit/printSpecialtiesInfo.jsp` | `wapjsp/student/personalManagement/majorsSplit/printSpecialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplit/showInfo.jsp` | `wapjsp/student/personalManagement/majorsSplit/showInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplit/specialtiesInfo.jsp` | `wapjsp/student/personalManagement/majorsSplit/specialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplitOther/choseProfession.jsp` | `wapjsp/student/personalManagement/majorsSplitOther/choseProfession.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplitOther/index.jsp` | `wapjsp/student/personalManagement/majorsSplitOther/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplitOther/printSpecialtiesInfo.jsp` | `wapjsp/student/personalManagement/majorsSplitOther/printSpecialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplitOther/showInfo.jsp` | `wapjsp/student/personalManagement/majorsSplitOther/showInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/majorsSplitOther/specialtiesInfo.jsp` | `wapjsp/student/personalManagement/majorsSplitOther/specialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/Errer.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/Errer.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/Errer1.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/Errer1.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/Errer2.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/Errer2.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/Tree.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/Tree.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/XjFxfazc3.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/XjFxfazc3.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/applicationFailedCourse.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/applicationFailedCourse.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/attendedCoursesNotReplaced.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/attendedCoursesNotReplaced.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/bjgkcxz.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/bjgkcxz.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/fabgzc2.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/fabgzc2.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/fajhInforBody.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/fajhInforBody.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/faxdsq1.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/faxdsq1.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/index.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/kcfabgwc.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/kcfabgwc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/kcsxbgwc.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/kcsxbgwc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/kctdbgwc.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/kctdbgwc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/logoutMinorProgram.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/logoutMinorProgram.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/logoutMinorProgramForscdx.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/logoutMinorProgramForscdx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/look.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/look.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/lookKc.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/lookKc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/modifyCourseForTraining.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/modifyCourseForTraining.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/modifyCourseProperty.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/modifyCourseProperty.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/sqxx.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/sqxx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/wxkctdwc.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/wxkctdwc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/wxkcxz.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/wxkcxz.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/xj_kcfabgsq.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/xj_kcfabgsq.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/xj_kcsxbgsq.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/xj_kcsxbgsq.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/xj_kctd_wxkc.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/xj_kctd_wxkc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/xj_kctdsqb.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/xj_kctdsqb.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/minorProgramRegistration/xsFxfaxdsq_detail.jsp` | `wapjsp/student/personalManagement/minorProgramRegistration/xsFxfaxdsq_detail.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/myRollCard/index.jsp` | `wapjsp/student/personalManagement/myRollCard/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/myRollCard/myInfoEdit.jsp` | `wapjsp/student/personalManagement/myRollCard/myInfoEdit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/ordertransfer/choseProfession.jsp` | `wapjsp/student/personalManagement/ordertransfer/choseProfession.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/ordertransfer/index.jsp` | `wapjsp/student/personalManagement/ordertransfer/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/ordertransfer/printSpecialtiesInfo.jsp` | `wapjsp/student/personalManagement/ordertransfer/printSpecialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/ordertransfer/showInfo.jsp` | `wapjsp/student/personalManagement/ordertransfer/showInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/ordertransfer/specialtiesInfo.jsp` | `wapjsp/student/personalManagement/ordertransfer/specialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperPublicationManagement/add.jsp` | `wapjsp/student/personalManagement/paperPublicationManagement/add.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperPublicationManagement/index.jsp` | `wapjsp/student/personalManagement/paperPublicationManagement/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperPublicationManagement/selectApprover.jsp` | `wapjsp/student/personalManagement/paperPublicationManagement/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperSubmit/downloadLwxxFj.jsp` | `wapjsp/student/personalManagement/paperSubmit/downloadLwxxFj.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperSubmit/index.jsp` | `wapjsp/student/personalManagement/paperSubmit/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperSubmit/showLwxx.jsp` | `wapjsp/student/personalManagement/paperSubmit/showLwxx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperSubmit/showLwxxDbcj.jsp` | `wapjsp/student/personalManagement/paperSubmit/showLwxxDbcj.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperSubmit/uploadfile.jsp` | `wapjsp/student/personalManagement/paperSubmit/uploadfile.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperSubmit/uploadlwyw.jsp` | `wapjsp/student/personalManagement/paperSubmit/uploadlwyw.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/paperSubmit/uploadlwywsx.jsp` | `wapjsp/student/personalManagement/paperSubmit/uploadlwywsx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalApplication/curriculumReplacement/addInfo.jsp` | `wapjsp/student/personalManagement/personalApplication/curriculumReplacement/addInfo.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalApplication/curriculumReplacement/index.jsp` | `wapjsp/student/personalManagement/personalApplication/curriculumReplacement/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalApplication/curriculumReplacement/look.jsp` | `wapjsp/student/personalManagement/personalApplication/curriculumReplacement/look.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalApplication/curriculumReplacement/rules.jsp` | `wapjsp/student/personalManagement/personalApplication/curriculumReplacement/rules.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalApplication/curriculumReplacement/seeInfo.jsp` | `wapjsp/student/personalManagement/personalApplication/curriculumReplacement/seeInfo.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalApplication/curriculumReplacement/sqyy.jsp` | `wapjsp/student/personalManagement/personalApplication/curriculumReplacement/sqyy.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalInfoUpdate/informationCollection.jsp` | `wapjsp/student/personalManagement/personalInfoUpdate/informationCollection.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalInfoUpdate/modifyPass.jsp` | `wapjsp/student/personalManagement/personalInfoUpdate/modifyPass.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalInfoUpdate/whjyjsxy/xjInfo.jsp` | `wapjsp/student/personalManagement/personalInfoUpdate/whjyjsxy/xjInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/personalInfoUpdate/xjInfo.jsp` | `wapjsp/student/personalManagement/personalInfoUpdate/xjInfo.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/index.jsp` | `wapjsp/student/personalManagement/processManagement/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/paperDraftApply/add.jsp` | `wapjsp/student/personalManagement/processManagement/paperDraftApply/add.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/paperDraftApply/edit.jsp` | `wapjsp/student/personalManagement/processManagement/paperDraftApply/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/paperDraftApply/index.jsp` | `wapjsp/student/personalManagement/processManagement/paperDraftApply/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/selectApprover.jsp` | `wapjsp/student/personalManagement/processManagement/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/add.jsp` | `wapjsp/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/add.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/edit.jsp` | `wapjsp/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/index.jsp` | `wapjsp/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/selectApprover.jsp` | `wapjsp/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/update.jsp` | `wapjsp/student/personalManagement/processManagement/update.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/writingPlan/history.jsp` | `wapjsp/student/personalManagement/processManagement/writingPlan/history.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/processManagement/writingPlan/index.jsp` | `wapjsp/student/personalManagement/processManagement/writingPlan/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/projectScore/fxcjIndex.jsp` | `wapjsp/student/personalManagement/projectScore/fxcjIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/projectScore/index.jsp` | `wapjsp/student/personalManagement/projectScore/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/projectSelect/index.jsp` | `wapjsp/student/personalManagement/projectSelect/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/projectSelect/showfj.jsp` | `wapjsp/student/personalManagement/projectSelect/showfj.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/projectSelect/view.jsp` | `wapjsp/student/personalManagement/projectSelect/view.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/pushexemptionmgt/destinationApply/addApply.jsp` | `wapjsp/student/personalManagement/pushexemptionmgt/destinationApply/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/pushexemptionmgt/destinationApply/index.jsp` | `wapjsp/student/personalManagement/pushexemptionmgt/destinationApply/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/pushexemptionmgt/destinationApply/seeApply.jsp` | `wapjsp/student/personalManagement/pushexemptionmgt/destinationApply/seeApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/pushexemptionmgt/destinationApply/selectApprover.jsp` | `wapjsp/student/personalManagement/pushexemptionmgt/destinationApply/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/pushexemptionmgt/resultconfirmation/index.jsp` | `wapjsp/student/personalManagement/pushexemptionmgt/resultconfirmation/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/pushexemptionmgt/stusubapply/edit.jsp` | `wapjsp/student/personalManagement/pushexemptionmgt/stusubapply/edit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/pushexemptionmgt/stusubapply/index.jsp` | `wapjsp/student/personalManagement/pushexemptionmgt/stusubapply/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/recordOfTheDefence/addApply.jsp` | `wapjsp/student/personalManagement/recordOfTheDefence/addApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/recordOfTheDefence/index.jsp` | `wapjsp/student/personalManagement/recordOfTheDefence/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/reviewResults/index.jsp` | `wapjsp/student/personalManagement/reviewResults/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/reviewResults/zymzdx/index.jsp` | `wapjsp/student/personalManagement/reviewResults/zymzdx/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/detail.jsp` | `wapjsp/student/personalManagement/rollInfo/detail.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/dzzc.jsp` | `wapjsp/student/personalManagement/rollInfo/dzzc.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/jcxx.jsp` | `wapjsp/student/personalManagement/rollInfo/jcxx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/jcxxInfo.jsp` | `wapjsp/student/personalManagement/rollInfo/jcxxInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/update.jsp` | `wapjsp/student/personalManagement/rollInfo/update.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/whzyjsxy/xjInfo.jsp` | `wapjsp/student/personalManagement/rollInfo/whzyjsxy/xjInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/xjInfo.jsp` | `wapjsp/student/personalManagement/rollInfo/xjInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/ydxx.jsp` | `wapjsp/student/personalManagement/rollInfo/ydxx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/ydxxInfo.jsp` | `wapjsp/student/personalManagement/rollInfo/ydxxInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/zln.jsp` | `wapjsp/student/personalManagement/rollInfo/zln.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/add.jsp` | `wapjsp/student/personalManagement/studentChange/add.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/addCurriculum.jsp` | `wapjsp/student/personalManagement/studentChange/addCurriculum.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/choseProfession.jsp` | `wapjsp/student/personalManagement/studentChange/choseProfession.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/printSpecialtiesInfo.jsp` | `wapjsp/student/personalManagement/studentChange/printSpecialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/selectApprover.jsp` | `wapjsp/student/personalManagement/studentChange/selectApprover.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/showInfo.jsp` | `wapjsp/student/personalManagement/studentChange/showInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/specialtiesApply.jsp` | `wapjsp/student/personalManagement/studentChange/specialtiesApply.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/specialtiesInfo.jsp` | `wapjsp/student/personalManagement/studentChange/specialtiesInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/view.jsp` | `wapjsp/student/personalManagement/studentChange/view.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/xjyddy.jsp` | `wapjsp/student/personalManagement/studentChange/xjyddy.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentIdCard/addInfo.jsp` | `wapjsp/student/personalManagement/studentIdCard/addInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentIdCard/editInfo.jsp` | `wapjsp/student/personalManagement/studentIdCard/editInfo.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/studentIdCard/index.jsp` | `wapjsp/student/personalManagement/studentIdCard/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/thesisGuidanceProcess/index.jsp` | `wapjsp/student/personalManagement/thesisGuidanceProcess/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/thesisGuidanceProcess/seeGuidance.jsp` | `wapjsp/student/personalManagement/thesisGuidanceProcess/seeGuidance.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/thesisGuidanceRecord/index.jsp` | `wapjsp/student/personalManagement/thesisGuidanceRecord/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/thesisGuidanceRecord/seeGuidance.jsp` | `wapjsp/student/personalManagement/thesisGuidanceRecord/seeGuidance.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/thesisGuidanceRecord/writeGuidance.jsp` | `wapjsp/student/personalManagement/thesisGuidanceRecord/writeGuidance.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/thesisarchivalapply/index.jsp` | `wapjsp/student/personalManagement/thesisarchivalapply/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/transfermajor/add.jsp` | `wapjsp/student/personalManagement/transfermajor/add.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/transfermajor/index.jsp` | `wapjsp/student/personalManagement/transfermajor/index.jsp` | ✅ 已完成 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/transfermajor/printconfirm.jsp` | `wapjsp/student/personalManagement/transfermajor/printconfirm.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/viewTaskBook/index.jsp` | `wapjsp/student/personalManagement/viewTaskBook/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/viewingTheListOfTransferStudentsAnnouncement/index.jsp` | `wapjsp/student/personalManagement/viewingTheListOfTransferStudentsAnnouncement/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/personalManagement/warning/index.jsp` | `wapjsp/student/personalManagement/warning/index.jsp` | ✅ 已完成 | 🔴 高 |

**模块完成度**: 12/286 (4.2%)

### postgraduate 模块

**文件数量**: 1

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/postgraduate/postgraduateExamination/index.jsp` | `wapjsp/student/postgraduate/postgraduateExamination/index.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 0/1 (0.0%)

### practicing 模块

**文件数量**: 6

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/practicing/independentPractice/add.jsp` | `wapjsp/student/practicing/independentPractice/add.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/practicing/independentPractice/edit.jsp` | `wapjsp/student/practicing/independentPractice/edit.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/practicing/independentPractice/index.jsp` | `wapjsp/student/practicing/independentPractice/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/practicing/independentPractice/showApply.jsp` | `wapjsp/student/practicing/independentPractice/showApply.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/practicing/practice/index.jsp` | `wapjsp/student/practicing/practice/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/practicing/practice/showApply.jsp` | `wapjsp/student/practicing/practice/showApply.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 2/6 (33.3%)

### professionalCertification 模块

**文件数量**: 2

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/professionalCertification/courseEvaluation/evaluation.jsp` | `wapjsp/student/professionalCertification/courseEvaluation/evaluation.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/professionalCertification/courseEvaluation/index.jsp` | `wapjsp/student/professionalCertification/courseEvaluation/index.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 0/2 (0.0%)

### schoolcalendar 模块

**文件数量**: 2

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/schoolcalendar/calendar.jsp` | `wapjsp/student/schoolcalendar/calendar.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/schoolcalendar/index.jsp` | `wapjsp/student/schoolcalendar/index.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 2/2 (100.0%)

### studentCertificatePrinting 模块

**文件数量**: 2

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/studentCertificatePrinting/index.jsp` | `wapjsp/student/studentCertificatePrinting/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/studentCertificatePrinting/provePdf.jsp` | `wapjsp/student/studentCertificatePrinting/provePdf.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 2/2 (100.0%)

### subjectCompetition 模块

**文件数量**: 6

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/subjectCompetition/add.jsp` | `wapjsp/student/subjectCompetition/add.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/subjectCompetition/index.jsp` | `wapjsp/student/subjectCompetition/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/subjectCompetition/indexXssq.jsp` | `wapjsp/student/subjectCompetition/indexXssq.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/subjectCompetition/tjhjxs.jsp` | `wapjsp/student/subjectCompetition/tjhjxs.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/subjectCompetition/tjzdjs.jsp` | `wapjsp/student/subjectCompetition/tjzdjs.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/subjectCompetition/views.jsp` | `wapjsp/student/subjectCompetition/views.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 2/6 (33.3%)

### teachingEvaluation 模块

**文件数量**: 22

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/comprehensiveReview/evaluationPage.jsp` | `wapjsp/student/teachingEvaluation/comprehensiveReview/evaluationPage.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/comprehensiveReview/index.jsp` | `wapjsp/student/teachingEvaluation/comprehensiveReview/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/coursemessageboard/index.jsp` | `wapjsp/student/teachingEvaluation/coursemessageboard/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/coursemessageboard/messageboard.jsp` | `wapjsp/student/teachingEvaluation/coursemessageboard/messageboard.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/industrial/evaluation/evaluation.jsp` | `wapjsp/student/teachingEvaluation/industrial/evaluation/evaluation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/industrial/evaluation/index.jsp` | `wapjsp/student/teachingEvaluation/industrial/evaluation/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/industrial/evaluation/show.jsp` | `wapjsp/student/teachingEvaluation/industrial/evaluation/show.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/industrial/messageinteraction/index.jsp` | `wapjsp/student/teachingEvaluation/industrial/messageinteraction/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/industrial/messageinteraction/toEdit.jsp` | `wapjsp/student/teachingEvaluation/industrial/messageinteraction/toEdit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/newEvaluation/editEvaluationResult.jsp` | `wapjsp/student/teachingEvaluation/newEvaluation/editEvaluationResult.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/newEvaluation/error.jsp` | `wapjsp/student/teachingEvaluation/newEvaluation/error.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/newEvaluation/evaluation.jsp` | `wapjsp/student/teachingEvaluation/newEvaluation/evaluation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/newEvaluation/evaluation2.jsp` | `wapjsp/student/teachingEvaluation/newEvaluation/evaluation2.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/newEvaluation/evaluationPage.jsp` | `wapjsp/student/teachingEvaluation/newEvaluation/evaluationPage.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/newEvaluation/index.jsp` | `wapjsp/student/teachingEvaluation/newEvaluation/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/newEvaluation/lookEvaluation.jsp` | `wapjsp/student/teachingEvaluation/newEvaluation/lookEvaluation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/newEvaluation/lookList.jsp` | `wapjsp/student/teachingEvaluation/newEvaluation/lookList.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/teachingEvaluation/evaluationPage.jsp` | `wapjsp/student/teachingEvaluation/teachingEvaluation/evaluationPage.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/teachingEvaluation/evaluationReusltPage.jsp` | `wapjsp/student/teachingEvaluation/teachingEvaluation/evaluationReusltPage.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/teachingEvaluation/index.jsp` | `wapjsp/student/teachingEvaluation/teachingEvaluation/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/teachingEvaluation/page2.jsp` | `wapjsp/student/teachingEvaluation/teachingEvaluation/page2.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluation/teachingEvaluation/zgnyEvaluationCover.jsp` | `wapjsp/student/teachingEvaluation/teachingEvaluation/zgnyEvaluationCover.jsp` | ⏳ 待适配 | 🔴 高 |

**模块完成度**: 0/22 (0.0%)

### teachingEvaluationGc 模块

**文件数量**: 27

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/comprehensiveReview/evaluationPage.jsp` | `wapjsp/student/teachingEvaluationGc/comprehensiveReview/evaluationPage.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/comprehensiveReview/index.jsp` | `wapjsp/student/teachingEvaluationGc/comprehensiveReview/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/industrial/evaluation/evaluation.jsp` | `wapjsp/student/teachingEvaluationGc/industrial/evaluation/evaluation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/industrial/evaluation/index.jsp` | `wapjsp/student/teachingEvaluationGc/industrial/evaluation/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/industrial/evaluation/show.jsp` | `wapjsp/student/teachingEvaluationGc/industrial/evaluation/show.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/industrial/messageinteraction/index.jsp` | `wapjsp/student/teachingEvaluationGc/industrial/messageinteraction/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/industrial/messageinteraction/toEdit.jsp` | `wapjsp/student/teachingEvaluationGc/industrial/messageinteraction/toEdit.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/newEvaluation/editEvaluationResult.jsp` | `wapjsp/student/teachingEvaluationGc/newEvaluation/editEvaluationResult.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/newEvaluation/evaluation.jsp` | `wapjsp/student/teachingEvaluationGc/newEvaluation/evaluation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/newEvaluation/evaluation2.jsp` | `wapjsp/student/teachingEvaluationGc/newEvaluation/evaluation2.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/newEvaluation/evaluationPage.jsp` | `wapjsp/student/teachingEvaluationGc/newEvaluation/evaluationPage.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/newEvaluation/index.jsp` | `wapjsp/student/teachingEvaluationGc/newEvaluation/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/newEvaluation/lookEvaluation.jsp` | `wapjsp/student/teachingEvaluationGc/newEvaluation/lookEvaluation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/newEvaluation/lookList.jsp` | `wapjsp/student/teachingEvaluationGc/newEvaluation/lookList.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/error.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/error.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/evaluationPage.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/evaluationPage.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/evaluationPageHbdx.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/evaluationPageHbdx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/evaluationReusltPage.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/evaluationReusltPage.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/index.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/index.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/indexHbdx.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/indexHbdx.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/lyhd.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/lyhd.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/lyhdIndex.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/lyhdIndex.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/notEvaluation.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/notEvaluation.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/page2.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/page2.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/showLyhfTips.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/showLyhfTips.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/verificationcode.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/verificationcode.jsp` | ⏳ 待适配 | 🔴 高 |
| `urpSoft/WEB-INF/jsp/student/teachingEvaluationGc/teachingEvaluation/zgnyEvaluationCover.jsp` | `wapjsp/student/teachingEvaluationGc/teachingEvaluation/zgnyEvaluationCover.jsp` | ⏳ 待适配 | 🔴 高 |

**模块完成度**: 0/27 (0.0%)

### teachingResources 模块

**文件数量**: 24

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/teachingResources/classCurriculum/curriculumInfo.jsp` | `wapjsp/student/teachingResources/classCurriculum/curriculumInfo.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classCurriculum/index.jsp` | `wapjsp/student/teachingResources/classCurriculum/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classroomCurriculum/index.jsp` | `wapjsp/student/teachingResources/classroomCurriculum/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classroomCurriculum/searchClassroomUesedInfo.jsp` | `wapjsp/student/teachingResources/classroomCurriculum/searchClassroomUesedInfo.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classroomCurriculum/searchCurrentWeek.jsp` | `wapjsp/student/teachingResources/classroomCurriculum/searchCurrentWeek.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classroomCurriculum/searchCurriculumInfo.jsp` | `wapjsp/student/teachingResources/classroomCurriculum/searchCurriculumInfo.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classroomCurriculum/show.jsp` | `wapjsp/student/teachingResources/classroomCurriculum/show.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classroomCurriculum/showCurrentWeek.jsp` | `wapjsp/student/teachingResources/classroomCurriculum/showCurrentWeek.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classroomStatusQuery/classroomInfo.jsp` | `wapjsp/student/teachingResources/classroomStatusQuery/classroomInfo.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/classroomStatusQuery/index.jsp` | `wapjsp/student/teachingResources/classroomStatusQuery/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/courseCurriculum/curriculumInfo.jsp` | `wapjsp/student/teachingResources/courseCurriculum/curriculumInfo.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/courseCurriculum/index.jsp` | `wapjsp/student/teachingResources/courseCurriculum/index.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/afTorDate.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/afTorDate.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/chooseWS.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/chooseWS.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/classroomQuery.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/classroomQuery.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/custom.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/custom.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/custom1.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/custom1.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/customDate.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/customDate.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/index.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/room.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/room.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/today.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/today.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/torDate.jsp` | `wapjsp/student/teachingResources/freeClassroomQuery/torDate.jsp` | ⏳ 待适配 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/teacherCurriculum/index.jsp` | `wapjsp/student/teachingResources/teacherCurriculum/index.jsp` | ✅ 已完成 | 🟢 低 |
| `urpSoft/WEB-INF/jsp/student/teachingResources/teacherCurriculum/searchCurriculumInfo.jsp` | `wapjsp/student/teachingResources/teacherCurriculum/searchCurriculumInfo.jsp` | ⏳ 待适配 | 🟢 低 |

**模块完成度**: 2/24 (8.3%)

### thesis 模块

**文件数量**: 4

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/thesis/stageddocuments/uploadsub/index.jsp` | `wapjsp/student/thesis/stageddocuments/uploadsub/index.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/thesis/stageddocuments/uploadsub/showIndex.jsp` | `wapjsp/student/thesis/stageddocuments/uploadsub/showIndex.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/thesis/stageddocuments/uploadsub/uploadstageddoc.jsp` | `wapjsp/student/thesis/stageddocuments/uploadsub/uploadstageddoc.jsp` | ⏳ 待适配 | 🟡 中 |
| `urpSoft/WEB-INF/jsp/student/thesis/thesisDefenseInfo/index.jsp` | `wapjsp/student/thesis/thesisDefenseInfo/index.jsp` | ✅ 已完成 | 🟡 中 |

**模块完成度**: 1/4 (25.0%)

### trainProgram 模块

**文件数量**: 1

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/trainProgram/index.jsp` | `wapjsp/student/trainProgram/index.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 1/1 (100.0%)

### weekLySchedule 模块

**文件数量**: 1

| PC端文件路径 | 移动端文件路径 | 状态 | 优先级 |
|-------------|---------------|------|--------|
| `urpSoft/WEB-INF/jsp/student/weekLySchedule/index.jsp` | `wapjsp/student/weekLySchedule/index.jsp` | ✅ 已完成 | 🟢 低 |

**模块完成度**: 1/1 (100.0%)

## 🚀 实施计划

### 第一阶段：核心功能模块 (优先级：高)

#### personalManagement
待适配文件: 274 个

- `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/detail.jsp`
- `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/detail_short.jsp`
- `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/edit.jsp`
- `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/edit_short.jsp`
- `urpSoft/WEB-INF/jsp/student/personalManagement/achievementDetermination/queryCourses.jsp`
- ... 还有 269 个文件

#### integratedQuery
待适配文件: 23 个

- `urpSoft/WEB-INF/jsp/student/integratedQuery/course/courseBasicInformation/basicInf.jsp`
- `urpSoft/WEB-INF/jsp/student/integratedQuery/course/courseSchdule/courseDetail.jsp`
- `urpSoft/WEB-INF/jsp/student/integratedQuery/instructionPlanQuery/detail/index.jsp`
- `urpSoft/WEB-INF/jsp/student/integratedQuery/instructionPlanQuery/detail/showPyfa.jsp`
- `urpSoft/WEB-INF/jsp/student/integratedQuery/planCompletion/pfsx.jsp`
- ... 还有 18 个文件

#### courseSelectManagement
待适配文件: 39 个

- `urpSoft/WEB-INF/jsp/student/courseSelectManagement/bxkc.jsp`
- `urpSoft/WEB-INF/jsp/student/courseSelectManagement/courseSelectPriority.jsp`
- `urpSoft/WEB-INF/jsp/student/courseSelectManagement/cxxk.jsp`
- `urpSoft/WEB-INF/jsp/student/courseSelectManagement/deleteKcList.jsp`
- `urpSoft/WEB-INF/jsp/student/courseSelectManagement/draw.jsp`
- ... 还有 34 个文件

#### courseTableOfThisSemester
待适配文件: 6 个

- `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/canlendar_show.jsp`
- `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/courseSelectResult.jsp`
- `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/evaluateIndex.jsp`
- `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/fileNoExist.jsp`
- `urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/mobileindex.jsp`
- ... 还有 1 个文件

#### examinationManagement
待适配文件: 19 个

- `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/cns.jsp`
- `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/dyzkz.jsp`
- `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/grade.jsp`
- `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/hdxx.jsp`
- `urpSoft/WEB-INF/jsp/student/examinationManagement/cet/index.jsp`
- ... 还有 14 个文件

#### teachingEvaluation
待适配文件: 22 个

- `urpSoft/WEB-INF/jsp/student/teachingEvaluation/comprehensiveReview/evaluationPage.jsp`
- `urpSoft/WEB-INF/jsp/student/teachingEvaluation/comprehensiveReview/index.jsp`
- `urpSoft/WEB-INF/jsp/student/teachingEvaluation/coursemessageboard/index.jsp`
- `urpSoft/WEB-INF/jsp/student/teachingEvaluation/coursemessageboard/messageboard.jsp`
- `urpSoft/WEB-INF/jsp/student/teachingEvaluation/industrial/evaluation/evaluation.jsp`
- ... 还有 17 个文件

### 第二阶段：扩展功能模块 (优先级：中)

#### experiment
待适配文件: 29 个

#### internship
待适配文件: 10 个

#### innovationCredits
待适配文件: 10 个

#### thesis
待适配文件: 3 个

### 第三阶段：管理功能模块 (优先级：低)

#### certificationExam
待适配文件: 2 个

#### courseVoluntary
待适配文件: 1 个

#### credibleReportCard
待适配文件: 4 个

#### creditCheck
待适配文件: 2 个

#### creditTuition
待适配文件: 1 个

#### exemptsExam
待适配文件: 4 个

#### graduatesManagement
待适配文件: 21 个

#### laborEducation
待适配文件: 9 个

#### lnuinnovationCredits
待适配文件: 1 个

#### myAttention
待适配文件: 2 个

#### postgraduate
待适配文件: 1 个

#### practicing
待适配文件: 4 个

#### professionalCertification
待适配文件: 2 个

#### subjectCompetition
待适配文件: 4 个

#### teachingEvaluationGc
待适配文件: 27 个

#### teachingResources
待适配文件: 22 个

