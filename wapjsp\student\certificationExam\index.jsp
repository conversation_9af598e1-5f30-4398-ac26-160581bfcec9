<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>认证考试</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 认证考试页面样式 */
        .exam-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .exam-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .exam-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
        }
        
        .exam-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .exam-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .exam-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .exam-item:last-child {
            border-bottom: none;
        }
        
        .exam-item:active {
            background: var(--bg-color-active);
        }
        
        .exam-item.available {
            border-left: 4px solid var(--success-color);
        }
        
        .exam-item.registered {
            border-left: 4px solid var(--info-color);
        }
        
        .exam-item.completed {
            border-left: 4px solid var(--text-disabled);
        }
        
        .exam-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .exam-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-registered {
            background: var(--info-color);
            color: white;
        }
        
        .status-completed {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-closed {
            background: var(--error-color);
            color: white;
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .exam-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-description {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
        }
        
        .exam-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-register {
            background: var(--success-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .registration-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .registration-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel-form {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">认证考试</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="exam-header">
            <div class="header-title">认证考试</div>
            <div class="header-subtitle">报名参加各类认证考试</div>
        </div>
        
        <!-- 考试标签 -->
        <div class="exam-tabs">
            <div class="exam-tab active" data-tab="available" onclick="switchTab('available')">可报名</div>
            <div class="exam-tab" data-tab="registered" onclick="switchTab('registered')">已报名</div>
            <div class="exam-tab" data-tab="completed" onclick="switchTab('completed')">已完成</div>
        </div>
        
        <!-- 考试列表 -->
        <div class="exam-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-certificate"></i>
                    <span id="listTitle">可报名考试</span>
                </div>
                <div class="list-count" id="examCount">0</div>
            </div>
            
            <div id="examItems">
                <!-- 考试列表将动态填充 -->
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-certificate"></i>
            <div id="emptyMessage">暂无考试信息</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>
    
    <!-- 报名表单 -->
    <div class="registration-form" id="registrationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeRegistrationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">考试报名</div>
        </div>
        
        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人信息</span>
                </div>
                
                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" disabled>
                </div>
                
                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" disabled>
                </div>
                
                <div class="form-group">
                    <div class="form-label">身份证号</div>
                    <input type="text" class="form-input" id="idCard" placeholder="请输入身份证号">
                </div>
                
                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>
            </div>
            
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-certificate"></i>
                    <span>考试信息</span>
                </div>
                
                <div class="form-group">
                    <div class="form-label">考试名称</div>
                    <input type="text" class="form-input" id="examName" disabled>
                </div>
                
                <div class="form-group">
                    <div class="form-label">考试时间</div>
                    <input type="text" class="form-input" id="examTime" disabled>
                </div>
                
                <div class="form-group">
                    <div class="form-label">报名费用</div>
                    <input type="text" class="form-input" id="examFee" disabled>
                </div>
                
                <div class="form-group">
                    <div class="form-label">备注</div>
                    <textarea class="form-input" id="note" placeholder="其他需要说明的情况（选填）" style="min-height: 80px; resize: vertical;"></textarea>
                </div>
            </div>
        </div>
        
        <div class="form-actions">
            <button class="btn-mobile btn-cancel-form flex-1" onclick="closeRegistrationForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitRegistration();">确认报名</button>
        </div>
    </div>

    <script>
        // 全局变量
        let allExams = [];
        let currentTab = 'available';
        let currentExam = null;

        $(function() {
            initPage();
            loadExamData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载考试数据
        function loadExamData() {
            showLoading(true);
            
            $.ajax({
                url: "/student/certificationExam/getExamData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allExams = data.exams || [];
                    renderExamList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载考试数据失败');
                    showLoading(false);
                }
            });
        }

        // 切换标签
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签状态
            $('.exam-tab').removeClass('active');
            $(`.exam-tab[data-tab="${tab}"]`).addClass('active');
            
            // 更新列表标题
            const titles = {
                'available': '可报名考试',
                'registered': '已报名考试',
                'completed': '已完成考试'
            };
            $('#listTitle').text(titles[tab]);
            
            renderExamList();
        }

        // 渲染考试列表
        function renderExamList() {
            const filteredExams = allExams.filter(exam => {
                const status = getExamStatus(exam);
                return currentTab === 'available' ? status === 'available' :
                       currentTab === 'registered' ? status === 'registered' :
                       status === 'completed';
            });
            
            $('#examCount').text(filteredExams.length);
            
            const container = $('#examItems');
            container.empty();
            
            if (filteredExams.length === 0) {
                showEmptyState('暂无考试信息');
                return;
            } else {
                hideEmptyState();
            }
            
            filteredExams.forEach(exam => {
                const examHtml = createExamItem(exam);
                container.append(examHtml);
            });
        }

        // 创建考试项
        function createExamItem(exam) {
            const status = getExamStatus(exam);
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);
            
            return `
                <div class="exam-item ${status}" onclick="showExamDetail('${exam.id}')">
                    <div class="exam-basic">
                        <div class="exam-name">${exam.name}</div>
                        <div class="exam-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="exam-details">
                        <div class="exam-detail-item">
                            <span>考试时间:</span>
                            <span>${formatDate(exam.examTime)}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>报名截止:</span>
                            <span>${formatDate(exam.registrationDeadline)}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>报名费用:</span>
                            <span>¥${exam.fee}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>考试地点:</span>
                            <span>${exam.location || '待定'}</span>
                        </div>
                    </div>
                    ${exam.description ? `<div class="exam-description">${exam.description}</div>` : ''}
                    <div class="exam-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showExamDetail('${exam.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'available' ? `
                            <button class="btn-mobile btn-register" onclick="event.stopPropagation(); showRegistrationForm('${exam.id}');">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>报名</span>
                            </button>
                        ` : status === 'registered' ? `
                            <button class="btn-mobile btn-cancel" onclick="event.stopPropagation(); cancelRegistration('${exam.id}');">
                                <i class="ace-icon fa fa-times"></i>
                                <span>取消报名</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-check"></i>
                                <span>已完成</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取考试状态
        function getExamStatus(exam) {
            if (exam.isRegistered) {
                return exam.isCompleted ? 'completed' : 'registered';
            } else {
                const now = new Date();
                const deadline = new Date(exam.registrationDeadline);
                return deadline > now ? 'available' : 'closed';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'available': return '可报名';
                case 'registered': return '已报名';
                case 'completed': return '已完成';
                case 'closed': return '已截止';
                default: return '未知';
            }
        }

        // 显示考试详情
        function showExamDetail(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;
            
            let message = `考试详情\n\n`;
            message += `考试名称：${exam.name}\n`;
            message += `考试时间：${formatDate(exam.examTime)}\n`;
            message += `报名截止：${formatDate(exam.registrationDeadline)}\n`;
            message += `报名费用：¥${exam.fee}\n`;
            message += `考试地点：${exam.location || '待定'}\n`;
            
            if (exam.description) {
                message += `\n考试说明：${exam.description}\n`;
            }
            
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示报名表单
        function showRegistrationForm(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;
            
            currentExam = exam;
            
            // 填充表单数据
            $('#formTitle').text(`${exam.name} - 报名`);
            $('#studentId').val('2021001001'); // 模拟数据
            $('#studentName').val('张三'); // 模拟数据
            $('#examName').val(exam.name);
            $('#examTime').val(formatDate(exam.examTime));
            $('#examFee').val(`¥${exam.fee}`);
            
            // 清空可编辑字段
            $('#idCard').val('');
            $('#phone').val('');
            $('#note').val('');
            
            $('#registrationForm').addClass('show');
        }

        // 关闭报名表单
        function closeRegistrationForm() {
            $('#registrationForm').removeClass('show');
            currentExam = null;
        }

        // 提交报名
        function submitRegistration() {
            if (!currentExam) return;
            
            const formData = {
                examId: currentExam.id,
                idCard: $('#idCard').val().trim(),
                phone: $('#phone').val().trim(),
                note: $('#note').val().trim()
            };
            
            if (!validateForm(formData)) {
                return;
            }
            
            const message = `确定要报名"${currentExam.name}"吗？\n\n报名费用：¥${currentExam.fee}`;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitRegistration(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitRegistration(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.idCard) {
                showError('请输入身份证号');
                return false;
            }
            
            if (!formData.phone) {
                showError('请输入联系电话');
                return false;
            }
            
            // 验证身份证号格式
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idCardRegex.test(formData.idCard)) {
                showError('请输入正确的身份证号');
                return false;
            }
            
            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }
            
            return true;
        }

        // 执行提交报名
        function doSubmitRegistration(formData) {
            $.ajax({
                url: "/student/certificationExam/submitRegistration",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('报名成功');
                        closeRegistrationForm();
                        loadExamData(); // 重新加载数据
                    } else {
                        showError(data.message || '报名失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 取消报名
        function cancelRegistration(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;
            
            const message = `确定要取消报名"${exam.name}"吗？`;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doCancelRegistration(examId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doCancelRegistration(examId);
                }
            }
        }

        // 执行取消报名
        function doCancelRegistration(examId) {
            $.ajax({
                url: "/student/certificationExam/cancelRegistration",
                type: "post",
                data: { examId: examId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('取消报名成功');
                        loadExamData(); // 重新加载数据
                    } else {
                        showError(data.message || '取消报名失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadExamData();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
