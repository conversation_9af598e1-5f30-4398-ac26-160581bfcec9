<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>奖学金申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 奖学金申请页面样式 */
        .scholarship-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .type-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .type-item:last-child {
            border-bottom: none;
        }
        
        .type-item:active {
            background: var(--bg-color-active);
        }
        
        .type-item.disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .type-icon.academic {
            background: var(--success-color);
        }
        
        .type-icon.excellence {
            background: var(--warning-color);
        }
        
        .type-icon.innovation {
            background: var(--info-color);
        }
        
        .type-icon.hardship {
            background: var(--primary-color);
        }
        
        .type-content {
            flex: 1;
        }
        
        .type-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: 4px;
        }
        
        .type-amount {
            font-size: var(--font-size-small);
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .type-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-applied {
            background: var(--warning-color);
            color: white;
        }
        
        .status-closed {
            background: var(--text-disabled);
            color: white;
        }
        
        .application-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .form-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-lg);
            text-align: center;
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .eligibility-check {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-lg);
        }
        
        .check-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .check-title i {
            margin-right: var(--margin-xs);
            color: var(--info-color);
        }
        
        .check-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .check-item {
            display: flex;
            align-items: center;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .check-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: var(--font-size-mini);
        }
        
        .check-icon.pass {
            background: var(--success-color);
            color: white;
        }
        
        .check-icon.fail {
            background: var(--error-color);
            color: white;
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            display: block;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-control {
            width: 100%;
            min-height: 44px;
            padding: 12px 16px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color var(--transition-base);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-control.readonly {
            background: var(--bg-secondary);
            border-color: var(--border-secondary);
            color: var(--text-secondary);
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-help {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            margin-top: var(--margin-xs);
            line-height: var(--line-height-base);
        }
        
        .file-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .file-upload:hover {
            border-color: var(--primary-color);
            background: rgba(24, 144, 255, 0.05);
        }
        
        .upload-icon {
            font-size: 32px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
        }
        
        .upload-hint {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .file-list {
            margin-top: var(--margin-md);
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }
        
        .file-icon {
            color: var(--primary-color);
            margin-right: var(--margin-sm);
        }
        
        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
            padding: 4px;
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-back {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-primary);
        }
        
        .application-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .list-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .app-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .app-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .app-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .app-time {
            margin-top: var(--margin-xs);
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .notice-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--info-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            margin-right: var(--margin-xs);
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">奖学金申请</div>
            <div class="navbar-action" onclick="refreshApplications();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 申请通知 -->
        <div class="notice-section">
            <div class="notice-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>申请须知</span>
            </div>
            <div class="notice-content">
                奖学金申请需要满足相应条件，请仔细阅读申请要求。申请材料需真实有效，如有虚假将取消申请资格。申请截止时间请关注学校通知。
            </div>
        </div>

        <!-- 奖学金类型选择 -->
        <div class="scholarship-types" id="scholarshipTypes">
            <!-- 奖学金类型将通过JavaScript动态填充 -->
        </div>

        <!-- 申请表单 -->
        <div class="application-form" id="applicationForm">
            <div class="form-title" id="formTitle">奖学金申请</div>

            <!-- 资格检查 -->
            <div class="eligibility-check">
                <div class="check-title">
                    <i class="ace-icon fa fa-check-circle"></i>
                    <span>资格检查</span>
                </div>
                <div class="check-list" id="checkList">
                    <!-- 资格检查项将动态填充 -->
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="form-group">
                <label class="form-label">学生姓名</label>
                <input type="text" class="form-control readonly" id="studentName" readonly>
            </div>

            <div class="form-group">
                <label class="form-label">学号</label>
                <input type="text" class="form-control readonly" id="studentId" readonly>
            </div>

            <div class="form-group">
                <label class="form-label">专业班级</label>
                <input type="text" class="form-control readonly" id="majorClass" readonly>
            </div>

            <div class="form-group">
                <label class="form-label">联系电话</label>
                <input type="tel" class="form-control" id="contactPhone" placeholder="请输入联系电话">
            </div>

            <!-- 学业成绩 -->
            <div class="form-group">
                <label class="form-label">学业成绩</label>
                <input type="text" class="form-control readonly" id="gpa" readonly>
                <div class="form-help">系统自动获取，如有疑问请联系教务处</div>
            </div>

            <div class="form-group">
                <label class="form-label">专业排名</label>
                <input type="text" class="form-control readonly" id="ranking" readonly>
            </div>

            <!-- 申请理由 -->
            <div class="form-group">
                <label class="form-label required">申请理由</label>
                <textarea class="form-control form-textarea" id="applicationReason"
                          placeholder="请详细说明申请该奖学金的理由..."></textarea>
                <div class="form-help">请结合自身学习情况、家庭状况等详细说明</div>
            </div>

            <!-- 个人表现 -->
            <div class="form-group">
                <label class="form-label required">个人表现</label>
                <textarea class="form-control form-textarea" id="personalPerformance"
                          placeholder="请描述在学习、科研、社会实践等方面的表现..."></textarea>
                <div class="form-help">包括获奖情况、社会活动、志愿服务等</div>
            </div>

            <!-- 家庭情况 -->
            <div class="form-group" id="familyGroup" style="display: none;">
                <label class="form-label required">家庭经济情况</label>
                <textarea class="form-control form-textarea" id="familySituation"
                          placeholder="请详细说明家庭经济状况..."></textarea>
                <div class="form-help">如申请困难补助类奖学金，请详细说明家庭经济情况</div>
            </div>

            <!-- 附件上传 -->
            <div class="form-group">
                <label class="form-label">证明材料</label>
                <div class="file-upload" id="fileUpload" onclick="selectFiles()">
                    <div class="upload-icon">
                        <i class="ace-icon fa fa-cloud-upload"></i>
                    </div>
                    <div class="upload-text">点击上传或拖拽文件到此处</div>
                    <div class="upload-hint">支持PDF、JPG、PNG格式，单个文件不超过5MB</div>
                </div>
                <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                <div class="file-list" id="fileList"></div>
                <div class="form-help">可上传成绩单、获奖证书、家庭经济证明等材料</div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
                <button class="btn-mobile btn-back flex-1" onclick="hideApplicationForm();">返回</button>
                <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
                <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
            </div>
        </div>

        <!-- 申请记录 -->
        <div class="application-list">
            <div class="list-header">
                <i class="ace-icon fa fa-list"></i>
                <span>申请记录</span>
            </div>
            <div id="applicationListContent">
                <!-- 申请记录将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let scholarshipTypes = [];
        let currentScholarshipType = '';
        let uploadedFiles = [];
        let applications = [];
        let studentInfo = {};

        $(function() {
            initPage();
            loadScholarshipTypes();
            loadApplications();
            loadStudentInfo();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 文件拖拽事件
            const fileUpload = document.getElementById('fileUpload');

            fileUpload.addEventListener('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            fileUpload.addEventListener('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            fileUpload.addEventListener('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                handleFiles(e.dataTransfer.files);
            });

            // 文件选择事件
            $('#fileInput').on('change', function(e) {
                handleFiles(e.target.files);
            });
        }

        // 加载奖学金类型
        function loadScholarshipTypes() {
            showLoading(true);

            $.ajax({
                url: "/student/scholarship/application/getScholarshipTypes",
                type: "post",
                dataType: "json",
                success: function(data) {
                    scholarshipTypes = data || [];
                    renderScholarshipTypes();
                    showLoading(false);
                },
                error: function() {
                    showError('加载奖学金类型失败');
                    showLoading(false);
                }
            });
        }

        // 渲染奖学金类型
        function renderScholarshipTypes() {
            const container = $('#scholarshipTypes');
            container.empty();

            if (scholarshipTypes.length === 0) {
                container.html('<div style="padding: var(--padding-lg); text-align: center; color: var(--text-secondary);">暂无可申请的奖学金</div>');
                return;
            }

            scholarshipTypes.forEach(type => {
                const typeHtml = createScholarshipTypeItem(type);
                container.append(typeHtml);
            });
        }

        // 创建奖学金类型项
        function createScholarshipTypeItem(type) {
            const statusClass = getStatusClass(type.status);
            const statusText = getStatusText(type.status);
            const isDisabled = type.status === 'closed' || type.status === 'applied';

            return `
                <div class="type-item ${isDisabled ? 'disabled' : ''}" onclick="${isDisabled ? '' : `showApplicationForm('${type.id}')`}">
                    <div class="type-icon ${type.category}">
                        <i class="ace-icon fa ${getTypeIcon(type.category)}"></i>
                    </div>
                    <div class="type-content">
                        <div class="type-title">${type.name}</div>
                        <div class="type-desc">${type.description}</div>
                        <div class="type-amount">奖金金额：¥${type.amount}</div>
                    </div>
                    <div class="type-status ${statusClass}">${statusText}</div>
                </div>
            `;
        }

        // 获取类型图标
        function getTypeIcon(category) {
            const icons = {
                academic: 'fa-graduation-cap',
                excellence: 'fa-star',
                innovation: 'fa-lightbulb-o',
                hardship: 'fa-heart'
            };
            return icons[category] || 'fa-trophy';
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'available': return 'status-available';
                case 'applied': return 'status-applied';
                case 'closed': return 'status-closed';
                default: return 'status-available';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'available': return '可申请';
                case 'applied': return '已申请';
                case 'closed': return '已截止';
                default: return '未知';
            }
        }

        // 显示申请表单
        function showApplicationForm(typeId) {
            const type = scholarshipTypes.find(t => t.id === typeId);
            if (!type) return;

            currentScholarshipType = typeId;

            // 设置表单标题
            $('#formTitle').text(type.name + ' 申请');

            // 显示/隐藏家庭情况字段
            if (type.category === 'hardship') {
                $('#familyGroup').show();
            } else {
                $('#familyGroup').hide();
            }

            // 检查申请资格
            checkEligibility(type);

            // 填充学生信息
            populateStudentInfo();

            // 重置表单
            resetForm();

            // 显示表单，隐藏类型选择
            $('#scholarshipTypes').hide();
            $('#applicationForm').show();

            // 滚动到顶部
            $('html, body').animate({scrollTop: 0}, 300);
        }

        // 隐藏申请表单
        function hideApplicationForm() {
            $('#applicationForm').hide();
            $('#scholarshipTypes').show();
            currentScholarshipType = '';
            uploadedFiles = [];
        }

        // 检查申请资格
        function checkEligibility(type) {
            const container = $('#checkList');
            container.empty();

            // 根据奖学金类型检查不同条件
            const checks = getEligibilityChecks(type);

            checks.forEach(check => {
                const iconClass = check.passed ? 'pass' : 'fail';
                const iconSymbol = check.passed ? '✓' : '✗';

                const checkHtml = `
                    <div class="check-item">
                        <div class="check-icon ${iconClass}">${iconSymbol}</div>
                        <span>${check.description}</span>
                    </div>
                `;
                container.append(checkHtml);
            });
        }

        // 获取资格检查项
        function getEligibilityChecks(type) {
            const checks = [];

            // 基本检查项
            checks.push({
                description: '学籍状态正常',
                passed: studentInfo.status === 'normal'
            });

            // 根据奖学金类型添加特定检查
            switch(type.category) {
                case 'academic':
                    checks.push({
                        description: 'GPA ≥ 3.5',
                        passed: parseFloat(studentInfo.gpa) >= 3.5
                    });
                    checks.push({
                        description: '专业排名前20%',
                        passed: studentInfo.rankingPercent <= 20
                    });
                    break;
                case 'excellence':
                    checks.push({
                        description: 'GPA ≥ 3.0',
                        passed: parseFloat(studentInfo.gpa) >= 3.0
                    });
                    checks.push({
                        description: '无违纪记录',
                        passed: !studentInfo.hasDisciplinaryRecord
                    });
                    break;
                case 'innovation':
                    checks.push({
                        description: '有创新项目或专利',
                        passed: studentInfo.hasInnovationProject
                    });
                    break;
                case 'hardship':
                    checks.push({
                        description: '家庭经济困难认定',
                        passed: studentInfo.isFinanciallyNeedy
                    });
                    break;
            }

            return checks;
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/scholarship/application/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 填充学生信息
        function populateStudentInfo() {
            $('#studentName').val(studentInfo.name || '');
            $('#studentId').val(studentInfo.id || '');
            $('#majorClass').val(studentInfo.majorClass || '');
            $('#contactPhone').val(studentInfo.phone || '');
            $('#gpa').val(studentInfo.gpa || '');
            $('#ranking').val(studentInfo.ranking || '');
        }

        // 重置表单
        function resetForm() {
            $('#applicationReason').val('');
            $('#personalPerformance').val('');
            $('#familySituation').val('');
            $('#fileList').empty();
            uploadedFiles = [];
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                // 验证文件类型
                const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];

                if (!allowedTypes.includes(file.type)) {
                    showError(`文件 ${file.name} 格式不支持`);
                    return;
                }

                // 验证文件大小
                if (file.size > 5 * 1024 * 1024) {
                    showError(`文件 ${file.name} 大小超过5MB限制`);
                    return;
                }

                // 添加到文件列表
                uploadedFiles.push(file);
                addFileToList(file);
            });
        }

        // 添加文件到列表
        function addFileToList(file) {
            const fileId = 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const fileHtml = `
                <div class="file-item" id="${fileId}">
                    <i class="ace-icon fa fa-file file-icon"></i>
                    <span class="file-name">${file.name}</span>
                    <i class="ace-icon fa fa-times file-remove" onclick="removeFile('${fileId}', '${file.name}')"></i>
                </div>
            `;
            $('#fileList').append(fileHtml);
        }

        // 移除文件
        function removeFile(fileId, fileName) {
            $(`#${fileId}`).remove();
            uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
        }

        // 获取表单数据
        function getFormData() {
            return {
                scholarshipTypeId: currentScholarshipType,
                studentName: $('#studentName').val(),
                studentId: $('#studentId').val(),
                majorClass: $('#majorClass').val(),
                contactPhone: $('#contactPhone').val(),
                applicationReason: $('#applicationReason').val(),
                personalPerformance: $('#personalPerformance').val(),
                familySituation: $('#familySituation').val()
            };
        }

        // 验证表单
        function validateForm() {
            const errors = [];

            if (!$('#contactPhone').val().trim()) {
                errors.push('请填写联系电话');
            }

            if (!$('#applicationReason').val().trim()) {
                errors.push('请填写申请理由');
            }

            if (!$('#personalPerformance').val().trim()) {
                errors.push('请填写个人表现');
            }

            // 如果是困难补助类奖学金，需要填写家庭情况
            const type = scholarshipTypes.find(t => t.id === currentScholarshipType);
            if (type && type.category === 'hardship' && !$('#familySituation').val().trim()) {
                errors.push('请填写家庭经济情况');
            }

            if (errors.length > 0) {
                showError(errors.join('\n'));
                return false;
            }

            return true;
        }

        // 保存草稿
        function saveDraft() {
            const formData = getFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交申请
        function submitApplication() {
            if (!validateForm()) return;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要提交奖学金申请吗？提交后将无法修改。', function(confirmed) {
                    if (confirmed) {
                        doSubmitApplication();
                    }
                });
            } else {
                if (confirm('确定要提交奖学金申请吗？提交后将无法修改。')) {
                    doSubmitApplication();
                }
            }
        }

        // 执行提交申请
        function doSubmitApplication() {
            const formData = getFormData();
            formData.isDraft = false;

            submitFormData(formData, '申请提交成功');
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            const submitData = new FormData();

            // 添加表单数据
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            // 添加文件
            uploadedFiles.forEach((file, index) => {
                submitData.append(`attachment_${index}`, file);
            });

            $.ajax({
                url: "/student/scholarship/application/submitApplication",
                type: "post",
                data: submitData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);

                        if (!formData.isDraft) {
                            // 提交成功后返回列表
                            hideApplicationForm();
                            loadApplications();
                            loadScholarshipTypes(); // 重新加载类型状态
                        }
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 加载申请记录
        function loadApplications() {
            $.ajax({
                url: "/student/scholarship/application/getApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    applications = data || [];
                    renderApplications();
                },
                error: function() {
                    console.log('加载申请记录失败');
                }
            });
        }

        // 渲染申请记录
        function renderApplications() {
            const container = $('#applicationListContent');
            container.empty();

            if (applications.length === 0) {
                container.html('<div style="padding: var(--padding-lg); text-align: center; color: var(--text-secondary);">暂无申请记录</div>');
                return;
            }

            applications.forEach(app => {
                const appHtml = createApplicationItem(app);
                container.append(appHtml);
            });
        }

        // 创建申请记录项
        function createApplicationItem(app) {
            const statusClass = getAppStatusClass(app.status);
            const statusText = getAppStatusText(app.status);

            return `
                <div class="application-item" onclick="showApplicationDetail('${app.id}')">
                    <div class="app-header">
                        <div class="app-type">${app.scholarshipName}</div>
                        <div class="app-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="app-details">
                        申请金额：¥${app.amount} | 申请时间：${app.applicationTime}
                    </div>
                    ${app.reviewComment ? `<div class="app-details">审核意见：${app.reviewComment}</div>` : ''}
                    <div class="app-time">最后更新：${app.updateTime}</div>
                </div>
            `;
        }

        // 获取申请状态样式类
        function getAppStatusClass(status) {
            switch(status) {
                case 'pending': return 'status-pending';
                case 'approved': return 'status-approved';
                case 'rejected': return 'status-rejected';
                default: return 'status-pending';
            }
        }

        // 获取申请状态文本
        function getAppStatusText(status) {
            switch(status) {
                case 'pending': return '审核中';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 显示申请详情
        function showApplicationDetail(appId) {
            const app = applications.find(a => a.id === appId);
            if (!app) return;

            let message = `申请详情\n\n`;
            message += `奖学金类型：${app.scholarshipName}\n`;
            message += `申请金额：¥${app.amount}\n`;
            message += `申请状态：${getAppStatusText(app.status)}\n`;
            message += `申请时间：${app.applicationTime}\n`;

            if (app.reviewTime) {
                message += `审核时间：${app.reviewTime}\n`;
            }

            if (app.reviewComment) {
                message += `审核意见：${app.reviewComment}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 刷新申请记录
        function refreshApplications() {
            loadScholarshipTypes();
            loadApplications();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
