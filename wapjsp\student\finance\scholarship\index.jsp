<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>奖学金查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 奖学金查询页面样式 */
        .scholarship-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .scholarship-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .summary-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .summary-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .summary-number.total {
            color: var(--primary-color);
        }
        
        .summary-number.amount {
            color: var(--success-color);
        }
        
        .summary-number.pending {
            color: var(--warning-color);
        }
        
        .summary-number.rejected {
            color: var(--error-color);
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .year-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .selector-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .year-tabs {
            display: flex;
            gap: var(--spacing-xs);
            overflow-x: auto;
            padding-bottom: var(--padding-xs);
        }
        
        .year-tab {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            white-space: nowrap;
            transition: all var(--transition-base);
        }
        
        .year-tab:hover {
            background: var(--primary-light);
        }
        
        .year-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .scholarship-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .scholarship-list.show {
            display: block;
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .scholarship-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .scholarship-item:last-child {
            border-bottom: none;
        }
        
        .scholarship-item:active {
            background: var(--bg-color-active);
        }
        
        .scholarship-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .scholarship-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .scholarship-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .scholarship-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .scholarship-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .scholarship-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .scholarship-amount {
            background: var(--success-light);
            color: var(--success-color);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            text-align: center;
            font-weight: 600;
        }
        
        .scholarship-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .scholarship-description {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .scholarship-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-apply {
            background: var(--success-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .scholarship-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .scholarship-detail-modal.show {
            display: flex;
        }
        
        .scholarship-detail-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .scholarship-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .scholarship-detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .scholarship-detail-close {
            color: var(--text-secondary);
            cursor: pointer;
            font-size: var(--font-size-h4);
        }
        
        .scholarship-detail-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .detail-section {
            margin-bottom: var(--margin-md);
        }
        
        .detail-section:last-child {
            margin-bottom: 0;
        }
        
        .detail-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .available-scholarships {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .available-header {
            background: var(--info-color);
            color: white;
            padding: var(--padding-md);
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .available-header i {
            margin-right: var(--margin-xs);
        }
        
        .available-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .available-item:last-child {
            border-bottom: none;
        }
        
        .available-item:active {
            background: var(--bg-color-active);
        }
        
        .available-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .available-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .available-deadline {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
            background: var(--warning-color);
            color: white;
        }
        
        .available-amount {
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 6px;
            padding: var(--padding-xs) var(--padding-sm);
            font-weight: 600;
            text-align: center;
            margin-bottom: var(--margin-sm);
        }
        
        .available-requirements {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
        }
        
        .available-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">奖学金查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="scholarship-header">
            <div class="header-title">奖学金查询</div>
            <div class="header-subtitle">查看奖学金获得情况和申请机会</div>
        </div>

        <!-- 奖学金汇总 -->
        <div class="scholarship-summary">
            <div class="summary-title">
                <i class="ace-icon fa fa-trophy"></i>
                <span>奖学金汇总</span>
            </div>

            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-number total" id="totalCount">0</div>
                    <div class="summary-label">获得次数</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number amount" id="totalAmount">¥0</div>
                    <div class="summary-label">总金额</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number pending" id="pendingCount">0</div>
                    <div class="summary-label">申请中</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number rejected" id="rejectedCount">0</div>
                    <div class="summary-label">未通过</div>
                </div>
            </div>
        </div>

        <!-- 学年选择 -->
        <div class="year-selector">
            <div class="selector-title">
                <i class="ace-icon fa fa-calendar"></i>
                <span>学年奖学金</span>
            </div>

            <div class="year-tabs" id="yearTabs">
                <!-- 学年标签将动态填充 -->
            </div>
        </div>

        <!-- 奖学金列表 -->
        <div class="scholarship-list" id="scholarshipList">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span id="yearTitle">奖学金记录</span>
                </div>
                <div class="list-count" id="yearCount">0项</div>
            </div>

            <div id="scholarshipItems">
                <!-- 奖学金列表将动态填充 -->
            </div>
        </div>

        <!-- 可申请奖学金 -->
        <div class="available-scholarships" id="availableScholarships">
            <div class="available-header">
                <i class="ace-icon fa fa-plus-circle"></i>
                <span>可申请奖学金</span>
            </div>

            <div id="availableItems">
                <!-- 可申请奖学金将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-trophy"></i>
            <div id="emptyMessage">暂无奖学金记录</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 奖学金详情模态框 -->
    <div class="scholarship-detail-modal" id="scholarshipDetailModal">
        <div class="scholarship-detail-content">
            <div class="scholarship-detail-header">
                <div class="scholarship-detail-title" id="scholarshipDetailTitle">奖学金详情</div>
                <div class="scholarship-detail-close" onclick="closeScholarshipDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="scholarship-detail-body" id="scholarshipDetailBody">
                <!-- 奖学金详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let scholarshipData = {};
        let yearsData = [];
        let availableScholarships = [];
        let currentYear = '';

        $(function() {
            initPage();
            loadScholarshipData();
            loadAvailableScholarships();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载奖学金数据
        function loadScholarshipData() {
            showLoading(true);

            $.ajax({
                url: "/student/finance/scholarship/getScholarshipData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    scholarshipData = data.scholarshipData || {};
                    yearsData = data.years || [];

                    updateScholarshipSummary();
                    renderYearTabs();

                    // 默认选择第一个学年
                    if (yearsData.length > 0) {
                        selectYear(yearsData[0].year);
                    }

                    showLoading(false);
                },
                error: function() {
                    showError('加载奖学金数据失败');
                    showLoading(false);
                }
            });
        }

        // 更新奖学金汇总
        function updateScholarshipSummary() {
            $('#totalCount').text(scholarshipData.totalCount || 0);
            $('#totalAmount').text('¥' + (scholarshipData.totalAmount || 0).toLocaleString());
            $('#pendingCount').text(scholarshipData.pendingCount || 0);
            $('#rejectedCount').text(scholarshipData.rejectedCount || 0);
        }

        // 渲染学年标签
        function renderYearTabs() {
            const container = $('#yearTabs');
            container.empty();

            yearsData.forEach((year, index) => {
                const isActive = index === 0 ? 'active' : '';
                const tabHtml = `
                    <div class="year-tab ${isActive}" data-year="${year.year}" onclick="selectYear('${year.year}')">
                        ${year.year}学年
                    </div>
                `;
                container.append(tabHtml);
            });
        }

        // 选择学年
        function selectYear(year) {
            currentYear = year;

            // 更新标签状态
            $('.year-tab').removeClass('active');
            $(`.year-tab[data-year="${year}"]`).addClass('active');

            // 加载学年奖学金
            loadYearScholarships(year);
        }

        // 加载学年奖学金
        function loadYearScholarships(year) {
            $.ajax({
                url: "/student/finance/scholarship/getYearScholarships",
                type: "post",
                data: { year: year },
                dataType: "json",
                success: function(data) {
                    const yearData = yearsData.find(y => y.year === year);
                    const scholarships = data.scholarships || [];

                    renderYearScholarships(yearData, scholarships);
                    showScholarshipList();
                },
                error: function() {
                    console.log('加载学年奖学金失败');
                    showEmptyState('加载学年奖学金失败');
                }
            });
        }

        // 渲染学年奖学金
        function renderYearScholarships(yearData, scholarships) {
            $('#yearTitle').text(`${yearData.year}学年 奖学金记录`);
            $('#yearCount').text(`${scholarships.length}项`);

            const container = $('#scholarshipItems');
            container.empty();

            if (scholarships.length === 0) {
                showEmptyState('该学年暂无奖学金记录');
                return;
            } else {
                hideEmptyState();
            }

            scholarships.forEach(scholarship => {
                const scholarshipHtml = createScholarshipItem(scholarship);
                container.append(scholarshipHtml);
            });
        }

        // 创建奖学金项
        function createScholarshipItem(scholarship) {
            const status = scholarship.status || 'pending';
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);

            return `
                <div class="scholarship-item ${status}" onclick="showScholarshipDetail('${scholarship.id}')">
                    <div class="scholarship-basic">
                        <div class="scholarship-name">${scholarship.name}</div>
                        <div class="scholarship-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="scholarship-amount">¥${scholarship.amount.toLocaleString()}</div>
                    <div class="scholarship-details">
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(scholarship.applyTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>奖学金类型:</span>
                            <span>${getScholarshipTypeText(scholarship.type)}</span>
                        </div>
                        <div class="detail-item">
                            <span>评定等级:</span>
                            <span>${scholarship.level || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span>发放状态:</span>
                            <span>${scholarship.paymentStatus || '未发放'}</span>
                        </div>
                    </div>
                    ${scholarship.description ? `<div class="scholarship-description">${scholarship.description}</div>` : ''}
                    <div class="scholarship-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showScholarshipDetail('${scholarship.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'pending' ? `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-clock-o"></i>
                                <span>审核中</span>
                            </button>
                        ` : status === 'approved' ? `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-check"></i>
                                <span>已获得</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-times"></i>
                                <span>未通过</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'approved': return '已获得';
                case 'pending': return '审核中';
                case 'rejected': return '未通过';
                default: return '未知';
            }
        }

        // 获取奖学金类型文本
        function getScholarshipTypeText(type) {
            switch(type) {
                case 'academic': return '学业奖学金';
                case 'merit': return '优秀学生奖学金';
                case 'need': return '助学金';
                case 'special': return '专项奖学金';
                case 'enterprise': return '企业奖学金';
                default: return '其他奖学金';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示奖学金详情
        function showScholarshipDetail(scholarshipId) {
            $.ajax({
                url: "/student/finance/scholarship/getScholarshipDetail",
                type: "post",
                data: { scholarshipId: scholarshipId },
                dataType: "json",
                success: function(data) {
                    const scholarship = data.scholarship;
                    if (scholarship) {
                        renderScholarshipDetail(scholarship);
                    }
                },
                error: function() {
                    showError('加载奖学金详情失败');
                }
            });
        }

        // 渲染奖学金详情
        function renderScholarshipDetail(scholarship) {
            let detailHtml = `
                <div class="detail-section">
                    <div class="detail-section-title">基本信息</div>
                    <div class="detail-item">
                        <span class="detail-label">奖学金名称:</span>
                        <span class="detail-value">${scholarship.name}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">奖学金类型:</span>
                        <span class="detail-value">${getScholarshipTypeText(scholarship.type)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">奖励金额:</span>
                        <span class="detail-value">¥${scholarship.amount.toLocaleString()}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">评定等级:</span>
                        <span class="detail-value">${scholarship.level || '-'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">申请状态:</span>
                        <span class="detail-value">${getStatusText(scholarship.status)}</span>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-section-title">时间信息</div>
                    <div class="detail-item">
                        <span class="detail-label">申请时间:</span>
                        <span class="detail-value">${formatDate(scholarship.applyTime)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">审核时间:</span>
                        <span class="detail-value">${formatDate(scholarship.reviewTime)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">发放时间:</span>
                        <span class="detail-value">${formatDate(scholarship.paymentTime)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">发放状态:</span>
                        <span class="detail-value">${scholarship.paymentStatus || '未发放'}</span>
                    </div>
                </div>
            `;

            if (scholarship.description) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">奖学金说明</div>
                        <div style="line-height: 1.6;">${scholarship.description}</div>
                    </div>
                `;
            }

            if (scholarship.requirements) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">申请条件</div>
                        <div style="line-height: 1.6;">${scholarship.requirements}</div>
                    </div>
                `;
            }

            if (scholarship.reviewComment) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">审核意见</div>
                        <div style="line-height: 1.6;">${scholarship.reviewComment}</div>
                    </div>
                `;
            }

            $('#scholarshipDetailTitle').text(`${scholarship.name} - 详情`);
            $('#scholarshipDetailBody').html(detailHtml);
            $('#scholarshipDetailModal').addClass('show');
        }

        // 关闭奖学金详情
        function closeScholarshipDetail() {
            $('#scholarshipDetailModal').removeClass('show');
        }

        // 加载可申请奖学金
        function loadAvailableScholarships() {
            $.ajax({
                url: "/student/finance/scholarship/getAvailableScholarships",
                type: "post",
                dataType: "json",
                success: function(data) {
                    availableScholarships = data.scholarships || [];
                    renderAvailableScholarships();
                },
                error: function() {
                    console.log('加载可申请奖学金失败');
                }
            });
        }

        // 渲染可申请奖学金
        function renderAvailableScholarships() {
            const container = $('#availableItems');
            container.empty();

            if (availableScholarships.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无可申请的奖学金
                    </div>
                `);
                return;
            }

            availableScholarships.forEach(scholarship => {
                const availableHtml = createAvailableItem(scholarship);
                container.append(availableHtml);
            });
        }

        // 创建可申请奖学金项
        function createAvailableItem(scholarship) {
            const deadline = new Date(scholarship.deadline);
            const now = new Date();
            const daysLeft = Math.ceil((deadline - now) / (1000 * 60 * 60 * 24));

            return `
                <div class="available-item" onclick="showAvailableDetail('${scholarship.id}')">
                    <div class="available-basic">
                        <div class="available-name">${scholarship.name}</div>
                        <div class="available-deadline">${daysLeft > 0 ? `${daysLeft}天后截止` : '已截止'}</div>
                    </div>
                    <div class="available-amount">¥${scholarship.amount.toLocaleString()}</div>
                    <div class="available-requirements">${scholarship.requirements || '详见申请条件'}</div>
                    <div class="available-actions">
                        <button class="btn-mobile btn-view flex-1" onclick="event.stopPropagation(); showAvailableDetail('${scholarship.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看详情</span>
                        </button>
                        ${daysLeft > 0 ? `
                            <button class="btn-mobile btn-apply flex-1" onclick="event.stopPropagation(); applyScholarship('${scholarship.id}');">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>申请</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled flex-1">
                                <i class="ace-icon fa fa-clock-o"></i>
                                <span>已截止</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 显示可申请奖学金详情
        function showAvailableDetail(scholarshipId) {
            const scholarship = availableScholarships.find(s => s.id === scholarshipId);
            if (!scholarship) return;

            let message = `${scholarship.name}\n\n`;
            message += `奖励金额：¥${scholarship.amount.toLocaleString()}\n`;
            message += `申请截止：${formatDate(scholarship.deadline)}\n`;
            message += `奖学金类型：${getScholarshipTypeText(scholarship.type)}\n\n`;

            if (scholarship.requirements) {
                message += `申请条件：\n${scholarship.requirements}\n\n`;
            }

            if (scholarship.description) {
                message += `详细说明：\n${scholarship.description}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 申请奖学金
        function applyScholarship(scholarshipId) {
            const scholarship = availableScholarships.find(s => s.id === scholarshipId);
            if (!scholarship) return;

            const message = `确定要申请"${scholarship.name}"吗？\n\n奖励金额：¥${scholarship.amount.toLocaleString()}\n申请截止：${formatDate(scholarship.deadline)}`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doApplyScholarship(scholarshipId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doApplyScholarship(scholarshipId);
                }
            }
        }

        // 执行申请奖学金
        function doApplyScholarship(scholarshipId) {
            $.ajax({
                url: "/student/finance/scholarship/applyScholarship",
                type: "post",
                data: { scholarshipId: scholarshipId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('奖学金申请提交成功');
                        loadScholarshipData(); // 重新加载数据
                        loadAvailableScholarships();
                    } else {
                        showError(data.message || '申请失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示奖学金列表
        function showScholarshipList() {
            $('#scholarshipList').addClass('show');
        }

        // 刷新数据
        function refreshData() {
            loadScholarshipData();
            loadAvailableScholarships();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('#scholarshipList').removeClass('show');
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('#scholarshipList').addClass('show');
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#scholarshipDetailModal').click(function(e) {
            if (e.target === this) {
                closeScholarshipDetail();
            }
        });
    </script>
</body>
</html>
