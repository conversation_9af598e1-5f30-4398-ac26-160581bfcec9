<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>体测成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 体测成绩页面样式 */
        .score-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .summary-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .year-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .year-tabs {
            display: flex;
            gap: var(--spacing-sm);
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .year-tab {
            padding: 8px 16px;
            border-radius: 20px;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .year-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .test-record {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .test-record.excellent {
            border-left-color: var(--success-color);
        }
        
        .test-record.good {
            border-left-color: var(--info-color);
        }
        
        .test-record.pass {
            border-left-color: var(--warning-color);
        }
        
        .test-record.fail {
            border-left-color: var(--error-color);
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
        }
        
        .record-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .record-score {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 600;
            color: white;
        }
        
        .score-excellent {
            background: var(--success-color);
        }
        
        .score-good {
            background: var(--info-color);
        }
        
        .score-pass {
            background: var(--warning-color);
        }
        
        .score-fail {
            background: var(--error-color);
        }
        
        .record-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .test-items {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
        }
        
        .items-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .item-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 8px;
            background: var(--bg-primary);
            border-radius: 4px;
            font-size: var(--font-size-mini);
        }
        
        .item-name {
            color: var(--text-secondary);
        }
        
        .item-score {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .item-score.excellent {
            color: var(--success-color);
        }
        
        .item-score.good {
            color: var(--info-color);
        }
        
        .item-score.pass {
            color: var(--warning-color);
        }
        
        .item-score.fail {
            color: var(--error-color);
        }
        
        .chart-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .chart-container {
            height: 200px;
            position: relative;
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
        }
        
        .chart-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-disabled);
            font-size: var(--font-size-small);
        }
        
        .standards-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .standards-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .standard-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .standard-item:last-child {
            border-bottom: none;
        }
        
        .standard-name {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .standard-range {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .tips-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tips-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .tips-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .tip-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .tip-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">体测成绩</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 成绩概览 -->
        <div class="score-summary">
            <div class="summary-title">体测成绩概览</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalScore">0</div>
                    <div class="stat-label">总分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="currentLevel">-</div>
                    <div class="stat-label">等级</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="testCount">0</div>
                    <div class="stat-label">测试次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="passRate">0%</div>
                    <div class="stat-label">合格率</div>
                </div>
            </div>
        </div>

        <!-- 年度选择 -->
        <div class="year-selector">
            <div class="selector-title">选择学年</div>
            <div class="year-tabs" id="yearTabs">
                <!-- 年度标签将动态填充 -->
            </div>
        </div>

        <!-- 体测记录列表 -->
        <div class="container-mobile">
            <div id="recordList">
                <!-- 体测记录将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-heartbeat"></i>
                <div id="emptyMessage">暂无体测记录</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>

        <!-- 成绩趋势图 -->
        <div class="chart-section">
            <div class="chart-title">成绩趋势</div>
            <div class="chart-container">
                <div class="chart-placeholder">
                    <span>暂无数据显示趋势图</span>
                </div>
            </div>
        </div>

        <!-- 评分标准 -->
        <div class="standards-section">
            <div class="standards-title">评分标准</div>
            <div class="standard-item">
                <div class="standard-name">优秀</div>
                <div class="standard-range">90-100分</div>
            </div>
            <div class="standard-item">
                <div class="standard-name">良好</div>
                <div class="standard-range">80-89分</div>
            </div>
            <div class="standard-item">
                <div class="standard-name">及格</div>
                <div class="standard-range">60-79分</div>
            </div>
            <div class="standard-item">
                <div class="standard-name">不及格</div>
                <div class="standard-range">0-59分</div>
            </div>
        </div>

        <!-- 温馨提示 -->
        <div class="tips-section">
            <div class="tips-title">
                <i class="ace-icon fa fa-lightbulb-o"></i>
                <span>温馨提示</span>
            </div>
            <div class="tip-item">
                体测成绩是学生综合素质评价的重要组成部分，请重视体育锻炼。
            </div>
            <div class="tip-item">
                如对成绩有疑问，请在测试后一周内向体育部门申请复查。
            </div>
            <div class="tip-item">
                建议平时加强体育锻炼，提高身体素质，为体测做好准备。
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allRecords = [];
        let filteredRecords = [];
        let currentYear = '';
        let availableYears = [];

        $(function() {
            initPage();
            loadAvailableYears();
            loadSummaryData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载可用年度
        function loadAvailableYears() {
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/physicalTest/getAvailableYears",
                type: "post",
                dataType: "json",
                success: function(data) {
                    availableYears = data.years || [];
                    renderYearTabs();

                    // 默认选择最新年度
                    if (availableYears.length > 0) {
                        selectYear(availableYears[0]);
                    }
                },
                error: function() {
                    console.log('加载年度列表失败');
                }
            });
        }

        // 渲染年度标签
        function renderYearTabs() {
            const container = $('#yearTabs');
            container.empty();

            availableYears.forEach(year => {
                const tabHtml = `<div class="year-tab" onclick="selectYear('${year}')">${year}</div>`;
                container.append(tabHtml);
            });
        }

        // 选择年度
        function selectYear(year) {
            currentYear = year;

            // 更新标签状态
            $('.year-tab').removeClass('active');
            $('.year-tab').each(function() {
                if ($(this).text() === year) {
                    $(this).addClass('active');
                }
            });

            // 加载该年度的记录
            loadRecordsByYear(year);
        }

        // 根据年度加载记录
        function loadRecordsByYear(year) {
            showLoading(true);

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/physicalTest/getRecordsByYear",
                type: "post",
                data: { year: year },
                dataType: "json",
                success: function(data) {
                    allRecords = data.records || [];
                    renderRecordList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载体测记录失败');
                    showLoading(false);
                }
            });
        }

        // 加载概览数据
        function loadSummaryData() {
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/physicalTest/getSummaryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    updateSummaryData(data);
                },
                error: function() {
                    console.log('加载概览数据失败');
                }
            });
        }

        // 更新概览数据
        function updateSummaryData(data) {
            if (!data) return;

            $('#totalScore').text(data.totalScore || 0);
            $('#currentLevel').text(data.currentLevel || '-');
            $('#testCount').text(data.testCount || 0);
            $('#passRate').text((data.passRate || 0) + '%');
        }

        // 渲染记录列表
        function renderRecordList() {
            const container = $('#recordList');
            container.empty();

            if (allRecords.length === 0) {
                showEmptyState('该学年暂无体测记录');
                return;
            } else {
                hideEmptyState();
            }

            allRecords.forEach(record => {
                const recordHtml = createRecordItem(record);
                container.append(recordHtml);
            });
        }

        // 创建记录项
        function createRecordItem(record) {
            const levelClass = getLevelClass(record.totalScore);
            const scoreClass = getScoreClass(record.totalScore);
            const levelText = getLevelText(record.totalScore);

            return `
                <div class="test-record ${levelClass}">
                    <div class="record-header">
                        <div class="record-title">${record.testName || '体质健康测试'}</div>
                        <div class="record-score ${scoreClass}">${record.totalScore}分 ${levelText}</div>
                    </div>
                    <div class="record-info">
                        <div class="info-item">
                            <span>测试时间:</span>
                            <span>${formatDate(record.testDate)}</span>
                        </div>
                        <div class="info-item">
                            <span>测试地点:</span>
                            <span>${record.testLocation || '体育馆'}</span>
                        </div>
                        <div class="info-item">
                            <span>身高体重:</span>
                            <span>${record.height}cm/${record.weight}kg</span>
                        </div>
                        <div class="info-item">
                            <span>BMI指数:</span>
                            <span>${record.bmi || calculateBMI(record.height, record.weight)}</span>
                        </div>
                    </div>
                    <div class="test-items">
                        <div class="items-title">各项成绩</div>
                        <div class="item-list">
                            ${createTestItems(record.items)}
                        </div>
                    </div>
                </div>
            `;
        }

        // 创建测试项目
        function createTestItems(items) {
            if (!items || items.length === 0) {
                return '<div class="test-item"><span class="item-name">暂无详细数据</span><span class="item-score">-</span></div>';
            }

            return items.map(item => {
                const scoreClass = getScoreClass(item.score);
                return `
                    <div class="test-item">
                        <span class="item-name">${item.name}</span>
                        <span class="item-score ${scoreClass}">${item.result}</span>
                    </div>
                `;
            }).join('');
        }

        // 获取等级样式类
        function getLevelClass(score) {
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 60) return 'pass';
            return 'fail';
        }

        // 获取分数样式类
        function getScoreClass(score) {
            if (score >= 90) return 'score-excellent';
            if (score >= 80) return 'score-good';
            if (score >= 60) return 'score-pass';
            return 'score-fail';
        }

        // 获取等级文本
        function getLevelText(score) {
            if (score >= 90) return '优秀';
            if (score >= 80) return '良好';
            if (score >= 60) return '及格';
            return '不及格';
        }

        // 计算BMI
        function calculateBMI(height, weight) {
            if (!height || !weight) return '-';
            const heightM = height / 100;
            const bmi = weight / (heightM * heightM);
            return bmi.toFixed(1);
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadAvailableYears();
            loadSummaryData();
            if (currentYear) {
                loadRecordsByYear(currentYear);
            }
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#recordList').hide();
            } else {
                $('#loadingState').hide();
                $('#recordList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
