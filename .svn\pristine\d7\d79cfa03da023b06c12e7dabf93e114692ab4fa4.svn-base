<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
	<head>
		<style type="text/css">
			.table-bordered, td, th {
			    border-radius: 0 !important;
			}
			
			.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
			    border: 1px solid #ddd;
			}
			.table {
			    width: 100%;
			    max-width: 100%;
			    margin-bottom: 20px;
			}
			pre code, table {
			    background-color: transparent;
			}
			table {
			    border-collapse: collapse;
			    border-spacing: 0;
			}
			.table>thead>tr {
			    color: #707070;
			    font-weight: 400;
			    background: repeat-x #F2F2F2;
			    background-image: -webkit-linear-gradient(top, #F8F8F8 0, #ECECEC 100%);
			    background-image: -o-linear-gradient(top,#F8F8F8 0,#ECECEC 100%);
			    background-image: linear-gradient(to bottom, #F8F8F8 0, #ECECEC 100%);
			    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff8f8f8', endColorstr='#ffececec', GradientType=0);
			}
			.table.table-bordered>thead>tr>th:first-child {
			    border-left-color: #ddd;
			}
			
			.table>caption+thead>tr:first-child>td, .table>caption+thead>tr:first-child>th, .table>colgroup+thead>tr:first-child>td, .table>colgroup+thead>tr:first-child>th, .table>thead:first-child>tr:first-child>td, .table>thead:first-child>tr:first-child>th {
			    border-top: 0;
			}
			.table.table-bordered>thead>tr>th {
			    vertical-align: middle;
			}
			.table>thead>tr>th:first-child {
			    border-left-color: #F1F1F1;
			}
			.table>thead>tr>th {
			    border-color: #ddd;
			    font-weight: 700;
			}
			.table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
			    border-bottom-width: 2px;
			}
			.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
			    border: 1px solid #ddd;
			}
			.table>thead>tr>th {
			    vertical-align: bottom;
			    border-bottom: 2px solid #ddd;
			}
			.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
			    padding: 8px;
			    line-height: 1.42857143;
			    vertical-align: top;
			    border-top: 1px solid #ddd;
			}
			.table-bordered, td, th {
			    border-radius: 0 !important;
			}
			caption, th {
			    text-align: left;
			}
			.table-striped>tbody>tr:nth-of-type(odd) {
			    background-color: #f9f9f9;
			}
			.badge {
			    text-shadow: none;
			    font-size: 12px;
			    padding-top: 1px;
			    padding-bottom: 3px;
			    font-weight: 400;
			    line-height: 15px;
			    background-color: #abbac3 !important;
			}
			
			.no-border {
			    border-width: 0;
			}
			.badge {
			    display: inline-block;
			    min-width: 10px;
			    padding: 3px 7px;
			    font-size: 12px;
			    font-weight: 700;
			    color: #fff;
			    line-height: 1;
			    vertical-align: baseline;
			    white-space: nowrap;
			    text-align: center;
			    background-color: #777;
			    border-radius: 10px;
			}
			
			.badge-success, .label-success {
			    background-color: #82af6f !important;
			}
			.badge-purple, .label-purple {
			    background-color: #9585bf !important;
			}
			
			
			.dropdown {
			    position: relative;
			}
			.open>.dropdown-menu {
			    display: block;
			}
			
			.dropdown-self {
			    float: none;
			    min-width: 400px;
			    padding: 5px;
			    font-size: 12px;
			    border: 1px solid orange;
			    box-shadow: 5px 5px 5px rgba(255, 165, 0, .5);
			    -webkit-box-shadow: 5px 5px 5px rgba(255, 165, 0, .5);
			    border-radius: 5px !important;
			}
			.dropdown-menu {
			    border-radius: 0 !important;
			    -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
			    box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
			}
			.dropdown-menu {
			    position: absolute;
			    top: 100%;
			    left: 0;
			    z-index: 1000;
			    display: none;
			    float: left;
			    min-width: 160px;
			    padding: 5px 0;
			    margin: 2px 0 0;
			    list-style: none;
			    font-size: 14px;
			    text-align: left;
			    background-color: #fff;
			    border: 1px solid #ccc;
			    border: 1px solid rgba(0, 0, 0, .15);
			    border-radius: 4px;
			    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
			    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
			    background-clip: padding-box;
			}
			.dropdown-menu.dropdown-caret:after {
			    border-bottom: 6px solid #FFF;
			    -moz-border-bottom-colors: #FFF;
			    border-left: 6px solid transparent;
			    border-right: 6px solid transparent;
			    content: "";
			    display: inline-block;
			    left: 10px;
			    position: absolute;
			    top: -6px;
			}
			.btn.btn-round {
			    border-radius: 4px !important;
			}
			
			.btn.btn-bold, .btn.btn-round {
			    border-bottom-width: 2px;
			}
			
			.btn-group-xs>.btn, .btn-xs {
			    padding-top: 3px;
			    padding-bottom: 3px;
			    border-width: 3px;
			}
			.btn {
			    color: #FFF !important;
			    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
			    background-image: none !important;
			    border: 5px solid #FFF;
			    border-radius: 0;
			    box-shadow: none !important;
			    -webkit-transition: background-color .15s, border-color .15s, opacity .15s;
			    -o-transition: background-color .15s,border-color .15s,opacity .15s;
			    transition: background-color .15s, border-color .15s, opacity .15s;
			    vertical-align: middle;
			    margin: 0;
			    position: relative;
			    font-weight: 400;
			}
			.breadcrumb, .breadcrumb>li>a, .btn {
			    display: inline-block;
			}
			.btn, .dropdown-colorpicker a {
			    cursor: pointer;
			}
			.btn-group-xs>.btn, .btn-xs {
			    padding: 1px 5px;
			    font-size: 12px;
			    line-height: 1.3;
			    border-radius: 3px;
			}
			.btn, .btn-danger.active, .btn-danger:active, .btn-default.active, .btn-default:active, .btn-info.active, .btn-info:active, .btn-purple.active, .btn-purple:active, .btn-warning.active, .btn-warning:active, .btn.active, .btn:active, .dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover, .form-control, .navbar-toggle, .open>.dropdown-toggle.btn-danger, .open>.dropdown-toggle.btn-default, .open>.dropdown-toggle.btn-info, .open>.dropdown-toggle.btn-purple, .open>.dropdown-toggle.btn-warning {
			    background-image: none;
			}
			button, input, select, textarea {
			    font-family: inherit;
			    font-size: inherit;
			    line-height: inherit;
			}
			button, html input[type=button], input[type=reset], input[type=submit] {
			    -webkit-appearance: button;
			    cursor: pointer;
			}
			button, select {
			    text-transform: none;
			}
			button {
			    overflow: visible;
			}
			button, input, optgroup, select, textarea {
			    color: inherit;
			    font: inherit;
			    margin: 0;
			}
			.btn, .btn-default, .btn-default.focus, .btn-default:focus, .btn.focus, .btn:focus {
			    background-color: #ABBAC3 !important;
			    border-color: #ABBAC3;
			}
			.btn-success, .btn-success.focus, .btn-success:focus {
			    background-color: #87B87F !important;
			    border-color: #87B87F;
			}
			.btn-purple, .btn-purple:focus {
			    background-color: #9585bf !important;
    			border-color: #9585bf;
			}
			.btn-danger, .btn-danger:focus {
			    background-color: #d15b47 !important;
			    border-color: #d15b47;
			}
			
			.btn-default.disabled,.btn-default.disabled.active,.btn-default.disabled:active,.btn-default.disabled:focus,.btn-default.disabled:hover,.btn-default[disabled],.btn-default[disabled].active,.btn-default[disabled]:active,.btn-default[disabled]:focus,.btn-default[disabled]:hover,.btn.disabled,.btn.disabled.active,.btn.disabled:active,.btn.disabled:focus,.btn.disabled:hover,.btn[disabled],.btn[disabled].active,.btn[disabled]:active,.btn[disabled]:focus,.btn[disabled]:hover,fieldset[disabled] .btn,fieldset[disabled] .btn-default,fieldset[disabled] .btn-default.active,fieldset[disabled] .btn-default:active,fieldset[disabled] .btn-default:focus,fieldset[disabled] .btn-default:hover,fieldset[disabled] .btn.active,fieldset[disabled] .btn:active,fieldset[disabled] .btn:focus,fieldset[disabled] .btn:hover {
			    background-color: #abbac3!important;
			    border-color: #abbac3
			}
			.btn-success.disabled, .btn-success.disabled.active, .btn-success.disabled:active, .btn-success.disabled:focus, .btn-success.disabled:hover, .btn-success[disabled], .btn-success[disabled].active, .btn-success[disabled]:active, .btn-success[disabled]:focus, .btn-success[disabled]:hover, fieldset[disabled] .btn-success, fieldset[disabled] .btn-success.active, fieldset[disabled] .btn-success:active, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success:hover {
			    background-color: #87b87f !important;
			    border-color: #87b87f;
			}
			.btn-purple.disabled, .btn-purple.disabled.active, .btn-purple.disabled:active, .btn-purple.disabled:focus, .btn-purple.disabled:hover, .btn-purple[disabled], .btn-purple[disabled].active, .btn-purple[disabled]:active, .btn-purple[disabled]:focus, .btn-purple[disabled]:hover, fieldset[disabled] .btn-purple, fieldset[disabled] .btn-purple.active, fieldset[disabled] .btn-purple:active, fieldset[disabled] .btn-purple:focus, fieldset[disabled] .btn-purple:hover {
			    background-color: #9585bf !important;
			    border-color: #9585bf;
			}
			.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
			    cursor: not-allowed;
			    pointer-events: none;
			    opacity: .65;
			    filter: alpha(opacity = 65);
			    -webkit-box-shadow: none;
			    box-shadow: none;
			}
		</style>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<script src="/js/jQuery/jquery-3.4.1.min.js"></script>
		<script type="text/javascript" src="/assets/layer/layer.js"></script>
		<script type="text/javascript" src="/js/customjs/coursetable.js"></script>
		<script type="text/javascript" src="/js/json/json2.js"></script>
		<script type="text/javascript">
			var jdyxct = $(parent.document).find("#checkCt").val();
		
			var yxkc = "";
			var kckxh = new Array();
			var kcMap = "";	
			var noTimeKc = "";
			var kylMap = "";
			
			function queryData() {
				$("#mycoursetable").html("");
	            $("#tb_inten_sel").html("");
	            coursetable.init('mycoursetable','');
	            
				var index;
				$.ajax({
					url: "/student/courseSelect/intentCourse/courseList",
					method: "post",
					data: {
	                    fajhh: $("#fajhh").val(),
	                    mxbj: $("#filterClassCourses").length > 0 && $("#filterClassCourses").is(":checked") ? "1" : "0"
	                },
					dataType: "json",
					success: function(data){
						kcMap = data['hasTime'];
						kylMap = data["kylMap"];
						noTimeKc = data['noTime'];
						fillCourseToTableCell();
						yxkc = JSON.parse(data['yxkclist']);
						fillNoTimeList(noTimeKc);
						setYxkcStatus();
						setHoverHeader();
					},
					error: function(){
						urp.alert("选课列表查询失败！");
					},
	                beforeSend: function () {
	                    index = layer.load(0, {
	                        shade: [0.2, "#000"]
	                        //0.1透明度的白色背景
	                    });
	                },
	                complete: function () {
	                    layer.close(index);
	                }
				});
			}
			
			$(function(){
				queryData();
			});
			
			function setHoverHeader() {
				var theadcont = $("#mycoursetable").find("thead");
				$("#mycoursetableHead").html($(theadcont).html());
				$(theadcont).find("th").each(function(i, v) {
					$("#mycoursetableHead").find("th:eq("+i+")")
						.width($(this).width());
				});
				$("#div_kc_tj").scroll(function() {
					var st = $(this).scrollTop();
					//$("#div1").scrollLeft($(this).scrollLeft());
					if (st > 0) {
						$("#mycoursetableHeadDiv").show();
					}
					if (st == 0) {
						$("#mycoursetableHeadDiv").hide();
					}
				});
			}
			
			function checkRepeat(it,arr){
				var xqarr = arr.split(",");
				var flag = true;
				for(var i=0;i<xqarr.length;i++){
					if(it==xqarr[i]){
						flag = false;
						break;
					}
				}
				return flag;
			}
			
			function checksfczfskt(kch, kxh) {
				var flag = false;
				$.map(kcMap, function(t,i){
					var ids = i.split("_");
					if (kch == ids[0]) {
						$.each(t,function(j,v){
							if (v.zkxh == kxh) {
								flag = true;
							}
						});
					}
				});
 				if (!flag) {
 					$(noTimeKc).each(function(i, v) {
 						if (v.kch == kch && v.zkxh == kxh) {
 							flag = true;
 							return false;
 						}
 					});
 				}
				return flag;
			}
			
			
			function queryZjJsJl(id, obj) {
				if (id != undefined && $(obj).attr("title") == undefined) {
					$(obj).css("cursor", "wait");
					$.ajax({
						url: "/student/courseSelect/queryTeacherJL",
						method: "post",
						data: {id: id },
						dataType: "json",
						success: function(d) {
							var cont = "负责教师：" + d[0][0] + "\r\n";
							$(obj).attr("title", cont + (d[0][1]==null? "": d[0][1]));
						},
						error: function() {
						},
						complete : function(){
							$(obj).css("cursor", "pointer");
						}
					});
				}
			}
			
			function leaveJs(obj) {
				$(obj).css("cursor", "pointer");
			}
				
			function fillCourseToTableCell(){
				if(kcMap != null){
					$.map(kcMap, function(t,i){
						var skxq = "";
						$.each(t,function(j,v){
							var kch = v.kch;
							var kxh = v.kxh;
							var xq = v.skxq;
							var jc = v.skjc;
							var bkskyl = ${!fromRedis} ? v.bkskyl : kylMap[v.zxjxjhh+"_"+v.kch+"_"+v.kxh];
							if(checkRepeat(xq,skxq)){
								skxq += xq + ",";
							}
							
							
							//创建课程下拉框架
							var cellid = xq+"_"+jc;
							var cell = $("#"+cellid);
							if(cell.html()==null || cell.html()=="" || $("#tbody_"+kch+"_"+xq+"_"+jc).attr("id")==undefined){
								var tc = "<div class='dropdown'>"
										+"	<button title='"+v.kcm+"' type='button' id='btnmenu_"+kch+"_"+xq+"_"+jc+"' onclick='if($(this).parent().hasClass(\"open\")) {$(this).parent().removeClass(\"open\")} else {$(\"div.dropdown.open\").removeClass(\"open\"); $(this).parent().addClass(\"open\")}' \
										data-toggle='dropdown' class='badge badge-purple no-border' data-position='down' name='btn-dropdown-p' \
										style='white-space:normal;word-break:break-all;max-width:160px; cursor: pointer;'>"+v.kcm+"</button>"
										+"	<div class='dropdown-menu dropdown-caret dropdown-self'>"
										+"		<table class='table table-striped table-bordered' style='margin-bottom:0px;' id='ktTable'>"
										+"			<thead><tr><td>课序号</td><td>附属课堂</td><td>教师</td><td>学分</td><td>课余量/已选人数</td>\
										<td>上课周次</td><td>上课星期</td><td>允许跨校区选课</td><td>操作</td></tr></thead>"
										+"			<tbody id='tbody_"+kch+"_"+xq+"_"+jc+"'></tbody></table></div></div>";
								$(cell).append(tc);
							}
							
							//拼接课堂信息到课程框架
							var czfskt = v.zkxh == "" && checksfczfskt(kch, kxh);//v.sfczfskt=="1" && checksfczfskt(kch);
							var fsktms = czfskt ? "主课堂" : v.fsktms == "" ? "无" : v.fsktms;
							var tcc = "<tr><td>"+v.kxh + (v.zkxh ? "_<"+ v.zkxh +">" : "") +"</td><td>"+fsktms+"</td><td onmouseenter='queryZjJsJl(\""+v.zxjxjhh+"_"+v.kch+"_"+v.kxh+"\", this);'\
								 onmouseleave='leaveJs(this);' style='color:#579EC8;cursor:pointer;'>"+v.skjs+"</td><td>"+v.xf+"</td><td>"+bkskyl+"/<a title='点击查看' href='javascript:void(0);' \
								onclick='viewXkCount(\""+v.zxjxjhh+"\",\""+v.kch+"\",\""+v.kxh+"\", this);'>点击查看</a></td>\
									<td>"+v.zcsm+"</td><td name='insertxq_"+i+"'></td><td>"+(v.yxkxqxk=="1" ? "是" : "否")+"</td>"
									+ "<td><button czfskt='"+czfskt+"' id='btnadd_"+kch+"_"+kxh+"_"+xq+"_"+jc+"' zkch='"+ v.zkch +"' class='btn btn-xs btn-round btn btn-purple no-border \
									"+(czfskt ? "disabled" : "")+" ' "+(czfskt ? "title='选择附属课堂会连带选择主课堂'" : "")+" type='button' name='btn-intention-add' value='"+JSON.stringify(v)+"'>"
									+ "<i class='ace-icon fa fa-plus bigger-120'></i>十</button></td></tr>";
							$(cell).find("#tbody_"+kch+"_"+xq+"_"+jc).append(tcc);
						});
						$("td[name=insertxq_"+i+"]").html(skxq.substr(0,skxq.length-1));
					});
				}

				//禁止冒泡
				$('.dropdown-menu').click(function(e) {
				    e.stopPropagation();
				});
				
				
				//点击课程显示下拉
				$("button[name=btn-dropdown-p]").bind("click",function(){
					var btndiv = $(this).parent();
					var tabdiv = $(btndiv).find("div")[0];
					var btndivw = btndiv.width();
					var btndivwl = btndiv.offset().left;
					var contdiv = $("#div_kc_tj");
					var contdivwl = contdiv.offset().left+contdiv.width();
					if((contdivwl-btndivwl)<$(tabdiv).width() && !$(tabdiv).hasClass("dropdown-menu-right")){
						$(tabdiv).addClass("dropdown-menu-right");
						$(tabdiv).css("right",btndivw-30);
					}
				});
				
				//点击意向课程
				$("button[name=btn-intention-add]").bind("click", function() {
					var obj = eval("("+$(this).val()+")");

					//该课堂的所有上课时间选中
					var xtkc = $("button[id^=btnadd_"+obj.kch+"_"+obj.kxh+"_]");
					$(xtkc).removeClass("btn-purple").addClass("btn-success");
					$(xtkc).html("<i class='ace-icon fa fa-check-square-o bigger-120'></i> ✔");
					$(xtkc).prop("disabled",true);
					$(xtkc).parents(".dropdown").find("button[id^=btnmenu_]").removeClass("badge-purple").addClass("badge-success");
					
					//选附属课程主课堂选中
					if (obj.zkxh != "") {
						xtkc = $("button[id^=btnadd_"+obj.kch+"_"+obj.zkxh+"_]");
						if (xtkc.length > 0) {
							$(xtkc).removeClass("btn-purple").addClass("btn-success");
							$(xtkc).html("<i class='ace-icon fa fa-check-square-o bigger-120'></i> ✔");
							$(xtkc).prop("disabled",true);
							$(xtkc).parents(".dropdown").find("button[id^=btnmenu_]").removeClass("badge-purple").addClass("badge-success");
						} else {
							var selZkt = $("#notimecourse").find("input[id=selectadd_"+obj.kch+"_"+obj.zkxh+"]");
							var selAll = $("#notimecourse").find("input[id^=selectadd_"+obj.kch+"_]");
							$(selZkt).prop("checked",true);
							$(selAll).not(selZkt).attr("disabled", true);
						}
					}
					
					//主课程其他子课程不可选
					var zkch = $(this).attr("zkch");
					if(zkch != "" && obj.xmcjhc != "1") {
						var qtxm = $("button[id^=btnadd_][zkch='"+ zkch +"']").filter(".btn-purple");
						$(qtxm).removeClass("btn-purple").addClass("btn-default");
						$(qtxm).html("<i class='ace-icon fa fa-times bigger-120'></i> ✕");
						$(qtxm).prop("disabled",true);
						$("#notimecourse").find("input[id^=selectadd_][zkch='"+ zkch +"']").not(":disabled").attr("disabled", true);
					}
					
					var sfczfskt = $(this).attr("czfskt");
					
					//其他时间冲突课程
					var zcxkpdctf = obj.zcxkpdctf;
					if(zcxkpdctf != null && zcxkpdctf == "1") {
						otherkt(obj, sfczfskt);
					}
					
					//添加课堂到意向列表中
					var czfsktval = sfczfskt == "true" ? "1":"0";
					addToAttention(obj, czfsktval, "b");
				});
			}
			
			function otherkt(obj, czfskt) {
				//所有未设置状态的添加button
				var allBtnAdd = $("button[id^=btnadd_]").filter(".btn-purple");
				$(allBtnAdd).each(function(i, v) {
					var objTemp = eval("("+$(this).val()+")");
					var zcxkpdctf = objTemp.zcxkpdctf;
					if ($(this).hasClass("btn-purple") && zcxkpdctf != null && zcxkpdctf == "1") {
						var sfczfskt = $(this).attr("czfskt");
						if(checkTimeConflict(objTemp, obj, czfskt)) {
							//设置冲突状态
							var ctkc = $("button[id^=btnadd_"+objTemp.kch+"_"+objTemp.kxh+"_]");
							$(ctkc).removeClass("btn-purple").addClass("btn-default");
							$(ctkc).html("<i class='ace-icon fa fa-times bigger-120'></i> ✕");
							$(ctkc).prop("disabled",true);
							
							//设置附属课堂冲突状态
							if (sfczfskt == "true") {
								$("button[id^=btnadd_"+objTemp.kch+"_]").filter(".btn-purple").each(function (ii, vv) {
									var temp = eval("("+$(this).val()+")");
									if (temp.zkxh == objTemp.kxh) {
										ctkc = $("button[id^=btnadd_"+temp.kch+"_"+temp.kxh+"_]");
										$(ctkc).removeClass("btn-purple").addClass("btn-default");
										$(ctkc).html("<i class='ace-icon fa fa-times bigger-120'></i> ✕");
										$(ctkc).prop("disabled",true);
									}
								});
								$("#notimecourse").find("input[id^=selectadd_"+objTemp.kch+"_]").each(function(ii, vv) {
									$(this).prop("disabled",true);
								});
							} else {
								//设置主课堂冲突状态
								if (objTemp.zkxh != "") {
									var hasPrimary = false;
									$("button[id^=btnadd_"+objTemp.kch+"_]").filter(".btn-purple").each(function (ii, vv) {
										var temp = eval("("+$(this).val()+")");
										if (temp.zkxh == objTemp.zkxh && $(this).hasClass("btn-purple")) {
											hasPrimary = true;
											return false;
										}
									});
									$("#notimecourse").find("input[id^=selectadd_"+objTemp.kch+"_]").each(function(ii, vv) {
										var temp = eval("("+$(this).val()+")");
										if (temp.zkxh == objTemp.zkxh && !$(this).prop("disabled")) {
											hasPrimary = true;
											return false;
										}
									});
									if (!hasPrimary) {
										ctkc = $("button[id^=btnadd_"+objTemp.kch+"_"+objTemp.zkxh+"_]");
										$(ctkc).removeClass("btn-purple").addClass("btn-default");
										$(ctkc).html("<i class='ace-icon fa fa-times bigger-120'></i> ✕");
										$(ctkc).prop("disabled",true);
									}
								}
							}
						}
					}
				});
				//重置课程课序数组
				kckxh = new Array();
				
				//设置所有课程状态
				var allBtnMenu = $("button[id^=btnmenu_]").filter(".badge-purple");
				$(allBtnMenu).each(function(i,v){
					var twxAdd = $(this).parent().find(".btn-purple");
					if(twxAdd.length==0) {
						$(this).removeClass("badge-purple").addClass("badge-default");
					}
				});
			}
			
			//时间冲突课程校验（当前课堂+主课堂， 循环对象， 当前课堂，是否存在附属课堂）
			function checkTimeConflict(objTemp, obj, sfczfskt) {
				var ff = false;
				var f1 = true;
				var kch = objTemp.kch;
				var kxh = objTemp.kxh;
				var fsyx = false;
				
				var txzkc = "";
				var ktData = kcMap[obj.kch+"_"+obj.kxh];
				if (ktData != undefined) {
					txzkc = ktData;
				}
				
				if (obj.zkxh != "") {
					var zkc = kcMap[obj.kch+"_"+obj.zkxh];
					if (zkc != undefined) {
						txzkc = txzkc.concat(zkc);
					}
				}
				
				//与已选课程对比，是否为同一课堂的子课堂
				$(yxkc).each(function(i, v) {
					if (v.id.coureNumber == kch && v.id.coureSequenceNumber != kxh && v.zkxh == objTemp.zkxh) {
						fsyx = true;
						return false;
					}
				});
				
				if(obj.kch==kch 
						&& (fsyx 
							|| (!fsyx 
								&& sfczfskt == "false" 
									|| (sfczfskt == "true"
										&& (objTemp.zkxh =="" 
											|| (objTemp.zkxh !="" && objTemp.zkxh != obj.kxh)))))){
				//if(fsyx || (obj.kch==kch && (sfczfskt == "false" || objTemp.zkxh =="" || objTemp.zkxh != obj.kxh))){
					ff = true;
				} else {
					var tempKckxh = kch + "-" + kxh;
					for(var i=0; i<kckxh.length; i++){
						if(kckxh[i] == tempKckxh) {
							f1 = false;
							break;
						}
					}
					if(f1 && jdyxct != "1") {
						var skzcTemp = objTemp.skzc;
						for(var o=0; o<txzkc.length; o++){
							var skzc = txzkc[o].skzc;
							if(skzc!=undefined && skzc!="" && skzcTemp!=undefined && skzcTemp!="" && Math.abs(parseInt(skzc, 2)&parseInt(skzcTemp, 2))){
								var skxq = txzkc[o].skxq;
								var skxqTemp = objTemp.skxq;
								var skjc = txzkc[o].skjc;
								var skjcTemp = objTemp.skjc;
								
								if(skxq == skxqTemp && skjc == skjcTemp){
									kckxh[kckxh.length] = tempKckxh;
									ff = true;
									break;
								}
							}
						}
						if(!ff) {
							for(var o=0; o<yxkc.length; o++){
								var yxkcSjdd = yxkc[o].timeAndPlaceList;
								if(yxkcSjdd && yxkcSjdd.length > 0) {
									for(var k=0; k<yxkcSjdd.length; k++){
										var skzc = yxkcSjdd[k].classWeek;
										if(skzc!=undefined && skzc!="" && skzcTemp!=undefined && skzcTemp!="" && Math.abs(parseInt(skzc, 2)&parseInt(skzcTemp, 2))){
											var skxq = yxkcSjdd[k].classDay;
											var skxqTemp = objTemp.skxq;
											var skjc = yxkcSjdd[k].classSessions;
											var skjcTemp = objTemp.skjc;
											var cxjc = yxkcSjdd[k].continuingSession;
											
											if(skxq == skxqTemp && skjc <= skjcTemp && skjcTemp <= (parseInt(skjc) + parseInt(cxjc) - 1)){
												kckxh[kckxh.length] = tempKckxh;
												ff = true;
												break;
											}
										}
									}
								}
							}
						}
					}
				}
				
				return ff;
			}
			
			function dsq(zxjxjhh, kch, kxh, id){
				var cc = 3;
				myinterval();
				$("button[id^=cxkylbtn_]").attr("disabled",true);
				$("button[id^=cxkylbtn_]").removeClass("btn-success").addClass("btn-default");
				var aa = setInterval(myinterval,1000);
				function myinterval(){
					$("button[id^=cxkylbtn_]").text(cc+"s");
					if(cc==0){
						$("button[id^=cxkylbtn_]").attr("disabled",false);
						$("button[id^=cxkylbtn_]").removeClass("btn-default").addClass("btn-success");
						$("button[id^=cxkylbtn_]").html("<i class='ace-icon fa fa-refresh bigger-120'></i> ⟳");
						clearInterval(aa);
					}
					cc--;
				}
				cxkyl(zxjxjhh, kch, kxh, id);
			}
			
			function cxkyl(zxjxjhh, kch, kxh, id){
				$.ajax({
					url : "/student/courseSelect/intentCourse/queryRest",
					method : "post",
					data : "zxjxjhh=" + zxjxjhh + "&kch=" + kch + "&kxh=" + kxh,
					success : function(data){
						$("#span_"+id).html(data);
					},
					error : function(){
						urp.alert("刷新课余量失败！");
					}
				});
			}
				
			function addToAttention(obj, czfskt, f){
				var kckx = obj.kch+"_"+obj.kxh;
				var bkskyl = ${!fromRedis} ? obj.bkskyl : kylMap[obj.zxjxjhh+"_"+obj.kch+"_"+obj.kxh];
				if(removeYxkxAtten(kckx) && $("#intentiontr_"+obj.kch+"_"+obj.kxh+"_"+obj.zxjxjhh+"_"+f).length == 0){
					var tc = "<tr id='intentiontr_"+kckx+"_"+obj.zxjxjhh+"_"+f+"' sfczfskt='"+czfskt+"'>\
								<td id='kcm_"+obj.kcm + (obj.zkxh == "" ? "" : "_" + obj.zkxh) + (obj.fsktms == "" ? "" : "_" +obj.fsktms) +"_"+obj.kxh+"'>"+ 
								obj.kcm + (obj.zkxh == "" ? "" : "_" + obj.zkxh) + (obj.fsktms == "" ? "" : "_" +obj.fsktms) +"_"+obj.kxh +"</td><td>"+obj.skjs+"</td>"
								+"    <td><span id='span_"+obj.id+"'>"+bkskyl+"</span><button id='cxkylbtn_"+obj.id+"' " +
								"class='btn btn-xs btn-round btn-success' onclick='dsq(\""+obj.zxjxjhh+"\",\""+obj.kch+"\",\""+obj.kxh+"\",\""+obj.id+"\");'>"
								+"		      <i class='ace-icon fa fa-refresh bigger-120'></i> ⟳"
								+"		  </button></td>"
								+"    <td><button id='resel_"+kckx+"_"+f+"' zkxh='"+obj.zkxh+"' class='btn btn-xs btn-round btn-danger " + 
								(czfskt=="1" ? "disabled'" : "' onclick='resumeselect(this);'")+" value='"+kckx+"_"+obj.zxjxjhh+"_"+f+"'>"
								+"		      <i class='ace-icon fa fa-trash-o bigger-120'></i> 🗑"
								+"		  </button></td></tr>";
					$(tc).fadeIn(200, function(){
						$("#tb_inten_sel").append(tc);
					});
				}
			}
			
			function removeYxkxAtten(kckx){
				var ff = true;
				$.each(yxkc,function(i, v){
					var temp = v.id.coureNumber + "_" + v.id.coureSequenceNumber;
					if(temp == kckx){
						ff = false;
						return false;
					}
				});
				return ff;
			}
				
			function fillNoTimeList(d){
				var noTimeCont = "";
				if(d.length>0){
					$.each(d,function(i,t){
						var bkskyl = ${!fromRedis} ? t.bkskyl : kylMap[t.zxjxjhh + "_" + t.kch + "_" + t.kxh];
						var czfskt = t.zkxh=="" && checksfczfskt(t.kch, t.kxh);//t.sfczfskt=="1" && checksfczfskt(t.kch);
						var fsktms = czfskt ? "主课堂" : t.fsktms == "" ? "无" : t.fsktms;
						
						noTimeCont += "<tr><td><label><input class='ace ace-checkbox-2' "+(czfskt?" disabled " : "")+" \
							id='selectadd_"+t.kch+"_"+t.kxh+"' zkch='"+ t.zkch +"' type='checkbox' name='checkKc' czfskt='"+czfskt+"'\
							value='"+JSON.stringify(t)+"'/><span class='lbl'></span></label></td>";
						noTimeCont += "<td>"+t.kch+"</td>";
						noTimeCont += "<td id='"+t.kch+"_"+t.kxh+"'>"+t.kcm+"</td>";
						noTimeCont += "<td>"+t.kxh + (t.zkxh ? "_<"+ t.zkxh +">" : "") +"</td>";
						noTimeCont += "<td>"+fsktms+"</td>";
						noTimeCont += "<td>"+t.xf+"</td>";
						noTimeCont += "<td>"+t.kcsxmc+"</td>";
						noTimeCont += "<td>"+t.kslxmc+"</td>";
						noTimeCont += "<td>"+t.skjs+"</td>";
						noTimeCont += "<td>"+bkskyl+"/<a title='点击查看' href='javascript:void(0);' \
								onclick='viewXkCount(\""+t.zxjxjhh+"\",\""+t.kch+"\",\""+t.kxh+"\", this);'>点击查看</a></td>";
						noTimeCont += "<td>"+t.xkmssm+"</td>";
						noTimeCont += "<td>"+t.xkkzsm+"</td>";
						var xkxzsm = t.xkxzsm;
						if(t.xkxzsm.length>8){
							xkxzsm = "<span style='color:#579EC8;' title='"+t.xkxzsm+"'>"+t.xkxzsm.substring(0,9)+"...</span>";
						}
						noTimeCont += "<td>"+xkxzsm+"</td>";
					});
				}else{
					noTimeCont = "<tr><td colspan='13' style='color:red;'>未安排时间和地点的课程在此显示，目前没有此类数据！</td></tr>";
				}
				$("#notimecourse").html(noTimeCont);
				
				//选择没有时间的课程
				$("input[id^=selectadd_]").bind("click",function(){
					var thisid = $(this).attr("id").split("_");
					var sfczfskt = $(this).parents("tr").attr("sfczfskt") == "1" ? true : false;
					if($(this).is(":checked")){
						var obj = eval("("+$(this).val()+")");
						addToAttention(obj, "0", "l");
						if (obj.zkxh != "") {
							var selzkt = $("#notimecourse").find("input[id^=selectadd_"+thisid[1]+"_"+obj.zkxh+"]");
							if (selzkt.length > 0) {
								$(selzkt).prop("checked", true);
							} else {
								var btnzkt = $("button[id^=btnadd_"+thisid[1]+"_"+obj.zkxh+"_]");
								if (btnzkt.length > 0) {
									$(btnzkt).removeClass("btn-purple").addClass("btn-success");
									$(btnzkt).html("<i class='ace-icon fa fa-check-square-o bigger-120'></i> ✔");
									$(btnzkt).prop("disabled",true);
									$(btnzkt).parents(".dropdown").find("button[id^=btnmenu_]").removeClass("badge-purple").addClass("badge-success");
									otherkt (eval("("+btnzkt[0].value+")"), sfczfskt);
								}
							}
						}
						$("#notimecourse").find("input[id^=selectadd_"+thisid[1]+"]")
							.not("input[id^=selectadd_"+thisid[1]+"_"+obj.zkxh+"], input[id^=selectadd_"+thisid[1]+"_"+thisid[2]+"]").attr("disabled",true);
						

						//分项目课程
						var zkch = $(this).attr("zkch");
						if(zkch != "" && obj.xmcjhc != "1") {
							var qtxm = $("button[id^=btnadd_][zkch='"+ zkch +"']").filter(".btn-purple");
							$(qtxm).removeClass("btn-purple").addClass("btn-default");
							$(qtxm).html("<i class='ace-icon fa fa-times bigger-120'></i> ✕");
							$(qtxm).prop("disabled",true);
							$("#notimecourse").find("input[id^=selectadd_][zkch='"+ zkch +"']").not(":disabled").attr("disabled", true);
						}
					}else{
						$("#tb_inten_sel").find("#resel_"+thisid[1]+"_"+thisid[2]+"_"+"l").click();
					}
				});
			}
				
			//取消选择
			function resumeselect(o){
				parent.urp.confirm("确定从意向中移除该课程？",function(f){
					var value = o.value.split("_");
					var kckx = value[0]+"_"+value[1];
					if(f){
						$("#intentiontr_"+o.value).fadeOut(200, function(){ 
							$(this).remove();
						});
						$("button[id^=btnmenu_]").removeClass("badge-default badge-success").addClass("badge-purple");
						var btnadd = $("button[id^=btnadd_]");
						$(btnadd).removeClass("btn-default btn-success").addClass("btn-purple");
						$(btnadd).html("<i class='ace-icon fa fa-plus bigger-120'></i> 十");
						$(btnadd).prop("disabled",false);
						$("#notimecourse").find("input[type=checkbox]").prop("checked",false);
						$("#notimecourse").find("input[id^=selectadd_]").each(function() {
							if ($(this).attr("czfskt") == "false") {
								$(this).attr("disabled",false);
							}
						});
						setYxkcStatus();
						
						var trs = $("tr[id^=intentiontr_]").not("#intentiontr_"+o.value);
						for(var i=0;i<trs.length;i++){
							var yxid = trs[i].id.split("_");
							var obj = $("button[id^=btnadd_"+yxid[1]+"_"+yxid[2]+"_]");
							if (obj.length == 0) {
								obj = $("#notimecourse").find("input[id^=selectadd_"+yxid[1]+"_"+yxid[2]+"]");
							}
							if (obj.length > 0) {
								$(obj).first().click();
							}
						}
					} else if (value[3]=="l"){
						$("#notimecourse").find("#selectadd_"+kckx).prop("checked",true);
					}
				});
			}
			
			function xuanze(){
				var kcIds = "";
				var kcms = "";
				var trs = $("#tb_inten_sel tr");
				for(var i=0;i<trs.length;i++){
					var trid = trs[i].id.split("_");
					kcIds += trid[1]+"_"+trid[2]+"_"+trid[3]+",";
					var kcmid = $(trs[i]).find("td[id^=kcm_]").attr("id");
					kcms += kcmid.substring(4)+",";
				}
				kcIds = kcIds.substr(0,kcIds.length-1);
				kcms = kcms.substr(0,kcms.length-1);
				
				$("#kcIds").val(kcIds);
				var xxa = "";
				for(var i=0; i<kcms.length; i++) {
					xxa += kcms.charCodeAt(i) + ",";
				}
				$("#kcms").val(xxa);
			}
			
			//设置已选课程状态
			function setYxkcStatus(){
				$.each(yxkc,function(i,v){
					var kckx = v.id.coureNumber + "_" + v.id.coureSequenceNumber;
					if($("button[id^=btnadd_"+kckx+"_]").length > 0){
						$("button[id^=btnadd_"+kckx+"_]").first().click();
					} else if($("#notimecourse").find("#selectadd_"+kckx).length > 0) {
						$("#notimecourse").find("#selectadd_"+kckx).click();
						$("#notimecourse").find("#selectadd_"+kckx).prop("disabled", true);
					} else {
						
					}
				});
			}
			
			function jhqx(){
				$("button[id^=btnmenu_]").removeClass("badge-default badge-success").addClass("badge-purple");
				var btnadd = $("button[id^=btnadd_]");
				$(btnadd).removeClass("btn-default btn-success").addClass("btn-purple");
				$(btnadd).html("<i class='ace-icon fa fa-plus bigger-120'></i> 十");
				$(btnadd).prop("disabled",false);
				$("#tb_inten_sel").html("");
				
				$("#notimecourse").find("input[type=checkbox]").prop("checked",false);
				$("#notimecourse").find("input[id^=selectadd_]").each(function() {
					if ($(this).attr("czfskt") == "false") {
						$(this).attr("disabled",false);
					}
				});
				setYxkcStatus();
			}
			
			function viewXkCount(zxjxjhh, kch, kxh, obj){
				 $.ajax({
	                url: "/student/courseSelect/selectCourse/viewXkCount/"+zxjxjhh+"/"+kch+"/"+kxh,
	                cache: false,
	                type: "post",
	                data: "",
	                dataType: "json",
	                beforeSend: function () {
	                },
	                complete: function () {
	                },
	                success: function (d) {
		                $(obj).html(d);
		                $(obj).attr("title", "点击更新");
	                },
	                error: function (xhr) {
	                    urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
	                }
				});
			}
			
			$(document).on("click", (e) => {
				if(!e.target || !$(e.target).attr("id")?.startsWith("btnmenu_")) {
					$("div.dropdown.open").removeClass("open");
				}
			});
		</script>
		<style type="text/css">
			body{
				background-color:white;
				margin:0;
				padding:0;
			}
			.dropdown-self{
				float:none;
				min-width:400px;
				padding:5px;
				font-size:12px;
				border:1px solid orange;
				box-shadow:5px 5px 5px rgba(255,165,0,.5);
				-webkit-box-shadow:5px 5px 5px rgba(255,165,0,.5);
				border-radius:5px!important;
			}
			#courseTable td {
				padding: 0px !important;
			}
			 #courseTableHead th, #ktTable td, #ktTable th, #courseTable th, #att-table td, #att-table th, #notimetable td, #notimetable th{
				padding: 4px !important;
			}
		</style>
	</head>
	<body>
		<input type="hidden" id="param_value" name="param_value" value="${schoolCode }">
		<div>
			<div class="col-xs-9" style="width: 75%; display: inline-block;">
		        <label style="margin: 10px; display: inline-block;">
		            <input type="checkbox" class="ace" checked id="filterClassCourses" onclick="queryData();"/>
		            <span class="lbl">按任务面向班级显示课程</span>
		        </label>
		        <div id="questiontip" style="float: right; display: inline-block; padding: 8px; color: white; text-align: center;">
					<span style="background-color:#9585bf; padding:2px 4px; border-radius: 5px; display: inline-block; width: 50px;">可选</span>
					<span style="background-color:#82af6f; padding:2px 4px; border-radius: 5px; display: inline-block; width: 50px;">已选</span>
					<span style="background-color:#ddd; padding:2px 4px; border-radius: 5px; display: inline-block; width: 50px;">不可选</span>
				</div>
			</div>
		</div>
		<div class="col-xs-9" id="div_kc_tj" style="overflow:auto;padding:0; max-height: calc(100vh - 50px); position: relative; display: inline-block; width: calc(75vw); vertical-align: top;">
			<div id="mycoursetableHeadDiv" style="display: none; position: fixed;z-index: 100;">
				<table class="table table-hover table-striped table-bordered" style="margin-bottom: 0px;">
					<thead id="mycoursetableHead"></thead>
				</table>
			</div>
			<form action="/student/courseSelect/selectCourses/waitingfor" name="frm" method="POST" target="_parent">
				<input type="hidden" name="dealType" value="1">
				<input type="hidden" id="fajhh" name="fajhh" value="${fajhh}">
				<input type="hidden" name="kcIds" id="kcIds" value="">
				<input type="hidden" name="kcms" id="kcms" value="">
				<div id="mycoursetable"></div>
				
				<table class="table table-hover table-striped table-bordered" id="notimetable">
					<thead>
						<tr>
							<th>选择</th>
							<th>课程号</th>
							<th>课程名</th>
							<th>课序号</th>
							<th>附属课堂</th>
							<th>学分</th>
							<th>课程属性</th>
							<th>考试类型</th>
							<th>上课教师</th>
							<th>课余量/已选人数</th>
							<th>选课模式</th>
							<th>选课控制</th>
							<th>选课限制说明</th>
						</tr>
					</thead>
					<tbody id="notimecourse"></tbody>
				</table>
			</form>
		</div>
		
		<div class="col-xs-3" id="div_kb" style="overFlow: auto; max-height: calc(100vh - 45px); display: inline-block; width: calc(25vw - 5px);">
			<label>已选意向课程：</label>
			<table class="table table-hover table-striped table-bordered" id="att-table">
				<thead>
					<tr>
						<th>课程名</th>
						<th>教师</th>
						<th>课余量</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="tb_inten_sel"></tbody>
			</table>
		</div>
		<div class="clear: both;"></div>
	</body>
</html>
