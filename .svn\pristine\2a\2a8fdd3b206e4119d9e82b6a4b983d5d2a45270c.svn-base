package educationalAdministration.student.individualApplication.gradeChange.controller;

import com.urpSoft.business.utils.UrpResult;
import com.urpSoft.core.data.query.component.QueryInfo;
import com.urpSoft.core.document.common.service.CommonExportService;
import com.urpSoft.core.pagination.page.Page;
import com.urpSoft.core.pagination.service.IPageService;
import com.urpSoft.core.service.IBaseService;
import com.urpSoft.core.util.AuthUtil;
import com.urpSoft.core.util.CSRFToken;
import educationalAdministration.dictionary.entity.*;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.individualApplication.applyCommon.service.ApplyCommonService;
import educationalAdministration.student.individualApplication.gradeChange.service.GradeChangeService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025-06-18 11:03
 * @Description 历年成绩变更申请
 * @Version v1.0
 */
@Controller
public class GradeChangeController {

    @Resource
    private IBaseService baseService;

    @Resource
    private IPageService pageService;

    @Resource
    private GradeChangeService gradeChangeService;

    @Resource
    private ApplyCommonService applyCommonService;

    @Resource
    private CommonExportService commonExportService;

    private CSRFToken csrfToken = CSRFToken.getInstance();

    @RequestMapping("/student/application/gradeChange/index")
    public String index(Model model) {
        String flag = "";
        SysYwhdkzb ywsqkzb = baseService.queryEntityById(SysYwhdkzb.class, "10045");
        if (ywsqkzb == null) {
            flag = "nonparametric";
        } else {
            if (ywsqkzb.getQyf().equals("0")) {
                flag = "notenabled";
            } else {
                String sql = " select count(1) from sys_ywhdkzb where id = '10045' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) and kg = '1' ";
                long timeCount = baseService.queryCounBySql(sql);
                if (timeCount == 0) {
                    flag = "nottime";
                } else {
                    model.addAttribute("kzkg", ywsqkzb);
                    flag = "showAdd";
                }
            }
        }
        model.addAttribute("flag", flag);
        String zxjxjhh = CommonUtils.queryCurrentXnxq();
        model.addAttribute("zxjxjhh", zxjxjhh);
        return "/student/personalManagement/individualApplication/gradeChange/index";
    }


    /**
     * 分页-查询变更记录信息
     *
     * @param pageNum
     * @param pageSize
     */
    @RequestMapping(value = "/student/application/gradeChange/getApplyList")
    @ResponseBody
    public UrpResult getTeachersList(@RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "30") int pageSize,
                                     String kkxsh_cx, String kch_cx, String kcm_cx,String zxjxjhh_cx) {
        String queryVal = "";
        String userId = AuthUtil.getCurrentUser().getIdNumber();
        queryVal += " and a.user_code = '" + userId + "'";
        if (StringUtils.isNotBlank(zxjxjhh_cx)) {
            queryVal += " and b.zxjxjhh = '" + zxjxjhh_cx + "'";
        }
        if (StringUtils.isNotBlank(kkxsh_cx)) {
            queryVal += " and pn.kc_kkxsdm(b.kch) = '" + kkxsh_cx + "'";
        }
        if (StringUtils.isNotBlank(kch_cx)) {
            queryVal += " and b.kch = '" + kch_cx + "'";
        }
        if (StringUtils.isNotBlank(kcm_cx)) {
            queryVal += " and pn.kcm(b.kch) = '" + kcm_cx+ "'";
        }
        String sql = "select i.*, rownum rn from (SELECT a.apply_id,a.apply_type, (SELECT c.apply_name  FROM ea_apply_type c " +
                "WHERE a.apply_type = c.apply_type) AS apply_name,a.user_code,a.commit_dt,a.rollback_dt,a.apply_status,a.ea_rslt," +
                "a.note,b.sqyy,pn.xnxqmc(b.zxjxjhh) xnxq,pn.kc_kkxs(b.kch) kkxsm,b.kch,pn.kcm(b.kch) kcm,b.kxh,(SELECT to_char(wm_concat(fjmc)) " +
                "FROM SYS_SQFJB WHERE sqbh = a.apply_id) fj FROM ea_applys a, CJXG_XSSQB b " +
                "WHERE a.apply_id = b.sqbh AND a.user_code = b.sqr " + queryVal + ") i";
        QueryInfo info = new QueryInfo();
        info.setPageNum(pageNum);
        info.setMaxResult(pageSize);
        info.setSql(sql);
        Page<Object> page = pageService.queryPageBySql(info);
        return UrpResult.ok(page);
    }

    /**
     * 校验是否可以申请
     * @return
     */
    @RequestMapping("/student/application/gradeChange/checkaddApply")
    @ResponseBody
    public Map<String, Object> checkApplyNote() {
        Map<String, Object> map = new HashMap<String, Object>();
        SysYwhdkzb sysYwhdkzb = baseService.queryEntityById(SysYwhdkzb.class, "10045");
        if (sysYwhdkzb != null) {
            if (sysYwhdkzb.getQyf().equals("0")) {
                map.put("msg", "申请开关已关闭，不允许申请！");
            } else {
                String sql = "SELECT count(1) from sys_ywhdkzb where id = '10045' and (to_char(sysdate,'yyyymmddHH24mi') between kssj and jssj) and kg = '1'";
                long timeCount = baseService.queryCounBySql(sql);
                if (timeCount == 0) {
                    map.put("msg", "当前时间不允许进行申请！");
                } else {
                    map.put("sfxyd", sysYwhdkzb.getSfxyd());
                    map.put("ydnr", sysYwhdkzb.getYdnrstr());
                    map.put("qzydms", sysYwhdkzb.getQzydms());
                }
            }
        } else {
            map.put("msg", "未维护申请控制开关，请联系管理员处理！");
        }
        return map;
    }

    /**
     * 新增/修改申请页面
     *
     * @param model
     * @return
     */
    @RequestMapping("/student/application/gradeChange/addApply")
    public String addLncjBgInfo(Model model, String sqbh) {
        if(StringUtils.isNotBlank(sqbh)) {
            model.addAttribute("sqxx", gradeChangeService.querySqById(sqbh));
            model.addAttribute("fjs", gradeChangeService.queryFjById(sqbh));
        } else {
            String zxjxjhh = CommonUtils.queryCurrentXnxq();
            model.addAttribute("zxjxjhh", zxjxjhh);
        }
        List<SysColConfig> sysColConfigList = applyCommonService.querySysColConfigByTabName("CJXG_XSSQB");
        List<SysColConfig> list = new ArrayList<SysColConfig>();
        for (SysColConfig sysColConfig : sysColConfigList) {
            if (StringUtils.isNotBlank(sysColConfig.getColcheck())) {
                CodeRegular codeRegular = applyCommonService.queryEntityById(CodeRegular.class, sysColConfig.getColcheck());
                if (codeRegular != null) {
                    sysColConfig.setRegexp(codeRegular.getRegexp());
                    sysColConfig.setRegname(codeRegular.getRegname());
                }
            }
            if (StringUtils.isNotBlank(sysColConfig.getColexp())) {
                sysColConfig.setColexp(sysColConfig.getColexp().replace("，", ","));
            }
            list.add(sysColConfig);
        }
        model.addAttribute("sysColConfigList", list);
        model.addAttribute("fjkz", gradeChangeService.queryFjKz());
        return "/student/personalManagement/individualApplication/gradeChange/addApply";
    }


    /**
     * 查询成绩记录
     * @param model
     * @return
     */
    @RequestMapping("/student/application/gradeChange/selectGrade/page")
    public String scPage(Model model) {
        model.addAttribute("xscj", gradeChangeService.queryXsCj());
        return "/student/personalManagement/individualApplication/gradeChange/selectGrade";
    }


    /**
     * 暂存/提交申请
     * @param request
     * @param session
     * @return
     */
    @RequestMapping(value = "/student/application/gradeChange/saveInfo")
    @ResponseBody
    public UrpResult saveInfo(HttpServletRequest request, HttpSession session,
                              String sqbh, String xnxq, String kch, String kxh, String sqyy,String fjids,
                              @RequestParam(defaultValue = "", required = false) MultipartFile[] sqfj,
                              String apply_status, String spjsh) {
        Map<String, Object> map = new HashMap<String, Object>();
        if (!csrfToken.isTokenValid(request)) {
            map.put("result", csrfToken.gotoAjaxIndex());
        } else {
            map.put("token", session.getAttribute("token_in_session").toString());
            map.put("result", gradeChangeService.saveInfo(request, sqbh, xnxq, kch, kxh,sqyy, fjids, sqfj, apply_status, spjsh));
        }
        return UrpResult.ok(map);
    }

    @RequestMapping("/student/application/gradeChange/downLoad/{id}")
    public void downloadFile(HttpServletResponse response, @PathVariable("id") String id) {
        SysSqfjb fjb = baseService.queryEntityById(SysSqfjb.class, id);
        commonExportService.exportCommon(fjb.getFjmc(), fjb.getFjnr(), response);
    }

    /**
     * 撤回申请
     *
     * @param request
     * @param session
     * @param sqbh
     * @return
     */
    @RequestMapping(value = "/student/application/gradeChange/revokeApply")
    @ResponseBody
    public UrpResult revokeApply(HttpServletRequest request, HttpSession session, String sqbh) {
        Map<String, Object> map = new HashMap<String, Object>();
        if (!csrfToken.isTokenValid(request)) {
            map.put("result", csrfToken.gotoAjaxIndex());
        } else {
            map.put("token", session.getAttribute("token_in_session").toString());
            map.put("result", gradeChangeService.revokeApply(request, sqbh));
        }
        return UrpResult.ok(map);
    }

    /**
     * 查看
     *
     * @param model
     * @param sqbh
     * @return
     */
    @RequestMapping("/student/application/gradeChange/seeApply")
    public String seeApply(Model model, String sqbh) {
        model.addAttribute("sqxx", gradeChangeService.querySqById(sqbh));
        EaApplys eaApplys = baseService.queryEntityById(EaApplys.class, sqbh);
        model.addAttribute("eaApplys", eaApplys);
        List<SysColConfig> sysColConfigList = applyCommonService.querySysColConfigByTabName("CJXG_XSSQB");
        model.addAttribute("colList", sysColConfigList);
        model.addAttribute("eaResult", gradeChangeService.queryResult(sqbh));
        model.addAttribute("fjs", gradeChangeService.queryFjById(sqbh));
        return "/student/personalManagement/individualApplication/scoreCheck/seeApply";
    }

    /**
     * 弹出指定审批人页面
     * @param model
     * @param sqbh
     * @return
     */
    @RequestMapping(value = "/student/application/gradeChange/selectApprover", method = RequestMethod.GET)
    public String selectApprover(Model model, String sqbh) {
        model.addAttribute("apply_type", "10045");
        model.addAttribute("sqbh", sqbh);
        return "/student/personalManagement/individualApplication/scoreCheck/selectApprover";
    }

    /**
     * 查询审批人
     * @return
     */
    @RequestMapping(value = "/student/application/gradeChange/queryApprover")
    @ResponseBody
    public UrpResult queryApprover(String zxjxjhh, String kch, String kxh, String sqyy) {
        Map<String, Object> map = new HashMap<>();
        List<Object[]> list = applyCommonService.queryEalByApplyType("10045");
        Object[] names = list.get(0);
        String czr = AuthUtil.getCurrentUser().getIdNumber();
        String sqbh = applyCommonService.querySqbhByApply();
        String sql = "insert into CJXG_XSSQB (sqbh, sqr, zxjxjhh, kch, kxh, sqyy) values ('"+ sqbh +"', '"+czr+"', '"+zxjxjhh+"', '"+kch+"', '"+kxh+"', '"+sqyy+"')";
        String eazxjxjhh = CommonUtils.queryCurrentXnxq();
        map = applyCommonService.queryApprovers(sql, "10045", names, sqbh, eazxjxjhh, "");
        map.put("eal_name", names[0]);
        map.put("eap_code", names[2]);
        map.put("eal_code", names[3]);
        return UrpResult.ok(map);
    }

}
