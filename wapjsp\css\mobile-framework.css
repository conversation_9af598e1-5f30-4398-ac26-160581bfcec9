/* 移动端UI框架 - 基于设计系统规范 */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;
  
  /* 功能色彩 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  /* 中性色彩 */
  --text-primary: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.65);
  --text-disabled: rgba(0, 0, 0, 0.25);
  
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-color-active: #ececec;
  
  --border-primary: #d9d9d9;
  --border-secondary: #f0f0f0;
  --divider-color: #f0f0f0;
  
  /* 字体系统 */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                         'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 
                         'Helvetica Neue', Helvetica, Arial, sans-serif;
  
  --font-size-h1: 24px;
  --font-size-h2: 20px;
  --font-size-h3: 18px;
  --font-size-h4: 16px;
  --font-size-large: 16px;
  --font-size-base: 14px;
  --font-size-small: 12px;
  --font-size-mini: 10px;
  
  --line-height-tight: 1.2;
  --line-height-base: 1.5;
  --line-height-loose: 1.8;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  --padding-xs: var(--spacing-xs);
  --padding-sm: var(--spacing-sm);
  --padding-md: var(--spacing-md);
  --padding-lg: var(--spacing-lg);
  
  --margin-xs: var(--spacing-xs);
  --margin-sm: var(--spacing-sm);
  --margin-md: var(--spacing-md);
  --margin-lg: var(--spacing-lg);
  
  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: rgba(255, 255, 255, 0.85);
    --text-secondary: rgba(255, 255, 255, 0.65);
    --text-disabled: rgba(255, 255, 255, 0.25);
    
    --bg-primary: #141414;
    --bg-secondary: #1f1f1f;
    --bg-tertiary: #262626;
    --bg-color-active: #373737;
    
    --border-primary: #434343;
    --border-secondary: #303030;
    --divider-color: #303030;
  }
}

/* 基础重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 页面容器 */
.page-mobile {
  min-height: 100vh;
  background: var(--bg-secondary);
}

.container-mobile {
  padding: 0;
  margin: 0;
}

/* 导航栏组件 */
.navbar-mobile {
  height: 56px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--divider-color);
  display: flex;
  align-items: center;
  padding: 0 var(--padding-md);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-back {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color var(--transition-fast);
}

.navbar-back:active {
  background: var(--bg-color-active);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: var(--font-size-h4);
  font-weight: 500;
  color: var(--text-primary);
}

.navbar-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color var(--transition-fast);
}

.navbar-action:active {
  background: var(--bg-color-active);
}

/* 按钮组件 */
.btn-mobile {
  min-height: 44px;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: var(--font-size-base);
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* 卡片组件 */
.card-mobile {
  background: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--margin-md);
  overflow: hidden;
}

.card-header {
  padding: var(--padding-md);
  border-bottom: 1px solid var(--divider-color);
  font-weight: 500;
}

.card-body {
  padding: var(--padding-md);
}

.card-footer {
  padding: var(--padding-md);
  border-top: 1px solid var(--divider-color);
  background: var(--bg-secondary);
}

/* 列表组件 */
.list-mobile {
  background: var(--bg-primary);
  border-radius: 8px;
  overflow: hidden;
}

.list-item {
  padding: var(--padding-md);
  border-bottom: 1px solid var(--divider-color);
  display: flex;
  align-items: center;
  min-height: 56px;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background: var(--bg-color-active);
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  margin-bottom: 4px;
}

.list-item-subtitle {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

/* 表单组件 */
.form-mobile {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: var(--padding-md);
}

.form-group {
  margin-bottom: var(--margin-md);
}

.form-label {
  display: block;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  margin-bottom: var(--margin-sm);
  font-weight: 500;
}

.form-control {
  width: 100%;
  min-height: 44px;
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  font-size: var(--font-size-base);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-base);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

/* 加载和空状态 */
.loading-container {
  text-align: center;
  padding: var(--padding-lg);
  color: var(--text-secondary);
}

.empty-state {
  text-align: center;
  padding: var(--padding-xxl);
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: var(--margin-md);
  color: var(--text-disabled);
}

/* 工具类 */
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.flex-1 { flex: 1; }

.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mt-sm { margin-top: var(--margin-sm); }
.mb-md { margin-bottom: var(--margin-md); }
.p-lg { padding: var(--padding-lg); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }

/* 响应式断点 */
@media (max-width: 575.98px) {
  .container-mobile {
    padding: 0 var(--padding-sm);
  }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .container-mobile {
    padding: 0 var(--padding-md);
  }
}

@media (min-width: 768px) {
  .container-mobile {
    max-width: 768px;
    margin: 0 auto;
    padding: 0 var(--padding-lg);
  }
}

/* 动画效果 */
.fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all var(--transition-base);
}

.slide-enter {
  transform: translateX(100%);
}

.slide-enter-active {
  transform: translateX(0);
  transition: transform var(--transition-base);
}

/* 触摸反馈 */
.touch-feedback {
  position: relative;
  overflow: hidden;
}

.touch-feedback::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.touch-feedback:active::after {
  width: 200px;
  height: 200px;
}
