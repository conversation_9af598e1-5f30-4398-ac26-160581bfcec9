<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>成绩单打印</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 成绩单打印页面样式 */
        .print-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .options-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .options-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .options-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .option-group {
            margin-bottom: var(--margin-md);
        }
        
        .option-group:last-child {
            margin-bottom: 0;
        }
        
        .option-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
            font-weight: 500;
        }
        
        .option-items {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .option-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .option-item:active {
            background: var(--bg-color-active);
        }
        
        .option-item.selected {
            background: var(--primary-light);
            color: var(--primary-color);
        }
        
        .option-radio {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-primary);
            border-radius: 50%;
            margin-right: var(--margin-sm);
            position: relative;
            transition: all var(--transition-base);
        }
        
        .option-item.selected .option-radio {
            border-color: var(--primary-color);
        }
        
        .option-item.selected .option-radio::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }
        
        .option-text {
            flex: 1;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        .option-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 2px;
        }
        
        .range-selector {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .range-input {
            flex: 1;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .preview-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .preview-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .preview-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .preview-content {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            border: 1px dashed var(--border-primary);
            text-align: center;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .preview-placeholder i {
            font-size: var(--font-size-h2);
            color: var(--text-disabled);
        }
        
        .transcript-preview {
            background: white;
            color: black;
            padding: var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            line-height: var(--line-height-base);
            text-align: left;
            width: 100%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .transcript-header {
            text-align: center;
            margin-bottom: var(--margin-lg);
            border-bottom: 2px solid #333;
            padding-bottom: var(--padding-sm);
        }
        
        .transcript-title {
            font-size: var(--font-size-h3);
            font-weight: bold;
            margin-bottom: var(--margin-sm);
        }
        
        .transcript-subtitle {
            font-size: var(--font-size-base);
            color: #666;
        }
        
        .student-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-lg);
            font-size: var(--font-size-small);
        }
        
        .info-item {
            display: flex;
        }
        
        .info-label {
            font-weight: bold;
            margin-right: var(--margin-xs);
            min-width: 60px;
        }
        
        .score-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: var(--margin-lg);
            font-size: var(--font-size-mini);
        }
        
        .score-table th,
        .score-table td {
            border: 1px solid #333;
            padding: 4px 6px;
            text-align: center;
        }
        
        .score-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        
        .score-table .course-name {
            text-align: left;
            max-width: 120px;
            word-wrap: break-word;
        }
        
        .transcript-footer {
            margin-top: var(--margin-lg);
            padding-top: var(--padding-sm);
            border-top: 1px solid #333;
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-mini);
        }
        
        .actions-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-preview {
            background: var(--info-color);
            color: white;
        }
        
        .btn-print {
            background: var(--success-color);
            color: white;
        }
        
        .btn-download {
            background: var(--primary-color);
            color: white;
        }
        
        .tips-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tips-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .tips-title i {
            margin-right: var(--margin-xs);
            color: var(--warning-color);
        }
        
        .tip-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .tip-item:last-child {
            margin-bottom: 0;
        }
        
        @media print {
            .page-mobile > *:not(.preview-section) {
                display: none !important;
            }
            
            .preview-section {
                margin: 0;
                padding: 0;
                box-shadow: none;
                background: transparent;
            }
            
            .preview-title {
                display: none;
            }
            
            .transcript-preview {
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">成绩单打印</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="print-header">
            <div class="header-title">成绩单生成</div>
            <div class="header-subtitle">选择打印选项，生成官方成绩单</div>
        </div>

        <!-- 打印选项 -->
        <div class="options-section">
            <div class="options-title">
                <i class="ace-icon fa fa-cog"></i>
                <span>打印选项</span>
            </div>

            <!-- 成绩单类型 -->
            <div class="option-group">
                <div class="option-label">成绩单类型</div>
                <div class="option-items">
                    <div class="option-item selected" onclick="selectOption(this, 'type', 'official')">
                        <div class="option-radio"></div>
                        <div class="option-content">
                            <div class="option-text">官方成绩单</div>
                            <div class="option-desc">带学校公章，可用于官方用途</div>
                        </div>
                    </div>
                    <div class="option-item" onclick="selectOption(this, 'type', 'unofficial')">
                        <div class="option-radio"></div>
                        <div class="option-content">
                            <div class="option-text">非官方成绩单</div>
                            <div class="option-desc">仅供个人查看，无法律效力</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成绩范围 -->
            <div class="option-group">
                <div class="option-label">成绩范围</div>
                <div class="option-items">
                    <div class="option-item selected" onclick="selectOption(this, 'range', 'all')">
                        <div class="option-radio"></div>
                        <div class="option-content">
                            <div class="option-text">全部成绩</div>
                            <div class="option-desc">包含所有学期的成绩记录</div>
                        </div>
                    </div>
                    <div class="option-item" onclick="selectOption(this, 'range', 'passed')">
                        <div class="option-radio"></div>
                        <div class="option-content">
                            <div class="option-text">及格成绩</div>
                            <div class="option-desc">仅显示及格的课程成绩</div>
                        </div>
                    </div>
                    <div class="option-item" onclick="selectOption(this, 'range', 'custom')">
                        <div class="option-radio"></div>
                        <div class="option-content">
                            <div class="option-text">自定义范围</div>
                            <div class="option-desc">选择特定学期的成绩</div>
                        </div>
                    </div>
                </div>

                <!-- 自定义范围选择器 -->
                <div class="range-selector" id="customRange" style="display: none;">
                    <select class="range-input" id="startTerm">
                        <option value="">起始学期</option>
                    </select>
                    <select class="range-input" id="endTerm">
                        <option value="">结束学期</option>
                    </select>
                </div>
            </div>

            <!-- 语言选择 -->
            <div class="option-group">
                <div class="option-label">语言版本</div>
                <div class="option-items">
                    <div class="option-item selected" onclick="selectOption(this, 'language', 'chinese')">
                        <div class="option-radio"></div>
                        <div class="option-content">
                            <div class="option-text">中文版</div>
                            <div class="option-desc">标准中文成绩单</div>
                        </div>
                    </div>
                    <div class="option-item" onclick="selectOption(this, 'language', 'english')">
                        <div class="option-radio"></div>
                        <div class="option-content">
                            <div class="option-text">英文版</div>
                            <div class="option-desc">英文成绩单，用于出国申请</div>
                        </div>
                    </div>
                    <div class="option-item" onclick="selectOption(this, 'language', 'bilingual')">
                        <div class="option-radio"></div>
                        <div class="option-content">
                            <div class="option-text">中英文对照</div>
                            <div class="option-desc">中英文双语成绩单</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预览区域 -->
        <div class="preview-section">
            <div class="preview-title">
                <i class="ace-icon fa fa-eye"></i>
                <span>成绩单预览</span>
            </div>
            <div class="preview-content" id="previewContent">
                <div class="preview-placeholder">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <span>点击预览按钮查看成绩单</span>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions-section">
            <div class="actions-title">操作选项</div>
            <div class="action-buttons">
                <button class="btn-mobile btn-preview flex-1" onclick="previewTranscript();">
                    <i class="ace-icon fa fa-eye"></i> 预览
                </button>
                <button class="btn-mobile btn-print flex-1" onclick="printTranscript();">
                    <i class="ace-icon fa fa-print"></i> 打印
                </button>
                <button class="btn-mobile btn-download flex-1" onclick="downloadTranscript();">
                    <i class="ace-icon fa fa-download"></i> 下载
                </button>
            </div>
        </div>

        <!-- 温馨提示 -->
        <div class="tips-section">
            <div class="tips-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>重要提示</span>
            </div>
            <div class="tip-item">
                官方成绩单需要到教务处盖章后方可用于正式场合。
            </div>
            <div class="tip-item">
                英文成绩单翻译以学校官方版本为准，建议提前申请。
            </div>
            <div class="tip-item">
                成绩单打印后请妥善保管，遗失需重新申请。
            </div>
            <div class="tip-item">
                如发现成绩有误，请及时联系教务处核实修正。
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let printOptions = {
            type: 'official',
            range: 'all',
            language: 'chinese',
            startTerm: '',
            endTerm: ''
        };
        let studentInfo = {};
        let scoreData = [];
        let availableTerms = [];

        $(function() {
            initPage();
            loadStudentInfo();
            loadAvailableTerms();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 选择选项
        function selectOption(element, optionType, value) {
            // 移除同组其他选项的选中状态
            $(element).siblings('.option-item').removeClass('selected');
            // 添加当前选项的选中状态
            $(element).addClass('selected');

            // 更新选项值
            printOptions[optionType] = value;

            // 特殊处理自定义范围
            if (optionType === 'range') {
                if (value === 'custom') {
                    $('#customRange').show();
                } else {
                    $('#customRange').hide();
                }
            }
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/transcript/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 加载可用学期
        function loadAvailableTerms() {
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/transcript/getAvailableTerms",
                type: "post",
                dataType: "json",
                success: function(data) {
                    availableTerms = data.terms || [];
                    renderTermOptions();
                },
                error: function() {
                    console.log('加载学期列表失败');
                }
            });
        }

        // 渲染学期选项
        function renderTermOptions() {
            const startSelect = $('#startTerm');
            const endSelect = $('#endTerm');

            startSelect.find('option:not(:first)').remove();
            endSelect.find('option:not(:first)').remove();

            availableTerms.forEach(term => {
                const option = `<option value="${term.id}">${term.name}</option>`;
                startSelect.append(option);
                endSelect.append(option);
            });
        }

        // 预览成绩单
        function previewTranscript() {
            // 更新自定义范围选项
            if (printOptions.range === 'custom') {
                printOptions.startTerm = $('#startTerm').val();
                printOptions.endTerm = $('#endTerm').val();

                if (!printOptions.startTerm || !printOptions.endTerm) {
                    showError('请选择起始和结束学期');
                    return;
                }
            }

            // 加载成绩数据
            loadScoreData(function() {
                generateTranscriptPreview();
            });
        }

        // 加载成绩数据
        function loadScoreData(callback) {
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/transcript/getScoreData",
                type: "post",
                data: printOptions,
                dataType: "json",
                success: function(data) {
                    scoreData = data.scores || [];
                    if (callback) callback();
                },
                error: function() {
                    showError('加载成绩数据失败');
                }
            });
        }

        // 生成成绩单预览
        function generateTranscriptPreview() {
            const language = printOptions.language;
            let transcriptHtml = '';

            if (language === 'chinese' || language === 'bilingual') {
                transcriptHtml += generateChineseTranscript();
            }

            if (language === 'english' || language === 'bilingual') {
                if (language === 'bilingual') {
                    transcriptHtml += '<div style="page-break-before: always;"></div>';
                }
                transcriptHtml += generateEnglishTranscript();
            }

            $('#previewContent').html(transcriptHtml);
        }

        // 生成中文成绩单
        function generateChineseTranscript() {
            const isOfficial = printOptions.type === 'official';

            let html = `
                <div class="transcript-preview">
                    <div class="transcript-header">
                        <div class="transcript-title">${studentInfo.schoolName || '学校名称'}</div>
                        <div class="transcript-subtitle">学生成绩单</div>
                        ${isOfficial ? '<div style="color: red; font-size: 12px; margin-top: 5px;">（官方版本）</div>' : ''}
                    </div>

                    <div class="student-info">
                        <div class="info-item">
                            <span class="info-label">姓名:</span>
                            <span>${studentInfo.name || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">学号:</span>
                            <span>${studentInfo.studentId || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">专业:</span>
                            <span>${studentInfo.major || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">班级:</span>
                            <span>${studentInfo.className || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">入学时间:</span>
                            <span>${studentInfo.enrollmentDate || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">学制:</span>
                            <span>${studentInfo.duration || ''}年</span>
                        </div>
                    </div>

                    <table class="score-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>课程代码</th>
                                <th>课程名称</th>
                                <th>学分</th>
                                <th>成绩</th>
                                <th>绩点</th>
                                <th>学期</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${generateScoreRows()}
                        </tbody>
                    </table>

                    <div class="transcript-footer">
                        <div>
                            <strong>总学分: ${calculateTotalCredits()}</strong><br>
                            <strong>平均分: ${calculateAverageScore()}</strong><br>
                            <strong>平均绩点: ${calculateAverageGPA()}</strong>
                        </div>
                        <div style="text-align: right;">
                            <div>打印时间: ${new Date().toLocaleString()}</div>
                            ${isOfficial ? '<div style="margin-top: 20px;">教务处（盖章）</div>' : ''}
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        // 生成英文成绩单
        function generateEnglishTranscript() {
            const isOfficial = printOptions.type === 'official';

            let html = `
                <div class="transcript-preview">
                    <div class="transcript-header">
                        <div class="transcript-title">${studentInfo.schoolNameEn || 'University Name'}</div>
                        <div class="transcript-subtitle">Official Transcript</div>
                        ${isOfficial ? '<div style="color: red; font-size: 12px; margin-top: 5px;">(Official Version)</div>' : ''}
                    </div>

                    <div class="student-info">
                        <div class="info-item">
                            <span class="info-label">Name:</span>
                            <span>${studentInfo.nameEn || studentInfo.name || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Student ID:</span>
                            <span>${studentInfo.studentId || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Major:</span>
                            <span>${studentInfo.majorEn || studentInfo.major || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Class:</span>
                            <span>${studentInfo.classNameEn || studentInfo.className || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Enrollment:</span>
                            <span>${studentInfo.enrollmentDate || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Duration:</span>
                            <span>${studentInfo.duration || ''} Years</span>
                        </div>
                    </div>

                    <table class="score-table">
                        <thead>
                            <tr>
                                <th>No.</th>
                                <th>Course Code</th>
                                <th>Course Name</th>
                                <th>Credits</th>
                                <th>Grade</th>
                                <th>GPA</th>
                                <th>Term</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${generateScoreRowsEn()}
                        </tbody>
                    </table>

                    <div class="transcript-footer">
                        <div>
                            <strong>Total Credits: ${calculateTotalCredits()}</strong><br>
                            <strong>Average Score: ${calculateAverageScore()}</strong><br>
                            <strong>GPA: ${calculateAverageGPA()}</strong>
                        </div>
                        <div style="text-align: right;">
                            <div>Print Date: ${new Date().toLocaleDateString('en-US')}</div>
                            ${isOfficial ? '<div style="margin-top: 20px;">Academic Affairs Office (Seal)</div>' : ''}
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        // 生成成绩行（中文）
        function generateScoreRows() {
            if (!scoreData || scoreData.length === 0) {
                return '<tr><td colspan="7" style="text-align: center; color: #999;">暂无成绩数据</td></tr>';
            }

            return scoreData.map((score, index) => {
                return `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${score.courseCode}</td>
                        <td class="course-name">${score.courseName}</td>
                        <td>${score.credit}</td>
                        <td>${score.score}</td>
                        <td>${score.gpa || '-'}</td>
                        <td>${score.termName}</td>
                    </tr>
                `;
            }).join('');
        }

        // 生成成绩行（英文）
        function generateScoreRowsEn() {
            if (!scoreData || scoreData.length === 0) {
                return '<tr><td colspan="7" style="text-align: center; color: #999;">No score data available</td></tr>';
            }

            return scoreData.map((score, index) => {
                return `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${score.courseCode}</td>
                        <td class="course-name">${score.courseNameEn || score.courseName}</td>
                        <td>${score.credit}</td>
                        <td>${score.score}</td>
                        <td>${score.gpa || '-'}</td>
                        <td>${score.termNameEn || score.termName}</td>
                    </tr>
                `;
            }).join('');
        }

        // 计算总学分
        function calculateTotalCredits() {
            if (!scoreData || scoreData.length === 0) return '0';

            const total = scoreData.reduce((sum, score) => {
                return sum + parseFloat(score.credit || 0);
            }, 0);

            return total.toFixed(1);
        }

        // 计算平均分
        function calculateAverageScore() {
            if (!scoreData || scoreData.length === 0) return '0';

            const validScores = scoreData.filter(score => score.score && !isNaN(score.score));
            if (validScores.length === 0) return '0';

            const total = validScores.reduce((sum, score) => {
                return sum + parseFloat(score.score);
            }, 0);

            return (total / validScores.length).toFixed(2);
        }

        // 计算平均绩点
        function calculateAverageGPA() {
            if (!scoreData || scoreData.length === 0) return '0.00';

            const validGPAs = scoreData.filter(score => score.gpa && !isNaN(score.gpa));
            if (validGPAs.length === 0) return '0.00';

            const total = validGPAs.reduce((sum, score) => {
                return sum + parseFloat(score.gpa);
            }, 0);

            return (total / validGPAs.length).toFixed(2);
        }

        // 打印成绩单
        function printTranscript() {
            if ($('#previewContent .transcript-preview').length === 0) {
                showError('请先预览成绩单');
                return;
            }

            // 确认打印
            const message = printOptions.type === 'official' ?
                '确定要打印官方成绩单吗？打印后请到教务处盖章。' :
                '确定要打印成绩单吗？';

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doPrint();
                    }
                });
            } else {
                if (confirm(message)) {
                    doPrint();
                }
            }
        }

        // 执行打印
        function doPrint() {
            // 记录打印日志
            recordPrintLog();

            // 执行打印
            window.print();
        }

        // 下载成绩单
        function downloadTranscript() {
            if ($('#previewContent .transcript-preview').length === 0) {
                showError('请先预览成绩单');
                return;
            }

            // 生成PDF下载
            generatePDF();
        }

        // 生成PDF
        function generatePDF() {
            const options = {
                ...printOptions,
                format: 'pdf'
            };

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/transcript/generatePDF",
                type: "post",
                data: options,
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data) {
                    // 创建下载链接
                    const blob = new Blob([data], { type: 'application/pdf' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `成绩单_${studentInfo.name || 'transcript'}_${new Date().toISOString().slice(0, 10)}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showSuccess('成绩单下载成功');
                },
                error: function() {
                    showError('生成PDF失败，请重试');
                }
            });
        }

        // 记录打印日志
        function recordPrintLog() {
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/transcript/recordPrintLog",
                type: "post",
                data: {
                    type: printOptions.type,
                    range: printOptions.range,
                    language: printOptions.language,
                    printTime: new Date().toISOString()
                },
                success: function() {
                    console.log('打印日志记录成功');
                },
                error: function() {
                    console.log('打印日志记录失败');
                }
            });
        }

        // 刷新数据
        function refreshData() {
            loadStudentInfo();
            loadAvailableTerms();

            // 清空预览
            $('#previewContent').html(`
                <div class="preview-placeholder">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <span>点击预览按钮查看成绩单</span>
                </div>
            `);
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 监听自定义范围选择变化
        $('#startTerm, #endTerm').change(function() {
            printOptions.startTerm = $('#startTerm').val();
            printOptions.endTerm = $('#endTerm').val();
        });
    </script>
</body>
</html>
