<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证注册</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证注册页面样式 */
        .register-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .register-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .status-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .status-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
        }
        
        .status-icon.registered {
            background: var(--success-color);
        }
        
        .status-icon.pending {
            background: var(--warning-color);
        }
        
        .status-icon.unregistered {
            background: var(--error-color);
        }
        
        .status-content {
            flex: 1;
        }
        
        .status-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .status-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .register-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .form-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-section {
            margin-bottom: var(--margin-lg);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-xs);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .photo-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            background: var(--bg-tertiary);
        }
        
        .photo-upload:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .photo-preview {
            width: 120px;
            height: 150px;
            border-radius: 6px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-disabled);
            font-size: var(--font-size-h3);
            margin: 0 auto var(--margin-md);
            border: 2px solid var(--border-primary);
            overflow: hidden;
        }
        
        .upload-text {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            font-weight: 500;
        }
        
        .upload-hint {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
        }
        
        .btn-register {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-reset {
            background: var(--text-disabled);
            color: white;
        }
        
        .register-guide {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .guide-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .guide-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .guide-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .guide-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: var(--margin-md);
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .guide-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            margin-right: var(--margin-sm);
            flex-shrink: 0;
        }
        
        .guide-content {
            flex: 1;
        }
        
        .guide-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: var(--line-height-base);
        }
        
        .register-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-item.success {
            border-left: 4px solid var(--success-color);
        }
        
        .history-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .history-item.failed {
            border-left: 4px solid var(--error-color);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-success {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-failed {
            background: var(--error-color);
            color: white;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证注册</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="register-header">
            <div class="header-title">学生证注册</div>
            <div class="header-subtitle">注册学生证信息和上传照片</div>
        </div>

        <!-- 注册状态 -->
        <div class="register-status">
            <div class="status-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>注册状态</span>
            </div>

            <div class="status-card" id="registerStatusCard">
                <!-- 状态信息将动态填充 -->
            </div>
        </div>

        <!-- 注册表单 -->
        <div class="register-form" id="registerForm">
            <div class="form-title">
                <i class="ace-icon fa fa-edit"></i>
                <span>学生证注册</span>
            </div>

            <div class="form-section">
                <div class="section-title">个人信息</div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">性别</div>
                    <select class="form-input" id="gender">
                        <option value="">请选择性别</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">身份证号</div>
                    <input type="text" class="form-input" id="idCard" placeholder="请输入身份证号">
                </div>

                <div class="form-group">
                    <div class="form-label">民族</div>
                    <input type="text" class="form-input" id="ethnicity" placeholder="请输入民族">
                </div>

                <div class="form-group">
                    <div class="form-label">出生日期</div>
                    <input type="date" class="form-input" id="birthDate">
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">学籍信息</div>

                <div class="form-group">
                    <div class="form-label">专业</div>
                    <input type="text" class="form-input" id="major" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">班级</div>
                    <input type="text" class="form-input" id="className" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">入学年份</div>
                    <input type="text" class="form-input" id="enrollmentYear" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">学制</div>
                    <select class="form-input" id="duration">
                        <option value="">请选择学制</option>
                        <option value="4">四年制</option>
                        <option value="5">五年制</option>
                        <option value="3">三年制</option>
                    </select>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">联系信息</div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">家庭地址</div>
                    <textarea class="form-input" id="address" placeholder="请输入家庭地址" style="min-height: 60px; resize: vertical;"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">紧急联系人</div>
                    <input type="text" class="form-input" id="emergencyContact" placeholder="请输入紧急联系人姓名">
                </div>

                <div class="form-group">
                    <div class="form-label">紧急联系电话</div>
                    <input type="tel" class="form-input" id="emergencyPhone" placeholder="请输入紧急联系电话">
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">照片上传</div>

                <div class="form-group">
                    <div class="form-label">学生证照片</div>
                    <div class="photo-upload" onclick="selectPhoto();">
                        <div class="photo-preview" id="photoPreview">
                            <i class="ace-icon fa fa-camera"></i>
                        </div>
                        <div class="upload-text">点击上传照片</div>
                        <div class="upload-hint">
                            请上传近期免冠照片<br>
                            格式：JPG、PNG<br>
                            大小：不超过2MB<br>
                            尺寸：建议295×413像素
                        </div>
                    </div>
                    <input type="file" id="photoInput" accept="image/*" style="display: none;">
                </div>
            </div>

            <div class="form-actions">
                <button class="btn-mobile btn-reset flex-1" onclick="resetForm();">重置</button>
                <button class="btn-mobile btn-register flex-1" onclick="submitRegistration();">提交注册</button>
            </div>
        </div>

        <!-- 注册指南 -->
        <div class="register-guide">
            <div class="guide-title">
                <i class="ace-icon fa fa-question-circle"></i>
                <span>注册指南</span>
            </div>

            <ul class="guide-list">
                <li class="guide-item">
                    <div class="guide-number">1</div>
                    <div class="guide-content">
                        <div class="guide-text">填写完整的个人信息，确保信息准确无误</div>
                    </div>
                </li>
                <li class="guide-item">
                    <div class="guide-number">2</div>
                    <div class="guide-content">
                        <div class="guide-text">上传符合要求的证件照片，照片将用于制作学生证</div>
                    </div>
                </li>
                <li class="guide-item">
                    <div class="guide-number">3</div>
                    <div class="guide-content">
                        <div class="guide-text">提交注册申请后，等待审核通过</div>
                    </div>
                </li>
                <li class="guide-item">
                    <div class="guide-number">4</div>
                    <div class="guide-content">
                        <div class="guide-text">审核通过后，可到指定地点领取学生证</div>
                    </div>
                </li>
            </ul>
        </div>

        <!-- 注册历史 -->
        <div class="register-history" id="registerHistory" style="display: none;">
            <div class="history-header">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>注册记录</span>
                </div>
                <div class="history-count" id="historyCount">0</div>
            </div>

            <div id="historyItems">
                <!-- 注册记录将动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let studentInfo = {};
        let registerStatus = {};
        let historyData = [];
        let selectedPhoto = null;

        $(function() {
            initPage();
            loadStudentInfo();
            loadRegisterStatus();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 照片选择事件
            $('#photoInput').change(function() {
                handlePhotoSelect(this.files[0]);
            });
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/studentCardRegister/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data.student || {};
                    fillStudentInfo();
                },
                error: function() {
                    // 使用模拟数据
                    studentInfo = {
                        studentId: '2021001001',
                        name: '张三',
                        major: '计算机科学与技术',
                        className: '计科2021-1班',
                        enrollmentYear: '2021'
                    };
                    fillStudentInfo();
                }
            });
        }

        // 填充学生信息
        function fillStudentInfo() {
            $('#studentId').val(studentInfo.studentId || '2021001001');
            $('#studentName').val(studentInfo.name || '张三');
            $('#major').val(studentInfo.major || '计算机科学与技术');
            $('#className').val(studentInfo.className || '计科2021-1班');
            $('#enrollmentYear').val(studentInfo.enrollmentYear || '2021');
        }

        // 加载注册状态
        function loadRegisterStatus() {
            showLoading(true);

            $.ajax({
                url: "/student/studentCardRegister/getRegisterStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    registerStatus = data.status || {};
                    renderRegisterStatus();
                    updateFormVisibility();
                    showLoading(false);
                },
                error: function() {
                    // 使用模拟数据
                    registerStatus = {
                        status: 'unregistered',
                        description: '尚未注册学生证，请填写信息并上传照片'
                    };
                    renderRegisterStatus();
                    updateFormVisibility();
                    showLoading(false);
                }
            });
        }

        // 渲染注册状态
        function renderRegisterStatus() {
            const status = registerStatus.status || 'unregistered';
            const iconClass = getStatusIconClass(status);
            const statusText = getStatusText(status);
            const metaText = registerStatus.description || '请完成学生证注册';

            const statusHtml = `
                <div class="status-icon ${status}">
                    <i class="ace-icon fa ${iconClass}"></i>
                </div>
                <div class="status-content">
                    <div class="status-text">${statusText}</div>
                    <div class="status-meta">${metaText}</div>
                </div>
            `;

            $('#registerStatusCard').html(statusHtml);
        }

        // 获取状态图标类
        function getStatusIconClass(status) {
            switch(status) {
                case 'registered': return 'fa-check';
                case 'pending': return 'fa-clock-o';
                case 'unregistered': return 'fa-times';
                default: return 'fa-question';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'registered': return '已注册';
                case 'pending': return '审核中';
                case 'unregistered': return '未注册';
                default: return '未知状态';
            }
        }

        // 更新表单可见性
        function updateFormVisibility() {
            const status = registerStatus.status || 'unregistered';

            if (status === 'registered') {
                $('#registerForm').hide();
                loadHistoryData();
                $('#registerHistory').show();
            } else {
                $('#registerForm').show();
                $('#registerHistory').hide();
            }
        }

        // 选择照片
        function selectPhoto() {
            $('#photoInput').click();
        }

        // 处理照片选择
        function handlePhotoSelect(file) {
            if (!file) return;

            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showError('请选择图片文件');
                return;
            }

            // 检查文件大小
            if (file.size > 2 * 1024 * 1024) {
                showError('照片大小不能超过2MB');
                return;
            }

            selectedPhoto = file;

            // 预览照片
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#photoPreview').html(`<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover;">`);
            };
            reader.readAsDataURL(file);
        }

        // 重置表单
        function resetForm() {
            const message = '确定要重置表单吗？所有填写的信息将被清空。';

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doResetForm();
                    }
                });
            } else {
                if (confirm(message)) {
                    doResetForm();
                }
            }
        }

        // 执行重置表单
        function doResetForm() {
            // 清空可编辑字段
            $('#gender').val('');
            $('#idCard').val('');
            $('#ethnicity').val('');
            $('#birthDate').val('');
            $('#duration').val('');
            $('#phone').val('');
            $('#address').val('');
            $('#emergencyContact').val('');
            $('#emergencyPhone').val('');

            // 重置照片
            selectedPhoto = null;
            $('#photoPreview').html('<i class="ace-icon fa fa-camera"></i>');
            $('#photoInput').val('');
        }

        // 提交注册
        function submitRegistration() {
            const formData = {
                gender: $('#gender').val(),
                idCard: $('#idCard').val().trim(),
                ethnicity: $('#ethnicity').val().trim(),
                birthDate: $('#birthDate').val(),
                duration: $('#duration').val(),
                phone: $('#phone').val().trim(),
                address: $('#address').val().trim(),
                emergencyContact: $('#emergencyContact').val().trim(),
                emergencyPhone: $('#emergencyPhone').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            const message = '确定要提交学生证注册申请吗？';

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitRegistration(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitRegistration(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.gender) {
                showError('请选择性别');
                return false;
            }

            if (!formData.idCard) {
                showError('请输入身份证号');
                return false;
            }

            if (!formData.ethnicity) {
                showError('请输入民族');
                return false;
            }

            if (!formData.birthDate) {
                showError('请选择出生日期');
                return false;
            }

            if (!formData.duration) {
                showError('请选择学制');
                return false;
            }

            if (!formData.phone) {
                showError('请输入联系电话');
                return false;
            }

            if (!formData.address) {
                showError('请输入家庭地址');
                return false;
            }

            if (!formData.emergencyContact) {
                showError('请输入紧急联系人');
                return false;
            }

            if (!formData.emergencyPhone) {
                showError('请输入紧急联系电话');
                return false;
            }

            if (!selectedPhoto) {
                showError('请上传学生证照片');
                return false;
            }

            // 验证身份证号格式
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idCardRegex.test(formData.idCard)) {
                showError('请输入正确的身份证号');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            if (!phoneRegex.test(formData.emergencyPhone)) {
                showError('请输入正确的紧急联系电话');
                return false;
            }

            return true;
        }

        // 执行提交注册
        function doSubmitRegistration(formData) {
            const submitData = new FormData();

            // 添加表单数据
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            // 添加照片
            if (selectedPhoto) {
                submitData.append('photo', selectedPhoto);
            }

            $.ajax({
                url: "/student/studentCardRegister/submitRegistration",
                type: "post",
                data: submitData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('注册申请提交成功，请等待审核');
                        loadRegisterStatus(); // 重新加载状态
                    } else {
                        showError(data.message || '注册申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 加载历史数据
        function loadHistoryData() {
            $.ajax({
                url: "/student/studentCardRegister/getHistoryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    historyData = data.history || [];
                    renderHistoryData();
                },
                error: function() {
                    // 使用模拟数据
                    historyData = [
                        {
                            id: '1',
                            type: '学生证注册',
                            status: 'success',
                            applyTime: '2024-01-15',
                            processTime: '2024-01-20'
                        }
                    ];
                    renderHistoryData();
                }
            });
        }

        // 渲染历史数据
        function renderHistoryData() {
            $('#historyCount').text(historyData.length);

            const container = $('#historyItems');
            container.empty();

            if (historyData.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无注册记录
                    </div>
                `);
                return;
            }

            historyData.forEach(item => {
                const historyHtml = createHistoryItem(item);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(item) {
            const status = item.status || 'pending';
            const statusClass = getHistoryStatusClass(status);
            const statusText = getHistoryStatusText(status);

            return `
                <div class="history-item ${status}" onclick="showHistoryDetail('${item.id}')">
                    <div class="history-basic">
                        <div class="history-type">${item.type}</div>
                        <div class="history-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(item.applyTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>处理时间:</span>
                            <span>${formatDate(item.processTime)}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取历史状态样式类
        function getHistoryStatusClass(status) {
            return `status-${status}`;
        }

        // 获取历史状态文本
        function getHistoryStatusText(status) {
            switch(status) {
                case 'success': return '成功';
                case 'pending': return '处理中';
                case 'failed': return '失败';
                default: return '未知';
            }
        }

        // 显示历史详情
        function showHistoryDetail(historyId) {
            const item = historyData.find(h => h.id === historyId);
            if (!item) return;

            let message = `注册详情\n\n`;
            message += `类型：${item.type}\n`;
            message += `状态：${getHistoryStatusText(item.status)}\n`;
            message += `申请时间：${formatDate(item.applyTime)}\n`;
            message += `处理时间：${formatDate(item.processTime)}\n`;

            if (item.comment) {
                message += `处理意见：${item.comment}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadStudentInfo();
            loadRegisterStatus();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
