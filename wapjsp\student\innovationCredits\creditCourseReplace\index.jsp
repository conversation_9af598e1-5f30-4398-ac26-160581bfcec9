<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学分课程替代申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学分课程替代申请页面样式 */
        .application-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .application-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .application-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-reviewing {
            background: var(--primary-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .status-cancelled {
            background: var(--text-disabled);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
            padding-top: var(--padding-sm);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-action {
            flex: 1;
            min-height: 36px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .fab-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            z-index: 100;
            transition: all var(--transition-base);
        }
        
        .fab-button:active {
            transform: scale(0.95);
        }
        
        .course-info {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学分课程替代申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 申请列表 -->
        <div id="applicationList">
            <!-- 动态加载内容 -->
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无申请记录</div>
            <button class="btn-mobile btn-primary" onclick="addApplication();" style="margin-top: 16px;">
                <i class="ace-icon fa fa-plus"></i>
                <span>添加申请</span>
            </button>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 浮动添加按钮 -->
        <button class="fab-button" onclick="addApplication();" id="fabButton">
            <i class="ace-icon fa fa-plus"></i>
        </button>
    </div>

    <script>
        // 全局变量
        let applicationData = [];
        let currentPage = 1;
        let hasMore = true;
        let tokenValue = '${token_in_session}';

        $(function() {
            initPage();
            loadData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载数据
        function loadData(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }
            
            showLoading(true);
            
            $.ajax({
                url: "/student/innovationCredits/creditCourseReplace/queryPage",
                type: "post",
                data: "pageNum=" + page + "&pageSize=20",
                dataType: "json",
                success: function(data) {
                    if (data.data && data.data.records && data.data.records.length > 0) {
                        if (reset) {
                            applicationData = data.data.records;
                        } else {
                            applicationData = applicationData.concat(data.data.records);
                        }
                        
                        hasMore = applicationData.length < data.data.pageContext.totalCount;
                        renderApplicationList();
                        showFabButton(true);
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            applicationData = [];
                            renderApplicationList();
                        }
                        showFabButton(false);
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationList() {
            const container = $('#applicationList');
            container.empty();
            
            applicationData.forEach(function(item, index) {
                const itemHtml = createApplicationItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建申请项目HTML
        function createApplicationItem(item, index) {
            const status = getStatusInfo(item.SQZT);
            const canEdit = item.SQZT == 0; // 暂存状态可编辑
            const canSubmit = item.SQZT == 0; // 暂存状态可提交
            const canDelete = item.SQZT == 0; // 暂存状态可删除

            return `
                <div class="application-item">
                    <div class="application-header">
                        <div class="application-title">申请 #${index + 1}</div>
                        <div class="application-time">${item.SQSJSTR || '-'}</div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">申请状态</span>
                            <span class="detail-value">
                                <span class="status-badge ${status.class}">${status.text}</span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">学年学期</span>
                            <span class="detail-value">${item.ZXJXJHM || '-'}</span>
                        </div>
                        <div class="detail-item full-width">
                            <span class="detail-label">申请课程</span>
                            <span class="detail-value">${item.KC || '-'}</span>
                        </div>
                    </div>

                    ${item.KC ? `
                        <div class="course-info">
                            申请课程：${item.KC}
                        </div>
                    ` : ''}

                    <div class="action-buttons">
                        ${canSubmit ? `
                            <button class="btn-action btn-submit" onclick="submitApplication('${item.SQID}');">
                                <i class="ace-icon fa fa-check"></i>
                                <span>提交</span>
                            </button>
                        ` : ''}

                        ${canEdit ? `
                            <button class="btn-action btn-edit" onclick="editApplication('${item.SQID}');">
                                <i class="ace-icon fa fa-edit"></i>
                                <span>修改</span>
                            </button>
                        ` : `
                            <button class="btn-action btn-view" onclick="viewApplication('${item.SQID}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看</span>
                            </button>
                        `}

                        ${canDelete ? `
                            <button class="btn-action btn-delete" onclick="deleteApplication('${item.SQID}');">
                                <i class="ace-icon fa fa-trash"></i>
                                <span>删除</span>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(status) {
            const statusMap = {
                '-2': { text: '取消[终审后]', class: 'status-cancelled' },
                '-3': { text: '撤回[终审前]', class: 'status-cancelled' },
                '-1': { text: '拒绝', class: 'status-rejected' },
                '0': { text: '暂存', class: 'status-draft' },
                '1': { text: '待审批', class: 'status-pending' },
                '2': { text: '审批中', class: 'status-reviewing' },
                '3': { text: '批准', class: 'status-approved' }
            };
            return statusMap[status] || { text: '未知', class: 'status-draft' };
        }

        // 添加申请
        function addApplication() {
            if (parent && parent.addTab) {
                parent.addTab('添加申请', '/student/innovationCredits/creditCourseReplace/add?sqid=');
            } else {
                window.location.href = '/student/innovationCredits/creditCourseReplace/add?sqid=';
            }
        }

        // 编辑申请
        function editApplication(sqid) {
            if (parent && parent.addTab) {
                parent.addTab('修改申请', '/student/innovationCredits/creditCourseReplace/add?sqid=' + sqid);
            } else {
                window.location.href = '/student/innovationCredits/creditCourseReplace/add?sqid=' + sqid;
            }
        }

        // 查看申请
        function viewApplication(sqid) {
            if (parent && parent.addTab) {
                parent.addTab('查看申请', '/student/innovationCredits/creditCourseReplace/view?sqid=' + sqid);
            } else {
                window.location.href = '/student/innovationCredits/creditCourseReplace/view?sqid=' + sqid;
            }
        }

        // 提交申请
        function submitApplication(sqid) {
            if (!confirm('确认提交当前申请？')) {
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/creditCourseReplace/doSubmit",
                type: "post",
                data: {
                    tokenValue: tokenValue,
                    sqid: sqid
                },
                dataType: "json",
                success: function(data) {
                    tokenValue = data.token;
                    if (data.result === "ok") {
                        showSuccess("提交成功！");
                        loadData(1, true);
                    } else {
                        showError(data.result || "提交失败");
                    }
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 删除申请
        function deleteApplication(sqid) {
            if (!confirm('确认删除当前申请？')) {
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/creditCourseReplace/doDel",
                type: "post",
                data: {
                    tokenValue: tokenValue,
                    sqid: sqid
                },
                dataType: "json",
                success: function(data) {
                    tokenValue = data.token;
                    if (data.result === "ok") {
                        showSuccess("删除成功！");
                        loadData(1, true);
                    } else {
                        showError("删除失败！");
                    }
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 刷新数据
        function refreshData() {
            loadData(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#applicationList').hide();
            } else {
                $('#emptyState').hide();
                $('#applicationList').show();
            }
        }

        // 显示浮动按钮
        function showFabButton(show) {
            if (show) {
                $('#fabButton').show();
            } else {
                $('#fabButton').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 无限滚动加载
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    currentPage++;
                    loadData(currentPage, false);
                }
            }
        });
    </script>
</body>
</html>
