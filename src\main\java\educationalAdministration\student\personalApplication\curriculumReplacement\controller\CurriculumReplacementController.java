package educationalAdministration.student.personalApplication.curriculumReplacement.controller;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.urpSoft.business.utils.UrpResult;
import com.urpSoft.core.data.query.component.QueryInfo;
import com.urpSoft.core.pagination.page.Page;
import com.urpSoft.core.pagination.service.IPageService;
import com.urpSoft.core.service.BaseService;
import com.urpSoft.core.util.AuthUtil;
import com.urpSoft.core.util.CSRFToken;

import educationalAdministration.dictionary.entity.EaApplyType;
import educationalAdministration.dictionary.entity.StudentRollView;
import educationalAdministration.dictionary.entity.SysYwhdkzb;
import educationalAdministration.student.common.service.CommonService;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.individualApplication.applyCommon.service.ApplyCommonService;
import educationalAdministration.student.personalApplication.curriculumReplacement.entity.CjKctdSqb;
import educationalAdministration.student.personalApplication.curriculumReplacement.service.CurriculumReplacementService;
import educationalAdministration.student.personalManagement.entity.JhFajhkcbView;
import educationalAdministration.student.personalManagement.entity.JhFakzsView;
import educationalAdministration.student.personalManagement.entity.PxCsb;


@Controller
public class CurriculumReplacementController {


	@Resource
	private IPageService pageService;

	private CSRFToken csrfToken = CSRFToken.getInstance();

	@Resource
	private CurriculumReplacementService curriculumReplacementService;

	@Resource
	private ApplyCommonService applyCommonService;

	@Resource
	private CommonService commonService;

	@Resource
	private BaseService baseService;

	@SuppressWarnings("unused")
	@ModelAttribute
	private void doGetCurrentUser() {
		AuthUtil.getCurrentUser();
	}

	/**
	 * 课程替代首页
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping(value = {"/student/personalManagement/personalApplication/curriculumReplacement/index",
	"/tjgydx/student/personalManagement/personalApplication/curriculumReplacement/index"})
	public String gotoIndex(Model model, HttpSession session, HttpServletRequest request) {
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		StudentRollView xsXjbView = baseService.queryEntityById(StudentRollView.class, xh);
		String schoolCode = commonService.queryParamValue();
		model.addAttribute("schoolCode", schoolCode);
		if("100007".equals(schoolCode)){
			if(!("01".equals(xsXjbView.getXjztdm())||"05".equals(xsXjbView.getXjztdm())||"07".equals(xsXjbView.getXjztdm()))){
				String errorMessage = "对不起，你没有学籍信息，不能申请课程替代！";
				model.addAttribute("errorInfo", errorMessage);
				return "system/error/printErrorMessage";
			}
		}else{
			if(!"是".equals(xsXjbView.getSfyxj())){
				String errorMessage = "对不起，你没有学籍信息，不能申请课程替代！";
				model.addAttribute("errorInfo", errorMessage);
				return "system/error/printErrorMessage";
			}
		}

		if(StringUtils.isBlank(xsXjbView.getXsh())){
			String errorMessage = "对不起，所属院系未维护，不能申请课程替代！";
			model.addAttribute("errorInfo", errorMessage);
			return "system/error/printErrorMessage";
		}

		PxCsb csb = CommonUtils.queryPxCsbById("kctdsp", "qygzl");
		String approvalProcess=(csb!=null&&"0".equals(csb.getCsz()))?"0":"1";
		model.addAttribute("approvalProcess", approvalProcess);

		int i=0;
		SysYwhdkzb ywhdkzb = baseService.queryEntityById(SysYwhdkzb.class, "10001");
		if(ywhdkzb!=null&&"1".equals(ywhdkzb.getQyf())){
			i = applyCommonService.queryTimeFrame("10001");
		}
		model.addAttribute("pxcsb", i);
		boolean mobile = false;
		if (CommonUtils.checkMobile(request)) {
			mobile = true;
		}
		model.addAttribute("mobile", mobile);
		return "student/personalManagement/personalApplication/curriculumReplacement/index";
	}

	/**
	 * 分页查询
	 * @param model
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value="/student/personalManagement/personalApplication/curriculumReplacement/index/getPage")
	@ResponseBody
	public UrpResult getPage(Model model,@RequestParam(defaultValue="1")int pageNum, @RequestParam(defaultValue="10")int pageSize){	
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String sql = "SELECT DISTINCT m.sqbh,m.xh,m.sqrq,m.tdlx,(select t.tdlxsm from code_tdlxb t where t.tdlx = m.tdlx) tdlxsm,m.sqzt," +
				"CASE m.tdlx WHEN '01' THEN (SELECT d.tdkch || ' | ' || pn.kcm(d.tdkch)||'【'||pkg_com.f_NNF(pn.kc_xf(d.tdkch))||'学分'||" +
				"decode(pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.tdkch, 1)),null,'',' | ' || pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.tdkch, 1)))" +
				"||'】' FROM cj_kctd_sqkcb d WHERE d.sqbh = m.sqbh AND ROWNUM = 1) " +
				"WHEN '03' THEN (SELECT d.tdkch || ' | ' || pn.kcm(d.tdkch)||'【'||pkg_com.f_NNF(pn.kc_xf(d.tdkch))||'学分'||" +
				"decode(pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.tdkch, 1)),null,'',' | ' || pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.tdkch, 1)))" +
				"||'】' FROM cj_kctd_sqkcb d WHERE d.sqbh = m.sqbh AND ROWNUM = 1) " +
				"ELSE (SELECT listagg(d.tdkch || ' | ' || pn.kcm(d.tdkch)||'【'||pkg_com.f_NNF(pn.kc_xf(d.tdkch))||'学分'||" +
				"decode(pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.tdkch, 1)),null,'',' | ' || pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.tdkch, 1)))" +
				"||'】;') WITHIN GROUP(ORDER BY d.tdkch) FROM cj_kctd_sqkcb d WHERE d.sqbh = m.sqbh group by d.sqbh) END AS tdkcm," +
				"CASE m.tdlx WHEN '01' THEN (SELECT d.kch || ' | ' || pn.kcm(d.kch)||'【'||pkg_com.f_NNF(pn.kc_xf(d.kch))||'学分'||" +
				"decode(pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.kch, 1)),null,'',' | ' || pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.kch, 1)))" +
				"||'】' FROM cj_kctd_sqkcb d WHERE d.sqbh = m.sqbh AND ROWNUM = 1) " +
				"WHEN '03' THEN (SELECT listagg(d.kch || ' | ' || pn.kcm(d.kch)||'【'||pkg_com.f_NNF(pn.kc_xf(d.kch))||'学分'||" +
				"decode(pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.kch, 1)),null,'',' | ' || pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.kch, 1)))" +
				"||'】;') WITHIN GROUP(ORDER BY d.kch) FROM cj_kctd_sqkcb d WHERE d.sqbh = m.sqbh group by d.sqbh) " +
				"ELSE (SELECT d.kch || ' | ' || pn.kcm(d.kch)||'【'||pkg_com.f_NNF(pn.kc_xf(d.kch))||'学分'||" +
				"decode(pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.kch, 1)),null,'',' | ' || pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj(m.xh, d.kch, 1)))" +
				"||'】' FROM cj_kctd_sqkcb d WHERE d.sqbh = m.sqbh AND ROWNUM = 1) END AS kcm,m.tdyym, (select t.tdyy from cj_kctd_tdyyb t where t.tdyym=m.tdyym) tdyy," +
				"m.sqyy,rownum rn FROM cj_kctd_sqb m where m.xh='"+xh+"' order by m.sqbh desc";
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page<Object> page = pageService.queryPageBySql(info);
		return UrpResult.ok(page);
	}

	/**
	 * 课程替代申请增加
	 * @param model
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/personalApplication/curriculumReplacement/addInfo")
	public String addInfoModel(Model model, HttpSession session, HttpServletRequest request){
		int i=0;
		SysYwhdkzb ywhdkzb = baseService.queryEntityById(SysYwhdkzb.class, "10001");
		if(ywhdkzb!=null&&"1".equals(ywhdkzb.getQyf())){
			i = applyCommonService.queryTimeFrame("10001");
		}
		if(i>0){
			PxCsb csb = CommonUtils.queryPxCsbById("kctdsp", "qygzl");
			String approvalProcess=(csb!=null&&"0".equals(csb.getCsz()))?"0":"1";
			model.addAttribute("approvalProcess", approvalProcess);
			String schoolCode = commonService.queryParamValue();
			model.addAttribute("schoolCode", schoolCode);
			if ("1".equals(approvalProcess)) {
				EaApplyType eaApplyType = applyCommonService.queryEntityById(EaApplyType.class, "10001");
				model.addAttribute("assigning", eaApplyType.getAssigning());
			}
			csb = commonService.queryPxCsbById("cjgl", "cjtdyy");
			model.addAttribute("cjtdyy", (csb==null||StringUtils.isBlank(csb.getCsz()))?"0":csb.getCsz());
			model.addAttribute("ywhdkzb", ywhdkzb);
			String studentId = AuthUtil.getCurrentUser().getIdNumber(); //获得用户信息
			List<Object[]> listAll = curriculumReplacementService.queryAllPyfa(studentId);
			model.addAttribute("listAll", listAll);
			boolean mobile = false;
			if (CommonUtils.checkMobile(request)) {
				mobile = true;
			}
			model.addAttribute("mobile", mobile);
			return "student/personalManagement/personalApplication/curriculumReplacement/addInfo";
		}else{
			String errorMessage = "对不起，当前申请不在开放期内！";
			model.addAttribute("errorInfo", errorMessage);
			return "system/error/printErrorMessage";
		}
	}

	/**
	 * 查询树节点方法
	 * @param model
	 * @param type
	 * @param id
	 * @return
	 */
	@RequestMapping("/student/personalManagement/personalApplication/curriculumReplacement/getTreeNodes")
	public @ResponseBody
	List<Map<String, Object>> getTreeNodes(HttpServletRequest request, 
			Model model, String type, String id,String fajhh,String parentIds){
		List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
		List<Object[]> nodes = null;
		String ids = null;
		String xh = AuthUtil.getCurrentUser().getIdNumber();//获取当前学生号
		if (StringUtils.isNotBlank(type)) {
			if ("root".equals(type)) {
				nodes = curriculumReplacementService.queryRootNode(fajhh);
			}  else if ("kzh".equals(type)) {
				List<JhFakzsView> fakzsList = curriculumReplacementService.findJhFakzsList(fajhh, id);
				if(fakzsList.size()>0){
					nodes=curriculumReplacementService.queryKzNode(fajhh, id);
				}else{
					nodes = curriculumReplacementService.queryKcNode(fajhh,id,xh);
				}
			} 
			for (Object[] node : nodes) {
				Map<String, Object> orgMap = new HashMap<String, Object>();
				orgMap.put("id",node[0].toString());
				orgMap.put("name",node[1].toString());
				orgMap.put("type",node[2].toString());
				List<JhFakzsView> fakzsList = curriculumReplacementService.findJhFakzsList(fajhh, node[0].toString());
				if(fakzsList.size()>0){
					orgMap.put("isParent",true);
				}else{
					List<JhFajhkcbView> fajhkcList = curriculumReplacementService.findJhFajhkcList(fajhh, node[0].toString());
					if(fajhkcList.size()>0){
						orgMap.put("isParent",true);
					}else{
						orgMap.put("isParent",false);
					}
				}
				orgMap.put("title",node.length > 4 ? node[4].toString() : "" );
				orgMap.put("param",node.length > 5 ? node[5].toString() : "" );
				orgMap.put("parentIds",ids);
				resultList.add(orgMap);
			}
		} else {
			Map<String, Object> orgMap = new HashMap<String, Object>();
			orgMap.put("id", fajhh);
			orgMap.put("name", "<i class=\"ace-icon fa fa-home home-icon\" style=\"font-size: 17px;\"></i>"+"方案计划课程列表");
			orgMap.put("title","方案计划课程列表");
			orgMap.put("type","root");
			orgMap.put("isParent",true);
			orgMap.put("open",true);
			resultList.add(orgMap);
		}
		return resultList;
	}

	/**
	 * 查询替代课程
	 * @param model
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value="/student/personalManagement/personalApplication/curriculumReplacement/index/queryCurriculumList")
	@ResponseBody
	public Map<String,Object> queryCurriculumList(Model model,String kclx,String fajhh,String fakzh,String kch,String kcm){	
		Map<String,Object> map = new HashMap<String, Object >();
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		List<Object[]> studentScoreList = new ArrayList<Object[]>();
		if("fa_kc".equals(kclx)){
			studentScoreList = curriculumReplacementService.queryfajhkcb(fajhh, fakzh, xh, kch, kcm);
		}else{
			studentScoreList = curriculumReplacementService.queryStudentScore(xh,kch,kcm);
		}
		map.put("studentScoreList", studentScoreList);
		return map;
	}

	/**
	 * 保存替代课程申请
	 * @param model
	 * @param pageNum
	 * @param pageSize
	 * @param tdlx
	 * @param type
	 * @return
	 */
	@RequestMapping(value="/student/personalManagement/personalApplication/curriculumReplacement/index/saveCurriculumInfo")
	@ResponseBody
	public UrpResult saveCurriculumInfo(Model model,HttpServletRequest request,HttpSession session,String state,@RequestBody List<JSONObject> list){	
		Map<String,Object> map = new HashMap<String,Object>();
		if(!csrfToken.isTokenValid(request)){
			map.put("result", csrfToken.gotoAjaxIndex());
			return UrpResult.ok(map);
		}else{
			map= curriculumReplacementService.saveCurriculumInfo(map, request, state, list);
			map.put("token",session.getAttribute("token_in_session").toString());
			return UrpResult.ok(map);
		}
	}

	/**
	 * 删除申请
	 * @param model
	 * @param request
	 * @param session
	 * @param sqbh
	 * @param type
	 * @return
	 */
	@RequestMapping(value="/student/personalManagement/personalApplication/curriculumReplacement/revokeInfo")
	@ResponseBody
	public UrpResult revokeInfo(Model model,HttpServletRequest request,HttpSession session,String sqbh){	
		Map<String,Object> map = new HashMap<String,Object>();
		if(!csrfToken.isTokenValid(request)){
			map.put("result", csrfToken.gotoAjaxIndex());
			return UrpResult.ok(map);
		}else{
			String result="ok";
			SysYwhdkzb ywhdkzb = baseService.queryEntityById(SysYwhdkzb.class, "10001");
			if(ywhdkzb!=null&&"1".equals(ywhdkzb.getQyf())){
				int i = applyCommonService.queryTimeFrame("10001");
				if(i>0){
					CjKctdSqb cjKctdSqb = baseService.queryEntityById(CjKctdSqb.class, sqbh);
					String xh = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
					if(cjKctdSqb!=null){
						if("0".equals(cjKctdSqb.getSqzt())&&xh.equals(cjKctdSqb.getXh())){
							curriculumReplacementService.doDeleteAllCurriculum(sqbh);
							baseService.doDelete(CjKctdSqb.class, sqbh);
						}
					}
				}else{
					result="当前申请不在开放期，不能删除！";
				}
			}else{
				result="申请开关已关闭，不能删除！";
			}
			map.put("result", result);
			map.put("token",session.getAttribute("token_in_session").toString());
			return UrpResult.ok(map);
		}
	}

	/**
	 * 撤回
	 * @param model
	 * @param request
	 * @param session
	 * @return
	 */
	@RequestMapping(value="/student/personalManagement/personalApplication/curriculumReplacement/doRevoke")
	@ResponseBody
	public Map<String,Object> doRevoke(Model model,HttpServletRequest request,HttpSession session,@RequestParam("sqbh") String sqbh,@RequestParam("cxyy") String cxyy){	
		Map<String,Object> map = new HashMap<String,Object>();
		if(!csrfToken.isTokenValid(request)){
			map.put("result", csrfToken.gotoAjaxIndex());
			return map;
		}else{
			String result="操作失败";
			SysYwhdkzb ywhdkzb = baseService.queryEntityById(SysYwhdkzb.class, "10001");
			if(ywhdkzb!=null&&"1".equals(ywhdkzb.getQyf())){
				int i = applyCommonService.queryTimeFrame("10001");
				if(i>0){
					String schoolCode = commonService.queryParamValue();
					if(!"100027".equals(schoolCode)){
						CjKctdSqb sqb = baseService.queryEntityById(CjKctdSqb.class, sqbh);
						if(sqb!=null){
							PxCsb csb = CommonUtils.queryPxCsbById("kctdsp", "qygzl");
							String approvalProcess=(csb!=null&&"0".equals(csb.getCsz()))?"0":"1";
							String cxzt="3";
							if ("0".equals(approvalProcess)) {
								cxzt="2";
							}
							if(cxzt.equals(sqb.getSqzt())){
								result=curriculumReplacementService.revoke(request, sqbh, cxyy);
							}else{
								curriculumReplacementService.doRevoke(request, sqbh, cxyy);
								result="0";
							}
						}else{
							result="当前申请不存在！";
						}
					}
				}else{
					result="当前申请不在开放期，不能撤回申请！";
				}
			}else{
				result="申请开关已关闭，不能撤回申请！";
			}

			map.put("result", result);
			map.put("token",session.getAttribute("token_in_session").toString());
			return map;
		}
	}

	/**
	 * 指定审批人
	 * @param model
	 * @param request
	 * @param session
	 * @param tdlx
	 * @param kcList
	 * @param tdkcList
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/personalApplication/curriculumReplacement/addSqyy")
	public String addSqyy(Model model, HttpServletRequest request, HttpSession session,String tdlx,String kcList,String tdkcList){
		PxCsb csb = commonService.queryPxCsbById("cjgl", "cjtdyy");
		model.addAttribute("cjtdyy", (csb==null||StringUtils.isBlank(csb.getCsz()))?"0":csb.getCsz());
		List<Object[]> ealist = applyCommonService.queryEalByApplyType("10001");
		if(ealist!=null&&ealist.size() > 0){
			Object[] names = ealist.get(0);

			model.addAttribute("eal_name", names[0]);
			Map<String, Object> map = curriculumReplacementService.queryApprovers(request, names, tdlx, kcList, tdkcList);
			if(map.get("result")=="ok"){
				model.addAttribute("approvers", map.get("approvers"));
			}else{
				model.addAttribute("errorInfo", map.get("result"));
				return "system/error/printErrorMessage";
			}

		}
		return "student/personalManagement/personalApplication/curriculumReplacement/sqyy";
	}

	/**
	 * 查询指定审批人
	 * @param model
	 * @param request
	 * @param session
	 * @param tdlx
	 * @param kcList
	 * @param tdkcList
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/personalApplication/curriculumReplacement/queryApprovers")
	@ResponseBody
	public UrpResult queryApprovers(Model model, HttpServletRequest request, HttpSession session,String tdlx,String kcList,String tdkcList){
		Map<String,Object> map = new HashMap<String,Object>();
		List<Object[]> ealist = applyCommonService.queryEalByApplyType("10001");
		if(ealist!=null&&ealist.size() > 0){
			Object[] names = ealist.get(0);
			map = curriculumReplacementService.queryApprovers(request, names, tdlx, kcList, tdkcList);
			map.put("eal_name", names[0]);
			if(map.get("result")=="ok"){
				map.put("approvers", map.get("approvers"));
			}
		}else{
			map.put("result", "未找到当前申请类型对应的审批环节数据！");
		}
		return UrpResult.ok(map);
	}

	/**
	 * 课程替代查看
	 * @param model
	 * @param session
	 * @param sqbh
	 * @param tdlx
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/personalApplication/curriculumReplacement/seeInfo")
	public String seeInfoModel(Model model, HttpSession session,String sqbh){
		CjKctdSqb cjKctdSqb = baseService.queryEntityById(CjKctdSqb.class, sqbh);
		String xh = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
		String schoolCode = commonService.queryParamValue();
		if(cjKctdSqb!=null){
			if(xh.equals(cjKctdSqb.getXh())){
				List<Object[]> kchList = curriculumReplacementService.queryKchList(sqbh);
				List<Object[]> tdkchList = curriculumReplacementService.queryTdkchList(sqbh);
				String name = curriculumReplacementService.queryParamValueById();
				model.addAttribute("name",name);
				String jwckssp="0";
				PxCsb csb = commonService.queryPxCsbById("kctdsp", "jwckssp");
				if(csb!=null&&"1".equals(csb.getCsz())){
					jwckssp="1";
					Object[] obj = curriculumReplacementService.queryCjKctdSqb(sqbh);
					model.addAttribute("jwcksspInfo",obj);
				}
				model.addAttribute("jwckssp",jwckssp);
				model.addAttribute("tdkchList", tdkchList);
				model.addAttribute("kchList", kchList);
				model.addAttribute("cjKctdSqb", cjKctdSqb);
				if("100016".equals(schoolCode)){
					List<Object[]> spjlbList = curriculumReplacementService.queryKctdSpjlb(sqbh);
					model.addAttribute("spjlbList", spjlbList);
					if(spjlbList.size()>0){
						return "student/personalManagement/personalApplication/curriculumReplacement/look";
					}
				}
				return "student/personalManagement/personalApplication/curriculumReplacement/seeInfo";
			}
		}
		return "student/personalManagement/personalApplication/curriculumReplacement/index";
	}

	/**
	 * 课程替代规则 首页
	 * 
	 */
	@RequestMapping(value = "/student/personalManagement/personalApplication/curriculumReplacement/rules", method = RequestMethod.GET)
	public String rulesIndex(Model model) {

		return "student/personalManagement/personalApplication/curriculumReplacement/rules";
	}

	/**
	 * 分页查询
	 * @param model
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value="/student/personalManagement/personalApplication/curriculumReplacement/getRulesPage")
	@ResponseBody
	public UrpResult getRulesPage(Model model,@RequestParam(defaultValue="1")int pageNum,@RequestParam(defaultValue="10")int pageSize,
			String tdlx,String kch,String kcm,String tdkch,String tdkcm){	
		String searchVal="";
		if(StringUtils.isNotBlank(tdlx)){
			searchVal+=" and r.gzlx='"+tdlx+"' ";
		}
		if(StringUtils.isNotBlank(kch)){
			searchVal+=" and r.kcm like'%"+kch+"%' ";
		}
		if(StringUtils.isNotBlank(kcm)){
			searchVal+=" and r.kcm like'%"+kcm+"%' ";
		}
		if(StringUtils.isNotBlank(tdkch)){
			searchVal+=" and r.tdkcm like'%"+tdkch+"%' ";
		}
		if(StringUtils.isNotBlank(tdkcm)){
			searchVal+=" and r.tdkcm like'%"+tdkcm+"%' ";
		}
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		StudentRollView xsXjbView = baseService.queryEntityById(StudentRollView.class, xh);
		String njdm=null;
		String xsh=null;
		String zyh=null;
		String zyfxh=null;
		if(xsXjbView!=null){
			njdm=xsXjbView.getNjdm();
			xsh=xsXjbView.getXsh();
			zyh=xsXjbView.getZyh();
			zyfxh=xsXjbView.getZyfxh();
		}

		String sql="select r.*,rownum rn from (select m.gzid,m.gzlx,(select t.tdlxsm from code_tdlxb t where t.tdlx=m.gzlx) gzlxsm,m.sfqy,m.dtysf,m.czsj," +
				" CASE m.gzlx WHEN '01' THEN (SELECT d.tdkch || '-' || pn.kcm(d.tdkch) FROM cj_kctd_gxb d WHERE d.gzid = m.gzid )" +
				" WHEN '03' THEN (SELECT d.tdkch || '-' || pn.kcm(d.tdkch) FROM cj_kctd_gxb d WHERE d.gzid = m.gzid AND ROWNUM = 1)" +
				" ELSE (SELECT DISTINCT listagg(d.tdkch || '-' || pn.kcm(d.tdkch) || ',') WITHIN GROUP(ORDER BY d.tdkch) OVER(PARTITION BY d.gzid) FROM cj_kctd_gxb d WHERE d.gzid = m.gzid) END AS tdkcm," +
				" CASE m.gzlx WHEN '01' THEN (SELECT d.kch || '-' || pn.kcm(d.kch) FROM cj_kctd_gxb d WHERE d.gzid = m.gzid )" +
				" WHEN '03' THEN (SELECT DISTINCT listagg(d.kch || '-' || pn.kcm(d.kch) || ',') WITHIN GROUP(ORDER BY d.kch) OVER(PARTITION BY d.gzid) FROM cj_kctd_gxb d WHERE d.gzid = m.gzid)" +
				" ELSE (SELECT d.kch || '-' || pn.kcm(d.kch) FROM cj_kctd_gxb d WHERE d.gzid = m.gzid AND ROWNUM = 1) END AS kcm," +
				"(select listagg (case when t.njdm is null and t.xsh is null and t.zyh is null and t.zyfxh is null and t.ydlbdm is null then '全校适用' else " +
				"rtrim((decode(t.njdm,null,'',pn.njmc(t.njdm)||'-')||decode(t.xsh,null,'',pn.xsm(t.xsh)||'-')||decode(t.zyh,null,'',pn.zym(t.xsh,t.zyh)||'-')||" +
				"decode(t.zyfxh,null,'',pn.zyfxm(t.xsh,t.zyh,t.zyfxh)||'-')||decode(t.ydlbdm,null,'',f.ydlb||'-')),'-') end , ',') WITHIN GROUP (ORDER BY t.njdm,t.xsh,t.zyh,t.zyfxh,t.ydlbdm) end " +
				"from cj_kctd_syfwb t,code_ydlb f where m.gzid=t.gzid and t.ydlbdm=f.yddm(+)) syfw from cj_kctd_gzb m where m.sfqy='1'";
		sql+=" and exists(select 1 from cj_kctd_syfwb n where m.gzid=n.gzid and (n.njdm is null or n.njdm='"+njdm+"') and (n.xsh is null or n.xsh='"+xsh+"') and (n.zyh is null or n.zyh='"+zyh+"') and (n.zyfxh is null or n.zyfxh='"+zyfxh+"'))";
		sql+=") r where r.tdkcm is not null and r.kcm is not null "+searchVal+" order by r.czsj desc";
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page<Object> page = pageService.queryPageBySql(info);
		return UrpResult.ok(page);
	}

}