<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>完善个人信息</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 个人信息完善页面样式 */
        .info-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .info-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .info-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        /* 表单区域样式 */
        .form-section-mobile {
            margin-bottom: var(--margin-lg);
        }
        
        .section-header-mobile {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding: var(--padding-md);
            background: var(--bg-tertiary);
            border-radius: 8px;
        }
        
        .section-title-mobile {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .section-title-mobile i {
            font-size: 18px;
        }
        
        .add-btn-mobile {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .add-btn-mobile:hover {
            background: var(--success-dark);
        }
        
        .add-btn-mobile:active {
            transform: translateY(1px);
        }
        
        .info-block-mobile {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            margin-bottom: var(--margin-md);
            overflow: hidden;
        }
        
        .block-header-mobile {
            background: var(--bg-tertiary);
            padding: var(--padding-sm) var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .block-title-mobile {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .delete-btn-mobile {
            background: var(--error-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .delete-btn-mobile:hover {
            background: var(--error-dark);
        }
        
        .block-content-mobile {
            padding: var(--padding-md);
        }
        
        .form-group-mobile {
            margin-bottom: var(--margin-md);
        }
        
        .form-label-mobile {
            display: block;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .form-input-mobile {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--transition-base);
            box-sizing: border-box;
        }
        
        .form-input-mobile:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-textarea-mobile {
            min-height: 80px;
            resize: vertical;
            font-family: inherit;
        }
        
        .form-select-mobile {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            box-sizing: border-box;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-select-mobile:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .date-input-group {
            position: relative;
        }
        
        .date-input-group .form-input-mobile {
            padding-right: 40px;
        }
        
        .date-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .empty-state-mobile {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin-bottom: var(--margin-md);
        }
        
        .empty-state-mobile i {
            font-size: 48px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-md);
        }
        
        .empty-state-mobile .empty-text {
            font-size: var(--font-size-base);
            margin-bottom: var(--margin-sm);
        }
        
        .empty-state-mobile .empty-action {
            color: var(--primary-color);
            cursor: pointer;
            text-decoration: underline;
        }
        
        .form-actions-mobile {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
            z-index: 1000;
        }
        
        .form-error-mobile {
            color: var(--error-color);
            font-size: var(--font-size-small);
            margin-top: 4px;
            display: none;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .section-header-mobile {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: stretch;
            }
            
            .block-header-mobile {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: stretch;
            }
        }
        
        /* 为底部固定按钮留出空间 */
        .page-mobile {
            padding-bottom: 80px;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="closeModal();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">完善个人信息</div>
            <div class="navbar-action" onclick="saveInfo();">
                <i class="ace-icon fa fa-save"></i>
            </div>
        </nav>
        
        <!-- 信息头部 -->
        <div class="info-header">
            <div class="info-title">完善个人信息</div>
            <div class="info-desc">请完善您的家庭成员和学习工作经历信息</div>
        </div>
        
        <!-- 家庭成员信息 -->
        <div class="card-mobile">
            <div class="section-header-mobile">
                <div class="section-title-mobile">
                    <i class="ace-icon fa fa-users"></i>
                    <span>家庭成员信息</span>
                </div>
                <button class="add-btn-mobile" onclick="addBlock('jtcy');">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>添加</span>
                </button>
            </div>
            
            <div id="jtcy_container">
                <div class="empty-state-mobile" id="jtcy_alert">
                    <i class="ace-icon fa fa-users"></i>
                    <div class="empty-text">暂时没有您的家庭成员信息</div>
                    <div class="empty-action" onclick="addBlock('jtcy');">点此添加</div>
                </div>
            </div>
        </div>
        
        <!-- 学习工作经历信息 -->
        <div class="card-mobile">
            <div class="section-header-mobile">
                <div class="section-title-mobile">
                    <i class="ace-icon fa fa-briefcase"></i>
                    <span>学习工作经历</span>
                </div>
                <button class="add-btn-mobile" onclick="addBlock('grjl');">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>添加</span>
                </button>
            </div>
            
            <div id="grjl_container">
                <div class="empty-state-mobile" id="grjl_alert">
                    <i class="ace-icon fa fa-briefcase"></i>
                    <div class="empty-text">暂时没有您的学习工作经历</div>
                    <div class="empty-action" onclick="addBlock('grjl');">点此添加</div>
                </div>
            </div>
        </div>
        
        <!-- 固定底部操作按钮 -->
        <div class="form-actions-mobile">
            <button type="button" class="btn-mobile btn-primary flex-1" onclick="saveInfo();">
                <i class="ace-icon fa fa-save"></i>
                <span>保存信息</span>
            </button>
            <button type="button" class="btn-mobile btn-secondary flex-1" onclick="closeModal();">
                <i class="ace-icon fa fa-times"></i>
                <span>关闭</span>
            </button>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>保存中...</span>
        </div>
    </div>

    <!-- 隐藏的模板 -->
    <div id="jtcyDemo" style="display:none;">
        <!-- 家庭成员模板内容将通过JavaScript动态生成 -->
    </div>
    
    <div id="grjlDemo" style="display:none;">
        <!-- 学习工作经历模板内容将通过JavaScript动态生成 -->
    </div>
    
    <input type="hidden" id="tokenValue" value="${token_in_session}"/>

    <script>
        // 全局变量
        var data = eval('(${data})');
        var blockXh = 0;
        var jtStr = "";
        var jlStr = "";

        $(function() {
            initPage();
            initTemplates();
            loadData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化模板
        function initTemplates() {
            // 家庭成员模板
            jtStr = `
                <div class="info-block-mobile">
                    <div class="block-header-mobile">
                        <div class="block-title-mobile">家庭成员 #{{index}}</div>
                        <button class="delete-btn-mobile" onclick="delBlock(this);">
                            <i class="ace-icon fa fa-trash"></i>
                            <span>删除</span>
                        </button>
                    </div>
                    <div class="block-content-mobile">
                        <form class="family-form">
                            <input type="hidden" name="id" value="1"/>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">姓名</label>
                                <input type="text" name="xm" class="form-input-mobile" placeholder="请输入姓名" maxlength="100"/>
                                <div class="form-error-mobile">请输入姓名</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">关系</label>
                                <input type="text" name="gx" class="form-input-mobile" placeholder="请输入关系" maxlength="20"/>
                                <div class="form-error-mobile">请输入关系</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">出生年月</label>
                                <div class="date-input-group">
                                    <input type="date" name="csny" class="form-input-mobile"/>
                                    <i class="ace-icon fa fa-calendar date-icon"></i>
                                </div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">工作单位</label>
                                <textarea name="gzdw" class="form-input-mobile form-textarea-mobile"
                                          placeholder="请输入工作单位" maxlength="100"></textarea>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">职务</label>
                                <input type="text" name="zw" class="form-input-mobile" placeholder="请输入职务" maxlength="20"/>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">联系电话</label>
                                <input type="tel" name="lxdh" class="form-input-mobile" placeholder="请输入联系电话" maxlength="20"/>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">政治面貌</label>
                                <select name="zzmmdm" class="form-select-mobile">
                                    <option value="">请选择政治面貌</option>
                                    <cache:query var="zzmm" region="code_zzmmb"/>
                                    <c:forEach items="${zzmm}" var="zzmm">
                                        <option value="${zzmm.zzmmdm}">${zzmm.zzmmmc}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // 学习工作经历模板
            jlStr = `
                <div class="info-block-mobile">
                    <div class="block-header-mobile">
                        <div class="block-title-mobile">学习工作经历 #{{index}}</div>
                        <button class="delete-btn-mobile" onclick="delBlock(this);">
                            <i class="ace-icon fa fa-trash"></i>
                            <span>删除</span>
                        </button>
                    </div>
                    <div class="block-content-mobile">
                        <form class="experience-form">
                            <input type="hidden" name="id" value="2"/>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">起止年月</label>
                                <input type="text" name="qzny" class="form-input-mobile"
                                       placeholder="格式：2020.09-2024.06" maxlength="20"/>
                                <div class="form-error-mobile">请输入起止年月</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">工作单位</label>
                                <textarea name="dw" class="form-input-mobile form-textarea-mobile"
                                          placeholder="请输入工作单位或学校" maxlength="100"></textarea>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">职务</label>
                                <input type="text" name="zw" class="form-input-mobile" placeholder="请输入职务或职位" maxlength="20"/>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">证明人</label>
                                <input type="text" name="zmr" class="form-input-mobile" placeholder="请输入证明人" maxlength="20"/>
                            </div>
                        </form>
                    </div>
                </div>
            `;
        }

        // 加载数据
        function loadData() {
            if (data != undefined) {
                var jtzd = data["jtzd"];
                var jlzd = data["jlzd"];
                var jtValue = data["jtValue"];
                var jlValue = data["jlValue"];

                // 加载家庭成员数据
                if (jtValue != "" && jtValue.length > 0) {
                    $("#jtcy_alert").hide();
                    for (var i = 0; i < jtValue.length; i++) {
                        var jtArr = jtValue[i];
                        var xh = addBlock("jtcy");

                        // 填充数据
                        var form = $("#jtcy_container .family-form").last();
                        if (jtzd != "") {
                            var zds = jtzd.split(",");
                            for (var j = 0; j < zds.length && j < jtArr.length; j++) {
                                var zdm = zds[j].toLowerCase();
                                form.find('[name="' + zdm + '"]').val(jtArr[j]);
                            }
                        }
                    }
                }

                // 加载学习工作经历数据
                if (jlValue != "" && jlValue.length > 0) {
                    $("#grjl_alert").hide();
                    for (var i = 0; i < jlValue.length; i++) {
                        var jlArr = jlValue[i];
                        var xh = addBlock("grjl");

                        // 填充数据
                        var form = $("#grjl_container .experience-form").last();
                        if (jlzd != "") {
                            var zds = jlzd.split(",");
                            for (var j = 0; j < zds.length && j < jlArr.length; j++) {
                                var zdm = zds[j].toLowerCase();
                                form.find('[name="' + zdm + '"]').val(jlArr[j]);
                            }
                        }
                    }
                }
            }
        }

        // 添加信息块
        function addBlock(type) {
            $("#" + type + "_alert").hide();

            var str = "";
            if (type == "jtcy") {
                str = jtStr;
            } else if (type == "grjl") {
                str = jlStr;
            }

            var xh = blockXh;
            var cont = str.replace(/{{index}}/g, xh + 1);

            $("#" + type + "_container").append(cont);

            // 初始化新添加的表单验证
            initFormValidation();

            blockXh++;
            return xh;
        }

        // 删除信息块
        function delBlock(obj) {
            var container = $(obj).closest('[id$="_container"]');
            var block = $(obj).closest('.info-block-mobile');

            block.remove();

            // 检查是否还有数据，如果没有则显示空状态
            var containerId = container.attr('id');
            if (container.find('.info-block-mobile').length === 0) {
                if (containerId === 'jtcy_container') {
                    $("#jtcy_alert").show();
                } else if (containerId === 'grjl_container') {
                    $("#grjl_alert").show();
                }
            }

            // 重新编号
            container.find('.info-block-mobile').each(function(index) {
                $(this).find('.block-title-mobile').text(
                    $(this).find('.block-title-mobile').text().replace(/#\d+/, '#' + (index + 1))
                );
            });
        }

        // 初始化表单验证
        function initFormValidation() {
            // 简单的表单验证
            $('.form-input-mobile[required], .form-input-mobile[name="xm"], .form-input-mobile[name="gx"], .form-input-mobile[name="qzny"]').on('blur', function() {
                validateField($(this));
            });
        }

        // 验证单个字段
        function validateField(field) {
            var value = field.val().trim();
            var errorDiv = field.siblings('.form-error-mobile');
            var fieldName = field.attr('name');

            var isValid = true;
            var errorMsg = '';

            // 必填字段验证
            if ((fieldName === 'xm' || fieldName === 'gx' || fieldName === 'qzny') && !value) {
                isValid = false;
                errorMsg = '此字段为必填项';
            }

            // 字符长度验证
            var maxLength = field.attr('maxlength');
            if (maxLength && getStringLength(value) > parseInt(maxLength)) {
                isValid = false;
                errorMsg = '最大长度不能超过' + maxLength + '个字符，一个汉字为两个字符';
            }

            // 起止年月格式验证
            if (fieldName === 'qzny' && value && !/^\d{4}\.\d{2}-\d{4}\.\d{2}$/.test(value)) {
                isValid = false;
                errorMsg = '格式不正确，请使用格式：2020.09-2024.06';
            }

            if (isValid) {
                errorDiv.hide();
                field.removeClass('error');
            } else {
                errorDiv.text(errorMsg).show();
                field.addClass('error');
            }

            return isValid;
        }

        // 计算字符串长度（中文算2个字符）
        function getStringLength(str) {
            var length = 0;
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) > 19967) {
                    length += 2;
                } else {
                    length++;
                }
            }
            return length;
        }

        // 保存信息
        function saveInfo() {
            var isValid = true;

            // 验证所有表单
            $('.form-input-mobile[name="xm"], .form-input-mobile[name="gx"], .form-input-mobile[name="qzny"]').each(function() {
                if (!validateField($(this))) {
                    isValid = false;
                }
            });

            if (!isValid) {
                showToast("数据校验不通过，请检查数据！");
                return false;
            }

            var params = {};

            // 收集家庭成员数据
            var jtcy = [];
            $("#jtcy_container .family-form").each(function() {
                var formData = {};
                $(this).find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    if (name && value) {
                        formData[name] = value;
                    }
                });
                if (Object.keys(formData).length > 1) { // 除了id字段外还有其他数据
                    jtcy.push(formData);
                }
            });
            params.jtcy = jtcy;

            // 收集学习工作经历数据
            var grjl = [];
            $("#grjl_container .experience-form").each(function() {
                var formData = {};
                $(this).find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    if (name && value) {
                        formData[name] = value;
                    }
                });
                if (Object.keys(formData).length > 1) { // 除了id字段外还有其他数据
                    grjl.push(formData);
                }
            });
            params.grjl = grjl;

            // 发送数据
            showLoading(true);

            $.ajax({
                url: "/student/rollManagement/myRollCard/save?tokenValue=" + $("#tokenValue").val(),
                cache: false,
                type: "post",
                contentType: "application/json",
                data: JSON.stringify(params),
                dataType: "json",
                success: function(d) {
                    if (d["result"].indexOf("/") != -1) {
                        window.location.href = d["result"];
                    } else {
                        $("#tokenValue").val(d["token"]);
                        if (d["result"] == "ok") {
                            showSuccess("操作成功！", function() {
                                closeModal();
                            });
                        } else {
                            showToast(d["result"]);
                        }
                    }
                },
                error: function(xhr) {
                    showToast("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 关闭模态框
        function closeModal() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                window.history.back();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                if (typeof urp !== 'undefined' && urp.showLoading) {
                    urp.showLoading();
                } else {
                    $('#loadingState').show();
                }
            } else {
                if (typeof urp !== 'undefined' && urp.hideLoading) {
                    urp.hideLoading();
                } else {
                    $('#loadingState').hide();
                }
            }
        }

        // 显示提示信息
        function showToast(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) {
                    callback();
                }
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 添加错误样式
        $(document).ready(function() {
            $('<style>')
                .prop('type', 'text/css')
                .html(`
                    .form-input-mobile.error {
                        border-color: var(--error-color) !important;
                        box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
                    }
                `)
                .appendTo('head');
        });
    </script>
</body>
</html>
