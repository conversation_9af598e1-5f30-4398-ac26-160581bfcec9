<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证页面样式 */
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .student-card {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 12px;
            padding: var(--padding-lg);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }
        
        .card-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            opacity: 0.1;
            border-radius: 12px;
        }
        
        .card-content {
            position: relative;
            z-index: 1;
        }
        
        .card-header-info {
            text-align: center;
            margin-bottom: var(--margin-lg);
        }
        
        .university-name {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--margin-xs);
        }
        
        .card-title {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .card-body {
            display: flex;
            gap: var(--spacing-lg);
            margin-bottom: var(--margin-lg);
        }
        
        .student-photo {
            width: 80px;
            height: 100px;
            border-radius: 6px;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-disabled);
            font-size: var(--font-size-h4);
            flex-shrink: 0;
            border: 2px solid var(--border-primary);
        }
        
        .student-info {
            flex: 1;
            min-width: 0;
        }
        
        .info-row {
            display: flex;
            margin-bottom: var(--margin-md);
        }
        
        .info-label {
            width: 60px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .info-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
            flex: 1;
        }
        
        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }
        
        .card-number {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-family: monospace;
        }
        
        .card-valid {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .card-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .actions-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .action-btn:active {
            background: var(--bg-color-active);
        }
        
        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--margin-sm);
            font-size: var(--font-size-base);
            color: white;
        }
        
        .action-icon.reissue {
            background: var(--warning-color);
        }
        
        .action-icon.report {
            background: var(--error-color);
        }
        
        .action-icon.photo {
            background: var(--info-color);
        }
        
        .action-icon.history {
            background: var(--success-color);
        }
        
        .action-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .action-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .card-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-item.reissue {
            border-left: 4px solid var(--warning-color);
        }
        
        .history-item.report {
            border-left: 4px solid var(--error-color);
        }
        
        .history-item.photo {
            border-left: 4px solid var(--info-color);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-processing {
            background: var(--warning-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .application-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .application-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="card-header">
            <div class="header-title">学生证</div>
            <div class="header-subtitle">查看和管理学生证信息</div>
        </div>

        <!-- 学生证卡片 -->
        <div class="student-card">
            <div class="card-bg"></div>
            <div class="card-content">
                <div class="card-header-info">
                    <div class="university-name">华中科技大学</div>
                    <div class="card-title">学生证</div>
                </div>

                <div class="card-body">
                    <div class="student-photo" id="studentPhoto">
                        <i class="ace-icon fa fa-user"></i>
                    </div>
                    <div class="student-info">
                        <div class="info-row">
                            <div class="info-label">姓名:</div>
                            <div class="info-value" id="studentName">张三</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">学号:</div>
                            <div class="info-value" id="studentId">2021001001</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">专业:</div>
                            <div class="info-value" id="major">计算机科学与技术</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">班级:</div>
                            <div class="info-value" id="className">计科2021-1班</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">入学:</div>
                            <div class="info-value" id="enrollmentYear">2021年</div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="card-number" id="cardNumber">卡号: 2021001001</div>
                    <div class="card-valid" id="validDate">有效期至: 2025.06</div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="card-actions">
            <div class="actions-title">
                <i class="ace-icon fa fa-cogs"></i>
                <span>相关服务</span>
            </div>

            <div class="action-buttons">
                <div class="action-btn" onclick="showApplicationForm('reissue')">
                    <div class="action-icon reissue">
                        <i class="ace-icon fa fa-refresh"></i>
                    </div>
                    <div class="action-name">补办学生证</div>
                    <div class="action-desc">学生证丢失或损坏时申请补办</div>
                </div>

                <div class="action-btn" onclick="showApplicationForm('report')">
                    <div class="action-icon report">
                        <i class="ace-icon fa fa-exclamation-triangle"></i>
                    </div>
                    <div class="action-name">挂失学生证</div>
                    <div class="action-desc">学生证丢失时进行挂失处理</div>
                </div>

                <div class="action-btn" onclick="showApplicationForm('photo')">
                    <div class="action-icon photo">
                        <i class="ace-icon fa fa-camera"></i>
                    </div>
                    <div class="action-name">更换照片</div>
                    <div class="action-desc">申请更换学生证上的照片</div>
                </div>

                <div class="action-btn" onclick="showHistory()">
                    <div class="action-icon history">
                        <i class="ace-icon fa fa-history"></i>
                    </div>
                    <div class="action-name">办理记录</div>
                    <div class="action-desc">查看学生证相关办理历史</div>
                </div>
            </div>
        </div>

        <!-- 办理记录 -->
        <div class="card-history" id="cardHistory" style="display: none;">
            <div class="history-header">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>办理记录</span>
                </div>
                <div class="history-count" id="historyCount">0</div>
            </div>

            <div id="historyItems">
                <!-- 办理记录将动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 申请表单 -->
    <div class="application-form" id="applicationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeApplicationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">学生证申请</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="formStudentId" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="formStudentName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">专业</div>
                    <input type="text" class="form-input" id="formMajor" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">班级</div>
                    <input type="text" class="form-input" id="formClassName" disabled>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">申请类型</div>
                    <input type="text" class="form-input" id="applicationType" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">申请原因</div>
                    <textarea class="form-input" id="reason" placeholder="请详细说明申请原因" style="min-height: 80px; resize: vertical;"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>

                <div class="form-group" id="photoGroup" style="display: none;">
                    <div class="form-label">照片上传</div>
                    <input type="file" class="form-input" id="photoFile" accept="image/*">
                    <div style="font-size: var(--font-size-mini); color: var(--text-secondary); margin-top: var(--margin-xs);">
                        请上传近期免冠照片，格式为JPG或PNG，大小不超过2MB
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">备注说明</div>
                    <textarea class="form-input" id="note" placeholder="其他需要说明的情况（选填）" style="min-height: 60px; resize: vertical;"></textarea>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeApplicationForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let studentInfo = {};
        let historyData = [];
        let currentApplicationType = '';

        $(function() {
            initPage();
            loadStudentInfo();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学生信息
        function loadStudentInfo() {
            showLoading(true);

            $.ajax({
                url: "/student/studentCard/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data.student || {};
                    renderStudentInfo();
                    showLoading(false);
                },
                error: function() {
                    // 使用模拟数据
                    studentInfo = {
                        studentId: '2021001001',
                        name: '张三',
                        major: '计算机科学与技术',
                        className: '计科2021-1班',
                        enrollmentYear: '2021',
                        cardNumber: '2021001001',
                        validDate: '2025.06',
                        photo: null
                    };
                    renderStudentInfo();
                    showLoading(false);
                }
            });
        }

        // 渲染学生信息
        function renderStudentInfo() {
            $('#studentName').text(studentInfo.name || '张三');
            $('#studentId').text(studentInfo.studentId || '2021001001');
            $('#major').text(studentInfo.major || '计算机科学与技术');
            $('#className').text(studentInfo.className || '计科2021-1班');
            $('#enrollmentYear').text((studentInfo.enrollmentYear || '2021') + '年');
            $('#cardNumber').text('卡号: ' + (studentInfo.cardNumber || studentInfo.studentId || '2021001001'));
            $('#validDate').text('有效期至: ' + (studentInfo.validDate || '2025.06'));

            // 如果有照片，显示照片
            if (studentInfo.photo) {
                $('#studentPhoto').html(`<img src="${studentInfo.photo}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">`);
            }
        }

        // 显示申请表单
        function showApplicationForm(type) {
            currentApplicationType = type;

            // 设置表单标题和申请类型
            const titles = {
                'reissue': '补办学生证',
                'report': '挂失学生证',
                'photo': '更换照片'
            };

            $('#formTitle').text(titles[type] + ' - 申请');
            $('#applicationType').val(titles[type]);

            // 填充基本信息
            $('#formStudentId').val(studentInfo.studentId || '2021001001');
            $('#formStudentName').val(studentInfo.name || '张三');
            $('#formMajor').val(studentInfo.major || '计算机科学与技术');
            $('#formClassName').val(studentInfo.className || '计科2021-1班');

            // 清空表单
            $('#reason').val('');
            $('#phone').val('');
            $('#note').val('');
            $('#photoFile').val('');

            // 显示/隐藏照片上传组
            if (type === 'photo') {
                $('#photoGroup').show();
            } else {
                $('#photoGroup').hide();
            }

            $('#applicationForm').addClass('show');
        }

        // 关闭申请表单
        function closeApplicationForm() {
            $('#applicationForm').removeClass('show');
            currentApplicationType = '';
        }

        // 提交申请
        function submitApplication() {
            const formData = {
                type: currentApplicationType,
                reason: $('#reason').val().trim(),
                phone: $('#phone').val().trim(),
                note: $('#note').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            const typeText = $('#applicationType').val();
            const message = `确定要提交${typeText}申请吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitApplication(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitApplication(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.reason) {
                showError('请填写申请原因');
                return false;
            }

            if (!formData.phone) {
                showError('请填写联系电话');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            // 如果是更换照片，检查是否上传了照片
            if (currentApplicationType === 'photo') {
                const photoFile = $('#photoFile')[0].files[0];
                if (!photoFile) {
                    showError('请上传照片');
                    return false;
                }

                // 检查文件大小
                if (photoFile.size > 2 * 1024 * 1024) {
                    showError('照片大小不能超过2MB');
                    return false;
                }
            }

            return true;
        }

        // 执行提交申请
        function doSubmitApplication(formData) {
            // 如果是更换照片，需要使用FormData上传文件
            let submitData;
            let contentType = 'application/x-www-form-urlencoded';
            let processData = true;

            if (currentApplicationType === 'photo') {
                submitData = new FormData();
                Object.keys(formData).forEach(key => {
                    submitData.append(key, formData[key]);
                });

                const photoFile = $('#photoFile')[0].files[0];
                if (photoFile) {
                    submitData.append('photo', photoFile);
                }

                contentType = false;
                processData = false;
            } else {
                submitData = formData;
            }

            $.ajax({
                url: "/student/studentCard/submitApplication",
                type: "post",
                data: submitData,
                contentType: contentType,
                processData: processData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('申请提交成功，请等待审核');
                        closeApplicationForm();
                        loadHistoryData(); // 重新加载历史记录
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示办理记录
        function showHistory() {
            if ($('#cardHistory').is(':visible')) {
                $('#cardHistory').hide();
            } else {
                loadHistoryData();
                $('#cardHistory').show();
            }
        }

        // 加载历史数据
        function loadHistoryData() {
            $.ajax({
                url: "/student/studentCard/getHistoryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    historyData = data.history || [];
                    renderHistoryData();
                },
                error: function() {
                    // 使用模拟数据
                    historyData = [
                        {
                            id: '1',
                            type: 'reissue',
                            status: 'completed',
                            applyTime: '2024-01-15',
                            processTime: '2024-01-20',
                            reason: '学生证丢失'
                        },
                        {
                            id: '2',
                            type: 'photo',
                            status: 'processing',
                            applyTime: '2024-03-10',
                            processTime: null,
                            reason: '照片更新'
                        }
                    ];
                    renderHistoryData();
                }
            });
        }

        // 渲染历史数据
        function renderHistoryData() {
            $('#historyCount').text(historyData.length);

            const container = $('#historyItems');
            container.empty();

            if (historyData.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无办理记录
                    </div>
                `);
                return;
            }

            historyData.forEach(item => {
                const historyHtml = createHistoryItem(item);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(item) {
            const type = item.type || 'reissue';
            const status = item.status || 'processing';
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);
            const typeText = getTypeText(type);

            return `
                <div class="history-item ${type}" onclick="showHistoryDetail('${item.id}')">
                    <div class="history-basic">
                        <div class="history-type">${typeText}</div>
                        <div class="history-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(item.applyTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>处理时间:</span>
                            <span>${formatDate(item.processTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>申请原因:</span>
                            <span>${item.reason || '-'}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>处理结果:</span>
                            <span>${item.result || '-'}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'completed': return '已完成';
                case 'processing': return '处理中';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 获取类型文本
        function getTypeText(type) {
            switch(type) {
                case 'reissue': return '补办学生证';
                case 'report': return '挂失学生证';
                case 'photo': return '更换照片';
                default: return '其他';
            }
        }

        // 显示历史详情
        function showHistoryDetail(historyId) {
            const item = historyData.find(h => h.id === historyId);
            if (!item) return;

            let message = `办理详情\n\n`;
            message += `申请类型：${getTypeText(item.type)}\n`;
            message += `申请状态：${getStatusText(item.status)}\n`;
            message += `申请时间：${formatDate(item.applyTime)}\n`;
            message += `处理时间：${formatDate(item.processTime)}\n`;
            message += `申请原因：${item.reason || '-'}\n`;

            if (item.result) {
                message += `处理结果：${item.result}\n`;
            }

            if (item.comment) {
                message += `处理意见：${item.comment}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadStudentInfo();
            if ($('#cardHistory').is(':visible')) {
                loadHistoryData();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
