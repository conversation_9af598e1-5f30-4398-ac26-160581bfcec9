<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>时间安排</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 时间安排页面样式 */
        .time-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .time-filters {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-tabs {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .filter-tab {
            flex: 1;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            background: var(--bg-primary);
            color: var(--text-secondary);
        }
        
        .filter-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .date-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--margin-md);
        }
        
        .date-nav {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .date-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: 1px solid var(--border-primary);
            background: var(--bg-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--primary-color);
        }
        
        .date-btn:active {
            background: var(--bg-color-active);
        }
        
        .current-date {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .time-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
        }
        
        .stat-item {
            text-align: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            background: var(--bg-tertiary);
        }
        
        .stat-number {
            font-size: var(--font-size-h5);
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: var(--margin-xs);
        }
        
        .stat-label {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .time-schedule {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .schedule-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .schedule-title {
            display: flex;
            align-items: center;
        }
        
        .schedule-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .time-slot {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .time-slot:last-child {
            border-bottom: none;
        }
        
        .time-slot.current {
            background: #f0f5ff;
            border-left: 3px solid var(--primary-color);
        }
        
        .time-slot.past {
            opacity: 0.6;
        }
        
        .time-slot.future {
            background: #f9f9f9;
        }
        
        .slot-time {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: var(--margin-xs);
        }
        
        .slot-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .slot-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: var(--margin-sm);
        }
        
        .slot-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .slot-location {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .slot-badges {
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .slot-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .badge-course {
            background: var(--primary-color);
            color: white;
        }
        
        .badge-exam {
            background: var(--error-color);
            color: white;
        }
        
        .badge-activity {
            background: var(--success-color);
            color: white;
        }
        
        .badge-meeting {
            background: var(--warning-color);
            color: white;
        }
        
        .badge-free {
            background: var(--info-color);
            color: white;
        }
        
        .empty-schedule {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-schedule i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .actions-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .action-btn {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            background: var(--bg-primary);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-primary);
            text-decoration: none;
        }
        
        .action-btn:active {
            background: var(--bg-color-active);
        }
        
        .action-btn i {
            display: block;
            font-size: 24px;
            margin-bottom: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .action-btn span {
            font-size: var(--font-size-small);
        }
        
        @media (max-width: 480px) {
            .filter-tabs {
                flex-wrap: wrap;
            }
            
            .filter-tab {
                min-width: 80px;
            }
            
            .time-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">时间安排</div>
            <div class="navbar-action" onclick="refreshSchedule();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="time-header">
            <div class="header-title">时间安排</div>
            <div class="header-subtitle">查看个人时间安排和课程表</div>
        </div>
        
        <!-- 时间筛选 -->
        <div class="time-filters">
            <div class="filter-tabs">
                <div class="filter-tab active" data-type="today">今天</div>
                <div class="filter-tab" data-type="week">本周</div>
                <div class="filter-tab" data-type="month">本月</div>
            </div>
            
            <div class="date-selector">
                <div class="date-nav">
                    <div class="date-btn" onclick="changeDate(-1);">
                        <i class="ace-icon fa fa-chevron-left"></i>
                    </div>
                    <div class="current-date" id="currentDate">2024年3月15日</div>
                    <div class="date-btn" onclick="changeDate(1);">
                        <i class="ace-icon fa fa-chevron-right"></i>
                    </div>
                </div>
            </div>
            
            <div class="time-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalEvents">0</div>
                    <div class="stat-label">总安排</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="courseCount">0</div>
                    <div class="stat-label">课程</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="freeTime">0</div>
                    <div class="stat-label">空闲时段</div>
                </div>
            </div>
        </div>
        
        <!-- 时间安排表 -->
        <div class="time-schedule" id="timeSchedule">
            <div class="schedule-header">
                <div class="schedule-title">
                    <i class="ace-icon fa fa-clock-o"></i>
                    <span id="scheduleTitle">今日安排</span>
                </div>
            </div>
            <div id="scheduleContent">
                <!-- 时间安排内容将动态填充 -->
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                <span>快捷操作</span>
            </div>
            <div class="action-buttons">
                <a href="/student/courseTable" class="action-btn">
                    <i class="ace-icon fa fa-table"></i>
                    <span>课程表</span>
                </a>
                <a href="/student/examSchedule" class="action-btn">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <span>考试安排</span>
                </a>
                <a href="/student/schoolcalendar" class="action-btn">
                    <i class="ace-icon fa fa-calendar"></i>
                    <span>校历</span>
                </a>
                <a href="/student/workAndRestTimeArrangement" class="action-btn">
                    <i class="ace-icon fa fa-clock-o"></i>
                    <span>作息时间</span>
                </a>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-calendar-o"></i>
            <div>暂无时间安排</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let scheduleData = [];
        let currentType = 'today';
        let currentDate = new Date();

        $(function() {
            initPage();
            loadSchedule();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
            updateDateDisplay();
        }

        // 绑定事件
        function bindEvents() {
            // 筛选标签点击
            $('.filter-tab').click(function() {
                const type = $(this).data('type');
                if (type !== currentType) {
                    $('.filter-tab').removeClass('active');
                    $(this).addClass('active');
                    currentType = type;
                    updateDateDisplay();
                    loadSchedule();
                }
            });
        }

        // 更新日期显示
        function updateDateDisplay() {
            const dateStr = formatDate(currentDate);
            $('#currentDate').text(dateStr);
            
            const titleMap = {
                'today': '今日安排',
                'week': '本周安排',
                'month': '本月安排'
            };
            $('#scheduleTitle').text(titleMap[currentType]);
        }

        // 切换日期
        function changeDate(direction) {
            if (currentType === 'today') {
                currentDate.setDate(currentDate.getDate() + direction);
            } else if (currentType === 'week') {
                currentDate.setDate(currentDate.getDate() + (direction * 7));
            } else if (currentType === 'month') {
                currentDate.setMonth(currentDate.getMonth() + direction);
            }
            
            updateDateDisplay();
            loadSchedule();
        }

        // 加载时间安排
        function loadSchedule() {
            showLoading(true);
            
            $.ajax({
                url: "/student/timeArrangement/getSchedule",
                type: "post",
                data: {
                    type: currentType,
                    date: formatDateForServer(currentDate)
                },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        scheduleData = data.schedule || [];
                        updateStats(data.stats);
                        renderSchedule();
                        showEmptyState(scheduleData.length === 0);
                    } else {
                        showError(data.message || '加载时间安排失败');
                        showEmptyState(true);
                    }
                },
                error: function() {
                    showError('网络请求失败');
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染时间安排
        function renderSchedule() {
            const container = $('#scheduleContent');
            container.empty();
            
            if (scheduleData.length === 0) {
                container.append(`
                    <div class="empty-schedule">
                        <i class="ace-icon fa fa-calendar-o"></i>
                        <div>暂无时间安排</div>
                    </div>
                `);
                return;
            }
            
            scheduleData.forEach(function(slot) {
                const slotHtml = createTimeSlot(slot);
                container.append(slotHtml);
            });
        }

        // 创建时间段
        function createTimeSlot(slot) {
            const now = new Date();
            const slotTime = new Date(slot.startTime);
            const endTime = new Date(slot.endTime);
            
            let classes = ['time-slot'];
            if (slotTime <= now && now <= endTime) {
                classes.push('current');
            } else if (endTime < now) {
                classes.push('past');
            } else {
                classes.push('future');
            }
            
            const badges = [];
            if (slot.type === 'course') badges.push('<span class="slot-badge badge-course">课程</span>');
            if (slot.type === 'exam') badges.push('<span class="slot-badge badge-exam">考试</span>');
            if (slot.type === 'activity') badges.push('<span class="slot-badge badge-activity">活动</span>');
            if (slot.type === 'meeting') badges.push('<span class="slot-badge badge-meeting">会议</span>');
            if (slot.type === 'free') badges.push('<span class="slot-badge badge-free">空闲</span>');
            
            return `
                <div class="${classes.join(' ')}" onclick="viewSlotDetail('${slot.id}')">
                    <div class="slot-time">${formatTime(slot.startTime)} - ${formatTime(slot.endTime)}</div>
                    <div class="slot-title">${slot.title}</div>
                    <div class="slot-content">${slot.content || ''}</div>
                    <div class="slot-meta">
                        <div class="slot-location">${slot.location || ''}</div>
                        <div class="slot-badges">${badges.join('')}</div>
                    </div>
                </div>
            `;
        }

        // 更新统计信息
        function updateStats(stats) {
            $('#totalEvents').text(stats.total || 0);
            $('#courseCount').text(stats.courses || 0);
            $('#freeTime').text(stats.freeSlots || 0);
        }

        // 查看时间段详情
        function viewSlotDetail(slotId) {
            // 实现时间段详情查看逻辑
            console.log('View slot detail:', slotId);
        }

        // 刷新时间安排
        function refreshSchedule() {
            loadSchedule();
        }

        // 格式化日期
        function formatDate(date) {
            return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
        }

        // 格式化服务器日期
        function formatDateForServer(date) {
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#timeSchedule').hide();
            } else {
                $('#emptyState').hide();
                $('#timeSchedule').show();
            }
        }

        // 显示错误消息
        function showError(message) {
            console.error('Error: ' + message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
        }
    </script>
</body>
</html>
