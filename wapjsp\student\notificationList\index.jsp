<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>通知列表</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 通知列表页面样式 */
        .notification-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notification-filters {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-tabs {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .filter-tab {
            flex: 1;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            background: var(--bg-primary);
            color: var(--text-secondary);
        }
        
        .filter-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .notification-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-sm);
        }
        
        .stat-item {
            text-align: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            background: var(--bg-tertiary);
        }
        
        .stat-number {
            font-size: var(--font-size-h5);
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: var(--margin-xs);
        }
        
        .stat-label {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .notification-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .notification-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            position: relative;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-item:active {
            background: var(--bg-color-active);
        }
        
        .notification-item.unread {
            background: #f0f5ff;
            border-left: 3px solid var(--primary-color);
        }
        
        .notification-item.urgent {
            background: #fff2f0;
            border-left: 3px solid var(--error-color);
        }
        
        .notification-item.important {
            background: #fff7e6;
            border-left: 3px solid var(--warning-color);
        }
        
        .notification-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .notification-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: 1.4;
        }
        
        .notification-time {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            white-space: nowrap;
        }
        
        .notification-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: var(--margin-sm);
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .notification-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .notification-category {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .notification-badges {
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .notification-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .badge-unread {
            background: var(--primary-color);
            color: white;
        }
        
        .badge-urgent {
            background: var(--error-color);
            color: white;
        }
        
        .badge-important {
            background: var(--warning-color);
            color: white;
        }
        
        .badge-academic {
            background: var(--info-color);
            color: white;
        }
        
        .badge-system {
            background: var(--success-color);
            color: white;
        }
        
        .notification-actions {
            position: absolute;
            top: var(--padding-md);
            right: var(--padding-md);
            display: none;
            gap: var(--spacing-xs);
        }
        
        .notification-item:hover .notification-actions {
            display: flex;
        }
        
        .action-btn {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .action-read {
            background: var(--success-color);
            color: white;
        }
        
        .action-star {
            background: var(--warning-color);
            color: white;
        }
        
        .action-delete {
            background: var(--error-color);
            color: white;
        }
        
        .load-more {
            text-align: center;
            padding: var(--padding-md);
            color: var(--primary-color);
            cursor: pointer;
            border-top: 1px solid var(--divider-color);
        }
        
        .load-more:active {
            background: var(--bg-color-active);
        }
        
        @media (max-width: 480px) {
            .filter-tabs {
                flex-wrap: wrap;
            }
            
            .filter-tab {
                min-width: 70px;
            }
            
            .notification-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .notification-actions {
                position: static;
                display: flex;
                margin-top: var(--margin-sm);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">通知列表</div>
            <div class="navbar-action" onclick="refreshNotifications();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="notification-header">
            <div class="header-title">通知中心</div>
            <div class="header-subtitle">查看学校和系统通知</div>
        </div>
        
        <!-- 通知筛选 -->
        <div class="notification-filters">
            <div class="filter-tabs">
                <div class="filter-tab active" data-type="all">全部</div>
                <div class="filter-tab" data-type="unread">未读</div>
                <div class="filter-tab" data-type="academic">教务</div>
                <div class="filter-tab" data-type="system">系统</div>
                <div class="filter-tab" data-type="urgent">紧急</div>
            </div>
            
            <div class="notification-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalCount">0</div>
                    <div class="stat-label">总通知</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="unreadCount">0</div>
                    <div class="stat-label">未读</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="urgentCount">0</div>
                    <div class="stat-label">紧急</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="academicCount">0</div>
                    <div class="stat-label">教务</div>
                </div>
            </div>
        </div>
        
        <!-- 通知列表 -->
        <div class="notification-list" id="notificationList">
            <!-- 通知项将动态填充 -->
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-bell-o"></i>
            <div>暂无通知</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let notifications = [];
        let currentFilter = 'all';
        let currentPage = 1;
        let pageSize = 20;
        let hasMore = true;

        $(function() {
            initPage();
            loadNotifications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 筛选标签点击
            $('.filter-tab').click(function() {
                const type = $(this).data('type');
                if (type !== currentFilter) {
                    $('.filter-tab').removeClass('active');
                    $(this).addClass('active');
                    currentFilter = type;
                    currentPage = 1;
                    hasMore = true;
                    loadNotifications();
                }
            });
        }

        // 加载通知
        function loadNotifications() {
            showLoading(true);
            
            $.ajax({
                url: "/student/notificationList/getNotifications",
                type: "post",
                data: {
                    type: currentFilter,
                    page: currentPage,
                    pageSize: pageSize
                },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        if (currentPage === 1) {
                            notifications = data.notifications || [];
                        } else {
                            notifications = notifications.concat(data.notifications || []);
                        }
                        
                        hasMore = data.hasMore || false;
                        updateStats(data.stats);
                        renderNotifications();
                        showEmptyState(notifications.length === 0);
                    } else {
                        showError(data.message || '加载通知失败');
                        showEmptyState(true);
                    }
                },
                error: function() {
                    showError('网络请求失败');
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染通知列表
        function renderNotifications() {
            const container = $('#notificationList');
            
            if (currentPage === 1) {
                container.empty();
            }
            
            notifications.forEach(function(notification, index) {
                if (index >= (currentPage - 1) * pageSize) {
                    const notificationHtml = createNotificationItem(notification);
                    container.append(notificationHtml);
                }
            });
            
            // 添加加载更多按钮
            if (hasMore) {
                const loadMoreHtml = `
                    <div class="load-more" onclick="loadMore();">
                        <i class="ace-icon fa fa-chevron-down"></i>
                        <span>加载更多</span>
                    </div>
                `;
                container.append(loadMoreHtml);
            }
        }

        // 创建通知项
        function createNotificationItem(notification) {
            const classes = ['notification-item'];
            if (!notification.isRead) classes.push('unread');
            if (notification.priority === 'urgent') classes.push('urgent');
            if (notification.priority === 'important') classes.push('important');
            
            const badges = [];
            if (!notification.isRead) badges.push('<span class="notification-badge badge-unread">未读</span>');
            if (notification.priority === 'urgent') badges.push('<span class="notification-badge badge-urgent">紧急</span>');
            if (notification.priority === 'important') badges.push('<span class="notification-badge badge-important">重要</span>');
            if (notification.category === 'academic') badges.push('<span class="notification-badge badge-academic">教务</span>');
            if (notification.category === 'system') badges.push('<span class="notification-badge badge-system">系统</span>');
            
            return `
                <div class="${classes.join(' ')}" onclick="viewNotification('${notification.id}')">
                    <div class="notification-header-info">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-time">${formatTime(notification.publishTime)}</div>
                    </div>
                    <div class="notification-content">${notification.content}</div>
                    <div class="notification-meta">
                        <div class="notification-category">分类：${getCategoryName(notification.category)}</div>
                        <div class="notification-badges">${badges.join('')}</div>
                    </div>
                    <div class="notification-actions">
                        <button class="action-btn action-read" onclick="markAsRead('${notification.id}', event)" title="标记已读">
                            <i class="ace-icon fa fa-check"></i>
                        </button>
                        <button class="action-btn action-star" onclick="starNotification('${notification.id}', event)" title="收藏">
                            <i class="ace-icon fa fa-star"></i>
                        </button>
                        <button class="action-btn action-delete" onclick="deleteNotification('${notification.id}', event)" title="删除">
                            <i class="ace-icon fa fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }

        // 获取分类名称
        function getCategoryName(category) {
            const categoryMap = {
                'academic': '教务通知',
                'system': '系统通知',
                'announcement': '公告通知',
                'exam': '考试通知',
                'course': '课程通知',
                'other': '其他通知'
            };
            return categoryMap[category] || '未知分类';
        }

        // 更新统计信息
        function updateStats(stats) {
            $('#totalCount').text(stats.total || 0);
            $('#unreadCount').text(stats.unread || 0);
            $('#urgentCount').text(stats.urgent || 0);
            $('#academicCount').text(stats.academic || 0);
        }

        // 查看通知
        function viewNotification(notificationId) {
            // 实现通知详情查看逻辑
            console.log('View notification:', notificationId);
        }

        // 标记已读
        function markAsRead(notificationId, event) {
            event.stopPropagation();
            // 实现标记已读逻辑
            console.log('Mark as read:', notificationId);
        }

        // 收藏通知
        function starNotification(notificationId, event) {
            event.stopPropagation();
            // 实现收藏通知逻辑
            console.log('Star notification:', notificationId);
        }

        // 删除通知
        function deleteNotification(notificationId, event) {
            event.stopPropagation();
            // 实现删除通知逻辑
            console.log('Delete notification:', notificationId);
        }

        // 加载更多
        function loadMore() {
            if (hasMore) {
                currentPage++;
                loadNotifications();
            }
        }

        // 刷新通知
        function refreshNotifications() {
            currentPage = 1;
            hasMore = true;
            loadNotifications();
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) {
                return '刚刚';
            } else if (diff < 3600000) {
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) {
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#notificationList').hide();
            } else {
                $('#emptyState').hide();
                $('#notificationList').show();
            }
        }

        // 显示错误消息
        function showError(message) {
            console.error('Error: ' + message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
        }
    </script>
</body>
</html>
