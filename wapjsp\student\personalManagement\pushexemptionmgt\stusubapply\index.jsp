<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>推免个人申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 推免个人申请页面样式 */
        .apply-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .apply-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .apply-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            background: var(--info-light);
            color: var(--info-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--info-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-section i {
            color: var(--info-color);
            margin-right: 8px;
        }
        
        .batch-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .batch-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .batch-title i {
            color: var(--primary-color);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-secondary);
            border-radius: 6px;
        }
        
        .info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .info-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
            flex: 1;
            margin-left: var(--margin-sm);
        }
        
        .student-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .student-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .student-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .student-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-register {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .student-content {
            padding: var(--padding-md);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .criteria-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .criteria-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .criteria-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .criteria-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .criteria-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .criteria-body {
            padding: var(--padding-md);
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .criteria-item {
            margin-bottom: var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border: 1px solid var(--divider-color);
        }
        
        .criteria-item-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .criteria-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .criteria-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
        }
        
        .criteria-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .criteria-status.pass {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .criteria-status.fail {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .criteria-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }
        
        .criteria-detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .criteria-detail-label {
            font-weight: 500;
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .criteria-details {
                grid-template-columns: 1fr;
            }
            
            .criteria-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">推免个人申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 推免申请头部 -->
        <div class="apply-header">
            <div class="apply-title">推免个人申请</div>
            <div class="apply-desc">管理推免个人申请信息</div>
        </div>
        
        <!-- 消息提示 -->
        <c:if test="${not empty msg}">
            <div class="notice-section">
                <i class="ace-icon fa fa-hand-o-right"></i>
                ${msg}
            </div>
        </c:if>
        
        <c:if test="${empty msg}">
            <!-- 批次信息 -->
            <div class="batch-info">
                <div class="batch-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    推免批次信息
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">推免批次名称</div>
                        <div class="info-value">${tmpcmc}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">推免报名时间</div>
                        <div class="info-value">${wssqkssjstr} ~ ${wssqjssjstr}</div>
                    </div>
                </div>
            </div>
            
            <!-- 学生信息 -->
            <div class="student-info">
                <div class="student-header">
                    <div class="student-title">
                        <i class="ace-icon fa fa-user"></i>
                        推免报名信息
                    </div>
                    <div class="action-buttons">
                        <c:if test="${bmflag}">
                            <button class="btn-register" onclick="handleRegistration('bm');">
                                <i class="ace-icon fa fa-check"></i>
                                <span>报名</span>
                            </button>
                        </c:if>
                        <c:if test="${!bmflag && sqzt == '10'}">
                            <button class="btn-edit" onclick="handleRegistration('bj');">
                                <i class="ace-icon fa fa-edit"></i>
                                <span>编辑</span>
                            </button>
                        </c:if>
                    </div>
                </div>
                
                <div class="student-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">学号</div>
                            <div class="info-value">${stuInfo.xh}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">姓名</div>
                            <div class="info-value">${stuInfo.xm}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">年级</div>
                            <div class="info-value">${stuInfo.njmc}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">学院</div>
                            <div class="info-value">${stuInfo.xsm}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">专业</div>
                            <div class="info-value">${stuInfo.zym}</div>
                        </div>
                        <c:if test="${!bmflag}">
                            <div class="info-item">
                                <div class="info-label">申请时间</div>
                                <div class="info-value">${sqsj}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">推免状态</div>
                                <div class="info-value">
                                    <span class="status-badge ${getStatusClass(sqztsm)}">${sqztsm}</span>
                                </div>
                            </div>
                        </c:if>
                    </div>
                </div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 审查标准模态框 -->
        <div class="criteria-modal" id="criteriaModal">
            <div class="criteria-content">
                <div class="criteria-header">
                    <div class="criteria-title">审查标准信息</div>
                    <button class="criteria-close" onclick="closeCriteriaModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="criteria-body" id="criteriaBody">
                    <!-- 动态加载审查标准内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 处理报名/编辑
        function handleRegistration(flag) {
            if (flag === "bm") {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/pushexemptionmgt/stusubapply/get/edit/checkRegistration",
                    type: "post",
                    data: "tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result === "0") {
                            // 直接跳转到编辑页面
                            if (parent && parent.addTab) {
                                parent.addTab('推免申请编辑', '/student/personalManagement/pushexemptionmgt/stusubapply/get/edit');
                            } else {
                                location.href = "/student/personalManagement/pushexemptionmgt/stusubapply/get/edit";
                            }
                        } else {
                            if (!data.zxjxjhh) {
                                showError(data.errmsg);
                            } else {
                                // 显示审查标准信息
                                loadCriteriaList(data.zxjxjhh, data.sch, data.scpcdm, data.fajhh, data.errmsg);
                            }
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            } else {
                // 编辑模式直接跳转
                if (parent && parent.addTab) {
                    parent.addTab('推免申请编辑', '/student/personalManagement/pushexemptionmgt/stusubapply/get/edit');
                } else {
                    location.href = "/student/personalManagement/pushexemptionmgt/stusubapply/get/edit";
                }
            }
        }

        // 加载审查标准列表
        function loadCriteriaList(zxjxjhh, sch, scpcdm, fajhh, errmsg) {
            showLoading(true);

            const pageSizeVal = "30_sl";
            const page = "1";
            const parr = pageSizeVal.split("_");
            const pageSize = parseInt(parr[0]);

            $.ajax({
                url: "/student/personalManagement/pushexemptionmgt/stusubapply/get/edit/getIndexsList",
                type: "post",
                data: "sch=" + sch + "&zxjxjhh=" + zxjxjhh + "&scpcdm=" + scpcdm + "&fajhh=" + fajhh + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    showCriteriaModal(data.records, errmsg);
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示审查标准模态框
        function showCriteriaModal(data, errmsg) {
            let content = `
                <div style="background: var(--error-light); color: var(--error-dark); padding: var(--padding-md); border-radius: 6px; margin-bottom: var(--margin-md); border-left: 4px solid var(--error-color);">
                    <i class="ace-icon fa fa-hand-o-right"></i>
                    ${errmsg}
                </div>
            `;

            if (data && data.length > 0) {
                data.forEach(function(item, index) {
                    const isPass = item.ZBSCSFTG === "是";
                    content += `
                        <div class="criteria-item">
                            <div class="criteria-item-header">
                                <div class="criteria-index">${index + 1}</div>
                                <div class="criteria-name">${item.SCZBMC || ''}</div>
                                <div class="criteria-status ${isPass ? 'pass' : 'fail'}">${item.ZBSCSFTG || ''}</div>
                            </div>

                            <div class="criteria-details">
                                <div class="criteria-detail-item">
                                    <span class="criteria-detail-label">原始值</span>
                                    <span>${item.ZBJGZ || ''}</span>
                                </div>
                                <div class="criteria-detail-item">
                                    <span class="criteria-detail-label">原始值通过否</span>
                                    <span>${item.ZBSCYSSFTG || ''}</span>
                                </div>
                                <div class="criteria-detail-item">
                                    <span class="criteria-detail-label">审查结论类别</span>
                                    <span>${item.SCJLLBSM || ''}</span>
                                </div>
                                <div class="criteria-detail-item">
                                    <span class="criteria-detail-label">审查人</span>
                                    <span>${item.SCRM || ''}</span>
                                </div>
                                <div class="criteria-detail-item">
                                    <span class="criteria-detail-label">审查时间</span>
                                    <span>${item.SCSJ || ''}</span>
                                </div>
                                <div class="criteria-detail-item" style="grid-column: 1 / -1;">
                                    <span class="criteria-detail-label">未通过原因</span>
                                    <span>${item.WTGYY || ''}</span>
                                </div>
                                <div class="criteria-detail-item" style="grid-column: 1 / -1;">
                                    <span class="criteria-detail-label">备注</span>
                                    <span>${item.BZ || ''}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }

            $('#criteriaBody').html(content);
            $('#criteriaModal').fadeIn(300);
        }

        // 关闭审查标准模态框
        function closeCriteriaModal() {
            $('#criteriaModal').fadeOut(300);
        }

        // 获取状态样式类
        function getStatusClass(status) {
            if (status && status.includes('通过')) {
                return 'status-approved';
            } else if (status && status.includes('不通过')) {
                return 'status-rejected';
            } else {
                return 'status-pending';
            }
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.criteria-modal', function(e) {
            if (e.target === this) {
                closeCriteriaModal();
            }
        });
    </script>
</body>
</html>
