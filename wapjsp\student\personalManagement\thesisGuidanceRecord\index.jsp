<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生填写指导记录</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 指导记录页面样式 */
        .record-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .record-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .record-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            background: var(--warning-light);
            color: var(--warning-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-section i {
            color: var(--warning-color);
            margin-right: 8px;
        }
        
        .actions-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .actions-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-add {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .records-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .records-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .records-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .records-title i {
            color: var(--primary-color);
        }
        
        .record-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .record-item:last-child {
            border-bottom: none;
        }
        
        .record-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .record-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .record-content {
            flex: 1;
        }
        
        .record-title-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .record-teacher {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .record-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 80px;
            flex-shrink: 0;
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
            line-height: 1.4;
        }
        
        .detail-value.long-text {
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-confirmed {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-unconfirmed {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-operation {
            flex: 1;
            min-width: 60px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .record-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .record-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .record-modal-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .record-modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .record-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .record-modal-body {
            padding: var(--padding-md);
            max-height: 60vh;
            overflow-y: auto;
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .operation-buttons {
                flex-direction: column;
            }
            
            .record-modal-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生填写指导记录</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 指导记录头部 -->
        <div class="record-header">
            <div class="record-title">学生填写指导记录</div>
            <div class="record-desc">管理论文指导记录</div>
        </div>
        
        <!-- 错误信息 -->
        <c:if test="${not empty errorMessage}">
            <div class="notice-section">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                ${errorMessage}
            </div>
        </c:if>
        
        <!-- 操作按钮 -->
        <c:if test="${yxcz == 0}">
            <div class="actions-section">
                <div class="actions-title">
                    <i class="ace-icon fa fa-cogs"></i>
                    操作
                </div>
                <div class="action-buttons">
                    <button class="btn-mobile btn-add" onclick="openToWriteGuidance('');">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>填写新指导过程</span>
                    </button>
                </div>
            </div>
        </c:if>
        
        <!-- 指导记录列表 -->
        <div class="records-section">
            <div class="records-header">
                <div class="records-title">
                    <i class="ace-icon fa fa-list"></i>
                    指导过程列表
                </div>
            </div>
            
            <div id="recordsList">
                <!-- 动态加载记录列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreRecords();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无指导记录数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 记录详情模态框 -->
        <div class="record-modal" id="recordModal">
            <div class="record-modal-content">
                <div class="record-modal-header">
                    <div class="record-modal-title" id="recordModalTitle">记录详情</div>
                    <button class="record-close" onclick="closeRecordModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="record-modal-body" id="recordModalBody">
                    <!-- 动态加载记录详情内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let recordData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let csz = '${csz}';

        $(function() {
            initPage();
            loadRecords(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载更多记录
        function loadMoreRecords() {
            if (hasMore) {
                loadRecords(currentPage + 1, false);
            }
        }

        // 加载记录数据
        function loadRecords(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/thesis/guidanceRecord/query/getPageList",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            recordData = data.records;
                        } else {
                            recordData = recordData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = recordData.length < totalCount;

                        renderRecordsList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            recordData = [];
                            renderRecordsList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染记录列表
        function renderRecordsList(reset = false) {
            const container = $('#recordsList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : recordData.length - pageSize;
            const endIndex = recordData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (recordData[i]) {
                    const itemHtml = createRecordItem(recordData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建记录项目HTML
        function createRecordItem(item, index) {
            // 构建详情项目
            let detailsHtml = '';

            // 指导地点
            if (csz.indexOf("ZDDD:1") !== -1) {
                detailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">指导地点</span>
                        <span class="detail-value">${item.ZDDD || ''}</span>
                    </div>
                `;
            }

            // 指导内容
            if (csz.indexOf("JCNR:1") !== -1) {
                detailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">指导内容</span>
                        <span class="detail-value long-text" title="${item.JCNR || ''}">${item.JCNR || ''}</span>
                    </div>
                `;
            }

            // 指导意见
            if (csz.indexOf("ZDNR:1") !== -1) {
                detailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">指导意见</span>
                        <span class="detail-value long-text" title="${item.ZDNR || ''}">${item.ZDNR || ''}</span>
                    </div>
                `;
            }

            detailsHtml += `
                <div class="detail-item">
                    <span class="detail-label">指导日期</span>
                    <span class="detail-value">${item.ZDSJ || ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">提交时间</span>
                    <span class="detail-value">${item.CZSJ || ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">提交人</span>
                    <span class="detail-value">${item.CZRM || ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">已确认</span>
                    <span class="detail-value">
                        <span class="status-badge ${getConfirmStatusClass(item.YQR)}">${getConfirmStatusText(item.YQR)}</span>
                    </span>
                </div>
            `;

            // 构建操作按钮
            let operationButtons = '';
            if (item.YQR !== "1" && item.CZR === item.XH && '${yxcz}' === "0") {
                operationButtons += `
                    <button class="btn-operation btn-edit" onclick="openToWriteGuidance('${item.ID}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                    <button class="btn-operation btn-delete" onclick="revokeInfo('${item.ID}');">
                        <i class="ace-icon fa fa-trash"></i>
                        <span>删除</span>
                    </button>
                `;
            }
            operationButtons += `
                <button class="btn-operation btn-view" onclick="seeinfo('${item.ID}');">
                    <i class="ace-icon fa fa-eye"></i>
                    <span>查看</span>
                </button>
            `;

            return `
                <div class="record-item">
                    <div class="record-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="record-index">${index + 1}</div>
                            <div class="record-content">
                                <div class="record-title-text">${item.TMMC || ''}</div>
                                <div class="record-teacher">指导教师：${item.ZDJS || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="record-details">
                        ${detailsHtml}
                    </div>

                    <div class="operation-buttons">
                        ${operationButtons}
                    </div>
                </div>
            `;
        }

        // 获取确认状态样式类
        function getConfirmStatusClass(yqr) {
            switch (yqr) {
                case "1": return 'status-confirmed';
                case "0": return 'status-unconfirmed';
                default: return 'status-unconfirmed';
            }
        }

        // 获取确认状态文本
        function getConfirmStatusText(yqr) {
            switch (yqr) {
                case "1": return '是';
                case "0": return '否';
                default: return '';
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && recordData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (recordData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.records-section').hide();
            } else {
                $('#emptyState').hide();
                $('.records-section').show();
            }
        }

        // 填写新指导过程
        function openToWriteGuidance(id) {
            if (parent && parent.addTab) {
                parent.addTab('填写指导记录', '/student/personalManagement/thesis/guidanceRecord/edit/openToWriteGuidance?id=' + (id || ''));
            } else {
                location.href = "/student/personalManagement/thesis/guidanceRecord/edit/openToWriteGuidance?id=" + (id || "");
            }
        }

        // 查看记录详情
        function seeinfo(id) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/thesis/guidanceRecord/edit/openToViewGuidance",
                type: "get",
                data: "id=" + id,
                success: function(response) {
                    $('#recordModalTitle').text('查看指导记录');
                    $('#recordModalBody').html(response);
                    $('#recordModal').fadeIn(300);
                },
                error: function(xhr) {
                    showError("获取记录详情失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 关闭记录详情模态框
        function closeRecordModal() {
            $('#recordModal').fadeOut(300);
        }

        // 删除记录
        function revokeInfo(id) {
            if (confirm("删除后不能恢复，确定删除吗？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/thesis/guidanceRecord/del/delGuidance/" + id,
                    type: "post",
                    data: "tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/logout") != -1) {
                            showError("页面已过期，请刷新页面！");
                        } else if (data.result === "ok") {
                            showSuccess("删除成功！", function() {
                                loadRecords(1, true);
                            });
                        } else {
                            showError(data.result);
                        }
                    },
                    error: function(xhr) {
                        showError("删除失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 刷新数据
        function refreshData() {
            loadRecords(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.record-modal', function(e) {
            if (e.target === this) {
                closeRecordModal();
            }
        });
    </script>
</body>
</html>
