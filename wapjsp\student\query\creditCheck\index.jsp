<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学分核查</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学分核查页面样式 */
        .credit-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .credit-overview {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .overview-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .overview-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .credit-summary {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--margin-md);
        }
        
        .summary-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .summary-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .summary-number.earned {
            color: var(--success-color);
        }
        
        .summary-number.required {
            color: var(--primary-color);
        }
        
        .summary-number.remaining {
            color: var(--warning-color);
        }
        
        .summary-number.gpa {
            color: var(--info-color);
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .progress-bar {
            background: var(--bg-tertiary);
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin-bottom: var(--margin-sm);
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), var(--primary-color));
            border-radius: 10px;
            transition: width var(--transition-base);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .progress-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            text-align: center;
        }
        
        .credit-categories {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .categories-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .categories-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .category-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-item:active {
            background: var(--bg-color-active);
        }
        
        .category-basic {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .category-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .category-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-in-progress {
            background: var(--warning-color);
            color: white;
        }
        
        .status-not-started {
            background: var(--text-disabled);
            color: white;
        }
        
        .category-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            text-align: center;
        }
        
        .detail-label {
            display: block;
            margin-bottom: 2px;
        }
        
        .detail-value {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .category-progress {
            background: var(--bg-tertiary);
            border-radius: 8px;
            height: 8px;
            overflow: hidden;
        }
        
        .category-progress-fill {
            height: 100%;
            border-radius: 8px;
            transition: width var(--transition-base);
        }
        
        .progress-completed {
            background: var(--success-color);
        }
        
        .progress-in-progress {
            background: var(--warning-color);
        }
        
        .progress-not-started {
            background: var(--text-disabled);
        }
        
        .semester-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .selector-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .semester-tabs {
            display: flex;
            gap: var(--spacing-xs);
            overflow-x: auto;
            padding-bottom: var(--padding-xs);
        }
        
        .semester-tab {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            white-space: nowrap;
            transition: all var(--transition-base);
        }
        
        .semester-tab:hover {
            background: var(--primary-light);
        }
        
        .semester-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .semester-credits {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .semester-credits.show {
            display: block;
        }
        
        .credits-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .credits-title {
            display: flex;
            align-items: center;
        }
        
        .credits-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .credits-total {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .course-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-item:last-child {
            border-bottom: none;
        }
        
        .course-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .course-grade {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .grade-excellent {
            background: var(--success-color);
            color: white;
        }
        
        .grade-good {
            background: var(--info-color);
            color: white;
        }
        
        .grade-pass {
            background: var(--warning-color);
            color: white;
        }
        
        .grade-fail {
            background: var(--error-color);
            color: white;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .course-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .graduation-check {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .check-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .check-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .check-result {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .check-status {
            font-size: var(--font-size-h4);
            font-weight: 600;
            margin-bottom: var(--margin-sm);
        }
        
        .check-status.qualified {
            color: var(--success-color);
        }
        
        .check-status.unqualified {
            color: var(--error-color);
        }
        
        .check-message {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .check-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-export {
            background: var(--info-color);
            color: white;
        }
        
        .btn-print {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学分核查</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="credit-header">
            <div class="header-title">学分核查</div>
            <div class="header-subtitle">查看学分获得情况和毕业要求</div>
        </div>

        <!-- 学分概览 -->
        <div class="credit-overview">
            <div class="overview-title">
                <i class="ace-icon fa fa-dashboard"></i>
                <span>学分概览</span>
            </div>

            <div class="credit-summary">
                <div class="summary-card">
                    <div class="summary-number earned" id="earnedCredits">0</div>
                    <div class="summary-label">已获学分</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number required" id="requiredCredits">0</div>
                    <div class="summary-label">毕业要求</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number remaining" id="remainingCredits">0</div>
                    <div class="summary-label">剩余学分</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number gpa" id="gpaScore">0.00</div>
                    <div class="summary-label">平均绩点</div>
                </div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%;">0%</div>
            </div>
            <div class="progress-text" id="progressText">学分完成进度</div>
        </div>

        <!-- 学分类别 -->
        <div class="credit-categories">
            <div class="categories-header">
                <i class="ace-icon fa fa-list"></i>
                <span>学分类别</span>
            </div>

            <div id="categoriesList">
                <!-- 学分类别列表将动态填充 -->
            </div>
        </div>

        <!-- 学期选择 -->
        <div class="semester-selector">
            <div class="selector-title">
                <i class="ace-icon fa fa-calendar"></i>
                <span>学期学分</span>
            </div>

            <div class="semester-tabs" id="semesterTabs">
                <!-- 学期标签将动态填充 -->
            </div>
        </div>

        <!-- 学期学分详情 -->
        <div class="semester-credits" id="semesterCredits">
            <div class="credits-header">
                <div class="credits-title">
                    <i class="ace-icon fa fa-book"></i>
                    <span id="semesterTitle">学期课程</span>
                </div>
                <div class="credits-total" id="semesterTotal">0学分</div>
            </div>

            <div id="coursesList">
                <!-- 课程列表将动态填充 -->
            </div>
        </div>

        <!-- 毕业资格核查 -->
        <div class="graduation-check">
            <div class="check-title">
                <i class="ace-icon fa fa-graduation-cap"></i>
                <span>毕业资格核查</span>
            </div>

            <div class="check-result">
                <div class="check-status" id="checkStatus">核查中...</div>
                <div class="check-message" id="checkMessage">正在核查毕业资格...</div>
            </div>

            <div class="check-actions">
                <button class="btn-mobile btn-export flex-1" onclick="exportCreditReport();">
                    <i class="ace-icon fa fa-download"></i>
                    <span>导出报告</span>
                </button>
                <button class="btn-mobile btn-print flex-1" onclick="printCreditReport();">
                    <i class="ace-icon fa fa-print"></i>
                    <span>打印报告</span>
                </button>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let creditData = {};
        let categoriesData = [];
        let semestersData = [];
        let currentSemester = '';

        $(function() {
            initPage();
            loadCreditData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学分数据
        function loadCreditData() {
            showLoading(true);

            $.ajax({
                url: "/student/query/creditCheck/getCreditData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    creditData = data.creditData || {};
                    categoriesData = data.categories || [];
                    semestersData = data.semesters || [];

                    updateCreditOverview();
                    renderCategories();
                    renderSemesters();
                    checkGraduationStatus();

                    showLoading(false);
                },
                error: function() {
                    showError('加载学分数据失败');
                    showLoading(false);
                }
            });
        }

        // 更新学分概览
        function updateCreditOverview() {
            const earned = creditData.earnedCredits || 0;
            const required = creditData.requiredCredits || 0;
            const remaining = Math.max(0, required - earned);
            const gpa = creditData.gpa || 0;

            $('#earnedCredits').text(earned);
            $('#requiredCredits').text(required);
            $('#remainingCredits').text(remaining);
            $('#gpaScore').text(gpa.toFixed(2));

            // 更新进度条
            const progress = required > 0 ? Math.min(100, (earned / required) * 100) : 0;
            $('#progressFill').css('width', progress + '%').text(Math.round(progress) + '%');

            if (progress >= 100) {
                $('#progressText').text('已完成毕业学分要求');
            } else {
                $('#progressText').text(`学分完成进度 (还需${remaining}学分)`);
            }
        }

        // 渲染学分类别
        function renderCategories() {
            const container = $('#categoriesList');
            container.empty();

            categoriesData.forEach(category => {
                const categoryHtml = createCategoryItem(category);
                container.append(categoryHtml);
            });
        }

        // 创建类别项
        function createCategoryItem(category) {
            const progress = category.required > 0 ? (category.earned / category.required) * 100 : 0;
            const status = getStatusClass(progress);
            const statusText = getStatusText(progress);

            return `
                <div class="category-item" onclick="showCategoryDetail('${category.id}')">
                    <div class="category-basic">
                        <div class="category-name">${category.name}</div>
                        <div class="category-status ${status}">${statusText}</div>
                    </div>
                    <div class="category-details">
                        <div class="detail-item">
                            <span class="detail-label">已获</span>
                            <span class="detail-value">${category.earned}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">要求</span>
                            <span class="detail-value">${category.required}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">进度</span>
                            <span class="detail-value">${Math.round(progress)}%</span>
                        </div>
                    </div>
                    <div class="category-progress">
                        <div class="category-progress-fill ${getProgressClass(progress)}" style="width: ${Math.min(100, progress)}%"></div>
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(progress) {
            if (progress >= 100) return 'status-completed';
            if (progress > 0) return 'status-in-progress';
            return 'status-not-started';
        }

        // 获取状态文本
        function getStatusText(progress) {
            if (progress >= 100) return '已完成';
            if (progress > 0) return '进行中';
            return '未开始';
        }

        // 获取进度条样式类
        function getProgressClass(progress) {
            if (progress >= 100) return 'progress-completed';
            if (progress > 0) return 'progress-in-progress';
            return 'progress-not-started';
        }

        // 显示类别详情
        function showCategoryDetail(categoryId) {
            const category = categoriesData.find(c => c.id === categoryId);
            if (!category) return;

            let message = `${category.name} - 详细信息\n\n`;
            message += `已获学分：${category.earned}\n`;
            message += `要求学分：${category.required}\n`;
            message += `剩余学分：${Math.max(0, category.required - category.earned)}\n`;

            if (category.description) {
                message += `\n说明：${category.description}\n`;
            }

            if (category.courses && category.courses.length > 0) {
                message += `\n相关课程：\n`;
                category.courses.forEach(course => {
                    message += `• ${course.name} (${course.credits}学分) - ${course.grade || '未修'}\n`;
                });
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 渲染学期标签
        function renderSemesters() {
            const container = $('#semesterTabs');
            container.empty();

            semestersData.forEach((semester, index) => {
                const isActive = index === 0 ? 'active' : '';
                const tabHtml = `
                    <div class="semester-tab ${isActive}" data-semester="${semester.id}" onclick="selectSemester('${semester.id}')">
                        ${semester.name}
                    </div>
                `;
                container.append(tabHtml);
            });

            // 默认选择第一个学期
            if (semestersData.length > 0) {
                selectSemester(semestersData[0].id);
            }
        }

        // 选择学期
        function selectSemester(semesterId) {
            currentSemester = semesterId;

            // 更新标签状态
            $('.semester-tab').removeClass('active');
            $(`.semester-tab[data-semester="${semesterId}"]`).addClass('active');

            // 加载学期课程
            loadSemesterCourses(semesterId);
        }

        // 加载学期课程
        function loadSemesterCourses(semesterId) {
            $.ajax({
                url: "/student/query/creditCheck/getSemesterCourses",
                type: "post",
                data: { semesterId: semesterId },
                dataType: "json",
                success: function(data) {
                    const semester = semestersData.find(s => s.id === semesterId);
                    const courses = data.courses || [];

                    renderSemesterCourses(semester, courses);
                    showSemesterCredits();
                },
                error: function() {
                    console.log('加载学期课程失败');
                }
            });
        }

        // 渲染学期课程
        function renderSemesterCourses(semester, courses) {
            $('#semesterTitle').text(`${semester.name} 课程`);

            const totalCredits = courses.reduce((sum, course) => sum + (course.credits || 0), 0);
            $('#semesterTotal').text(`${totalCredits}学分`);

            const container = $('#coursesList');
            container.empty();

            if (courses.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        该学期暂无课程记录
                    </div>
                `);
                return;
            }

            courses.forEach(course => {
                const courseHtml = createCourseItem(course);
                container.append(courseHtml);
            });
        }

        // 创建课程项
        function createCourseItem(course) {
            const gradeClass = getGradeClass(course.grade);
            const gradeText = course.grade || '未评定';

            return `
                <div class="course-item">
                    <div class="course-basic">
                        <div class="course-name">${course.name}</div>
                        <div class="course-grade ${gradeClass}">${gradeText}</div>
                    </div>
                    <div class="course-details">
                        <div class="course-detail-item">
                            <span>课程代码:</span>
                            <span>${course.code}</span>
                        </div>
                        <div class="course-detail-item">
                            <span>学分:</span>
                            <span>${course.credits}</span>
                        </div>
                        <div class="course-detail-item">
                            <span>类别:</span>
                            <span>${course.category}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取成绩样式类
        function getGradeClass(grade) {
            if (!grade) return '';

            const numGrade = parseFloat(grade);
            if (numGrade >= 90) return 'grade-excellent';
            if (numGrade >= 80) return 'grade-good';
            if (numGrade >= 60) return 'grade-pass';
            return 'grade-fail';
        }

        // 显示学期学分
        function showSemesterCredits() {
            $('#semesterCredits').addClass('show');
        }

        // 核查毕业资格
        function checkGraduationStatus() {
            const earned = creditData.earnedCredits || 0;
            const required = creditData.requiredCredits || 0;
            const gpa = creditData.gpa || 0;

            let status = '';
            let message = '';
            let statusClass = '';

            if (earned >= required && gpa >= 2.0) {
                status = '符合毕业要求';
                message = '恭喜！您已满足毕业学分要求，平均绩点达标。';
                statusClass = 'qualified';
            } else {
                status = '暂不符合毕业要求';
                const reasons = [];

                if (earned < required) {
                    reasons.push(`还需获得${required - earned}学分`);
                }

                if (gpa < 2.0) {
                    reasons.push(`平均绩点需达到2.0以上（当前${gpa.toFixed(2)}）`);
                }

                message = `您还需要：${reasons.join('；')}。`;
                statusClass = 'unqualified';
            }

            $('#checkStatus').text(status).removeClass('qualified unqualified').addClass(statusClass);
            $('#checkMessage').text(message);
        }

        // 导出学分报告
        function exportCreditReport() {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要导出学分报告吗？', function(confirmed) {
                    if (confirmed) {
                        doExportReport();
                    }
                });
            } else {
                if (confirm('确定要导出学分报告吗？')) {
                    doExportReport();
                }
            }
        }

        // 执行导出报告
        function doExportReport() {
            // 创建下载链接
            const link = document.createElement('a');
            link.href = '/student/query/creditCheck/exportReport';
            link.download = '学分报告.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showSuccess('学分报告导出成功');
        }

        // 打印学分报告
        function printCreditReport() {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要打印学分报告吗？', function(confirmed) {
                    if (confirmed) {
                        doPrintReport();
                    }
                });
            } else {
                if (confirm('确定要打印学分报告吗？')) {
                    doPrintReport();
                }
            }
        }

        // 执行打印报告
        function doPrintReport() {
            // 打开打印页面
            const printWindow = window.open('/student/query/creditCheck/printReport', '_blank');
            if (printWindow) {
                printWindow.onload = function() {
                    printWindow.print();
                };
            } else {
                showError('无法打开打印页面，请检查浏览器设置');
            }
        }

        // 刷新数据
        function refreshData() {
            loadCreditData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
