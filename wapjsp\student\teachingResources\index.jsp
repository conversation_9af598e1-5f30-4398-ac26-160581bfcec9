<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教学资源</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教学资源页面样式 */
        .resource-header {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .resource-icon {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            opacity: 0.9;
        }
        
        .resource-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .resource-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .function-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .function-card:hover::before {
            left: 100%;
        }
        
        .function-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .function-card:active {
            transform: translateY(-2px);
        }
        
        .function-icon {
            font-size: 40px;
            margin-bottom: var(--margin-md);
            color: var(--primary-color);
            position: relative;
            z-index: 1;
        }
        
        .function-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .function-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
            position: relative;
            z-index: 1;
        }
        
        .function-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: var(--success-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: var(--font-size-mini);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }
        
        .quick-search {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-search-title i {
            color: var(--warning-color);
        }
        
        .search-form {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .search-input {
            flex: 1;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-sm) var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .search-btn:hover {
            background: var(--primary-dark);
        }
        
        .popular-resources {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .popular-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .popular-title i {
            color: var(--success-color);
        }
        
        .popular-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .popular-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .popular-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .popular-item-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: var(--info-light);
            color: var(--info-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-right: var(--margin-sm);
        }
        
        .popular-item-content {
            flex: 1;
        }
        
        .popular-item-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .popular-item-desc {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .popular-item-arrow {
            color: var(--text-disabled);
            font-size: 14px;
        }
        
        .stats-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--error-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        @media (max-width: 480px) {
            .function-grid {
                grid-template-columns: 1fr;
            }
            
            .search-form {
                flex-direction: column;
            }
            
            .search-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教学资源</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 资源头部 -->
        <div class="resource-header">
            <div class="resource-icon">
                <i class="ace-icon fa fa-university"></i>
            </div>
            <div class="resource-title">教学资源查询</div>
            <div class="resource-desc">查询教室、课程、教师等教学资源信息</div>
        </div>
        
        <!-- 快速搜索 -->
        <div class="quick-search">
            <div class="quick-search-title">
                <i class="ace-icon fa fa-search"></i>
                快速搜索
            </div>
            <div class="search-form">
                <input type="text" class="search-input" id="searchInput" placeholder="输入教室号、课程名或教师姓名">
                <button class="search-btn" onclick="quickSearch();">
                    <i class="ace-icon fa fa-search"></i>
                    搜索
                </button>
            </div>
        </div>
        
        <!-- 资源统计 -->
        <div class="stats-section">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                资源统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalClassrooms">0</div>
                    <div class="stat-label">总教室</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="freeClassrooms">0</div>
                    <div class="stat-label">空闲教室</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalTeachers">0</div>
                    <div class="stat-label">任课教师</div>
                </div>
            </div>
        </div>
        
        <!-- 功能模块 -->
        <div class="function-grid">
            <div class="function-card" onclick="goToFunction('freeClassroomQuery');">
                <div class="function-badge">
                    <i class="ace-icon fa fa-star"></i>
                </div>
                <div class="function-icon">
                    <i class="ace-icon fa fa-home"></i>
                </div>
                <div class="function-title">空闲教室查询</div>
                <div class="function-desc">查询指定时间的空闲教室</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('classroomStatusQuery');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-info-circle"></i>
                </div>
                <div class="function-title">教室状态查询</div>
                <div class="function-desc">查看教室使用状态信息</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('classroomCurriculum');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-calendar"></i>
                </div>
                <div class="function-title">教室课表查询</div>
                <div class="function-desc">查看指定教室的课程安排</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('teacherCurriculum');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-user"></i>
                </div>
                <div class="function-title">教师课表查询</div>
                <div class="function-desc">查询教师的课程安排</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('courseCurriculum');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-book"></i>
                </div>
                <div class="function-title">课程安排查询</div>
                <div class="function-desc">查看课程的时间地点安排</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('classCurriculum');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-users"></i>
                </div>
                <div class="function-title">班级课表查询</div>
                <div class="function-desc">查询班级的课程表</div>
            </div>
        </div>
        
        <!-- 热门资源 -->
        <div class="popular-resources">
            <div class="popular-title">
                <i class="ace-icon fa fa-fire"></i>
                热门查询
            </div>
            <div class="popular-list">
                <div class="popular-item" onclick="goToPopular('today-free');">
                    <div class="popular-item-icon">
                        <i class="ace-icon fa fa-clock-o"></i>
                    </div>
                    <div class="popular-item-content">
                        <div class="popular-item-title">今日空闲教室</div>
                        <div class="popular-item-desc">查看今天的空闲教室</div>
                    </div>
                    <i class="popular-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="popular-item" onclick="goToPopular('current-week');">
                    <div class="popular-item-icon">
                        <i class="ace-icon fa fa-calendar-week-o"></i>
                    </div>
                    <div class="popular-item-content">
                        <div class="popular-item-title">本周课程安排</div>
                        <div class="popular-item-desc">查看本周的课程安排</div>
                    </div>
                    <i class="popular-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="popular-item" onclick="goToPopular('main-building');">
                    <div class="popular-item-icon">
                        <i class="ace-icon fa fa-building"></i>
                    </div>
                    <div class="popular-item-content">
                        <div class="popular-item-title">主楼教室查询</div>
                        <div class="popular-item-desc">查询主楼的教室信息</div>
                    </div>
                    <i class="popular-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let resourceStats = {};

        $(function() {
            initPage();
            loadResourceStats();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 绑定搜索框回车事件
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    quickSearch();
                }
            });
        }

        // 加载资源统计
        function loadResourceStats() {
            showLoading(true);

            // 获取教学资源统计信息
            $.ajax({
                url: "/student/teachingResources/getStats",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        resourceStats = data.data;
                        updateStats();
                    } else {
                        // 设置默认统计
                        updateStats({
                            totalClassrooms: 0,
                            freeClassrooms: 0,
                            totalTeachers: 0
                        });
                    }
                },
                error: function() {
                    console.log('获取资源统计失败');
                    // 设置默认统计
                    updateStats({
                        totalClassrooms: 0,
                        freeClassrooms: 0,
                        totalTeachers: 0
                    });
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新统计信息
        function updateStats(stats = resourceStats) {
            $('#totalClassrooms').text(stats.totalClassrooms || 0);
            $('#freeClassrooms').text(stats.freeClassrooms || 0);
            $('#totalTeachers').text(stats.totalTeachers || 0);
        }

        // 快速搜索
        function quickSearch() {
            const keyword = $('#searchInput').val().trim();
            if (!keyword) {
                showError('请输入搜索关键词');
                return;
            }

            // 根据关键词类型判断搜索类型
            if (/^\d+$/.test(keyword)) {
                // 纯数字，可能是教室号
                searchClassroom(keyword);
            } else if (keyword.includes('教师') || keyword.includes('老师')) {
                // 包含教师关键词
                searchTeacher(keyword);
            } else {
                // 其他情况，搜索课程
                searchCourse(keyword);
            }
        }

        // 搜索教室
        function searchClassroom(keyword) {
            if (parent && parent.addTab) {
                parent.addTab('教室搜索结果', '/student/teachingResources/classroomStatusQuery/index?keyword=' + encodeURIComponent(keyword));
            } else {
                window.location.href = '/student/teachingResources/classroomStatusQuery/index?keyword=' + encodeURIComponent(keyword);
            }
        }

        // 搜索教师
        function searchTeacher(keyword) {
            if (parent && parent.addTab) {
                parent.addTab('教师搜索结果', '/student/teachingResources/teacherCurriculum/index?keyword=' + encodeURIComponent(keyword));
            } else {
                window.location.href = '/student/teachingResources/teacherCurriculum/index?keyword=' + encodeURIComponent(keyword);
            }
        }

        // 搜索课程
        function searchCourse(keyword) {
            if (parent && parent.addTab) {
                parent.addTab('课程搜索结果', '/student/teachingResources/courseCurriculum/index?keyword=' + encodeURIComponent(keyword));
            } else {
                window.location.href = '/student/teachingResources/courseCurriculum/index?keyword=' + encodeURIComponent(keyword);
            }
        }

        // 跳转到功能页面
        function goToFunction(functionName) {
            let url = '';
            let title = '';

            switch(functionName) {
                case 'freeClassroomQuery':
                    url = '/student/teachingResources/freeClassroomQuery/index';
                    title = '空闲教室查询';
                    break;
                case 'classroomStatusQuery':
                    url = '/student/teachingResources/classroomStatusQuery/index';
                    title = '教室状态查询';
                    break;
                case 'classroomCurriculum':
                    url = '/student/teachingResources/classroomCurriculum/index';
                    title = '教室课表查询';
                    break;
                case 'teacherCurriculum':
                    url = '/student/teachingResources/teacherCurriculum/index';
                    title = '教师课表查询';
                    break;
                case 'courseCurriculum':
                    url = '/student/teachingResources/courseCurriculum/index';
                    title = '课程安排查询';
                    break;
                case 'classCurriculum':
                    url = '/student/teachingResources/classCurriculum/index';
                    title = '班级课表查询';
                    break;
                default:
                    showError('功能暂未开放');
                    return;
            }

            if (parent && parent.addTab) {
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 跳转到热门查询
        function goToPopular(type) {
            switch(type) {
                case 'today-free':
                    goToFunction('freeClassroomQuery');
                    break;
                case 'current-week':
                    if (parent && parent.addTab) {
                        parent.addTab('本周课程安排', '/student/weekLySchedule/index');
                    } else {
                        window.location.href = '/student/weekLySchedule/index';
                    }
                    break;
                case 'main-building':
                    if (parent && parent.addTab) {
                        parent.addTab('主楼教室', '/student/teachingResources/classroomCurriculum/index?building=main');
                    } else {
                        window.location.href = '/student/teachingResources/classroomCurriculum/index?building=main';
                    }
                    break;
                default:
                    showError('功能暂未开放');
            }
        }

        // 刷新数据
        function refreshData() {
            loadResourceStats();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
