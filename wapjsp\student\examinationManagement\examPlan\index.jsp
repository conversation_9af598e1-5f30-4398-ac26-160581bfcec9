<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>考试安排</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 考试安排页面样式 */
        .view-mode-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .mode-button {
            flex: 1;
            padding: var(--padding-sm);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .mode-button.active {
            background: var(--primary-color);
            color: white;
        }
        
        .mode-button:not(.active) {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
        }
        
        .warning-notice {
            background: var(--warning-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .exam-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .exam-item.completed {
            border-left: 4px solid var(--text-disabled);
        }
        
        .exam-item.upcoming {
            border-left: 4px solid var(--primary-color);
        }
        
        .exam-item.current {
            border-left: 4px solid var(--success-color);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .exam-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            padding: var(--padding-md);
            position: relative;
        }
        
        .exam-header.completed {
            background: linear-gradient(135deg, var(--text-disabled), var(--secondary-color));
        }
        
        .exam-header.current {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
        }
        
        .exam-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .exam-course {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .exam-status {
            position: absolute;
            top: var(--padding-sm);
            right: var(--padding-sm);
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .exam-content {
            padding: var(--padding-md);
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .detail-icon {
            color: var(--primary-color);
            font-size: 16px;
            width: 16px;
            text-align: center;
        }
        
        .detail-content {
            flex: 1;
        }
        
        .detail-label {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            margin-bottom: 2px;
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .exam-tips {
            background: var(--error-light);
            border: 1px solid var(--error-color);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
        }
        
        .tips-title {
            font-size: var(--font-size-small);
            color: var(--error-color);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .tips-content {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .no-scheduled-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            color: var(--warning-color);
        }
        
        .unscheduled-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--warning-color);
        }
        
        .unscheduled-item:last-child {
            margin-bottom: 0;
        }
        
        .unscheduled-course {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .unscheduled-exam {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .unscheduled-note {
            font-size: var(--font-size-mini);
            color: var(--warning-color);
            margin-top: 4px;
            font-style: italic;
        }
        
        .timeline-view {
            display: block;
        }
        
        .calendar-view {
            display: none;
        }
        
        .calendar-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
        }
        
        .calendar-nav {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
        }
        
        .calendar-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        @media (max-width: 480px) {
            .exam-details {
                grid-template-columns: 1fr;
            }
            
            .detail-item {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">考试安排</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 欠费提醒（特定学校） -->
        <c:if test="${paramVal == '100002' && count != 0}">
            <div class="warning-notice">
                <i class="ace-icon fa fa-warning"></i>
                <strong>欠费提醒：</strong>欠费金额：${count}元，请及时缴费，缴费后可查询考试安排！
            </div>
        </c:if>
        
        <!-- 视图模式选择 -->
        <c:if test="${paramVal != '100002' || (paramVal == '100002' && count == 0)}">
            <c:if test="${!(empty kwKsapViewList && empty notimeAndPlace)}">
                <div class="view-mode-selector">
                    <button class="mode-button active" id="timelineBtn" onclick="switchView('timeline');">
                        <i class="ace-icon fa fa-clock-o"></i>
                        <span>时间轴</span>
                    </button>
                    <button class="mode-button" id="calendarBtn" onclick="switchView('calendar');">
                        <i class="ace-icon fa fa-calendar"></i>
                        <span>日历</span>
                    </button>
                </div>
            </c:if>
        </c:if>
        
        <!-- 时间轴视图 -->
        <div id="timelineView" class="timeline-view">
            <c:if test="${paramVal != '100002' || (paramVal == '100002' && count == 0)}">
                <c:choose>
                    <c:when test="${empty kwKsapViewList && empty notimeAndPlace}">
                        <div class="empty-state">
                            <i class="ace-icon fa fa-calendar-check-o"></i>
                            <div>还没有和你相关的考试信息！</div>
                        </div>
                    </c:when>
                    <c:otherwise>
                        <!-- 已安排时间的考试 -->
                        <c:if test="${not empty kwKsapViewList}">
                            <c:forEach items="${kwKsapViewList}" var="exam">
                                <div class="exam-item ${exam.isbeyonddate == '1' ? 'completed' : (exam.isbeyonddate == '2' ? 'current' : 'upcoming')}">
                                    <div class="exam-header ${exam.isbeyonddate == '1' ? 'completed' : (exam.isbeyonddate == '2' ? 'current' : '')}">
                                        <div class="exam-title">
                                            <c:choose>
                                                <c:when test="${exam.wbks}">
                                                    ${exam.ksmc}
                                                </c:when>
                                                <c:otherwise>
                                                    ${exam.kcm}
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                        <c:if test="${!exam.wbks && exam.kch != null && exam.kxh != null}">
                                            <div class="exam-course">${exam.kch}-${exam.kxh}</div>
                                        </c:if>
                                        <div class="exam-status">
                                            <c:choose>
                                                <c:when test="${exam.isbeyonddate == '1'}">已结束</c:when>
                                                <c:when test="${exam.isbeyonddate == '2'}">进行中</c:when>
                                                <c:otherwise>未开始</c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                    
                                    <div class="exam-content">
                                        <c:choose>
                                            <c:when test="${paramVal == '100006' && exam.bz == '缓考'}">
                                                <div class="exam-details">
                                                    <div class="detail-item full-width">
                                                        <i class="detail-icon ace-icon fa fa-info-circle"></i>
                                                        <div class="detail-content">
                                                            <div class="detail-label">考试状态</div>
                                                            <div class="detail-value">缓考</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </c:when>
                                            <c:otherwise>
                                                <div class="exam-details">
                                                    <c:if test="${exam.wbks}">
                                                        <div class="detail-item">
                                                            <i class="detail-icon ace-icon fa fa-clock-o"></i>
                                                            <div class="detail-content">
                                                                <div class="detail-label">入场时间</div>
                                                                <div class="detail-value">${exam.kszc}</div>
                                                            </div>
                                                        </div>
                                                    </c:if>
                                                    
                                                    <c:if test="${!exam.wbks}">
                                                        <div class="detail-item">
                                                            <i class="detail-icon ace-icon fa fa-book"></i>
                                                            <div class="detail-content">
                                                                <div class="detail-label">考试名称</div>
                                                                <div class="detail-value">${exam.ksmc}</div>
                                                            </div>
                                                        </div>
                                                    </c:if>
                                                    
                                                    <div class="detail-item">
                                                        <i class="detail-icon ace-icon fa fa-calendar"></i>
                                                        <div class="detail-content">
                                                            <div class="detail-label">考试日期</div>
                                                            <div class="detail-value">
                                                                <fmt:parseDate value="${exam.ksrq}" pattern="yyyyMMdd" var="ksrq" />
                                                                <fmt:formatDate pattern="yyyy-MM-dd" value="${ksrq}" />
                                                                <c:choose>
                                                                    <c:when test="${exam.ksxq==1}">星期一</c:when>
                                                                    <c:when test="${exam.ksxq==2}">星期二</c:when>
                                                                    <c:when test="${exam.ksxq==3}">星期三</c:when>
                                                                    <c:when test="${exam.ksxq==4}">星期四</c:when>
                                                                    <c:when test="${exam.ksxq==5}">星期五</c:when>
                                                                    <c:when test="${exam.ksxq==6}">星期六</c:when>
                                                                    <c:when test="${exam.ksxq==7}">星期日</c:when>
                                                                </c:choose>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="detail-item">
                                                        <i class="detail-icon ace-icon fa fa-clock-o"></i>
                                                        <div class="detail-content">
                                                            <div class="detail-label">考试时间</div>
                                                            <div class="detail-value">${exam.kssj}</div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="detail-item">
                                                        <i class="detail-icon ace-icon fa fa-map-marker"></i>
                                                        <div class="detail-content">
                                                            <div class="detail-label">考试地点</div>
                                                            <div class="detail-value">${exam.xqm} ${exam.jxlm} ${exam.jasm}</div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="detail-item">
                                                        <i class="detail-icon ace-icon fa fa-user"></i>
                                                        <div class="detail-content">
                                                            <div class="detail-label">座位号</div>
                                                            <div class="detail-value">${exam.zwh}</div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="detail-item">
                                                        <i class="detail-icon ace-icon fa fa-id-card"></i>
                                                        <div class="detail-content">
                                                            <div class="detail-label">准考证号</div>
                                                            <div class="detail-value">${exam.zkzh}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <c:if test="${!exam.wbks && not empty exam.kstsxx}">
                                                    <div class="exam-tips">
                                                        <div class="tips-title">
                                                            <i class="ace-icon fa fa-warning"></i>
                                                            考试提示信息
                                                        </div>
                                                        <div class="tips-content">${exam.kstsxx}</div>
                                                    </div>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>
                            </c:forEach>
                        </c:if>
                        
                        <!-- 未安排时间的考试 -->
                        <c:if test="${not empty notimeAndPlace}">
                            <div class="no-scheduled-section">
                                <div class="section-title">
                                    <i class="ace-icon fa fa-clock-o"></i>
                                    未安排时间课程
                                </div>
                                
                                <c:forEach items="${notimeAndPlace}" var="notime">
                                    <div class="unscheduled-item">
                                        <div class="unscheduled-course">
                                            ${notime.kcm}
                                            <c:if test="${not empty notime.kch}">
                                                <small>(${notime.kch})</small>
                                            </c:if>
                                        </div>
                                        <div class="unscheduled-exam">${notime.ksmc}</div>
                                        
                                        <c:choose>
                                            <c:when test="${paramVal == '100006'}">
                                                <c:if test="${not empty notime.jasm}">
                                                    <div class="unscheduled-note">考试原因: ${notime.jasm}</div>
                                                </c:if>
                                                <c:if test="${not empty notime.bz}">
                                                    <div class="unscheduled-note">备注: ${notime.bz}</div>
                                                </c:if>
                                            </c:when>
                                            <c:otherwise>
                                                <div class="unscheduled-note">
                                                    时间: ${notime.kssj == null ? "暂未安排" : notime.kssj} | 
                                                    地点: ${notime.xqm == null ? "暂未安排" : notime.xqm.concat(' ').concat(notime.jxlm).concat(' ').concat(notime.jasm)}
                                                </div>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </c:forEach>
                            </div>
                        </c:if>
                    </c:otherwise>
                </c:choose>
            </c:if>
        </div>
        
        <!-- 日历视图 -->
        <div id="calendarView" class="calendar-view">
            <div class="calendar-container">
                <div class="calendar-header">
                    <button class="calendar-nav" onclick="previousMonth();">
                        <i class="ace-icon fa fa-chevron-left"></i>
                    </button>
                    <div class="calendar-title" id="calendarTitle">日历视图</div>
                    <button class="calendar-nav" onclick="nextMonth();">
                        <i class="ace-icon fa fa-chevron-right"></i>
                    </button>
                </div>
                <div id="calendarContent">
                    <!-- 日历内容将通过JavaScript渲染 -->
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentView = 'timeline';
        let examData = [];

        $(function() {
            initPage();
            loadExamData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载考试数据
        function loadExamData() {
            // 从服务器获取考试数据
            $.ajax({
                url: "/student/examinationManagement/examPlan/detail",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.length > 0) {
                        examData = data;
                        renderCalendarView();
                    }
                },
                error: function() {
                    console.log('获取考试数据失败');
                }
            });
        }

        // 切换视图
        function switchView(viewType) {
            currentView = viewType;

            // 更新按钮状态
            $('.mode-button').removeClass('active');
            if (viewType === 'timeline') {
                $('#timelineBtn').addClass('active');
                $('#timelineView').show();
                $('#calendarView').hide();
            } else {
                $('#calendarBtn').addClass('active');
                $('#timelineView').hide();
                $('#calendarView').show();
                renderCalendarView();
            }
        }

        // 渲染日历视图
        function renderCalendarView() {
            if (currentView !== 'calendar') return;

            const container = $('#calendarContent');
            container.empty();

            if (!examData || examData.length === 0) {
                container.html('<div class="empty-state"><i class="ace-icon fa fa-calendar"></i><div>暂无考试数据</div></div>');
                return;
            }

            // 简化的日历视图，显示考试列表
            let calendarHtml = '<div class="exam-list">';

            examData.forEach(function(exam) {
                calendarHtml += `
                    <div class="exam-calendar-item">
                        <div class="exam-date">${exam.start}</div>
                        <div class="exam-title">${exam.title}</div>
                    </div>
                `;
            });

            calendarHtml += '</div>';
            container.html(calendarHtml);
        }

        // 日历导航
        function previousMonth() {
            // 实现上一月逻辑
            console.log('Previous month');
        }

        function nextMonth() {
            // 实现下一月逻辑
            console.log('Next month');
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>

    <style>
        /* 日历视图额外样式 */
        .exam-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .exam-calendar-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .exam-date {
            font-size: var(--font-size-small);
            color: var(--primary-color);
            font-weight: 500;
        }

        .exam-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
    </style>
</body>
</html>
