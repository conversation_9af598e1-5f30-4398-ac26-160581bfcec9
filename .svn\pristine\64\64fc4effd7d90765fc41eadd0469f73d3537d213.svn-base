package educationalAdministration.student.individualApplication.creditCertification.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.urpSoft.business.utils.UrpResult;
import com.urpSoft.core.data.query.component.QueryInfo;
import com.urpSoft.core.document.common.service.CommonExportService;
import com.urpSoft.core.pagination.page.Page;
import com.urpSoft.core.pagination.service.IPageService;
import com.urpSoft.core.util.AuthUtil;
import com.urpSoft.core.util.CSRFToken;

import educationalAdministration.dictionary.entity.CodeKcb;
import educationalAdministration.dictionary.entity.EaApplyType;
import educationalAdministration.dictionary.entity.SysSqfjb;
import educationalAdministration.dictionary.entity.SysYwhdkzb;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.individualApplication.applyCommon.service.ApplyCommonService;
import educationalAdministration.student.individualApplication.creditCertification.entity.CodeXfrzlxb;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzJlxmb;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzRdxsxnkccj;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzXscjd;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzXskccj;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzXssqb;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzYxrdkzlb;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzYxrdkzlbPk;
import educationalAdministration.student.individualApplication.creditCertification.service.CreditCertificationService;

/**
 * 学分认证
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class CreditCertificationController {

	@Resource
	private IPageService pageService;

	private CSRFToken csrfToken = CSRFToken.getInstance();

	@Resource
	private CreditCertificationService creditCertificationService;

	@Resource
	private ApplyCommonService applyCommonService;

	@Resource
	private CommonExportService commonExportService;

	/**
	 * 学分认证 首页
	 * @return
	 */
	@RequestMapping(value={"/student/personalManagement/individualApplication/creditCertification/index"}, method=RequestMethod.GET)
	public String gotoIndex(Model model, HttpServletRequest request,@RequestParam("ywid") String ywid) {
		String applyName = "";
		String flag = "";
		SysYwhdkzb ywsqkzb = applyCommonService.querySysYwhdkzbById(ywid);
		if (ywsqkzb == null) {
			flag = "nonparametric";
		} else {
			applyName = ywsqkzb.getYwsm();
			if (ywsqkzb.getQyf().equals("0")) {
				flag = "notenabled";
			} else {
				int timeCount = applyCommonService.queryTimeFrame(ywid);
				if (timeCount == 0) {
					flag = "nottime";
				} else {
					CodeXfrzlxb codeXfrzlxb = creditCertificationService.queryCodeXfrzlxb(ywid);
					if(codeXfrzlxb!=null&&StringUtils.isNotBlank(codeXfrzlxb.getQzydlb())){
						String sql = "select count(1) from xs_xjydb where yddm in ('"+codeXfrzlxb.getQzydlb().replace(",", "','")+"') and xh ='"+AuthUtil.getCurrentUser().getIdNumber()+"'";
						long i = creditCertificationService.queryCounBySql(sql);
						if(i==0){
							flag = "notcondition";
						}else{
							flag = "showAdd";
						}
					}else{
						flag = "showAdd";
					}
					model.addAttribute("codeXfrzlxb", codeXfrzlxb);

					String schoolCode = CommonUtils.queryParamValue();
					model.addAttribute("schoolCode", schoolCode);
					if("100027".equals(schoolCode)&&"1".equals(codeXfrzlxb.getXzjlxm())){
						String xh = AuthUtil.getCurrentUser().getIdNumber();
						List<XfrzJlxmb> jlxmbList = creditCertificationService.queryXfrzJlxmb(xh, ywid);
						if(jlxmbList==null||jlxmbList.size()==0){
							flag = "notList";
						}
					}

				}
			}
		}
		model.addAttribute("flag", flag);
		model.addAttribute("ywid", ywid);
		model.addAttribute("applyName", applyName);
		boolean mobile = false;
		if (CommonUtils.checkMobile(request)) {
			mobile = true;
		}
		model.addAttribute("mobile", mobile);
		return "student/personalManagement/individualApplication/creditCertification/index";
	}

	/**
	 * 分页查询
	 *
	 * @param model
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/getPage")
	@ResponseBody
	public UrpResult getPage(Model model,@RequestParam(defaultValue = "1") int pageNum,@RequestParam(defaultValue = "10") int pageSize,@RequestParam("ywid") String ywid) {
		CodeXfrzlxb codeXfrzlxb = creditCertificationService.queryCodeXfrzlxb(ywid);
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String sql = "select i.*,rownum rn from (select a.apply_id,a.apply_type,(select c.apply_name from ea_apply_type c where a.apply_type = c.apply_type) as apply_name,a.user_code,a.commit_dt," +
				"a.rollback_dt,a.apply_status,a.ea_rslt,a.note,b.sqbh,b.xh,to_char(to_date(b.czsj,'yyyymmddhh24miss'),'yyyy-mm-dd hh24:mi:ss') sqsj,b.czip,b.kzlbdm,pn.kzlb(b.kzlbdm) kzlbmc,c.filename," +
				"c.fileurl from ea_applys a,XFRZ_XSSQB b,XFRZ_XSCJD c where a.apply_id = b.sqbh and a.user_code = b.xh and b.cjdid=c.cjdid ";
		if(codeXfrzlxb!=null&&"G".equals(codeXfrzlxb.getKcrdfs())){
			sql+="and a.apply_type in (select t.apply_type from XFRZ_YXRDKZLB t where t.xfrzlxm='"+codeXfrzlxb.getXfrzlxm()+"') ";
		}else{
			sql+="and a.apply_type='"+ywid+"' ";
		}
		sql+=" and b.xh = '" + xh + "') i";

		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page<Object> page = pageService.queryPageBySql(info);
		return UrpResult.ok(page);
	}

	/**
	 * 新增/修改
	 * @param model
	 * @param type
	 * @param sqbh
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/editInfo", method = RequestMethod.GET)
	public String editInfo(Model model, HttpServletRequest request,@RequestParam("ywid") String ywid,@RequestParam("type") String type,String sqbh) {
		boolean mobile = false;
		if (CommonUtils.checkMobile(request)) {
			mobile = true;
		}
		model.addAttribute("mobile", mobile);
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		model.addAttribute("ywid", ywid);
		CodeXfrzlxb codeXfrzlxb = creditCertificationService.queryCodeXfrzlxb(ywid);
		if("G".equals(codeXfrzlxb.getKcrdfs())){
			List<Object[]> kzlbbList = creditCertificationService.queryKzlbb(codeXfrzlxb.getXfrzlxm(),xh);
			model.addAttribute("kzlbbList", kzlbbList);

			List<Object[]> kzxxbList = creditCertificationService.queryKzxxb(codeXfrzlxb.getXfrzlxm(),xh);
			model.addAttribute("kzxxbList", kzxxbList);
		}else{
			if(applyCommonService.queryEaProcessCount(ywid)==0){
				model.addAttribute("msg", "当前申请无审批流程，请联系管理员！");
				model.addAttribute("flag", "showAdd");
				return "student/personalManagement/individualApplication/creditCertification/index";
			}

		}

		model.addAttribute("codeXfrzlxb", codeXfrzlxb);

		SysYwhdkzb ywsqkzb = applyCommonService.querySysYwhdkzbById(ywid);
		model.addAttribute("ywsqkzb", ywsqkzb);
		XfrzXssqb xssqb =new XfrzXssqb();
		XfrzXscjd xscjd =new XfrzXscjd();
		if("edit".equals(type)){
			xssqb = creditCertificationService.queryEntityById(XfrzXssqb.class, sqbh);
			xscjd = creditCertificationService.queryEntityById(XfrzXscjd.class, xssqb.getCjdid());
		}else{
			sqbh = "";
		}

		String schoolCode = CommonUtils.queryParamValue();
		model.addAttribute("schoolCode", schoolCode);

		model.addAttribute("xssqb", xssqb);
		model.addAttribute("xscjd", xscjd);

		model.addAttribute("type", type);
		model.addAttribute("sqbh", sqbh);

		if("100027".equals(schoolCode)&&"1".equals(codeXfrzlxb.getXzjlxm())){
			List<XfrzJlxmb> jlxmbList = creditCertificationService.queryXfrzJlxmb(xh, ywid);
			model.addAttribute("jlxmbList", jlxmbList);
			if("edit".equals(type)){
				SysSqfjb fjb = applyCommonService.querySysSqfjbBySqbh(sqbh);
				model.addAttribute("fjb", fjb);
			}
		}

		return "student/personalManagement/individualApplication/creditCertification/edit";
	}

	/**
	 * 根据申请编号查询课程信息
	 * @param model
	 * @param sqbh
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/queryCourses", method = RequestMethod.POST)
	@ResponseBody
	public UrpResult queryCourses(Model model, @RequestParam("sqbh") String sqbh) {
		Map<String, Object> map = new HashMap<String, Object>();
		XfrzXssqb xssqb = creditCertificationService.queryEntityById(XfrzXssqb.class, sqbh);
		CodeXfrzlxb codeXfrzlxb = creditCertificationService.queryEntityById(CodeXfrzlxb.class, xssqb.getXfrzlxm());
		List<XfrzRdxsxnkccj> xnkcList = creditCertificationService.queryXfrzRdxsxnkccj(sqbh);
		List<XfrzXskccj> xwkcList = creditCertificationService.queryXfrzXskccj(xssqb.getCjdid());
		HashMap<String, XfrzXskccj> xwkcMap=new HashMap<String, XfrzXskccj>();
		if("1".equals(codeXfrzlxb.getKcrdfs())||"G".equals(codeXfrzlxb.getKcrdfs())){
			for (XfrzXskccj xfrzXskccj : xwkcList) {
				xwkcMap.put(xfrzXskccj.getWbkccjid(), xfrzXskccj);
			}
		}
		for (XfrzRdxsxnkccj xfrzRdxsxnkccj : xnkcList) {
			if("1".equals(codeXfrzlxb.getKcrdfs())||"G".equals(codeXfrzlxb.getKcrdfs())){
				xfrzRdxsxnkccj.setXskccj(xwkcMap.get(xfrzRdxsxnkccj.getWbkccjid()));
			}
			if(StringUtils.isNotBlank(xfrzRdxsxnkccj.getZxjxjhh())){
				xfrzRdxsxnkccj.setZxjxjhm(CommonUtils.getZxjxjhmByzxjxjhh(xfrzRdxsxnkccj.getZxjxjhh()));
			}
			if(StringUtils.isNotBlank(xfrzRdxsxnkccj.getKch())){
				CodeKcb kcb = creditCertificationService.queryEntityById(CodeKcb.class, xfrzRdxsxnkccj.getKch());
				xfrzRdxsxnkccj.setKcm(kcb.getKcm());
				xfrzRdxsxnkccj.setXf(kcb.getXf().toString());
				xfrzRdxsxnkccj.setXs(kcb.getXs().toString());
				xfrzRdxsxnkccj.setKcsxmc(creditCertificationService.queryKcsxmc(xssqb.getXh(), xfrzRdxsxnkccj.getKch()));
			}
			if(StringUtils.isBlank(xfrzRdxsxnkccj.getCjlrfsdm())){
				xfrzRdxsxnkccj.setCjlrfsdm(codeXfrzlxb.getCjlrfsdm());
			}
			if(StringUtils.isNotBlank(xfrzRdxsxnkccj.getXghkccj())){
				xfrzRdxsxnkccj.setKccj(xfrzRdxsxnkccj.getXghkccj());
			}
		}
		map.put("xnkcList",xnkcList);
		map.put("xwkcList",xwkcList);
		return UrpResult.ok(map);
	}

	/**
	 * 根据交流学校从课程库选择课程
	 * @param model
	 * @param xfrzlxm
	 * @param kchs
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/selectCourse", method = RequestMethod.GET)
	public String selectCourse(Model model,@RequestParam("jlxxdm") String jlxxdm) {
		model.addAttribute("jlxxdm", jlxxdm);
		return "student/personalManagement/individualApplication/creditCertification/selectCourse";
	}

	/**
	 * 查询可选交流课程
	 * @param model
	 * @param pageNum
	 * @param pageSize
	 * @param kchs
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/queryExchangeCourse")
	@ResponseBody
	public UrpResult queryExchangeCourse(Model model,@RequestParam(defaultValue = "1") int pageNum,@RequestParam(defaultValue = "30") int pageSize,@RequestParam("jlxxdm") String jlxxdm) {
		String sql = "select i.*,rownum rn from (select a.kch,a.kcm,a.ywkcm,pkg_com.f_NNF(a.xf) xf,pkg_com.f_NNF(a.xs) xs from code_kcb a where a.jlxxdm = '"+jlxxdm+"' order by a.kch) i";
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page<Object> page = pageService.queryPageBySql(info);
		return UrpResult.ok(page);
	}

	/**
	 * 选择课程页面
	 * @param model
	 * @param xfrzlxm
	 * @param kchs
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/selectKc", method = RequestMethod.GET)
	public String selectKc(Model model,@RequestParam("xfrzlxm") String xfrzlxm,String kchs) {
		String schoolCode = CommonUtils.queryParamValue();
		model.addAttribute("schoolCode", schoolCode);
		CodeXfrzlxb codeXfrzlxb = creditCertificationService.queryEntityById(CodeXfrzlxb.class, xfrzlxm);
		model.addAttribute("codeXfrzlxb", codeXfrzlxb);
		model.addAttribute("kchs", kchs);
		return "student/personalManagement/individualApplication/creditCertification/kc";
	}

	/**
	 * 查询可选课程
	 * @param model
	 * @param pageNum
	 * @param pageSize
	 * @param kchs
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/getCoursesPage")
	@ResponseBody
	public UrpResult getCoursesPage(Model model,@RequestParam(defaultValue = "1") int pageNum,@RequestParam(defaultValue = "300") int pageSize,String kchs) {
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String sql = "select i.*,rownum rn from (select * from (select a.kch,pn.kcm(a.kch) kcm,a.xnxq,pn.xnxqmc(a.xnxq) zxjxjhm,pkg_com.f_NNF(pn.kc_xf(a.kch)) xf," +
				"pkg_com.f_NNF(pn.kc_xs(a.kch)) xs,a.kcsxdm,pn.kcsxmc(a.kcsxdm) kcsxmc,c.xdlxmc,'' as xkkzh,'' as xkkzm from jh_fajhkcb a ,jh_fajhb b,code_xdlxb c " +
				"where a.fajhh = b.fajhh and b.xdlxdm = c.xdlxdm and b.xdlxdm='00001' and a.fajhh in(select fajhh from xs_pyb where xh='"+xh+"') ";
		String schoolCode = CommonUtils.queryParamValue();
		if("100027".equals(schoolCode)){
			String zxjxjhh = CommonUtils.queryNowXnxqOne();
			sql+=" union select t.kch,pn.kcm(t.kch) kcm,'"+zxjxjhh+"' as xnxq,pn.xnxqmc("+zxjxjhh+") zxjxjhm,pkg_com.f_NNF(pn.kc_xf(t.kch)) xf,pkg_com.f_NNF(pn.kc_xs(t.kch)) xs," +
					"'003' kcsxdm,pn.kcsxmc('003') kcsxmc,'' xdlxmc,m.xkkzh,m.xkkzm from chx_xfrd_ktdkcb t,xk_xkkzb m where t.xkkzh=m.xkkzh(+) and t.sqlx='T2' and t.njdm in" +
					"(select njdm from xs_xjb where xh='"+xh+"') and not exists(select 1 from jh_fajhkcb a, jh_fajhb b, code_xdlxb c where t.kch=a.kch and a.fajhh = b.fajhh " +
					"and b.xdlxdm = c.xdlxdm and b.xdlxdm = '00001' and a.fajhh in (select fajhh from xs_pyb where xh = '"+xh+"'))";
		}
		sql+=") s where s.kch not in (select kch from XFRZ_RDXSXNKCCJ where kch is not null and sqbh in (select sqbh from XFRZ_XSSQB where xh = '"+xh+"' and sqzt > -1)) ";
		if(StringUtils.isNotBlank(kchs)){
			sql+=" and s.kch not in ('"+kchs.replace(",", "','")+"') ";
		}
		sql+="and not exists(select 1 from xs_cj_all t where s.kch=t.kch and t.xh='"+xh+"' and nvl(t.kccj,0)>=60) order by s.xnxq,s.kcsxdm,s.kch,s.xkkzh) i";
		QueryInfo info = new QueryInfo();
		info.setPageNum(pageNum);
		info.setMaxResult(pageSize);
		info.setSql(sql);
		Page<Object> page = pageService.queryPageBySql(info);
		return UrpResult.ok(page);
	}

	/**
	 * 保存
	 * @param model
	 * @param request
	 * @param session
	 * @param file
	 * @param sqzt
	 * @param type
	 * @param xfrzlxm
	 * @param jsh
	 * @param sqbh
	 * @param cjdid
	 * @param jlxmm
	 * @param xwkcArr
	 * @param xnkcArr
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/doSave", method = RequestMethod.POST)
	@ResponseBody
	public UrpResult doSave(Model model, HttpServletRequest request,HttpSession session,@RequestParam(value = "file") MultipartFile file, MultipartFile fj,
			@RequestParam("sqzt") String sqzt,@RequestParam("type") String type,@RequestParam("xfrzlxm") String xfrzlxm,String jsh,String sqbh,String jlxxdm,String ksxnxq,String jsxnxq,
			String kzlbdm,String jlxmm, String xwkcArr, String xnkcArr) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (!csrfToken.isTokenValid(request)) {
			map.put("result", csrfToken.gotoAjaxIndex());
			return UrpResult.ok(map);
		} else {
			map.put("token", session.getAttribute("token_in_session").toString());
			map.put("result", creditCertificationService.saveApply(request, file, fj, type, sqzt, jsh, sqbh, jlxxdm, ksxnxq, jsxnxq, kzlbdm,xfrzlxm, jlxmm, xwkcArr, xnkcArr));
			return UrpResult.ok(map);
		}
	}


	/**
	 * 查询学生当前课组类别对应的课组信息
	 * @param model
	 * @param kzlbdm
	 * @param fajhh
	 * @return
	 */
	@RequestMapping("/student/personalManagement/individualApplication/creditCertification/showPyfa")
	public String showPyfa(Model model, String kzlbdm,String fajhh) {
		model.addAttribute("kzlbdm", kzlbdm);
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		model.addAttribute("fajhh", creditCertificationService.queryFajhh(xh));
		return "student/personalManagement/individualApplication/creditCertification/show";
	}

	/**
	 * 撤回
	 * @param model
	 * @param request
	 * @param session
	 * @param sqbh
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/revokeInfo")
	@ResponseBody
	public UrpResult revokeInfo(Model model,HttpServletRequest request,HttpSession session,String sqbh) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (!csrfToken.isTokenValid(request)) {
			map.put("result", csrfToken.gotoAjaxIndex());
			return UrpResult.ok(map);
		} else {
			map.put("result", creditCertificationService.doRevokeInfo(sqbh));
			map.put("token", session.getAttribute("token_in_session").toString());
			return UrpResult.ok(map);
		}
	}

	/**
	 * 校验是否需要指定审批人
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/checkSpgzlZdspr")
	@ResponseBody
	public UrpResult checkSpgzlZdspr(Model model, String xfrzlxm, String kzlbdm) {
		CodeXfrzlxb codeXfrzlxb = creditCertificationService.queryEntityById(CodeXfrzlxb.class, xfrzlxm);
		String apply_type = codeXfrzlxb.getApplyType();
		String kcrdfs = codeXfrzlxb.getKcrdfs();
		if("G".equals(kcrdfs)){
			XfrzYxrdkzlb yxrdkzlb = creditCertificationService.queryEntityById(XfrzYxrdkzlb.class, new XfrzYxrdkzlbPk(xfrzlxm, kzlbdm));
			apply_type=yxrdkzlb.getApply_type();
		}
		EaApplyType eaApplyType = applyCommonService.queryEntityById(EaApplyType.class, apply_type);
		String csz = eaApplyType.getAssigning();
		return UrpResult.ok(csz);
	}

	/**
	 * 弹出指定审批人页面
	 * @param model
	 * @param apply_type
	 * @param sqbh
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/selectApprover", method = RequestMethod.GET)
	public String selectApprover(Model model,String apply_type, String xfrzlxm) {
		model.addAttribute("apply_type", apply_type);
		model.addAttribute("xfrzlxm", xfrzlxm);
		return "student/personalManagement/individualApplication/creditCertification/selectApprover";
	}

	/**
	 * 查询审批人
	 * @param model
	 * @param request
	 * @param session
	 * @param apply_type
	 * @param xfrzlxm
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/queryApprover")
	@ResponseBody
	public UrpResult queryApprover(Model model,HttpServletRequest request,HttpSession session,String apply_type,String xfrzlxm) {
		Map<String, Object> map = new HashMap<String, Object>();

		List<Object[]> list = applyCommonService.queryEalByApplyType(apply_type);
		Object[] names = list.get(0);
		String czr = AuthUtil.getCurrentUser().getIdNumber();
		String sqbh = applyCommonService.querySqbhByApply();
		String sql = "insert into XFRZ_XSSQB (sqbh, cjdid, xfrzlxm, xh) values ('"+sqbh+"', '"+CommonUtils.querySysGuid()+"', '"+xfrzlxm+"', '"+czr+"')";
		map = applyCommonService.queryApprovers(sql, apply_type, names, sqbh, CommonUtils.queryCurrentXnxq(), "");
		map.put("eal_name", names[0]);
		map.put("eap_code", names[2]);
		map.put("eal_code", names[3]);
		return UrpResult.ok(map);
	}

	/**
	 * 下载附件
	 *
	 * @param model
	 * @param response
	 * @param xfrzlxm
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/creditCertification/downFile")
	public void downFile(Model model,HttpServletResponse response,@RequestParam("xfrzlxm") String xfrzlxm) {
		CodeXfrzlxb codeXfrzlxb = creditCertificationService.queryEntityById(CodeXfrzlxb.class, xfrzlxm);
		if (codeXfrzlxb != null && codeXfrzlxb.getSqmbnr()!=null) {
			commonExportService.exportCommon(codeXfrzlxb.getSqmbmc(), codeXfrzlxb.getSqmbnr(), response);
		}
	}

}