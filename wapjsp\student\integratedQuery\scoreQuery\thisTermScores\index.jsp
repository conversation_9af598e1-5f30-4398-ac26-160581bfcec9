<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>本学期成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 本学期成绩页面样式 */
        .semester-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .semester-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }
        
        .semester-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .score-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .score-pending {
            border-left-color: var(--warning-color);
        }
        
        .score-failed {
            border-left-color: var(--error-color);
        }
        
        .score-excellent {
            border-left-color: var(--success-color);
        }
        
        .score-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .score-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 500;
            min-width: 50px;
            text-align: center;
        }
        
        .score-pass {
            background: var(--success-color);
            color: white;
        }
        
        .score-fail {
            background: var(--error-color);
            color: white;
        }
        
        .score-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .score-excellent {
            background: var(--info-color);
            color: white;
        }
        
        .score-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .score-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .score-detail-label {
            color: var(--text-secondary);
        }
        
        .score-detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .score-breakdown {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
        }
        
        .breakdown-title {
            font-weight: 500;
            margin-bottom: var(--margin-xs);
            color: var(--text-primary);
        }
        
        .breakdown-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .gpa-card {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .gpa-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .gpa-value {
            font-size: var(--font-size-h1);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--margin-sm);
        }
        
        .gpa-rank {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">本学期成绩</div>
            <div class="navbar-action" onclick="refreshScores();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学期信息 -->
        <div class="semester-header">
            <div class="semester-title" id="semesterTitle">2023-2024学年第一学期</div>
            <div class="semester-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalCourses">0</div>
                    <div class="stat-label">总课程</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="passedCourses">0</div>
                    <div class="stat-label">已通过</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalCredits">0</div>
                    <div class="stat-label">总学分</div>
                </div>
            </div>
        </div>
        
        <!-- GPA卡片 -->
        <div class="gpa-card">
            <div class="gpa-title">本学期绩点</div>
            <div class="gpa-value" id="semesterGPA">0.00</div>
            <div class="gpa-rank" id="gpaRank">专业排名: 加载中...</div>
        </div>
        
        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterScores('all')">全部</button>
                <button class="filter-chip" onclick="filterScores('published')">已出分</button>
                <button class="filter-chip" onclick="filterScores('pending')">未出分</button>
                <button class="filter-chip" onclick="filterScores('failed')">不及格</button>
            </div>
        </div>
        
        <!-- 成绩列表 -->
        <div class="container-mobile">
            <div id="scoreList">
                <!-- 成绩项将通过JavaScript动态填充 -->
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-file-text-o"></i>
                <div>暂无成绩记录</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allScores = [];
        let filteredScores = [];
        let currentFilter = 'all';

        $(function() {
            initPage();
            loadScores();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载成绩
        function loadScores() {
            showLoading(true);
            
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/thisTermScores/getScores",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allScores = data.scores || [];
                    updateStatistics(data.statistics);
                    applyFilter();
                    showLoading(false);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                    showLoading(false);
                }
            });
        }

        // 渲染成绩列表
        function renderScores() {
            const container = $('#scoreList');
            container.empty();
            
            if (filteredScores.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            filteredScores.forEach(function(score, index) {
                const scoreHtml = createScoreItem(score, index);
                container.append(scoreHtml);
            });
        }

        // 创建成绩项HTML
        function createScoreItem(score, index) {
            const scoreStatus = getScoreStatus(score);
            const statusClass = getStatusClass(scoreStatus);
            const itemClass = getItemClass(scoreStatus);
            
            let breakdownHtml = '';
            if (score.breakdown && score.breakdown.length > 0) {
                breakdownHtml = `
                    <div class="score-breakdown">
                        <div class="breakdown-title">成绩构成</div>
                        ${score.breakdown.map(item => `
                            <div class="breakdown-item">
                                <span>${item.name}</span>
                                <span>${item.score}</span>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            
            return `
                <div class="score-item ${itemClass}">
                    <div class="score-header">
                        <div class="course-name">${score.courseName}</div>
                        <div class="score-badge ${statusClass}">${getScoreDisplay(score)}</div>
                    </div>
                    <div class="score-details">
                        <div class="score-detail-item">
                            <span class="score-detail-label">课程号:</span>
                            <span class="score-detail-value">${score.courseCode}</span>
                        </div>
                        <div class="score-detail-item">
                            <span class="score-detail-label">学分:</span>
                            <span class="score-detail-value">${score.credits}</span>
                        </div>
                        <div class="score-detail-item">
                            <span class="score-detail-label">课程属性:</span>
                            <span class="score-detail-value">${score.courseType}</span>
                        </div>
                        <div class="score-detail-item">
                            <span class="score-detail-label">绩点:</span>
                            <span class="score-detail-value">${score.gpa || '-'}</span>
                        </div>
                    </div>
                    ${breakdownHtml}
                </div>
            `;
        }

        // 获取成绩状态
        function getScoreStatus(score) {
            if (!score.finalScore && !score.totalScore) {
                return 'pending';
            }
            
            const scoreValue = parseFloat(score.finalScore || score.totalScore);
            if (isNaN(scoreValue)) {
                return 'pending';
            }
            
            if (scoreValue < 60) {
                return 'failed';
            } else if (scoreValue >= 90) {
                return 'excellent';
            } else {
                return 'passed';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'passed': return 'score-pass';
                case 'failed': return 'score-fail';
                case 'pending': return 'score-pending';
                case 'excellent': return 'score-excellent';
                default: return 'score-pass';
            }
        }

        // 获取项目样式类
        function getItemClass(status) {
            switch(status) {
                case 'failed': return 'score-failed';
                case 'pending': return 'score-pending';
                case 'excellent': return 'score-excellent';
                default: return '';
            }
        }

        // 获取成绩显示
        function getScoreDisplay(score) {
            if (score.finalScore) {
                return score.finalScore;
            } else if (score.totalScore) {
                return score.totalScore;
            } else {
                return '未出分';
            }
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'published':
                    filteredScores = allScores.filter(score => 
                        score.finalScore || score.totalScore
                    );
                    break;
                case 'pending':
                    filteredScores = allScores.filter(score => 
                        !score.finalScore && !score.totalScore
                    );
                    break;
                case 'failed':
                    filteredScores = allScores.filter(score => {
                        const scoreValue = parseFloat(score.finalScore || score.totalScore);
                        return !isNaN(scoreValue) && scoreValue < 60;
                    });
                    break;
                default:
                    filteredScores = allScores;
            }
            
            renderScores();
        }

        // 筛选成绩
        function filterScores(filter) {
            currentFilter = filter;
            
            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');
            
            applyFilter();
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;
            
            $('#totalCourses').text(statistics.totalCourses || 0);
            $('#passedCourses').text(statistics.passedCourses || 0);
            $('#totalCredits').text(statistics.totalCredits || 0);
            $('#semesterGPA').text(statistics.semesterGPA || '0.00');
            $('#gpaRank').text(`专业排名: ${statistics.rank || '未知'}`);
            $('#semesterTitle').text(statistics.semesterName || '本学期');
        }

        // 刷新成绩
        function refreshScores() {
            loadScores();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#scoreList, .semester-header, .gpa-card, .filter-section').hide();
            } else {
                $('#loadingState').hide();
                $('#scoreList, .semester-header, .gpa-card, .filter-section').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
