<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>审查结果</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 审查结果页面样式 */
        .review-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .review-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .review-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .tabs-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            background: var(--bg-tertiary);
            padding: 0;
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
            white-space: nowrap;
        }
        
        .tabs-nav {
            display: flex;
            min-width: max-content;
        }
        
        .tab-item {
            padding: var(--padding-md);
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-base);
            white-space: nowrap;
        }
        
        .tab-item.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-primary);
        }
        
        .tab-content {
            padding: var(--padding-md);
            min-height: 200px;
        }
        
        .result-summary {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .summary-title i {
            color: var(--info-color);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-primary);
            border-radius: 6px;
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .summary-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
            flex: 1;
            margin-left: var(--margin-sm);
        }
        
        .criteria-section {
            margin-bottom: var(--margin-md);
        }
        
        .criteria-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .criteria-title i {
            color: var(--success-color);
        }
        
        .criteria-item {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            border-left: 4px solid var(--divider-color);
        }
        
        .criteria-item.pass {
            border-left-color: var(--success-color);
        }
        
        .criteria-item.fail {
            border-left-color: var(--error-color);
        }
        
        .criteria-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .criteria-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .criteria-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
        }
        
        .criteria-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .criteria-status.pass {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .criteria-status.fail {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .criteria-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .criteria-detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .criteria-detail-label {
            font-weight: 500;
        }
        
        .criteria-reason {
            background: var(--bg-primary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
        }
        
        .reason-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .reason-content {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .courses-table {
            background: var(--bg-primary);
            border-radius: 8px;
            overflow: hidden;
            margin-top: var(--margin-md);
        }
        
        .courses-header {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
        }
        
        .course-group {
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-group:last-child {
            border-bottom: none;
        }
        
        .group-header {
            background: var(--bg-secondary);
            padding: var(--padding-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .group-name {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .group-status {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .group-status.pass {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .group-status.fail {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .group-stats {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            margin-top: 4px;
            line-height: 1.3;
        }
        
        .course-item {
            padding: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-sm);
            align-items: center;
        }
        
        .course-item:last-child {
            border-bottom: none;
        }
        
        .course-info {
            font-size: var(--font-size-small);
        }
        
        .course-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .course-details {
            color: var(--text-secondary);
            font-size: var(--font-size-mini);
        }
        
        .course-score {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            text-align: center;
            min-width: 50px;
        }
        
        .score-pass {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .score-fail {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .score-selected {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        @media (max-width: 480px) {
            .criteria-details {
                grid-template-columns: 1fr;
            }
            
            .course-item {
                grid-template-columns: 1fr;
                gap: var(--spacing-xs);
            }
            
            .course-score {
                justify-self: start;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">审查结果</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 审查结果头部 -->
        <div class="review-header">
            <div class="review-title">审查结果</div>
            <div class="review-desc">查看审查结果信息</div>
        </div>
        
        <!-- 选项卡内容 -->
        <div class="tabs-section">
            <div class="tabs-header">
                <div class="tabs-nav" id="tabsNav">
                    <!-- 动态生成选项卡 -->
                </div>
            </div>
            
            <div class="tab-content" id="tabContent">
                <!-- 动态加载内容 -->
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无审查结果数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let reviewData = {};
        let currentTab = '';

        $(function() {
            initPage();
            loadReviewResults();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载审查结果
        function loadReviewResults() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/reviewResults/index/queryPageList",
                type: "post",
                data: "sch=",
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.scmcs && data.scmcs.length > 0) {
                        reviewData = data;
                        renderTabs(data.scmcs);
                        renderTabContent(data.scmcs[0], data.scxxs);
                        showEmptyState(false);
                    } else {
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    showEmptyState(true);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染选项卡
        function renderTabs(scmcs) {
            const tabsNav = $('#tabsNav');
            tabsNav.empty();

            scmcs.forEach(function(scmc, index) {
                const tabItem = $(`
                    <button class="tab-item ${index === 0 ? 'active' : ''}" data-tab="${scmc[0]}">
                        ${scmc[1]}
                    </button>
                `);

                tabItem.on('click', function() {
                    const tabId = $(this).data('tab');
                    switchTab(tabId);
                });

                tabsNav.append(tabItem);
            });

            if (scmcs.length > 0) {
                currentTab = scmcs[0][0];
            }
        }

        // 切换选项卡
        function switchTab(tabId) {
            if (currentTab === tabId) return;

            $('.tab-item').removeClass('active');
            $(`.tab-item[data-tab="${tabId}"]`).addClass('active');

            currentTab = tabId;

            const scmc = reviewData.scmcs.find(item => item[0] === tabId);
            if (scmc) {
                renderTabContent(scmc, reviewData.scxxs);
            }
        }

        // 渲染选项卡内容
        function renderTabContent(scmc, scxxs) {
            const tabContent = $('#tabContent');
            tabContent.empty();

            // 审查结果摘要
            const summaryHtml = createSummary(scmc);
            tabContent.append(summaryHtml);

            // 指标审查结果
            const zbList = scxxs[scmc[0] + "zbList"];
            if (zbList && zbList.length > 0) {
                const criteriaHtml = createCriteriaSection(zbList);
                tabContent.append(criteriaHtml);
            }

            // 课程完成情况
            const kcList = scxxs[scmc[0] + "kcList"];
            if (kcList && kcList.length > 0) {
                const coursesHtml = createCoursesSection(kcList);
                tabContent.append(coursesHtml);
            }
        }

        // 创建审查结果摘要
        function createSummary(scmc) {
            return `
                <div class="result-summary">
                    <div class="summary-title">
                        <i class="ace-icon fa fa-info-circle"></i>
                        审查结果
                    </div>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <div class="summary-label">方案</div>
                            <div class="summary-value">${scmc[4] || ''}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">审查结果</div>
                            <div class="summary-value">${scmc[7] || '审查不通过'}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">审查结论内容</div>
                            <div class="summary-value">${scmc[5] || ''}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 创建指标审查结果
        function createCriteriaSection(zbList) {
            let html = `
                <div class="criteria-section">
                    <div class="criteria-title">
                        <i class="ace-icon fa fa-list"></i>
                        指标审查结果
                    </div>
            `;

            zbList.forEach(function(item, index) {
                const isPass = item.zbscsftg === "是";
                html += `
                    <div class="criteria-item ${isPass ? 'pass' : 'fail'}">
                        <div class="criteria-header">
                            <div style="display: flex; align-items: center;">
                                <div class="criteria-index">${index + 1}</div>
                                <div class="criteria-name">${item.sczbmc || ''}</div>
                            </div>
                            <div class="criteria-status ${isPass ? 'pass' : 'fail'}">${item.zbscsftg || ''}</div>
                        </div>

                        <div class="criteria-details">
                            <div class="criteria-detail-item">
                                <span class="criteria-detail-label">原始值</span>
                                <span>${item.zbjgz || ''}</span>
                            </div>
                            <div class="criteria-detail-item">
                                <span class="criteria-detail-label">原始值通过否</span>
                                <span>${item.zbscyssftg || ''}</span>
                            </div>
                            <div class="criteria-detail-item">
                                <span class="criteria-detail-label">审查结论类别</span>
                                <span>${item.scjllbsm || ''}</span>
                            </div>
                            <div class="criteria-detail-item">
                                <span class="criteria-detail-label">审查人</span>
                                <span>${item.scrm || ''}</span>
                            </div>
                            <div class="criteria-detail-item">
                                <span class="criteria-detail-label">审查时间</span>
                                <span>${item.scsj || ''}</span>
                            </div>
                        </div>

                        ${(item.wtgyy || item.bz) ? `
                        <div class="criteria-reason">
                            ${item.wtgyy ? `
                            <div>
                                <div class="reason-title">未通过原因</div>
                                <div class="reason-content">${item.wtgyy}</div>
                            </div>
                            ` : ''}
                            ${item.bz ? `
                            <div style="margin-top: ${item.wtgyy ? '8px' : '0'};">
                                <div class="reason-title">备注</div>
                                <div class="reason-content">${item.bz}</div>
                            </div>
                            ` : ''}
                        </div>
                        ` : ''}
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        // 创建课程完成情况
        function createCoursesSection(kcList) {
            if (!kcList || kcList.length === 0) return '';

            // 按课组分组
            const groupedCourses = {};
            kcList.forEach(function(course) {
                const groupKey = course.kzlbdm + '_' + course.kzh;
                if (!groupedCourses[groupKey]) {
                    groupedCourses[groupKey] = {
                        info: course,
                        courses: []
                    };
                }
                groupedCourses[groupKey].courses.push(course);
            });

            let html = `
                <div class="courses-table">
                    <div class="courses-header">课程完成情况</div>
            `;

            Object.keys(groupedCourses).forEach(function(groupKey) {
                const group = groupedCourses[groupKey];
                const groupInfo = group.info;
                const isGroupPass = groupInfo.sftg === "是";

                html += `
                    <div class="course-group">
                        <div class="group-header">
                            <div>
                                <div class="group-name">${groupInfo.kzlbmc || ''} - ${groupInfo.kzm || ''}</div>
                                <div class="group-stats">
                                    应修学分：${groupInfo.zsxf || ''} | 完成学分：${groupInfo.wcxf || ''}<br>
                                    应修门数：${groupInfo.zsms || ''} | 完成门数：${groupInfo.wcms || ''}<br>
                                    应修学时：${groupInfo.zsxs || ''} | 完成学时：${groupInfo.wcxs || ''}
                                </div>
                            </div>
                            <div class="group-status ${isGroupPass ? 'pass' : 'fail'}">${groupInfo.sftg || ''}</div>
                        </div>
                `;

                // 显示该课组的课程
                group.courses.forEach(function(course) {
                    const scoreClass = getScoreClass(course);
                    html += `
                        <div class="course-item">
                            <div class="course-info">
                                <div class="course-name">${course.kcm || ''}</div>
                                <div class="course-details">
                                    课程号：${course.kch || ''} | 属性：${course.kcsxmc || ''} | 学分：${course.xf || ''} | 学时：${course.xs || ''}
                                </div>
                            </div>
                            <div class="course-score ${scoreClass}">${course.zcj || ''}</div>
                        </div>
                    `;
                });

                html += '</div>';
            });

            html += '</div>';
            return html;
        }

        // 获取成绩样式类
        function getScoreClass(course) {
            if (course.zcj === "选课记录") {
                return 'score-selected';
            } else if (course.zscj && course.zscj < 60) {
                return 'score-fail';
            } else {
                return 'score-pass';
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.tabs-section').hide();
            } else {
                $('#emptyState').hide();
                $('.tabs-section').show();
            }
        }

        // 刷新数据
        function refreshData() {
            loadReviewResults();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
