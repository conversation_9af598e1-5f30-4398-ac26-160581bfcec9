<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教师课表查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教师课表查询页面样式 */
        .schedule-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .search-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .search-row {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .search-item {
            flex: 1;
        }
        
        .search-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .search-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-search {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-reset {
            background: var(--text-disabled);
            color: white;
        }
        
        .teacher-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .teacher-info.show {
            display: block;
        }
        
        .info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .info-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .teacher-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .teacher-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-h3);
            margin-right: var(--margin-md);
        }
        
        .teacher-details {
            flex: 1;
        }
        
        .teacher-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .teacher-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .schedule-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .schedule-container.show {
            display: block;
        }
        
        .schedule-header-bar {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .schedule-title {
            display: flex;
            align-items: center;
        }
        
        .schedule-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .week-selector {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .week-nav {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .week-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            min-width: 120px;
            text-align: center;
        }
        
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .schedule-table th,
        .schedule-table td {
            border: 1px solid var(--divider-color);
            padding: 8px 4px;
            text-align: center;
            font-size: var(--font-size-small);
            vertical-align: top;
        }
        
        .schedule-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .time-header {
            width: 60px;
            min-width: 60px;
        }
        
        .day-header {
            width: calc((100% - 60px) / 7);
        }
        
        .time-cell {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-secondary);
            writing-mode: vertical-lr;
            text-orientation: mixed;
        }
        
        .course-cell {
            height: 60px;
            position: relative;
            cursor: pointer;
        }
        
        .course-item {
            background: var(--primary-light);
            color: var(--primary-color);
            border-radius: 4px;
            padding: 4px;
            margin: 2px;
            font-size: var(--font-size-mini);
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .course-item.conflict {
            background: var(--error-light);
            color: var(--error-color);
        }
        
        .course-item.lab {
            background: var(--warning-light);
            color: var(--warning-color);
        }
        
        .course-item.exam {
            background: var(--info-light);
            color: var(--info-color);
        }
        
        .no-schedule {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .no-schedule i {
            font-size: var(--font-size-h1);
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .course-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .course-detail-modal.show {
            display: flex;
        }
        
        .course-detail-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .course-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .course-detail-close {
            color: var(--text-secondary);
            cursor: pointer;
            font-size: var(--font-size-h4);
        }
        
        .course-detail-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .detail-item {
            margin-bottom: var(--margin-sm);
            display: flex;
            justify-content: space-between;
        }
        
        .detail-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教师课表查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="schedule-header">
            <div class="header-title">教师课表查询</div>
            <div class="header-subtitle">查询任课教师的课程安排</div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                <span>搜索教师</span>
            </div>

            <div class="search-form">
                <div class="search-row">
                    <div class="search-item">
                        <div class="search-label">教师姓名</div>
                        <input type="text" class="search-input" id="teacherName" placeholder="请输入教师姓名">
                    </div>
                    <div class="search-item">
                        <div class="search-label">工号</div>
                        <input type="text" class="search-input" id="teacherId" placeholder="请输入教师工号">
                    </div>
                </div>

                <div class="search-row">
                    <div class="search-item">
                        <div class="search-label">学院</div>
                        <select class="search-input" id="college">
                            <option value="">请选择学院</option>
                        </select>
                    </div>
                    <div class="search-item">
                        <div class="search-label">学期</div>
                        <select class="search-input" id="semester">
                            <option value="">请选择学期</option>
                        </select>
                    </div>
                </div>

                <div class="search-actions">
                    <button class="btn-mobile btn-search flex-1" onclick="searchTeacher();">
                        <i class="ace-icon fa fa-search"></i>
                        <span>搜索</span>
                    </button>
                    <button class="btn-mobile btn-reset flex-1" onclick="resetSearch();">
                        <i class="ace-icon fa fa-refresh"></i>
                        <span>重置</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 教师信息 -->
        <div class="teacher-info" id="teacherInfo">
            <div class="info-title">
                <i class="ace-icon fa fa-user"></i>
                <span>教师信息</span>
            </div>

            <div class="teacher-card" id="teacherCard">
                <!-- 教师信息将动态填充 -->
            </div>
        </div>

        <!-- 课表容器 -->
        <div class="schedule-container" id="scheduleContainer">
            <div class="schedule-header-bar">
                <div class="schedule-title">
                    <i class="ace-icon fa fa-calendar"></i>
                    <span>课程表</span>
                </div>

                <div class="week-selector">
                    <button class="week-nav" onclick="changeWeek(-1);">
                        <i class="ace-icon fa fa-chevron-left"></i>
                    </button>
                    <div class="week-info" id="weekInfo">第1周</div>
                    <button class="week-nav" onclick="changeWeek(1);">
                        <i class="ace-icon fa fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="schedule-content" id="scheduleContent">
                <!-- 课表内容将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-search"></i>
            <div id="emptyMessage">请输入搜索条件查询教师课表</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 课程详情模态框 -->
    <div class="course-detail-modal" id="courseDetailModal">
        <div class="course-detail-content">
            <div class="course-detail-header">
                <div class="course-detail-title" id="courseDetailTitle">课程详情</div>
                <div class="course-detail-close" onclick="closeCourseDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="course-detail-body" id="courseDetailBody">
                <!-- 课程详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTeacher = null;
        let currentWeek = 1;
        let currentSemester = '';
        let scheduleData = {};
        let colleges = [];
        let semesters = [];

        $(function() {
            initPage();
            loadColleges();
            loadSemesters();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            setCurrentWeek();
        }

        // 设置当前周
        function setCurrentWeek() {
            const now = new Date();
            const startOfYear = new Date(now.getFullYear(), 0, 1);
            const dayOfYear = Math.floor((now - startOfYear) / (24 * 60 * 60 * 1000)) + 1;
            currentWeek = Math.ceil(dayOfYear / 7);
            updateWeekInfo();
        }

        // 更新周信息
        function updateWeekInfo() {
            $('#weekInfo').text(`第${currentWeek}周`);
        }

        // 加载学院列表
        function loadColleges() {
            $.ajax({
                url: "/student/query/teacherSchedule/getColleges",
                type: "post",
                dataType: "json",
                success: function(data) {
                    colleges = data.colleges || [];
                    renderCollegeOptions();
                },
                error: function() {
                    console.log('加载学院列表失败');
                }
            });
        }

        // 渲染学院选项
        function renderCollegeOptions() {
            const select = $('#college');
            select.find('option:not(:first)').remove();

            colleges.forEach(college => {
                select.append(`<option value="${college.id}">${college.name}</option>`);
            });
        }

        // 加载学期列表
        function loadSemesters() {
            $.ajax({
                url: "/student/query/teacherSchedule/getSemesters",
                type: "post",
                dataType: "json",
                success: function(data) {
                    semesters = data.semesters || [];
                    renderSemesterOptions();
                    setCurrentSemester();
                },
                error: function() {
                    console.log('加载学期列表失败');
                }
            });
        }

        // 渲染学期选项
        function renderSemesterOptions() {
            const select = $('#semester');
            select.find('option:not(:first)').remove();

            semesters.forEach(semester => {
                select.append(`<option value="${semester.id}">${semester.name}</option>`);
            });
        }

        // 设置当前学期
        function setCurrentSemester() {
            if (semesters.length > 0) {
                const currentSem = semesters.find(s => s.isCurrent) || semesters[0];
                $('#semester').val(currentSem.id);
                currentSemester = currentSem.id;
            }
        }

        // 搜索教师
        function searchTeacher() {
            const teacherName = $('#teacherName').val().trim();
            const teacherId = $('#teacherId').val().trim();
            const college = $('#college').val();
            const semester = $('#semester').val();

            if (!teacherName && !teacherId) {
                showError('请输入教师姓名或工号');
                return;
            }

            if (!semester) {
                showError('请选择学期');
                return;
            }

            currentSemester = semester;

            showLoading(true);

            $.ajax({
                url: "/student/query/teacherSchedule/searchTeacher",
                type: "post",
                data: {
                    teacherName: teacherName,
                    teacherId: teacherId,
                    college: college,
                    semester: semester
                },
                dataType: "json",
                success: function(data) {
                    if (data.success && data.teacher) {
                        currentTeacher = data.teacher;
                        showTeacherInfo(data.teacher);
                        loadTeacherSchedule(data.teacher.id, semester);
                    } else {
                        showError(data.message || '未找到匹配的教师');
                        hideTeacherInfo();
                        hideSchedule();
                    }
                    showLoading(false);
                },
                error: function() {
                    showError('搜索失败，请重试');
                    showLoading(false);
                }
            });
        }

        // 显示教师信息
        function showTeacherInfo(teacher) {
            const teacherHtml = `
                <div class="teacher-avatar">
                    ${teacher.name.charAt(0)}
                </div>
                <div class="teacher-details">
                    <div class="teacher-name">${teacher.name}</div>
                    <div class="teacher-meta">
                        工号：${teacher.id}<br>
                        学院：${teacher.collegeName}<br>
                        职称：${teacher.title || '未知'}<br>
                        联系方式：${teacher.phone || '未公开'}
                    </div>
                </div>
            `;

            $('#teacherCard').html(teacherHtml);
            $('#teacherInfo').addClass('show');
            hideEmptyState();
        }

        // 隐藏教师信息
        function hideTeacherInfo() {
            $('#teacherInfo').removeClass('show');
        }

        // 加载教师课表
        function loadTeacherSchedule(teacherId, semester) {
            $.ajax({
                url: "/student/query/teacherSchedule/getTeacherSchedule",
                type: "post",
                data: {
                    teacherId: teacherId,
                    semester: semester,
                    week: currentWeek
                },
                dataType: "json",
                success: function(data) {
                    scheduleData = data.schedule || {};
                    renderSchedule();
                    showSchedule();
                },
                error: function() {
                    showError('加载课表失败');
                    hideSchedule();
                }
            });
        }

        // 渲染课表
        function renderSchedule() {
            if (!scheduleData || Object.keys(scheduleData).length === 0) {
                $('#scheduleContent').html(`
                    <div class="no-schedule">
                        <i class="ace-icon fa fa-calendar-o"></i>
                        <div>该教师本周暂无课程安排</div>
                    </div>
                `);
                return;
            }

            const timeSlots = [
                '第1节\n08:00-08:45',
                '第2节\n08:55-09:40',
                '第3节\n10:00-10:45',
                '第4节\n10:55-11:40',
                '第5节\n14:00-14:45',
                '第6节\n14:55-15:40',
                '第7节\n16:00-16:45',
                '第8节\n16:55-17:40',
                '第9节\n19:00-19:45',
                '第10节\n19:55-20:40'
            ];

            const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

            let tableHtml = '<table class="schedule-table">';

            // 表头
            tableHtml += '<tr>';
            tableHtml += '<th class="time-header">时间</th>';
            weekDays.forEach(day => {
                tableHtml += `<th class="day-header">${day}</th>`;
            });
            tableHtml += '</tr>';

            // 表格内容
            timeSlots.forEach((timeSlot, timeIndex) => {
                tableHtml += '<tr>';
                tableHtml += `<td class="time-cell">${timeSlot}</td>`;

                for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
                    const courses = getCoursesByTime(dayIndex + 1, timeIndex + 1);
                    tableHtml += `<td class="course-cell" onclick="showCourseDetail(${dayIndex + 1}, ${timeIndex + 1})">`;

                    courses.forEach(course => {
                        const courseClass = getCourseClass(course.type);
                        tableHtml += `<div class="course-item ${courseClass}" title="${course.name}">${course.name}</div>`;
                    });

                    tableHtml += '</td>';
                }

                tableHtml += '</tr>';
            });

            tableHtml += '</table>';
            $('#scheduleContent').html(tableHtml);
        }

        // 根据时间获取课程
        function getCoursesByTime(dayOfWeek, timeSlot) {
            const key = `${dayOfWeek}-${timeSlot}`;
            return scheduleData[key] || [];
        }

        // 获取课程样式类
        function getCourseClass(type) {
            switch(type) {
                case 'lab': return 'lab';
                case 'exam': return 'exam';
                case 'conflict': return 'conflict';
                default: return '';
            }
        }

        // 显示课程详情
        function showCourseDetail(dayOfWeek, timeSlot) {
            const courses = getCoursesByTime(dayOfWeek, timeSlot);

            if (courses.length === 0) {
                return;
            }

            let detailHtml = '';

            courses.forEach((course, index) => {
                if (index > 0) {
                    detailHtml += '<hr style="margin: 12px 0; border: none; border-top: 1px solid var(--divider-color);">';
                }

                detailHtml += `
                    <div class="detail-item">
                        <span class="detail-label">课程名称:</span>
                        <span class="detail-value">${course.name}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">课程代码:</span>
                        <span class="detail-value">${course.code}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">上课地点:</span>
                        <span class="detail-value">${course.location}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">上课班级:</span>
                        <span class="detail-value">${course.className}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">学分:</span>
                        <span class="detail-value">${course.credits}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">课程类型:</span>
                        <span class="detail-value">${getCourseTypeText(course.type)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">上课时间:</span>
                        <span class="detail-value">${course.timeRange}</span>
                    </div>
                `;

                if (course.note) {
                    detailHtml += `
                        <div class="detail-item">
                            <span class="detail-label">备注:</span>
                            <span class="detail-value">${course.note}</span>
                        </div>
                    `;
                }
            });

            $('#courseDetailTitle').text(courses.length > 1 ? '课程详情 (多门课程)' : '课程详情');
            $('#courseDetailBody').html(detailHtml);
            $('#courseDetailModal').addClass('show');
        }

        // 获取课程类型文本
        function getCourseTypeText(type) {
            switch(type) {
                case 'theory': return '理论课';
                case 'lab': return '实验课';
                case 'practice': return '实践课';
                case 'exam': return '考试';
                default: return '普通课程';
            }
        }

        // 关闭课程详情
        function closeCourseDetail() {
            $('#courseDetailModal').removeClass('show');
        }

        // 切换周
        function changeWeek(direction) {
            currentWeek += direction;
            if (currentWeek < 1) currentWeek = 1;
            if (currentWeek > 20) currentWeek = 20;

            updateWeekInfo();

            if (currentTeacher && currentSemester) {
                loadTeacherSchedule(currentTeacher.id, currentSemester);
            }
        }

        // 重置搜索
        function resetSearch() {
            $('#teacherName').val('');
            $('#teacherId').val('');
            $('#college').val('');
            setCurrentSemester();

            currentTeacher = null;
            scheduleData = {};

            hideTeacherInfo();
            hideSchedule();
            showEmptyState('请输入搜索条件查询教师课表');
        }

        // 显示课表
        function showSchedule() {
            $('#scheduleContainer').addClass('show');
        }

        // 隐藏课表
        function hideSchedule() {
            $('#scheduleContainer').removeClass('show');
        }

        // 刷新数据
        function refreshData() {
            if (currentTeacher && currentSemester) {
                loadTeacherSchedule(currentTeacher.id, currentSemester);
            }
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            hideTeacherInfo();
            hideSchedule();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#courseDetailModal').click(function(e) {
            if (e.target === this) {
                closeCourseDetail();
            }
        });

        // 初始显示空状态
        showEmptyState('请输入搜索条件查询教师课表');
    </script>
</body>
</html>
