<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实验管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实验管理页面样式 */
        .experiment-header {
            background: linear-gradient(135deg, var(--success-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .experiment-icon {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            opacity: 0.9;
        }
        
        .experiment-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .experiment-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .function-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .function-card:hover::before {
            left: 100%;
        }
        
        .function-card:hover {
            border-color: var(--success-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .function-card:active {
            transform: translateY(-2px);
        }
        
        .function-icon {
            font-size: 40px;
            margin-bottom: var(--margin-md);
            color: var(--success-color);
            position: relative;
            z-index: 1;
        }
        
        .function-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .function-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
            position: relative;
            z-index: 1;
        }
        
        .function-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: var(--warning-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: var(--font-size-mini);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }
        
        .experiment-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--info-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .recent-activities {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .activities-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .activities-title i {
            color: var(--warning-color);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .activity-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .activity-item:last-child {
            margin-bottom: 0;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: var(--success-light);
            color: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-right: var(--margin-sm);
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .activity-desc {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .activity-arrow {
            color: var(--text-disabled);
            font-size: 14px;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-actions-title i {
            color: var(--error-color);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-item {
            text-align: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .action-item:hover {
            background: var(--bg-tertiary);
            transform: translateY(-2px);
        }
        
        .action-item-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--info-light);
            color: var(--info-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto var(--margin-sm);
        }
        
        .action-item-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        @media (max-width: 480px) {
            .function-grid {
                grid-template-columns: 1fr;
            }
            
            .action-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实验管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 实验管理头部 -->
        <div class="experiment-header">
            <div class="experiment-icon">
                <i class="ace-icon fa fa-flask"></i>
            </div>
            <div class="experiment-title">实验管理中心</div>
            <div class="experiment-desc">实验项目选择、预约、报告管理</div>
        </div>
        
        <!-- 实验统计 -->
        <div class="experiment-stats">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                实验统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalExperiments">0</div>
                    <div class="stat-label">总实验数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedExperiments">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="pendingExperiments">0</div>
                    <div class="stat-label">待完成</div>
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="quick-actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                快速操作
            </div>
            <div class="action-grid">
                <div class="action-item" onclick="goToFunction('choseProj');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-check-square"></i>
                    </div>
                    <div class="action-item-title">项目选择</div>
                </div>
                <div class="action-item" onclick="goToFunction('subscribe');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-calendar-plus-o"></i>
                    </div>
                    <div class="action-item-title">实验预约</div>
                </div>
                <div class="action-item" onclick="goToFunction('courseTableQuery');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-table"></i>
                    </div>
                    <div class="action-item-title">课表查询</div>
                </div>
                <div class="action-item" onclick="goToFunction('safetyExamination');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-shield"></i>
                    </div>
                    <div class="action-item-title">安全考试</div>
                </div>
            </div>
        </div>
        
        <!-- 功能模块 -->
        <div class="function-grid">
            <div class="function-card" onclick="goToFunction('choseProj');">
                <div class="function-badge">
                    <i class="ace-icon fa fa-star"></i>
                </div>
                <div class="function-icon">
                    <i class="ace-icon fa fa-check-square-o"></i>
                </div>
                <div class="function-title">实验项目选择</div>
                <div class="function-desc">选择实验项目和课程</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('subscribe');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-calendar-plus-o"></i>
                </div>
                <div class="function-title">实验预约</div>
                <div class="function-desc">预约实验时间和地点</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('courseTableQuery');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-table"></i>
                </div>
                <div class="function-title">实验课表查询</div>
                <div class="function-desc">查看实验课程安排</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('largeDeviceYy');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-cogs"></i>
                </div>
                <div class="function-title">大型设备预约</div>
                <div class="function-desc">预约大型实验设备</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('safetyExamination');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-shield"></i>
                </div>
                <div class="function-title">安全考试</div>
                <div class="function-desc">实验室安全考试</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('dxyqsbgxgl');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-file-text-o"></i>
                </div>
                <div class="function-title">实验报告管理</div>
                <div class="function-desc">提交和管理实验报告</div>
            </div>
        </div>
        
        <!-- 最近活动 -->
        <div class="recent-activities" id="recentActivities" style="display: none;">
            <div class="activities-title">
                <i class="ace-icon fa fa-clock-o"></i>
                最近活动
            </div>
            <div id="activitiesList">
                <!-- 动态加载活动内容 -->
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let experimentStats = {};

        $(function() {
            initPage();
            loadExperimentStats();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载实验统计
        function loadExperimentStats() {
            showLoading(true);

            // 获取实验统计信息
            $.ajax({
                url: "/student/experiment/getStats",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        experimentStats = data.data;
                        updateStats();
                        loadRecentActivities();
                    } else {
                        // 设置默认统计
                        updateStats({
                            totalExperiments: 0,
                            completedExperiments: 0,
                            pendingExperiments: 0
                        });
                    }
                },
                error: function() {
                    console.log('获取实验统计失败');
                    // 设置默认统计
                    updateStats({
                        totalExperiments: 0,
                        completedExperiments: 0,
                        pendingExperiments: 0
                    });
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新统计信息
        function updateStats(stats = experimentStats) {
            $('#totalExperiments').text(stats.totalExperiments || 0);
            $('#completedExperiments').text(stats.completedExperiments || 0);
            $('#pendingExperiments').text(stats.pendingExperiments || 0);
        }

        // 加载最近活动
        function loadRecentActivities() {
            $.ajax({
                url: "/student/experiment/getRecentActivities",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success && data.data && data.data.length > 0) {
                        renderRecentActivities(data.data);
                        $('#recentActivities').show();
                    }
                },
                error: function() {
                    console.log('获取最近活动失败');
                }
            });
        }

        // 渲染最近活动
        function renderRecentActivities(activities) {
            const container = $('#activitiesList');
            container.empty();

            activities.forEach(function(activity) {
                const activityHtml = `
                    <div class="activity-item" onclick="handleActivity('${activity.id}', '${activity.type}');">
                        <div class="activity-icon">
                            <i class="ace-icon fa fa-${getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.title}</div>
                            <div class="activity-desc">${activity.description}</div>
                        </div>
                        <i class="activity-arrow ace-icon fa fa-chevron-right"></i>
                    </div>
                `;
                container.append(activityHtml);
            });
        }

        // 获取活动图标
        function getActivityIcon(type) {
            switch(type) {
                case 'project': return 'check-square-o';
                case 'subscribe': return 'calendar-plus-o';
                case 'report': return 'file-text-o';
                case 'safety': return 'shield';
                case 'device': return 'cogs';
                default: return 'flask';
            }
        }

        // 处理活动点击
        function handleActivity(activityId, activityType) {
            // 根据活动类型跳转到相应页面
            switch(activityType) {
                case 'project':
                    goToFunction('choseProj');
                    break;
                case 'subscribe':
                    goToFunction('subscribe');
                    break;
                case 'report':
                    goToFunction('dxyqsbgxgl');
                    break;
                case 'safety':
                    goToFunction('safetyExamination');
                    break;
                case 'device':
                    goToFunction('largeDeviceYy');
                    break;
                default:
                    showError('未知活动类型');
            }
        }

        // 跳转到功能页面
        function goToFunction(functionName) {
            let url = '';
            let title = '';

            switch(functionName) {
                case 'choseProj':
                    url = '/student/experiment/choseProj/index';
                    title = '实验项目选择';
                    break;
                case 'subscribe':
                    url = '/student/experiment/subscribe/index';
                    title = '实验预约';
                    break;
                case 'courseTableQuery':
                    url = '/student/experiment/courseTableQuery/index';
                    title = '实验课表查询';
                    break;
                case 'largeDeviceYy':
                    url = '/student/experiment/largeDeviceYy/index';
                    title = '大型设备预约';
                    break;
                case 'safetyExamination':
                    url = '/student/experiment/safetyExamination/index';
                    title = '安全考试';
                    break;
                case 'dxyqsbgxgl':
                    url = '/student/experiment/dxyqsbgxgl/index';
                    title = '实验报告管理';
                    break;
                default:
                    showError('功能暂未开放');
                    return;
            }

            if (parent && parent.addTab) {
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 刷新数据
        function refreshData() {
            loadExperimentStats();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
