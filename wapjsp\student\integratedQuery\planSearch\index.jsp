<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>方案计划查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 方案计划查询页面样式 */
        .plan-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .plan-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .plan-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .plan-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .selector-title i {
            color: var(--primary-color);
        }
        
        .plan-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .plan-item {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-md);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .plan-item:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .plan-item.active {
            border-color: var(--primary-color);
            background: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .plan-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .plan-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .plan-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .plan-arrow {
            color: var(--text-disabled);
            font-size: var(--font-size-base);
        }
        
        .plan-item.active .plan-arrow {
            color: var(--primary-color);
        }
        
        .tab-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tab-header {
            display: flex;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .tab-item {
            flex: 1;
            padding: var(--padding-md);
            text-align: center;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
            border-bottom: 2px solid transparent;
        }
        
        .tab-item.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-primary);
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .plan-detail-card {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .detail-title i {
            color: var(--primary-color);
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .detail-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        .tree-container {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .tree-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
        }
        
        .tree-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .tree-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-tree {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-tree:hover {
            background: var(--primary-dark);
        }
        
        .tree-node {
            padding: var(--padding-sm);
            border-left: 2px solid var(--divider-color);
            margin-left: var(--margin-sm);
            margin-bottom: var(--margin-sm);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .tree-node:hover {
            border-left-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .tree-node.active {
            border-left-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .node-content {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .node-children {
            margin-left: var(--margin-md);
            margin-top: var(--margin-sm);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        @media (max-width: 480px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .tab-item {
                font-size: var(--font-size-mini);
                padding: var(--padding-sm);
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="fajhh_info" name="fajhh_info" value="">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">方案计划查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 方案计划头部 -->
        <div class="plan-header">
            <div class="plan-title">方案计划查询</div>
            <div class="plan-desc">查看培养方案和教学计划详细信息</div>
        </div>
        
        <!-- 方案选择器 -->
        <div class="plan-selector">
            <div class="selector-title">
                <i class="ace-icon fa fa-list"></i>
                选择方案
            </div>
            
            <div class="plan-list" id="planList">
                <!-- 动态加载方案列表 -->
            </div>
        </div>
        
        <!-- 方案详情选项卡 -->
        <div class="tab-container" id="planDetailContainer" style="display: none;">
            <div class="tab-header">
                <div class="tab-item active" onclick="switchTab('info')">方案信息</div>
                <div class="tab-item" onclick="switchTab('structure')">方案结构</div>
            </div>
            
            <div class="tab-content">
                <!-- 方案信息 -->
                <div id="info" class="tab-pane active">
                    <div class="plan-detail-card">
                        <div class="detail-title">
                            <i class="ace-icon fa fa-info-circle"></i>
                            基本信息
                        </div>
                        <div id="planBasicInfo">
                            <!-- 动态加载基本信息 -->
                        </div>
                    </div>
                </div>
                
                <!-- 方案结构 -->
                <div id="structure" class="tab-pane">
                    <div class="tree-container">
                        <div class="tree-header">
                            <div class="tree-title" id="treeTitle">方案结构</div>
                            <div class="tree-actions">
                                <button class="btn-tree" onclick="expandAll()">
                                    <i class="ace-icon fa fa-folder-open-o"></i> 展开
                                </button>
                                <button class="btn-tree" onclick="collapseAll()">
                                    <i class="ace-icon fa fa-folder-o"></i> 收起
                                </button>
                            </div>
                        </div>
                        
                        <div id="planTree">
                            <!-- 动态加载方案树 -->
                        </div>
                    </div>
                    
                    <!-- 详细信息显示区域 -->
                    <div id="detailInfo" style="display: none;">
                        <!-- 课组信息 -->
                        <div id="kz" class="plan-detail-card" style="display: none;">
                            <div class="detail-title">
                                <i class="ace-icon fa fa-folder"></i>
                                课组信息
                            </div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">课组类型</div>
                                    <div class="detail-value" id="kz_kzlx"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">课组名</div>
                                    <div class="detail-value" id="kz_kzm"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">课程总学分</div>
                                    <div class="detail-value" id="kz_kczxf"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">课程总学时</div>
                                    <div class="detail-value" id="kz_kczxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最少门数</div>
                                    <div class="detail-value" id="kz_zsms"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最少学分</div>
                                    <div class="detail-value" id="kz_zsxf"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最少学时</div>
                                    <div class="detail-value" id="kz_zsxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">生成要求</div>
                                    <div class="detail-value" id="kz_scyq"></div>
                                </div>
                            </div>
                            <div class="detail-item" style="margin-top: var(--margin-md);">
                                <div class="detail-label">备注</div>
                                <div class="detail-value" id="kz_bz"></div>
                            </div>
                        </div>
                        
                        <!-- 学年学期信息 -->
                        <div id="xnxq" class="plan-detail-card" style="display: none;">
                            <div class="detail-title">
                                <i class="ace-icon fa fa-calendar"></i>
                                学年学期信息
                            </div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">计划学年</div>
                                    <div class="detail-value" id="xnxq_jhxn"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">学期类型</div>
                                    <div class="detail-value" id="xnxq_xqlx"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">计划学期</div>
                                    <div class="detail-value" id="xnxq_jhxq"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最少修读门数</div>
                                    <div class="detail-value" id="xnxq_zsxdms"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最多修读门数</div>
                                    <div class="detail-value" id="xnxq_zdxdms"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最少修读学分</div>
                                    <div class="detail-value" id="xnxq_zsxdxf"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最多修读学分</div>
                                    <div class="detail-value" id="xnxq_zdxdxf"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最少修读学时</div>
                                    <div class="detail-value" id="xnxq_zsxdxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最多修读学时</div>
                                    <div class="detail-value" id="xnxq_zdxdxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">生成要求</div>
                                    <div class="detail-value" id="xnxq_scyq"></div>
                                </div>
                            </div>
                            <div class="detail-item" style="margin-top: var(--margin-md);">
                                <div class="detail-label">备注</div>
                                <div class="detail-value" id="xnxq_bz"></div>
                            </div>
                        </div>
                        
                        <!-- 课程信息 -->
                        <div id="kc" class="plan-detail-card" style="display: none;">
                            <div class="detail-title">
                                <i class="ace-icon fa fa-book"></i>
                                课程信息
                            </div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">课程号</div>
                                    <div class="detail-value" id="kc_kch"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">课程名</div>
                                    <div class="detail-value" id="kc_kcm"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">英文课程名</div>
                                    <div class="detail-value" id="kc_ywkcm"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">开课院系</div>
                                    <div class="detail-value" id="kc_xsm"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">开课校区</div>
                                    <div class="detail-value" id="kc_kkxq"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">必修标志</div>
                                    <div class="detail-value" id="kc_bybz"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">学分</div>
                                    <div class="detail-value" id="kc_xf"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">学时</div>
                                    <div class="detail-value" id="kc_xs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">开始日期</div>
                                    <div class="detail-value" id="kc_ksrq"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">结束日期</div>
                                    <div class="detail-value" id="kc_jsrq"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">课程状态</div>
                                    <div class="detail-value" id="kc_kcztmc"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">课程类别</div>
                                    <div class="detail-value" id="kc_kclbmc"></div>
                                </div>
                            </div>
                            
                            <!-- 学时分配 -->
                            <div class="detail-title" style="margin-top: var(--margin-lg);">
                                <i class="ace-icon fa fa-clock-o"></i>
                                学时分配
                            </div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">课内总学时</div>
                                    <div class="detail-value" id="kc_knzxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">讲课总学时</div>
                                    <div class="detail-value" id="kc_jkzxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">实践总学时</div>
                                    <div class="detail-value" id="kc_sjzxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">实验总学时</div>
                                    <div class="detail-value" id="kc_syzxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">其中实践总学时</div>
                                    <div class="detail-value" id="kc_qzsjzxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">讨论辅导总学时</div>
                                    <div class="detail-value" id="kc_tlfdzxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">实践作业总学时</div>
                                    <div class="detail-value" id="kc_sjzyzxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">课外总学时</div>
                                    <div class="detail-value" id="kc_kwzxs"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">课外学分</div>
                                    <div class="detail-value" id="kc_kwxf"></div>
                                </div>
                            </div>
                            
                            <!-- 其他信息 -->
                            <div class="detail-title" style="margin-top: var(--margin-lg);">
                                <i class="ace-icon fa fa-info"></i>
                                其他信息
                            </div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">教学方式</div>
                                    <div class="detail-value" id="kc_jxfssm"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">教师</div>
                                    <div class="detail-value" id="kc_jsm"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">教材</div>
                                    <div class="detail-value" id="kc_jc"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">参考书</div>
                                    <div class="detail-value" id="kc_cks"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">所在单位</div>
                                    <div class="detail-value" id="kc_szdw"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">考试类型</div>
                                    <div class="detail-value" id="kc_kslxmc"></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">校区</div>
                                    <div class="detail-value" id="kc_xqm"></div>
                                </div>
                            </div>
                            
                            <div class="detail-item" style="margin-top: var(--margin-md);">
                                <div class="detail-label">课程说明</div>
                                <div class="detail-value" id="kc_kcsm"></div>
                            </div>
                            <div class="detail-item" style="margin-top: var(--margin-md);">
                                <div class="detail-label">内容简介</div>
                                <div class="detail-value" id="kc_nrjj"></div>
                            </div>
                            <div class="detail-item" style="margin-top: var(--margin-md);">
                                <div class="detail-label">备注</div>
                                <div class="detail-value" id="kc_bz"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'info';
        let currentPlan = null;
        let treeData = null;

        $(function() {
            initPage();
            loadPlanList();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载方案列表
        function loadPlanList() {
            // 这里应该从后端获取用户的方案列表
            // 暂时使用模拟数据
            const plans = [
                {
                    fajhh: 'plan001',
                    famc: '计算机科学与技术专业培养方案',
                    jhmc: '2020级本科教学计划',
                    xsm: '计算机学院',
                    zym: '计算机科学与技术'
                }
            ];

            renderPlanList(plans);
        }

        // 渲染方案列表
        function renderPlanList(plans) {
            const container = $('#planList');
            container.empty();

            if (!plans || plans.length === 0) {
                container.html(`
                    <div class="empty-state">
                        <i class="ace-icon fa fa-folder-o"></i>
                        <div class="empty-state-text">暂无方案信息</div>
                        <div class="empty-state-desc">请联系管理员配置培养方案</div>
                    </div>
                `);
                return;
            }

            plans.forEach(function(plan, index) {
                const planHtml = `
                    <div class="plan-item" onclick="selectPlan('${plan.fajhh}', this)">
                        <div class="plan-info">
                            <div class="plan-name">${plan.famc || ''}</div>
                            <div class="plan-details">${plan.jhmc || ''} | ${plan.xsm || ''}</div>
                        </div>
                        <div class="plan-arrow">
                            <i class="ace-icon fa fa-chevron-right"></i>
                        </div>
                    </div>
                `;
                container.append(planHtml);
            });
        }

        // 选择方案
        function selectPlan(fajhh, element) {
            // 更新选中状态
            $('.plan-item').removeClass('active');
            $(element).addClass('active');

            // 保存当前选中的方案
            $('#fajhh_info').val(fajhh);
            currentPlan = fajhh;

            // 显示详情容器
            $('#planDetailContainer').show();

            // 加载方案信息
            loadPlanInfo(fajhh, '0');
        }

        // 加载方案信息
        function loadPlanInfo(fajhh, lx) {
            if (!fajhh) {
                fajhh = $('#fajhh_info').val();
            }

            const url = '/student/rollManagement/minorProgramRegistration/detail';

            showLoading(true);

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: "fajhh=" + fajhh + "&lx=" + lx,
                dataType: "json",
                success: function(d) {
                    renderPlanInfo(d);
                    treeData = d["treeList"];
                    renderPlanTree(treeData);
                },
                error: function(xhr) {
                    showError("获取方案信息失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染方案信息
        function renderPlanInfo(data) {
            const v = data["jhFajhb"];

            if (!v || !v.fajhh) {
                $('#planBasicInfo').html(`
                    <div class="empty-state">
                        <i class="ace-icon fa fa-exclamation-circle"></i>
                        <div class="empty-state-text">没有查询到方案信息</div>
                        <div class="empty-state-desc">请联系管理员</div>
                    </div>
                `);
                return;
            }

            const infoHtml = `
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">方案</div>
                        <div class="detail-value">${v.famc || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">计划</div>
                        <div class="detail-value">${v.jhmc || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">年级</div>
                        <div class="detail-value">${v.njmc || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">院系</div>
                        <div class="detail-value">${v.xsm || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">专业</div>
                        <div class="detail-value">${v.zym || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">专业方向</div>
                        <div class="detail-value">${v.zyfxm || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">学位</div>
                        <div class="detail-value">${v.xwm || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">毕业类型</div>
                        <div class="detail-value">${v.bylxmc || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">学制类型</div>
                        <div class="detail-value">${v.xzlxmc || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">修读类型</div>
                        <div class="detail-value">${v.xdlxmc || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">方案计划类型</div>
                        <div class="detail-value">${v.fajhlx || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">开始学年</div>
                        <div class="detail-value">${v.xnmc || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">学期类型</div>
                        <div class="detail-value">${v.xqlxm || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">开始学期</div>
                        <div class="detail-value">${v.xqm || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">要求总学分</div>
                        <div class="detail-value">${v.yqzxf || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">课程总学分</div>
                        <div class="detail-value">${v.kczxf || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">课程总门数</div>
                        <div class="detail-value">${v.kczms || ''}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">课程总学时</div>
                        <div class="detail-value">${v.kczxs || ''}</div>
                    </div>
                </div>

                ${v.pymb ? `
                    <div class="detail-item" style="margin-top: var(--margin-md);">
                        <div class="detail-label">培养目标</div>
                        <div class="detail-value">${v.pymb}</div>
                    </div>
                ` : ''}

                ${v.xdyq ? `
                    <div class="detail-item" style="margin-top: var(--margin-md);">
                        <div class="detail-label">修读要求</div>
                        <div class="detail-value">${v.xdyq}</div>
                    </div>
                ` : ''}

                ${v.bz ? `
                    <div class="detail-item" style="margin-top: var(--margin-md);">
                        <div class="detail-label">备注</div>
                        <div class="detail-value">${v.bz}</div>
                    </div>
                ` : ''}
            `;

            $('#planBasicInfo').html(infoHtml);
            $('#treeTitle').text(data["title"] || '方案结构');
        }

        // 渲染方案树
        function renderPlanTree(treeData) {
            const container = $('#planTree');
            container.empty();

            if (!treeData || treeData.length === 0) {
                container.html(`
                    <div class="empty-state">
                        <i class="ace-icon fa fa-sitemap"></i>
                        <div class="empty-state-text">暂无方案结构</div>
                        <div class="empty-state-desc">方案结构信息为空</div>
                    </div>
                `);
                return;
            }

            // 构建树形结构
            const treeHtml = buildTreeHtml(treeData);
            container.html(treeHtml);
        }

        // 构建树形HTML
        function buildTreeHtml(nodes, level = 0) {
            let html = '';

            nodes.forEach(function(node) {
                const indent = level * 20;
                const hasChildren = node.children && node.children.length > 0;

                html += `
                    <div class="tree-node" style="margin-left: ${indent}px;" onclick="selectTreeNode('${node.urlPath}', this)">
                        <div class="node-content">
                            ${hasChildren ? '<i class="ace-icon fa fa-folder"></i>' : '<i class="ace-icon fa fa-file-o"></i>'}
                            ${node.name || ''}
                        </div>
                    </div>
                `;

                if (hasChildren) {
                    html += buildTreeHtml(node.children, level + 1);
                }
            });

            return html;
        }

        // 选择树节点
        function selectTreeNode(urlPath, element) {
            if (!urlPath) return;

            // 更新选中状态
            $('.tree-node').removeClass('active');
            $(element).addClass('active');

            // 隐藏所有详情
            $('#kz, #xnxq, #kc').hide();

            // 获取节点详情
            $.get(urlPath, function(data) {
                if (data.flag == '1') {
                    // 课组信息
                    $('#kz_kzlx').text(data.kz.kzlx || '');
                    $('#kz_kzm').text(data.kz.kzm || '');
                    $('#kz_kczxf').text(data.kz.kczxf || '');
                    $('#kz_kczxs').text(data.kz.kczxs || '');
                    $('#kz_zsms').text(data.kz.zsms || '');
                    $('#kz_zsxf').text(data.kz.zsxf || '');
                    $('#kz_zsxs').text(data.kz.zsxs || '');
                    $('#kz_scyq').text(data.kz.scyq || '');
                    $('#kz_bz').text(data.kz.bz || '');
                    $('#kz').show();
                    $('#detailInfo').show();
                } else if (data.flag == '3' || data.flag == '4') {
                    // 学年学期信息
                    $('#xnxq_jhxn').text(data.jhxnxq.id.jhxn || '');
                    $('#xnxq_xqlx').text(data.jhxnxq.id.xqlxdm || '');
                    $('#xnxq_jhxq').text(data.jhxnxq.id.xqdm || '');
                    $('#xnxq_zsxdms').text(data.jhxnxq.zsms || '');
                    $('#xnxq_zdxdms').text(data.jhxnxq.zdms || '');
                    $('#xnxq_zsxdxf').text(data.jhxnxq.zsxf || '');
                    $('#xnxq_zdxdxf').text(data.jhxnxq.zdxf || '');
                    $('#xnxq_zsxdxs').text(data.jhxnxq.zsxs || '');
                    $('#xnxq_zdxdxs').text(data.jhxnxq.zdxs || '');
                    $('#xnxq_scyq').text(data.jhxnxq.scyq || '');
                    $('#xnxq_bz').text(data.jhxnxq.bz || '');
                    $('#xnxq').show();
                    $('#detailInfo').show();
                } else if (data.flag == '5' || data.flag == '2') {
                    // 课程信息
                    $('#kc_kch').text(data.kc.kch || '');
                    $('#kc_kcm').text(data.kc.kcm || '');
                    $('#kc_ywkcm').text(data.kc.ywkcm || '');
                    $('#kc_xsm').text(data.kc.xsm || '');
                    $('#kc_kkxq').text(data.kc.kkxq || '');
                    $('#kc_bybz').text(data.kc.bybz || '');
                    $('#kc_xf').text(data.kc.xf || '');
                    $('#kc_xs').text(data.kc.xs || '');
                    $('#kc_ksrq').text(data.kc.ksrq || '');
                    $('#kc_jsrq').text(data.kc.jsrq || '');
                    $('#kc_kcztmc').text(data.kc.kcztsm || '');
                    $('#kc_knzxs').text(data.kc.knzxs || '');
                    $('#kc_jkzxs').text(data.kc.jkzxs || '');
                    $('#kc_sjzxs').text(data.kc.sjzxs || '');
                    $('#kc_syzxs').text(data.kc.syzxs || '');
                    $('#kc_qzsjzxs').text(data.kc.qzsjzxs || '');
                    $('#kc_tlfdzxs').text(data.kc.tlfdzxs || '');
                    $('#kc_sjzyzxs').text(data.kc.sjzyzxs || '');
                    $('#kc_kwzxs').text(data.kc.kwzxs || '');
                    $('#kc_kwxf').text(data.kc.kwxf || '');
                    $('#kc_kclbmc').text(data.kc.kclbmc || '');
                    $('#kc_jxfssm').text(data.kc.jxfssm || '');
                    $('#kc_jsm').text(data.kc.jsm || '');
                    $('#kc_jc').text(data.kc.jc || '');
                    $('#kc_cks').text(data.kc.cks || '');
                    $('#kc_szdw').text(data.kc.szdw || '');
                    $('#kc_kcsm').text(data.kc.kcsm || '');
                    $('#kc_kslxmc').text(data.kc.kslxmc || '');
                    $('#kc_xqm').text(data.kc.xqm || '');
                    $('#kc_nrjj').text(data.kc.nrjj || '');
                    $('#kc_bz').text(data.kc.bz || '');
                    $('#kc').show();
                    $('#detailInfo').show();
                }
            }).fail(function() {
                showError("获取详细信息失败");
            });
        }

        // 切换选项卡
        function switchTab(tabName) {
            // 更新选项卡状态
            $('.tab-item').removeClass('active');
            $(`.tab-item[onclick="switchTab('${tabName}')"]`).addClass('active');

            // 更新内容显示
            $('.tab-pane').removeClass('active');
            $(`#${tabName}`).addClass('active');

            currentTab = tabName;

            // 如果切换到结构页面，隐藏详情
            if (tabName === 'structure') {
                $('#detailInfo').hide();
            }
        }

        // 展开所有节点
        function expandAll() {
            $('.tree-node').show();
        }

        // 收起所有节点
        function collapseAll() {
            $('.tree-node').each(function(index) {
                if (index > 0) { // 保留根节点
                    $(this).hide();
                }
            });
        }

        // 刷新数据
        function refreshData() {
            loadPlanList();
            $('#planDetailContainer').hide();
            $('#detailInfo').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight() || 0;
            const availableHeight = windowHeight - navbarHeight;

            $('.page-mobile').css('min-height', availableHeight + 'px');
        }
    </script>
</body>
</html>
