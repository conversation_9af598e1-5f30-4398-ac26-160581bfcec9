<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>移动端专属功能</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 移动端专属功能页面样式 */
        .mobile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .mobile-features {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .features-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .features-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .feature-card {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            cursor: pointer;
            transition: all var(--transition-base);
            text-align: center;
        }
        
        .feature-card:active {
            background: var(--bg-color-active);
            transform: scale(0.98);
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--margin-sm);
            font-size: 24px;
        }
        
        .feature-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .feature-description {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .gesture-controls {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .controls-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .controls-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .gesture-list {
            display: grid;
            gap: var(--spacing-sm);
        }
        
        .gesture-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            background: var(--bg-tertiary);
        }
        
        .gesture-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: var(--info-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: 14px;
        }
        
        .gesture-content {
            flex: 1;
        }
        
        .gesture-name {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .gesture-description {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .device-features {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .device-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .device-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .device-list {
            display: grid;
            gap: var(--spacing-md);
        }
        
        .device-item {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .device-item:active {
            background: var(--bg-color-active);
        }
        
        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .device-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }
        
        .device-icon {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .device-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-supported {
            background: var(--success-color);
            color: white;
        }
        
        .status-partial {
            background: var(--warning-color);
            color: white;
        }
        
        .status-unsupported {
            background: var(--error-color);
            color: white;
        }
        
        .device-description {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .actions-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .action-btn {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-primary);
            text-decoration: none;
        }
        
        .action-btn:active {
            background: var(--bg-color-active);
        }
        
        .action-btn i {
            display: block;
            font-size: 24px;
            margin-bottom: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .action-btn span {
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        @media (max-width: 480px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">移动端专属功能</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="mobile-header">
            <div class="header-title">移动端专属功能</div>
            <div class="header-subtitle">专为移动设备优化的功能特性</div>
        </div>
        
        <!-- 移动端特性 -->
        <div class="mobile-features">
            <div class="features-title">
                <i class="ace-icon fa fa-mobile"></i>
                <span>移动端特性</span>
            </div>
            <div class="feature-grid">
                <div class="feature-card" onclick="viewFeature('touch')">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-hand-pointer-o"></i>
                    </div>
                    <div class="feature-name">触摸操作</div>
                    <div class="feature-description">优化的触摸交互体验</div>
                </div>
                
                <div class="feature-card" onclick="viewFeature('responsive')">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-expand"></i>
                    </div>
                    <div class="feature-name">响应式布局</div>
                    <div class="feature-description">自适应各种屏幕尺寸</div>
                </div>
                
                <div class="feature-card" onclick="viewFeature('offline')">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-cloud-download"></i>
                    </div>
                    <div class="feature-name">离线缓存</div>
                    <div class="feature-description">支持离线数据访问</div>
                </div>
                
                <div class="feature-card" onclick="viewFeature('notification')">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-bell"></i>
                    </div>
                    <div class="feature-name">推送通知</div>
                    <div class="feature-description">实时消息推送提醒</div>
                </div>
                
                <div class="feature-card" onclick="viewFeature('camera')">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-camera"></i>
                    </div>
                    <div class="feature-name">相机集成</div>
                    <div class="feature-description">支持拍照和扫码功能</div>
                </div>
                
                <div class="feature-card" onclick="viewFeature('location')">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-map-marker"></i>
                    </div>
                    <div class="feature-name">位置服务</div>
                    <div class="feature-description">基于位置的服务功能</div>
                </div>
            </div>
        </div>
        
        <!-- 手势控制 -->
        <div class="gesture-controls">
            <div class="controls-title">
                <i class="ace-icon fa fa-hand-rock-o"></i>
                <span>手势控制</span>
            </div>
            <div class="gesture-list">
                <div class="gesture-item">
                    <div class="gesture-icon">
                        <i class="ace-icon fa fa-hand-o-up"></i>
                    </div>
                    <div class="gesture-content">
                        <div class="gesture-name">点击</div>
                        <div class="gesture-description">单击选择和确认操作</div>
                    </div>
                </div>
                
                <div class="gesture-item">
                    <div class="gesture-icon">
                        <i class="ace-icon fa fa-arrows"></i>
                    </div>
                    <div class="gesture-content">
                        <div class="gesture-name">滑动</div>
                        <div class="gesture-description">上下滑动浏览内容</div>
                    </div>
                </div>
                
                <div class="gesture-item">
                    <div class="gesture-icon">
                        <i class="ace-icon fa fa-expand"></i>
                    </div>
                    <div class="gesture-content">
                        <div class="gesture-name">缩放</div>
                        <div class="gesture-description">双指缩放查看详情</div>
                    </div>
                </div>
                
                <div class="gesture-item">
                    <div class="gesture-icon">
                        <i class="ace-icon fa fa-long-arrow-left"></i>
                    </div>
                    <div class="gesture-content">
                        <div class="gesture-name">侧滑</div>
                        <div class="gesture-description">左右滑动切换页面</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 设备功能 -->
        <div class="device-features">
            <div class="device-title">
                <i class="ace-icon fa fa-cog"></i>
                <span>设备功能</span>
            </div>
            <div class="device-list">
                <div class="device-item" onclick="viewDevice('camera')">
                    <div class="device-header">
                        <div class="device-name">
                            <i class="ace-icon fa fa-camera device-icon"></i>
                            摄像头
                        </div>
                        <div class="device-status status-supported">已支持</div>
                    </div>
                    <div class="device-description">支持拍照、扫码、文档识别等功能</div>
                </div>
                
                <div class="device-item" onclick="viewDevice('gps')">
                    <div class="device-header">
                        <div class="device-name">
                            <i class="ace-icon fa fa-map-marker device-icon"></i>
                            GPS定位
                        </div>
                        <div class="device-status status-supported">已支持</div>
                    </div>
                    <div class="device-description">提供位置服务和地图导航功能</div>
                </div>
                
                <div class="device-item" onclick="viewDevice('storage')">
                    <div class="device-header">
                        <div class="device-name">
                            <i class="ace-icon fa fa-hdd-o device-icon"></i>
                            本地存储
                        </div>
                        <div class="device-status status-supported">已支持</div>
                    </div>
                    <div class="device-description">支持离线数据存储和缓存功能</div>
                </div>
                
                <div class="device-item" onclick="viewDevice('notification')">
                    <div class="device-header">
                        <div class="device-name">
                            <i class="ace-icon fa fa-bell device-icon"></i>
                            推送通知
                        </div>
                        <div class="device-status status-partial">部分支持</div>
                    </div>
                    <div class="device-description">支持系统通知和消息推送</div>
                </div>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                <span>快捷操作</span>
            </div>
            <div class="action-buttons">
                <a href="/student/systemOverview" class="action-btn">
                    <i class="ace-icon fa fa-dashboard"></i>
                    <span>系统概览</span>
                </a>
                <a href="/student/userExperience" class="action-btn">
                    <i class="ace-icon fa fa-star"></i>
                    <span>用户体验</span>
                </a>
                <a href="/student/weeklySchedule" class="action-btn">
                    <i class="ace-icon fa fa-table"></i>
                    <span>课程表</span>
                </a>
                <a href="/student/personalManagement" class="action-btn">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人管理</span>
                </a>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            detectDeviceFeatures();
        }

        // 检测设备功能
        function detectDeviceFeatures() {
            // 检测摄像头
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                console.log('Camera supported');
            }
            
            // 检测GPS
            if (navigator.geolocation) {
                console.log('GPS supported');
            }
            
            // 检测本地存储
            if (typeof(Storage) !== "undefined") {
                console.log('Local storage supported');
            }
            
            // 检测推送通知
            if ("Notification" in window) {
                console.log('Notification supported');
            }
        }

        // 查看功能详情
        function viewFeature(featureType) {
            // 实现功能详情查看逻辑
            console.log('View feature:', featureType);
        }

        // 查看设备功能
        function viewDevice(deviceType) {
            // 实现设备功能查看逻辑
            console.log('View device:', deviceType);
        }

        // 刷新数据
        function refreshData() {
            showLoading(true);
            
            setTimeout(function() {
                showLoading(false);
                showSuccess('移动端功能数据已更新');
            }, 1000);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            // 这里可以添加成功提示的实现
            console.log('Success: ' + message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
        }
    </script>
</body>
</html>
