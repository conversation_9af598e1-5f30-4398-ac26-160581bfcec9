<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学分学费</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学分学费页面样式 */
        .warning-notice {
            background: var(--warning-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .info-notice {
            background: var(--info-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .summary-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .section-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .summary-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
            font-weight: 500;
        }
        
        .detail-value.amount {
            color: var(--primary-color);
        }
        
        .detail-value.payment-link {
            color: var(--primary-color);
            text-decoration: underline;
            cursor: pointer;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .detail-item.highlight {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 4px;
            margin: 4px 0;
        }
        
        .detail-item.highlight .detail-value {
            color: var(--error-color);
            font-weight: 600;
        }
        
        .course-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .course-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .course-code {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .subtotal-item {
            background: var(--bg-tertiary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .subtotal-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .subtotal-value {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .tabs-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            display: flex;
            background: var(--bg-tertiary);
        }
        
        .tab-button {
            flex: 1;
            padding: var(--padding-md);
            background: transparent;
            border: none;
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .tab-button.active {
            background: var(--bg-primary);
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学分学费</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 提示信息 -->
        <c:if test="${not empty kg}">
            <div class="warning-notice">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <strong>提示：</strong>${kg}
            </div>
        </c:if>
        
        <c:if test="${empty kg}">
            <!-- 说明信息 -->
            <div class="info-notice">
                <strong>说明：</strong><br>
                1. 应预交学费总额：为财务处提供的每学年学费预交额之和<br>
                2. 实修学分费用：为实际修读学分按培养方案中课程类别学分收费标准核算的费用之和<br>
                3. 方案修读总费用：实修学分超过培养方案要求最低学分时，为实修学分费用；不超过时，为培养方案要求最低学分费用之和<br>
                4. 应补缴加修学分费用=方案修读总费用-应预交学费总额
            </div>
            
            <!-- 选项卡 -->
            <div class="tabs-container">
                <div class="tabs-header">
                    <button class="tab-button active" onclick="switchTab('summary')">
                        <i class="ace-icon fa fa-list"></i>
                        汇总信息
                    </button>
                    <button class="tab-button" onclick="switchTab('detail')">
                        <i class="ace-icon fa fa-table"></i>
                        明细信息
                    </button>
                </div>
                
                <div class="tab-content">
                    <!-- 汇总信息 -->
                    <div class="tab-pane active" id="summaryTab">
                        <div id="summaryList">
                            <!-- 动态加载汇总数据 -->
                        </div>
                    </div>
                    
                    <!-- 明细信息 -->
                    <div class="tab-pane" id="detailTab">
                        <div id="detailList">
                            <!-- 动态加载明细数据 -->
                        </div>
                    </div>
                </div>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无学分学费数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let summaryData = [];
        let detailData = [];
        let currentTab = 'summary';

        $(function() {
            initPage();
            loadSummaryData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换选项卡
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新按钮状态
            $('.tab-button').removeClass('active');
            $(`.tab-button:contains('${tab === 'summary' ? '汇总信息' : '明细信息'}')`).addClass('active');
            
            // 更新内容显示
            $('.tab-pane').removeClass('active');
            if (tab === 'summary') {
                $('#summaryTab').addClass('active');
                if (summaryData.length === 0) {
                    loadSummaryData();
                }
            } else {
                $('#detailTab').addClass('active');
                if (detailData.length === 0) {
                    loadDetailData();
                }
            }
        }

        // 加载汇总数据
        function loadSummaryData() {
            showLoading(true);

            $.ajax({
                url: "/student/creditTuition/search",
                type: "post",
                data: "pageNum=1&pageSize=50",
                dataType: "json",
                success: function(data) {
                    if (data.status === 200 && data.data && data.data.records) {
                        summaryData = data.data.records;
                        renderSummaryList();
                        showEmptyState(false);
                    } else {
                        summaryData = [];
                        renderSummaryList();
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染汇总列表
        function renderSummaryList() {
            const container = $('#summaryList');
            container.empty();

            if (summaryData.length === 0) {
                showEmptyState(true);
                return;
            }

            summaryData.forEach(function(item, index) {
                const itemHtml = createSummaryItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建汇总项目HTML
        function createSummaryItem(item, index) {
            return `
                <div class="summary-item">
                    <div class="summary-header">
                        <div class="summary-title">${item.FAMC || '培养方案'}</div>
                    </div>

                    <div class="summary-details">
                        <div class="detail-item">
                            <span class="detail-label">课程总学分</span>
                            <span class="detail-value">${item.KCZXF || '0'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">实修学分费用</span>
                            <span class="detail-value amount">¥${item.SJJE || '0.00'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">其他学分</span>
                            <span class="detail-value">${item.BZ || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">方案修读总费用</span>
                            <span class="detail-value amount">¥${item.FAXDZE || '0.00'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">应预交学费总额</span>
                            <span class="detail-value payment-link" onclick="openPaymentQuery();">¥${item.YJXFZE || '0.00'}</span>
                        </div>
                        <div class="detail-item highlight full-width">
                            <span class="detail-label">应补缴加修学分费用</span>
                            <span class="detail-value">¥${item.YBJFYZE || '0.00'}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 加载明细数据
        function loadDetailData() {
            showLoading(true);

            $.ajax({
                url: "/student/creditTuition/searchMx",
                type: "post",
                data: "pageNum=1&pageSize=200",
                dataType: "json",
                success: function(data) {
                    if (data && data.length > 0) {
                        detailData = data;
                        renderDetailList();
                        showEmptyState(false);
                    } else {
                        detailData = [];
                        renderDetailList();
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染明细列表
        function renderDetailList() {
            const container = $('#detailList');
            container.empty();

            if (detailData.length === 0) {
                showEmptyState(true);
                return;
            }

            let currentGroup = '';
            let groupTotal = 0;
            let currentPlan = '';
            let planTotal = 0;

            detailData.forEach(function(item, index) {
                const planName = item[2];
                const groupName = item[3] + item[4];
                const amount = parseFloat(item[9] || 0);

                // 检查是否需要显示课组小计
                if (currentGroup && currentGroup !== groupName) {
                    appendSubtotal(container, '课组小计', groupTotal);
                    groupTotal = 0;
                }

                // 检查是否需要显示方案合计
                if (currentPlan && currentPlan !== planName) {
                    appendSubtotal(container, '方案合计', planTotal);
                    planTotal = 0;
                }

                currentGroup = groupName;
                currentPlan = planName;
                groupTotal += amount;
                planTotal += amount;

                const itemHtml = createDetailItem(item, index);
                container.append(itemHtml);

                // 最后一项添加小计
                if (index === detailData.length - 1) {
                    appendSubtotal(container, '课组小计', groupTotal);
                    appendSubtotal(container, '方案合计', planTotal);
                }
            });
        }

        // 创建明细项目HTML
        function createDetailItem(item, index) {
            const status = item[10] === '已修' ? 'completed' : 'pending';

            return `
                <div class="course-item">
                    <div class="course-header">
                        <div class="course-title">${item[6] || '课程名称'}</div>
                        <div class="course-code">${item[5] || '-'}</div>
                    </div>

                    <div class="course-details">
                        <div class="detail-item">
                            <span class="detail-label">方案名称</span>
                            <span class="detail-value">${item[2] || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">父课组</span>
                            <span class="detail-value">${item[3] || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">课组名称</span>
                            <span class="detail-value">${item[4] || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">课程学分</span>
                            <span class="detail-value">${item[7] || '0'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">收费标准</span>
                            <span class="detail-value">¥${item[8] || '0.00'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">收费金额</span>
                            <span class="detail-value amount">¥${item[9] || '0.00'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">修读状态</span>
                            <span class="detail-value">
                                <span class="status-badge status-${status}">
                                    ${item[10] || '未修'}
                                </span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">统计学期</span>
                            <span class="detail-value">${item[17] || '-'}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 添加小计
        function appendSubtotal(container, label, total) {
            const subtotalHtml = `
                <div class="subtotal-item">
                    <div class="subtotal-label">${label}</div>
                    <div class="subtotal-value">¥${total.toFixed(2)}</div>
                </div>
            `;
            container.append(subtotalHtml);
        }

        // 打开缴费查询
        function openPaymentQuery() {
            if (parent && parent.addTab) {
                parent.addTab('缴费查询', '/student/creditTuition/searchPayment/index');
            } else {
                window.location.href = '/student/creditTuition/searchPayment/index';
            }
        }

        // 刷新数据
        function refreshData() {
            if (currentTab === 'summary') {
                loadSummaryData();
            } else {
                loadDetailData();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
            } else {
                $('#emptyState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
