<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实验报告</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实验报告页面样式 */
        .report-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }

        .experiment-info {
            text-align: center;
        }

        .experiment-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }

        .experiment-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            font-size: var(--font-size-small);
            opacity: 0.9;
        }

        .detail-item {
            text-align: center;
        }

        .detail-label {
            margin-bottom: 4px;
        }

        .detail-value {
            font-weight: 500;
        }

        .report-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            margin-bottom: var(--margin-lg);
        }

        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }

        .form-group {
            margin-bottom: var(--margin-md);
        }

        .form-label {
            display: block;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }

        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }

        .form-control {
            width: 100%;
            min-height: 44px;
            padding: 12px 16px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color var(--transition-base);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-help {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            margin-top: var(--margin-xs);
            line-height: var(--line-height-base);
        }

        .file-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .file-upload:hover {
            border-color: var(--primary-color);
            background: rgba(24, 144, 255, 0.05);
        }

        .upload-icon {
            font-size: 32px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }

        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
        }

        .upload-hint {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }

        .file-list {
            margin-top: var(--margin-md);
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }

        .file-icon {
            color: var(--primary-color);
            margin-right: var(--margin-sm);
        }

        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }

        .file-size {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            margin-right: var(--margin-sm);
        }

        .file-remove {
            color: var(--error-color);
            cursor: pointer;
            padding: 4px;
        }

        .progress-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .progress-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            margin-bottom: var(--margin-md);
        }

        .progress-step {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            position: relative;
            z-index: 2;
        }

        .progress-step.active {
            background: var(--primary-color);
            color: white;
        }

        .progress-step.completed {
            background: var(--success-color);
            color: white;
        }

        .progress-line {
            position: absolute;
            top: 50%;
            left: 16px;
            right: 16px;
            height: 2px;
            background: var(--bg-tertiary);
            z-index: 1;
        }

        .progress-line::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .progress-labels {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }

        .btn-save {
            background: var(--warning-color);
            color: white;
        }

        .btn-submit {
            background: var(--success-color);
            color: white;
        }

        .btn-preview {
            background: var(--info-color);
            color: white;
        }

        .report-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
        }

        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--info-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }

        .status-title i {
            margin-right: var(--margin-xs);
        }

        .status-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }

        .template-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .template-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }

        .template-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }

        .template-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .template-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }

        .template-info {
            flex: 1;
        }

        .template-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 2px;
        }

        .template-desc {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }

        .btn-download {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实验报告</div>
            <div class="navbar-action" onclick="showHelp();">
                <i class="ace-icon fa fa-question-circle"></i>
            </div>
        </nav>

        <!-- 实验信息 -->
        <div class="report-header">
            <div class="experiment-info">
                <div class="experiment-title" id="experimentTitle">实验名称</div>
                <div class="experiment-details">
                    <div class="detail-item">
                        <div class="detail-label">课程名称</div>
                        <div class="detail-value" id="courseName">-</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">指导教师</div>
                        <div class="detail-value" id="teacherName">-</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">实验时间</div>
                        <div class="detail-value" id="experimentTime">-</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">截止时间</div>
                        <div class="detail-value" id="deadline">-</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-section">
            <div class="progress-title">报告进度</div>
            <div class="progress-steps">
                <div class="progress-line" id="progressLine"></div>
                <div class="progress-step completed" id="step1">1</div>
                <div class="progress-step active" id="step2">2</div>
                <div class="progress-step" id="step3">3</div>
                <div class="progress-step" id="step4">4</div>
            </div>
            <div class="progress-labels">
                <span>开始</span>
                <span>编写</span>
                <span>审核</span>
                <span>完成</span>
            </div>
        </div>

        <!-- 报告状态 -->
        <div class="report-status" id="reportStatus">
            <div class="status-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>报告状态</span>
            </div>
            <div class="status-content" id="statusContent">
                报告正在编写中，请按时提交。
            </div>
        </div>

        <!-- 模板下载 -->
        <div class="template-section">
            <div class="template-title">
                <i class="ace-icon fa fa-download"></i>
                <span>报告模板</span>
            </div>
            <div class="template-list" id="templateList">
                <!-- 模板列表 -->
            </div>
        </div>

        <!-- 报告表单 -->
        <div class="report-form">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <label class="form-label">学生姓名</label>
                    <input type="text" class="form-control" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <label class="form-label">学号</label>
                    <input type="text" class="form-control" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <label class="form-label">班级</label>
                    <input type="text" class="form-control" id="className" readonly>
                </div>

                <div class="form-group">
                    <label class="form-label">实验日期</label>
                    <input type="date" class="form-control" id="experimentDate">
                </div>
            </div>

            <!-- 实验内容 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-flask"></i>
                    <span>实验内容</span>
                </div>

                <div class="form-group">
                    <label class="form-label required">实验目的</label>
                    <textarea class="form-control form-textarea" id="experimentPurpose"
                              placeholder="请描述本次实验的目的和要求..."></textarea>
                    <div class="form-help">详细说明实验的目标和预期达到的效果</div>
                </div>

                <div class="form-group">
                    <label class="form-label required">实验原理</label>
                    <textarea class="form-control form-textarea" id="experimentPrinciple"
                              placeholder="请阐述实验的理论基础和原理..."></textarea>
                    <div class="form-help">说明实验涉及的理论知识和科学原理</div>
                </div>

                <div class="form-group">
                    <label class="form-label required">实验步骤</label>
                    <textarea class="form-control form-textarea" id="experimentSteps"
                              placeholder="请详细描述实验的操作步骤..."></textarea>
                    <div class="form-help">按顺序列出实验的具体操作步骤</div>
                </div>
            </div>

            <!-- 实验结果 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-bar-chart"></i>
                    <span>实验结果</span>
                </div>

                <div class="form-group">
                    <label class="form-label required">实验数据</label>
                    <textarea class="form-control form-textarea" id="experimentData"
                              placeholder="请记录实验过程中获得的数据..."></textarea>
                    <div class="form-help">如实记录实验中测量或观察到的数据</div>
                </div>

                <div class="form-group">
                    <label class="form-label required">结果分析</label>
                    <textarea class="form-control form-textarea" id="resultAnalysis"
                              placeholder="请分析实验结果，讨论数据的意义..."></textarea>
                    <div class="form-help">对实验结果进行分析和讨论</div>
                </div>

                <div class="form-group">
                    <label class="form-label required">实验结论</label>
                    <textarea class="form-control form-textarea" id="experimentConclusion"
                              placeholder="请总结实验的结论..."></textarea>
                    <div class="form-help">总结实验得出的结论和收获</div>
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-paperclip"></i>
                    <span>附件材料</span>
                </div>

                <div class="form-group">
                    <label class="form-label">实验图片/数据文件</label>
                    <div class="file-upload" id="fileUpload" onclick="selectFiles()">
                        <div class="upload-icon">
                            <i class="ace-icon fa fa-cloud-upload"></i>
                        </div>
                        <div class="upload-text">点击上传或拖拽文件到此处</div>
                        <div class="upload-hint">支持图片、Excel、Word、PDF格式，单个文件不超过10MB</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept="image/*,.pdf,.doc,.docx,.xls,.xlsx" style="display: none;">
                    <div class="file-list" id="fileList"></div>
                    <div class="form-help">可上传实验过程中的照片、数据表格、计算文件等</div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn-mobile btn-save flex-1" onclick="saveDraft();">保存草稿</button>
                <button class="btn-mobile btn-preview flex-1" onclick="previewReport();">预览</button>
                <button class="btn-mobile btn-submit flex-1" onclick="submitReport();">提交报告</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let experimentId = '';
        let reportData = {};
        let uploadedFiles = [];
        let hasChanges = false;

        $(function() {
            initPage();
            loadExperimentInfo();
            loadReportData();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 获取实验ID
            const urlParams = new URLSearchParams(window.location.search);
            experimentId = urlParams.get('experimentId') || '';

            if (!experimentId) {
                showError('缺少实验ID参数');
                return;
            }
        }

        // 绑定事件
        function bindEvents() {
            // 监听表单变化
            $('.form-control').on('input change', function() {
                hasChanges = true;
                updateProgress();
            });

            // 文件拖拽事件
            const fileUpload = document.getElementById('fileUpload');

            fileUpload.addEventListener('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            fileUpload.addEventListener('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            fileUpload.addEventListener('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                handleFiles(e.dataTransfer.files);
            });

            // 文件选择事件
            $('#fileInput').on('change', function(e) {
                handleFiles(e.target.files);
            });

            // 阻止页面刷新时丢失数据
            $(window).on('beforeunload', function(e) {
                if (hasChanges) {
                    const message = '您有未保存的修改，确定要离开吗？';
                    e.returnValue = message;
                    return message;
                }
            });
        }

        // 加载实验信息
        function loadExperimentInfo() {
            $.ajax({
                url: "/student/experiment/report/getExperimentInfo",
                type: "post",
                data: { experimentId: experimentId },
                dataType: "json",
                success: function(data) {
                    if (data && data.experiment) {
                        const exp = data.experiment;
                        $('#experimentTitle').text(exp.name);
                        $('#courseName').text(exp.courseName);
                        $('#teacherName').text(exp.teacher);
                        $('#experimentTime').text(exp.startTime);
                        $('#deadline').text(exp.deadline);

                        // 加载模板列表
                        loadTemplates(data.templates);

                        // 更新状态
                        updateReportStatus(data.status);
                    }
                },
                error: function() {
                    showError('加载实验信息失败');
                }
            });
        }

        // 加载报告数据
        function loadReportData() {
            $.ajax({
                url: "/student/experiment/report/getReportData",
                type: "post",
                data: { experimentId: experimentId },
                dataType: "json",
                success: function(data) {
                    if (data && data.report) {
                        reportData = data.report;
                        populateForm(reportData);
                        hasChanges = false;
                        updateProgress();
                    } else {
                        // 加载学生基本信息
                        loadStudentInfo();
                    }
                },
                error: function() {
                    console.log('暂无报告数据，加载学生信息');
                    loadStudentInfo();
                }
            });
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/experiment/report/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data && data.student) {
                        $('#studentName').val(data.student.name);
                        $('#studentId').val(data.student.id);
                        $('#className').val(data.student.className);
                    }
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 填充表单
        function populateForm(data) {
            $('#studentName').val(data.studentName || '');
            $('#studentId').val(data.studentId || '');
            $('#className').val(data.className || '');
            $('#experimentDate').val(data.experimentDate || '');
            $('#experimentPurpose').val(data.experimentPurpose || '');
            $('#experimentPrinciple').val(data.experimentPrinciple || '');
            $('#experimentSteps').val(data.experimentSteps || '');
            $('#experimentData').val(data.experimentData || '');
            $('#resultAnalysis').val(data.resultAnalysis || '');
            $('#experimentConclusion').val(data.experimentConclusion || '');

            // 加载已上传的文件
            if (data.attachments) {
                data.attachments.forEach(file => {
                    addFileToList(file, true);
                });
            }
        }

        // 加载模板列表
        function loadTemplates(templates) {
            const container = $('#templateList');
            container.empty();

            if (!templates || templates.length === 0) {
                container.html('<div style="text-align: center; color: var(--text-secondary);">暂无模板</div>');
                return;
            }

            templates.forEach(template => {
                const templateHtml = `
                    <div class="template-item">
                        <div class="template-info">
                            <div class="template-name">${template.name}</div>
                            <div class="template-desc">${template.description}</div>
                        </div>
                        <button class="btn-download" onclick="downloadTemplate('${template.id}')">下载</button>
                    </div>
                `;
                container.append(templateHtml);
            });
        }

        // 下载模板
        function downloadTemplate(templateId) {
            window.open(`/student/experiment/report/downloadTemplate?templateId=${templateId}`);
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                // 验证文件类型
                const allowedTypes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
                    'application/pdf',
                    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                ];

                if (!allowedTypes.includes(file.type)) {
                    showError(`文件 ${file.name} 格式不支持`);
                    return;
                }

                // 验证文件大小
                if (file.size > 10 * 1024 * 1024) {
                    showError(`文件 ${file.name} 大小超过10MB限制`);
                    return;
                }

                // 添加到文件列表
                uploadedFiles.push(file);
                addFileToList(file, false);
                hasChanges = true;
            });
        }

        // 添加文件到列表
        function addFileToList(file, isExisting = false) {
            const fileId = isExisting ? file.id : 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const fileName = isExisting ? file.name : file.name;
            const fileSize = isExisting ? file.size : formatFileSize(file.size);

            const fileHtml = `
                <div class="file-item" id="${fileId}">
                    <i class="ace-icon fa fa-file file-icon"></i>
                    <span class="file-name">${fileName}</span>
                    <span class="file-size">${fileSize}</span>
                    <i class="ace-icon fa fa-times file-remove" onclick="removeFile('${fileId}', '${fileName}', ${isExisting})"></i>
                </div>
            `;
            $('#fileList').append(fileHtml);
        }

        // 移除文件
        function removeFile(fileId, fileName, isExisting) {
            $(`#${fileId}`).remove();

            if (isExisting) {
                // 标记为删除的已存在文件
                if (!reportData.deletedAttachments) {
                    reportData.deletedAttachments = [];
                }
                reportData.deletedAttachments.push(fileId);
            } else {
                // 移除新上传的文件
                uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
            }

            hasChanges = true;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取表单数据
        function getFormData() {
            return {
                experimentId: experimentId,
                studentName: $('#studentName').val(),
                studentId: $('#studentId').val(),
                className: $('#className').val(),
                experimentDate: $('#experimentDate').val(),
                experimentPurpose: $('#experimentPurpose').val(),
                experimentPrinciple: $('#experimentPrinciple').val(),
                experimentSteps: $('#experimentSteps').val(),
                experimentData: $('#experimentData').val(),
                resultAnalysis: $('#resultAnalysis').val(),
                experimentConclusion: $('#experimentConclusion').val(),
                deletedAttachments: reportData.deletedAttachments || []
            };
        }

        // 验证表单
        function validateForm() {
            const errors = [];

            if (!$('#experimentPurpose').val().trim()) {
                errors.push('请填写实验目的');
            }

            if (!$('#experimentPrinciple').val().trim()) {
                errors.push('请填写实验原理');
            }

            if (!$('#experimentSteps').val().trim()) {
                errors.push('请填写实验步骤');
            }

            if (!$('#experimentData').val().trim()) {
                errors.push('请填写实验数据');
            }

            if (!$('#resultAnalysis').val().trim()) {
                errors.push('请填写结果分析');
            }

            if (!$('#experimentConclusion').val().trim()) {
                errors.push('请填写实验结论');
            }

            if (errors.length > 0) {
                showError(errors.join('\n'));
                return false;
            }

            return true;
        }

        // 保存草稿
        function saveDraft() {
            const formData = getFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交报告
        function submitReport() {
            if (!validateForm()) return;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要提交实验报告吗？提交后将无法修改。', function(confirmed) {
                    if (confirmed) {
                        doSubmitReport();
                    }
                });
            } else {
                if (confirm('确定要提交实验报告吗？提交后将无法修改。')) {
                    doSubmitReport();
                }
            }
        }

        // 执行提交报告
        function doSubmitReport() {
            const formData = getFormData();
            formData.isDraft = false;

            submitFormData(formData, '报告提交成功');
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            const submitData = new FormData();

            // 添加表单数据
            Object.keys(formData).forEach(key => {
                if (key === 'deletedAttachments') {
                    submitData.append(key, JSON.stringify(formData[key]));
                } else {
                    submitData.append(key, formData[key]);
                }
            });

            // 添加新上传的文件
            uploadedFiles.forEach((file, index) => {
                submitData.append(`attachment_${index}`, file);
            });

            $.ajax({
                url: "/student/experiment/report/submitReport",
                type: "post",
                data: submitData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        hasChanges = false;

                        // 更新报告状态
                        updateReportStatus(data.status);
                        updateProgress();

                        if (!formData.isDraft) {
                            // 提交成功后可选择返回
                            setTimeout(() => {
                                if (typeof urp !== 'undefined' && urp.confirm) {
                                    urp.confirm('报告提交成功，是否返回实验列表？', function(confirmed) {
                                        if (confirmed) {
                                            goBack();
                                        }
                                    });
                                }
                            }, 1000);
                        }
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 预览报告
        function previewReport() {
            if (!validateForm()) return;

            const formData = getFormData();

            // 生成预览内容
            let previewContent = `实验报告预览\n\n`;
            previewContent += `实验名称：${$('#experimentTitle').text()}\n`;
            previewContent += `学生姓名：${formData.studentName}\n`;
            previewContent += `学号：${formData.studentId}\n`;
            previewContent += `班级：${formData.className}\n`;
            previewContent += `实验日期：${formData.experimentDate}\n\n`;
            previewContent += `实验目的：\n${formData.experimentPurpose}\n\n`;
            previewContent += `实验原理：\n${formData.experimentPrinciple}\n\n`;
            previewContent += `实验步骤：\n${formData.experimentSteps}\n\n`;
            previewContent += `实验数据：\n${formData.experimentData}\n\n`;
            previewContent += `结果分析：\n${formData.resultAnalysis}\n\n`;
            previewContent += `实验结论：\n${formData.experimentConclusion}`;

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(previewContent);
            } else {
                alert(previewContent);
            }
        }

        // 更新进度
        function updateProgress() {
            const formData = getFormData();
            let completedFields = 0;
            let totalFields = 6; // 必填字段数量

            // 检查必填字段完成情况
            if (formData.experimentPurpose.trim()) completedFields++;
            if (formData.experimentPrinciple.trim()) completedFields++;
            if (formData.experimentSteps.trim()) completedFields++;
            if (formData.experimentData.trim()) completedFields++;
            if (formData.resultAnalysis.trim()) completedFields++;
            if (formData.experimentConclusion.trim()) completedFields++;

            const progress = Math.round((completedFields / totalFields) * 100);
            let step = 2; // 默认在编写阶段

            if (progress >= 100) step = 3; // 可以提交
            if (reportData.isSubmitted) step = 4; // 已提交

            // 更新进度条
            const progressLine = $('#progressLine');
            progressLine.removeClass('step-1 step-2 step-3 step-4');
            progressLine.addClass(`step-${step}`);

            // 更新步骤状态
            $('.progress-step').removeClass('active completed');
            for (let i = 1; i <= 4; i++) {
                const $step = $(`#step${i}`);
                if (i < step) {
                    $step.addClass('completed');
                } else if (i === step) {
                    $step.addClass('active');
                }
            }
        }

        // 更新报告状态
        function updateReportStatus(status) {
            if (!status) return;

            const statusElement = $('#reportStatus');
            const contentElement = $('#statusContent');

            switch(status.type) {
                case 'draft':
                    statusElement.removeClass().addClass('report-status').css('border-left-color', 'var(--warning-color)');
                    contentElement.text('报告已保存为草稿，请继续完善并提交。');
                    break;
                case 'submitted':
                    statusElement.removeClass().addClass('report-status').css('border-left-color', 'var(--success-color)');
                    contentElement.text('报告已提交，等待教师批阅。');
                    // 禁用表单
                    $('.form-control').prop('disabled', true);
                    $('.btn-mobile').prop('disabled', true);
                    break;
                case 'graded':
                    statusElement.removeClass().addClass('report-status').css('border-left-color', 'var(--info-color)');
                    contentElement.text(`报告已批阅，成绩：${status.score}分`);
                    $('.form-control').prop('disabled', true);
                    $('.btn-mobile').prop('disabled', true);
                    break;
                default:
                    contentElement.text('报告正在编写中，请按时提交。');
            }
        }

        // 返回上一页
        function goBack() {
            if (hasChanges) {
                if (typeof urp !== 'undefined' && urp.confirm) {
                    urp.confirm('您有未保存的修改，确定要离开吗？', function(confirmed) {
                        if (confirmed) {
                            doGoBack();
                        }
                    });
                } else {
                    if (confirm('您有未保存的修改，确定要离开吗？')) {
                        doGoBack();
                    }
                }
            } else {
                doGoBack();
            }
        }

        // 执行返回
        function doGoBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else if (history.length > 1) {
                history.back();
            } else {
                window.location.href = '/student/experiment/schedule/index';
            }
        }

        // 显示帮助
        function showHelp() {
            const helpText = `
实验报告填写说明：

1. 基本信息会自动填充，请确认无误。

2. 实验内容部分：
   - 实验目的：说明本次实验要达到的目标
   - 实验原理：阐述相关的理论基础
   - 实验步骤：详细记录操作过程

3. 实验结果部分：
   - 实验数据：如实记录测量数据
   - 结果分析：分析数据的意义
   - 实验结论：总结实验收获

4. 可上传相关的图片、数据文件作为附件。

5. 建议先保存草稿，完善后再正式提交。

6. 提交后无法修改，请仔细检查。
            `;

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(helpText);
            } else {
                alert(helpText);
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>