<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学分认定</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学分认定页面样式 */
        .credit-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .credit-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .summary-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .summary-number {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .recognition-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .type-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .type-item:last-child {
            border-bottom: none;
        }
        
        .type-item:active {
            background: var(--bg-color-active);
        }
        
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .type-icon.course {
            background: var(--success-color);
        }
        
        .type-icon.competition {
            background: var(--info-color);
        }
        
        .type-icon.certificate {
            background: var(--warning-color);
        }
        
        .type-icon.practice {
            background: var(--error-color);
        }
        
        .type-icon.research {
            background: var(--primary-color);
        }
        
        .type-content {
            flex: 1;
        }
        
        .type-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .type-credit {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
            background: var(--primary-color);
            color: white;
        }
        
        .my-recognitions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .recognitions-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .recognitions-title {
            display: flex;
            align-items: center;
        }
        
        .recognitions-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .recognitions-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .recognition-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .recognition-item:last-child {
            border-bottom: none;
        }
        
        .recognition-item:active {
            background: var(--bg-color-active);
        }
        
        .recognition-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .recognition-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .recognition-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .recognition-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .recognition-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .recognition-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .recognition-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .credit-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            margin-bottom: var(--margin-md);
        }
        
        .credit-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .credit-content {
            color: var(--text-secondary);
        }
        
        .recognition-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .credit-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .credit-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .form-upload:hover {
            border-color: var(--primary-color);
            background: var(--bg-tertiary);
        }
        
        .upload-icon {
            font-size: var(--font-size-h2);
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-list {
            margin-top: var(--margin-sm);
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }
        
        .file-item:last-child {
            margin-bottom: 0;
        }
        
        .file-icon {
            margin-right: var(--margin-sm);
            color: var(--primary-color);
        }
        
        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学分认定</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="credit-header">
            <div class="header-title">学分认定</div>
            <div class="header-subtitle">申请各类学分认定，丰富学习经历</div>
        </div>

        <!-- 学分统计 -->
        <div class="credit-summary">
            <div class="summary-title">学分统计</div>
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="summary-number" id="totalCredits">0</div>
                    <div class="summary-label">已获学分</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" id="recognizedCredits">0</div>
                    <div class="summary-label">认定学分</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" id="pendingCredits">0</div>
                    <div class="summary-label">待认定</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" id="requiredCredits">0</div>
                    <div class="summary-label">毕业要求</div>
                </div>
            </div>
        </div>

        <!-- 认定类型 -->
        <div class="recognition-types">
            <div class="type-item" onclick="showCreditForm('course')">
                <div class="type-icon course">
                    <i class="ace-icon fa fa-book"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">课程学分</div>
                    <div class="type-desc">校外修读课程学分认定</div>
                </div>
                <div class="type-credit">最高8学分</div>
            </div>

            <div class="type-item" onclick="showCreditForm('competition')">
                <div class="type-icon competition">
                    <i class="ace-icon fa fa-trophy"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">竞赛学分</div>
                    <div class="type-desc">学科竞赛获奖学分认定</div>
                </div>
                <div class="type-credit">最高6学分</div>
            </div>

            <div class="type-item" onclick="showCreditForm('certificate')">
                <div class="type-icon certificate">
                    <i class="ace-icon fa fa-certificate"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">证书学分</div>
                    <div class="type-desc">职业资格证书学分认定</div>
                </div>
                <div class="type-credit">最高4学分</div>
            </div>

            <div class="type-item" onclick="showCreditForm('practice')">
                <div class="type-icon practice">
                    <i class="ace-icon fa fa-cogs"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">实践学分</div>
                    <div class="type-desc">社会实践活动学分认定</div>
                </div>
                <div class="type-credit">最高4学分</div>
            </div>

            <div class="type-item" onclick="showCreditForm('research')">
                <div class="type-icon research">
                    <i class="ace-icon fa fa-flask"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">科研学分</div>
                    <div class="type-desc">科研成果发表学分认定</div>
                </div>
                <div class="type-credit">最高10学分</div>
            </div>
        </div>

        <!-- 我的认定 -->
        <div class="my-recognitions">
            <div class="recognitions-header">
                <div class="recognitions-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的认定</span>
                </div>
                <div class="recognitions-count" id="recognitionsCount">0</div>
            </div>

            <div id="recognitionsList">
                <!-- 认定列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-graduation-cap"></i>
            <div id="emptyMessage">暂无学分认定申请</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 学分认定申请表单 -->
    <div class="credit-form" id="creditForm">
        <div class="form-header">
            <div class="form-back" onclick="closeCreditForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">学分认定申请</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">认定类型</div>
                    <input type="text" class="form-input" id="recognitionType" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业班级</div>
                    <input type="text" class="form-input" id="majorClass" readonly>
                </div>
            </div>

            <!-- 认定内容 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span>认定内容</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">项目名称</div>
                    <input type="text" class="form-input" id="projectName" placeholder="请输入项目/课程/证书名称">
                </div>

                <div class="form-group">
                    <div class="form-label required">获得时间</div>
                    <input type="date" class="form-input" id="obtainDate">
                </div>

                <div class="form-group">
                    <div class="form-label required">申请学分</div>
                    <input type="number" class="form-input" id="appliedCredits" placeholder="请输入申请认定的学分数" min="0" max="10" step="0.5">
                </div>

                <div class="form-group">
                    <div class="form-label required">详细描述</div>
                    <textarea class="form-input form-textarea" id="description"
                              placeholder="请详细描述项目内容、获得过程、学习收获等..."></textarea>
                </div>
            </div>

            <!-- 认定依据 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <span>认定依据</span>
                </div>

                <div class="form-group" id="courseGroup" style="display: none;">
                    <div class="form-label">开设机构</div>
                    <input type="text" class="form-input" id="institution" placeholder="请输入开设机构名称">
                </div>

                <div class="form-group" id="competitionGroup" style="display: none;">
                    <div class="form-label">竞赛级别</div>
                    <select class="form-input" id="competitionLevel">
                        <option value="">请选择竞赛级别</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                        <option value="school">校级</option>
                    </select>
                </div>

                <div class="form-group" id="awardGroup" style="display: none;">
                    <div class="form-label">获奖等级</div>
                    <select class="form-input" id="awardLevel">
                        <option value="">请选择获奖等级</option>
                        <option value="first">一等奖</option>
                        <option value="second">二等奖</option>
                        <option value="third">三等奖</option>
                        <option value="excellence">优秀奖</option>
                    </select>
                </div>

                <div class="form-group" id="certificateGroup" style="display: none;">
                    <div class="form-label">证书类型</div>
                    <select class="form-input" id="certificateType">
                        <option value="">请选择证书类型</option>
                        <option value="professional">职业资格证书</option>
                        <option value="skill">技能等级证书</option>
                        <option value="language">语言能力证书</option>
                        <option value="computer">计算机等级证书</option>
                    </select>
                </div>

                <div class="form-group" id="researchGroup" style="display: none;">
                    <div class="form-label">成果类型</div>
                    <select class="form-input" id="researchType">
                        <option value="">请选择成果类型</option>
                        <option value="paper">学术论文</option>
                        <option value="patent">发明专利</option>
                        <option value="software">软件著作权</option>
                        <option value="project">科研项目</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label required">认定理由</div>
                    <textarea class="form-input form-textarea" id="reason"
                              placeholder="请说明申请学分认定的理由和依据..."></textarea>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮箱地址</div>
                    <input type="email" class="form-input" id="email" placeholder="请输入邮箱地址">
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-paperclip"></i>
                    <span>证明材料</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">上传附件</div>
                    <div class="form-upload" onclick="selectFiles()">
                        <div class="upload-icon">
                            <i class="ace-icon fa fa-cloud-upload"></i>
                        </div>
                        <div class="upload-text">点击上传证书、成绩单等证明材料</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                    <div class="file-list" id="fileList">
                        <!-- 文件列表将动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeCreditForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitCreditRecognition();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let myRecognitions = [];
        let currentRecognitionType = '';
        let uploadedFiles = [];
        let studentInfo = {};
        let creditSummary = {};

        $(function() {
            initPage();
            loadStudentInfo();
            loadCreditSummary();
            loadMyRecognitions();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFileUpload();
        }

        // 绑定文件上传
        function bindFileUpload() {
            $('#fileInput').change(function() {
                handleFileSelect(this.files);
            });
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/personalManagement/creditRecognition/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 加载学分统计
        function loadCreditSummary() {
            $.ajax({
                url: "/student/personalManagement/creditRecognition/getCreditSummary",
                type: "post",
                dataType: "json",
                success: function(data) {
                    creditSummary = data || {};
                    updateCreditSummary();
                },
                error: function() {
                    console.log('加载学分统计失败');
                }
            });
        }

        // 更新学分统计
        function updateCreditSummary() {
            $('#totalCredits').text(creditSummary.totalCredits || 0);
            $('#recognizedCredits').text(creditSummary.recognizedCredits || 0);
            $('#pendingCredits').text(creditSummary.pendingCredits || 0);
            $('#requiredCredits').text(creditSummary.requiredCredits || 0);
        }

        // 加载我的认定
        function loadMyRecognitions() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/creditRecognition/getMyRecognitions",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myRecognitions = data.recognitions || [];
                    renderRecognitionsList();
                    updateRecognitionsCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载认定列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染认定列表
        function renderRecognitionsList() {
            const container = $('#recognitionsList');
            container.empty();

            if (myRecognitions.length === 0) {
                showEmptyState('暂无学分认定申请');
                return;
            } else {
                hideEmptyState();
            }

            myRecognitions.forEach(recognition => {
                const recognitionHtml = createRecognitionItem(recognition);
                container.append(recognitionHtml);
            });
        }

        // 创建认定项
        function createRecognitionItem(recognition) {
            const statusClass = getRecognitionStatusClass(recognition.status);
            const statusText = getRecognitionStatusText(recognition.status);

            return `
                <div class="recognition-item ${statusClass}" onclick="showRecognitionDetail('${recognition.id}')">
                    <div class="recognition-basic">
                        <div class="recognition-title">${recognition.projectName}</div>
                        <div class="recognition-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="recognition-details">
                        <div class="detail-item">
                            <span>认定类型:</span>
                            <span>${getRecognitionTypeText(recognition.type)}</span>
                        </div>
                        <div class="detail-item">
                            <span>申请学分:</span>
                            <span>${recognition.appliedCredits}学分</span>
                        </div>
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(recognition.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>获得时间:</span>
                            <span>${formatDate(recognition.obtainDate)}</span>
                        </div>
                    </div>
                    <div class="credit-info">
                        <div class="credit-title">认定说明</div>
                        <div class="credit-content">${recognition.description}</div>
                    </div>
                    <div class="recognition-actions">
                        ${createRecognitionActions(recognition)}
                    </div>
                </div>
            `;
        }

        // 创建认定操作按钮
        function createRecognitionActions(recognition) {
            const canEdit = recognition.status === 'draft';

            let actions = [];

            actions.push(`<button class="btn-mobile btn-view" onclick="showRecognitionDetail('${recognition.id}')">查看</button>`);

            if (canEdit) {
                actions.push(`<button class="btn-mobile btn-edit" onclick="editRecognition('${recognition.id}')">编辑</button>`);
                actions.push(`<button class="btn-mobile btn-delete" onclick="deleteRecognition('${recognition.id}')">删除</button>`);
            }

            return actions.join('');
        }

        // 获取认定状态样式类
        function getRecognitionStatusClass(status) {
            switch(status) {
                case 'pending': return 'pending';
                case 'approved': return 'approved';
                case 'rejected': return 'rejected';
                default: return 'pending';
            }
        }

        // 获取认定状态文本
        function getRecognitionStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 获取认定类型文本
        function getRecognitionTypeText(type) {
            switch(type) {
                case 'course': return '课程学分';
                case 'competition': return '竞赛学分';
                case 'certificate': return '证书学分';
                case 'practice': return '实践学分';
                case 'research': return '科研学分';
                default: return '学分认定';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示学分认定申请表单
        function showCreditForm(type) {
            currentRecognitionType = type;

            // 设置表单标题
            $('#formTitle').text(getRecognitionTypeText(type) + '申请');
            $('#recognitionType').val(getRecognitionTypeText(type));

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 显示对应类型的特定字段
            showTypeSpecificFields(type);

            // 清空表单
            resetForm();

            // 显示表单
            $('#creditForm').addClass('show');
        }

        // 显示类型特定字段
        function showTypeSpecificFields(type) {
            // 隐藏所有特定字段
            $('#courseGroup, #competitionGroup, #awardGroup, #certificateGroup, #researchGroup').hide();

            // 根据类型显示对应字段
            switch(type) {
                case 'course':
                    $('#courseGroup').show();
                    break;
                case 'competition':
                    $('#competitionGroup, #awardGroup').show();
                    break;
                case 'certificate':
                    $('#certificateGroup').show();
                    break;
                case 'research':
                    $('#researchGroup').show();
                    break;
            }
        }

        // 重置表单
        function resetForm() {
            $('#projectName').val('');
            $('#obtainDate').val('');
            $('#appliedCredits').val('');
            $('#description').val('');
            $('#institution').val('');
            $('#competitionLevel').val('');
            $('#awardLevel').val('');
            $('#certificateType').val('');
            $('#researchType').val('');
            $('#reason').val('');
            $('#contactPhone').val('');
            $('#email').val('');
            uploadedFiles = [];
            renderFileList();
        }

        // 关闭学分认定申请表单
        function closeCreditForm() {
            $('#creditForm').removeClass('show');
        }

        // 编辑认定
        function editRecognition(recognitionId) {
            const recognition = myRecognitions.find(r => r.id === recognitionId);
            if (!recognition) return;

            // 设置当前认定类型
            currentRecognitionType = recognition.type;

            // 填充表单
            $('#formTitle').text('编辑' + getRecognitionTypeText(recognition.type));
            $('#recognitionType').val(getRecognitionTypeText(recognition.type));

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 显示对应类型的特定字段
            showTypeSpecificFields(recognition.type);

            // 填充认定数据
            fillRecognitionForm(recognition);

            // 显示表单
            $('#creditForm').addClass('show');
        }

        // 填充认定表单
        function fillRecognitionForm(recognition) {
            $('#projectName').val(recognition.projectName);
            $('#obtainDate').val(recognition.obtainDate);
            $('#appliedCredits').val(recognition.appliedCredits);
            $('#description').val(recognition.description);
            $('#reason').val(recognition.reason);
            $('#contactPhone').val(recognition.contactPhone);
            $('#email').val(recognition.email);

            // 填充类型特定字段
            if (recognition.institution) $('#institution').val(recognition.institution);
            if (recognition.competitionLevel) $('#competitionLevel').val(recognition.competitionLevel);
            if (recognition.awardLevel) $('#awardLevel').val(recognition.awardLevel);
            if (recognition.certificateType) $('#certificateType').val(recognition.certificateType);
            if (recognition.researchType) $('#researchType').val(recognition.researchType);
        }

        // 删除认定
        function deleteRecognition(recognitionId) {
            const recognition = myRecognitions.find(r => r.id === recognitionId);
            if (!recognition) return;

            const message = `确定要删除"${recognition.projectName}"的学分认定申请吗？\n\n删除后无法恢复。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doDeleteRecognition(recognitionId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doDeleteRecognition(recognitionId);
                }
            }
        }

        // 执行删除认定
        function doDeleteRecognition(recognitionId) {
            $.ajax({
                url: "/student/personalManagement/creditRecognition/deleteRecognition",
                type: "post",
                data: { recognitionId: recognitionId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('认定申请删除成功');
                        loadCreditSummary();
                        loadMyRecognitions();
                    } else {
                        showError(data.message || '删除失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件选择
        function handleFileSelect(files) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (validateFile(file)) {
                    uploadedFiles.push({
                        id: Date.now() + i,
                        file: file,
                        name: file.name,
                        size: file.size
                    });
                }
            }
            renderFileList();
        }

        // 验证文件
        function validateFile(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['application/pdf', 'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'image/jpeg', 'image/jpg', 'image/png'];

            if (file.size > maxSize) {
                showError('文件大小不能超过10MB');
                return false;
            }

            if (!allowedTypes.includes(file.type)) {
                showError('只支持PDF、Word文档和图片格式');
                return false;
            }

            return true;
        }

        // 渲染文件列表
        function renderFileList() {
            const container = $('#fileList');
            container.empty();

            uploadedFiles.forEach(fileItem => {
                const fileHtml = `
                    <div class="file-item">
                        <i class="file-icon ace-icon fa fa-file"></i>
                        <span class="file-name">${fileItem.name}</span>
                        <i class="file-remove ace-icon fa fa-times" onclick="removeFile('${fileItem.id}')"></i>
                    </div>
                `;
                container.append(fileHtml);
            });
        }

        // 移除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id != fileId);
            renderFileList();
        }

        // 保存草稿
        function saveDraft() {
            if (!validateForm(false)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交学分认定申请
        function submitCreditRecognition() {
            if (!validateForm(true)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = false;

            const message = `确定要提交${getRecognitionTypeText(currentRecognitionType)}申请吗？\n\n提交后将进入审核流程，请确保信息准确无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitFormData(formData, '学分认定申请提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    submitFormData(formData, '学分认定申请提交成功');
                }
            }
        }

        // 收集表单数据
        function collectFormData() {
            const formData = {
                recognitionType: currentRecognitionType,
                projectName: $('#projectName').val(),
                obtainDate: $('#obtainDate').val(),
                appliedCredits: $('#appliedCredits').val(),
                description: $('#description').val(),
                reason: $('#reason').val(),
                contactPhone: $('#contactPhone').val(),
                email: $('#email').val(),
                attachments: uploadedFiles
            };

            // 添加类型特定字段
            switch(currentRecognitionType) {
                case 'course':
                    formData.institution = $('#institution').val();
                    break;
                case 'competition':
                    formData.competitionLevel = $('#competitionLevel').val();
                    formData.awardLevel = $('#awardLevel').val();
                    break;
                case 'certificate':
                    formData.certificateType = $('#certificateType').val();
                    break;
                case 'research':
                    formData.researchType = $('#researchType').val();
                    break;
            }

            return formData;
        }

        // 验证表单
        function validateForm(isSubmit) {
            if (!$('#projectName').val().trim()) {
                showError('请填写项目名称');
                return false;
            }

            if (!$('#obtainDate').val()) {
                showError('请选择获得时间');
                return false;
            }

            if (!$('#appliedCredits').val()) {
                showError('请填写申请学分');
                return false;
            }

            const credits = parseFloat($('#appliedCredits').val());
            if (credits <= 0 || credits > 10) {
                showError('申请学分必须在0-10之间');
                return false;
            }

            if (!$('#description').val().trim()) {
                showError('请填写详细描述');
                return false;
            }

            if (!$('#reason').val().trim()) {
                showError('请填写认定理由');
                return false;
            }

            if (!$('#contactPhone').val().trim()) {
                showError('请填写联系电话');
                return false;
            }

            if (isSubmit && uploadedFiles.length === 0) {
                showError('请上传证明材料');
                return false;
            }

            // 验证类型特定字段
            switch(currentRecognitionType) {
                case 'competition':
                    if (!$('#competitionLevel').val()) {
                        showError('请选择竞赛级别');
                        return false;
                    }
                    if (!$('#awardLevel').val()) {
                        showError('请选择获奖等级');
                        return false;
                    }
                    break;
                case 'certificate':
                    if (!$('#certificateType').val()) {
                        showError('请选择证书类型');
                        return false;
                    }
                    break;
                case 'research':
                    if (!$('#researchType').val()) {
                        showError('请选择成果类型');
                        return false;
                    }
                    break;
            }

            return true;
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            $.ajax({
                url: "/student/personalManagement/creditRecognition/submitRecognition",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        closeCreditForm();
                        loadCreditSummary();
                        loadMyRecognitions();
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示认定详情
        function showRecognitionDetail(recognitionId) {
            const recognition = myRecognitions.find(r => r.id === recognitionId);
            if (!recognition) return;

            let message = `学分认定详情\n\n`;
            message += `项目名称：${recognition.projectName}\n`;
            message += `认定类型：${getRecognitionTypeText(recognition.type)}\n`;
            message += `申请学分：${recognition.appliedCredits}学分\n`;
            message += `获得时间：${formatDate(recognition.obtainDate)}\n`;
            message += `申请时间：${formatDate(recognition.createTime)}\n`;
            message += `当前状态：${getRecognitionStatusText(recognition.status)}\n\n`;
            message += `详细描述：\n${recognition.description}\n\n`;
            message += `认定理由：\n${recognition.reason}\n`;

            // 添加类型特定信息
            switch(recognition.type) {
                case 'course':
                    if (recognition.institution) {
                        message += `\n开设机构：${recognition.institution}\n`;
                    }
                    break;
                case 'competition':
                    if (recognition.competitionLevel) {
                        message += `\n竞赛级别：${getCompetitionLevelText(recognition.competitionLevel)}\n`;
                    }
                    if (recognition.awardLevel) {
                        message += `获奖等级：${getAwardLevelText(recognition.awardLevel)}\n`;
                    }
                    break;
                case 'certificate':
                    if (recognition.certificateType) {
                        message += `\n证书类型：${getCertificateTypeText(recognition.certificateType)}\n`;
                    }
                    break;
                case 'research':
                    if (recognition.researchType) {
                        message += `\n成果类型：${getResearchTypeText(recognition.researchType)}\n`;
                    }
                    break;
            }

            if (recognition.reviewComment) {
                message += `\n审核意见：${recognition.reviewComment}\n`;
            }

            if (recognition.reviewTime) {
                message += `审核时间：${formatDate(recognition.reviewTime)}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 获取竞赛级别文本
        function getCompetitionLevelText(level) {
            switch(level) {
                case 'national': return '国家级';
                case 'provincial': return '省级';
                case 'municipal': return '市级';
                case 'school': return '校级';
                default: return level;
            }
        }

        // 获取获奖等级文本
        function getAwardLevelText(level) {
            switch(level) {
                case 'first': return '一等奖';
                case 'second': return '二等奖';
                case 'third': return '三等奖';
                case 'excellence': return '优秀奖';
                default: return level;
            }
        }

        // 获取证书类型文本
        function getCertificateTypeText(type) {
            switch(type) {
                case 'professional': return '职业资格证书';
                case 'skill': return '技能等级证书';
                case 'language': return '语言能力证书';
                case 'computer': return '计算机等级证书';
                default: return type;
            }
        }

        // 获取科研成果类型文本
        function getResearchTypeText(type) {
            switch(type) {
                case 'paper': return '学术论文';
                case 'patent': return '发明专利';
                case 'software': return '软件著作权';
                case 'project': return '科研项目';
                default: return type;
            }
        }

        // 更新认定数量
        function updateRecognitionsCount() {
            $('#recognitionsCount').text(myRecognitions.length);
        }

        // 刷新数据
        function refreshData() {
            loadCreditSummary();
            loadMyRecognitions();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.my-recognitions').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.my-recognitions').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.my-recognitions').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#creditForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#creditForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeCreditForm();
            }
        });

        $('#creditForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
