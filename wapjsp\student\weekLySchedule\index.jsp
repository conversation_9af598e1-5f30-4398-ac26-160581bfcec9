<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>本学期课程表</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 周课表页面样式 */
        .schedule-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .schedule-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            text-align: center;
            font-size: var(--font-size-h4);
            font-weight: 500;
        }
        
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--font-size-small);
        }
        
        .schedule-table th,
        .schedule-table td {
            border: 1px solid var(--divider-color);
            padding: 4px;
            text-align: center;
            vertical-align: top;
            position: relative;
        }
        
        .schedule-table th {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            font-weight: 500;
            font-size: var(--font-size-mini);
        }
        
        .time-header {
            width: 60px;
            background: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        
        .day-header {
            background: var(--info-color);
            color: white;
            font-weight: 500;
        }
        
        .time-cell {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            font-size: var(--font-size-mini);
            width: 60px;
        }
        
        .course-cell {
            height: 80px;
            min-height: 80px;
            position: relative;
            padding: 2px;
        }
        
        .course-item {
            background: var(--primary-color);
            color: white;
            border-radius: 4px;
            padding: 4px;
            margin: 1px;
            font-size: var(--font-size-mini);
            line-height: 1.2;
            cursor: pointer;
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            word-wrap: break-word;
        }
        
        .course-item:hover {
            opacity: 0.9;
            transform: scale(1.02);
        }
        
        .course-item:active {
            transform: scale(0.98);
        }
        
        .course-name {
            font-weight: 500;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .course-teacher {
            opacity: 0.9;
            font-size: calc(var(--font-size-mini) - 1px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .course-location {
            opacity: 0.8;
            font-size: calc(var(--font-size-mini) - 1px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .course-weeks {
            opacity: 0.7;
            font-size: calc(var(--font-size-mini) - 2px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 课程颜色主题 */
        .course-color-1 { background: #1890ff; }
        .course-color-2 { background: #52c41a; }
        .course-color-3 { background: #fa8c16; }
        .course-color-4 { background: #eb2f96; }
        .course-color-5 { background: #722ed1; }
        .course-color-6 { background: #13c2c2; }
        .course-color-7 { background: #f5222d; }
        .course-color-8 { background: #fa541c; }
        .course-color-9 { background: #2f54eb; }
        .course-color-10 { background: #389e0d; }
        
        .course-info-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .course-info-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            max-width: 400px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .course-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-info-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .course-info-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .course-info-details {
            display: grid;
            gap: var(--spacing-sm);
        }
        
        .course-info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: var(--padding-sm) 0;
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-info-item:last-child {
            border-bottom: none;
        }
        
        .course-info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            min-width: 80px;
        }
        
        .course-info-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
            flex: 1;
            margin-left: var(--margin-sm);
        }
        
        .course-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-course-action {
            flex: 1;
            padding: var(--padding-sm);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-calendar {
            background: var(--info-color);
            color: white;
        }
        
        .btn-outline {
            background: var(--success-color);
            color: white;
        }
        
        .summary-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .summary-title i {
            color: var(--primary-color);
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: var(--padding-md);
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            width: 100%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: var(--font-size-h4);
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: var(--padding-md);
        }

        .modal-footer {
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }

        .radio-group-vertical {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .radio-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: var(--font-size-base);
            color: var(--text-primary);
            padding: var(--padding-sm);
            border-radius: 6px;
            transition: background-color var(--transition-base);
        }

        .radio-label:hover {
            background: var(--bg-tertiary);
        }

        .radio-label input[type="radio"] {
            margin: 0;
        }

        @media (max-width: 480px) {
            .schedule-table {
                font-size: calc(var(--font-size-mini) - 1px);
            }
            
            .course-cell {
                height: 60px;
                min-height: 60px;
            }
            
            .course-item {
                padding: 2px;
                font-size: calc(var(--font-size-mini) - 1px);
            }
            
            .time-cell {
                width: 50px;
            }
            
            .time-header {
                width: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">本学期课程表</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 课程统计 -->
        <div class="summary-container" id="summaryContainer" style="display: none;">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                课程统计
            </div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-value" id="totalCourses">0</div>
                    <div class="stat-label">总课程</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalCredits">0</div>
                    <div class="stat-label">总学分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalHours">0</div>
                    <div class="stat-label">总学时</div>
                </div>
            </div>
        </div>
        
        <!-- 课程表 -->
        <div class="schedule-container">
            <div class="schedule-header">
                <span id="scheduleTitle">本学期课程表</span>
            </div>
            
            <table class="schedule-table" id="scheduleTable">
                <thead>
                    <tr>
                        <th class="time-header">时间</th>
                        <th class="day-header">周一</th>
                        <th class="day-header">周二</th>
                        <th class="day-header">周三</th>
                        <th class="day-header">周四</th>
                        <th class="day-header">周五</th>
                        <th class="day-header">周六</th>
                        <th class="day-header">周日</th>
                    </tr>
                </thead>
                <tbody id="scheduleBody">
                    <!-- 动态生成课程表内容 -->
                </tbody>
            </table>
        </div>
        
        <!-- 课程详情面板 -->
        <div class="course-info-panel" id="courseInfoPanel">
            <div class="course-info-content">
                <div class="course-info-header">
                    <div class="course-info-title" id="courseInfoTitle">课程详情</div>
                    <button class="course-info-close" onclick="closeCourseInfo();">×</button>
                </div>
                <div class="course-info-details" id="courseInfoDetails">
                    <!-- 动态加载课程详情 -->
                </div>
                <div class="course-actions" id="courseActions">
                    <!-- 动态加载操作按钮 -->
                </div>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-calendar"></i>
            <div>暂无课程安排</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let scheduleData = [];
        let courseColors = {};
        let colorIndex = 0;
        let currentSemester = '${xqh}';
        let schoolCode = '${param_value}';

        $(function() {
            initPage();
            loadScheduleData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            initScheduleTable();
        }

        // 初始化课程表结构
        function initScheduleTable() {
            const tbody = $('#scheduleBody');
            tbody.empty();
            
            // 生成12节课的时间表
            for (let i = 1; i <= 12; i++) {
                const timeSlot = getTimeSlot(i);
                const row = `
                    <tr>
                        <td class="time-cell">${i}<br><small>${timeSlot}</small></td>
                        <td class="course-cell" id="cell_1_${i}"></td>
                        <td class="course-cell" id="cell_2_${i}"></td>
                        <td class="course-cell" id="cell_3_${i}"></td>
                        <td class="course-cell" id="cell_4_${i}"></td>
                        <td class="course-cell" id="cell_5_${i}"></td>
                        <td class="course-cell" id="cell_6_${i}"></td>
                        <td class="course-cell" id="cell_7_${i}"></td>
                    </tr>
                `;
                tbody.append(row);
            }
        }

        // 获取时间段
        function getTimeSlot(period) {
            const timeSlots = {
                1: '08:00', 2: '08:50', 3: '09:50', 4: '10:40',
                5: '11:30', 6: '14:00', 7: '14:50', 8: '15:40',
                9: '16:30', 10: '17:20', 11: '19:00', 12: '19:50'
            };
            return timeSlots[period] || '';
        }

        // 加载课程表数据
        function loadScheduleData() {
            showLoading(true);

            const url = "/student/courseSelect/thisSemesterCurriculum/ajaxStudentSchedule/weekLySchedule/callback/1";

            $.get(url, function(courseInfo) {
                try {
                    const infoArr = JSON.parse(JSON.stringify(courseInfo));
                    const hasTime = infoArr["xkxx"];
                    const dateList = infoArr["dateList"];
                    const allUnits = infoArr["allUnits"];

                    if (hasTime && hasTime.length > 0) {
                        scheduleData = hasTime[0];
                        renderSchedule();
                        updateSummary(dateList, allUnits);
                        showEmptyState(false);
                    } else {
                        showEmptyState(true);
                    }
                } catch (error) {
                    console.error('解析课程数据失败:', error);
                    showError('加载课程数据失败');
                    showEmptyState(true);
                }
            }).fail(function() {
                showError('网络请求失败');
                showEmptyState(true);
            }).always(function() {
                showLoading(false);
            });
        }

        // 渲染课程表
        function renderSchedule() {
            // 清空所有课程格子
            $('.course-cell').empty();

            // 重置颜色索引
            colorIndex = 0;
            courseColors = {};

            // 遍历课程数据
            Object.keys(scheduleData).forEach(function(key) {
                const course = scheduleData[key];
                if (course.timeAndPlaceList && course.timeAndPlaceList.length > 0) {
                    course.timeAndPlaceList.forEach(function(timePlace) {
                        renderCourseItem(course, timePlace);
                    });
                }
            });
        }

        // 渲染单个课程项
        function renderCourseItem(course, timePlace) {
            const day = timePlace.classDay;
            const startPeriod = timePlace.classSessions;
            const duration = timePlace.continuingSession;

            if (!day || !startPeriod) return;

            // 获取课程颜色
            const courseKey = course.id.coureNumber + "_" + course.id.coureSequenceNumber;
            if (!courseColors[courseKey]) {
                courseColors[courseKey] = (colorIndex % 10) + 1;
                colorIndex++;
            }

            // 创建课程项HTML
            const courseHtml = createCourseItemHtml(course, timePlace, courseColors[courseKey]);

            // 放置到对应的格子中
            const cellId = `cell_${day}_${startPeriod}`;
            const cell = $(`#${cellId}`);

            if (cell.length > 0) {
                // 如果是多节连续课程，需要合并单元格
                if (duration > 1) {
                    // 设置课程项高度
                    const courseItem = $(courseHtml);
                    courseItem.css('height', (80 * duration - 4) + 'px');
                    cell.append(courseItem);

                    // 隐藏后续的单元格
                    for (let i = 1; i < duration; i++) {
                        $(`#cell_${day}_${startPeriod + i}`).hide();
                    }
                } else {
                    cell.append(courseHtml);
                }
            }
        }

        // 创建课程项HTML
        function createCourseItemHtml(course, timePlace, colorClass) {
            const courseName = course.courseName || '未知课程';
            const teacher = course.attendClassTeacher || '未知教师';
            const location = (timePlace.campusName || '') +
                           (timePlace.teachingBuildingName || '') +
                           (timePlace.classroomName || '');
            const weeks = timePlace.weekDescription || '';

            return `
                <div class="course-item course-color-${colorClass}"
                     onclick="showCourseInfo('${course.id.coureNumber}', '${course.id.coureSequenceNumber}');">
                    <div class="course-name">${courseName}</div>
                    <div class="course-teacher">${teacher}</div>
                    <div class="course-location">${location}</div>
                    <div class="course-weeks">${weeks}</div>
                </div>
            `;
        }

        // 显示课程详情
        function showCourseInfo(courseNumber, sequenceNumber) {
            const course = findCourse(courseNumber, sequenceNumber);
            if (!course) return;

            $('#courseInfoTitle').text(course.courseName || '课程详情');

            const detailsHtml = `
                <div class="course-info-item">
                    <span class="course-info-label">课程号</span>
                    <span class="course-info-value">${course.id.coureNumber || '-'}</span>
                </div>
                <div class="course-info-item">
                    <span class="course-info-label">课序号</span>
                    <span class="course-info-value">${course.id.coureSequenceNumber || '-'}</span>
                </div>
                <div class="course-info-item">
                    <span class="course-info-label">学分</span>
                    <span class="course-info-value">${course.unit || '-'}</span>
                </div>
                <div class="course-info-item">
                    <span class="course-info-label">课程属性</span>
                    <span class="course-info-value">${course.coursePropertiesName || '-'}</span>
                </div>
                <div class="course-info-item">
                    <span class="course-info-label">课程类别</span>
                    <span class="course-info-value">${course.courseCategoryName || '-'}</span>
                </div>
                <div class="course-info-item">
                    <span class="course-info-label">考试类型</span>
                    <span class="course-info-value">${course.examTypeName || '-'}</span>
                </div>
                <div class="course-info-item">
                    <span class="course-info-label">任课教师</span>
                    <span class="course-info-value">${course.attendClassTeacher || '-'}</span>
                </div>
                <div class="course-info-item">
                    <span class="course-info-label">修读方式</span>
                    <span class="course-info-value">${course.studyModeName || '-'}</span>
                </div>
                <div class="course-info-item">
                    <span class="course-info-label">选课状态</span>
                    <span class="course-info-value">${course.selectCourseStatusName || '-'}</span>
                </div>
            `;

            $('#courseInfoDetails').html(detailsHtml);

            // 生成操作按钮
            const actionsHtml = generateCourseActions(course);
            $('#courseActions').html(actionsHtml);

            $('#courseInfoPanel').show();
        }

        // 生成课程操作按钮
        function generateCourseActions(course) {
            let actionsHtml = '';

            // 根据学校代码决定是否显示教学日历和大纲按钮
            if (schoolCode !== "100010") {
                // 教学日历按钮
                if (course.rlFlag && course.rlFlag !== "") {
                    actionsHtml += `
                        <button class="btn-course-action btn-calendar"
                                onclick="viewCalendar('${course.id.executiveEducationPlanNumber}', '${course.id.coureNumber}', '${course.id.coureSequenceNumber}', '${course.rlFlag}');">
                            <i class="ace-icon fa fa-calendar"></i>
                            <span>教学日历</span>
                        </button>
                    `;
                }

                // 教学大纲按钮
                if (course.dgFlag && course.dgFlag !== "") {
                    actionsHtml += `
                        <button class="btn-course-action btn-outline"
                                onclick="viewOutline('${course.id.executiveEducationPlanNumber}', '${course.id.coureNumber}', '${course.id.coureSequenceNumber}', '${course.dgFlag}');">
                            <i class="ace-icon fa fa-tasks"></i>
                            <span>教学大纲</span>
                        </button>
                    `;
                }
            } else {
                // 100010学校只显示下载大纲按钮
                actionsHtml += `
                    <button class="btn-course-action btn-outline"
                            onclick="downloadOutline('${course.id.coureNumber}');">
                        <i class="ace-icon fa fa-download"></i>
                        <span>下载大纲</span>
                    </button>
                `;
            }

            return actionsHtml;
        }

        // 查找课程
        function findCourse(courseNumber, sequenceNumber) {
            return Object.values(scheduleData).find(course =>
                course.id.coureNumber === courseNumber &&
                course.id.coureSequenceNumber === sequenceNumber
            );
        }

        // 更新课程统计
        function updateSummary(dateList, allUnits) {
            let totalCourses = 0;
            let totalCredits = parseFloat(allUnits) || 0;
            let totalHours = 0;

            if (dateList && dateList.length > 0) {
                dateList.forEach(function(date) {
                    if (date.selectCourseList) {
                        totalCourses += date.selectCourseList.length;
                    }
                });
            }

            // 计算总学时（简单估算：学分 * 16）
            totalHours = Math.round(totalCredits * 16);

            $('#totalCourses').text(totalCourses);
            $('#totalCredits').text(totalCredits.toFixed(1));
            $('#totalHours').text(totalHours);
            $('#summaryContainer').show();
        }

        // 查看教学日历
        function viewCalendar(planNumber, courseNumber, sequenceNumber, rlFlag) {
            const teachers = rlFlag.split("|");
            if (teachers.length > 1) {
                showTeacherSelection(planNumber, courseNumber, sequenceNumber, teachers, 'rl');
            } else {
                const info = teachers[0].split(",");
                if (info[1] && info[1].indexOf("（无）") !== -1) {
                    showError("该教师还没有上传日历！");
                } else {
                    openDocument(planNumber, courseNumber, sequenceNumber, info[0], 'rl');
                }
            }
        }

        // 查看教学大纲
        function viewOutline(planNumber, courseNumber, sequenceNumber, dgFlag) {
            const teachers = dgFlag.split("|");
            if (teachers.length > 1) {
                showTeacherSelection(planNumber, courseNumber, sequenceNumber, teachers, 'dg');
            } else {
                const info = teachers[0].split(",");
                if (info[1] && info[1].indexOf("（无）") !== -1) {
                    showError("该教师还没有上传大纲！");
                } else {
                    openDocument(planNumber, courseNumber, sequenceNumber, info[0], 'dg');
                }
            }
        }

        // 显示教师选择对话框
        function showTeacherSelection(planNumber, courseNumber, sequenceNumber, teachers, type) {
            let optionsHtml = '';
            teachers.forEach(function(teacher, index) {
                const info = teacher.split(",");
                if (info.length >= 2) {
                    optionsHtml += `
                        <label class="radio-label">
                            <input type="radio" name="teacherSelection" value="${info[0]}" ${index === 0 ? 'checked' : ''}>
                            <span>${info[1]}</span>
                        </label>
                    `;
                }
            });

            const title = type === 'rl' ? '选择教师查看日历' : '选择教师查看大纲';
            const dialogHtml = `
                <div class="modal-overlay" id="teacherDialog" onclick="closeTeacherDialog();">
                    <div class="modal-content" onclick="event.stopPropagation();">
                        <div class="modal-header">
                            <h3>${title}</h3>
                            <button class="modal-close" onclick="closeTeacherDialog();">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="radio-group-vertical">
                                ${optionsHtml}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn-mobile btn-secondary" onclick="closeTeacherDialog();">取消</button>
                            <button class="btn-mobile btn-primary" onclick="confirmTeacherSelection('${planNumber}', '${courseNumber}', '${sequenceNumber}', '${type}');">确定</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(dialogHtml);
        }

        // 关闭教师选择对话框
        function closeTeacherDialog() {
            $('#teacherDialog').remove();
        }

        // 确认教师选择
        function confirmTeacherSelection(planNumber, courseNumber, sequenceNumber, type) {
            const selectedTeacher = $('input[name="teacherSelection"]:checked').val();
            const selectedTeacherName = $('input[name="teacherSelection"]:checked + span').text();

            if (!selectedTeacher) {
                showError('请选择教师！');
                return;
            }

            if (selectedTeacherName.indexOf("（无）") !== -1) {
                const message = type === 'rl' ? "该教师还没有上传日历！" : "该教师还没有上传大纲！";
                showError(message);
                return;
            }

            closeTeacherDialog();
            openDocument(planNumber, courseNumber, sequenceNumber, selectedTeacher, type);
        }

        // 打开文档
        function openDocument(planNumber, courseNumber, sequenceNumber, teacherId, type) {
            const form = $('<form>');
            form.attr('style', 'display:none');
            form.attr('target', '_blank');
            form.attr('method', 'post');
            form.attr('action', `/student/courseSelect/thisSemesterCurriculum/dgrl/${planNumber}/${courseNumber}/${sequenceNumber}/${teacherId}/${type}`);

            $('body').append(form);
            form.submit();
            form.remove();
        }

        // 下载大纲（100010学校专用）
        function downloadOutline(courseNumber) {
            if (parent && parent.addTab) {
                parent.addTab('课程大纲', '/student/courseSelect/thisSemesterCurriculum/downloadOutline?courseNumber=' + courseNumber);
            } else {
                window.location.href = '/student/courseSelect/thisSemesterCurriculum/downloadOutline?courseNumber=' + courseNumber;
            }
        }

        // 关闭课程详情
        function closeCourseInfo() {
            $('#courseInfoPanel').hide();
        }

        // 刷新数据
        function refreshData() {
            loadScheduleData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.schedule-container').hide();
                $('#summaryContainer').hide();
            } else {
                $('#emptyState').hide();
                $('.schedule-container').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击面板背景关闭
        $('#courseInfoPanel').click(function(e) {
            if (e.target === this) {
                closeCourseInfo();
            }
        });
    </script>
</body>
</html>
