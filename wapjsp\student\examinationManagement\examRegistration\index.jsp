<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>考试报名</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 考试报名页面样式 */
        .registration-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .status-indicator {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .status-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .status-number {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .status-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .exam-categories {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .category-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-item:active {
            background: var(--bg-color-active);
        }
        
        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .category-icon.cet {
            background: var(--success-color);
        }
        
        .category-icon.computer {
            background: var(--info-color);
        }
        
        .category-icon.professional {
            background: var(--warning-color);
        }
        
        .category-icon.other {
            background: var(--error-color);
        }
        
        .category-content {
            flex: 1;
        }
        
        .category-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .category-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .category-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-open {
            background: var(--success-color);
            color: white;
        }
        
        .status-closed {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-coming {
            background: var(--warning-color);
            color: white;
        }
        
        .exam-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .exam-item.registered {
            border-left-color: var(--success-color);
        }
        
        .exam-item.closed {
            border-left-color: var(--text-disabled);
            opacity: 0.7;
        }
        
        .exam-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .exam-fee {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-small);
            font-weight: 500;
            background: var(--primary-color);
            color: white;
        }
        
        .exam-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-schedule {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
            font-size: var(--font-size-small);
        }
        
        .schedule-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .schedule-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .exam-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-register {
            background: var(--success-color);
            color: white;
        }
        
        .btn-registered {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .btn-cancel {
            background: var(--error-color);
            color: white;
        }
        
        .btn-detail {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .registration-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .form-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
            position: relative;
            width: 100%;
        }
        
        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .form-close {
            font-size: var(--font-size-h4);
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .form-checkbox input {
            margin-right: var(--margin-xs);
        }
        
        .form-checkbox label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel-form {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">考试报名</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="registration-header">
            <div class="header-title">考试报名</div>
            <div class="header-subtitle">选择考试项目进行报名</div>
        </div>

        <!-- 报名状态 -->
        <div class="status-indicator">
            <div class="status-title">报名统计</div>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-number" id="availableExams">0</div>
                    <div class="status-label">可报名考试</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="registeredExams">0</div>
                    <div class="status-label">已报名考试</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="totalFee">¥0</div>
                    <div class="status-label">报名费用</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="upcomingExams">0</div>
                    <div class="status-label">即将开考</div>
                </div>
            </div>
        </div>

        <!-- 考试分类 -->
        <div class="exam-categories">
            <div class="category-item" onclick="showExamCategory('cet')">
                <div class="category-icon cet">
                    <i class="ace-icon fa fa-language"></i>
                </div>
                <div class="category-content">
                    <div class="category-title">英语等级考试</div>
                    <div class="category-desc">四六级、专四专八等英语考试</div>
                </div>
                <div class="category-status status-open">开放</div>
            </div>

            <div class="category-item" onclick="showExamCategory('computer')">
                <div class="category-icon computer">
                    <i class="ace-icon fa fa-desktop"></i>
                </div>
                <div class="category-content">
                    <div class="category-title">计算机等级考试</div>
                    <div class="category-desc">全国计算机等级考试</div>
                </div>
                <div class="category-status status-open">开放</div>
            </div>

            <div class="category-item" onclick="showExamCategory('professional')">
                <div class="category-icon professional">
                    <i class="ace-icon fa fa-certificate"></i>
                </div>
                <div class="category-content">
                    <div class="category-title">专业资格考试</div>
                    <div class="category-desc">各类专业技能认证考试</div>
                </div>
                <div class="category-status status-coming">即将开放</div>
            </div>

            <div class="category-item" onclick="showExamCategory('other')">
                <div class="category-icon other">
                    <i class="ace-icon fa fa-file-text"></i>
                </div>
                <div class="category-content">
                    <div class="category-title">其他考试</div>
                    <div class="category-desc">普通话、教师资格证等</div>
                </div>
                <div class="category-status status-open">开放</div>
            </div>
        </div>

        <!-- 考试列表 -->
        <div class="container-mobile">
            <div id="examList">
                <!-- 考试列表将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-file-text-o"></i>
                <div id="emptyMessage">请选择考试分类</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <!-- 报名表单 -->
    <div class="registration-form" id="registrationForm">
        <div class="form-content">
            <div class="form-header">
                <div class="form-title" id="formTitle">考试报名</div>
                <div class="form-close" onclick="closeRegistrationForm();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>

            <div class="form-group">
                <div class="form-label">考试名称</div>
                <input type="text" class="form-input" id="examName" readonly>
            </div>

            <div class="form-group">
                <div class="form-label">考试时间</div>
                <input type="text" class="form-input" id="examTime" readonly>
            </div>

            <div class="form-group">
                <div class="form-label">报名费用</div>
                <input type="text" class="form-input" id="examFee" readonly>
            </div>

            <div class="form-group">
                <div class="form-label">联系电话</div>
                <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
            </div>

            <div class="form-group">
                <div class="form-label">身份证号</div>
                <input type="text" class="form-input" id="idNumber" placeholder="请输入身份证号">
            </div>

            <div class="form-group">
                <div class="form-label">特殊需求</div>
                <textarea class="form-input" id="specialNeeds" rows="3" placeholder="如有特殊需求请说明（可选）"></textarea>
            </div>

            <div class="form-checkbox">
                <input type="checkbox" id="agreeTerms">
                <label for="agreeTerms">我已阅读并同意考试报名相关条款和规定</label>
            </div>

            <div class="form-checkbox">
                <input type="checkbox" id="confirmInfo">
                <label for="confirmInfo">我确认以上信息真实有效，如有虚假承担相应责任</label>
            </div>

            <div class="form-actions">
                <button class="btn-mobile btn-cancel-form flex-1" onclick="closeRegistrationForm();">取消</button>
                <button class="btn-mobile btn-submit flex-1" onclick="submitRegistration();">确认报名</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allExams = [];
        let filteredExams = [];
        let currentCategory = '';
        let currentExam = null;
        let registrationStats = {};

        $(function() {
            initPage();
            loadRegistrationStats();
            showExamCategory('cet'); // 默认显示英语等级考试
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载报名统计
        function loadRegistrationStats() {
            $.ajax({
                url: "/student/examinationManagement/examRegistration/getRegistrationStats",
                type: "post",
                dataType: "json",
                success: function(data) {
                    registrationStats = data || {};
                    updateRegistrationStats();
                },
                error: function() {
                    console.log('加载报名统计失败');
                }
            });
        }

        // 更新报名统计
        function updateRegistrationStats() {
            $('#availableExams').text(registrationStats.availableExams || 0);
            $('#registeredExams').text(registrationStats.registeredExams || 0);
            $('#totalFee').text('¥' + (registrationStats.totalFee || 0));
            $('#upcomingExams').text(registrationStats.upcomingExams || 0);
        }

        // 显示考试分类
        function showExamCategory(category) {
            currentCategory = category;
            loadExamsByCategory(category);
        }

        // 根据分类加载考试
        function loadExamsByCategory(category) {
            showLoading(true);

            $.ajax({
                url: "/student/examinationManagement/examRegistration/getExamsByCategory",
                type: "post",
                data: { category: category },
                dataType: "json",
                success: function(data) {
                    allExams = data.exams || [];
                    renderExamList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载考试列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染考试列表
        function renderExamList() {
            const container = $('#examList');
            container.empty();

            if (allExams.length === 0) {
                showEmptyState('该分类暂无可报名考试');
                return;
            } else {
                hideEmptyState();
            }

            allExams.forEach(exam => {
                const examHtml = createExamItem(exam);
                container.append(examHtml);
            });
        }

        // 创建考试项
        function createExamItem(exam) {
            const statusClass = getExamStatusClass(exam.status);
            const isRegistered = exam.isRegistered;
            const isClosed = exam.status === 'closed';

            let actionButtons = '';
            if (isClosed) {
                actionButtons = '<button class="btn-mobile btn-registered flex-1">报名已结束</button>';
            } else if (isRegistered) {
                actionButtons = `
                    <button class="btn-mobile btn-cancel flex-1" onclick="cancelRegistration('${exam.id}')">取消报名</button>
                    <button class="btn-mobile btn-detail flex-1" onclick="showExamDetail('${exam.id}')">查看详情</button>
                `;
            } else {
                actionButtons = `
                    <button class="btn-mobile btn-detail flex-1" onclick="showExamDetail('${exam.id}')">查看详情</button>
                    <button class="btn-mobile btn-register flex-1" onclick="showRegistrationForm('${exam.id}')">立即报名</button>
                `;
            }

            return `
                <div class="exam-item ${statusClass}">
                    <div class="exam-header">
                        <div class="exam-name">${exam.name}</div>
                        <div class="exam-fee">¥${exam.fee}</div>
                    </div>
                    <div class="exam-info">
                        <div class="info-item">
                            <span>考试时间:</span>
                            <span>${formatDate(exam.examDate)}</span>
                        </div>
                        <div class="info-item">
                            <span>报名截止:</span>
                            <span>${formatDate(exam.registrationDeadline)}</span>
                        </div>
                        <div class="info-item">
                            <span>考试地点:</span>
                            <span>${exam.location || '待定'}</span>
                        </div>
                        <div class="info-item">
                            <span>报名人数:</span>
                            <span>${exam.registeredCount}/${exam.maxCapacity}</span>
                        </div>
                    </div>
                    <div class="exam-schedule">
                        <div class="schedule-title">重要时间节点</div>
                        <div class="schedule-item">
                            <span>报名开始:</span>
                            <span>${formatDate(exam.registrationStart)}</span>
                        </div>
                        <div class="schedule-item">
                            <span>报名结束:</span>
                            <span>${formatDate(exam.registrationEnd)}</span>
                        </div>
                        <div class="schedule-item">
                            <span>准考证打印:</span>
                            <span>${formatDate(exam.admissionTicketDate)}</span>
                        </div>
                    </div>
                    <div class="exam-actions">
                        ${actionButtons}
                    </div>
                </div>
            `;
        }

        // 获取考试状态样式类
        function getExamStatusClass(status) {
            switch(status) {
                case 'registered': return 'registered';
                case 'closed': return 'closed';
                default: return '';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString().slice(0, 5);
        }

        // 显示报名表单
        function showRegistrationForm(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            currentExam = exam;

            // 填充表单数据
            $('#formTitle').text(exam.name + ' - 报名');
            $('#examName').val(exam.name);
            $('#examTime').val(formatDate(exam.examDate));
            $('#examFee').val('¥' + exam.fee);

            // 清空用户输入
            $('#contactPhone').val('');
            $('#idNumber').val('');
            $('#specialNeeds').val('');
            $('#agreeTerms').prop('checked', false);
            $('#confirmInfo').prop('checked', false);

            // 显示表单
            $('#registrationForm').show();
        }

        // 关闭报名表单
        function closeRegistrationForm() {
            $('#registrationForm').hide();
            currentExam = null;
        }

        // 提交报名
        function submitRegistration() {
            if (!currentExam) return;

            // 验证表单
            if (!validateRegistrationForm()) {
                return;
            }

            const formData = {
                examId: currentExam.id,
                contactPhone: $('#contactPhone').val(),
                idNumber: $('#idNumber').val(),
                specialNeeds: $('#specialNeeds').val()
            };

            $.ajax({
                url: "/student/examinationManagement/examRegistration/submitRegistration",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('报名成功！请按时参加考试。');
                        closeRegistrationForm();
                        loadRegistrationStats();
                        loadExamsByCategory(currentCategory);
                    } else {
                        showError(data.message || '报名失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 验证报名表单
        function validateRegistrationForm() {
            const contactPhone = $('#contactPhone').val().trim();
            const idNumber = $('#idNumber').val().trim();
            const agreeTerms = $('#agreeTerms').prop('checked');
            const confirmInfo = $('#confirmInfo').prop('checked');

            if (!contactPhone) {
                showError('请输入联系电话');
                return false;
            }

            if (!/^1[3-9]\d{9}$/.test(contactPhone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            if (!idNumber) {
                showError('请输入身份证号');
                return false;
            }

            if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(idNumber)) {
                showError('请输入正确的身份证号');
                return false;
            }

            if (!agreeTerms) {
                showError('请阅读并同意考试报名相关条款');
                return false;
            }

            if (!confirmInfo) {
                showError('请确认信息真实有效');
                return false;
            }

            return true;
        }

        // 取消报名
        function cancelRegistration(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            const message = `确定要取消"${exam.name}"的报名吗？\n\n取消后报名费将原路退回，请注意查收。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doCancelRegistration(examId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doCancelRegistration(examId);
                }
            }
        }

        // 执行取消报名
        function doCancelRegistration(examId) {
            $.ajax({
                url: "/student/examinationManagement/examRegistration/cancelRegistration",
                type: "post",
                data: { examId: examId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('取消报名成功，报名费将在3-5个工作日内退回');
                        loadRegistrationStats();
                        loadExamsByCategory(currentCategory);
                    } else {
                        showError(data.message || '取消报名失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示考试详情
        function showExamDetail(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            let message = `考试详情\n\n`;
            message += `考试名称：${exam.name}\n`;
            message += `考试时间：${formatDate(exam.examDate)}\n`;
            message += `考试地点：${exam.location || '待定'}\n`;
            message += `报名费用：¥${exam.fee}\n`;
            message += `报名截止：${formatDate(exam.registrationDeadline)}\n`;
            message += `准考证打印：${formatDate(exam.admissionTicketDate)}\n`;
            message += `报名人数：${exam.registeredCount}/${exam.maxCapacity}\n`;

            if (exam.description) {
                message += `\n考试说明：\n${exam.description}\n`;
            }

            if (exam.requirements) {
                message += `\n报名要求：\n${exam.requirements}\n`;
            }

            if (exam.materials) {
                message += `\n考试用品：\n${exam.materials}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 刷新数据
        function refreshData() {
            loadRegistrationStats();
            if (currentCategory) {
                loadExamsByCategory(currentCategory);
            }
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#examList').hide();
            } else {
                $('#loadingState').hide();
                $('#examList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击表单背景关闭
        $('#registrationForm').click(function(e) {
            if (e.target === this) {
                closeRegistrationForm();
            }
        });
    </script>
</body>
</html>
