<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>承诺与使用授权书</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 论文阅读确认页面样式 */
        .commitment-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .commitment-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .commitment-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .document-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .document-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .document-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .document-title i {
            color: var(--info-color);
        }
        
        .document-content {
            padding: var(--padding-md);
            text-align: center;
        }
        
        .document-image {
            width: 100%;
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .status-confirmed {
            background: var(--success-light);
            color: var(--success-dark);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--success-color);
            font-size: var(--font-size-base);
            line-height: 1.4;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-confirmed i {
            color: var(--success-color);
            font-size: var(--font-size-lg);
        }
        
        .confirm-button {
            background: var(--info-color);
            color: white;
            border: none;
            padding: var(--padding-md) var(--padding-xl);
            border-radius: 8px;
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
            transition: all var(--transition-base);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
        
        .confirm-button:hover {
            background: var(--info-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
        }
        
        .confirm-button:active {
            transform: translateY(0);
        }
        
        .confirm-button i {
            font-size: var(--font-size-lg);
        }
        
        .info-notice {
            background: var(--info-light);
            color: var(--info-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--info-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .info-notice i {
            color: var(--info-color);
            margin-right: 8px;
        }
        
        .actions-section {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-back {
            background: var(--text-disabled);
            color: white;
            width: 100%;
        }
        
        @media (max-width: 480px) {
            .document-image {
                max-height: 60vh;
                object-fit: contain;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">承诺与使用授权书</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 承诺书头部 -->
        <div class="commitment-header">
            <div class="commitment-title">承诺与使用授权书</div>
            <div class="commitment-desc">请仔细阅读并确认</div>
        </div>
        
        <!-- 提示信息 -->
        <div class="info-notice">
            <i class="ace-icon fa fa-info-circle"></i>
            请仔细阅读以下承诺与使用授权书内容，确认无误后点击确认按钮。
        </div>
        
        <!-- 文档内容 -->
        <div class="document-section">
            <div class="document-header">
                <div class="document-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    承诺与使用授权书
                </div>
            </div>
            
            <div class="document-content">
                <img id="showpdfmark" src="/cnsyqr.png" alt="承诺与使用授权书" class="document-image">
            </div>
        </div>
        
        <!-- 状态和操作 -->
        <div class="status-section">
            <c:choose>
                <c:when test="${qrzt == '1'}">
                    <div class="status-confirmed">
                        <i class="ace-icon fa fa-check-circle"></i>
                        <div>您在${qrrq}阅读并确认了承诺与使用授权书！</div>
                    </div>
                </c:when>
                <c:otherwise>
                    <button class="confirm-button" onclick="doSaveYdqr();">
                        <i class="ace-icon fa fa-check"></i>
                        <span>确认</span>
                    </button>
                </c:otherwise>
            </c:choose>
        </div>
        
        <!-- 底部留白，避免被固定按钮遮挡 -->
        <div style="height: 80px;"></div>
        
        <!-- 固定底部操作按钮 -->
        <div class="actions-section">
            <button type="button" class="btn-mobile btn-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
                <span>返回</span>
            </button>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>处理中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 阅读确认
        function doSaveYdqr() {
            if (confirm("我已阅读并确认\"承诺与使用授权书\"？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/lwydqr/doSaveYdqr",
                    type: "post",
                    data: "tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);
                        
                        if (data.result.indexOf("/") != -1) {
                            showError("页面已过期，请刷新页面！");
                        } else if (data.result === "ok") {
                            showSuccess("承诺与使用授权书确认成功！", function() {
                                location.reload();
                            });
                        } else {
                            showError(data.msg);
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 返回
        function goBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                history.back();
            }
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
