<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>课程重修申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 课程重修申请页面样式 */
        .retake-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .stats-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .stat-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .filter-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .filter-row {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .filter-row:last-child {
            margin-bottom: 0;
        }
        
        .filter-item {
            flex: 1;
        }
        
        .filter-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .filter-select {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .course-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--error-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .course-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .course-item.retaking {
            border-left-color: var(--warning-color);
        }
        
        .course-item.completed {
            border-left-color: var(--success-color);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .course-score {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-small);
            font-weight: 500;
            color: white;
            background: var(--error-color);
        }
        
        .course-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .retake-options {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
        }
        
        .options-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .option-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .option-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 8px;
            background: var(--bg-primary);
            border-radius: 4px;
            font-size: var(--font-size-small);
        }
        
        .option-name {
            color: var(--text-secondary);
        }
        
        .option-status {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .option-status.available {
            color: var(--success-color);
        }
        
        .option-status.full {
            color: var(--error-color);
        }
        
        .option-status.closed {
            color: var(--text-disabled);
        }
        
        .course-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-apply {
            background: var(--success-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .my-applications {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .applications-title {
            display: flex;
            align-items: center;
        }
        
        .applications-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .applications-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .application-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .application-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .application-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .application-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .retake-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .retake-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-radio-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .form-radio {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .form-radio:hover {
            background: var(--bg-color-active);
        }
        
        .form-radio input {
            margin-right: var(--margin-sm);
        }
        
        .radio-content {
            flex: 1;
        }
        
        .radio-title {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .radio-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 2px;
        }
        
        .radio-status {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">课程重修申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="retake-header">
            <div class="header-title">课程重修申请</div>
            <div class="header-subtitle">申请重修不及格或需要提高成绩的课程</div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
            <div class="stats-title">重修统计</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="failedCourses">0</div>
                    <div class="stat-label">不及格课程</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="retakingCourses">0</div>
                    <div class="stat-label">重修中课程</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completedRetakes">0</div>
                    <div class="stat-label">重修完成</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="pendingApplications">0</div>
                    <div class="stat-label">待审核申请</div>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="ace-icon fa fa-filter"></i>
                <span>筛选条件</span>
            </div>

            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">学年学期</div>
                    <select class="filter-select" id="termSelect">
                        <option value="">全部学期</option>
                    </select>
                </div>
                <div class="filter-item">
                    <div class="filter-label">课程类型</div>
                    <select class="filter-select" id="courseTypeSelect">
                        <option value="">全部类型</option>
                        <option value="required">必修课</option>
                        <option value="elective">选修课</option>
                        <option value="practical">实践课</option>
                    </select>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">重修状态</div>
                    <select class="filter-select" id="statusSelect">
                        <option value="">全部状态</option>
                        <option value="failed">不及格</option>
                        <option value="retaking">重修中</option>
                        <option value="completed">已完成</option>
                    </select>
                </div>
                <div class="filter-item">
                    <div class="filter-label">搜索课程</div>
                    <input type="text" class="filter-select" id="searchInput" placeholder="输入课程名称..." onkeyup="searchCourses()">
                </div>
            </div>
        </div>

        <!-- 课程列表 -->
        <div class="container-mobile">
            <div id="courseList">
                <!-- 课程列表将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-graduation-cap"></i>
                <div id="emptyMessage">暂无需要重修的课程</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>

        <!-- 我的申请 -->
        <div class="my-applications">
            <div class="applications-header">
                <div class="applications-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的申请</span>
                </div>
                <div class="applications-count" id="applicationsCount">0</div>
            </div>

            <div id="applicationsList">
                <!-- 申请列表将通过JavaScript动态填充 -->
            </div>
        </div>
    </div>

    <!-- 重修申请表单 -->
    <div class="retake-form" id="retakeForm">
        <div class="form-header">
            <div class="form-back" onclick="closeRetakeForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">课程重修申请</div>
        </div>

        <div class="form-content">
            <!-- 课程信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-book"></i>
                    <span>课程信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">课程名称</div>
                    <input type="text" class="form-input" id="courseName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">课程代码</div>
                    <input type="text" class="form-input" id="courseCode" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学分</div>
                    <input type="text" class="form-input" id="courseCredit" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">原始成绩</div>
                    <input type="text" class="form-input" id="originalScore" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">授课教师</div>
                    <input type="text" class="form-input" id="teacher" readonly>
                </div>
            </div>

            <!-- 重修方式 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-cogs"></i>
                    <span>重修方式</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">选择重修方式</div>
                    <div class="form-radio-group" id="retakeMethodGroup">
                        <!-- 重修方式选项将动态填充 -->
                    </div>
                </div>
            </div>

            <!-- 申请信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">申请原因</div>
                    <select class="form-input" id="applicationReason">
                        <option value="">请选择申请原因</option>
                        <option value="failed">课程不及格</option>
                        <option value="improve">提高成绩</option>
                        <option value="absent">缺考补修</option>
                        <option value="other">其他原因</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label required">详细说明</div>
                    <textarea class="form-input form-textarea" id="detailDescription"
                              placeholder="请详细说明申请重修的原因和情况..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">期望学期</div>
                    <select class="form-input" id="expectedTerm">
                        <option value="">请选择期望重修学期</option>
                    </select>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">家长联系电话</div>
                    <input type="tel" class="form-input" id="parentPhone" placeholder="请输入家长联系电话">
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeRetakeForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitRetakeApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let allCourses = [];
        let filteredCourses = [];
        let myApplications = [];
        let currentCourse = null;
        let retakeStats = {};
        let availableTerms = [];

        $(function() {
            initPage();
            loadRetakeStats();
            loadAvailableTerms();
            loadCourses();
            loadMyApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFilterEvents();
        }

        // 绑定筛选事件
        function bindFilterEvents() {
            $('#termSelect, #courseTypeSelect, #statusSelect').change(function() {
                applyFilters();
            });
        }

        // 加载重修统计
        function loadRetakeStats() {
            $.ajax({
                url: "/student/personalManagement/retakeApplication/getRetakeStats",
                type: "post",
                dataType: "json",
                success: function(data) {
                    retakeStats = data || {};
                    updateRetakeStats();
                },
                error: function() {
                    console.log('加载重修统计失败');
                }
            });
        }

        // 更新重修统计
        function updateRetakeStats() {
            $('#failedCourses').text(retakeStats.failedCourses || 0);
            $('#retakingCourses').text(retakeStats.retakingCourses || 0);
            $('#completedRetakes').text(retakeStats.completedRetakes || 0);
            $('#pendingApplications').text(retakeStats.pendingApplications || 0);
        }

        // 加载可用学期
        function loadAvailableTerms() {
            $.ajax({
                url: "/student/personalManagement/retakeApplication/getAvailableTerms",
                type: "post",
                dataType: "json",
                success: function(data) {
                    availableTerms = data.terms || [];
                    renderTermOptions();
                },
                error: function() {
                    console.log('加载学期列表失败');
                }
            });
        }

        // 渲染学期选项
        function renderTermOptions() {
            const termSelect = $('#termSelect');
            const expectedTermSelect = $('#expectedTerm');

            termSelect.find('option:not(:first)').remove();
            expectedTermSelect.find('option:not(:first)').remove();

            availableTerms.forEach(term => {
                const option = `<option value="${term.id}">${term.name}</option>`;
                termSelect.append(option);
                expectedTermSelect.append(option);
            });
        }

        // 加载课程数据
        function loadCourses() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/retakeApplication/getCourses",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allCourses = data.courses || [];
                    applyFilters();
                    showLoading(false);
                },
                error: function() {
                    showError('加载课程数据失败');
                    showLoading(false);
                }
            });
        }

        // 加载我的申请
        function loadMyApplications() {
            $.ajax({
                url: "/student/personalManagement/retakeApplication/getMyApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myApplications = data.applications || [];
                    renderApplicationsList();
                    updateApplicationsCount();
                },
                error: function() {
                    console.log('加载申请列表失败');
                }
            });
        }

        // 应用筛选条件
        function applyFilters() {
            const filters = {
                term: $('#termSelect').val(),
                courseType: $('#courseTypeSelect').val(),
                status: $('#statusSelect').val(),
                search: $('#searchInput').val().toLowerCase()
            };

            filteredCourses = allCourses.filter(course => {
                if (filters.term && course.termId !== filters.term) return false;
                if (filters.courseType && course.type !== filters.courseType) return false;
                if (filters.status && course.retakeStatus !== filters.status) return false;
                if (filters.search && !course.name.toLowerCase().includes(filters.search)) return false;
                return true;
            });

            renderCourseList();
        }

        // 搜索课程
        function searchCourses() {
            applyFilters();
        }

        // 渲染课程列表
        function renderCourseList() {
            const container = $('#courseList');
            container.empty();

            if (filteredCourses.length === 0) {
                showEmptyState('暂无符合条件的课程');
                return;
            } else {
                hideEmptyState();
            }

            filteredCourses.forEach(course => {
                const courseHtml = createCourseItem(course);
                container.append(courseHtml);
            });
        }

        // 创建课程项
        function createCourseItem(course) {
            const statusClass = getRetakeStatusClass(course.retakeStatus);

            return `
                <div class="course-item ${statusClass}" onclick="showCourseDetail('${course.id}')">
                    <div class="course-header">
                        <div class="course-name">${course.name}</div>
                        <div class="course-score">${course.score}分</div>
                    </div>
                    <div class="course-info">
                        <div class="info-item">
                            <span>课程代码:</span>
                            <span>${course.code}</span>
                        </div>
                        <div class="info-item">
                            <span>学分:</span>
                            <span>${course.credit}</span>
                        </div>
                        <div class="info-item">
                            <span>授课教师:</span>
                            <span>${course.teacher}</span>
                        </div>
                        <div class="info-item">
                            <span>学期:</span>
                            <span>${course.termName}</span>
                        </div>
                    </div>
                    <div class="retake-options">
                        <div class="options-title">重修选项</div>
                        <div class="option-list">
                            ${createRetakeOptions(course.retakeOptions)}
                        </div>
                    </div>
                    <div class="course-actions">
                        ${createCourseActions(course)}
                    </div>
                </div>
            `;
        }

        // 创建重修选项
        function createRetakeOptions(options) {
            if (!options || options.length === 0) {
                return '<div class="option-item"><span class="option-name">暂无重修选项</span><span class="option-status">-</span></div>';
            }

            return options.map(option => {
                const statusClass = getOptionStatusClass(option.status);
                const statusText = getOptionStatusText(option.status);
                return `
                    <div class="option-item">
                        <span class="option-name">${option.name}</span>
                        <span class="option-status ${statusClass}">${statusText}</span>
                    </div>
                `;
            }).join('');
        }

        // 创建课程操作按钮
        function createCourseActions(course) {
            const canApply = course.retakeStatus === 'failed' && course.canApply;

            if (canApply) {
                return `
                    <button class="btn-mobile btn-apply flex-1" onclick="showRetakeForm('${course.id}')">申请重修</button>
                    <button class="btn-mobile btn-view flex-1" onclick="showCourseDetail('${course.id}')">查看详情</button>
                `;
            } else {
                const buttonText = getActionButtonText(course.retakeStatus);
                return `
                    <button class="btn-mobile btn-disabled flex-1">${buttonText}</button>
                    <button class="btn-mobile btn-view flex-1" onclick="showCourseDetail('${course.id}')">查看详情</button>
                `;
            }
        }

        // 获取重修状态样式类
        function getRetakeStatusClass(status) {
            switch(status) {
                case 'retaking': return 'retaking';
                case 'completed': return 'completed';
                default: return '';
            }
        }

        // 获取选项状态样式类
        function getOptionStatusClass(status) {
            switch(status) {
                case 'available': return 'available';
                case 'full': return 'full';
                case 'closed': return 'closed';
                default: return '';
            }
        }

        // 获取选项状态文本
        function getOptionStatusText(status) {
            switch(status) {
                case 'available': return '可选';
                case 'full': return '已满';
                case 'closed': return '已关闭';
                default: return '未知';
            }
        }

        // 获取操作按钮文本
        function getActionButtonText(status) {
            switch(status) {
                case 'retaking': return '重修中';
                case 'completed': return '已完成';
                default: return '不可申请';
            }
        }

        // 显示重修申请表单
        function showRetakeForm(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;

            currentCourse = course;

            // 填充课程信息
            $('#formTitle').text(course.name + ' - 重修申请');
            $('#courseName').val(course.name);
            $('#courseCode').val(course.code);
            $('#courseCredit').val(course.credit + '学分');
            $('#originalScore').val(course.score + '分');
            $('#teacher').val(course.teacher);

            // 加载重修方式选项
            loadRetakeMethods(courseId);

            // 清空表单
            resetRetakeForm();

            // 显示表单
            $('#retakeForm').addClass('show');
        }

        // 加载重修方式
        function loadRetakeMethods(courseId) {
            $.ajax({
                url: "/student/personalManagement/retakeApplication/getRetakeMethods",
                type: "post",
                data: { courseId: courseId },
                dataType: "json",
                success: function(data) {
                    const methods = data.methods || [];
                    renderRetakeMethods(methods);
                },
                error: function() {
                    console.log('加载重修方式失败');
                }
            });
        }

        // 渲染重修方式
        function renderRetakeMethods(methods) {
            const container = $('#retakeMethodGroup');
            container.empty();

            methods.forEach((method, index) => {
                const statusClass = getOptionStatusClass(method.status);
                const statusText = getOptionStatusText(method.status);
                const disabled = method.status !== 'available' ? 'disabled' : '';

                const methodHtml = `
                    <div class="form-radio">
                        <input type="radio" name="retakeMethod" value="${method.id}" ${disabled} ${index === 0 ? 'checked' : ''}>
                        <div class="radio-content">
                            <div class="radio-title">${method.name}</div>
                            <div class="radio-desc">${method.description}</div>
                        </div>
                        <div class="radio-status ${statusClass}">${statusText}</div>
                    </div>
                `;
                container.append(methodHtml);
            });
        }

        // 重置重修表单
        function resetRetakeForm() {
            $('#applicationReason').val('');
            $('#detailDescription').val('');
            $('#expectedTerm').val('');
            $('#contactPhone').val('');
            $('#parentPhone').val('');
        }

        // 关闭重修申请表单
        function closeRetakeForm() {
            $('#retakeForm').removeClass('show');
        }

        // 提交重修申请
        function submitRetakeApplication() {
            if (!validateRetakeForm()) {
                return;
            }

            const formData = collectRetakeFormData();

            const message = `确定要提交"${currentCourse.name}"的重修申请吗？\n\n提交后将进入审核流程，请确保信息准确无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitRetakeFormData(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    submitRetakeFormData(formData);
                }
            }
        }

        // 收集重修表单数据
        function collectRetakeFormData() {
            return {
                courseId: currentCourse.id,
                retakeMethod: $('input[name="retakeMethod"]:checked').val(),
                applicationReason: $('#applicationReason').val(),
                detailDescription: $('#detailDescription').val(),
                expectedTerm: $('#expectedTerm').val(),
                contactPhone: $('#contactPhone').val(),
                parentPhone: $('#parentPhone').val()
            };
        }

        // 验证重修表单
        function validateRetakeForm() {
            if (!$('input[name="retakeMethod"]:checked').val()) {
                showError('请选择重修方式');
                return false;
            }

            if (!$('#applicationReason').val()) {
                showError('请选择申请原因');
                return false;
            }

            if (!$('#detailDescription').val().trim()) {
                showError('请填写详细说明');
                return false;
            }

            if (!$('#contactPhone').val().trim()) {
                showError('请填写联系电话');
                return false;
            }

            return true;
        }

        // 提交重修表单数据
        function submitRetakeFormData(formData) {
            $.ajax({
                url: "/student/personalManagement/retakeApplication/submitRetakeApplication",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('重修申请提交成功');
                        closeRetakeForm();
                        loadRetakeStats();
                        loadCourses();
                        loadMyApplications();
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示课程详情
        function showCourseDetail(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;

            let message = `课程详情\n\n`;
            message += `课程名称：${course.name}\n`;
            message += `课程代码：${course.code}\n`;
            message += `学分：${course.credit}\n`;
            message += `原始成绩：${course.score}分\n`;
            message += `授课教师：${course.teacher}\n`;
            message += `学期：${course.termName}\n`;
            message += `重修状态：${getRetakeStatusText(course.retakeStatus)}\n`;

            if (course.retakeOptions && course.retakeOptions.length > 0) {
                message += `\n重修选项：\n`;
                course.retakeOptions.forEach(option => {
                    message += `- ${option.name}: ${getOptionStatusText(option.status)}\n`;
                });
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 获取重修状态文本
        function getRetakeStatusText(status) {
            switch(status) {
                case 'failed': return '不及格';
                case 'retaking': return '重修中';
                case 'completed': return '已完成';
                default: return '未知';
            }
        }

        // 渲染申请列表
        function renderApplicationsList() {
            const container = $('#applicationsList');
            container.empty();

            if (myApplications.length === 0) {
                return;
            }

            myApplications.forEach(application => {
                const applicationHtml = createApplicationItem(application);
                container.append(applicationHtml);
            });
        }

        // 创建申请项
        function createApplicationItem(application) {
            const statusClass = getApplicationStatusClass(application.status);
            const statusText = getApplicationStatusText(application.status);

            return `
                <div class="application-item ${statusClass}" onclick="showApplicationDetail('${application.id}')">
                    <div class="application-basic">
                        <div class="application-title">${application.courseName}</div>
                        <div class="application-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="application-details">
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(application.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>重修方式:</span>
                            <span>${application.retakeMethod}</span>
                        </div>
                        <div class="detail-item">
                            <span>申请原因:</span>
                            <span>${getReasonText(application.reason)}</span>
                        </div>
                        <div class="detail-item">
                            <span>期望学期:</span>
                            <span>${application.expectedTerm || '-'}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取申请状态样式类
        function getApplicationStatusClass(status) {
            switch(status) {
                case 'pending': return 'pending';
                case 'approved': return 'approved';
                case 'rejected': return 'rejected';
                default: return 'pending';
            }
        }

        // 获取申请状态文本
        function getApplicationStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 获取原因文本
        function getReasonText(reason) {
            switch(reason) {
                case 'failed': return '课程不及格';
                case 'improve': return '提高成绩';
                case 'absent': return '缺考补修';
                case 'other': return '其他原因';
                default: return reason;
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示申请详情
        function showApplicationDetail(applicationId) {
            const application = myApplications.find(app => app.id === applicationId);
            if (!application) return;

            let message = `重修申请详情\n\n`;
            message += `课程名称：${application.courseName}\n`;
            message += `申请时间：${formatDate(application.createTime)}\n`;
            message += `重修方式：${application.retakeMethod}\n`;
            message += `申请原因：${getReasonText(application.reason)}\n`;
            message += `详细说明：${application.description}\n`;
            message += `期望学期：${application.expectedTerm || '-'}\n`;
            message += `当前状态：${getApplicationStatusText(application.status)}\n`;

            if (application.reviewComment) {
                message += `审核意见：${application.reviewComment}\n`;
            }

            if (application.reviewTime) {
                message += `审核时间：${formatDate(application.reviewTime)}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 更新申请数量
        function updateApplicationsCount() {
            $('#applicationsCount').text(myApplications.length);
        }

        // 刷新数据
        function refreshData() {
            loadRetakeStats();
            loadCourses();
            loadMyApplications();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#courseList').hide();
            } else {
                $('#loadingState').hide();
                $('#courseList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#retakeForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#retakeForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeRetakeForm();
            }
        });

        $('#retakeForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
