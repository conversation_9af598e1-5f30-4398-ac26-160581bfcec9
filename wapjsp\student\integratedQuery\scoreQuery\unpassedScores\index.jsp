<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>不及格成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 不及格成绩页面样式 */
        .warning-header {
            background: linear-gradient(135deg, var(--error-color), #ff7875);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .warning-title {
            display: flex;
            align-items: center;
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }
        
        .warning-title i {
            margin-right: var(--margin-xs);
        }
        
        .warning-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .risk-notice {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--warning-color);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--warning-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            margin-right: var(--margin-xs);
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .failed-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--error-color);
        }
        
        .failed-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .failed-score {
            background: var(--error-color);
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        .failed-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .failed-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .failed-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .retake-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .retake-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-retaken {
            background: var(--info-color);
            color: white;
        }
        
        .status-expired {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-retake {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-retake:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .semester-group {
            margin-bottom: var(--margin-lg);
        }
        
        .semester-title {
            background: var(--bg-tertiary);
            padding: var(--padding-sm) var(--padding-md);
            margin: 0 var(--margin-md) var(--margin-sm);
            border-radius: 6px;
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            position: sticky;
            top: 56px;
            z-index: 100;
        }
        
        .progress-warning {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--error-color);
        }
        
        .progress-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--error-color);
            margin-bottom: var(--margin-sm);
        }
        
        .progress-bar {
            background: var(--bg-tertiary);
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-bottom: var(--margin-sm);
        }
        
        .progress-fill {
            background: var(--error-color);
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">不及格成绩</div>
            <div class="navbar-action" onclick="refreshScores();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 警告统计 -->
        <div class="warning-header">
            <div class="warning-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>学业预警</span>
            </div>
            <div class="warning-stats">
                <div class="stat-item">
                    <div class="stat-number" id="failedCourses">0</div>
                    <div class="stat-label">不及格课程</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="failedCredits">0</div>
                    <div class="stat-label">不及格学分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="retakeAvailable">0</div>
                    <div class="stat-label">可重修</div>
                </div>
            </div>
        </div>
        
        <!-- 学业进度预警 -->
        <div class="progress-warning" id="progressWarning" style="display: none;">
            <div class="progress-title">学业进度预警</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText"></div>
        </div>
        
        <!-- 风险提示 -->
        <div class="risk-notice" id="riskNotice" style="display: none;">
            <div class="notice-title">
                <i class="ace-icon fa fa-warning"></i>
                <span>重要提醒</span>
            </div>
            <div class="notice-content" id="riskContent">
                <!-- 风险提示内容 -->
            </div>
        </div>
        
        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterScores('all')">全部</button>
                <button class="filter-chip" onclick="filterScores('retakable')">可重修</button>
                <button class="filter-chip" onclick="filterScores('retaken')">已重修</button>
                <button class="filter-chip" onclick="filterScores('required')">必修课</button>
            </div>
        </div>
        
        <!-- 不及格成绩列表 -->
        <div class="container-mobile">
            <div id="scoreList">
                <!-- 成绩项将通过JavaScript动态填充 -->
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-check-circle"></i>
                <div>恭喜！暂无不及格成绩</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allScores = [];
        let filteredScores = [];
        let currentFilter = 'all';
        let groupedScores = {};

        $(function() {
            initPage();
            loadScores();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载不及格成绩
        function loadScores() {
            showLoading(true);
            
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/unpassedScores/getScores",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allScores = data.scores || [];
                    updateStatistics(data.statistics);
                    updateWarnings(data.warnings);
                    applyFilter();
                    showLoading(false);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                    showLoading(false);
                }
            });
        }

        // 渲染成绩列表
        function renderScores() {
            const container = $('#scoreList');
            container.empty();
            
            if (filteredScores.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            // 按学期分组
            groupedScores = groupBySemester(filteredScores);
            
            Object.keys(groupedScores).forEach(semester => {
                // 添加学期标题
                const semesterHtml = `<div class="semester-title">${semester}</div>`;
                container.append(semesterHtml);
                
                // 添加该学期的成绩
                groupedScores[semester].forEach(function(score, index) {
                    const scoreHtml = createFailedItem(score, index);
                    container.append(scoreHtml);
                });
            });
        }

        // 按学期分组
        function groupBySemester(scores) {
            const grouped = {};
            scores.forEach(score => {
                const semester = score.semester || '未知学期';
                if (!grouped[semester]) {
                    grouped[semester] = [];
                }
                grouped[semester].push(score);
            });
            return grouped;
        }

        // 创建不及格成绩项HTML
        function createFailedItem(score, index) {
            const retakeStatus = getRetakeStatus(score);
            const statusClass = getRetakeStatusClass(retakeStatus);
            
            let actionButton = '';
            if (retakeStatus === 'available') {
                actionButton = `<button class="btn-retake" onclick="applyRetake('${score.id}')">申请重修</button>`;
            } else if (retakeStatus === 'retaken') {
                actionButton = `<button class="btn-retake" disabled>已重修</button>`;
            } else {
                actionButton = `<button class="btn-retake" disabled>不可重修</button>`;
            }
            
            return `
                <div class="failed-item">
                    <div class="failed-header">
                        <div class="course-name">${score.courseName}</div>
                        <div class="failed-score">${score.finalScore || score.totalScore}</div>
                    </div>
                    <div class="failed-details">
                        <div class="failed-detail-item">
                            <span>课程号:</span>
                            <span>${score.courseCode}</span>
                        </div>
                        <div class="failed-detail-item">
                            <span>学分:</span>
                            <span>${score.credits}</span>
                        </div>
                        <div class="failed-detail-item">
                            <span>课程属性:</span>
                            <span>${score.courseType}</span>
                        </div>
                        <div class="failed-detail-item">
                            <span>考试类型:</span>
                            <span>${score.examType}</span>
                        </div>
                    </div>
                    <div class="failed-actions">
                        <div class="retake-info">
                            <span class="retake-status ${statusClass}">${getRetakeStatusText(retakeStatus)}</span>
                        </div>
                        ${actionButton}
                    </div>
                </div>
            `;
        }

        // 获取重修状态
        function getRetakeStatus(score) {
            if (score.isRetaken) {
                return 'retaken';
            } else if (score.canRetake) {
                return 'available';
            } else {
                return 'expired';
            }
        }

        // 获取重修状态样式类
        function getRetakeStatusClass(status) {
            switch(status) {
                case 'available': return 'status-available';
                case 'retaken': return 'status-retaken';
                case 'expired': return 'status-expired';
                default: return 'status-expired';
            }
        }

        // 获取重修状态文本
        function getRetakeStatusText(status) {
            switch(status) {
                case 'available': return '可重修';
                case 'retaken': return '已重修';
                case 'expired': return '不可重修';
                default: return '不可重修';
            }
        }

        // 申请重修
        function applyRetake(scoreId) {
            const score = allScores.find(s => s.id === scoreId);
            if (!score) return;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要申请重修《${score.courseName}》吗？`, function(confirmed) {
                    if (confirmed) {
                        doApplyRetake(scoreId);
                    }
                });
            } else {
                if (confirm(`确定要申请重修《${score.courseName}》吗？`)) {
                    doApplyRetake(scoreId);
                }
            }
        }

        // 执行申请重修
        function doApplyRetake(scoreId) {
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/unpassedScores/applyRetake",
                type: "post",
                data: { scoreId: scoreId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('重修申请提交成功');
                        loadScores(); // 重新加载数据
                    } else {
                        showError(data.message || '申请失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'retakable':
                    filteredScores = allScores.filter(score => score.canRetake && !score.isRetaken);
                    break;
                case 'retaken':
                    filteredScores = allScores.filter(score => score.isRetaken);
                    break;
                case 'required':
                    filteredScores = allScores.filter(score => score.courseType === '必修');
                    break;
                default:
                    filteredScores = allScores;
            }
            
            renderScores();
        }

        // 筛选成绩
        function filterScores(filter) {
            currentFilter = filter;
            
            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');
            
            applyFilter();
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;
            
            $('#failedCourses').text(statistics.failedCourses || 0);
            $('#failedCredits').text(statistics.failedCredits || 0);
            $('#retakeAvailable').text(statistics.retakeAvailable || 0);
        }

        // 更新预警信息
        function updateWarnings(warnings) {
            if (!warnings) return;
            
            // 显示学业进度预警
            if (warnings.progressWarning) {
                const progress = warnings.progressWarning;
                $('#progressFill').css('width', progress.percentage + '%');
                $('#progressText').text(`已获得学分: ${progress.earnedCredits}/${progress.totalCredits} (${progress.percentage}%)`);
                $('#progressWarning').show();
            }
            
            // 显示风险提示
            if (warnings.riskNotice) {
                $('#riskContent').text(warnings.riskNotice);
                $('#riskNotice').show();
            }
        }

        // 刷新成绩
        function refreshScores() {
            loadScores();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#scoreList, .warning-header, .filter-section').hide();
            } else {
                $('#loadingState').hide();
                $('#scoreList, .warning-header, .filter-section').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
