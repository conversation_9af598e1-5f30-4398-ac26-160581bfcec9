<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>选择成绩</title>  
</head> 
<body>
    <div class="row">
        <div class="col-sm-12 self-margin">
            <div class="modal-header no-padding">
                <div class="table-header">
                     <button type="button" class="close" data-dismiss="modal" aria-hidden="true" id="close_selectScore">
	                    <span class="white">×</span>
	                </button>
                    <i class="fa fa-plus-circle"></i> 选择成绩
                </div>
            </div>
			<div class="widget-body">
				<div id="curriculum_scroll" style="max-height: calc(100vh - 345px);overflow: auto;">
					<table class="table table-bordered table-hover"  id="curriculumTable">
						<thead >
							<tr class="center">
								<th>选择</th>
								<th>序号</th>
								<th>成绩学年学期</th>
								<th>课程号</th>
								<th>课程名</th>
								<th>课序号</th>
								<th>考试时间</th>
								<th>课程成绩</th>
								<th>等级成绩</th>
								<th>绩点成绩</th>
								<th>修读方式</th>
							</tr>
						</thead>
						<tbody id="curriculumtbody" style="overflow: auto;">
							<c:if test="${empty xscj}">
								<tr><td colspan='10'>未查询到学生学期成绩！</td></tr>
							</c:if>
							<c:forEach var="cj" items="${xscj}" varStatus="var">
								<tr>
									<td class="center">
										<i title="选择" class="fa fa-check iinfo" onclick="selectThis(this);" value="${cj[0]},${cj[1]},${cj[2]},${cj[3]},${cj[4]}"></i>
									</td>
									<td>${var.index + 1}</td>  
									<td>${cj[3] }</td>
									<td>${cj[1] }</td>
									<td>${cj[4] }</td>
									<td>${cj[2] }</td>
									<td>${cj[5] }</td>
									<td>${cj[6] }</td>
									<td>${cj[7] }</td>
									<td>${cj[8] }</td>
									<td>${cj[9] }</td>
								</tr>
							</c:forEach>
						</tbody>
					</table>
				</div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
    	$(function() {
    		urp.fixedheader("curriculum_scroll");
    	});
    	function selectThis(obj) {
    		var _this = $(obj);
    		var val = _this.attr("value").split(",");//zxjxjhh,kch,kxh,xnxqmc,kcm
    		$("[name=zxjxjhh]:hidden").val(val[0]);
			$("[name=kch]:hidden").val(val[1]);
    		$("[name=kxh]:hidden").val(val[2]);
    		$("#kxcc_xs").html("【"+ val[1] + "_" + val[2] +"】" + val[4]);
			$("#zxjxjhh_xs").html(val[3]);
    		$("#close_selectScore").click();
    	}
    </script>
</body>
</html>