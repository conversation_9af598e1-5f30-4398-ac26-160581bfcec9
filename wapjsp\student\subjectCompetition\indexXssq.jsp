<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学科竞赛申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学科竞赛申请页面样式 */
        .competition-header {
            background: linear-gradient(135deg, var(--warning-color), #ff9c6e);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .competition-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .competition-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .action-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .action-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-title i {
            color: var(--warning-color);
        }
        
        .add-button {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .add-button:hover {
            background: var(--success-dark);
        }
        
        .application-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-header i {
            color: var(--warning-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            transition: all var(--transition-base);
            border-left: 4px solid var(--warning-color);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:hover {
            background: var(--bg-tertiary);
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-sm);
        }
        
        .application-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .application-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .application-event {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .application-index {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            flex-shrink: 0;
        }
        
        .application-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin: var(--margin-sm) 0;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .detail-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .approval-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-view:hover {
            background: var(--info-dark);
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-edit:hover {
            background: var(--primary-dark);
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-delete:hover {
            background: var(--error-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            padding: var(--padding-md);
            text-align: center;
        }
        
        .load-more-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md) var(--padding-lg);
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .load-more-button:hover {
            background: var(--primary-dark);
        }
        
        .load-more-button:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .application-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .application-details {
                grid-template-columns: 1fr;
            }
            
            .application-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学科竞赛申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学科竞赛申请头部 -->
        <div class="competition-header">
            <div class="competition-title">学科竞赛申请</div>
            <div class="competition-desc">我申请的学科竞赛</div>
        </div>
        
        <!-- 操作区域 -->
        <div class="action-container">
            <div class="action-title">
                <i class="ace-icon fa fa-plus"></i>
                申请学科竞赛
            </div>
            
            <button class="add-button" onclick="addJs();">
                <i class="ace-icon fa fa-plus"></i>
                <span>申请学科竞赛</span>
            </button>
        </div>
        
        <!-- 申请列表 -->
        <div class="application-list" id="applicationList" style="display: none;">
            <div class="list-header">
                <i class="ace-icon fa fa-trophy"></i>
                我申请的学科竞赛
            </div>
            
            <div id="applicationItems">
                <!-- 动态加载申请项 -->
            </div>
            
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <button class="load-more-button" id="loadMoreButton" onclick="loadMore();">
                    加载更多
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-trophy"></i>
            <div class="empty-state-text">暂无申请记录</div>
            <div class="empty-state-desc">点击上方按钮申请学科竞赛</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let isLoading = false;

        $(function() {
            initPage();
            getBatchList(1, "30_sl", true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 分页查询
        function getBatchList(page, pageSizeVal, conditionChanged) {
            if (pageSizeVal == undefined) {
                pageSizeVal = "30_sl";
                page = "1";
            }

            const parr = (pageSizeVal + "").split("_");
            const pageSize = parseInt(parr[0]);

            showLoading(true);

            $.ajax({
                url: "/student/subjectCompetition/selectXkjsSq",
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(d) {
                    const data = d.data;

                    if (data && data.records && data.records.length > 0) {
                        totalCount = data.pageContext.totalCount;
                        fillTable(data.records, false, page, pageSize);
                        showEmptyState(false);
                        showApplicationList(true);
                    } else {
                        fillTable(null, false, page, pageSize);
                        showEmptyState(true);
                        showApplicationList(false);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示数据
        function fillTable(data, isScroll, page, pageSize) {
            const container = $('#applicationItems');
            let content = '';

            if (data != null && data.length > 0) {
                data.forEach(function(item, index) {
                    const tableId = isScroll ? (page - 1) * pageSize + 1 + index : index + 1;
                    content += createApplicationItem(item, tableId);
                });
            }

            if (isScroll) {
                container.append(content);
            } else {
                container.html(content);
            }
        }

        // 创建申请项HTML
        function createApplicationItem(item, index) {
            const approvalStatusText = getApprovalStatusText(item.ZDJSSPZT);
            const approvalStatusClass = getApprovalStatusClass(item.ZDJSSPZT);

            return `
                <div class="application-item">
                    <div class="application-header">
                        <div class="application-info">
                            <div class="application-name">${item.JSMC || '学科竞赛名称'}</div>
                            <div class="application-event">${item.SSJSMC || '所属赛事'}</div>
                        </div>
                        <div class="application-index">#${index}</div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <div class="detail-label">获奖等级</div>
                            <div class="detail-value">${item.HJDJM || '-'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">获奖级别</div>
                            <div class="detail-value">${item.HJJBM || '-'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">获奖时间</div>
                            <div class="detail-value">${item.HJSJ || '-'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">审批状态</div>
                            <div class="detail-value">
                                <span class="approval-status ${approvalStatusClass}">${approvalStatusText}</span>
                            </div>
                        </div>
                    </div>

                    ${item.ZDJSSPYJ ? `
                        <div class="detail-item" style="margin-top: var(--margin-sm);">
                            <div class="detail-label">审批意见</div>
                            <div class="detail-value">${item.ZDJSSPYJ}</div>
                        </div>
                    ` : ''}

                    <div class="application-actions">
                        <button class="action-btn btn-view" onclick="views('${item.SQID}');">
                            <i class="ace-icon fa fa-eye"></i>
                            查看
                        </button>
                        ${item.TJZT == "0" ? `
                            <button class="action-btn btn-edit" onclick="addInfo('${item.SQID}');">
                                <i class="ace-icon fa fa-pencil-square-o"></i>
                                修改
                            </button>
                            <button class="action-btn btn-delete" onclick="doDel('${item.SQID}');">
                                <i class="ace-icon fa fa-trash-o"></i>
                                删除
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 获取审批状态文本
        function getApprovalStatusText(status) {
            if (status == null || status == "" || status == "01") {
                return "待审批";
            } else if (status == "02") {
                return "已批准";
            } else {
                return "未批准";
            }
        }

        // 获取审批状态样式类
        function getApprovalStatusClass(status) {
            if (status == null || status == "" || status == "01") {
                return "status-pending";
            } else if (status == "02") {
                return "status-approved";
            } else {
                return "status-rejected";
            }
        }

        // 删除申请
        function doDel(id) {
            showConfirm("删除后不可恢复，是否确认删除?", function() {
                showLoading(true);

                $.ajax({
                    url: "/student/subjectCompetition/delXkjsSq",
                    cache: false,
                    data: "&id=" + id + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(d) {
                        const data = d.data;
                        $("#tokenValue").val(d.token);

                        if (d.status != 200) {
                            showError(d.msg);
                        } else {
                            if (data["result"].indexOf("/logout") != -1) {
                                window.location.href = data["result"];
                            } else {
                                if (data["result"] == "ok") {
                                    showSuccess("删除成功！");
                                    setTimeout(() => {
                                        getBatchList(1, "30_sl", true);
                                    }, 1500);
                                } else {
                                    showError(data.msg || "删除失败");
                                }
                            }
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            });
        }

        // 查看申请详情
        function views(sqid) {
            const url = "/student/subjectCompetition/views?sqid=" + sqid;
            window.open(url, "_blank");
        }

        // 修改申请
        function addInfo(sqid) {
            window.location.href = "/student/subjectCompetition/editSq?sqid=" + sqid;
        }

        // 新增申请
        function addJs() {
            window.location.href = "/student/subjectCompetition/to/add";
        }

        // 加载更多
        function loadMore() {
            currentPage++;
            getBatchList(currentPage, pageSize + "_sl", false);
        }

        // 刷新数据
        function refreshData() {
            getBatchList(1, "30_sl", true);
        }

        // 显示申请列表
        function showApplicationList(show) {
            if (show) {
                $('#applicationList').show();
            } else {
                $('#applicationList').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
            } else {
                $('#emptyState').hide();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示确认对话框
        function showConfirm(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            alert(message);
        }

        // 显示错误信息
        function showError(message) {
            alert(message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
