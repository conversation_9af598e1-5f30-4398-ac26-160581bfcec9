<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>分项成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 分项成绩页面样式 */
        .subitem-header {
            background: linear-gradient(135deg, var(--success-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .subitem-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .subitem-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .course-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .course-name {
            font-size: var(--font-size-large);
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
            margin-bottom: var(--margin-sm);
        }
        
        .course-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            text-align: center;
        }
        
        .subitem-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-header i {
            color: var(--success-color);
        }
        
        .subitem-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            transition: all var(--transition-base);
        }
        
        .subitem-item:last-child {
            border-bottom: none;
        }
        
        .subitem-item:hover {
            background: var(--bg-tertiary);
        }
        
        .subitem-header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-sm);
        }
        
        .subitem-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .subitem-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .subitem-index {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .subitem-score {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            height: 30px;
            border-radius: 15px;
            font-size: var(--font-size-base);
            font-weight: 500;
            flex-shrink: 0;
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .subitem-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .meta-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .meta-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .weight-indicator {
            background: var(--info-light);
            color: var(--info-dark);
            border-radius: 12px;
            padding: 4px 8px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            display: inline-block;
            margin-top: var(--margin-sm);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            padding: var(--padding-md);
            text-align: center;
        }
        
        .load-more-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md) var(--padding-lg);
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .load-more-button:hover {
            background: var(--primary-dark);
        }
        
        .load-more-button:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .subitem-header-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .subitem-meta {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    <input type="hidden" id="param" name="param" value="${param}">
    <input type="hidden" id="fxcjId" name="fxcjId" value="${fxcjId}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="window.history.go(-1);">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">分项成绩</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 分项成绩头部 -->
        <div class="subitem-header">
            <div class="subitem-title">分项成绩详情</div>
            <div class="subitem-desc">查看课程各分项成绩构成</div>
        </div>
        
        <!-- 课程信息 -->
        <div class="course-info">
            <div class="course-name">${fn:replace(fn:replace(kcm, '（', ''), '）', '')}</div>
            <div class="course-details">课程分项成绩详细信息</div>
        </div>
        
        <!-- 分项成绩列表 -->
        <div class="subitem-list" id="subitemList" style="display: none;">
            <div class="list-header">
                <i class="ace-icon fa fa-list"></i>
                分项成绩列表
            </div>
            
            <div id="subitemItems">
                <!-- 动态加载分项成绩项 -->
            </div>
            
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <button class="load-more-button" id="loadMoreButton" onclick="loadMore();">
                    加载更多
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-bar-chart-o"></i>
            <div class="empty-state-text">暂无分项成绩</div>
            <div class="empty-state-desc">该课程暂无分项成绩信息</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let isLoading = false;
        let methodMarkCode = '${method_mark_code}';

        $(function() {
            initPage();
            search();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 查询分项成绩
        function search() {
            currentPage = 1;
            getResultsList(currentPage, true);
        }

        // 加载更多
        function loadMore() {
            if (isLoading) return;
            currentPage++;
            getResultsList(currentPage, false);
        }

        // 获取结果列表
        function getResultsList(page, isNewSearch) {
            if (isLoading) return;

            isLoading = true;
            showLoading(true);

            const fxcjId = $('#fxcjId').val();

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/${url_check_code}/subitemScore/searchFxcj",
                cache: false,
                type: "post",
                data: "fxcjId=" + fxcjId + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    totalCount = data["pageContext"].totalCount;

                    if (data["records"] != null) {
                        fillResultsTable(data["records"], !isNewSearch, page);
                    } else {
                        fillResultsTable(null, !isNewSearch, page);
                    }

                    updatePagination();
                },
                error: function(xhr) {
                    showError("获取数据失败，请重试");
                },
                complete: function() {
                    isLoading = false;
                    showLoading(false);
                }
            });
        }

        // 填充结果表格
        function fillResultsTable(data, isAppend, page) {
            let html = '';

            if (data != null && data.length > 0) {
                data.forEach(function(item, index) {
                    const tableId = isAppend ? (page - 1) * pageSize + 1 + index : index + 1;
                    html += createSubitemItem(item, tableId);
                });

                if (isAppend) {
                    $('#subitemItems').append(html);
                } else {
                    $('#subitemItems').html(html);
                }

                $('#subitemList').show();
                $('#emptyState').hide();
            } else {
                if (!isAppend) {
                    $('#subitemItems').html('');
                    $('#subitemList').hide();
                    $('#emptyState').show();
                }
            }
        }

        // 创建分项成绩项HTML
        function createSubitemItem(item, index) {
            return `
                <div class="subitem-item">
                    <div class="subitem-header-row">
                        <div class="subitem-info">
                            <div class="subitem-name">${item.CJFXMC || ''}</div>
                            <div class="subitem-index"># ${index}</div>
                        </div>
                        <div class="subitem-score">
                            ${item.FXCJ || ''}
                        </div>
                    </div>

                    <div class="subitem-meta">
                        <div class="meta-item">
                            <div class="meta-label">分项成绩</div>
                            <div class="meta-value">${item.FXCJ || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">分项所占系数</div>
                            <div class="meta-value">${item.CJFXZB ? parseFloat(item.CJFXZB) : ''}</div>
                        </div>
                    </div>

                    ${item.CJFXZB ? `
                        <div class="weight-indicator">
                            权重: ${parseFloat(item.CJFXZB)}
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 更新分页
        function updatePagination() {
            const hasMore = currentPage * pageSize < totalCount;

            if (hasMore) {
                $('#paginationContainer').show();
                $('#loadMoreButton').prop('disabled', false);
            } else {
                $('#paginationContainer').hide();
            }
        }

        // 刷新数据
        function refreshData() {
            search();
        }

        // 返回上一页
        function returnIndex() {
            window.location.href = "/student/integratedQuery/scoreQuery/subitemScore/index";
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight() || 0;
            const availableHeight = windowHeight - navbarHeight;

            $('.page-mobile').css('min-height', availableHeight + 'px');
        }
    </script>
</body>
</html>
