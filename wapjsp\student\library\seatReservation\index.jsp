<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>座位预约</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 座位预约页面样式 */
        .reservation-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-row {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .filter-row:last-child {
            margin-bottom: 0;
        }
        
        .filter-item {
            flex: 1;
        }
        
        .filter-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .filter-select {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .time-picker {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .time-input {
            flex: 1;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .btn-search {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
            width: 100%;
        }
        
        .seat-map {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .map-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .seat-legend {
            display: flex;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: var(--margin-xs);
        }
        
        .legend-available {
            background: var(--success-color);
        }
        
        .legend-occupied {
            background: var(--error-color);
        }
        
        .legend-selected {
            background: var(--primary-color);
        }
        
        .legend-reserved {
            background: var(--warning-color);
        }
        
        .seat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 8px;
            max-width: 100%;
            margin: 0 auto;
        }
        
        .seat {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
        }
        
        .seat.available {
            background: var(--success-color);
            color: white;
        }
        
        .seat.occupied {
            background: var(--error-color);
            color: white;
            cursor: not-allowed;
        }
        
        .seat.selected {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-dark);
            transform: scale(1.1);
        }
        
        .seat.reserved {
            background: var(--warning-color);
            color: white;
            cursor: not-allowed;
        }
        
        .seat:hover:not(.occupied):not(.reserved) {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .seat-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .info-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .reservation-actions {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-reserve {
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--error-color);
            color: white;
        }
        
        .btn-extend {
            background: var(--warning-color);
            color: white;
        }
        
        .my-reservations {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .reservations-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .reservations-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .reservation-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .reservation-item:last-child {
            border-bottom: none;
        }
        
        .reservation-item:active {
            background: var(--bg-color-active);
        }
        
        .reservation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .reservation-location {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .reservation-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-active {
            background: var(--success-color);
            color: white;
        }
        
        .status-upcoming {
            background: var(--info-color);
            color: white;
        }
        
        .status-expired {
            background: var(--text-disabled);
            color: white;
        }
        
        .reservation-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .reservation-time {
            margin-top: var(--margin-xs);
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-btn {
            padding: var(--padding-md);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid var(--border-primary);
            background: var(--bg-primary);
        }
        
        .action-btn:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .action-icon {
            font-size: var(--font-size-h3);
            color: var(--primary-color);
            margin-bottom: var(--margin-sm);
        }
        
        .action-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .rules-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
        }
        
        .rules-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--info-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .rules-title i {
            margin-right: var(--margin-xs);
        }
        
        .rules-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .rules-list {
            margin: 0;
            padding-left: var(--padding-md);
        }
        
        .rules-list li {
            margin-bottom: var(--margin-xs);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">座位预约</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 预约统计 -->
        <div class="reservation-header">
            <div class="header-title">图书馆座位预约</div>
            <div class="header-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalSeats">0</div>
                    <div class="stat-label">总座位</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="availableSeats">0</div>
                    <div class="stat-label">可预约</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="myReservations">0</div>
                    <div class="stat-label">我的预约</div>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">选择楼层</div>
                    <select class="filter-select" id="floorSelect">
                        <option value="">全部楼层</option>
                    </select>
                </div>
                <div class="filter-item">
                    <div class="filter-label">选择区域</div>
                    <select class="filter-select" id="areaSelect">
                        <option value="">全部区域</option>
                    </select>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">预约日期</div>
                    <input type="date" class="filter-select" id="reservationDate">
                </div>
                <div class="filter-item">
                    <div class="filter-label">时间段</div>
                    <div class="time-picker">
                        <input type="time" class="time-input" id="startTime" value="08:00">
                        <input type="time" class="time-input" id="endTime" value="18:00">
                    </div>
                </div>
            </div>

            <button class="btn-search" onclick="searchSeats();">查找座位</button>
        </div>

        <!-- 座位地图 -->
        <div class="seat-map" id="seatMap" style="display: none;">
            <div class="map-title" id="mapTitle">座位分布图</div>

            <!-- 图例 -->
            <div class="seat-legend">
                <div class="legend-item">
                    <div class="legend-color legend-available"></div>
                    <span>可预约</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-occupied"></div>
                    <span>已占用</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-selected"></div>
                    <span>已选择</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-reserved"></div>
                    <span>我的预约</span>
                </div>
            </div>

            <!-- 座位网格 -->
            <div class="seat-grid" id="seatGrid">
                <!-- 座位将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 座位信息 -->
        <div class="seat-info" id="seatInfo">
            <div class="info-title" id="seatTitle">座位信息</div>
            <div class="info-details">
                <div class="info-item">
                    <span>座位号:</span>
                    <span id="seatNumber">-</span>
                </div>
                <div class="info-item">
                    <span>楼层:</span>
                    <span id="seatFloor">-</span>
                </div>
                <div class="info-item">
                    <span>区域:</span>
                    <span id="seatArea">-</span>
                </div>
                <div class="info-item">
                    <span>设施:</span>
                    <span id="seatFacilities">-</span>
                </div>
            </div>
            <div class="reservation-actions">
                <button class="btn-mobile btn-reserve flex-1" onclick="reserveSeat();" id="btnReserve">预约座位</button>
                <button class="btn-mobile btn-extend flex-1" onclick="extendReservation();" id="btnExtend" style="display: none;">延长预约</button>
                <button class="btn-mobile btn-cancel flex-1" onclick="cancelReservation();" id="btnCancel" style="display: none;">取消预约</button>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-title">快捷操作</div>
            <div class="action-buttons">
                <div class="action-btn" onclick="quickReserve();">
                    <div class="action-icon">
                        <i class="ace-icon fa fa-bolt"></i>
                    </div>
                    <div class="action-text">快速预约</div>
                </div>
                <div class="action-btn" onclick="showMyReservations();">
                    <div class="action-icon">
                        <i class="ace-icon fa fa-list"></i>
                    </div>
                    <div class="action-text">我的预约</div>
                </div>
                <div class="action-btn" onclick="checkIn();">
                    <div class="action-icon">
                        <i class="ace-icon fa fa-check-in"></i>
                    </div>
                    <div class="action-text">签到入座</div>
                </div>
                <div class="action-btn" onclick="checkOut();">
                    <div class="action-icon">
                        <i class="ace-icon fa fa-sign-out"></i>
                    </div>
                    <div class="action-text">签退离座</div>
                </div>
            </div>
        </div>

        <!-- 我的预约记录 -->
        <div class="my-reservations">
            <div class="reservations-header">
                <i class="ace-icon fa fa-calendar"></i>
                <span>我的预约</span>
            </div>
            <div id="reservationList">
                <!-- 预约记录将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 预约规则 -->
        <div class="rules-section">
            <div class="rules-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>预约规则</span>
            </div>
            <div class="rules-content">
                <ul class="rules-list">
                    <li>每人每天最多可预约2个座位</li>
                    <li>预约时间最长不超过4小时</li>
                    <li>预约后请在15分钟内到达并签到</li>
                    <li>离开超过30分钟座位将被释放</li>
                    <li>恶意占座将被限制预约权限</li>
                </ul>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-calendar-times-o"></i>
            <div>暂无可预约座位</div>
        </div>
    </div>

    <script>
        // 全局变量
        let floors = [];
        let areas = [];
        let seats = [];
        let selectedSeat = null;
        let myReservations = [];
        let currentFilter = {};

        $(function() {
            initPage();
            loadInitialData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            $('#reservationDate').val(today);
        }

        // 加载初始数据
        function loadInitialData() {
            showLoading(true);

            Promise.all([
                loadFloors(),
                loadAreas(),
                loadMyReservations(),
                loadStatistics()
            ]).then(() => {
                showLoading(false);
            }).catch(() => {
                showLoading(false);
                showError('加载数据失败');
            });
        }

        // 加载楼层信息
        function loadFloors() {
            return $.ajax({
                url: "/student/library/seatReservation/getFloors",
                type: "post",
                dataType: "json",
                success: function(data) {
                    floors = data || [];
                    renderFloorOptions();
                }
            });
        }

        // 渲染楼层选项
        function renderFloorOptions() {
            const select = $('#floorSelect');
            select.find('option:not(:first)').remove();

            floors.forEach(floor => {
                select.append(`<option value="${floor.id}">${floor.name}</option>`);
            });
        }

        // 加载区域信息
        function loadAreas() {
            return $.ajax({
                url: "/student/library/seatReservation/getAreas",
                type: "post",
                dataType: "json",
                success: function(data) {
                    areas = data || [];
                    renderAreaOptions();
                }
            });
        }

        // 渲染区域选项
        function renderAreaOptions() {
            const select = $('#areaSelect');
            select.find('option:not(:first)').remove();

            areas.forEach(area => {
                select.append(`<option value="${area.id}">${area.name}</option>`);
            });
        }

        // 加载统计信息
        function loadStatistics() {
            return $.ajax({
                url: "/student/library/seatReservation/getStatistics",
                type: "post",
                dataType: "json",
                success: function(data) {
                    updateStatistics(data);
                }
            });
        }

        // 更新统计信息
        function updateStatistics(stats) {
            if (!stats) return;

            $('#totalSeats').text(stats.totalSeats || 0);
            $('#availableSeats').text(stats.availableSeats || 0);
            $('#myReservations').text(stats.myReservations || 0);
        }

        // 搜索座位
        function searchSeats() {
            const filter = {
                floor: $('#floorSelect').val(),
                area: $('#areaSelect').val(),
                date: $('#reservationDate').val(),
                startTime: $('#startTime').val(),
                endTime: $('#endTime').val()
            };

            if (!filter.date) {
                showError('请选择预约日期');
                return;
            }

            if (!filter.startTime || !filter.endTime) {
                showError('请选择时间段');
                return;
            }

            if (filter.startTime >= filter.endTime) {
                showError('结束时间必须大于开始时间');
                return;
            }

            currentFilter = filter;
            loadSeats(filter);
        }

        // 加载座位数据
        function loadSeats(filter) {
            showLoading(true);

            $.ajax({
                url: "/student/library/seatReservation/getSeats",
                type: "post",
                data: filter,
                dataType: "json",
                success: function(data) {
                    seats = data.seats || [];
                    renderSeatMap();
                    updateMapTitle(filter);
                    showLoading(false);

                    if (seats.length === 0) {
                        $('#emptyState').show();
                        $('#seatMap').hide();
                    } else {
                        $('#emptyState').hide();
                        $('#seatMap').show();
                    }
                },
                error: function() {
                    showError('加载座位信息失败');
                    showLoading(false);
                }
            });
        }

        // 渲染座位地图
        function renderSeatMap() {
            const container = $('#seatGrid');
            container.empty();

            // 根据座位数量调整网格列数
            const columns = Math.min(Math.ceil(Math.sqrt(seats.length)), 10);
            container.css('grid-template-columns', `repeat(${columns}, 1fr)`);

            seats.forEach(seat => {
                const seatElement = createSeatElement(seat);
                container.append(seatElement);
            });
        }

        // 创建座位元素
        function createSeatElement(seat) {
            let seatClass = 'seat';

            switch(seat.status) {
                case 'available':
                    seatClass += ' available';
                    break;
                case 'occupied':
                    seatClass += ' occupied';
                    break;
                case 'reserved':
                    seatClass += ' reserved';
                    break;
            }

            const seatElement = $(`
                <div class="${seatClass}" data-seat-id="${seat.id}" onclick="selectSeat('${seat.id}')">
                    ${seat.number}
                </div>
            `);

            return seatElement;
        }

        // 选择座位
        function selectSeat(seatId) {
            const seat = seats.find(s => s.id === seatId);
            if (!seat || seat.status === 'occupied') return;

            // 清除之前的选择
            $('.seat').removeClass('selected');
            selectedSeat = seat;

            // 标记当前选择
            $(`.seat[data-seat-id="${seatId}"]`).addClass('selected');

            // 显示座位信息
            showSeatInfo(seat);
        }

        // 显示座位信息
        function showSeatInfo(seat) {
            $('#seatNumber').text(seat.number);
            $('#seatFloor').text(seat.floorName);
            $('#seatArea').text(seat.areaName);
            $('#seatFacilities').text(seat.facilities || '基础设施');

            // 根据座位状态显示不同按钮
            $('#btnReserve, #btnExtend, #btnCancel').hide();

            if (seat.status === 'available') {
                $('#btnReserve').show();
            } else if (seat.status === 'reserved' && seat.isMyReservation) {
                $('#btnExtend, #btnCancel').show();
            }

            $('#seatInfo').show();

            // 滚动到座位信息
            $('html, body').animate({
                scrollTop: $('#seatInfo').offset().top - 100
            }, 300);
        }

        // 更新地图标题
        function updateMapTitle(filter) {
            let title = '座位分布图';

            if (filter.floor) {
                const floor = floors.find(f => f.id === filter.floor);
                if (floor) title += ` - ${floor.name}`;
            }

            if (filter.area) {
                const area = areas.find(a => a.id === filter.area);
                if (area) title += ` - ${area.name}`;
            }

            $('#mapTitle').text(title);
        }

        // 预约座位
        function reserveSeat() {
            if (!selectedSeat) {
                showError('请先选择座位');
                return;
            }

            const reservationData = {
                seatId: selectedSeat.id,
                date: currentFilter.date,
                startTime: currentFilter.startTime,
                endTime: currentFilter.endTime
            };

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要预约座位 ${selectedSeat.number} 吗？\n时间：${reservationData.date} ${reservationData.startTime}-${reservationData.endTime}`, function(confirmed) {
                    if (confirmed) {
                        doReserveSeat(reservationData);
                    }
                });
            } else {
                if (confirm(`确定要预约座位 ${selectedSeat.number} 吗？\n时间：${reservationData.date} ${reservationData.startTime}-${reservationData.endTime}`)) {
                    doReserveSeat(reservationData);
                }
            }
        }

        // 执行预约
        function doReserveSeat(reservationData) {
            $.ajax({
                url: "/student/library/seatReservation/reserve",
                type: "post",
                data: reservationData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('预约成功');
                        refreshData();
                        $('#seatInfo').hide();
                    } else {
                        showError(data.message || '预约失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 延长预约
        function extendReservation() {
            if (!selectedSeat || !selectedSeat.reservationId) {
                showError('无法延长预约');
                return;
            }

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要延长预约时间吗？', function(confirmed) {
                    if (confirmed) {
                        doExtendReservation(selectedSeat.reservationId);
                    }
                });
            } else {
                if (confirm('确定要延长预约时间吗？')) {
                    doExtendReservation(selectedSeat.reservationId);
                }
            }
        }

        // 执行延长预约
        function doExtendReservation(reservationId) {
            $.ajax({
                url: "/student/library/seatReservation/extend",
                type: "post",
                data: { reservationId: reservationId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('延长成功');
                        refreshData();
                        $('#seatInfo').hide();
                    } else {
                        showError(data.message || '延长失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 取消预约
        function cancelReservation() {
            if (!selectedSeat || !selectedSeat.reservationId) {
                showError('无法取消预约');
                return;
            }

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要取消预约吗？', function(confirmed) {
                    if (confirmed) {
                        doCancelReservation(selectedSeat.reservationId);
                    }
                });
            } else {
                if (confirm('确定要取消预约吗？')) {
                    doCancelReservation(selectedSeat.reservationId);
                }
            }
        }

        // 执行取消预约
        function doCancelReservation(reservationId) {
            $.ajax({
                url: "/student/library/seatReservation/cancel",
                type: "post",
                data: { reservationId: reservationId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('取消成功');
                        refreshData();
                        $('#seatInfo').hide();
                    } else {
                        showError(data.message || '取消失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 快速预约
        function quickReserve() {
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentHour = now.getHours();
            const startTime = String(currentHour).padStart(2, '0') + ':00';
            const endTime = String(Math.min(currentHour + 2, 22)).padStart(2, '0') + ':00';

            $('#reservationDate').val(today);
            $('#startTime').val(startTime);
            $('#endTime').val(endTime);

            // 自动搜索可用座位
            searchSeats();
        }

        // 加载我的预约
        function loadMyReservations() {
            return $.ajax({
                url: "/student/library/seatReservation/getMyReservations",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myReservations = data || [];
                    renderMyReservations();
                }
            });
        }

        // 渲染我的预约
        function renderMyReservations() {
            const container = $('#reservationList');
            container.empty();

            if (myReservations.length === 0) {
                container.html('<div style="padding: var(--padding-lg); text-align: center; color: var(--text-secondary);">暂无预约记录</div>');
                return;
            }

            myReservations.forEach(reservation => {
                const reservationHtml = createReservationItem(reservation);
                container.append(reservationHtml);
            });
        }

        // 创建预约记录项
        function createReservationItem(reservation) {
            const statusClass = getReservationStatusClass(reservation.status);
            const statusText = getReservationStatusText(reservation.status);

            return `
                <div class="reservation-item" onclick="showReservationDetail('${reservation.id}')">
                    <div class="reservation-header">
                        <div class="reservation-location">
                            ${reservation.floorName} - ${reservation.areaName} - 座位${reservation.seatNumber}
                        </div>
                        <div class="reservation-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="reservation-details">
                        预约时间：${reservation.date} ${reservation.startTime}-${reservation.endTime}
                    </div>
                    <div class="reservation-time">
                        预约于：${reservation.createTime}
                    </div>
                </div>
            `;
        }

        // 获取预约状态样式类
        function getReservationStatusClass(status) {
            switch(status) {
                case 'active': return 'status-active';
                case 'upcoming': return 'status-upcoming';
                case 'expired': return 'status-expired';
                default: return 'status-upcoming';
            }
        }

        // 获取预约状态文本
        function getReservationStatusText(status) {
            switch(status) {
                case 'active': return '使用中';
                case 'upcoming': return '未开始';
                case 'expired': return '已过期';
                default: return '未知';
            }
        }

        // 显示预约详情
        function showReservationDetail(reservationId) {
            const reservation = myReservations.find(r => r.id === reservationId);
            if (!reservation) return;

            let message = `预约详情\n\n`;
            message += `座位位置：${reservation.floorName} - ${reservation.areaName} - 座位${reservation.seatNumber}\n`;
            message += `预约时间：${reservation.date} ${reservation.startTime}-${reservation.endTime}\n`;
            message += `预约状态：${getReservationStatusText(reservation.status)}\n`;
            message += `预约时间：${reservation.createTime}\n`;

            if (reservation.checkInTime) {
                message += `签到时间：${reservation.checkInTime}\n`;
            }

            if (reservation.checkOutTime) {
                message += `签退时间：${reservation.checkOutTime}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示我的预约
        function showMyReservations() {
            $('html, body').animate({
                scrollTop: $('.my-reservations').offset().top - 100
            }, 300);
        }

        // 签到入座
        function checkIn() {
            // 获取当前活跃的预约
            const activeReservation = myReservations.find(r => r.status === 'upcoming' || r.status === 'active');

            if (!activeReservation) {
                showError('您当前没有可签到的预约');
                return;
            }

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要为座位 ${activeReservation.seatNumber} 签到吗？`, function(confirmed) {
                    if (confirmed) {
                        doCheckIn(activeReservation.id);
                    }
                });
            } else {
                if (confirm(`确定要为座位 ${activeReservation.seatNumber} 签到吗？`)) {
                    doCheckIn(activeReservation.id);
                }
            }
        }

        // 执行签到
        function doCheckIn(reservationId) {
            $.ajax({
                url: "/student/library/seatReservation/checkIn",
                type: "post",
                data: { reservationId: reservationId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('签到成功');
                        loadMyReservations();
                    } else {
                        showError(data.message || '签到失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 签退离座
        function checkOut() {
            // 获取当前使用中的预约
            const activeReservation = myReservations.find(r => r.status === 'active');

            if (!activeReservation) {
                showError('您当前没有使用中的座位');
                return;
            }

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要为座位 ${activeReservation.seatNumber} 签退吗？`, function(confirmed) {
                    if (confirmed) {
                        doCheckOut(activeReservation.id);
                    }
                });
            } else {
                if (confirm(`确定要为座位 ${activeReservation.seatNumber} 签退吗？`)) {
                    doCheckOut(activeReservation.id);
                }
            }
        }

        // 执行签退
        function doCheckOut(reservationId) {
            $.ajax({
                url: "/student/library/seatReservation/checkOut",
                type: "post",
                data: { reservationId: reservationId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('签退成功');
                        loadMyReservations();
                    } else {
                        showError(data.message || '签退失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 刷新数据
        function refreshData() {
            loadInitialData();

            // 如果有当前筛选条件，重新搜索
            if (Object.keys(currentFilter).length > 0) {
                loadSeats(currentFilter);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
