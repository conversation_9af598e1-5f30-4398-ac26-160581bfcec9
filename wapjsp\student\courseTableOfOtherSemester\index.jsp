<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>历史课程表</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 历史课程表页面样式 */
        .semester-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .semester-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .semester-item {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            background: var(--bg-primary);
        }
        
        .semester-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .semester-item.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }
        
        .semester-name {
            font-size: var(--font-size-small);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .semester-year {
            font-size: var(--font-size-mini);
            opacity: 0.8;
        }
        
        .timetable-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .view-mode-selector {
            display: flex;
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin: var(--margin-md);
            padding: 4px;
        }
        
        .view-mode-btn {
            flex: 1;
            padding: 8px 16px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            border-radius: 4px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .view-mode-btn.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
        }
        
        /* 表格视图 */
        .timetable {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--font-size-small);
        }
        
        .timetable th,
        .timetable td {
            border: 1px solid var(--divider-color);
            padding: 4px;
            text-align: center;
            vertical-align: top;
            height: 60px;
        }
        
        .timetable th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
            height: 40px;
        }
        
        .time-slot {
            background: var(--bg-tertiary);
            font-weight: 500;
            width: 60px;
            font-size: var(--font-size-mini);
        }
        
        .course-item {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-radius: 4px;
            padding: 4px;
            margin: 1px;
            font-size: 10px;
            line-height: 1.2;
            cursor: pointer;
            transition: all var(--transition-base);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .course-item:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
        
        /* 列表视图 */
        .day-view {
            display: none;
        }
        
        .day-view.active {
            display: block;
        }
        
        .day-selector {
            display: flex;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
        }
        
        .day-btn {
            flex: 0 0 auto;
            padding: 12px 16px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-base);
            white-space: nowrap;
        }
        
        .day-btn.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .day-course-list {
            padding: var(--padding-md);
        }
        
        .day-course-item {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .day-course-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .course-time {
            font-size: var(--font-size-small);
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .course-name {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .course-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
        }
        
        .course-location {
            display: flex;
            align-items: center;
        }
        
        .course-location i {
            margin-right: 4px;
        }
        
        .semester-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .export-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .export-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .export-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-export {
            flex: 1;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-export:hover {
            background: var(--primary-dark);
        }
        
        .btn-export i {
            margin-right: var(--margin-xs);
        }
        
        /* 响应式优化 */
        @media (max-width: 480px) {
            .timetable th,
            .timetable td {
                height: 50px;
                font-size: 10px;
            }
            
            .course-item {
                font-size: 9px;
                padding: 2px;
            }
            
            .time-slot {
                width: 50px;
                font-size: 9px;
            }
            
            .semester-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">历史课程表</div>
            <div class="navbar-action" onclick="refreshTimetable();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学期选择器 -->
        <div class="semester-selector">
            <div class="selector-title">选择学期</div>
            <div class="semester-grid" id="semesterGrid">
                <!-- 学期选项将通过JavaScript动态填充 -->
            </div>
        </div>
        
        <!-- 学期统计 -->
        <div class="semester-summary" id="semesterSummary" style="display: none;">
            <div class="summary-title" id="summaryTitle">学期统计</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalCourses">0</div>
                    <div class="stat-label">总课程</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalCredits">0</div>
                    <div class="stat-label">总学分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="weeklyHours">0</div>
                    <div class="stat-label">周学时</div>
                </div>
            </div>
        </div>
        
        <!-- 视图模式选择器 -->
        <div class="view-mode-selector" id="viewModeSelector" style="display: none;">
            <button class="view-mode-btn active" onclick="switchView('table')">表格视图</button>
            <button class="view-mode-btn" onclick="switchView('list')">列表视图</button>
        </div>
        
        <!-- 表格视图 -->
        <div class="timetable-container" id="tableView" style="display: none;">
            <table class="timetable" id="mycoursetable">
                <thead>
                    <tr>
                        <th class="time-slot">时间</th>
                        <th>周一</th>
                        <th>周二</th>
                        <th>周三</th>
                        <th>周四</th>
                        <th>周五</th>
                        <th>周六</th>
                        <th>周日</th>
                    </tr>
                </thead>
                <tbody id="timetableBody">
                    <!-- 课程表内容将通过JavaScript填充 -->
                </tbody>
            </table>
        </div>
        
        <!-- 列表视图 -->
        <div class="day-view" id="listView">
            <!-- 日期选择器 -->
            <div class="day-selector" id="daySelector">
                <button class="day-btn active" data-day="1">周一</button>
                <button class="day-btn" data-day="2">周二</button>
                <button class="day-btn" data-day="3">周三</button>
                <button class="day-btn" data-day="4">周四</button>
                <button class="day-btn" data-day="5">周五</button>
                <button class="day-btn" data-day="6">周六</button>
                <button class="day-btn" data-day="7">周日</button>
            </div>
            
            <!-- 每日课程列表 -->
            <div class="day-course-list" id="dayCourseList">
                <!-- 课程列表内容 -->
            </div>
        </div>
        
        <!-- 导出功能 -->
        <div class="export-section" id="exportSection" style="display: none;">
            <div class="export-title">导出课程表</div>
            <div class="export-buttons">
                <button class="btn-export" onclick="exportTimetable('pdf');">
                    <i class="ace-icon fa fa-file-pdf-o"></i>
                    <span>导出PDF</span>
                </button>
                <button class="btn-export" onclick="exportTimetable('excel');">
                    <i class="ace-icon fa fa-file-excel-o"></i>
                    <span>导出Excel</span>
                </button>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-calendar-o"></i>
            <div>请选择学期查看课程表</div>
        </div>
    </div>

    <script>
        // 全局变量
        let semesters = [];
        let currentSemester = null;
        let courseData = [];
        let currentView = 'table';
        let currentDay = 1;
        let timeSlots = [
            '第1-2节\n08:00-09:40',
            '第3-4节\n10:00-11:40', 
            '第5-6节\n14:00-15:40',
            '第7-8节\n16:00-17:40',
            '第9-10节\n19:00-20:40'
        ];

        $(function() {
            initPage();
            loadSemesters();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 日期选择器事件
            $('.day-btn').click(function() {
                $('.day-btn').removeClass('active');
                $(this).addClass('active');
                currentDay = parseInt($(this).data('day'));
                renderDayView();
            });
        }

        // 加载学期列表
        function loadSemesters() {
            showLoading(true);
            
            $.ajax({
                url: "/student/courseTableOfOtherSemester/getSemesters",
                type: "post",
                dataType: "json",
                success: function(data) {
                    semesters = data || [];
                    renderSemesters();
                    showLoading(false);
                },
                error: function() {
                    showError('加载学期列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染学期选择器
        function renderSemesters() {
            const container = $('#semesterGrid');
            container.empty();
            
            if (semesters.length === 0) {
                $('#emptyState').show();
                return;
            }
            
            semesters.forEach(semester => {
                const semesterHtml = `
                    <div class="semester-item" onclick="selectSemester('${semester.id}')">
                        <div class="semester-name">${semester.name}</div>
                        <div class="semester-year">${semester.year}</div>
                    </div>
                `;
                container.append(semesterHtml);
            });
        }

        // 选择学期
        function selectSemester(semesterId) {
            const semester = semesters.find(s => s.id === semesterId);
            if (!semester) return;
            
            currentSemester = semester;
            
            // 更新选中状态
            $('.semester-item').removeClass('active');
            $(event.target).closest('.semester-item').addClass('active');
            
            // 加载该学期的课程表
            loadTimetable(semesterId);
        }

        // 加载课程表数据
        function loadTimetable(semesterId) {
            showLoading(true);
            
            $.ajax({
                url: "/student/courseTableOfOtherSemester/getTimetable",
                type: "post",
                data: { semesterId: semesterId },
                dataType: "json",
                success: function(data) {
                    try {
                        courseData = data.courses || [];
                        
                        // 显示相关组件
                        $('#semesterSummary').show();
                        $('#viewModeSelector').show();
                        $('#exportSection').show();
                        
                        // 更新统计信息
                        updateSummary(data.statistics);
                        
                        // 渲染课程表
                        renderTableView();
                        renderDayView();
                        
                        // 显示表格视图
                        switchView('table');
                        
                        showLoading(false);
                    } catch (error) {
                        console.error('解析课程数据失败:', error);
                        showError('加载课程表失败');
                        showLoading(false);
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                    showLoading(false);
                }
            });
        }

        // 更新统计信息
        function updateSummary(statistics) {
            if (!statistics) return;
            
            $('#summaryTitle').text(currentSemester.name + ' 统计');
            $('#totalCourses').text(statistics.totalCourses || 0);
            $('#totalCredits').text(statistics.totalCredits || 0);
            $('#weeklyHours').text(statistics.weeklyHours || 0);
        }

        // 渲染表格视图
        function renderTableView() {
            const tbody = $('#timetableBody');
            tbody.empty();
            
            // 创建时间段行
            timeSlots.forEach((timeSlot, slotIndex) => {
                const row = $('<tr></tr>');
                
                // 时间列
                const timeCell = $(`<td class="time-slot">${timeSlot}</td>`);
                row.append(timeCell);
                
                // 星期列
                for (let day = 1; day <= 7; day++) {
                    const dayCell = $('<td></td>');
                    const coursesInSlot = getCoursesByDayAndSlot(day, slotIndex + 1);
                    
                    coursesInSlot.forEach(course => {
                        const courseElement = createCourseElement(course);
                        dayCell.append(courseElement);
                    });
                    
                    row.append(dayCell);
                }
                
                tbody.append(row);
            });
            
            $('#tableView').show();
        }

        // 渲染列表视图
        function renderDayView() {
            const container = $('#dayCourseList');
            container.empty();
            
            const coursesInDay = getCoursesByDay(currentDay);
            
            if (coursesInDay.length === 0) {
                container.html(`
                    <div class="empty-state">
                        <i class="ace-icon fa fa-calendar-o"></i>
                        <div>今天没有课程安排</div>
                    </div>
                `);
                return;
            }
            
            coursesInDay.forEach(course => {
                const courseHtml = createDayCourseItem(course);
                container.append(courseHtml);
            });
        }

        // 获取指定日期和时间段的课程
        function getCoursesByDayAndSlot(day, slot) {
            return courseData.filter(course => {
                return course.xqj == day && isInTimeSlot(course.jc, slot);
            });
        }

        // 获取指定日期的所有课程
        function getCoursesByDay(day) {
            return courseData.filter(course => course.xqj == day)
                           .sort((a, b) => a.jc - b.jc);
        }

        // 判断节次是否在时间段内
        function isInTimeSlot(jc, slot) {
            const slotRanges = [
                [1, 2], [3, 4], [5, 6], [7, 8], [9, 10]
            ];
            const range = slotRanges[slot - 1];
            return jc >= range[0] && jc <= range[1];
        }

        // 创建课程元素
        function createCourseElement(course) {
            const element = $(`
                <div class="course-item" onclick="showCourseDetail('${course.jxb_id}')">
                    ${course.kcmc}
                </div>
            `);
            return element;
        }

        // 创建日视图课程项
        function createDayCourseItem(course) {
            const timeInfo = getTimeSlotInfo(course.jc);
            
            return `
                <div class="day-course-item" onclick="showCourseDetail('${course.jxb_id}')">
                    <div class="course-time">${timeInfo}</div>
                    <div class="course-name">${course.kcmc}</div>
                    <div class="course-info">
                        <div class="course-location">
                            <i class="ace-icon fa fa-map-marker"></i>
                            <span>${course.jsmc}</span>
                        </div>
                        <div class="course-teacher">${course.xm}</div>
                    </div>
                </div>
            `;
        }

        // 获取时间段信息
        function getTimeSlotInfo(jc) {
            const timeMap = {
                1: '08:00-08:45', 2: '08:55-09:40',
                3: '10:00-10:45', 4: '10:55-11:40',
                5: '14:00-14:45', 6: '14:55-15:40',
                7: '16:00-16:45', 8: '16:55-17:40',
                9: '19:00-19:45', 10: '19:55-20:40'
            };
            return `第${jc}节 ${timeMap[jc] || ''}`;
        }

        // 显示课程详情
        function showCourseDetail(courseId) {
            const course = courseData.find(c => c.jxb_id === courseId);
            if (!course) return;
            
            let message = `课程名称：${course.kcmc}\n`;
            message += `授课教师：${course.xm}\n`;
            message += `上课地点：${course.jsmc}\n`;
            message += `上课时间：周${course.xqj} ${getTimeSlotInfo(course.jc)}\n`;
            message += `课程号：${course.kch}\n`;
            message += `学分：${course.xf}\n`;
            
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 切换视图模式
        function switchView(viewType) {
            currentView = viewType;
            
            // 更新按钮状态
            $('.view-mode-btn').removeClass('active');
            $(event.target).addClass('active');
            
            // 切换视图
            if (viewType === 'table') {
                $('#tableView').show();
                $('#listView').removeClass('active');
            } else {
                $('#tableView').hide();
                $('#listView').addClass('active');
                renderDayView();
            }
        }

        // 导出课程表
        function exportTimetable(format) {
            if (!currentSemester) {
                showError('请先选择学期');
                return;
            }
            
            const url = `/student/courseTableOfOtherSemester/export?semesterId=${currentSemester.id}&format=${format}`;
            window.open(url);
        }

        // 刷新课程表
        function refreshTimetable() {
            if (currentSemester) {
                loadTimetable(currentSemester.id);
            } else {
                loadSemesters();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#semesterSummary, #viewModeSelector, #tableView, #listView, #exportSection').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
