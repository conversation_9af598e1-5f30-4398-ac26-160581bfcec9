<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>研究生入学考试</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 研究生入学考试页面样式 */
        .graduate-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .exam-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .exam-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
        }
        
        .exam-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .exam-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .info-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .info-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .info-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .info-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .info-number.registration {
            color: var(--info-color);
        }
        
        .info-number.exam {
            color: var(--warning-color);
        }
        
        .info-number.admission {
            color: var(--success-color);
        }
        
        .info-number.score {
            color: var(--primary-color);
        }
        
        .info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .exam-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .exam-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .exam-item:last-child {
            border-bottom: none;
        }
        
        .exam-item:active {
            background: var(--bg-color-active);
        }
        
        .exam-item.registration {
            border-left: 4px solid var(--info-color);
        }
        
        .exam-item.exam {
            border-left: 4px solid var(--warning-color);
        }
        
        .exam-item.admission {
            border-left: 4px solid var(--success-color);
        }
        
        .exam-item.score {
            border-left: 4px solid var(--primary-color);
        }
        
        .exam-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .exam-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-registration {
            background: var(--info-color);
            color: white;
        }
        
        .status-exam {
            background: var(--warning-color);
            color: white;
        }
        
        .status-admission {
            background: var(--success-color);
            color: white;
        }
        
        .status-score {
            background: var(--primary-color);
            color: white;
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .exam-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-description {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
        }
        
        .exam-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-register {
            background: var(--success-color);
            color: white;
        }
        
        .btn-download {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .registration-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .registration-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">研究生入学考试</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="graduate-header">
            <div class="header-title">研究生入学考试</div>
            <div class="header-subtitle">研究生考试报名和成绩查询</div>
        </div>

        <!-- 考试标签 -->
        <div class="exam-tabs">
            <div class="exam-tab active" data-tab="registration" onclick="switchTab('registration')">报名</div>
            <div class="exam-tab" data-tab="exam" onclick="switchTab('exam')">考试</div>
            <div class="exam-tab" data-tab="score" onclick="switchTab('score')">成绩</div>
            <div class="exam-tab" data-tab="admission" onclick="switchTab('admission')">录取</div>
        </div>

        <!-- 考试信息汇总 -->
        <div class="exam-info">
            <div class="info-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>考试统计</span>
            </div>

            <div class="info-cards">
                <div class="info-card">
                    <div class="info-number registration" id="registrationCount">0</div>
                    <div class="info-label">报名人数</div>
                </div>
                <div class="info-card">
                    <div class="info-number exam" id="examCount">0</div>
                    <div class="info-label">考试场次</div>
                </div>
                <div class="info-card">
                    <div class="info-number score" id="scoreCount">0</div>
                    <div class="info-label">成绩发布</div>
                </div>
                <div class="info-card">
                    <div class="info-number admission" id="admissionCount">0</div>
                    <div class="info-label">录取人数</div>
                </div>
            </div>
        </div>

        <!-- 考试列表 -->
        <div class="exam-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span id="listTitle">报名信息</span>
                </div>
                <div class="list-count" id="itemCount">0</div>
            </div>

            <div id="examItems">
                <!-- 考试列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-graduation-cap"></i>
            <div id="emptyMessage">暂无相关信息</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 报名表单 -->
    <div class="registration-form" id="registrationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeRegistrationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">研究生考试报名</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">身份证号</div>
                    <input type="text" class="form-input" id="idCard" placeholder="请输入身份证号">
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span>报考信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">考试类型</div>
                    <select class="form-input" id="examType">
                        <option value="">请选择考试类型</option>
                        <option value="master">硕士研究生</option>
                        <option value="doctor">博士研究生</option>
                        <option value="mba">MBA</option>
                        <option value="mpa">MPA</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">报考专业</div>
                    <input type="text" class="form-input" id="major" placeholder="请输入报考专业">
                </div>

                <div class="form-group">
                    <div class="form-label">研究方向</div>
                    <input type="text" class="form-input" id="direction" placeholder="请输入研究方向">
                </div>

                <div class="form-group">
                    <div class="form-label">导师选择</div>
                    <select class="form-input" id="supervisor">
                        <option value="">请选择导师</option>
                        <!-- 导师选项将动态填充 -->
                    </select>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>其他信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">毕业院校</div>
                    <input type="text" class="form-input" id="graduateSchool" placeholder="请输入毕业院校">
                </div>

                <div class="form-group">
                    <div class="form-label">毕业专业</div>
                    <input type="text" class="form-input" id="graduateMajor" placeholder="请输入毕业专业">
                </div>

                <div class="form-group">
                    <div class="form-label">英语水平</div>
                    <select class="form-input" id="englishLevel">
                        <option value="">请选择英语水平</option>
                        <option value="cet4">CET-4</option>
                        <option value="cet6">CET-6</option>
                        <option value="toefl">TOEFL</option>
                        <option value="ielts">IELTS</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">备注说明</div>
                    <textarea class="form-input" id="note" placeholder="其他需要说明的情况（选填）" style="min-height: 80px; resize: vertical;"></textarea>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeRegistrationForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitRegistration();">提交报名</button>
        </div>
    </div>

    <script>
        // 全局变量
        let allExams = [];
        let currentTab = 'registration';
        let summaryData = {};
        let currentExam = null;

        $(function() {
            initPage();
            loadSummaryData();
            loadExamData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载汇总数据
        function loadSummaryData() {
            $.ajax({
                url: "/student/graduateEntranceExamination/getSummaryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    summaryData = data.summary || {};
                    renderSummaryData();
                },
                error: function() {
                    console.log('加载汇总数据失败');
                }
            });
        }

        // 渲染汇总数据
        function renderSummaryData() {
            $('#registrationCount').text(summaryData.registrationCount || 0);
            $('#examCount').text(summaryData.examCount || 0);
            $('#scoreCount').text(summaryData.scoreCount || 0);
            $('#admissionCount').text(summaryData.admissionCount || 0);
        }

        // 加载考试数据
        function loadExamData() {
            showLoading(true);

            $.ajax({
                url: "/student/graduateEntranceExamination/getExamData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allExams = data.exams || [];
                    renderExamList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载考试数据失败');
                    showLoading(false);
                }
            });
        }

        // 切换标签
        function switchTab(tab) {
            currentTab = tab;

            // 更新标签状态
            $('.exam-tab').removeClass('active');
            $(`.exam-tab[data-tab="${tab}"]`).addClass('active');

            // 更新列表标题
            const titles = {
                'registration': '报名信息',
                'exam': '考试安排',
                'score': '成绩查询',
                'admission': '录取结果'
            };
            $('#listTitle').text(titles[tab]);

            renderExamList();
        }

        // 渲染考试列表
        function renderExamList() {
            const filteredExams = allExams.filter(exam => {
                return exam.category === currentTab;
            });

            $('#itemCount').text(filteredExams.length);

            const container = $('#examItems');
            container.empty();

            if (filteredExams.length === 0) {
                const messages = {
                    'registration': '暂无报名信息',
                    'exam': '暂无考试安排',
                    'score': '暂无成绩信息',
                    'admission': '暂无录取结果'
                };
                showEmptyState(messages[currentTab]);
                return;
            } else {
                hideEmptyState();
            }

            filteredExams.forEach(exam => {
                const examHtml = createExamItem(exam);
                container.append(examHtml);
            });
        }

        // 创建考试项
        function createExamItem(exam) {
            const category = exam.category || 'registration';
            const statusClass = getStatusClass(category);
            const statusText = getStatusText(category);

            return `
                <div class="exam-item ${category}" onclick="showExamDetail('${exam.id}')">
                    <div class="exam-basic">
                        <div class="exam-name">${exam.name}</div>
                        <div class="exam-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="exam-details">
                        <div class="exam-detail-item">
                            <span>考试类型:</span>
                            <span>${getExamTypeText(exam.type)}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>报考专业:</span>
                            <span>${exam.major || '-'}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>考试时间:</span>
                            <span>${formatDate(exam.examTime)}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>报名截止:</span>
                            <span>${formatDate(exam.deadline)}</span>
                        </div>
                    </div>
                    ${exam.description ? `
                        <div class="exam-description">${exam.description}</div>
                    ` : ''}
                    <div class="exam-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showExamDetail('${exam.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${category === 'registration' && !exam.isRegistered ? `
                            <button class="btn-mobile btn-register" onclick="event.stopPropagation(); showRegistrationForm('${exam.id}');">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>报名</span>
                            </button>
                        ` : category === 'score' && exam.hasScore ? `
                            <button class="btn-mobile btn-download" onclick="event.stopPropagation(); downloadScore('${exam.id}');">
                                <i class="ace-icon fa fa-download"></i>
                                <span>下载</span>
                            </button>
                        ` : category === 'admission' && exam.hasAdmission ? `
                            <button class="btn-mobile btn-download" onclick="event.stopPropagation(); downloadAdmission('${exam.id}');">
                                <i class="ace-icon fa fa-download"></i>
                                <span>录取通知</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-check"></i>
                                <span>已处理</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(category) {
            return `status-${category}`;
        }

        // 获取状态文本
        function getStatusText(category) {
            switch(category) {
                case 'registration': return '报名阶段';
                case 'exam': return '考试阶段';
                case 'score': return '成绩阶段';
                case 'admission': return '录取阶段';
                default: return '未知';
            }
        }

        // 获取考试类型文本
        function getExamTypeText(type) {
            switch(type) {
                case 'master': return '硕士研究生';
                case 'doctor': return '博士研究生';
                case 'mba': return 'MBA';
                case 'mpa': return 'MPA';
                default: return '其他';
            }
        }

        // 显示考试详情
        function showExamDetail(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            let message = `考试详情\n\n`;
            message += `考试名称：${exam.name}\n`;
            message += `考试类型：${getExamTypeText(exam.type)}\n`;
            message += `报考专业：${exam.major || '-'}\n`;
            message += `考试时间：${formatDate(exam.examTime)}\n`;
            message += `报名截止：${formatDate(exam.deadline)}\n`;
            message += `状态：${getStatusText(exam.category)}\n`;

            if (exam.location) {
                message += `考试地点：${exam.location}\n`;
            }

            if (exam.score) {
                message += `考试成绩：${exam.score}分\n`;
            }

            if (exam.admissionResult) {
                message += `录取结果：${exam.admissionResult}\n`;
            }

            if (exam.description) {
                message += `\n说明：${exam.description}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示报名表单
        function showRegistrationForm(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            currentExam = exam;

            // 填充基本信息
            $('#studentId').val('2021001001'); // 模拟数据
            $('#studentName').val('张三'); // 模拟数据

            // 清空表单
            $('#idCard').val('');
            $('#phone').val('');
            $('#examType').val(exam.type || '');
            $('#major').val('');
            $('#direction').val('');
            $('#supervisor').val('');
            $('#graduateSchool').val('');
            $('#graduateMajor').val('');
            $('#englishLevel').val('');
            $('#note').val('');

            $('#formTitle').text(`${exam.name} - 报名`);
            $('#registrationForm').addClass('show');
        }

        // 关闭报名表单
        function closeRegistrationForm() {
            $('#registrationForm').removeClass('show');
            currentExam = null;
        }

        // 提交报名
        function submitRegistration() {
            if (!currentExam) return;

            const formData = {
                examId: currentExam.id,
                idCard: $('#idCard').val().trim(),
                phone: $('#phone').val().trim(),
                examType: $('#examType').val(),
                major: $('#major').val().trim(),
                direction: $('#direction').val().trim(),
                supervisor: $('#supervisor').val(),
                graduateSchool: $('#graduateSchool').val().trim(),
                graduateMajor: $('#graduateMajor').val().trim(),
                englishLevel: $('#englishLevel').val(),
                note: $('#note').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要报名"${currentExam.name}"吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitRegistration(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitRegistration(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.idCard) {
                showError('请输入身份证号');
                return false;
            }

            if (!formData.phone) {
                showError('请输入联系电话');
                return false;
            }

            if (!formData.examType) {
                showError('请选择考试类型');
                return false;
            }

            if (!formData.major) {
                showError('请输入报考专业');
                return false;
            }

            if (!formData.graduateSchool) {
                showError('请输入毕业院校');
                return false;
            }

            // 验证身份证号格式
            const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idCardRegex.test(formData.idCard)) {
                showError('请输入正确的身份证号');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            return true;
        }

        // 执行提交报名
        function doSubmitRegistration(formData) {
            $.ajax({
                url: "/student/graduateEntranceExamination/submitRegistration",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('报名成功');
                        closeRegistrationForm();
                        loadExamData(); // 重新加载数据
                    } else {
                        showError(data.message || '报名失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 下载成绩
        function downloadScore(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            // 创建下载链接
            const link = document.createElement('a');
            link.href = `/student/graduateEntranceExamination/downloadScore?examId=${examId}`;
            link.download = `${exam.name}_成绩单.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showSuccess('成绩单下载成功');
        }

        // 下载录取通知
        function downloadAdmission(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            // 创建下载链接
            const link = document.createElement('a');
            link.href = `/student/graduateEntranceExamination/downloadAdmission?examId=${examId}`;
            link.download = `${exam.name}_录取通知书.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showSuccess('录取通知书下载成功');
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadSummaryData();
            loadExamData();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
