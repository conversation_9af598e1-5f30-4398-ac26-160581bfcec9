<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>证明打印</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 证明打印页面样式 */
        .certificate-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .certificate-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .types-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
        }
        
        .types-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .certificate-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .certificate-item:last-child {
            border-bottom: none;
        }
        
        .certificate-item:active {
            background: var(--bg-color-active);
        }
        
        .certificate-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
        }
        
        .certificate-icon.enrollment {
            background: var(--success-color);
        }
        
        .certificate-icon.study {
            background: var(--info-color);
        }
        
        .certificate-icon.grade {
            background: var(--warning-color);
        }
        
        .certificate-icon.graduation {
            background: var(--primary-color);
        }
        
        .certificate-icon.conduct {
            background: var(--error-color);
        }
        
        .certificate-content {
            flex: 1;
        }
        
        .certificate-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .certificate-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .certificate-arrow {
            color: var(--text-disabled);
            font-size: var(--font-size-base);
        }
        
        .print-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-processing {
            background: var(--warning-color);
            color: white;
        }
        
        .status-failed {
            background: var(--error-color);
            color: white;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .history-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-download {
            background: var(--success-color);
            color: white;
        }
        
        .btn-reprint {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .certificate-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .certificate-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .form-checkbox input {
            margin-right: var(--margin-xs);
        }
        
        .form-checkbox label {
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .preview-section {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .preview-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .preview-content {
            background: white;
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: var(--padding-md);
            font-size: var(--font-size-small);
            line-height: var(--line-height-base);
            color: var(--text-primary);
            min-height: 200px;
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-preview {
            background: var(--info-color);
            color: white;
        }
        
        .btn-print {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">证明打印</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="certificate-header">
            <div class="header-title">证明打印</div>
            <div class="header-subtitle">在线申请和打印各类学籍证明</div>
        </div>

        <!-- 证明类型 -->
        <div class="certificate-types">
            <div class="types-header">
                <i class="ace-icon fa fa-file-text"></i>
                <span>证明类型</span>
            </div>

            <div class="certificate-item" onclick="showCertificateForm('enrollment')">
                <div class="certificate-icon enrollment">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                </div>
                <div class="certificate-content">
                    <div class="certificate-name">在读证明</div>
                    <div class="certificate-desc">证明学生在校学习状态，用于各类申请</div>
                </div>
                <div class="certificate-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="certificate-item" onclick="showCertificateForm('study')">
                <div class="certificate-icon study">
                    <i class="ace-icon fa fa-book"></i>
                </div>
                <div class="certificate-content">
                    <div class="certificate-name">学习证明</div>
                    <div class="certificate-desc">证明学生学习经历和专业信息</div>
                </div>
                <div class="certificate-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="certificate-item" onclick="showCertificateForm('grade')">
                <div class="certificate-icon grade">
                    <i class="ace-icon fa fa-bar-chart"></i>
                </div>
                <div class="certificate-content">
                    <div class="certificate-name">成绩证明</div>
                    <div class="certificate-desc">证明学生各科成绩和学业表现</div>
                </div>
                <div class="certificate-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="certificate-item" onclick="showCertificateForm('graduation')">
                <div class="certificate-icon graduation">
                    <i class="ace-icon fa fa-certificate"></i>
                </div>
                <div class="certificate-content">
                    <div class="certificate-name">毕业证明</div>
                    <div class="certificate-desc">证明学生毕业状态和学位信息</div>
                </div>
                <div class="certificate-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>

            <div class="certificate-item" onclick="showCertificateForm('conduct')">
                <div class="certificate-icon conduct">
                    <i class="ace-icon fa fa-user-check"></i>
                </div>
                <div class="certificate-content">
                    <div class="certificate-name">品行证明</div>
                    <div class="certificate-desc">证明学生在校期间品行表现</div>
                </div>
                <div class="certificate-arrow">
                    <i class="ace-icon fa fa-chevron-right"></i>
                </div>
            </div>
        </div>

        <!-- 打印历史 -->
        <div class="print-history">
            <div class="history-header">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>打印历史</span>
                </div>
                <div class="history-count" id="historyCount">0</div>
            </div>

            <div id="historyItems">
                <!-- 打印历史将动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 证明申请表单 -->
    <div class="certificate-form" id="certificateForm">
        <div class="form-header">
            <div class="form-back" onclick="closeCertificateForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">证明申请</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业</div>
                    <input type="text" class="form-input" id="major" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">班级</div>
                    <input type="text" class="form-input" id="className" readonly>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>证明信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">证明类型</div>
                    <select class="form-input" id="certificateType">
                        <option value="">请选择证明类型</option>
                        <option value="enrollment">在读证明</option>
                        <option value="study">学习证明</option>
                        <option value="grade">成绩证明</option>
                        <option value="graduation">毕业证明</option>
                        <option value="conduct">品行证明</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">申请用途</div>
                    <input type="text" class="form-input" id="purpose" placeholder="请输入证明用途">
                </div>

                <div class="form-group">
                    <div class="form-label">申请份数</div>
                    <select class="form-input" id="copies">
                        <option value="1">1份</option>
                        <option value="2">2份</option>
                        <option value="3">3份</option>
                        <option value="4">4份</option>
                        <option value="5">5份</option>
                    </select>
                </div>

                <div class="form-group" id="languageGroup">
                    <div class="form-label">证明语言</div>
                    <div class="form-checkbox">
                        <input type="checkbox" id="chineseLang" checked>
                        <label for="chineseLang">中文</label>
                    </div>
                    <div class="form-checkbox">
                        <input type="checkbox" id="englishLang">
                        <label for="englishLang">英文</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">备注说明</div>
                    <textarea class="form-input form-textarea" id="note" placeholder="其他需要说明的情况（选填）"></textarea>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-eye"></i>
                    <span>证明预览</span>
                </div>

                <div class="preview-section">
                    <div class="preview-title">证明内容预览</div>
                    <div class="preview-content" id="previewContent">
                        请填写完整信息后点击预览按钮查看证明内容
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeCertificateForm();">取消</button>
            <button class="btn-mobile btn-preview flex-1" onclick="previewCertificate();">预览</button>
            <button class="btn-mobile btn-print flex-1" onclick="submitCertificate();">申请打印</button>
        </div>
    </div>

    <script>
        // 全局变量
        let printHistory = [];
        let currentCertificateType = '';

        $(function() {
            initPage();
            loadPrintHistory();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 证明类型变化事件
            $('#certificateType').change(function() {
                const type = $(this).val();
                updateFormFields(type);
            });
        }

        // 加载打印历史
        function loadPrintHistory() {
            showLoading(true);

            $.ajax({
                url: "/student/service/certificate/getPrintHistory",
                type: "post",
                dataType: "json",
                success: function(data) {
                    printHistory = data.history || [];
                    renderPrintHistory();
                    showLoading(false);
                },
                error: function() {
                    showError('加载打印历史失败');
                    showLoading(false);
                }
            });
        }

        // 渲染打印历史
        function renderPrintHistory() {
            $('#historyCount').text(printHistory.length);

            const container = $('#historyItems');
            container.empty();

            if (printHistory.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无打印记录
                    </div>
                `);
                return;
            }

            printHistory.forEach(item => {
                const historyHtml = createHistoryItem(item);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(item) {
            const status = item.status || 'processing';
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);

            return `
                <div class="history-item" onclick="showHistoryDetail('${item.id}')">
                    <div class="history-basic">
                        <div class="history-name">${getCertificateTypeText(item.type)}</div>
                        <div class="history-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(item.applyTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>申请用途:</span>
                            <span>${item.purpose || '-'}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>申请份数:</span>
                            <span>${item.copies}份</span>
                        </div>
                        <div class="history-detail-item">
                            <span>处理时间:</span>
                            <span>${formatDate(item.processTime)}</span>
                        </div>
                    </div>
                    <div class="history-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showHistoryDetail('${item.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'completed' ? `
                            <button class="btn-mobile btn-download" onclick="event.stopPropagation(); downloadCertificate('${item.id}');">
                                <i class="ace-icon fa fa-download"></i>
                                <span>下载</span>
                            </button>
                            <button class="btn-mobile btn-reprint" onclick="event.stopPropagation(); reprintCertificate('${item.id}');">
                                <i class="ace-icon fa fa-print"></i>
                                <span>重打</span>
                            </button>
                        ` : status === 'failed' ? `
                            <button class="btn-mobile btn-reprint" onclick="event.stopPropagation(); reprintCertificate('${item.id}');">
                                <i class="ace-icon fa fa-refresh"></i>
                                <span>重新申请</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-clock-o"></i>
                                <span>处理中</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'completed': return '已完成';
                case 'processing': return '处理中';
                case 'failed': return '失败';
                default: return '未知';
            }
        }

        // 获取证明类型文本
        function getCertificateTypeText(type) {
            switch(type) {
                case 'enrollment': return '在读证明';
                case 'study': return '学习证明';
                case 'grade': return '成绩证明';
                case 'graduation': return '毕业证明';
                case 'conduct': return '品行证明';
                default: return '其他证明';
            }
        }

        // 显示证明申请表单
        function showCertificateForm(type) {
            currentCertificateType = type;

            // 设置表单标题
            $('#formTitle').text(`${getCertificateTypeText(type)}申请`);

            // 设置证明类型
            $('#certificateType').val(type);

            // 更新表单字段
            updateFormFields(type);

            // 填充基本信息
            fillBasicInfo();

            // 显示表单
            $('#certificateForm').addClass('show');
        }

        // 更新表单字段
        function updateFormFields(type) {
            // 根据证明类型调整表单字段显示
            if (type === 'grade') {
                // 成绩证明可能需要特殊处理
            } else if (type === 'graduation') {
                // 毕业证明可能需要特殊处理
            }
        }

        // 填充基本信息
        function fillBasicInfo() {
            // 这里应该从用户信息中获取，暂时使用模拟数据
            $('#studentId').val('2021001001');
            $('#studentName').val('张三');
            $('#major').val('计算机科学与技术');
            $('#className').val('计科2021-1班');
        }

        // 关闭证明申请表单
        function closeCertificateForm() {
            $('#certificateForm').removeClass('show');
            clearForm();
        }

        // 清空表单
        function clearForm() {
            $('#certificateType').val('');
            $('#purpose').val('');
            $('#copies').val('1');
            $('#chineseLang').prop('checked', true);
            $('#englishLang').prop('checked', false);
            $('#note').val('');
            $('#previewContent').text('请填写完整信息后点击预览按钮查看证明内容');
        }

        // 预览证明
        function previewCertificate() {
            const formData = getFormData();

            if (!validateForm(formData)) {
                return;
            }

            $.ajax({
                url: "/student/service/certificate/previewCertificate",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        $('#previewContent').html(data.content);
                    } else {
                        showError(data.message || '预览失败');
                    }
                },
                error: function() {
                    showError('预览失败，请重试');
                }
            });
        }

        // 提交证明申请
        function submitCertificate() {
            const formData = getFormData();

            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要申请${getCertificateTypeText(formData.type)}吗？\n\n申请用途：${formData.purpose}\n申请份数：${formData.copies}份`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitCertificate(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitCertificate(formData);
                }
            }
        }

        // 获取表单数据
        function getFormData() {
            return {
                type: $('#certificateType').val(),
                purpose: $('#purpose').val().trim(),
                copies: $('#copies').val(),
                chineseLang: $('#chineseLang').is(':checked'),
                englishLang: $('#englishLang').is(':checked'),
                note: $('#note').val().trim()
            };
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.type) {
                showError('请选择证明类型');
                return false;
            }

            if (!formData.purpose) {
                showError('请填写申请用途');
                return false;
            }

            if (!formData.chineseLang && !formData.englishLang) {
                showError('请至少选择一种证明语言');
                return false;
            }

            return true;
        }

        // 执行提交证明申请
        function doSubmitCertificate(formData) {
            $.ajax({
                url: "/student/service/certificate/submitCertificate",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('证明申请提交成功，请等待处理');
                        closeCertificateForm();
                        loadPrintHistory(); // 重新加载打印历史
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示历史详情
        function showHistoryDetail(historyId) {
            const item = printHistory.find(h => h.id === historyId);
            if (!item) return;

            let message = `证明申请详情\n\n`;
            message += `证明类型：${getCertificateTypeText(item.type)}\n`;
            message += `申请状态：${getStatusText(item.status)}\n`;
            message += `申请时间：${formatDate(item.applyTime)}\n`;
            message += `申请用途：${item.purpose}\n`;
            message += `申请份数：${item.copies}份\n`;

            const languages = [];
            if (item.chineseLang) languages.push('中文');
            if (item.englishLang) languages.push('英文');
            message += `证明语言：${languages.join('、')}\n`;

            if (item.processTime) {
                message += `处理时间：${formatDate(item.processTime)}\n`;
            }

            if (item.processComment) {
                message += `处理意见：${item.processComment}\n`;
            }

            if (item.note) {
                message += `备注：${item.note}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 下载证明
        function downloadCertificate(historyId) {
            const item = printHistory.find(h => h.id === historyId);
            if (!item) return;

            // 创建下载链接
            const link = document.createElement('a');
            link.href = `/student/service/certificate/download?id=${historyId}`;
            link.download = `${getCertificateTypeText(item.type)}.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showSuccess('证明下载成功');
        }

        // 重新打印证明
        function reprintCertificate(historyId) {
            const item = printHistory.find(h => h.id === historyId);
            if (!item) return;

            const message = `确定要重新申请${getCertificateTypeText(item.type)}吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doReprintCertificate(historyId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doReprintCertificate(historyId);
                }
            }
        }

        // 执行重新打印
        function doReprintCertificate(historyId) {
            $.ajax({
                url: "/student/service/certificate/reprintCertificate",
                type: "post",
                data: { historyId: historyId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('重新申请提交成功');
                        loadPrintHistory(); // 重新加载打印历史
                    } else {
                        showError(data.message || '重新申请失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadPrintHistory();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
