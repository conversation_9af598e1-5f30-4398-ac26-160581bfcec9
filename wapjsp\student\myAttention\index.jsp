<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的关注</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 我的关注页面样式 */
        .attention-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .attention-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .attention-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .overview-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .card-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: 16px;
            color: white;
        }
        
        .card-icon.notice {
            background: var(--warning-color);
        }
        
        .card-icon.academic {
            background: var(--success-color);
        }
        
        .card-icon.course {
            background: var(--info-color);
        }
        
        .card-icon.download {
            background: var(--primary-color);
        }
        
        .card-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .notice-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .notice-item {
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .notice-item:hover {
            background: var(--bg-secondary);
        }
        
        .notice-item:last-child {
            margin-bottom: 0;
        }
        
        .notice-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            line-height: 1.4;
        }
        
        .academic-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .stat-group {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .stat-value {
            font-size: var(--font-size-h2);
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .academic-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-top: var(--margin-md);
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--margin-sm);
            font-size: var(--font-size-small);
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            color: var(--text-secondary);
        }
        
        .info-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .course-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .course-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--info-color);
        }
        
        .course-item:last-child {
            margin-bottom: 0;
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .course-credit {
            background: var(--info-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            white-space: nowrap;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .course-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .download-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .download-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
        }
        
        .download-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .download-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--primary-light);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin: 0 auto var(--margin-sm);
        }
        
        .download-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .actions-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--warning-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: 16px;
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
        }
        
        .action-item:hover {
            border-color: var(--warning-color);
            transform: translateY(-2px);
        }
        
        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--warning-light);
            color: var(--warning-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto var(--margin-sm);
        }
        
        .action-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        @media (max-width: 480px) {
            .academic-stats {
                grid-template-columns: 1fr;
            }
            
            .course-details {
                grid-template-columns: 1fr;
            }
            
            .download-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">我的关注</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 我的关注头部 -->
        <div class="attention-header">
            <div class="attention-title">我的关注</div>
            <div class="attention-desc">学业概览与重要信息</div>
        </div>
        
        <!-- 概览卡片 -->
        <div class="overview-grid">
            <!-- 通知公告 -->
            <div class="overview-card">
                <div class="card-header">
                    <div class="card-icon notice">
                        <i class="ace-icon fa fa-bullhorn"></i>
                    </div>
                    <div class="card-title">通知公告</div>
                </div>
                <div class="notice-list" id="noticeList">
                    <!-- 动态加载通知公告 -->
                </div>
            </div>
            
            <!-- 学业信息 -->
            <div class="overview-card">
                <div class="card-header">
                    <div class="card-icon academic">
                        <i class="ace-icon fa fa-graduation-cap"></i>
                    </div>
                    <div class="card-title">学业信息</div>
                </div>
                <div class="academic-stats">
                    <div class="stat-group">
                        <div class="stat-value" id="totalCourses">29</div>
                        <div class="stat-label">已修门数</div>
                    </div>
                    <div class="stat-group">
                        <div class="stat-value" id="totalCredits">89</div>
                        <div class="stat-label">总学分</div>
                    </div>
                </div>
                <div class="academic-info">
                    <div class="info-row">
                        <span class="info-label">修读年限</span>
                        <span class="info-value" id="studyYears">-</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">要求学分</span>
                        <span class="info-value" id="requiredCredits">-</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">培养方案</span>
                        <span class="info-value" id="cultivationPlan">-</span>
                    </div>
                </div>
            </div>
            
            <!-- 本学期课程 -->
            <div class="overview-card">
                <div class="card-header">
                    <div class="card-icon course">
                        <i class="ace-icon fa fa-table"></i>
                    </div>
                    <div class="card-title">本学期课程</div>
                </div>
                <div class="academic-info">
                    <div class="info-row">
                        <span class="info-label" id="semesterInfo">${dateInfo}</span>
                    </div>
                    <div class="info-row" id="courseStatsInfo">
                        <!-- 动态加载课程统计 -->
                    </div>
                    <div class="info-row" id="evaluationInfo">
                        <!-- 动态加载评估信息 -->
                    </div>
                </div>
                <div class="course-list" id="courseList">
                    <!-- 动态加载课程列表 -->
                </div>
            </div>
            
            <!-- 文件下载 -->
            <div class="overview-card">
                <div class="card-header">
                    <div class="card-icon download">
                        <i class="ace-icon fa fa-download"></i>
                    </div>
                    <div class="card-title">文件下载</div>
                </div>
                <div class="download-grid" id="downloadList">
                    <!-- 动态加载下载文件 -->
                </div>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-header">
                <div class="actions-icon">
                    <i class="ace-icon fa fa-star"></i>
                </div>
                <div class="actions-title">我要</div>
            </div>
            <div class="actions-grid">
                <div class="action-item" onclick="goToAction('courseSelect');">
                    <div class="action-icon">
                        <i class="ace-icon fa fa-plus"></i>
                    </div>
                    <div class="action-name">选课</div>
                </div>
                <div class="action-item" onclick="goToAction('cet');">
                    <div class="action-icon">
                        <i class="ace-icon fa fa-certificate"></i>
                    </div>
                    <div class="action-name">报考四六级</div>
                </div>
                <div class="action-item" onclick="goToAction('innovation');">
                    <div class="action-icon">
                        <i class="ace-icon fa fa-lightbulb-o"></i>
                    </div>
                    <div class="action-name">创新项目</div>
                </div>
                <div class="action-item" onclick="goToAction('competition');">
                    <div class="action-icon">
                        <i class="ace-icon fa fa-trophy"></i>
                    </div>
                    <div class="action-name">科技竞赛</div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let xnxqhs = [];
        <c:forEach items="${semesterCalendarList}" var="semester" varStatus="s">
        xnxqhs['${s.index}'] = "${semester[0]}";
        </c:forEach>
        
        let xtdqxnxq = "${dqxtzxjxjhh}";
        let sydqxnxq = xtdqxnxq;

        $(function() {
            initPage();
            loadAllData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载所有数据
        function loadAllData() {
            showLoading(true);

            Promise.all([
                loadNotices(),
                loadDownloadFiles(),
                loadCultivationPlan(),
                loadCourseInfo(),
                loadCourseStats()
            ]).finally(() => {
                showLoading(false);
            });
        }

        // 加载通知公告
        function loadNotices() {
            return new Promise((resolve) => {
                $.get("/student/overview/attention/myAttention/inform", function(data) {
                    const container = $('#noticeList');
                    container.empty();

                    if (data && Object.keys(data).length > 0) {
                        for (let property in data) {
                            const noticeHtml = `
                                <div class="notice-item" onclick="showNotice('${property}');">
                                    <div class="notice-title">${data[property]}</div>
                                </div>
                            `;
                            container.append(noticeHtml);
                        }
                    } else {
                        container.html('<div class="notice-item"><div class="notice-title">暂无通知公告</div></div>');
                    }
                    resolve();
                }).fail(() => resolve());
            });
        }

        // 显示通知详情
        function showNotice(tzid) {
            const url = "/student/overview/attention/myAttention/inform/" + tzid + "/showInfo";
            if (parent && parent.addTab) {
                parent.addTab("通知详情", url);
            } else {
                window.open(url, 'noticeWindow', 'height=400,width=800,scrollbars=yes,resizable=yes');
            }
        }

        // 加载下载文件
        function loadDownloadFiles() {
            return new Promise((resolve) => {
                $.get("/student/overview/attention/myAttention/downFiles", function(data) {
                    const container = $('#downloadList');
                    container.empty();

                    if (data && Object.keys(data).length > 0) {
                        for (let property in data) {
                            const downloadHtml = `
                                <div class="download-item" onclick="downloadFile('${property}');">
                                    <div class="download-icon">
                                        <i class="ace-icon fa fa-file-o"></i>
                                    </div>
                                    <div class="download-name">${data[property]}</div>
                                </div>
                            `;
                            container.append(downloadHtml);
                        }
                    } else {
                        container.html('<div class="download-item"><div class="download-icon"><i class="ace-icon fa fa-file-o"></i></div><div class="download-name">暂无文件</div></div>');
                    }
                    resolve();
                }).fail(() => resolve());
            });
        }

        // 下载文件
        function downloadFile(fjid) {
            window.location.href = "/student/overview/attention/myAttention/downLoadFile?fjid=" + fjid;
        }

        // 加载培养方案信息
        function loadCultivationPlan() {
            return new Promise((resolve) => {
                $.get("/student/overview/attention/myAttention/cultivationPlanInfo", function(data) {
                    if (data) {
                        $('#studyYears').text(data.xzlxdm || '-');
                        $('#requiredCredits').text(data.yqzxf || '-');
                        $('#cultivationPlan').text(data.famc || '-');
                    }
                    resolve();
                }).fail(() => resolve());
            });
        }

        // 加载课程信息
        function loadCourseInfo() {
            return new Promise((resolve) => {
                const container = $('#courseList');
                container.empty();

                <c:forEach items="${courseList}" var="course" varStatus="s">
                const courseHtml = `
                    <div class="course-item">
                        <div class="course-header">
                            <div class="course-name">${course.courseName}</div>
                            <div class="course-credit">${course.unit}学分</div>
                        </div>
                        <div class="course-details">
                            <div class="course-detail-item">
                                <span>课程号</span>
                                <span>${course.id.coureNumber}</span>
                            </div>
                            <div class="course-detail-item">
                                <span>考试类型</span>
                                <span>${course.examTypeName}</span>
                            </div>
                        </div>
                        <div id="course-${course.id.coureNumber}-${course.id.coureSequenceNumber}" style="margin-top: 8px; font-size: 12px; color: #666;">
                            <!-- 动态加载课程状态 -->
                        </div>
                    </div>
                `;
                container.append(courseHtml);

                // 加载课程状态
                const sfhc = ${s.count == fn:length(courseList)};
                const url = "/student/overview/attention/myAttention/courseInfo?zxjxjhh=" + sydqxnxq + "&kch=${course.id.coureNumber}&kxh=${course.id.coureSequenceNumber}&sfhc=" + sfhc;
                $.get(url, function(data) {
                    let info = "";
                    if (data.cjxx != null && data.cjxx != "") {
                        info += "成绩：" + (data.cjxx.wclyscj != null && data.cjxx.wclyscj != "" ? data.cjxx.courseScore : "");
                    } else {
                        const ksap = data.ksxx || "尚未安排";
                        info += "考试：" + ksap;
                    }

                    if (data.jxpgxx != null && data.jxpgxx != "" && data.jxpgxx.isEvaluated == "是") {
                        info += " | 评估：已评估";
                    } else {
                        info += " | 评估：未评估";
                    }

                    $('#course-${course.id.coureNumber}-${course.id.coureSequenceNumber}').html(info);
                });
                </c:forEach>

                resolve();
            });
        }

        // 加载课程统计
        function loadCourseStats() {
            return new Promise((resolve) => {
                const url = "/student/overview/attention/myAttention/courseCountInfo?zxjxjhh=" + sydqxnxq;
                $.get(url, function(data) {
                    if (data && data.length >= 6) {
                        const [bxqkcs, bjgkcs, zxf, hdxf, ypgkcs, dpgkcs] = data;

                        let courseStatsHtml = '';
                        if (bjgkcs > 0 || hdxf > 0) {
                            courseStatsHtml = `本学期：已修 <strong>${bxqkcs}</strong> 门，不及格 <strong>${bjgkcs}</strong> 门，获得学分 <strong>${hdxf}</strong>`;
                        } else {
                            courseStatsHtml = `本学期：共 <strong>${bxqkcs}</strong> 门课程，总计 <strong>${zxf}</strong> 学分`;
                        }

                        const evaluationHtml = `教学评估：已完成 <strong>${ypgkcs}</strong> 门，待评估 <strong>${dpgkcs}</strong> 门`;

                        $('#courseStatsInfo').html(courseStatsHtml);
                        $('#evaluationInfo').html(evaluationHtml);
                    }
                    resolve();
                }).fail(() => resolve());
            });
        }

        // 快捷操作
        function goToAction(action) {
            let url = '';
            let title = '';

            switch(action) {
                case 'courseSelect':
                    url = '/student/courseSelect/thisSemesterCurriculum/index';
                    title = '我的课表';
                    break;
                case 'cet':
                    url = '/student/certificationExam/index';
                    title = '认证考试';
                    break;
                case 'innovation':
                    url = '/student/innovationCredits/innovationProject/index';
                    title = '创新项目';
                    break;
                case 'competition':
                    url = '/student/subjectCompetition/index';
                    title = '学科竞赛';
                    break;
            }

            if (url) {
                if (parent && parent.addTab) {
                    parent.addTab(title, url);
                } else {
                    window.location.href = url;
                }
            }
        }

        // 查看培养方案详情
        function viewCultivationPlan() {
            const url = "/student/overview/attention/myAttention/showInfo";
            if (parent && parent.addTab) {
                parent.addTab("培养方案详情", url);
            } else {
                window.open(url, 'cultivationWindow', 'height=520,width=800,scrollbars=yes,resizable=yes');
            }
        }

        // 刷新数据
        function refreshData() {
            loadAllData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
