<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教材查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教材查询页面样式 */
        .material-header {
            background: linear-gradient(135deg, var(--warning-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .material-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .material-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--warning-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--warning-color);
            box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
        }
        
        .form-select {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-select:focus {
            outline: none;
            border-color: var(--warning-color);
            box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
        }
        
        .btn-search {
            background: var(--warning-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            grid-column: 1 / -1;
        }
        
        .btn-search:hover {
            background: var(--warning-dark);
        }
        
        .material-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-header i {
            color: var(--warning-color);
        }
        
        .material-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .material-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .material-item:last-child {
            border-bottom: none;
        }
        
        .material-header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .material-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .material-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .material-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .material-price {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            height: 30px;
            border-radius: 15px;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .material-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .meta-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .meta-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .material-description {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .stock-status {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .stock-available {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .stock-low {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .stock-out {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--warning-light);
            border-color: var(--warning-color);
            color: var(--warning-dark);
        }
        
        .btn-page.active {
            background: var(--warning-color);
            border-color: var(--warning-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .material-header-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .material-meta {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教材查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 教材查询头部 -->
        <div class="material-header">
            <div class="material-title">教材查询</div>
            <div class="material-desc">查询教材信息和库存状态</div>
        </div>
        
        <!-- 搜索条件 -->
        <div class="search-container">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form class="search-form" id="searchForm" name="inf">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">出版社</label>
                        <select class="form-select" name="cbsbh" id="cbsbh">
                            <option value="">全部</option>
                            <cache:query var="cbs" region="jcgl_cbs" orderby="cbsmc asc"/>
                            <c:forEach items="${cbs}" var="cbs">
                                <option value="${cbs.cbsbh}">${cbs.cbsmc}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">ISBN编号</label>
                        <input type="text" class="form-input" name="isbn" id="isbn" value="${YxzIsbn}" placeholder="请输入ISBN编号">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">条形码</label>
                        <input type="text" class="form-input" name="barcode" id="barcode" value="${YxzBarcode}" placeholder="请输入条形码">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">著作者</label>
                        <input type="text" class="form-input" name="zzz" id="zzz" placeholder="请输入著作者">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">教材名称</label>
                        <input type="text" class="form-input" name="jcmc" id="jcmc" placeholder="请输入教材名称">
                    </div>
                    
                    <div class="form-group">
                        <button type="button" class="btn-search" onclick="searchMaterials();">
                            <i class="ace-icon fa fa-search"></i>
                            查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 教材列表 -->
        <div class="material-container">
            <div class="container-header">
                <i class="ace-icon fa fa-list"></i>
                教材列表
            </div>
            
            <div class="material-list" id="materialList">
                <!-- 动态加载教材列表 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let params = '';

        $(function() {
            initPage();
            searchMaterials();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }
