<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>创新创业学分成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 创新创业学分成绩页面样式 */
        .score-container {
            background: var(--bg-primary);
            margin: var(--margin-md);
            border-radius: 12px;
            padding: var(--padding-xl);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            border: 2px solid var(--border-primary);
        }
        
        .score-container::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border: 2px dashed var(--border-secondary);
            border-radius: 16px;
            z-index: -1;
        }
        
        .score-header {
            font-size: var(--font-size-h2);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--margin-lg);
            font-family: 'Brush Script MT', cursive, '宋体', sans-serif;
        }
        
        .score-divider {
            width: 80%;
            height: 1px;
            background: var(--divider-color);
            margin: var(--margin-md) auto;
            border: none;
        }
        
        .student-info {
            margin: var(--margin-lg) 0;
        }
        
        .student-name {
            font-size: var(--font-size-h3);
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .student-title {
            font-size: var(--font-size-h3);
            color: var(--text-primary);
            font-weight: 600;
            font-family: '楷体', KaiTi, serif;
        }
        
        .score-content {
            font-size: var(--font-size-h4);
            line-height: 1.6;
            color: var(--text-primary);
            margin: var(--margin-lg) 0;
            padding: 0 var(--padding-md);
        }
        
        .score-highlight {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .score-date {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            margin-top: var(--margin-lg);
        }
        
        .no-score-message {
            font-size: var(--font-size-h4);
            color: var(--text-secondary);
            margin: var(--margin-xl) 0;
            line-height: 1.6;
        }
        
        .waiting-message {
            font-size: var(--font-size-h4);
            color: var(--warning-color);
            margin: var(--margin-xl) 0;
            line-height: 1.6;
        }
        
        .score-details {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-md) 0;
            text-align: left;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm) 0;
            border-bottom: 1px solid var(--divider-color);
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .grade-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 500;
            background: var(--success-color);
            color: white;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            color: var(--text-secondary);
        }
        
        .loading-container i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--primary-color);
        }
        
        @media (max-width: 480px) {
            .score-container {
                margin: var(--margin-sm);
                padding: var(--padding-lg);
            }
            
            .score-header {
                font-size: var(--font-size-h3);
            }
            
            .student-name, .student-title {
                font-size: var(--font-size-h4);
            }
            
            .score-content {
                font-size: var(--font-size-base);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">创新创业学分成绩</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 成绩信息容器 -->
        <div class="score-container">
            <div class="score-header">创新创业学分成绩信息</div>
            <div class="score-divider"></div>
            
            <c:choose>
                <c:when test="${not empty message}">
                    <c:if test="${'nothave' == message}">
                        <div class="no-score-message">
                            <i class="ace-icon fa fa-info-circle"></i><br>
                            当前没有查到您的成绩信息！
                        </div>
                        <div class="score-divider"></div>
                    </c:if>
                    <c:if test="${'notuse' == message}">
                        <div class="waiting-message">
                            <i class="ace-icon fa fa-clock-o"></i><br>
                            您的成绩信息在审核过程中，请耐心等待！
                        </div>
                        <div class="score-divider"></div>
                    </c:if>
                </c:when>
                <c:otherwise>
                    <!-- 学生信息 -->
                    <div class="student-info">
                        <span class="student-name">${xjxx.xm}【${xjxx.xh}】</span>
                        <span class="student-title">同学</span>
                    </div>
                    <div class="score-divider"></div>
                    
                    <!-- 成绩详情 -->
                    <div class="score-details">
                        <div class="detail-row">
                            <span class="detail-label">计算方式</span>
                            <span class="detail-value">${data.cjjsfs}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">课程成绩</span>
                            <span class="detail-value score-highlight">${data.kccj}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">成绩等级</span>
                            <span class="detail-value">
                                <span class="grade-badge">${data.djcjmc}</span>
                            </span>
                        </div>
                        
                        <c:if test="${'1' == cjjsfs}">
                            <div class="detail-row">
                                <span class="detail-label">专业人数</span>
                                <span class="detail-value">${data.zyrs}人</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">专业排名</span>
                                <span class="detail-value">第${data.zypm}名</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">成果总分值</span>
                                <span class="detail-value score-highlight">${data.cgzf}</span>
                            </div>
                        </c:if>
                        
                        <c:if test="${'2' == cjjsfs}">
                            <div class="detail-row">
                                <span class="detail-label">最高分值</span>
                                <span class="detail-value score-highlight">${data.cgzgf}</span>
                            </div>
                        </c:if>
                    </div>
                    
                    <div class="score-divider"></div>
                    
                    <!-- 成绩说明 -->
                    <div class="score-content">
                        按<span class="score-highlight">${data.cjjsfs}</span>计算，课程成绩为：<span class="score-highlight">${data.kccj}</span>，对应的等级为<span class="score-highlight">${data.djcjmc}</span>。
                        
                        <c:if test="${'1' == cjjsfs}">
                            <br><br>在专业<span class="score-highlight">${data.zyrs}</span>位同学中排名第<span class="score-highlight">${data.zypm}</span>，对应的成果总分值为：<span class="score-highlight">${data.cgzf}</span>。
                        </c:if>
                        
                        <c:if test="${'2' == cjjsfs}">
                            <br><br>所有成果分中最高的分值为：<span class="score-highlight">${data.cgzgf}</span>。
                        </c:if>
                    </div>
                    
                    <div class="score-divider"></div>
                    <div class="score-date">${date}</div>
                </c:otherwise>
            </c:choose>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
