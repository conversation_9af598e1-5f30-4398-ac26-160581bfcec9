<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>创新项目申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 创新项目申请页面样式 */
        .info-notice {
            background: var(--info-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .summary-card {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .summary-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .summary-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .project-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .project-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
            margin-right: var(--margin-sm);
        }
        
        .project-index {
            position: absolute;
            top: var(--padding-md);
            right: var(--padding-md);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .project-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .status-submitted {
            background: var(--info-color);
            color: white;
        }
        
        .status-reviewing {
            background: var(--primary-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .status-cancelled {
            background: var(--text-disabled);
            color: white;
        }
        
        .result-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: 4px;
        }
        
        .result-approved {
            background: var(--success-color);
            color: white;
        }
        
        .result-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
            padding-top: var(--padding-sm);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-action {
            flex: 1;
            min-height: 36px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-attachment {
            background: var(--secondary-color);
            color: white;
        }
        
        .fab-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            z-index: 100;
            transition: all var(--transition-base);
        }
        
        .fab-button:active {
            transform: scale(0.95);
        }
        
        .description-text {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .credit-highlight {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .attachment-info {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .attachment-info i {
            color: var(--info-color);
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: var(--padding-md);
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            width: 100%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: var(--font-size-h4);
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: var(--padding-md);
        }

        .modal-footer {
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }

        .radio-group-vertical {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .radio-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: var(--font-size-base);
            color: var(--text-primary);
            padding: var(--padding-sm);
            border-radius: 6px;
            transition: background-color var(--transition-base);
        }

        .radio-label:hover {
            background: var(--bg-tertiary);
        }

        .radio-label input[type="radio"] {
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">创新项目申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 提示信息 -->
        <c:if test="${not empty msg}">
            <div class="info-notice">
                <i class="ace-icon fa fa-info-circle"></i>
                ${msg}
            </div>
        </c:if>
        
        <c:if test="${empty msg}">
            <!-- 学分汇总 -->
            <div class="summary-card" id="creditSummary" style="display: none;">
                <div class="summary-title">已通过学分</div>
                <div class="summary-value" id="totalCredits">0</div>
            </div>
            
            <!-- 项目列表 -->
            <div id="projectList">
                <!-- 动态加载内容 -->
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-file-text-o"></i>
                <div>暂无申请记录</div>
                <c:if test="${flag == 'showAdd'}">
                    <button class="btn-mobile btn-primary" onclick="addProject();" style="margin-top: 16px;">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>新增申请</span>
                    </button>
                </c:if>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
            
            <!-- 浮动添加按钮 -->
            <c:if test="${flag == 'showAdd'}">
                <button class="fab-button" onclick="addProject();" id="fabButton">
                    <i class="ace-icon fa fa-plus"></i>
                </button>
            </c:if>
        </c:if>
    </div>

    <script>
        // 全局变量
        let projectData = [];
        let currentPage = 1;
        let hasMore = true;
        let tokenValue = '${token_in_session}';
        let schoolCode = '${schoolCode}';
        let ywid = '${ywid}';
        let totalCredits = 0;

        $(function() {
            initPage();
            if ('${empty msg}' === 'true') {
                loadData();
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 显示提示信息
            if ('${msg}' !== '') {
                showError('${msg}');
            }
        }

        // 加载数据
        function loadData(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
                totalCredits = 0;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/innovationProject/search",
                type: "post",
                data: "pageNum=" + page + "&pageSize=20",
                dataType: "json",
                success: function(data) {
                    if (data.records && data.records.length > 0) {
                        if (reset) {
                            projectData = data.records;
                        } else {
                            projectData = projectData.concat(data.records);
                        }

                        hasMore = projectData.length < data.pageContext.totalCount;
                        renderProjectList();
                        showFabButton(true);
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            projectData = [];
                            renderProjectList();
                        }
                        showFabButton(false);
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染项目列表
        function renderProjectList() {
            const container = $('#projectList');
            container.empty();

            totalCredits = 0;

            projectData.forEach(function(item, index) {
                // 计算已通过学分
                if (item.EA_RSLT === "1" && schoolCode !== "100048") {
                    totalCredits += parseFloat(item.XF || 0);
                }

                const itemHtml = createProjectItem(item, index);
                container.append(itemHtml);
            });

            // 显示学分汇总
            if (schoolCode !== "100048") {
                $('#totalCredits').text(totalCredits.toFixed(1));
                $('#creditSummary').show();
            }
        }

        // 创建项目项目HTML
        function createProjectItem(item, index) {
            const status = getStatusInfo(item);
            const result = getResultInfo(item.EA_RSLT);
            const canEdit = status.code == 0 || status.code == -1 || status.code == -2;
            const canSubmit = status.code == 0;
            const canDelete = status.code == 0;
            const canRevoke = status.code == 1 || (schoolCode !== "100006" && status.code == 2);
            const hasAttachment = parseInt(item.CNT || 0) > 0;

            return `
                <div class="project-item">
                    <div class="project-index">#${index + 1}</div>

                    <div class="project-header">
                        <div class="project-title">${item.XMMC || '项目名称'}</div>
                    </div>

                    <div class="project-details">
                        ${schoolCode !== "100048" ? `
                            <div class="detail-item">
                                <span class="detail-label">学分</span>
                                <span class="detail-value credit-highlight">${item.XF || '0'}</span>
                            </div>
                        ` : ''}

                        ${schoolCode === "100006" ? `
                            <div class="detail-item">
                                <span class="detail-label">成绩</span>
                                <span class="detail-value">${item.CJ || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">学分属性</span>
                                <span class="detail-value">${item.KCSXDM === "001" ? "必修" : (item.KCSXDM === "003" ? "任选" : "-")}</span>
                            </div>
                        ` : ''}

                        <div class="detail-item">
                            <span class="detail-label">申请学期</span>
                            <span class="detail-value">${item.ZXJXJHM || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">申请时间</span>
                            <span class="detail-value">${item.SQSJ || '-'}</span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">申请状态</span>
                            <span class="detail-value">
                                <span class="status-badge ${status.class}">${status.text}</span>
                                ${result.text ? `<span class="result-badge ${result.class}">${result.text}</span>` : ''}
                            </span>
                        </div>

                        <div class="detail-item">
                            <span class="detail-label">附件</span>
                            <span class="detail-value">
                                <div class="attachment-info">
                                    <i class="ace-icon fa fa-${hasAttachment ? 'paperclip' : 'file-o'}"></i>
                                    <span>${hasAttachment ? (parseInt(item.CNT) > 1 ? '多个附件' : '1个附件') : '暂无附件'}</span>
                                </div>
                            </span>
                        </div>

                        ${schoolCode !== "100048" && item.SQSM ? `
                            <div class="detail-item full-width">
                                <span class="detail-label">申请原因</span>
                                <span class="detail-value">${item.SQSM}</span>
                            </div>
                        ` : ''}
                    </div>

                    ${schoolCode !== "100048" && item.SQSM ? `
                        <div class="description-text">
                            ${item.SQSM}
                        </div>
                    ` : ''}

                    <div class="action-buttons">
                        ${canSubmit ? `
                            <button class="btn-action btn-submit" onclick="submitProject('${item.ID}');">
                                <i class="ace-icon fa fa-check"></i>
                                <span>提交</span>
                            </button>
                        ` : ''}

                        ${canEdit ? `
                            <button class="btn-action btn-edit" onclick="editProject('${item.ID}', '${item.CGLXDM || ''}');">
                                <i class="ace-icon fa fa-edit"></i>
                                <span>修改</span>
                            </button>
                        ` : `
                            <button class="btn-action btn-view" onclick="viewProject('${item.ID}', '${item.APPLY_TYPE || ''}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看</span>
                            </button>
                        `}

                        ${canDelete ? `
                            <button class="btn-action btn-delete" onclick="deleteProject('${item.ID}');">
                                <i class="ace-icon fa fa-trash"></i>
                                <span>删除</span>
                            </button>
                        ` : ''}

                        ${canRevoke ? `
                            <button class="btn-action btn-revoke" onclick="revokeProject('${item.ID}');">
                                <i class="ace-icon fa fa-reply"></i>
                                <span>撤销</span>
                            </button>
                        ` : ''}

                        ${hasAttachment ? `
                            <button class="btn-action btn-attachment" onclick="viewAttachment('${item.ID}', ${item.CNT});">
                                <i class="ace-icon fa fa-${parseInt(item.CNT) > 1 ? 'eye' : 'download'}"></i>
                                <span>${parseInt(item.CNT) > 1 ? '查看' : '下载'}</span>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(item) {
            let status, code;

            if (schoolCode !== "100048") {
                status = item.APPLY_STATUS;
                code = item.APPLY_STATUS;
            } else {
                status = item.SQZT;
                code = item.SQZT;
            }

            const statusMap = {
                '-3': { text: '撤回[终审前]', class: 'status-cancelled' },
                '-2': { text: '取消[终审后]', class: 'status-cancelled' },
                '-1': { text: '拒绝', class: 'status-rejected' },
                '0': { text: '待提交', class: 'status-draft' },
                '1': { text: schoolCode === "100048" ? '已提交' : '已提交', class: 'status-submitted' },
                '2': { text: '审批中', class: 'status-reviewing' },
                '3': { text: schoolCode === "100048" ? '审批通过' : '审批结束', class: 'status-approved' }
            };

            return {
                ...statusMap[status] || { text: '未知', class: 'status-draft' },
                code: parseInt(code)
            };
        }

        // 获取审批结果信息
        function getResultInfo(result) {
            const resultMap = {
                '0': { text: '拒绝', class: 'result-rejected' },
                '1': { text: '批准', class: 'result-approved' }
            };
            return resultMap[result] || { text: '', class: '' };
        }

        // 添加项目
        function addProject() {
            if (schoolCode === "100048") {
                // 显示成果类型选择对话框
                showProjectTypeDialog();
            } else {
                // 直接跳转到添加页面
                if (parent && parent.addTab) {
                    parent.addTab('新增申请', '/student/innovationCredits/innovationProject/add');
                } else {
                    window.location.href = '/student/innovationCredits/innovationProject/add';
                }
            }
        }

        // 显示项目类型选择对话框
        function showProjectTypeDialog() {
            let optionsHtml = '';

            // 获取成果类型选项
            $.ajax({
                url: "/student/innovationCredits/innovationProject/getProjectTypes",
                type: "get",
                async: false,
                success: function(data) {
                    if (data && data.length > 0) {
                        data.forEach(function(item) {
                            optionsHtml += `
                                <label class="radio-label">
                                    <input type="radio" name="projectType" value="${item.cxxdm}">
                                    <span>${item.cxxmc}</span>
                                </label>
                            `;
                        });
                    }
                }
            });

            const dialogHtml = `
                <div class="modal-overlay" id="typeDialog" onclick="closeTypeDialog();">
                    <div class="modal-content" onclick="event.stopPropagation();">
                        <div class="modal-header">
                            <h3>选择成果类型</h3>
                            <button class="modal-close" onclick="closeTypeDialog();">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="radio-group-vertical">
                                ${optionsHtml}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn-mobile btn-secondary" onclick="closeTypeDialog();">取消</button>
                            <button class="btn-mobile btn-primary" onclick="confirmProjectType();">确定</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(dialogHtml);
        }

        // 关闭类型选择对话框
        function closeTypeDialog() {
            $('#typeDialog').remove();
        }

        // 确认项目类型
        function confirmProjectType() {
            const selectedType = $('input[name="projectType"]:checked').val();
            if (!selectedType) {
                showError('请选择成果类型！');
                return;
            }

            closeTypeDialog();

            if (parent && parent.addTab) {
                parent.addTab('新增申请', '/student/innovationCredits/innovationProject/add?cglxdm=' + selectedType);
            } else {
                window.location.href = '/student/innovationCredits/innovationProject/add?cglxdm=' + selectedType;
            }
        }

        // 编辑项目
        function editProject(id, cglxdm) {
            const url = cglxdm ?
                `/student/innovationCredits/innovationProject/edit?id=${id}&cglxdm=${cglxdm}` :
                `/student/innovationCredits/innovationProject/edit?id=${id}`;

            if (parent && parent.addTab) {
                parent.addTab('修改申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看项目
        function viewProject(id, applyType) {
            if (parent && parent.addTab) {
                parent.addTab('查看申请', `/student/application/index/seeInfo?applyId=${id}&applyType=${applyType}`);
            } else {
                window.location.href = `/student/application/index/seeInfo?applyId=${id}&applyType=${applyType}`;
            }
        }

        // 提交项目
        function submitProject(id) {
            if (!confirm('提交之后不可修改，是否确定要提交申请？')) {
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/innovationProject/doCommit",
                type: "post",
                data: {
                    applyId: id,
                    tokenValue: tokenValue
                },
                dataType: "json",
                success: function(data) {
                    tokenValue = data.data.token;
                    if (data.status === 200) {
                        showSuccess("提交成功！");
                        loadData(1, true);
                    } else {
                        showError(data.message || "提交失败");
                    }
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 删除项目
        function deleteProject(id) {
            if (!confirm('确认删除当前申请？')) {
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/innovationProject/doDel",
                type: "post",
                data: {
                    applyId: id,
                    tokenValue: tokenValue
                },
                dataType: "json",
                success: function(data) {
                    tokenValue = data.data.token;
                    if (data.status === 200) {
                        showSuccess("删除成功！");
                        loadData(1, true);
                    } else {
                        showError(data.message || "删除失败");
                    }
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 撤销项目
        function revokeProject(id) {
            if (!confirm('确认撤销当前申请？')) {
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/innovationProject/doRevoke",
                type: "post",
                data: {
                    applyId: id,
                    tokenValue: tokenValue
                },
                dataType: "json",
                success: function(data) {
                    tokenValue = data.data.token;
                    if (data.status === 200) {
                        showSuccess("撤销成功！");
                        loadData(1, true);
                    } else {
                        showError(data.message || "撤销失败");
                    }
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 查看附件
        function viewAttachment(id, count) {
            if (parseInt(count) > 1) {
                // 多个附件，打开附件列表页面
                if (parent && parent.addTab) {
                    parent.addTab('附件列表', '/student/innovationCredits/innovationProject/openUpload?applyId=' + id);
                } else {
                    window.location.href = '/student/innovationCredits/innovationProject/openUpload?applyId=' + id;
                }
            } else {
                // 单个附件，直接下载
                const form = $('<form>');
                form.attr('style', 'display:none');
                form.attr('target', '_blank');
                form.attr('method', 'post');
                form.attr('action', '/student/innovationCredits/innovationProject/downXfrdFjb');

                const input1 = $('<input type="hidden" name="zxjxjhh" value="">');
                const input2 = $('<input type="hidden" name="applyId" value="' + id + '">');

                form.append(input1);
                form.append(input2);
                $('body').append(form);
                form.submit();
                form.remove();
            }
        }

        // 刷新数据
        function refreshData() {
            loadData(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#projectList').hide();
                $('#creditSummary').hide();
            } else {
                $('#emptyState').hide();
                $('#projectList').show();
            }
        }

        // 显示浮动按钮
        function showFabButton(show) {
            if (show && '${flag}' === 'showAdd') {
                $('#fabButton').show();
            } else {
                $('#fabButton').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 无限滚动加载
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    currentPage++;
                    loadData(currentPage, false);
                }
            }
        });
    </script>
</body>
</html>
