<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>用户体验</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 用户体验页面样式 */
        .ux-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .ux-features {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .features-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .features-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .feature-list {
            display: grid;
            gap: var(--spacing-md);
        }
        
        .feature-item {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .feature-item:active {
            background: var(--bg-color-active);
            transform: scale(0.98);
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: 18px;
        }
        
        .feature-content {
            flex: 1;
        }
        
        .feature-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .feature-description {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .feature-status {
            display: flex;
            align-items: center;
            margin-top: var(--margin-sm);
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-right: var(--margin-sm);
        }
        
        .status-active {
            background: var(--success-color);
            color: white;
        }
        
        .status-optimized {
            background: var(--info-color);
            color: white;
        }
        
        .status-enhanced {
            background: var(--warning-color);
            color: white;
        }
        
        .performance-metrics {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .metrics-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .metrics-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .metric-item {
            text-align: center;
            padding: var(--padding-md);
            border-radius: 8px;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
        }
        
        .metric-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: var(--margin-xs);
        }
        
        .metric-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .optimization-tips {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tips-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .tips-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .tip-list {
            display: grid;
            gap: var(--spacing-sm);
        }
        
        .tip-item {
            display: flex;
            align-items: flex-start;
            padding: var(--padding-sm);
            border-radius: 6px;
            background: var(--bg-tertiary);
        }
        
        .tip-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            background: var(--info-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: 12px;
            flex-shrink: 0;
        }
        
        .tip-content {
            flex: 1;
        }
        
        .tip-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .tip-description {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            line-height: 1.3;
        }
        
        .feedback-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .feedback-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .feedback-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .feedback-form {
            display: grid;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: grid;
            gap: var(--spacing-xs);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .form-input {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .rating-group {
            display: flex;
            gap: var(--spacing-xs);
            align-items: center;
        }
        
        .rating-star {
            font-size: 24px;
            color: var(--text-disabled);
            cursor: pointer;
            transition: color var(--transition-base);
        }
        
        .rating-star.active {
            color: var(--warning-color);
        }
        
        .submit-btn {
            padding: var(--padding-md);
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .submit-btn:active {
            background: var(--primary-dark);
            transform: scale(0.98);
        }
        
        @media (max-width: 480px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">用户体验</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="ux-header">
            <div class="header-title">用户体验优化</div>
            <div class="header-subtitle">提升移动端使用体验</div>
        </div>
        
        <!-- 用户体验特性 -->
        <div class="ux-features">
            <div class="features-title">
                <i class="ace-icon fa fa-star"></i>
                <span>体验特性</span>
            </div>
            <div class="feature-list">
                <div class="feature-item" onclick="viewFeature('responsive')">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="ace-icon fa fa-mobile"></i>
                        </div>
                        <div class="feature-content">
                            <div class="feature-name">响应式设计</div>
                            <div class="feature-description">完全适配各种屏幕尺寸，提供一致的用户体验</div>
                        </div>
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-active">已启用</span>
                        <span>100% 移动端适配</span>
                    </div>
                </div>
                
                <div class="feature-item" onclick="viewFeature('performance')">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="ace-icon fa fa-rocket"></i>
                        </div>
                        <div class="feature-content">
                            <div class="feature-name">性能优化</div>
                            <div class="feature-description">优化页面加载速度和操作响应时间</div>
                        </div>
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-optimized">已优化</span>
                        <span>快速加载</span>
                    </div>
                </div>
                
                <div class="feature-item" onclick="viewFeature('interaction')">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="ace-icon fa fa-hand-pointer-o"></i>
                        </div>
                        <div class="feature-content">
                            <div class="feature-name">交互优化</div>
                            <div class="feature-description">触摸友好的交互设计和流畅的操作体验</div>
                        </div>
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-enhanced">已增强</span>
                        <span>触摸友好</span>
                    </div>
                </div>
                
                <div class="feature-item" onclick="viewFeature('accessibility')">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="ace-icon fa fa-universal-access"></i>
                        </div>
                        <div class="feature-content">
                            <div class="feature-name">无障碍访问</div>
                            <div class="feature-description">支持无障碍访问，提供包容性的用户体验</div>
                        </div>
                    </div>
                    <div class="feature-status">
                        <span class="status-badge status-active">已支持</span>
                        <span>无障碍友好</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 性能指标 -->
        <div class="performance-metrics">
            <div class="metrics-title">
                <i class="ace-icon fa fa-tachometer"></i>
                <span>性能指标</span>
            </div>
            <div class="metrics-grid">
                <div class="metric-item">
                    <div class="metric-value">< 2s</div>
                    <div class="metric-label">页面加载时间</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">98%</div>
                    <div class="metric-label">功能可用性</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">95%</div>
                    <div class="metric-label">用户满意度</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">100%</div>
                    <div class="metric-label">移动端兼容</div>
                </div>
            </div>
        </div>
        
        <!-- 优化建议 -->
        <div class="optimization-tips">
            <div class="tips-title">
                <i class="ace-icon fa fa-lightbulb-o"></i>
                <span>优化建议</span>
            </div>
            <div class="tip-list">
                <div class="tip-item">
                    <div class="tip-icon">
                        <i class="ace-icon fa fa-wifi"></i>
                    </div>
                    <div class="tip-content">
                        <div class="tip-title">网络优化</div>
                        <div class="tip-description">在良好的网络环境下使用，获得最佳体验</div>
                    </div>
                </div>
                
                <div class="tip-item">
                    <div class="tip-icon">
                        <i class="ace-icon fa fa-refresh"></i>
                    </div>
                    <div class="tip-content">
                        <div class="tip-title">定期更新</div>
                        <div class="tip-description">定期刷新页面数据，保持信息最新</div>
                    </div>
                </div>
                
                <div class="tip-item">
                    <div class="tip-icon">
                        <i class="ace-icon fa fa-mobile"></i>
                    </div>
                    <div class="tip-content">
                        <div class="tip-title">设备兼容</div>
                        <div class="tip-description">使用现代浏览器，获得更好的功能支持</div>
                    </div>
                </div>
                
                <div class="tip-item">
                    <div class="tip-icon">
                        <i class="ace-icon fa fa-bookmark"></i>
                    </div>
                    <div class="tip-content">
                        <div class="tip-title">快捷访问</div>
                        <div class="tip-description">添加到主屏幕，方便快速访问</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 用户反馈 -->
        <div class="feedback-section">
            <div class="feedback-title">
                <i class="ace-icon fa fa-comment-o"></i>
                <span>用户反馈</span>
            </div>
            <div class="feedback-form">
                <div class="form-group">
                    <label class="form-label">整体评价</label>
                    <div class="rating-group" id="overallRating">
                        <span class="rating-star" data-rating="1">★</span>
                        <span class="rating-star" data-rating="2">★</span>
                        <span class="rating-star" data-rating="3">★</span>
                        <span class="rating-star" data-rating="4">★</span>
                        <span class="rating-star" data-rating="5">★</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">反馈内容</label>
                    <textarea class="form-input form-textarea" id="feedbackContent" placeholder="请输入您的意见和建议..."></textarea>
                </div>
                
                <button class="submit-btn" onclick="submitFeedback();">提交反馈</button>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        let currentRating = 0;

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 评分星星点击
            $('.rating-star').click(function() {
                const rating = parseInt($(this).data('rating'));
                setRating(rating);
            });
        }

        // 设置评分
        function setRating(rating) {
            currentRating = rating;
            $('.rating-star').each(function(index) {
                if (index < rating) {
                    $(this).addClass('active');
                } else {
                    $(this).removeClass('active');
                }
            });
        }

        // 查看特性详情
        function viewFeature(featureType) {
            // 实现特性详情查看逻辑
            console.log('View feature:', featureType);
        }

        // 提交反馈
        function submitFeedback() {
            const content = $('#feedbackContent').val().trim();
            
            if (currentRating === 0) {
                alert('请选择评分');
                return;
            }
            
            if (!content) {
                alert('请输入反馈内容');
                return;
            }
            
            showLoading(true);
            
            $.ajax({
                url: "/student/userExperience/submitFeedback",
                type: "post",
                data: {
                    rating: currentRating,
                    content: content
                },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        alert('反馈提交成功，感谢您的建议！');
                        $('#feedbackContent').val('');
                        setRating(0);
                    } else {
                        alert(data.message || '提交失败');
                    }
                },
                error: function() {
                    alert('网络请求失败');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 刷新数据
        function refreshData() {
            showLoading(true);
            
            setTimeout(function() {
                showLoading(false);
                showSuccess('用户体验数据已更新');
            }, 1000);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            // 这里可以添加成功提示的实现
            console.log('Success: ' + message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
        }
    </script>
</body>
</html>
