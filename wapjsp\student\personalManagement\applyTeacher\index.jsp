<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>申请书院导师</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 申请书院导师页面样式 */
        .teacher-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .teacher-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .teacher-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .warning-notice {
            background: var(--warning-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-notice i {
            font-size: var(--font-size-base);
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--primary-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .btn-add {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .applications-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-title i {
            color: var(--success-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-id {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .application-teacher {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-draft {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-submitted {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-reviewing {
            background: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-cancelled {
            background: var(--text-disabled);
            color: white;
        }
        
        .result-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: 4px;
        }
        
        .result-approved {
            background: var(--success-color);
            color: white;
        }
        
        .result-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-application-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--error-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        @media (max-width: 480px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .search-actions {
                flex-direction: column;
            }
            
            .application-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" name="tokenValue" id="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">申请书院导师</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 申请导师头部 -->
        <div class="teacher-header">
            <div class="teacher-title">申请书院导师</div>
            <div class="teacher-desc">申请和管理书院导师申请</div>
        </div>
        
        <!-- 警告提示 -->
        <c:if test="${count == 0}">
            <div class="warning-notice">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>学生申请书院导师无审批流程，请联系管理员！</span>
            </div>
        </c:if>
        
        <c:if test="${empty sy}">
            <div class="warning-notice">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>学生还未安排书院！</span>
            </div>
        </c:if>
        
        <c:if test="${count != 0 && not empty sy}">
            <!-- 查询条件 -->
            <div class="search-section">
                <div class="search-title">
                    <i class="ace-icon fa fa-search"></i>
                    查询条件（${sy[0][1]}）
                </div>
                
                <form id="queryInfo" name="queryInfo" class="search-form">
                    <div class="form-group">
                        <label class="form-label">导师类型</label>
                        <select id="dslx" name="dslx" class="form-select">
                            <option value="">全部</option>
                            <c:forEach var="dslx" items="${dslx}">
                                <option value="${dslx[0]}">${dslx[1]}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">教师号</label>
                        <input type="text" name="jsh1" id="jsh1" class="form-input" placeholder="请输入教师号">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">教师名</label>
                        <input type="text" name="jsm1" id="jsm1" class="form-input" placeholder="请输入教师名">
                    </div>
                </form>
                
                <div class="search-actions">
                    <button class="btn-mobile btn-search" onclick="searchApplications();">
                        <i class="ace-icon fa fa-search"></i>
                        <span>查询</span>
                    </button>
                    <button class="btn-mobile btn-add" onclick="addApplication();">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>新增申请</span>
                    </button>
                </div>
            </div>
            
            <!-- 申请列表 -->
            <div class="applications-section">
                <div class="applications-header">
                    <div class="applications-title">
                        <i class="ace-icon fa fa-list"></i>
                        申请书院导师列表
                    </div>
                </div>
                
                <div id="applicationsList">
                    <!-- 动态加载申请列表 -->
                </div>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-user-plus"></i>
            <div>暂无申请记录</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let applicationData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let searchParams = '';

        $(function() {
            initPage();
            if ('${count}' !== '0' && '${not empty sy}' === 'true') {
                loadApplications(1, true);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索申请
        function searchApplications() {
            loadApplications(1, true);
        }

        // 加载申请数据
        function loadApplications(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
                searchParams = $('#queryInfo').serialize();
            }

            showLoading(true);

            $.ajax({
                url: "/students/personalManagement/applyTeacher/getApplyList",
                type: "post",
                data: searchParams + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            applicationData = data.records;
                        } else {
                            applicationData = applicationData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        hasMore = applicationData.length < totalCount;
                        renderApplicationsList();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            applicationData = [];
                            renderApplicationsList();
                        }
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationsList() {
            const container = $('#applicationsList');
            container.empty();

            applicationData.forEach(function(item, index) {
                const itemHtml = createApplicationItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建申请项目HTML
        function createApplicationItem(item, index) {
            const statusInfo = getStatusInfo(item.APPLY_STATUS);
            const resultInfo = getResultInfo(item.EA_RSLT);

            return `
                <div class="application-item">
                    <div class="application-header">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="application-id">申请编号：${item.APPLY_ID || ''}</div>
                                <div class="application-teacher">${item.DSLX || ''} - ${item.JSM || ''}（${item.JSH || ''}）</div>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 4px;">
                            <span class="application-status ${statusInfo.class}">${statusInfo.text}</span>
                            ${resultInfo ? `<span class="result-badge ${resultInfo.class}">${resultInfo.text}</span>` : ''}
                        </div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">所属机构</span>
                            <span>${item.XSM || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">备注</span>
                            <span>${item.BZ || '-'}</span>
                        </div>
                    </div>

                    <div class="application-actions">
                        ${getActionButtons(item)}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch (status) {
                case -1: return { text: '撤销', class: 'status-cancelled' };
                case 0: return { text: '待提交', class: 'status-draft' };
                case 1: return { text: '已提交', class: 'status-submitted' };
                case 2: return { text: '审批中', class: 'status-reviewing' };
                case 3: return { text: '审批结束', class: 'status-approved' };
                default: return { text: '未知', class: 'status-draft' };
            }
        }

        // 获取结果信息
        function getResultInfo(result) {
            if (result === '0') return { text: '拒绝', class: 'result-rejected' };
            if (result === '1') return { text: '批准', class: 'result-approved' };
            return null;
        }

        // 获取操作按钮
        function getActionButtons(item) {
            let buttons = '';

            if (item.APPLY_STATUS == 1 || item.APPLY_STATUS == 2 || item.APPLY_STATUS == 3 || item.APPLY_STATUS == -1) {
                buttons += `
                    <button class="btn-application-action btn-view" onclick="viewApplication('${item.APPLY_ID}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `;
            }

            if (item.APPLY_STATUS == 0) {
                buttons += `
                    <button class="btn-application-action btn-edit" onclick="editApplication('${item.APPLY_ID}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                    <button class="btn-application-action btn-revoke" onclick="revokeApplication('${item.APPLY_ID}');">
                        <i class="ace-icon fa fa-reply"></i>
                        <span>撤回</span>
                    </button>
                `;
            }

            return buttons;
        }

        // 新增申请
        function addApplication() {
            if (parent && parent.addTab) {
                parent.addTab('新增申请', '/students/personalManagement/applyTeacher/addApplyInfo');
            } else {
                window.location.href = '/students/personalManagement/applyTeacher/addApplyInfo';
            }
        }

        // 编辑申请
        function editApplication(sqbh) {
            if (parent && parent.addTab) {
                parent.addTab('修改申请', '/students/personalManagement/applyTeacher/addApplyInfo?sqbh=' + sqbh);
            } else {
                window.location.href = '/students/personalManagement/applyTeacher/addApplyInfo?sqbh=' + sqbh;
            }
        }

        // 查看申请
        function viewApplication(sqbh) {
            if (parent && parent.addTab) {
                parent.addTab('查看申请', '/students/personalManagement/applyTeacher/seeApply?sqbh=' + sqbh);
            } else {
                window.location.href = '/students/personalManagement/applyTeacher/seeApply?sqbh=' + sqbh;
            }
        }

        // 撤回申请
        function revokeApplication(sqbh) {
            if (confirm("确定要撤回申请？")) {
                showLoading(true);

                $.ajax({
                    url: "/students/personalManagement/applyTeacher/revokeApply",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(data) {
                        if (data.status != 200) {
                            showError(data.msg);
                        } else {
                            if (data.data.result.indexOf("/") != -1) {
                                showError("页面已过期，请刷新页面！");
                            } else {
                                if (data.data.result == "ok") {
                                    showSuccess("撤销成功！");
                                    loadApplications(1, true);
                                }
                            }
                            $("#tokenValue").val(data.data.token);
                        }
                    },
                    error: function() {
                        showError("撤销失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 刷新数据
        function refreshData() {
            loadApplications(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#applicationsList').parent().hide();
            } else {
                $('#emptyState').hide();
                $('#applicationsList').parent().show();
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
