<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生发表论文申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 论文发表管理页面样式 */
        .paper-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .paper-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .paper-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--primary-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .btn-add {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .papers-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .papers-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .papers-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .papers-title i {
            color: var(--success-color);
        }
        
        .paper-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .paper-item:last-child {
            border-bottom: none;
        }
        
        .paper-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .paper-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .paper-content {
            flex: 1;
        }
        
        .paper-title-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .paper-author {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .paper-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .category-badge {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-right: 4px;
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-operation {
            flex: 1;
            min-width: 60px;
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .search-actions {
                flex-direction: column;
            }
            
            .paper-details {
                grid-template-columns: 1fr;
            }
            
            .operation-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生发表论文申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 论文发表头部 -->
        <div class="paper-header">
            <div class="paper-title">学生发表论文申请</div>
            <div class="paper-desc">管理和申请论文发表</div>
        </div>
        
        <!-- 查询条件 -->
        <div class="search-section">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form id="queryForm" name="queryForm" class="search-form">
                <div class="form-group">
                    <label class="form-label">发表情况</label>
                    <select name="fbqkm" class="form-select">
                        <option value="">全部</option>
                        <cache:query var="fbqkbs" region="code_fbqkb" orderby="fbqkm desc"/>
                        <c:forEach var="fbqkb" items="${fbqkbs}">
                            <option value="${fbqkb.fbqkm}">${fbqkb.fbqkmc}</option>
                        </c:forEach>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">发表类别</label>
                    <select name="fblbm" class="form-select">
                        <option value="">全部</option>
                        <cache:query var="fblbbs" region="code_fblbb" orderby="fblbm desc"/>
                        <c:forEach var="fblbb" items="${fblbbs}">
                            <option value="${fblbb.fblbm}">${fblbb.fblbmc}</option>
                        </c:forEach>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">刊物级别</label>
                    <select name="kwjbm" class="form-select">
                        <option value="">全部</option>
                        <cache:query var="kwjbbs" region="code_kwjbb" orderby="kwjbdm desc"/>
                        <c:forEach var="kwjbb" items="${kwjbbs}">
                            <option value="${kwjbb.kwjbdm}">${kwjbb.kwjbsm}</option>
                        </c:forEach>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">论文题目</label>
                    <input type="text" name="lwtm" class="form-input" placeholder="请输入论文题目">
                </div>
                
                <div class="form-group">
                    <label class="form-label">刊物名称</label>
                    <input type="text" name="kwmc" class="form-input" placeholder="请输入刊物名称">
                </div>
                
                <div class="form-group">
                    <label class="form-label">期号</label>
                    <input type="text" name="qh" class="form-input" placeholder="请输入期号">
                </div>
            </form>
            
            <div class="search-actions">
                <button class="btn-mobile btn-search" onclick="searchPapers();">
                    <i class="ace-icon fa fa-search"></i>
                    <span>查询</span>
                </button>
                <button class="btn-mobile btn-add" onclick="addPaper();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>增加</span>
                </button>
            </div>
        </div>
        
        <!-- 论文列表 -->
        <div class="papers-section">
            <div class="papers-header">
                <div class="papers-title">
                    <i class="ace-icon fa fa-list"></i>
                    发表论文信息
                </div>
            </div>
            
            <div id="papersList">
                <!-- 动态加载论文列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMorePapers();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无论文发表数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let paperData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let searchParams = '';
        let lwsybList = ${lwsybList};

        $(function() {
            initPage();
            loadPapers(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索论文
        function searchPapers() {
            loadPapers(1, true);
        }

        // 加载更多论文
        function loadMorePapers() {
            if (hasMore) {
                loadPapers(currentPage + 1, false);
            }
        }

        // 加载论文数据
        function loadPapers(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
                searchParams = $('#queryForm').serialize();
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/paperPublicationManagement/search",
                type: "post",
                data: searchParams + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            paperData = data.records;
                        } else {
                            paperData = paperData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = paperData.length < totalCount;

                        renderPapersList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            paperData = [];
                            renderPapersList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染论文列表
        function renderPapersList(reset = false) {
            const container = $('#papersList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : paperData.length - pageSize;
            const endIndex = paperData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (paperData[i]) {
                    const itemHtml = createPaperItem(paperData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建论文项目HTML
        function createPaperItem(item, index) {
            // 构建索引类型标签
            let indexTypesHtml = '';
            if (item.SYIDS && lwsybList) {
                const syids = "," + item.SYIDS + ",";
                lwsybList.forEach(function(lwsy) {
                    if (syids.indexOf("," + lwsy[0] + ",") !== -1) {
                        indexTypesHtml += `<span class="category-badge" style="background-color: ${lwsy[2]};">${lwsy[1]}</span>`;
                    }
                });
            }

            // 构建操作按钮
            let operationButtons = `
                <button class="btn-operation btn-view" onclick="viewPaper('${item.SQBH}');">
                    <i class="ace-icon fa fa-eye"></i>
                    <span>详情</span>
                </button>
            `;

            if (item.SPZT === '0') {
                operationButtons += `
                    <button class="btn-operation btn-edit" onclick="editPaper('${item.XH}', '${item.XUH}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                    <button class="btn-operation btn-delete" onclick="deletePaper('${item.XH}', '${item.XUH}');">
                        <i class="ace-icon fa fa-trash"></i>
                        <span>删除</span>
                    </button>
                `;
            }

            return `
                <div class="paper-item">
                    <div class="paper-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="paper-index">${index + 1}</div>
                            <div class="paper-content">
                                <div class="paper-title-text">${item.LWTM || ''}</div>
                                <div class="paper-author">${item.XM || ''}（${item.XH || ''}）- ${item.XSM || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="paper-details">
                        <div class="detail-item">
                            <span class="detail-label">年级</span>
                            <span>${item.NJDM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">专业</span>
                            <span>${item.ZYM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">论文序号</span>
                            <span>${item.XUH || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">发表情况</span>
                            <span>
                                ${item.FBQKMC && item.FBQKYSM !== '-1' ?
                                    `<span class="category-badge" style="background-color: ${item.FBQKYSM};">${item.FBQKMC}</span>` :
                                    (item.FBQKMC || '')
                                }
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">发表类别</span>
                            <span>
                                ${item.FBLBMC && item.FBLBYSM !== '-1' ?
                                    `<span class="category-badge" style="background-color: ${item.FBLBYSM};">${item.FBLBMC}</span>` :
                                    (item.FBLBMC || '')
                                }
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">刊物代号</span>
                            <span>${item.KWDH || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">刊物名称</span>
                            <span>${item.KWMC || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">刊物级别</span>
                            <span>
                                ${item.KWJBMC && item.KWJBYSM !== '-1' ?
                                    `<span class="category-badge" style="background-color: ${item.KWJBYSM};">${item.KWJBMC}</span>` :
                                    (item.KWJBMC || '')
                                }
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">卷号</span>
                            <span>${item.JUAN || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">期号</span>
                            <span>${item.QH || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">作者署名排序</span>
                            <span>${item.DJZZ || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">会议名称</span>
                            <span>${item.HYMC || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">会议日期</span>
                            <span>${item.HYRQ || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">会议地点</span>
                            <span>${item.HYDD || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">索引类型</span>
                            <span>${indexTypesHtml}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">审批结论</span>
                            <span>${item.SCJLM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">备注</span>
                            <span>${item.BZ || ''}</span>
                        </div>
                    </div>

                    <div class="operation-buttons">
                        ${operationButtons}
                    </div>
                </div>
            `;
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && paperData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (paperData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.papers-section').hide();
            } else {
                $('#emptyState').hide();
                $('.papers-section').show();
            }
        }

        // 查看论文详情
        function viewPaper(sqbh) {
            // 这里需要实现查看详情的功能
            showInfo("查看详情功能待实现");
        }

        // 新增论文
        function addPaper() {
            if (parent && parent.addTab) {
                parent.addTab('新增论文', '/student/personalManagement/paperPublicationManagement/add?type=add');
            } else {
                location.href = "/student/personalManagement/paperPublicationManagement/add?type=add";
            }
        }

        // 编辑论文
        function editPaper(xh, xuh) {
            if (parent && parent.addTab) {
                parent.addTab('修改论文', '/student/personalManagement/paperPublicationManagement/add?type=edit&xh=' + xh + '&xuh=' + xuh);
            } else {
                location.href = "/student/personalManagement/paperPublicationManagement/add?type=edit&xh=" + xh + "&xuh=" + xuh;
            }
        }

        // 删除论文
        function deletePaper(xh, xuh) {
            if (confirm("确定要撤回这条记录？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/paperPublicationManagement/deleteFblw",
                    type: "post",
                    data: "xh=" + xh + "&xuh=" + xuh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/") != -1) {
                            showError("页面已过期，请刷新页面！");
                        } else if (data.result === "ok") {
                            showSuccess("删除成功！", function() {
                                location.reload();
                            });
                        } else {
                            showError(data.msg);
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 刷新数据
        function refreshData() {
            loadPapers(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示信息
        function showInfo(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
