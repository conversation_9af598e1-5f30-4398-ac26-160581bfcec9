<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
    <title>申请列表</title>
    <c:if test="${mobile}">
        <style type="text/css">
            .phone-profile-info-value {
                padding: 8px 6px;
            }

            .fa {
                width: 18px !important;
                height: 14px !important;
            }

        </style>
    </c:if>
</head>
<body>
<div class="row">
    <div class="self-margin col-xs-12">
        <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
        <input type="hidden" id="ywid" name="ywid" value="${ywid }">
        <c:if test="${mobile}">
            <h5 class="phone-header smaller lighter grey">
                <i class="ace-icon fa fa-times bigger-130 phone-header-left" onclick="returnIndex();"></i>
                <span class="phone-header-center">${applyName }</span>
            </h5>
            <div class="widget-toolbar phone-no-border">
                <h5 class="smaller lighter phone-header-font" style="color: black;font-weight: bold;">
                                                       我的申请记录
                    <span class="right_top_oper">
		                <c:if test="${flag == 'showAdd'}">
		                    <button title="增加" class="btn btn-success btn-xs btn-round" onclick="editInfo('add','');return false;">
		                        <i class="ace-icon fa fa-plus bigger-120"></i>新增
		                    </button>
                        </c:if>
		                <button type="button" class="btn btn-xs btn-round" title="返回" onclick="returnIndex();return false;">
		                    <i class="fa fa-reply bigger-120"></i> 返回
		                </button>
	            	</span>
                </h5>

                <div id="kctd_scroll" style="max-height: calc(100vh - 175px);overflow: auto;">
                    <div id="tktbody"></div>
                </div>
                <div id="urppagebar"></div>
            </div>
        </c:if>
        <c:if test="${!mobile}">
            <h4 class="header smaller lighter grey">
                <i class="glyphicon glyphicon-list"></i>
                我的申请记录
                <span class="right_top_oper">
						<c:if test="${flag == 'showAdd'}">
								<button title="增加" class="btn btn-success btn-xs btn-round" onclick="editInfo('add','');return false;">
									<i class="ace-icon fa fa-plus bigger-120"></i>新增
			                    </button>
                        </c:if>
		            	<button class="btn btn-xs btn-round" title="返回" onclick="returnIndex();">
	                        <i class="fa fa-reply bigger-120"></i> 返回
	                    </button>
	            	</span>
            </h4>
            <div class="widget-content widget-box" id="table_scroll" style="max-height: calc(100vh - 175px);overflow: auto;">
                <table class="table table-striped table-bordered" id="tkTable">
                    <thead>
                    <tr>
                        <th>操作</th>
                        <th>序号</th>
                        <th>申请编号</th>
                        <th>申请日期</th>
                        <c:if test="${codeXfrzlxb.kcrdfs=='G' }">
                            <th>课组类别</th>
                        </c:if>
                        <th>申请状态</th>
                    </tr>
                    </thead>
                    <tbody id="tktbody">
                    </tbody>
                </table>
            </div>
            <div id="urppagebar"></div>
        </c:if>
    </div>
</div>
<script type="text/javascript">
    var page_size = "30_sl";
    var mobile = ${mobile};
    if(mobile){
        page_size = "100000000_sl";
    }

    $(function(){
        getPageList("1",page_size,true);

        if("${msg}"!=""){
            urp.alert("${msg}");
        }
    });

    //分页查询
    function getPageList(page,pageSizeVal,conditionChanged){
        if(pageSizeVal == undefined){
            pageSizeVal = page_size;
            page = "1";
        }
        var parr = (pageSizeVal+"").split("_");
        var pageSize = parseInt(parr[0]);
        var url = "/student/personalManagement/individualApplication/creditCertification/getPage";
        $.ajax({
            url : url,
            cache : false,
            type : "post",
            data : "pageNum=" + page + "&pageSize=" + pageSize+"&ywid=${ywid}",
            dataType : "json",
            success : function(d){
                var data = d.data;
                urp.pagebar("urppagebar", pageSizeVal, page,data["pageContext"].totalCount, getPageList,"on", "table_scroll","暂时没有您的申请信息");
                var isScroll = (pageSizeVal+"").indexOf("_")!=-1 && page!=1?true:false;
                if(data["records"] != null && data["records"].length != 0){
                    fillTable(data["records"],isScroll,page,pageSize);
                }else{
                    fillTable(null,isScroll,page,pageSize);
                }
            },
            error : function(xhr){
                urp.alert("错误代码["+xhr.readyState+"-"+xhr.status+"]:获取数据失败！");
            }
        });
    }

    //数据显示
    function fillTable(data,isScroll,page,pageSize){
        var tcont = "";
        if(data != null){
            $.each(data,function(i,v){
                var tableId = "";
                if(isScroll){
                    tableId = (page-1)*pageSize+1+i;
                }else{
                    tableId = i+1;
                }

                var status = "";
                //申请状态(-1撤销0待提交1已提交2审批中3审批结束)
                if(v.APPLY_STATUS == -1){
                    status = "撤销";
                }else if(v.APPLY_STATUS == 0){
                    status = "待提交";
                }else if(v.APPLY_STATUS == 1){
                    status = "已提交";
                }else if(v.APPLY_STATUS == 2){
                    status = "审批中";
                } else if(v.APPLY_STATUS == 3){
                    status = "审批结束";
                    if(v.EA_RSLT == "0"){
                        status = "已拒绝";
                    }else if(v.EA_RSLT == "1"){
                        status = "已批准";
                    }
                }

                if(mobile){
                    tcont += "<div class=\"phone-message-item\">";
                    tcont += "<h5 style=\"margin-top: 0px; border-bottom: 1px solid white;\"><font>#"+tableId+"</font><span style=\"float: right; position: relative; bottom: 5px; border-radius: 20px; background: #3eabe1;\" class=\"label\">";
                    tcont += status+"</span></h5>";
                    tcont += "<p><i class=\"ace-icon fa fa-barcode bigger-110\"></i>&nbsp;" + (v.APPLY_ID == null ? "" : v.APPLY_ID) + " </p> ";
                    tcont += "<p><i class=\"ace-icon fa fa-clock-o bigger-120\"></i>&nbsp;" + (v.SQSJ == null ? "" : v.SQSJ) + " </p> ";
                    if("${codeXfrzlxb.kcrdfs }"=="G"){
                        tcont += "<p><i class=\"ace-icon fa fa-bookmark bigger-110\"></i>&nbsp;" + (v.KZLBMC == null ? "" : v.KZLBMC) + " </p> ";
                    }
                    tcont += " <div class=\"hr hr8 hr-dotted white\" style='border-top: 1px solid #FFF;'></div>";
                    tcont += " <span style=\"margin: 0px;height: 17px; display: block;\"> ";

                    if(v.APPLY_STATUS == 0){
                        tcont += "<button class='btn btn-xs btn-danger btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                            "border-bottom-width: 1px;border: 1px;' onclick='revokeInfo(\""+v.APPLY_ID+"\");'>撤回</button>";
                        tcont += "<button class='btn btn-xs btn-white btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                            "border-bottom-width: 1px;border: 1px;' onclick='editInfo(\"edit\",\""+v.APPLY_ID+"\");'>修改</button>";
                    }
                    if(v.APPLY_STATUS == 1 || v.APPLY_STATUS == 2 || v.APPLY_STATUS == 3 || v.APPLY_STATUS == -1){
                        tcont += "<button class='btn btn-xs btn-white btn-default btn-round' style='float: right;margin-left: 5px;border-radius: 10px!important;padding: 3px 10px 3px 10px;" +
                            "border-bottom-width: 1px;border: 1px;' onclick='seeInfo(\""+v.APPLY_ID+"\");'>查看</button>";
                    }

                    tcont += " </span>";
                    tcont += " </div>";
                }else{
                    tcont += "<tr>";
                    tcont += "<td>";
                    if(v.APPLY_STATUS == 1 || v.APPLY_STATUS == 2 || v.APPLY_STATUS == 3 || v.APPLY_STATUS == -1){
                        tcont += "<a style='cursor:pointer;' class='blue' title='查看' onclick='seeInfo(\""+v.APPLY_ID+"\");'><i class='ace-icon fa fa-eye bigger-130'></i></a>&nbsp;&nbsp;&nbsp;";
                    }
                    if(v.APPLY_STATUS == 0){
                        tcont += "<a style='cursor:pointer;' class='blue' title='修改' onclick='editInfo(\"edit\",\""+v.APPLY_ID+"\");'><i class='ace-icon fa fa-pencil-square-o bigger-130'></i></a>&nbsp;&nbsp;&nbsp;";
                        tcont += "<a style='cursor:pointer;' class='red' title='撤回' onclick='revokeInfo(\""+v.APPLY_ID+"\");'><i class='ace-icon fa fa-reply bigger-130'></i></a>&nbsp;&nbsp;&nbsp;";
                    }
                    /* if(v.FJ > 0){
                     tcont += "<a title='下载' class='blue' style='cursor:pointer;' onclick='doDownload(\""+v.APPLY_ID+"\");return false;'><i class='ace-icon fa fa-download bigger-130'></i></a>";
                     } */
                    tcont += "</td>";
                    tcont += "<td>"+tableId+"</td>";
                    tcont += "<td>"+(v.APPLY_ID == null ? "" : v.APPLY_ID)+"</td>";
                    tcont += "<td>"+(v.SQSJ == null ? "" : v.SQSJ)+"</td>";
                    if("${codeXfrzlxb.kcrdfs }"=="G"){
                        tcont += "<td>"+(v.KZLBMC == null ? "" : v.KZLBMC)+"</td>";
                    }
                    tcont += "<td>"+status+"</td>";
                    tcont += "</tr>";
                }
            });
        }
        if(isScroll){
            $("#tktbody").append(tcont);
        }else{
            $("#tktbody").html(tcont);
        }
    }

    function addslidersModel(id, width) {
        var modal = '<div id="' + id + '" class="modal right fade" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" tabindex="-1">\
			            <div class="modal-dialog">\
			                <div class="modal-content">\
			                    <div class="center">\
		                            <img src="/img/icon/pageloading.gif" style="width:28px;height:28px;">\
		                        </div>\
			                </div>\
			            </div>\
			        </div>';
        var modal = $(modal).appendTo('body');
        $(".modal-dialog").css("width", width);
        return modal;
    }

    //添加/修改数据
    function editInfo(type,sqbh) {
        location.href = "/student/personalManagement/individualApplication/creditCertification/editInfo?type="+type+"&sqbh=" + sqbh+"&ywid=${ywid}";
    }

    //查看数据
    function seeInfo(sqbh) {
        if(mobile){
            location.href = "/phone/student/application/index/seeInfo?applyId=" + sqbh + "&applyType=${ywid}";
        }else{
            var url = "/student/application/index/seeInfo?applyId=" + sqbh + "&applyType=${ywid}";
            var modal = addslidersModel("add_model", "60%");
            modal.modal({
                remote: url
            }).on('hide.bs.modal', function () {
                modal.remove();
            });
        }
    }

    //撤销数据
    function revokeInfo(sqbh){
        urp.confirm("确定要撤销申请？",callback);
        function callback(f){
            if(!f){
                return false;
            }else{
                $.ajax({
                    url: "/student/personalManagement/individualApplication/creditCertification/revokeInfo",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    beforeSend: function () {
                        $("#loading-btn").attr("data-loading-text", "正在提交...");
                        $("#loading-btn").button('loading');
                    },
                    complete: function () {
                        $("#loading-btn").removeAttr("data-loading-text");
                        $("#loading-btn").button('reset');
                    },
                    success: function (data) {
                        if(data.status != 200){
                            layer.alert(data.msg, {icon: 5,time: 3000});
                        } else {
                            if(data.data["result"].indexOf("/")!=-1){
                                window.location.href = data.data["result"];
                            }else{
                                if(data.data["result"] == "ok"){
                                    urp.alert("撤销成功！");
                                    getPageList(1,page_size,true);
                                }else{
                                    urp.alert(data.data["result"]);
                                }
                            }
                            $("#tokenValue").val(data.data["token"]);
                        }
                    },
                    error: function () {
                        urp.alert("撤销失败！");
                    }
                });
            }
        }
    }

    function returnIndex(){
        location.href = "/student/application/index";
    }
</script>
</body>
</html>