<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>辅修申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 辅修申请页面样式 */
        .minor-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .application-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .status-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            border-left: 4px solid var(--primary-color);
        }
        
        .status-info {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .status-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .minor-programs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .programs-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .programs-title {
            display: flex;
            align-items: center;
        }
        
        .programs-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .programs-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .program-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .program-item:last-child {
            border-bottom: none;
        }
        
        .program-item:active {
            background: var(--bg-color-active);
        }
        
        .program-item.available {
            border-left: 4px solid var(--success-color);
        }
        
        .program-item.full {
            border-left: 4px solid var(--warning-color);
        }
        
        .program-item.closed {
            border-left: 4px solid var(--error-color);
            opacity: 0.7;
        }
        
        .program-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .program-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .program-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-full {
            background: var(--warning-color);
            color: white;
        }
        
        .status-closed {
            background: var(--error-color);
            color: white;
        }
        
        .program-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .program-description {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
        }
        
        .program-requirements {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
        }
        
        .requirements-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .requirement-list {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .requirement-item {
            margin-bottom: 4px;
        }
        
        .requirement-item:last-child {
            margin-bottom: 0;
        }
        
        .program-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-apply {
            background: var(--success-color);
            color: white;
        }
        
        .btn-detail {
            background: var(--info-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .my-applications {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .applications-title {
            display: flex;
            align-items: center;
        }
        
        .applications-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .applications-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .application-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .application-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .application-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .application-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .minor-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .minor-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">辅修申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="minor-header">
            <div class="header-title">辅修申请</div>
            <div class="header-subtitle">申请辅修专业，拓展知识面和就业竞争力</div>
        </div>

        <!-- 申请状态 -->
        <div class="application-status">
            <div class="status-title">当前申请状态</div>
            <div class="status-card" id="currentStatus">
                <div class="status-info" id="statusInfo">暂无申请记录</div>
                <div class="status-desc" id="statusDesc">您可以申请辅修专业</div>
            </div>
        </div>

        <!-- 辅修专业列表 -->
        <div class="minor-programs">
            <div class="programs-header">
                <div class="programs-title">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span>可申请专业</span>
                </div>
                <div class="programs-count" id="programsCount">0</div>
            </div>

            <div id="programsList">
                <!-- 专业列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 我的申请 -->
        <div class="my-applications">
            <div class="applications-header">
                <div class="applications-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的申请</span>
                </div>
                <div class="applications-count" id="applicationsCount">0</div>
            </div>

            <div id="applicationsList">
                <!-- 申请列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-graduation-cap"></i>
            <div id="emptyMessage">暂无可申请的辅修专业</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 辅修申请表单 -->
    <div class="minor-form" id="minorForm">
        <div class="form-header">
            <div class="form-back" onclick="closeMinorForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">辅修申请</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">主修专业</div>
                    <input type="text" class="form-input" id="majorName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">年级班级</div>
                    <input type="text" class="form-input" id="gradeClass" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">当前GPA</div>
                    <input type="text" class="form-input" id="currentGPA" readonly>
                </div>
            </div>

            <!-- 申请专业信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span>申请专业信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">辅修专业</div>
                    <input type="text" class="form-input" id="minorProgram" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">所属学院</div>
                    <input type="text" class="form-input" id="minorCollege" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学制</div>
                    <input type="text" class="form-input" id="programDuration" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">总学分</div>
                    <input type="text" class="form-input" id="totalCredits" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学费标准</div>
                    <input type="text" class="form-input" id="tuitionFee" readonly>
                </div>
            </div>

            <!-- 申请信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">申请理由</div>
                    <textarea class="form-input form-textarea" id="applicationReason"
                              placeholder="请详细说明申请辅修该专业的理由和目标..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">相关基础</div>
                    <textarea class="form-input form-textarea" id="relatedBackground"
                              placeholder="请说明您在该专业领域的相关基础和经验..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">学习计划</div>
                    <textarea class="form-input form-textarea" id="studyPlan"
                              placeholder="请简述您的辅修学习计划和时间安排..."></textarea>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮箱地址</div>
                    <input type="email" class="form-input" id="email" placeholder="请输入邮箱地址">
                </div>

                <div class="form-group">
                    <div class="form-label">家长联系电话</div>
                    <input type="tel" class="form-input" id="parentPhone" placeholder="请输入家长联系电话">
                </div>
            </div>

            <!-- 承诺确认 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-check-circle"></i>
                    <span>承诺确认</span>
                </div>

                <div class="form-group">
                    <label class="form-checkbox">
                        <input type="checkbox" id="agreeTerms" required>
                        <span class="checkmark"></span>
                        我已阅读并同意辅修专业相关规定和要求
                    </label>
                </div>

                <div class="form-group">
                    <label class="form-checkbox">
                        <input type="checkbox" id="commitStudy" required>
                        <span class="checkmark"></span>
                        我承诺认真完成辅修专业的学习任务
                    </label>
                </div>

                <div class="form-group">
                    <label class="form-checkbox">
                        <input type="checkbox" id="acceptFee" required>
                        <span class="checkmark"></span>
                        我同意按规定缴纳辅修专业学费
                    </label>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeMinorForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitMinorApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let minorPrograms = [];
        let myApplications = [];
        let currentProgram = null;
        let studentInfo = {};
        let applicationStatus = {};

        $(function() {
            initPage();
            loadStudentInfo();
            loadApplicationStatus();
            loadMinorPrograms();
            loadMyApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/personalManagement/minorApplication/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 加载申请状态
        function loadApplicationStatus() {
            $.ajax({
                url: "/student/personalManagement/minorApplication/getApplicationStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    applicationStatus = data || {};
                    updateApplicationStatus();
                },
                error: function() {
                    console.log('加载申请状态失败');
                }
            });
        }

        // 更新申请状态
        function updateApplicationStatus() {
            if (applicationStatus.hasApplication) {
                $('#statusInfo').text(`已申请：${applicationStatus.programName}`);
                $('#statusDesc').text(`状态：${getStatusText(applicationStatus.status)}`);

                // 根据状态设置颜色
                const statusCard = $('.status-card');
                statusCard.removeClass('border-success border-warning border-error');

                switch(applicationStatus.status) {
                    case 'approved':
                        statusCard.css('border-left-color', 'var(--success-color)');
                        break;
                    case 'pending':
                        statusCard.css('border-left-color', 'var(--warning-color)');
                        break;
                    case 'rejected':
                        statusCard.css('border-left-color', 'var(--error-color)');
                        break;
                }
            } else {
                $('#statusInfo').text('暂无申请记录');
                $('#statusDesc').text('您可以申请辅修专业');
            }
        }

        // 加载辅修专业
        function loadMinorPrograms() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/minorApplication/getMinorPrograms",
                type: "post",
                dataType: "json",
                success: function(data) {
                    minorPrograms = data.programs || [];
                    renderProgramsList();
                    updateProgramsCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载辅修专业失败');
                    showLoading(false);
                }
            });
        }

        // 加载我的申请
        function loadMyApplications() {
            $.ajax({
                url: "/student/personalManagement/minorApplication/getMyApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myApplications = data.applications || [];
                    renderApplicationsList();
                    updateApplicationsCount();
                },
                error: function() {
                    console.log('加载申请列表失败');
                }
            });
        }

        // 渲染专业列表
        function renderProgramsList() {
            const container = $('#programsList');
            container.empty();

            if (minorPrograms.length === 0) {
                showEmptyState('暂无可申请的辅修专业');
                return;
            } else {
                hideEmptyState();
            }

            minorPrograms.forEach(program => {
                const programHtml = createProgramItem(program);
                container.append(programHtml);
            });
        }

        // 创建专业项
        function createProgramItem(program) {
            const statusClass = getProgramStatusClass(program.status);
            const statusText = getProgramStatusText(program.status);

            return `
                <div class="program-item ${statusClass}" onclick="showProgramDetail('${program.id}')">
                    <div class="program-header">
                        <div class="program-name">${program.name}</div>
                        <div class="program-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="program-info">
                        <div class="info-item">
                            <span>所属学院:</span>
                            <span>${program.college}</span>
                        </div>
                        <div class="info-item">
                            <span>学制:</span>
                            <span>${program.duration}</span>
                        </div>
                        <div class="info-item">
                            <span>总学分:</span>
                            <span>${program.totalCredits}</span>
                        </div>
                        <div class="info-item">
                            <span>学费:</span>
                            <span>¥${program.tuitionFee}/年</span>
                        </div>
                    </div>
                    <div class="program-description">${program.description}</div>
                    <div class="program-requirements">
                        <div class="requirements-title">申请要求</div>
                        <div class="requirement-list">
                            ${createRequirementList(program.requirements)}
                        </div>
                    </div>
                    <div class="program-actions">
                        ${createProgramActions(program)}
                    </div>
                </div>
            `;
        }

        // 创建要求列表
        function createRequirementList(requirements) {
            if (!requirements || requirements.length === 0) {
                return '<div class="requirement-item">暂无特殊要求</div>';
            }

            return requirements.map(req =>
                `<div class="requirement-item">• ${req}</div>`
            ).join('');
        }

        // 创建专业操作按钮
        function createProgramActions(program) {
            const canApply = program.status === 'available' && !applicationStatus.hasApplication;

            if (canApply) {
                return `
                    <button class="btn-mobile btn-apply flex-1" onclick="showMinorForm('${program.id}')">申请辅修</button>
                    <button class="btn-mobile btn-detail flex-1" onclick="showProgramDetail('${program.id}')">查看详情</button>
                `;
            } else {
                const buttonText = getActionButtonText(program.status, applicationStatus.hasApplication);
                return `
                    <button class="btn-mobile btn-disabled flex-1">${buttonText}</button>
                    <button class="btn-mobile btn-detail flex-1" onclick="showProgramDetail('${program.id}')">查看详情</button>
                `;
            }
        }

        // 获取专业状态样式类
        function getProgramStatusClass(status) {
            switch(status) {
                case 'available': return 'available';
                case 'full': return 'full';
                case 'closed': return 'closed';
                default: return 'closed';
            }
        }

        // 获取专业状态文本
        function getProgramStatusText(status) {
            switch(status) {
                case 'available': return '可申请';
                case 'full': return '名额已满';
                case 'closed': return '已关闭';
                default: return '未开放';
            }
        }

        // 获取操作按钮文本
        function getActionButtonText(status, hasApplication) {
            if (hasApplication) {
                return '已有申请';
            }

            switch(status) {
                case 'full': return '名额已满';
                case 'closed': return '申请已关闭';
                default: return '不可申请';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 显示辅修申请表单
        function showMinorForm(programId) {
            const program = minorPrograms.find(p => p.id === programId);
            if (!program) return;

            currentProgram = program;

            // 填充学生信息
            $('#formTitle').text(program.name + ' - 辅修申请');
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorName').val(studentInfo.majorName || '');
            $('#gradeClass').val((studentInfo.grade || '') + ' ' + (studentInfo.className || ''));
            $('#currentGPA').val(studentInfo.gpa || '');

            // 填充专业信息
            $('#minorProgram').val(program.name);
            $('#minorCollege').val(program.college);
            $('#programDuration').val(program.duration);
            $('#totalCredits').val(program.totalCredits + '学分');
            $('#tuitionFee').val('¥' + program.tuitionFee + '/年');

            // 清空申请信息
            resetMinorForm();

            // 显示表单
            $('#minorForm').addClass('show');
        }

        // 重置辅修表单
        function resetMinorForm() {
            $('#applicationReason').val('');
            $('#relatedBackground').val('');
            $('#studyPlan').val('');
            $('#contactPhone').val('');
            $('#email').val('');
            $('#parentPhone').val('');
            $('#agreeTerms').prop('checked', false);
            $('#commitStudy').prop('checked', false);
            $('#acceptFee').prop('checked', false);
        }

        // 关闭辅修申请表单
        function closeMinorForm() {
            $('#minorForm').removeClass('show');
        }

        // 提交辅修申请
        function submitMinorApplication() {
            if (!validateMinorForm()) {
                return;
            }

            const formData = collectMinorFormData();

            const message = `确定要申请"${currentProgram.name}"辅修专业吗？\n\n提交后将进入审核流程，请确保信息准确无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitMinorFormData(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    submitMinorFormData(formData);
                }
            }
        }

        // 收集辅修表单数据
        function collectMinorFormData() {
            return {
                programId: currentProgram.id,
                applicationReason: $('#applicationReason').val(),
                relatedBackground: $('#relatedBackground').val(),
                studyPlan: $('#studyPlan').val(),
                contactPhone: $('#contactPhone').val(),
                email: $('#email').val(),
                parentPhone: $('#parentPhone').val()
            };
        }

        // 验证辅修表单
        function validateMinorForm() {
            if (!$('#applicationReason').val().trim()) {
                showError('请填写申请理由');
                return false;
            }

            if (!$('#contactPhone').val().trim()) {
                showError('请填写联系电话');
                return false;
            }

            if (!$('#agreeTerms').prop('checked')) {
                showError('请同意辅修专业相关规定');
                return false;
            }

            if (!$('#commitStudy').prop('checked')) {
                showError('请承诺认真完成学习任务');
                return false;
            }

            if (!$('#acceptFee').prop('checked')) {
                showError('请同意缴纳辅修专业学费');
                return false;
            }

            return true;
        }

        // 提交辅修表单数据
        function submitMinorFormData(formData) {
            $.ajax({
                url: "/student/personalManagement/minorApplication/submitMinorApplication",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('辅修申请提交成功');
                        closeMinorForm();
                        loadApplicationStatus();
                        loadMinorPrograms();
                        loadMyApplications();
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示专业详情
        function showProgramDetail(programId) {
            const program = minorPrograms.find(p => p.id === programId);
            if (!program) return;

            let message = `辅修专业详情\n\n`;
            message += `专业名称：${program.name}\n`;
            message += `所属学院：${program.college}\n`;
            message += `学制：${program.duration}\n`;
            message += `总学分：${program.totalCredits}\n`;
            message += `学费标准：¥${program.tuitionFee}/年\n`;
            message += `申请状态：${getProgramStatusText(program.status)}\n`;
            message += `专业介绍：${program.description}\n`;

            if (program.requirements && program.requirements.length > 0) {
                message += `\n申请要求：\n`;
                program.requirements.forEach(req => {
                    message += `• ${req}\n`;
                });
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 渲染申请列表
        function renderApplicationsList() {
            const container = $('#applicationsList');
            container.empty();

            if (myApplications.length === 0) {
                return;
            }

            myApplications.forEach(application => {
                const applicationHtml = createApplicationItem(application);
                container.append(applicationHtml);
            });
        }

        // 创建申请项
        function createApplicationItem(application) {
            const statusClass = getApplicationStatusClass(application.status);
            const statusText = getStatusText(application.status);

            return `
                <div class="application-item ${statusClass}" onclick="showApplicationDetail('${application.id}')">
                    <div class="application-basic">
                        <div class="application-title">${application.programName}</div>
                        <div class="application-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="application-details">
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(application.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>所属学院:</span>
                            <span>${application.college}</span>
                        </div>
                        <div class="detail-item">
                            <span>学制:</span>
                            <span>${application.duration}</span>
                        </div>
                        <div class="detail-item">
                            <span>学费:</span>
                            <span>¥${application.tuitionFee}/年</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取申请状态样式类
        function getApplicationStatusClass(status) {
            switch(status) {
                case 'pending': return 'pending';
                case 'approved': return 'approved';
                case 'rejected': return 'rejected';
                default: return 'pending';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示申请详情
        function showApplicationDetail(applicationId) {
            const application = myApplications.find(app => app.id === applicationId);
            if (!application) return;

            let message = `辅修申请详情\n\n`;
            message += `专业名称：${application.programName}\n`;
            message += `申请时间：${formatDate(application.createTime)}\n`;
            message += `所属学院：${application.college}\n`;
            message += `申请理由：${application.applicationReason}\n`;
            message += `相关基础：${application.relatedBackground || '-'}\n`;
            message += `学习计划：${application.studyPlan || '-'}\n`;
            message += `当前状态：${getStatusText(application.status)}\n`;

            if (application.reviewComment) {
                message += `审核意见：${application.reviewComment}\n`;
            }

            if (application.reviewTime) {
                message += `审核时间：${formatDate(application.reviewTime)}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 更新专业数量
        function updateProgramsCount() {
            $('#programsCount').text(minorPrograms.length);
        }

        // 更新申请数量
        function updateApplicationsCount() {
            $('#applicationsCount').text(myApplications.length);
        }

        // 刷新数据
        function refreshData() {
            loadApplicationStatus();
            loadMinorPrograms();
            loadMyApplications();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.minor-programs').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.minor-programs').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.minor-programs').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#minorForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#minorForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeMinorForm();
            }
        });

        $('#minorForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
