<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>课程选择</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 选课页面样式 */
        .course-tabs {
            display: flex;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--divider-color);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .course-tab {
            flex: 1;
            padding: 12px 8px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-base);
        }
        
        .course-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: var(--bg-secondary);
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-row {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .search-row:last-child {
            margin-bottom: 0;
        }
        
        .course-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .course-credits {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .course-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .course-info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .course-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .course-status {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .btn-select {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 6px 16px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-select:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .btn-cancel {
            background: var(--error-color);
            color: white;
        }
        
        .btn-detail {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .selected-courses {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selected-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .selected-title {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .selected-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .selected-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm) 0;
            border-bottom: 1px solid var(--divider-color);
        }
        
        .selected-item:last-child {
            border-bottom: none;
        }
        
        .selected-course-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            flex: 1;
        }
        
        .selected-course-credits {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            margin-right: var(--margin-sm);
        }
        
        .time-conflict {
            border-left-color: var(--error-color);
        }
        
        .time-conflict .course-name {
            color: var(--error-color);
        }
        
        .conflict-warning {
            background: var(--error-color);
            color: white;
            padding: var(--padding-sm);
            border-radius: 4px;
            font-size: var(--font-size-mini);
            margin-top: var(--margin-xs);
        }
        
        .floating-summary {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border-top: 1px solid var(--divider-color);
            padding: var(--padding-md);
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .summary-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .summary-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .summary-credits {
            font-weight: 500;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">课程选择</div>
            <div class="navbar-action" onclick="showSelectedCourses();">
                <i class="ace-icon fa fa-list"></i>
            </div>
        </nav>
        
        <!-- 课程类型标签 -->
        <div class="course-tabs">
            <button class="course-tab active" onclick="switchTab('required')">必修课</button>
            <button class="course-tab" onclick="switchTab('elective')">选修课</button>
            <button class="course-tab" onclick="switchTab('public')">公共课</button>
            <button class="course-tab" onclick="switchTab('selected')">已选课程</button>
        </div>
        
        <!-- 搜索筛选 -->
        <div class="search-section">
            <div class="search-row">
                <select class="form-control" id="courseType" style="flex: 1;">
                    <option value="">课程类型</option>
                    <option value="必修">必修课</option>
                    <option value="选修">选修课</option>
                    <option value="公共">公共课</option>
                </select>
                <select class="form-control" id="college" style="flex: 1;">
                    <option value="">开课学院</option>
                </select>
            </div>
            <div class="search-row">
                <input type="text" class="form-control" id="courseName" placeholder="课程名称" style="flex: 1;">
                <button class="btn-mobile btn-primary" onclick="searchCourses();" style="flex: 0 0 80px;">搜索</button>
            </div>
        </div>
        
        <!-- 课程列表 -->
        <div class="container-mobile">
            <div id="courseList">
                <!-- 课程项将通过JavaScript动态填充 -->
            </div>
            
            <!-- 加载更多 -->
            <div class="loading-container" id="loadingMore" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-book"></i>
                <div>暂无可选课程</div>
            </div>
            
            <!-- 底部间距 -->
            <div style="height: 80px;"></div>
        </div>
        
        <!-- 浮动汇总 -->
        <div class="floating-summary" id="floatingSummary">
            <div class="summary-content">
                <div class="summary-info">
                    已选 <span class="summary-credits" id="selectedCount">0</span> 门课程，
                    共 <span class="summary-credits" id="totalCredits">0</span> 学分
                </div>
                <button class="btn-mobile btn-primary" onclick="submitSelection();">提交选课</button>
            </div>
        </div>
    </div>

    <!-- 课程详情模态框 -->
    <div class="modal fade" id="courseDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                    <h4 class="modal-title" id="courseDetailTitle">课程详情</h4>
                </div>
                <div class="modal-body" id="courseDetailBody">
                    <!-- 课程详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-mobile btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn-mobile btn-primary" id="selectCourseBtn">选择课程</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let isLoading = false;
        let hasMore = true;
        let allCourses = [];
        let selectedCourses = [];
        let currentTab = 'required';

        $(function() {
            initPage();
            loadColleges();
            loadCourses();
            loadSelectedCourses();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            
            // 绑定滚动事件
            $(window).scroll(function() {
                if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                    if (!isLoading && hasMore) {
                        loadMoreCourses();
                    }
                }
            });
        }

        // 加载学院列表
        function loadColleges() {
            $.ajax({
                url: "/student/courseSelectManagement/courseSelect/getColleges",
                type: "post",
                dataType: "json",
                success: function(data) {
                    const collegeSelect = $('#college');
                    collegeSelect.empty().append('<option value="">开课学院</option>');
                    
                    if (data && data.length > 0) {
                        data.forEach(function(college) {
                            collegeSelect.append(`<option value="${college.id}">${college.name}</option>`);
                        });
                    }
                },
                error: function() {
                    console.log('加载学院列表失败');
                }
            });
        }

        // 加载课程列表
        function loadCourses(page = 1, reset = true) {
            if (isLoading) return;
            
            isLoading = true;
            showLoading(true);

            const courseType = $('#courseType').val();
            const college = $('#college').val();
            const courseName = $('#courseName').val();

            $.ajax({
                url: "/student/courseSelectManagement/courseSelect/getCourses",
                type: "post",
                data: {
                    pageNum: page,
                    pageSize: 10,
                    courseType: courseType,
                    college: college,
                    courseName: courseName,
                    tab: currentTab
                },
                dataType: "json",
                success: function(data) {
                    const records = data.records || [];
                    const totalCount = data.pageContext.totalCount;
                    
                    if (reset) {
                        allCourses = records;
                        currentPage = 1;
                    } else {
                        allCourses = allCourses.concat(records);
                    }
                    
                    hasMore = allCourses.length < totalCount;
                    renderCourses();
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    isLoading = false;
                    showLoading(false);
                }
            });
        }

        // 渲染课程列表
        function renderCourses() {
            const container = $('#courseList');
            container.empty();
            
            if (allCourses.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            allCourses.forEach(function(course, index) {
                const courseHtml = createCourseItem(course, index);
                container.append(courseHtml);
            });
        }

        // 创建课程项HTML
        function createCourseItem(course, index) {
            const isSelected = selectedCourses.some(sc => sc.id === course.id);
            const hasConflict = checkTimeConflict(course);
            const conflictClass = hasConflict ? 'time-conflict' : '';
            
            let actionButton = '';
            if (currentTab === 'selected') {
                actionButton = `<button class="btn-select btn-cancel" onclick="cancelCourse('${course.id}')">退选</button>`;
            } else if (isSelected) {
                actionButton = `<button class="btn-select" disabled>已选</button>`;
            } else {
                actionButton = `<button class="btn-select" onclick="selectCourse('${course.id}')">选课</button>`;
            }
            
            return `
                <div class="course-item ${conflictClass}">
                    <div class="course-header">
                        <div class="course-name">${course.name}</div>
                        <div class="course-credits">${course.credits}学分</div>
                    </div>
                    <div class="course-info">
                        <div class="course-info-item">
                            <span>课程号:</span>
                            <span>${course.code}</span>
                        </div>
                        <div class="course-info-item">
                            <span>教师:</span>
                            <span>${course.teacher}</span>
                        </div>
                        <div class="course-info-item">
                            <span>时间:</span>
                            <span>${course.time}</span>
                        </div>
                        <div class="course-info-item">
                            <span>地点:</span>
                            <span>${course.location}</span>
                        </div>
                    </div>
                    ${hasConflict ? '<div class="conflict-warning">时间冲突</div>' : ''}
                    <div class="course-actions">
                        <div class="course-status">
                            余量: ${course.remaining}/${course.capacity}
                        </div>
                        <div>
                            <button class="btn-select btn-detail" onclick="showCourseDetail('${course.id}')">详情</button>
                            ${actionButton}
                        </div>
                    </div>
                </div>
            `;
        }

        // 检查时间冲突
        function checkTimeConflict(course) {
            return selectedCourses.some(sc => {
                return sc.time === course.time && sc.id !== course.id;
            });
        }

        // 选择课程
        function selectCourse(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;
            
            // 检查时间冲突
            if (checkTimeConflict(course)) {
                if (typeof urp !== 'undefined' && urp.confirm) {
                    urp.confirm('该课程与已选课程时间冲突，是否继续选择？', function(confirmed) {
                        if (confirmed) {
                            doSelectCourse(course);
                        }
                    });
                } else {
                    if (confirm('该课程与已选课程时间冲突，是否继续选择？')) {
                        doSelectCourse(course);
                    }
                }
            } else {
                doSelectCourse(course);
            }
        }

        // 执行选课
        function doSelectCourse(course) {
            $.ajax({
                url: "/student/courseSelectManagement/courseSelect/selectCourse",
                type: "post",
                data: { courseId: course.id },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        selectedCourses.push(course);
                        updateSummary();
                        renderCourses();
                        showSuccess('选课成功');
                    } else {
                        showError(data.message || '选课失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 退选课程
        function cancelCourse(courseId) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要退选该课程吗？', function(confirmed) {
                    if (confirmed) {
                        doCancelCourse(courseId);
                    }
                });
            } else {
                if (confirm('确定要退选该课程吗？')) {
                    doCancelCourse(courseId);
                }
            }
        }

        // 执行退选
        function doCancelCourse(courseId) {
            $.ajax({
                url: "/student/courseSelectManagement/courseSelect/cancelCourse",
                type: "post",
                data: { courseId: courseId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        selectedCourses = selectedCourses.filter(sc => sc.id !== courseId);
                        updateSummary();
                        renderCourses();
                        showSuccess('退选成功');
                    } else {
                        showError(data.message || '退选失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示课程详情
        function showCourseDetail(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;
            
            const detailHtml = `
                <div class="detail-item">
                    <div class="detail-label">课程名称</div>
                    <div class="detail-value">${course.name}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">课程号</div>
                    <div class="detail-value">${course.code}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">学分</div>
                    <div class="detail-value">${course.credits}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">授课教师</div>
                    <div class="detail-value">${course.teacher}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">上课时间</div>
                    <div class="detail-value">${course.time}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">上课地点</div>
                    <div class="detail-value">${course.location}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">课程容量</div>
                    <div class="detail-value">${course.capacity}人</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">剩余名额</div>
                    <div class="detail-value">${course.remaining}人</div>
                </div>
            `;
            
            $('#courseDetailTitle').text(course.name);
            $('#courseDetailBody').html(detailHtml);
            
            const isSelected = selectedCourses.some(sc => sc.id === course.id);
            const selectBtn = $('#selectCourseBtn');
            if (isSelected) {
                selectBtn.text('已选择').prop('disabled', true);
            } else {
                selectBtn.text('选择课程').prop('disabled', false).off('click').on('click', function() {
                    $('#courseDetailModal').modal('hide');
                    selectCourse(course.id);
                });
            }
            
            $('#courseDetailModal').modal('show');
        }

        // 切换标签
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签状态
            $('.course-tab').removeClass('active');
            $(event.target).addClass('active');
            
            // 重新加载课程
            loadCourses();
        }

        // 搜索课程
        function searchCourses() {
            loadCourses(1, true);
        }

        // 加载已选课程
        function loadSelectedCourses() {
            $.ajax({
                url: "/student/courseSelectManagement/courseSelect/getSelectedCourses",
                type: "post",
                dataType: "json",
                success: function(data) {
                    selectedCourses = data || [];
                    updateSummary();
                },
                error: function() {
                    console.log('加载已选课程失败');
                }
            });
        }

        // 更新汇总信息
        function updateSummary() {
            const count = selectedCourses.length;
            const credits = selectedCourses.reduce((sum, course) => sum + parseFloat(course.credits || 0), 0);
            
            $('#selectedCount').text(count);
            $('#totalCredits').text(credits.toFixed(1));
        }

        // 显示已选课程
        function showSelectedCourses() {
            switchTab('selected');
            $('.course-tab').removeClass('active');
            $('.course-tab').eq(3).addClass('active');
        }

        // 提交选课
        function submitSelection() {
            if (selectedCourses.length === 0) {
                showError('请先选择课程');
                return;
            }
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要提交选课结果吗？', function(confirmed) {
                    if (confirmed) {
                        doSubmitSelection();
                    }
                });
            } else {
                if (confirm('确定要提交选课结果吗？')) {
                    doSubmitSelection();
                }
            }
        }

        // 执行提交选课
        function doSubmitSelection() {
            const courseIds = selectedCourses.map(sc => sc.id);
            
            $.ajax({
                url: "/student/courseSelectManagement/courseSelect/submitSelection",
                type: "post",
                data: { courseIds: courseIds.join(',') },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('选课提交成功');
                        loadSelectedCourses();
                    } else {
                        showError(data.message || '提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 加载更多课程
        function loadMoreCourses() {
            if (!hasMore || isLoading) return;
            
            currentPage++;
            loadCourses(currentPage, false);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingMore').show();
            } else {
                $('#loadingMore').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const summaryHeight = $('.floating-summary').outerHeight();
            const containerHeight = windowHeight - navbarHeight - summaryHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
