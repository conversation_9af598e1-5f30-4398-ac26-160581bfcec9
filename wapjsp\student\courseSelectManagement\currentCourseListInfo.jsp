<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>补修选课</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 补修选课页面样式 */
        .course-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
            margin-right: var(--margin-sm);
        }
        
        .course-code {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        .btn-select {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            min-width: 60px;
        }
        
        .btn-select:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .btn-select:active {
            transform: translateY(0);
        }
        
        .btn-select:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
            transform: none;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
            font-weight: 500;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .teacher-info {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
        }
        
        .teacher-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .schedule-info {
            margin-top: var(--margin-sm);
        }
        
        .schedule-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .schedule-item {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-bottom: 4px;
            font-size: var(--font-size-small);
        }
        
        .schedule-item:last-child {
            margin-bottom: 0;
        }
        
        .time-info {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .location-info {
            color: var(--text-secondary);
            margin-top: 2px;
        }
        
        .capacity-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: var(--margin-sm);
        }
        
        .capacity-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .capacity-available {
            background: var(--success-color);
            color: white;
        }
        
        .capacity-limited {
            background: var(--warning-color);
            color: white;
        }
        
        .capacity-full {
            background: var(--error-color);
            color: white;
        }
        
        .restriction-info {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
        }
        
        .restriction-title {
            font-size: var(--font-size-small);
            color: var(--warning-color);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .restriction-content {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .no-courses-message {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-lg);
            margin: var(--margin-md);
            text-align: center;
        }
        
        .no-courses-icon {
            font-size: 48px;
            color: var(--info-color);
            margin-bottom: var(--margin-md);
        }
        
        .no-courses-text {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        @media (max-width: 480px) {
            .course-details {
                grid-template-columns: 1fr;
            }
            
            .course-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn-select {
                margin-top: var(--margin-sm);
                align-self: flex-end;
                width: auto;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">补修选课</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 课程列表 -->
        <div id="courseList">
            <c:choose>
                <c:when test="${!empty cxfxbxList}">
                    <c:forEach items="${cxfxbxList}" var="info" varStatus="infoIndex">
                        <div class="course-item">
                            <div class="course-header">
                                <div>
                                    <div class="course-title">${info.kcm}</div>
                                    <div class="course-code">${info.kch}_${info.kxh}</div>
                                </div>
                                <button class="btn-select" onclick="selectCourse('${info.kch}', '${info.kxh}', '${info.kcm}');">
                                    选择
                                </button>
                            </div>
                            
                            <div class="course-details">
                                <div class="detail-item">
                                    <span class="detail-label">开课院系:</span>
                                    <span class="detail-value">${info.kkxsjc}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">学分:</span>
                                    <span class="detail-value">${info.xf}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">考试类型:</span>
                                    <span class="detail-value">${info.kslxmc}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">选课模式:</span>
                                    <span class="detail-value">${info.xkmssm}</span>
                                </div>
                            </div>
                            
                            <div class="teacher-info">
                                <div class="teacher-name">
                                    <i class="ace-icon fa fa-user"></i>
                                    任课教师: ${info.skjs}
                                </div>
                            </div>
                            
                            <div class="capacity-info">
                                <span class="detail-label">课余量:</span>
                                <c:choose>
                                    <c:when test="${info.bkskyl > 10}">
                                        <span class="capacity-badge capacity-available">${info.bkskyl}人</span>
                                    </c:when>
                                    <c:when test="${info.bkskyl > 0}">
                                        <span class="capacity-badge capacity-limited">${info.bkskyl}人</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span class="capacity-badge capacity-full">已满</span>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                            
                            <c:if test="${!empty info.sjdd}">
                                <div class="schedule-info">
                                    <div class="schedule-title">
                                        <i class="ace-icon fa fa-clock-o"></i>
                                        上课安排
                                    </div>
                                    <c:forEach items="${info.sjdd}" var="sjdd">
                                        <div class="schedule-item">
                                            <div class="time-info">
                                                ${sjdd.zcsm == null || sjdd.zcsm == "null" ? '无' : sjdd.zcsm}
                                                <c:choose>
                                                    <c:when test="${sjdd.skxq == '1'}">星期一</c:when>
                                                    <c:when test="${sjdd.skxq == '2'}">星期二</c:when>
                                                    <c:when test="${sjdd.skxq == '3'}">星期三</c:when>
                                                    <c:when test="${sjdd.skxq == '4'}">星期四</c:when>
                                                    <c:when test="${sjdd.skxq == '5'}">星期五</c:when>
                                                    <c:when test="${sjdd.skxq == '6'}">星期六</c:when>
                                                    <c:when test="${sjdd.skxq == '7'}">星期日</c:when>
                                                    <c:otherwise>无</c:otherwise>
                                                </c:choose>
                                                第${sjdd.skjc == null || sjdd.skjc == "null" ? '无' : (sjdd.skjc).concat("~").concat(sjdd.skjc + sjdd.cxjc - 1)}节
                                            </div>
                                            <div class="location-info">
                                                <i class="ace-icon fa fa-map-marker"></i>
                                                ${sjdd.xqm == null || sjdd.xqm == "null" ? '无' : sjdd.xqm}
                                                ${sjdd.jxlm == null || sjdd.jxlm == "null" ? '' : sjdd.jxlm}
                                                ${sjdd.jasm == null || sjdd.jasm == "null" ? '' : sjdd.jasm}
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </c:if>
                            
                            <c:if test="${!empty info.xkkzsm || !empty info.xkxzsm}">
                                <div class="restriction-info">
                                    <div class="restriction-title">
                                        <i class="ace-icon fa fa-warning"></i>
                                        选课限制
                                    </div>
                                    <div class="restriction-content">
                                        <c:if test="${!empty info.xkkzsm}">
                                            <div>选课控制: ${info.xkkzsm}</div>
                                        </c:if>
                                        <c:if test="${!empty info.xkxzsm}">
                                            <div>选课限制: ${info.xkxzsm}</div>
                                        </c:if>
                                    </div>
                                </div>
                            </c:if>
                        </div>
                    </c:forEach>
                </c:when>
                <c:when test="${empty cxfxbxList}">
                    <div class="no-courses-message">
                        <div class="no-courses-icon">
                            <i class="ace-icon fa fa-info-circle"></i>
                        </div>
                        <div class="no-courses-text">
                            课程 ${kcm}（${kch}）的课程在当前学期没有开设或没有维护替代课，只有本学期开课了才可以修读
                        </div>
                    </div>
                </c:when>
            </c:choose>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 选择课程
        function selectCourse(kch, kxh, kcm) {
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm("确认选择" + kcm + "(" + kch + "_" + kxh + ")？", function(confirmed) {
                    if (confirmed) {
                        submitCourseSelection(kch, kxh, kcm);
                    }
                });
            } else {
                if (confirm("确认选择" + kcm + "(" + kch + "_" + kxh + ")？")) {
                    submitCourseSelection(kch, kxh, kcm);
                }
            }
        }

        // 提交选课
        function submitCourseSelection(kch, kxh, kcm) {
            showLoading(true);
            
            $.ajax({
                url: "/student/courseSelect/fixCourseSelect/submit",
                type: "post",
                dataType: "json",
                data: "kckxh=" + kch + "_" + kxh + "&kcm=" + kcm + "&tokenValue=" + $("#tokenValue").val(),
                success: function(data) {
                    const result = data["result"];
                    const info = result.split(";")[0].split(":")[1];
                    
                    if (info === "ok") {
                        showSuccess("选课成功！", function() {
                            window.location.reload();
                        });
                    } else {
                        $("#tokenValue").val(data["token"]);
                        const message = info || result;
                        showError(message);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
