<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>缓考申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 缓考申请页面样式 */
        .exam-header {
            background: linear-gradient(135deg, var(--warning-color), var(--error-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .exam-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .exam-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--warning-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--warning-color);
            box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
        }
        
        .form-select {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-select:focus {
            outline: none;
            border-color: var(--warning-color);
            box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
        }
        
        .btn-search {
            background: var(--warning-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-search:hover {
            background: var(--warning-dark);
        }
        
        .exam-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-header i {
            color: var(--warning-color);
        }
        
        .exam-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .exam-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .exam-item:last-child {
            border-bottom: none;
        }
        
        .exam-header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .exam-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--warning-light);
            color: var(--warning-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .exam-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .exam-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .exam-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .exam-actions {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
        }
        
        .btn-action {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-add {
            background: var(--success-color);
            color: white;
        }
        
        .btn-add:hover {
            background: var(--success-dark);
        }
        
        .btn-edit {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit:hover {
            background: var(--info-dark);
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-delete:hover {
            background: var(--error-dark);
        }
        
        .exam-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .meta-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .meta-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-reviewing {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--warning-light);
            border-color: var(--warning-color);
            color: var(--warning-dark);
        }
        
        .btn-page.active {
            background: var(--warning-color);
            border-color: var(--warning-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .exam-header-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .exam-meta {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">缓考申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 缓考申请头部 -->
        <div class="exam-header">
            <div class="exam-title">缓考申请管理</div>
            <div class="exam-desc">申请、修改和查看缓考信息</div>
        </div>
        
        <!-- 搜索条件 -->
        <div class="search-container">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form class="search-form" id="searchForm">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">课程号</label>
                        <input type="text" class="form-input" name="kch" id="kch" placeholder="请输入课程号">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">课程名</label>
                        <input type="text" class="form-input" name="kcm" id="kcm" placeholder="请输入课程名">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">院系审批状态</label>
                        <select class="form-select" name="yxspzt" id="yxspzt">
                            <option value="">请选择</option>
                            <c:forEach items="${sqztb}" var="spzt">
                                <option value="${spzt[0]}">${spzt[1]}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">教务处审批状态</label>
                        <select class="form-select" name="spzt" id="spzt">
                            <option value="">请选择</option>
                            <c:forEach items="${sqztb}" var="spzt">
                                <option value="${spzt[0]}">${spzt[1]}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <button type="button" class="btn-search" onclick="searchExams();">
                            <i class="ace-icon fa fa-search"></i>
                            查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 缓考申请列表 -->
        <div class="exam-container">
            <div class="container-header">
                <i class="ace-icon fa fa-list"></i>
                申请缓考列表
            </div>
            
            <div class="exam-list" id="examList">
                <!-- 动态加载缓考申请列表 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;

        $(function() {
            initPage();
            loadExams(1);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载缓考申请
        function loadExams(page = 1) {
            currentPage = page;
            showLoading(true);

            const formData = $('#searchForm').serialize();

            $.ajax({
                url: "/student/exam/ApplicationDelayedExam/queryAll",
                type: "post",
                data: formData + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    if (response && response.records) {
                        renderExams(response.records);
                        renderPagination(response.pageContext);
                    } else {
                        showEmptyState('examList', '暂无可申请数据');
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染缓考申请
        function renderExams(exams) {
            const container = $('#examList');

            if (!exams || exams.length === 0) {
                showEmptyState('examList', '暂无可申请数据');
                return;
            }

            let examsHtml = '';

            exams.forEach((exam, index) => {
                const serialNumber = (currentPage - 1) * pageSize + 1 + index;
                const canApply = !exam.ISEXIST && exam.YXSPZT !== "02" && exam.YXSPZT !== "03";
                const canEdit = exam.ISEXIST && exam.YXSPZT !== "02" && exam.YXSPZT !== "03";

                examsHtml += `
                    <div class="exam-item">
                        <div class="exam-header-row">
                            <div class="exam-number">${serialNumber}</div>
                            <div class="exam-info">
                                <div class="exam-course">${exam.KCM || exam.KCH}</div>
                                <div class="exam-details">
                                    学年学期：${exam.ZXJXJHM || ''} | 课序号：${exam.KXH || ''}
                                </div>
                            </div>
                            <div class="exam-actions">
                                ${canApply ? `
                                    <button class="btn-action btn-add" onclick="addApplication('${exam.KCH}', '${exam.KXH}');">
                                        <i class="ace-icon fa fa-plus"></i>
                                        申请
                                    </button>
                                ` : ''}
                                ${canEdit ? `
                                    <button class="btn-action btn-edit" onclick="editApplication('${exam.KCH}', '${exam.KXH}');">
                                        <i class="ace-icon fa fa-edit"></i>
                                        修改
                                    </button>
                                    <button class="btn-action btn-delete" onclick="deleteApplication('${exam.KCH}', '${exam.KXH}');">
                                        <i class="ace-icon fa fa-trash"></i>
                                        删除
                                    </button>
                                ` : ''}
                            </div>
                        </div>

                        <div class="exam-meta">
                            <div class="meta-item">
                                <div class="meta-label">申请原因</div>
                                <div class="meta-value">${exam.SQYY ? (exam.SQYY.length > 10 ? exam.SQYY.substring(0, 10) + '...' : exam.SQYY) : '未填写'}</div>
                            </div>

                            <div class="meta-item">
                                <div class="meta-label">附件</div>
                                <div class="meta-value">${exam.FJ || '无'}</div>
                            </div>

                            <div class="meta-item">
                                <div class="meta-label">院系审批状态</div>
                                <div class="meta-value">
                                    <span class="status-badge
                                        ${exam.YXSPZT === '01' ? 'status-pending' : ''}
                                        ${exam.YXSPZT === '02' ? 'status-approved' : ''}
                                        ${exam.YXSPZT === '03' ? 'status-rejected' : ''}
                                        ${exam.YXSPZT === '04' ? 'status-reviewing' : ''}
                                    ">
                                        ${exam.YXSPSM || '未审批'}
                                    </span>
                                </div>
                            </div>

                            <div class="meta-item">
                                <div class="meta-label">教务处审批状态</div>
                                <div class="meta-value">
                                    <span class="status-badge
                                        ${exam.SPZT === '01' ? 'status-pending' : ''}
                                        ${exam.SPZT === '02' ? 'status-approved' : ''}
                                        ${exam.SPZT === '03' ? 'status-rejected' : ''}
                                        ${exam.SPZT === '04' ? 'status-reviewing' : ''}
                                    ">
                                        ${exam.SPSM || '未审批'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.html(examsHtml);
        }

        // 显示空状态
        function showEmptyState(containerId, message) {
            const container = $('#' + containerId);
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <div class="empty-state-text">${message}</div>
                    <div class="empty-state-desc">请调整搜索条件后重试</div>
                </div>
            `);
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            if (!pageContext || pageContext.totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="loadExams(${currentPage - 1});">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<button class="btn-page" onclick="loadExams(1);">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="loadExams(${i});">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                paginationHtml += `<button class="btn-page" onclick="loadExams(${totalPages});">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="loadExams(${currentPage + 1});">下一页</button>`;

            container.html(paginationHtml);
            $('#paginationContainer').show();
        }

        // 搜索缓考申请
        function searchExams() {
            loadExams(1);
        }

        // 添加申请
        function addApplication(kch, kxh) {
            // 在移动端使用简化的模态框
            if (confirm('确定要申请缓考吗？')) {
                // 这里应该跳转到添加页面或打开模态框
                // 由于移动端限制，我们使用页面跳转
                window.location.href = `/student/exam/ApplicationDelayedExam/doAdd/${kch}/${kxh}`;
            }
        }

        // 编辑申请
        function editApplication(kch, kxh) {
            if (confirm('确定要修改申请吗？')) {
                window.location.href = `/student/exam/ApplicationDelayedExam/doUpdate/${kch}/${kxh}`;
            }
        }

        // 删除申请
        function deleteApplication(kch, kxh) {
            if (confirm('您是否要删除?')) {
                showLoading(true);

                $.ajax({
                    url: `/student/exam/ApplicationDelayedExam/doDel/${kch}/${kxh}?tokenValue=${$('#tokenValue').val()}`,
                    type: "delete",
                    dataType: "json",
                    success: function(response) {
                        $('#tokenValue').val(response.token);

                        if (response.result.indexOf("/") !== -1) {
                            window.location.href = response.result;
                        } else {
                            if (response.result === "ok") {
                                showSuccess("删除成功！");
                                loadExams(currentPage);
                            } else if (response.result === "no") {
                                showError("删除失败！");
                            } else if (response.result === "nostatus") {
                                showError("申请已审批，不能删除");
                            }
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 刷新数据
        function refreshData() {
            loadExams(currentPage);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
