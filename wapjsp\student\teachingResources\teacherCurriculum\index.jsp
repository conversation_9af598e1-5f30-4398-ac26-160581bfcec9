<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教师课表</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教师课表页面样式 */
        .teacher-header {
            background: linear-gradient(135deg, var(--info-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .teacher-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .teacher-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--info-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--info-color);
            box-shadow: 0 0 0 2px rgba(23, 162, 184, 0.2);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .btn-search {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            margin-top: var(--margin-sm);
        }
        
        .btn-search:hover {
            background: var(--info-dark);
        }
        
        .teachers-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-header i {
            color: var(--info-color);
        }
        
        .teacher-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .teacher-item:last-child {
            border-bottom: none;
        }
        
        .teacher-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--info-light);
            color: var(--info-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .teacher-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .teacher-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .teacher-details {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .teacher-detail {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
            flex-shrink: 0;
        }
        
        .btn-view:hover {
            background: var(--info-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--info-light);
            border-color: var(--info-color);
            color: var(--info-dark);
        }
        
        .btn-page.active {
            background: var(--info-color);
            border-color: var(--info-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .evaluation-notice {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            font-size: var(--font-size-small);
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .evaluation-notice i {
            color: var(--warning-color);
            margin-top: 2px;
        }
        
        .evaluation-notice-content {
            flex: 1;
            line-height: 1.4;
        }
        
        @media (max-width: 480px) {
            .teacher-details {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .teacher-item {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
            
            .btn-view {
                align-self: stretch;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="onOff" value="${onOff}">
    <input type="hidden" id="currentPlanCode" value="${currentPlanCode}">
    <input type="hidden" id="isFromIndex" value="${isFromIndex}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教师课表</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 教师课表头部 -->
        <div class="teacher-header">
            <div class="teacher-title">教师课表</div>
            <div class="teacher-desc">查询教师课程安排信息</div>
        </div>
        
        <!-- 评估提示 -->
        <c:if test="${onOff == '0'}">
            <div class="evaluation-notice">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <div class="evaluation-notice-content">
                    没有完成评估，不能查看课表！
                </div>
            </div>
        </c:if>
        
        <c:if test="${onOff == '1'}">
            <!-- 搜索容器 -->
            <div class="search-container">
                <div class="search-title">
                    <i class="ace-icon fa fa-search"></i>
                    搜索教师
                </div>
                
                <form class="search-form" id="searchForm" name="jskbform">
                    <input type="hidden" name="param_value" value="${param_value}">
                    
                    <div class="form-group">
                        <label class="form-label">学期</label>
                        <select class="form-input form-select" name="executiveEducationPlanNumber">
                            <option value="">请选择学期</option>
                            <cache:query var="planList" fields="executiveEducationPlanNumber,executiveEducationPlanName" region="JXJH_CACHE_XSJXJHB" orderby="executiveEducationPlanNumber desc"/>
                            <c:forEach items="${planList}" var="plan">
                                <option value="${plan.executiveEducationPlanNumber}">${plan.executiveEducationPlanName}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">教师姓名</label>
                        <input type="text" class="form-input" name="teacherName" placeholder="请输入教师姓名">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">院系</label>
                        <select class="form-input form-select" name="departmentNumber">
                            <option value="">请选择院系</option>
                            <cache:query var="deptList" fields="departmentNumber,departmentName" region="DEPT_CACHE_YXSB" orderby="departmentNumber asc"/>
                            <c:forEach items="${deptList}" var="dept">
                                <option value="${dept.departmentNumber}">${dept.departmentName}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <button type="button" class="btn-search" onclick="searchTeachers();">
                        <i class="ace-icon fa fa-search"></i>
                        搜索
                    </button>
                </form>
            </div>
            
            <!-- 教师列表 -->
            <div class="teachers-list" id="teachersList" style="display: none;">
                <div class="list-header">
                    <i class="ace-icon fa fa-list"></i>
                    教师列表
                </div>
                <div id="teachersContainer">
                    <!-- 动态加载教师列表 -->
                </div>
            </div>
            
            <!-- 分页容器 -->
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <div class="pagination-info" id="paginationInfo"></div>
                <div class="pagination-buttons" id="paginationButtons"></div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;

        $(function() {
            initPage();
            
            // 检查是否有初始数据
            const onOff = $('#onOff').val();
            const currentPlanCode = $('#currentPlanCode').val();
            const isFromIndex = $('#isFromIndex').val();
            
            if (onOff === '1') {
                if (currentPlanCode) {
                    $("select[name=executiveEducationPlanNumber]").val(currentPlanCode);
                }
                
                if (isFromIndex) {
                    try {
                        const data = JSON.parse(isFromIndex);
                        renderTeachers(data, false, 0, 0);
                    } catch (e) {
                        searchTeachers();
                    }
                } else {
                    searchTeachers();
                }
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索教师
        function searchTeachers(page = 1) {
            currentPage = page;
            showLoading(true);

            const formData = $('#searchForm').serialize() + `&pageNum=${page}&pageSize=${pageSize}`;

            $.ajax({
                url: "/student/teachingResources/teacherCurriculum/search",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data && data[0]) {
                        const records = data[0].records;
                        const pageContext = data[0].pageContext;

                        totalCount = pageContext.totalCount;
                        renderTeachers(records, page > 1, page, pageSize);
                        renderPagination(pageContext);

                        $('#teachersList').show();
                        $('#paginationContainer').show();
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("获取教师列表失败");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染教师列表
        function renderTeachers(teachers, isAppend, page, pageSize) {
            const container = $('#teachersContainer');

            if (!teachers || teachers.length === 0) {
                showEmptyState();
                return;
            }

            let teachersHtml = '';

            teachers.forEach((teacher, index) => {
                const serialNumber = isAppend ? (page - 1) * pageSize + 1 + index : index + 1;

                teachersHtml += `
                    <div class="teacher-item">
                        <div class="teacher-number">${serialNumber}</div>
                        <div class="teacher-info">
                            <div class="teacher-name">${teacher.teacherName || '-'}</div>
                            <div class="teacher-details">
                                <div class="teacher-detail">
                                    <i class="ace-icon fa fa-building"></i>
                                    <span>${teacher.departmentName || '-'}</span>
                                </div>
                                <div class="teacher-detail">
                                    <i class="ace-icon fa fa-graduation-cap"></i>
                                    <span>${teacher.professionalTitle || '-'}</span>
                                </div>
                            </div>
                        </div>
                        <button class="btn-view" onclick="viewTeacherSchedule('${teacher.id.executiveEducationPlanNumber}', '${teacher.id.teacherNumber}', '${teacher.executiveEducationPlanName}', '${teacher.teacherName}');">
                            <i class="ace-icon fa fa-eye"></i>
                            查看课表
                        </button>
                    </div>
                `;
            });

            if (isAppend) {
                container.append(teachersHtml);
            } else {
                container.html(teachersHtml);
            }
        }

        // 显示空状态
        function showEmptyState() {
            const container = $('#teachersContainer');
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-user-times"></i>
                    <div class="empty-state-text">暂无教师信息</div>
                    <div class="empty-state-desc">请调整搜索条件后重试</div>
                </div>
            `);
            $('#teachersList').show();
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            if (totalPages <= 1) {
                container.empty();
                return;
            }

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="searchTeachers(${currentPage - 1});">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<button class="btn-page" onclick="searchTeachers(1);">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="searchTeachers(${i});">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                paginationHtml += `<button class="btn-page" onclick="searchTeachers(${totalPages});">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="searchTeachers(${currentPage + 1});">下一页</button>`;

            container.html(paginationHtml);
        }

        // 查看教师课表
        function viewTeacherSchedule(planCode, teacherNum, planName, teacherName) {
            showLoading(true);

            $.ajax({
                url: "/student/teachingResources/teacherCurriculum/searchCurriculumInfoLo",
                type: "post",
                data: {
                    planCode: planCode,
                    teacherNum: teacherNum,
                    planName: planName,
                    teacherName: teacherName
                },
                dataType: "json",
                success: function(data) {
                    // 跳转到课表详情页面
                    const url = `/student/teachingResources/teacherCurriculum/detail?planCode=${data.planCode}&teacherNum=${data.teacherNum}&planName=${encodeURIComponent(data.planName)}&teacherName=${encodeURIComponent(data.teacherName)}`;

                    if (parent && parent.addTab) {
                        parent.addTab(`${data.teacherName}的课表`, url);
                    } else {
                        window.location.href = url;
                    }
                },
                error: function(xhr) {
                    showError("获取课表信息失败");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 刷新数据
        function refreshData() {
            currentPage = 1;
            searchTeachers();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
