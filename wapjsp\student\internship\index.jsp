<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实习管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实习管理页面样式 */
        .internship-header {
            background: linear-gradient(135deg, var(--warning-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .internship-icon {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            opacity: 0.9;
        }
        
        .internship-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .internship-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .function-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .function-card:hover::before {
            left: 100%;
        }
        
        .function-card:hover {
            border-color: var(--warning-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .function-card:active {
            transform: translateY(-2px);
        }
        
        .function-icon {
            font-size: 40px;
            margin-bottom: var(--margin-md);
            color: var(--warning-color);
            position: relative;
            z-index: 1;
        }
        
        .function-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .function-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
            position: relative;
            z-index: 1;
        }
        
        .function-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: var(--font-size-mini);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }
        
        .internship-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--info-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--warning-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-actions-title i {
            color: var(--success-color);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-item {
            text-align: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .action-item:hover {
            background: var(--bg-tertiary);
            transform: translateY(-2px);
        }
        
        .action-item-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--warning-light);
            color: var(--warning-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto var(--margin-sm);
        }
        
        .action-item-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .current-internship {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--warning-color);
        }
        
        .current-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .current-title i {
            color: var(--warning-color);
        }
        
        .internship-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .internship-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .internship-item:last-child {
            margin-bottom: 0;
        }
        
        .internship-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .internship-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .internship-code {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        .internship-status {
            background: var(--success-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .internship-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-item i {
            color: var(--warning-color);
            width: 14px;
        }
        
        .internship-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-internship-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-daily {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        @media (max-width: 480px) {
            .function-grid {
                grid-template-columns: 1fr;
            }
            
            .action-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .internship-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实习管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 实习管理头部 -->
        <div class="internship-header">
            <div class="internship-icon">
                <i class="ace-icon fa fa-briefcase"></i>
            </div>
            <div class="internship-title">实习管理中心</div>
            <div class="internship-desc">实习日志、报告、过程管理</div>
        </div>
        
        <!-- 实习统计 -->
        <div class="internship-stats">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                实习统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalInternships">0</div>
                    <div class="stat-label">总实习数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedReports">0</div>
                    <div class="stat-label">已提交报告</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="pendingReports">0</div>
                    <div class="stat-label">待提交报告</div>
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="quick-actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                快速操作
            </div>
            <div class="action-grid">
                <div class="action-item" onclick="goToFunction('daily');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-edit"></i>
                    </div>
                    <div class="action-item-title">实习日志</div>
                </div>
                <div class="action-item" onclick="goToFunction('sxbggl');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-file-text"></i>
                    </div>
                    <div class="action-item-title">实习报告</div>
                </div>
                <div class="action-item" onclick="goToFunction('sxgcgl');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-tasks"></i>
                    </div>
                    <div class="action-item-title">过程管理</div>
                </div>
                <div class="action-item" onclick="goToFunction('internshipExecutionPlanCompletion');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-check-circle"></i>
                    </div>
                    <div class="action-item-title">计划完成</div>
                </div>
            </div>
        </div>
        
        <!-- 功能模块 -->
        <div class="function-grid">
            <div class="function-card" onclick="goToFunction('daily');">
                <div class="function-badge">
                    <i class="ace-icon fa fa-star"></i>
                </div>
                <div class="function-icon">
                    <i class="ace-icon fa fa-edit"></i>
                </div>
                <div class="function-title">实习日志</div>
                <div class="function-desc">记录每日实习情况</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('sxbggl');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-file-text-o"></i>
                </div>
                <div class="function-title">实习报告管理</div>
                <div class="function-desc">提交和管理实习报告</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('sxgcgl');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-tasks"></i>
                </div>
                <div class="function-title">实习过程管理</div>
                <div class="function-desc">跟踪实习进度</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('internshipExecutionPlanCompletion');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-check-circle-o"></i>
                </div>
                <div class="function-title">实习计划完成</div>
                <div class="function-desc">查看计划执行情况</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('sxzbgl');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-cog"></i>
                </div>
                <div class="function-title">实习准备管理</div>
                <div class="function-desc">实习前期准备工作</div>
            </div>
        </div>
        
        <!-- 当前实习课程 -->
        <div class="current-internship" id="currentInternship" style="display: none;">
            <div class="current-title">
                <i class="ace-icon fa fa-clock-o"></i>
                当前实习课程
            </div>
            <div id="internshipList">
                <!-- 动态加载实习课程 -->
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let internshipStats = {};
        let currentInternships = [];

        $(function() {
            initPage();
            loadInternshipStats();
            loadCurrentInternships();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载实习统计
        function loadInternshipStats() {
            showLoading(true);

            // 获取实习统计信息
            $.ajax({
                url: "/student/internship/getStats",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        internshipStats = data.data;
                        updateStats();
                    } else {
                        // 设置默认统计
                        updateStats({
                            totalInternships: 0,
                            completedReports: 0,
                            pendingReports: 0
                        });
                    }
                },
                error: function() {
                    console.log('获取实习统计失败');
                    // 设置默认统计
                    updateStats({
                        totalInternships: 0,
                        completedReports: 0,
                        pendingReports: 0
                    });
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新统计信息
        function updateStats(stats = internshipStats) {
            $('#totalInternships').text(stats.totalInternships || 0);
            $('#completedReports').text(stats.completedReports || 0);
            $('#pendingReports').text(stats.pendingReports || 0);
        }

        // 加载当前实习课程
        function loadCurrentInternships() {
            $.ajax({
                url: "/student/internship/daily/queryPage",
                type: "post",
                data: "pageNum=1&pageSize=100",
                dataType: "json",
                success: function(data) {
                    if (data && data.records && data.records.length > 0) {
                        currentInternships = data.records;
                        renderCurrentInternships();
                        $('#currentInternship').show();
                    }
                },
                error: function() {
                    console.log('获取当前实习课程失败');
                }
            });
        }

        // 渲染当前实习课程
        function renderCurrentInternships() {
            const container = $('#internshipList');
            container.empty();

            currentInternships.forEach(function(internship) {
                const internshipHtml = createInternshipItem(internship);
                container.append(internshipHtml);
            });
        }

        // 创建实习项目HTML
        function createInternshipItem(internship) {
            const currentTime = new Date().toISOString().split('T')[0].replace(/-/g, '');
            const times = internship.TIMES ? internship.TIMES.split(',') : [];

            // 检查是否可以填写日报
            const canWriteDaily = (internship.KTB > 0 && internship.YTB == 0 && times.indexOf(currentTime) == -1);
            if (canWriteDaily) {
                times.push(currentTime);
            }

            const hasReports = internship.TBZS > 0;

            return `
                <div class="internship-item">
                    <div class="internship-header-info">
                        <div>
                            <div class="internship-name">${internship.KCM || ''}</div>
                            <div class="internship-code">课程号：${internship.KCH || ''} | 课序号：${internship.KXH || ''}</div>
                        </div>
                        <div class="internship-status">进行中</div>
                    </div>

                    <div class="internship-details">
                        <div class="detail-item">
                            <i class="ace-icon fa fa-calendar"></i>
                            <span>${internship.XNXQMC || ''}</span>
                        </div>
                        <div class="detail-item">
                            <i class="ace-icon fa fa-map-marker"></i>
                            <span>${(internship.SJDD || '').replace(/,/g, ' ').replace(/@/g, ' ')}</span>
                        </div>
                    </div>

                    <div class="internship-actions">
                        ${times.length > 0 ? createDailyButtons(internship.ID, times) : ''}
                        ${hasReports ? `
                            <button class="btn-internship-action btn-view" onclick="viewReports('${internship.ID}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看日报</span>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 创建日报按钮
        function createDailyButtons(internshipId, times) {
            if (times.length === 1) {
                return `
                    <button class="btn-internship-action btn-daily" onclick="writeDailyReport('${internshipId}', '${times[0]}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>实习日报维护</span>
                    </button>
                `;
            } else if (times.length > 1) {
                let buttons = `
                    <button class="btn-internship-action btn-daily" onclick="writeDailyReport('${internshipId}', '${times[0]}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>${times[0]}</span>
                    </button>
                `;

                // 如果有多个时间，可以添加下拉菜单或其他时间选择方式
                if (times.length > 1) {
                    buttons += `
                        <button class="btn-internship-action btn-view" onclick="showTimeSelector('${internshipId}', '${times.join(',')}');">
                            <i class="ace-icon fa fa-calendar"></i>
                            <span>更多日期</span>
                        </button>
                    `;
                }

                return buttons;
            }
            return '';
        }

        // 写实习日报
        function writeDailyReport(internshipId, date) {
            const url = "/student/internship/daily/add?rwid=" + internshipId + "&tbrq=" + date;

            if (parent && parent.addTab) {
                parent.addTab('实习日报维护', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看日报记录
        function viewReports(internshipId) {
            const url = "/student/internship/daily/view?rwid=" + internshipId;

            if (parent && parent.addTab) {
                parent.addTab('实习日报记录', url);
            } else {
                window.location.href = url;
            }
        }

        // 显示时间选择器
        function showTimeSelector(internshipId, timesStr) {
            const times = timesStr.split(',');
            let options = '';

            times.forEach(function(time, index) {
                if (index > 0) { // 跳过第一个，因为已经有按钮了
                    options += `<option value="${time}">${time}</option>`;
                }
            });

            if (options) {
                const selectHtml = `
                    <select onchange="writeDailyReport('${internshipId}', this.value); this.selectedIndex = 0;">
                        <option value="">选择日期</option>
                        ${options}
                    </select>
                `;

                // 这里可以用更好的UI组件替代简单的select
                showError('请选择要维护的日期');
            }
        }

        // 跳转到功能页面
        function goToFunction(functionName) {
            let url = '';
            let title = '';

            switch(functionName) {
                case 'daily':
                    url = '/student/internship/daily/index';
                    title = '实习日志';
                    break;
                case 'sxbggl':
                    url = '/student/internship/sxbggl/uploadIndex';
                    title = '实习报告管理';
                    break;
                case 'sxgcgl':
                    url = '/student/internship/sxgcgl/sxgcglIndex';
                    title = '实习过程管理';
                    break;
                case 'internshipExecutionPlanCompletion':
                    url = '/student/internship/internshipExecutionPlanCompletion/index';
                    title = '实习计划完成';
                    break;
                case 'sxzbgl':
                    url = '/student/internship/sxzbgl/sxzbglIndex';
                    title = '实习准备管理';
                    break;
                default:
                    showError('功能暂未开放');
                    return;
            }

            if (parent && parent.addTab) {
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 刷新数据
        function refreshData() {
            loadInternshipStats();
            loadCurrentInternships();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
