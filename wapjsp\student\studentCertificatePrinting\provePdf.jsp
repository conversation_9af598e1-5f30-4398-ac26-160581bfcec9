<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证明</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证明PDF查看页面样式 */
        .pdf-header {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .pdf-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .pdf-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .pdf-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .pdf-toolbar {
            background: var(--bg-tertiary);
            padding: var(--padding-sm) var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .pdf-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .pdf-info i {
            color: var(--info-color);
        }
        
        .pdf-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .pdf-action-btn {
            background: none;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .pdf-action-btn:hover {
            background: var(--bg-color-active);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .pdf-action-btn.primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .pdf-action-btn.primary:hover {
            background: var(--primary-dark);
        }
        
        .pdf-viewer {
            width: 100%;
            height: calc(100vh - 200px);
            min-height: 500px;
            border: none;
            background: var(--bg-secondary);
        }
        
        .pdf-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: var(--text-secondary);
            background: var(--bg-secondary);
        }
        
        .pdf-loading i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--info-color);
            animation: spin 2s linear infinite;
        }
        
        .pdf-loading-text {
            font-size: var(--font-size-base);
            margin-bottom: var(--margin-sm);
        }
        
        .pdf-loading-desc {
            font-size: var(--font-size-small);
            color: var(--text-disabled);
            text-align: center;
            line-height: 1.4;
        }
        
        .pdf-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: var(--error-color);
            background: var(--bg-secondary);
            text-align: center;
            padding: var(--padding-lg);
        }
        
        .pdf-error i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
        }
        
        .pdf-error-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }
        
        .pdf-error-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
            margin-bottom: var(--margin-md);
        }
        
        .btn-retry {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-retry:hover {
            background: var(--primary-dark);
        }
        
        .download-notice {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            font-size: var(--font-size-small);
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .download-notice i {
            color: var(--warning-color);
            margin-top: 2px;
        }
        
        .download-notice-content {
            flex: 1;
            line-height: 1.4;
        }
        
        .download-notice-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 480px) {
            .pdf-toolbar {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: stretch;
            }
            
            .pdf-actions {
                justify-content: center;
            }
            
            .pdf-viewer {
                height: calc(100vh - 250px);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证明</div>
            <div class="navbar-action" onclick="refreshPdf();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学生证明头部 -->
        <div class="pdf-header">
            <div class="pdf-title">学生证明</div>
            <div class="pdf-desc">查看和下载学生证明文件</div>
        </div>
        
        <!-- 下载提示 -->
        <div class="download-notice">
            <i class="ace-icon fa fa-info-circle"></i>
            <div class="download-notice-content">
                <div class="download-notice-title">温馨提示</div>
                <div>如果PDF无法正常显示，请点击下载按钮保存到本地查看</div>
            </div>
        </div>
        
        <!-- PDF容器 -->
        <div class="pdf-container">
            <div class="pdf-toolbar">
                <div class="pdf-info">
                    <i class="ace-icon fa fa-file-pdf-o"></i>
                    <span>学生证明文件</span>
                </div>
                <div class="pdf-actions">
                    <button class="pdf-action-btn" onclick="downloadPdf();">
                        <i class="ace-icon fa fa-download"></i>
                        <span>下载</span>
                    </button>
                    <button class="pdf-action-btn" onclick="printPdf();">
                        <i class="ace-icon fa fa-print"></i>
                        <span>打印</span>
                    </button>
                    <button class="pdf-action-btn primary" onclick="openFullscreen();">
                        <i class="ace-icon fa fa-expand"></i>
                        <span>全屏</span>
                    </button>
                </div>
            </div>
            
            <!-- PDF查看器 -->
            <div id="pdfViewerContainer">
                <div class="pdf-loading" id="pdfLoading">
                    <i class="ace-icon fa fa-spinner"></i>
                    <div class="pdf-loading-text">正在加载PDF文件...</div>
                    <div class="pdf-loading-desc">请稍候，正在为您准备证明文件</div>
                </div>
                
                <div class="pdf-error" id="pdfError" style="display: none;">
                    <i class="ace-icon fa fa-exclamation-triangle"></i>
                    <div class="pdf-error-title">PDF加载失败</div>
                    <div class="pdf-error-desc">
                        无法加载PDF文件，可能是网络问题或文件不存在。<br>
                        您可以尝试重新加载或直接下载文件。
                    </div>
                    <button class="btn-retry" onclick="retryLoadPdf();">
                        <i class="ace-icon fa fa-refresh"></i>
                        重新加载
                    </button>
                </div>
                
                <iframe id="pdfViewer" class="pdf-viewer" style="display: none;"></iframe>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>处理中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let pdfFileName = '${fjmc}';
        let pdfUrl = '';
        let isLoaded = false;

        $(function() {
            initPage();
            loadPdf();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载PDF
        function loadPdf() {
            if (!pdfFileName) {
                showError('PDF文件名不存在');
                return;
            }
            
            // 构建PDF URL
            const fileName = "/student/studentCertificatePrinting/index/prove/" + pdfFileName;
            pdfUrl = "/pdf/web/viewer.html?file=" + encodeURIComponent(fileName);
            
            // 显示加载状态
            showLoading();
            
            // 加载PDF
            const iframe = document.getElementById('pdfViewer');
            iframe.onload = function() {
                hideLoading();
                showPdf();
                isLoaded = true;
            };
            
            iframe.onerror = function() {
                hideLoading();
                showError('PDF加载失败');
            };
            
            // 设置超时
            setTimeout(() => {
                if (!isLoaded) {
                    hideLoading();
                    showError('PDF加载超时');
                }
            }, 10000);
            
            iframe.src = pdfUrl;
        }

        // 显示加载状态
        function showLoading() {
            $('#pdfLoading').show();
            $('#pdfError').hide();
            $('#pdfViewer').hide();
        }

        // 隐藏加载状态
        function hideLoading() {
            $('#pdfLoading').hide();
        }

        // 显示PDF
        function showPdf() {
            $('#pdfViewer').show();
            $('#pdfError').hide();
        }

        // 显示错误
        function showError(message) {
            $('#pdfError').show();
            $('#pdfViewer').hide();
            console.error('PDF Error:', message);
        }

        // 重新加载PDF
        function retryLoadPdf() {
            isLoaded = false;
            loadPdf();
        }

        // 刷新PDF
        function refreshPdf() {
            isLoaded = false;
            loadPdf();
        }

        // 下载PDF
        function downloadPdf() {
            if (!pdfFileName) {
                showAlert('PDF文件不存在');
                return;
            }
            
            const downloadUrl = "/student/studentCertificatePrinting/index/prove/" + pdfFileName;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = pdfFileName;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 打印PDF
        function printPdf() {
            if (!isLoaded) {
                showAlert('PDF尚未加载完成');
                return;
            }
            
            try {
                const iframe = document.getElementById('pdfViewer');
                iframe.contentWindow.print();
            } catch (e) {
                showAlert('打印功能不可用，请下载后打印');
            }
        }

        // 全屏查看
        function openFullscreen() {
            if (!pdfUrl) {
                showAlert('PDF文件不存在');
                return;
            }
            
            window.open(pdfUrl, '_blank');
        }

        // 显示提示信息
        function showAlert(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const headerHeight = $('.pdf-header').outerHeight();
            const noticeHeight = $('.download-notice').outerHeight();
            const toolbarHeight = $('.pdf-toolbar').outerHeight();
            
            const availableHeight = windowHeight - navbarHeight - headerHeight - noticeHeight - toolbarHeight - 40;
            $('.pdf-viewer').css('height', Math.max(availableHeight, 400) + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
