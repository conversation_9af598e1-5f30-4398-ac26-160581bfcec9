<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>新生入学登记表</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 入学登记表页面样式 */
        .registration-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .registration-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .registration-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        /* 表单区域样式 */
        .form-section-mobile {
            margin-bottom: var(--margin-lg);
        }
        
        .section-header-mobile {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding: var(--padding-md);
            background: var(--bg-tertiary);
            border-radius: 8px;
        }
        
        .section-title-mobile {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .section-title-mobile i {
            font-size: 18px;
        }
        
        .add-btn-mobile {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .add-btn-mobile:hover {
            background: var(--success-dark);
        }
        
        .add-btn-mobile:active {
            transform: translateY(1px);
        }
        
        .info-block-mobile {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            margin-bottom: var(--margin-md);
            overflow: hidden;
        }
        
        .block-header-mobile {
            background: var(--bg-tertiary);
            padding: var(--padding-sm) var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .block-title-mobile {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .delete-btn-mobile {
            background: var(--error-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .delete-btn-mobile:hover {
            background: var(--error-dark);
        }
        
        .block-content-mobile {
            padding: var(--padding-md);
        }
        
        .form-group-mobile {
            margin-bottom: var(--margin-md);
        }
        
        .form-label-mobile {
            display: block;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .form-label-mobile.required::after {
            content: " *";
            color: var(--error-color);
            font-weight: bold;
        }
        
        .form-input-mobile {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--transition-base);
            box-sizing: border-box;
        }
        
        .form-input-mobile:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-input-mobile:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .form-select-mobile {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            box-sizing: border-box;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-select-mobile:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .date-input-group {
            position: relative;
        }
        
        .date-input-group .form-input-mobile {
            padding-right: 40px;
        }
        
        .date-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .guardian-switch {
            background: var(--primary-light);
            color: var(--primary-dark);
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            margin-left: var(--margin-sm);
            transition: all var(--transition-base);
        }
        
        .guardian-switch:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .empty-state-mobile {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin-bottom: var(--margin-md);
        }
        
        .empty-state-mobile i {
            font-size: 48px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-md);
        }
        
        .empty-state-mobile .empty-text {
            font-size: var(--font-size-base);
            margin-bottom: var(--margin-sm);
        }
        
        .empty-state-mobile .empty-action {
            color: var(--primary-color);
            cursor: pointer;
            text-decoration: underline;
        }
        
        .form-actions-mobile {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
            z-index: 1000;
        }
        
        .form-error-mobile {
            color: var(--error-color);
            font-size: var(--font-size-small);
            margin-top: 4px;
            display: none;
        }
        
        .warning-container {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
        }
        
        .warning-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-title i {
            color: var(--warning-color);
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .section-header-mobile {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: stretch;
            }
            
            .block-header-mobile {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: stretch;
            }
        }
        
        /* 为底部固定按钮留出空间 */
        .page-mobile {
            padding-bottom: 80px;
        }
        
        /* 表单验证错误样式 */
        .form-input-mobile.error {
            border-color: var(--error-color) !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">新生入学登记表</div>
            <div class="navbar-action" onclick="doSave();" style="display: ${flag == 'showAdd' ? 'flex' : 'none'};">
                <i class="ace-icon fa fa-save"></i>
            </div>
        </nav>
        
        <!-- 登记表头部 -->
        <div class="registration-header">
            <div class="registration-title">新生入学登记表</div>
            <div class="registration-desc">完善您的家庭成员信息和教育经历</div>
        </div>
        
        <!-- 主要内容区域 -->
        <div id="showMessage">
            <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
            
            <!-- 家庭成员信息 -->
            <div class="card-mobile">
                <div class="section-header-mobile">
                    <div class="section-title-mobile">
                        <i class="ace-icon fa fa-users"></i>
                        <span>家庭成员信息</span>
                    </div>
                    <c:if test="${flag == 'showAdd'}">
                        <button class="add-btn-mobile" onclick="addXsJtcybs();">
                            <i class="ace-icon fa fa-plus"></i>
                            <span>添加</span>
                        </button>
                    </c:if>
                </div>
                
                <div id="xsJtcybs">
                    <!-- 家庭成员信息将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <!-- 个人教育经历 -->
            <div class="card-mobile">
                <div class="section-header-mobile">
                    <div class="section-title-mobile">
                        <i class="ace-icon fa fa-graduation-cap"></i>
                        <span>个人的教育经历</span>
                    </div>
                    <c:if test="${flag == 'showAdd'}">
                        <button class="add-btn-mobile" onclick="addXsGrjlbs();">
                            <i class="ace-icon fa fa-plus"></i>
                            <span>添加</span>
                        </button>
                    </c:if>
                </div>
                
                <div id="xsGrjlbs">
                    <!-- 个人教育经历将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <!-- 固定底部操作按钮 -->
            <c:if test="${flag == 'showAdd'}">
                <div class="form-actions-mobile">
                    <button type="button" class="btn-mobile btn-primary flex-1" onclick="doSave();">
                        <i class="ace-icon fa fa-save"></i>
                        <span>保存信息</span>
                    </button>
                    <button type="button" class="btn-mobile btn-secondary flex-1" onclick="returnIndex();">
                        <i class="ace-icon fa fa-arrow-left"></i>
                        <span>返回</span>
                    </button>
                </div>
            </c:if>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 隐藏的提示内容 -->
    <textarea class="form-control" id="ydnrstr" name="ydnrstr" style="display: none;">${ywsqkzb.ydnrstr}</textarea>

    <script>
        // 全局变量
        var xsJtcybList = [];
        var xsGrjlbList = [];
        var jtcyIndex = 0;
        var grjlIndex = 0;

        $(function() {
            initPage();
            initValidation();
            checkNotice();
            queryDiv();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 检查是否需要显示提示信息
        function checkNotice() {
            var sfxyd = '${ywsqkzb.sfxyd}';
            if (sfxyd == "1" && "${flag}" == "showAdd") {
                showNotice();
            }
        }

        // 显示提示信息
        function showNotice() {
            var time = parseInt('${ywsqkzb.qzydms}') || 10;
            var ydnr = $("#ydnrstr").val();

            if (!ydnr) return;

            var modal = `
                <div class="modal-overlay" id="noticeModal">
                    <div class="modal-content-mobile">
                        <div class="modal-header-mobile">
                            <h4>重要提示</h4>
                        </div>
                        <div class="modal-body-mobile">
                            <pre style="white-space: pre-wrap; max-height: 300px; overflow-y: auto;">${ydnr}</pre>
                        </div>
                        <div class="modal-footer-mobile">
                            <button class="btn-mobile btn-primary" id="noticeClose" disabled>
                                关闭（<span id="countdown">${time}</span>s）
                            </button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modal);

            var timer = setInterval(function() {
                time--;
                $('#countdown').text(time);
                if (time <= 0) {
                    $('#noticeClose').prop('disabled', false).html('关闭').off('click').on('click', function() {
                        $('#noticeModal').remove();
                    });
                    clearInterval(timer);
                }
            }, 1000);
        }

        // 查询数据
        function queryDiv() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/entranceRegistrationForm/index/queryDiv",
                cache: false,
                type: "post",
                dataType: "json",
                success: function(d) {
                    var data = d.data;
                    if ((data["xsGrjlbsCount"] > 0 || data["xsJtcybsCount"] > 0) || "${flag}" == "showAdd") {
                        buildDiv(data["xsJtcybList"], data["xsGrjlbList"], data["xsGrjlbs"], data["xsJtcybs"], data["xsGrjlbsCount"], data["xsJtcybsCount"]);
                    } else {
                        $("#showMessage").html(`
                            <div class="warning-container">
                                <div class="warning-title">
                                    <i class="ace-icon fa fa-exclamation-triangle"></i>
                                    提示信息
                                </div>
                                <div>当前时间不允许进行此申请</div>
                                <div style="margin-top: var(--margin-md);">
                                    <button class="btn-mobile btn-primary" onclick="returnIndex();">
                                        <i class="ace-icon fa fa-arrow-left"></i>
                                        <span>返回上一页</span>
                                    </button>
                                </div>
                            </div>
                        `);
                    }
                },
                error: function(xhr) {
                    showToast("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 构建页面内容
        function buildDiv(xsJtcybListData, xsGrjlbListData, xsGrjlbs, xsJtcybs, xsGrjlbsCount, xsJtcybsCount) {
            xsJtcybList = xsJtcybListData;
            xsGrjlbList = xsGrjlbListData;

            // 构建家庭成员信息
            if ("${flag}" == "showAdd") {
                if (xsJtcybsCount > 0) {
                    var jtcyData = JSON.parse(xsJtcybs);
                    jtcyData.forEach(function(item, index) {
                        addJtcyBlock(item, index);
                    });
                } else {
                    showJtcyEmpty();
                }

                // 构建个人教育经历
                if (xsGrjlbsCount > 0) {
                    var grjlData = JSON.parse(xsGrjlbs);
                    grjlData.forEach(function(item, index) {
                        addGrjlBlock(item, index);
                    });
                } else {
                    showGrjlEmpty();
                }

                initFormValidation();
            } else {
                // 只读模式的显示逻辑
                buildReadOnlyView(xsJtcybs, xsGrjlbs, xsJtcybsCount, xsGrjlbsCount);
            }
        }

        // 显示家庭成员空状态
        function showJtcyEmpty() {
            $("#xsJtcybs").html(`
                <div class="empty-state-mobile" id="jtcy_empty">
                    <i class="ace-icon fa fa-users"></i>
                    <div class="empty-text">暂无家庭成员信息</div>
                    <div class="empty-action" onclick="addXsJtcybs();">点击添加</div>
                </div>
            `);
        }

        // 显示教育经历空状态
        function showGrjlEmpty() {
            $("#xsGrjlbs").html(`
                <div class="empty-state-mobile" id="grjl_empty">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <div class="empty-text">暂无教育经历信息</div>
                    <div class="empty-action" onclick="addXsGrjlbs();">点击添加</div>
                </div>
            `);
        }

        // 添加家庭成员
        function addXsJtcybs() {
            $("#jtcy_empty").remove();
            addJtcyBlock({}, jtcyIndex);
            jtcyIndex++;
        }

        // 添加教育经历
        function addXsGrjlbs() {
            $("#grjl_empty").remove();
            addGrjlBlock({}, grjlIndex);
            grjlIndex++;
        }

        // 添加家庭成员块
        function addJtcyBlock(data, index) {
            var blockHtml = `
                <div class="info-block-mobile" id="jtcy_block_${index}">
                    <div class="block-header-mobile">
                        <div class="block-title-mobile">家庭成员信息${index > 0 ? index : ''}</div>
                        ${index > 0 ? `
                            <button class="delete-btn-mobile" onclick="deleteJtcyBlock(${index});">
                                <i class="ace-icon fa fa-trash"></i>
                                <span>删除</span>
                            </button>
                        ` : ''}
                    </div>
                    <div class="block-content-mobile">
                        <form class="jtcy-form">
                            <input type="hidden" name="xsJtcybs[${index}].id" value="${data.id || ''}"/>
                            <input type="hidden" name="xsJtcybs[${index}].xh" value="${data.xh || ''}"/>
            `;

            xsJtcybList.forEach(function(field) {
                if (field.id.colname != "id" && field.id.colname != "xh") {
                    blockHtml += `<div class="form-group-mobile">`;
                    blockHtml += `<label class="form-label-mobile required">${field.colcomment}</label>`;

                    if (field.id.colname == "csny") {
                        // 出生年月日期选择
                        blockHtml += `
                            <div class="date-input-group">
                                <input type="date" name="xsJtcybs[${index}].${field.id.colname}"
                                       class="form-input-mobile" value="${data[field.id.colname] || '${studentRoll.csrq}' || ''}" required/>
                                <i class="ace-icon fa fa-calendar date-icon"></i>
                            </div>
                        `;
                    } else if (field.id.colname == "zzmmdm") {
                        // 政治面貌选择
                        blockHtml += `
                            <select name="xsJtcybs[${index}].${field.id.colname}" class="form-select-mobile" required>
                                <option value="">--请选择--</option>
                        `;
                        // 这里需要从缓存中获取政治面貌数据
                        blockHtml += `
                            </select>
                        `;
                    } else if (field.id.colname == "gx") {
                        // 关系字段特殊处理
                        var defaultValue = index == 0 ? "父亲" : (index == 1 ? "母亲" : (data[field.id.colname] || ""));
                        var isDefault = (index == 0 && defaultValue == "父亲") || (index == 1 && defaultValue == "母亲");

                        if (isDefault) {
                            blockHtml += `
                                <div style="display: flex; align-items: center;">
                                    <span>${defaultValue}</span>
                                    <input type="hidden" name="xsJtcybs[${index}].${field.id.colname}" value="${defaultValue}"/>
                                    <button type="button" class="guardian-switch" onclick="switchGuardian(${index}, '${field.id.colname}');">
                                        填写其他监护人
                                    </button>
                                </div>
                            `;
                        } else {
                            blockHtml += `
                                <div style="display: flex; align-items: center;">
                                    <input type="text" name="xsJtcybs[${index}].${field.id.colname}"
                                           class="form-input-mobile" value="${defaultValue}" required
                                           style="flex: 1; margin-right: 8px;"/>
                                    <button type="button" class="guardian-switch" onclick="resetGuardian(${index}, '${field.id.colname}');">
                                        ${index == 0 ? '填写父亲信息' : '填写母亲信息'}
                                    </button>
                                </div>
                            `;
                        }
                    } else {
                        // 普通输入字段
                        var maxLength = "";
                        var inputType = "text";
                        var extraAttrs = "";

                        switch (field.id.colname) {
                            case "xm":
                                maxLength = "maxlength='100'";
                                break;
                            case "zw":
                                maxLength = "maxlength='20'";
                                break;
                            case "gzdw":
                                maxLength = "maxlength='100'";
                                break;
                            case "lxdh":
                                inputType = "tel";
                                maxLength = "maxlength='20'";
                                break;
                            case "wxh":
                                maxLength = "maxlength='30'";
                                break;
                            case "sfzh":
                                maxLength = "maxlength='20'";
                                break;
                        }

                        blockHtml += `
                            <input type="${inputType}" name="xsJtcybs[${index}].${field.id.colname}"
                                   class="form-input-mobile" value="${data[field.id.colname] || ''}"
                                   ${maxLength} required ${extraAttrs}/>
                        `;
                    }

                    blockHtml += `<div class="form-error-mobile"></div>`;
                    blockHtml += `</div>`;
                }
            });

            blockHtml += `
                        </form>
                    </div>
                </div>
            `;

            $("#xsJtcybs").append(blockHtml);
        }

        // 添加教育经历块
        function addGrjlBlock(data, index) {
            var blockHtml = `
                <div class="info-block-mobile" id="grjl_block_${index}">
                    <div class="block-header-mobile">
                        <div class="block-title-mobile">个人的教育经历${index > 0 ? index : ''}</div>
                        ${index > 0 ? `
                            <button class="delete-btn-mobile" onclick="deleteGrjlBlock(${index});">
                                <i class="ace-icon fa fa-trash"></i>
                                <span>删除</span>
                            </button>
                        ` : ''}
                    </div>
                    <div class="block-content-mobile">
                        <form class="grjl-form">
                            <input type="hidden" name="xsGrjlbs[${index}].id" value="${data.id || ''}"/>
                            <input type="hidden" name="xsGrjlbs[${index}].xh" value="${data.xh || ''}"/>
            `;

            xsGrjlbList.forEach(function(field) {
                if (field.id.colname != "id" && field.id.colname != "xh") {
                    blockHtml += `<div class="form-group-mobile">`;
                    blockHtml += `<label class="form-label-mobile required">${field.colcomment}</label>`;

                    var maxLength = "";
                    switch (field.id.colname) {
                        case "dw":
                            maxLength = "maxlength='100'";
                            break;
                        case "zw":
                            maxLength = "maxlength='20'";
                            break;
                        case "zmr":
                            maxLength = "maxlength='20'";
                            break;
                        case "qzny":
                            maxLength = "maxlength='20'";
                            break;
                    }

                    blockHtml += `
                        <input type="text" name="xsGrjlbs[${index}].${field.id.colname}"
                               class="form-input-mobile" value="${data[field.id.colname] || ''}"
                               ${maxLength} required/>
                    `;

                    blockHtml += `<div class="form-error-mobile"></div>`;
                    blockHtml += `</div>`;
                }
            });

            blockHtml += `
                        </form>
                    </div>
                </div>
            `;

            $("#xsGrjlbs").append(blockHtml);
        }

        // 删除家庭成员块
        function deleteJtcyBlock(index) {
            if (confirm("确定要删除这个家庭成员信息吗？")) {
                $("#jtcy_block_" + index).remove();

                // 检查是否还有数据
                if ($("#xsJtcybs .info-block-mobile").length === 0) {
                    showJtcyEmpty();
                }
            }
        }

        // 删除教育经历块
        function deleteGrjlBlock(index) {
            if (confirm("确定要删除这个教育经历信息吗？")) {
                $("#grjl_block_" + index).remove();

                // 检查是否还有数据
                if ($("#xsGrjlbs .info-block-mobile").length === 0) {
                    showGrjlEmpty();
                }
            }
        }

        // 切换监护人
        function switchGuardian(index, fieldName) {
            var container = $(`input[name="xsJtcybs[${index}].${fieldName}"]`).parent();
            var currentValue = $(`input[name="xsJtcybs[${index}].${fieldName}"]`).val();

            container.html(`
                <div style="display: flex; align-items: center;">
                    <input type="text" name="xsJtcybs[${index}].${fieldName}"
                           class="form-input-mobile" value="" required
                           style="flex: 1; margin-right: 8px;"/>
                    <button type="button" class="guardian-switch" onclick="resetGuardian(${index}, '${fieldName}');">
                        ${index == 0 ? '填写父亲信息' : '填写母亲信息'}
                    </button>
                </div>
            `);
        }

        // 重置监护人
        function resetGuardian(index, fieldName) {
            var container = $(`input[name="xsJtcybs[${index}].${fieldName}"]`).parent();
            var defaultValue = index == 0 ? "父亲" : "母亲";

            container.html(`
                <div style="display: flex; align-items: center;">
                    <span>${defaultValue}</span>
                    <input type="hidden" name="xsJtcybs[${index}].${fieldName}" value="${defaultValue}"/>
                    <button type="button" class="guardian-switch" onclick="switchGuardian(${index}, '${fieldName}');">
                        填写其他监护人
                    </button>
                </div>
            `);
        }

        // 初始化表单验证
        function initFormValidation() {
            // 实时验证
            $(document).on('blur', '.form-input-mobile[required]', function() {
                validateField($(this));
            });
        }

        // 验证单个字段
        function validateField(field) {
            var value = field.val().trim();
            var errorDiv = field.siblings('.form-error-mobile');
            var fieldName = field.attr('name');

            var isValid = true;
            var errorMsg = '';

            // 必填验证
            if (!value) {
                isValid = false;
                errorMsg = '此字段为必填项';
            } else {
                // 字符长度验证
                var maxLength = field.attr('maxlength');
                if (maxLength && getStringLength(value) > parseInt(maxLength)) {
                    isValid = false;
                    errorMsg = '最大长度不能超过' + maxLength + '个字符，一个汉字为两个字符';
                }

                // 特殊字段验证
                if (fieldName && fieldName.includes('.gx') && value && !/^[\u4e00-\u9fa5]*$/.test(value)) {
                    isValid = false;
                    errorMsg = '只能输入汉字';
                }

                if (fieldName && fieldName.includes('.lxdh') && value && !/^1[3-9]\d{9}$/.test(value)) {
                    isValid = false;
                    errorMsg = '请输入正确的手机号码';
                }

                if (fieldName && fieldName.includes('.sfzh') && value) {
                    var idCardError = validateIdCard(value);
                    if (idCardError) {
                        isValid = false;
                        errorMsg = idCardError;
                    }
                }

                if (fieldName && fieldName.includes('.wxh') && value && !/^[a-zA-Z]([-_a-zA-Z0-9]{5,19})+$/.test(value)) {
                    isValid = false;
                    errorMsg = '请填写正确的微信号';
                }
            }

            if (isValid) {
                errorDiv.hide();
                field.removeClass('error');
            } else {
                errorDiv.text(errorMsg).show();
                field.addClass('error');
            }

            return isValid;
        }

        // 计算字符串长度（中文算2个字符）
        function getStringLength(str) {
            var length = 0;
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) > 19967) {
                    length += 2;
                } else {
                    length++;
                }
            }
            return length;
        }

        // 身份证验证
        function validateIdCard(value) {
            var city = {11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外"};

            if (!value || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(value)) {
                return "身份证号格式错误！";
            } else if (!city[value.substr(0,2)]) {
                return "地址编码错误！";
            } else {
                // 18位身份证需要验证最后一位校验位
                if (value.length == 18) {
                    value = value.split('');
                    var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
                    var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
                    var sum = 0;
                    for (var i = 0; i < 17; i++) {
                        sum += value[i] * factor[i];
                    }
                    if (parity[sum % 11] != value[17]) {
                        return "校验位错误！";
                    }
                }
            }
            return "";
        }

        // 保存数据
        function doSave() {
            var isValid = true;

            // 验证所有必填字段
            $('.form-input-mobile[required]').each(function() {
                if (!validateField($(this))) {
                    isValid = false;
                }
            });

            if (!isValid) {
                showToast("数据校验不通过，请检查数据！");
                return false;
            }

            // 收集表单数据
            var formData = {};

            // 收集家庭成员数据
            var jtcyData = [];
            $('.jtcy-form').each(function() {
                var itemData = {};
                $(this).find('input, select').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    if (name && value) {
                        var fieldName = name.split('.')[1];
                        itemData[fieldName] = value;
                    }
                });
                if (Object.keys(itemData).length > 2) { // 除了id和xh外还有其他数据
                    jtcyData.push(itemData);
                }
            });

            // 收集教育经历数据
            var grjlData = [];
            $('.grjl-form').each(function() {
                var itemData = {};
                $(this).find('input, select').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    if (name && value) {
                        var fieldName = name.split('.')[1];
                        itemData[fieldName] = value;
                    }
                });
                if (Object.keys(itemData).length > 2) { // 除了id和xh外还有其他数据
                    grjlData.push(itemData);
                }
            });

            formData.xsJtcybs = jtcyData;
            formData.xsGrjlbs = grjlData;
            formData.tokenValue = $("#tokenValue").val();

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/individualApplication/entranceRegistrationForm/index/save",
                cache: false,
                type: "post",
                data: formData,
                dataType: "json",
                success: function(d) {
                    if (d["result"].indexOf("/") != -1) {
                        window.location.href = d["result"];
                    } else {
                        $("#tokenValue").val(d["token"]);
                        if (d["result"] == "success") {
                            showSuccess("保存成功！", function() {
                                returnIndex();
                            });
                        } else {
                            showToast(d["result"]);
                        }
                    }
                },
                error: function(xhr) {
                    showToast("错误代码[" + xhr.readyState + "-" + xhr.status + "]:保存失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 返回上一页
        function returnIndex() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                window.history.back();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                if (typeof urp !== 'undefined' && urp.showLoading) {
                    urp.showLoading();
                } else {
                    $('#loadingState').show();
                }
            } else {
                if (typeof urp !== 'undefined' && urp.hideLoading) {
                    urp.hideLoading();
                } else {
                    $('#loadingState').hide();
                }
            }
        }

        // 显示提示信息
        function showToast(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) {
                    callback();
                }
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 初始化验证规则
        function initValidation() {
            // 添加自定义CSS样式
            $('<style>')
                .prop('type', 'text/css')
                .html(`
                    .modal-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 9999;
                    }

                    .modal-content-mobile {
                        background: var(--bg-primary);
                        border-radius: 8px;
                        max-width: 90%;
                        max-height: 80%;
                        overflow: hidden;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    }

                    .modal-header-mobile {
                        background: var(--primary-color);
                        color: white;
                        padding: var(--padding-md);
                        font-weight: 600;
                    }

                    .modal-body-mobile {
                        padding: var(--padding-md);
                        max-height: 300px;
                        overflow-y: auto;
                    }

                    .modal-footer-mobile {
                        padding: var(--padding-md);
                        border-top: 1px solid var(--divider-color);
                        text-align: center;
                    }
                `)
                .appendTo('head');
        }
    </script>
</body>
</html>
