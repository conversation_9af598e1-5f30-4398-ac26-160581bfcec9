<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>个人管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 个人管理页面样式 */
        .personal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .personal-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            margin: 0 auto var(--margin-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .personal-name {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .personal-info {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .category-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .category-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .category-icon {
            color: var(--primary-color);
            font-size: 18px;
        }
        
        .function-list {
            padding: var(--padding-sm);
        }
        
        .function-item {
            display: flex;
            align-items: center;
            padding: var(--padding-md);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid transparent;
        }
        
        .function-item:hover {
            background: var(--bg-tertiary);
            border-color: var(--primary-color);
            transform: translateX(4px);
        }
        
        .function-item:last-child {
            margin-bottom: 0;
        }
        
        .function-item-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--primary-light);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin-right: var(--margin-md);
        }
        
        .function-item-content {
            flex: 1;
        }
        
        .function-item-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .function-item-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .function-item-arrow {
            color: var(--text-disabled);
            font-size: 16px;
        }
        
        .function-item-badge {
            background: var(--error-color);
            color: white;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: var(--font-size-mini);
            margin-right: var(--margin-sm);
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-actions-title i {
            color: var(--warning-color);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-item {
            text-align: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .action-item:hover {
            background: var(--bg-tertiary);
            transform: translateY(-2px);
        }
        
        .action-item-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--info-light);
            color: var(--info-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto var(--margin-sm);
        }
        
        .action-item-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .pending-tasks {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .pending-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .pending-title i {
            color: var(--error-color);
        }
        
        .task-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--warning-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .task-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .task-item:last-child {
            margin-bottom: 0;
        }
        
        .task-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .task-desc {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        @media (max-width: 480px) {
            .action-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">个人管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 个人信息头部 -->
        <div class="personal-header">
            <div class="personal-avatar">
                <i class="ace-icon fa fa-user"></i>
            </div>
            <div class="personal-name" id="studentName">学生姓名</div>
            <div class="personal-info" id="studentInfo">学号 | 专业 | 年级</div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="quick-actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                快速操作
            </div>
            <div class="action-grid">
                <div class="action-item" onclick="goToFunction('rollInfo');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-id-card"></i>
                    </div>
                    <div class="action-item-title">学籍信息</div>
                </div>
                <div class="action-item" onclick="goToFunction('personalInfoUpdate');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-edit"></i>
                    </div>
                    <div class="action-item-title">信息修改</div>
                </div>
                <div class="action-item" onclick="goToFunction('individualApplication');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-file-text"></i>
                    </div>
                    <div class="action-item-title">个人申请</div>
                </div>
                <div class="action-item" onclick="goToFunction('processManagement');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-tasks"></i>
                    </div>
                    <div class="action-item-title">流程管理</div>
                </div>
            </div>
        </div>
        
        <!-- 待办任务 -->
        <div class="pending-tasks" id="pendingSection" style="display: none;">
            <div class="pending-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                待办任务
            </div>
            <div id="pendingList">
                <!-- 动态加载待办任务 -->
            </div>
        </div>
        
        <!-- 学籍管理 -->
        <div class="category-section">
            <div class="category-header">
                <i class="category-icon ace-icon fa fa-id-card"></i>
                学籍管理
            </div>
            <div class="function-list">
                <div class="function-item" onclick="goToFunction('rollInfo');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-user"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">学籍信息</div>
                        <div class="function-item-desc">查看个人学籍基本信息</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('myRollCard');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-id-card-o"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">学生证管理</div>
                        <div class="function-item-desc">学生证信息维护</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('personalInfoUpdate');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-edit"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">个人信息修改</div>
                        <div class="function-item-desc">修改个人基本信息</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('warning');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-warning"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">学业预警</div>
                        <div class="function-item-desc">查看学业预警信息</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
            </div>
        </div>
        
        <!-- 申请管理 -->
        <div class="category-section">
            <div class="category-header">
                <i class="category-icon ace-icon fa fa-file-text"></i>
                申请管理
            </div>
            <div class="function-list">
                <div class="function-item" onclick="goToFunction('individualApplication');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-file-text-o"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">个人申请</div>
                        <div class="function-item-desc">各类个人申请事务</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('processManagement');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-tasks"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">流程管理</div>
                        <div class="function-item-desc">申请流程跟踪管理</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('studentChange');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-exchange"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">学籍异动</div>
                        <div class="function-item-desc">学籍变更申请</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('transfermajor');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-random"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">转专业申请</div>
                        <div class="function-item-desc">专业转换申请</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
            </div>
        </div>
        
        <!-- 学术管理 -->
        <div class="category-section">
            <div class="category-header">
                <i class="category-icon ace-icon fa fa-graduation-cap"></i>
                学术管理
            </div>
            <div class="function-list">
                <div class="function-item" onclick="goToFunction('paperSubmit');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-file-pdf-o"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">论文提交</div>
                        <div class="function-item-desc">毕业论文提交管理</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('thesisGuidanceRecord');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-comments"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">论文指导记录</div>
                        <div class="function-item-desc">导师指导记录查看</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('projectSelect');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-lightbulb-o"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">项目选择</div>
                        <div class="function-item-desc">毕业设计项目选择</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('excellentProject');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-star"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">优秀项目</div>
                        <div class="function-item-desc">优秀项目申报</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
            </div>
        </div>
        
        <!-- 专业分流 -->
        <div class="category-section">
            <div class="category-header">
                <i class="category-icon ace-icon fa fa-sitemap"></i>
                专业分流
            </div>
            <div class="function-list">
                <div class="function-item" onclick="goToFunction('largeClassDiversion');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-code-fork"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">大类分流</div>
                        <div class="function-item-desc">大类专业分流选择</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('majorsSplit');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-share-alt"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">专业分流</div>
                        <div class="function-item-desc">专业方向分流</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
                
                <div class="function-item" onclick="goToFunction('minorProgramRegistration');">
                    <div class="function-item-icon">
                        <i class="ace-icon fa fa-plus-square"></i>
                    </div>
                    <div class="function-item-content">
                        <div class="function-item-title">辅修专业</div>
                        <div class="function-item-desc">辅修专业注册</div>
                    </div>
                    <i class="function-item-arrow ace-icon fa fa-chevron-right"></i>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let studentInfo = {};
        let pendingTasks = [];

        $(function() {
            initPage();
            loadStudentInfo();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学生信息
        function loadStudentInfo() {
            showLoading(true);

            // 获取学生基本信息
            $.ajax({
                url: "/student/personalManagement/getStudentInfo",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        studentInfo = data.data;
                        updateStudentHeader();
                        loadPendingTasks();
                    } else {
                        // 设置默认信息
                        updateStudentHeader({
                            name: '学生',
                            studentNo: '学号',
                            major: '专业',
                            grade: '年级'
                        });
                    }
                },
                error: function() {
                    console.log('获取学生信息失败');
                    // 设置默认信息
                    updateStudentHeader({
                        name: '学生',
                        studentNo: '学号',
                        major: '专业',
                        grade: '年级'
                    });
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新学生头部信息
        function updateStudentHeader(info = studentInfo) {
            $('#studentName').text(info.name || '学生姓名');
            $('#studentInfo').text((info.studentNo || '学号') + ' | ' +
                                 (info.major || '专业') + ' | ' +
                                 (info.grade || '年级'));
        }

        // 加载待办任务
        function loadPendingTasks() {
            $.ajax({
                url: "/student/personalManagement/getPendingTasks",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success && data.data && data.data.length > 0) {
                        pendingTasks = data.data;
                        renderPendingTasks();
                        $('#pendingSection').show();
                    }
                },
                error: function() {
                    console.log('获取待办任务失败');
                }
            });
        }

        // 渲染待办任务
        function renderPendingTasks() {
            const container = $('#pendingList');
            container.empty();

            pendingTasks.forEach(function(task) {
                const taskHtml = `
                    <div class="task-item" onclick="handleTask('${task.id}', '${task.type}');">
                        <div class="task-title">${task.title}</div>
                        <div class="task-desc">
                            <i class="ace-icon fa fa-clock-o"></i>
                            ${task.deadline || '无截止时间'}
                        </div>
                    </div>
                `;
                container.append(taskHtml);
            });
        }

        // 处理待办任务
        function handleTask(taskId, taskType) {
            // 根据任务类型跳转到相应页面
            switch(taskType) {
                case 'application':
                    goToFunction('individualApplication');
                    break;
                case 'paper':
                    goToFunction('paperSubmit');
                    break;
                case 'process':
                    goToFunction('processManagement');
                    break;
                default:
                    showError('未知任务类型');
            }
        }

        // 跳转到功能页面
        function goToFunction(functionName) {
            let url = '';
            let title = '';

            switch(functionName) {
                case 'rollInfo':
                    url = '/student/personalManagement/rollInfo/jcxxInfo';
                    title = '学籍信息';
                    break;
                case 'myRollCard':
                    url = '/student/personalManagement/myRollCard/index';
                    title = '学生证管理';
                    break;
                case 'personalInfoUpdate':
                    url = '/student/personalManagement/personalInfoUpdate/xjInfo';
                    title = '个人信息修改';
                    break;
                case 'warning':
                    url = '/student/personalManagement/warning/index';
                    title = '学业预警';
                    break;
                case 'individualApplication':
                    url = '/student/personalManagement/individualApplication/index';
                    title = '个人申请';
                    break;
                case 'processManagement':
                    url = '/student/personalManagement/processManagement/index';
                    title = '流程管理';
                    break;
                case 'studentChange':
                    url = '/student/personalManagement/studentChange/view';
                    title = '学籍异动';
                    break;
                case 'transfermajor':
                    url = '/student/personalManagement/transfermajor/index';
                    title = '转专业申请';
                    break;
                case 'paperSubmit':
                    url = '/student/personalManagement/paperSubmit/index';
                    title = '论文提交';
                    break;
                case 'thesisGuidanceRecord':
                    url = '/student/personalManagement/thesisGuidanceRecord/index';
                    title = '论文指导记录';
                    break;
                case 'projectSelect':
                    url = '/student/personalManagement/projectSelect/index';
                    title = '项目选择';
                    break;
                case 'excellentProject':
                    url = '/student/personalManagement/excellentProject/index';
                    title = '优秀项目';
                    break;
                case 'largeClassDiversion':
                    url = '/student/personalManagement/largeClassDiversion/index';
                    title = '大类分流';
                    break;
                case 'majorsSplit':
                    url = '/student/personalManagement/majorsSplit/index';
                    title = '专业分流';
                    break;
                case 'minorProgramRegistration':
                    url = '/student/personalManagement/minorProgramRegistration/index';
                    title = '辅修专业';
                    break;
                default:
                    showError('功能暂未开放');
                    return;
            }

            if (parent && parent.addTab) {
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 刷新数据
        function refreshData() {
            loadStudentInfo();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
