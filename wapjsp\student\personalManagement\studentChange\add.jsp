<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>
        <c:if test="${empty xjydsqb}">新增学籍异动</c:if>
        <c:if test="${!empty xjydsqb}">修改学籍异动</c:if>
    </title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学籍异动申请页面样式 */
        .change-header {
            background: linear-gradient(135deg, var(--warning-color), var(--error-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .change-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .change-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        /* 表单区域样式 */
        .form-section-mobile {
            margin-bottom: var(--margin-lg);
        }
        
        .section-title-mobile {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 2px solid var(--primary-light);
        }
        
        .section-title-mobile i {
            font-size: 18px;
        }
        
        .form-group-mobile {
            margin-bottom: var(--margin-lg);
        }
        
        .form-label-mobile {
            display: block;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .form-label-mobile.required::before {
            content: "* ";
            color: var(--error-color);
            font-weight: bold;
        }
        
        .form-input-mobile {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--transition-base);
            box-sizing: border-box;
        }
        
        .form-input-mobile:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-input-mobile:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .form-textarea-mobile {
            min-height: 80px;
            resize: vertical;
            font-family: inherit;
        }
        
        .form-select-mobile {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            box-sizing: border-box;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-select-mobile:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .file-upload-mobile {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        .file-input-mobile {
            position: absolute;
            left: -9999px;
        }
        
        .file-label-mobile {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--padding-md);
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
            min-height: 50px;
        }
        
        .file-label-mobile:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .file-label-mobile.has-file {
            border-color: var(--success-color);
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .file-info-mobile {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        .custom-fields-mobile {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .course-table-mobile {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--margin-md);
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .course-table-mobile th,
        .course-table-mobile td {
            border: 1px solid var(--border-primary);
            padding: 8px;
            text-align: left;
        }
        
        .course-table-mobile th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .course-table-mobile td {
            color: var(--text-secondary);
        }
        
        .checkbox-mobile {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin: var(--margin-md) 0;
            padding: var(--padding-md);
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            color: var(--warning-dark);
        }
        
        .checkbox-mobile input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--warning-color);
        }
        
        .checkbox-mobile label {
            font-size: var(--font-size-small);
            font-weight: 500;
            margin: 0;
            cursor: pointer;
            flex: 1;
        }
        
        .form-actions-mobile {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
            z-index: 1000;
        }
        
        .form-error-mobile {
            color: var(--error-color);
            font-size: var(--font-size-small);
            margin-top: 4px;
            display: none;
        }
        
        .template-download {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .template-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--info-dark);
        }
        
        .template-title i {
            color: var(--info-color);
        }
        
        .template-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm) 0;
            border-bottom: 1px solid var(--info-light);
        }
        
        .template-item:last-child {
            border-bottom: none;
        }
        
        .template-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .template-download-btn {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .template-download-btn:hover {
            background: var(--info-dark);
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .form-actions-mobile {
                flex-direction: column;
            }
            
            .course-table-mobile {
                font-size: var(--font-size-mini);
            }
            
            .course-table-mobile th,
            .course-table-mobile td {
                padding: 6px;
            }
        }
        
        /* 为底部固定按钮留出空间 */
        .page-mobile {
            padding-bottom: 80px;
        }
        
        /* 表单验证错误样式 */
        .form-input-mobile.error,
        .form-select-mobile.error,
        .form-textarea-mobile.error {
            border-color: var(--error-color) !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="backWard();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">
                <c:if test="${empty xjydsqb}">新增学籍异动</c:if>
                <c:if test="${!empty xjydsqb}">修改学籍异动</c:if>
            </div>
            <div class="navbar-action" onclick="doSave('1');" id="submitBtn" style="display: none;">
                <i class="ace-icon fa fa-check"></i>
            </div>
        </nav>
        
        <!-- 异动申请头部 -->
        <div class="change-header">
            <div class="change-title">学籍异动申请</div>
            <div class="change-desc">请认真填写申请信息</div>
        </div>
        
        <!-- 主要内容区域 -->
        <form name="updateRollInfo" id="updateRollInfo" action="" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="tokenValue" value="${token_in_session}"/>
            <input type="hidden" name="sqztdm" id="sqztdm" value=""/>
            <input type="hidden" name="sqbh" id="sqbh" value="${xjydsqb.sqbh}"/>
            <input type="hidden" id="ydyydmtemp" value="${xjydsqb.codeYdyyb.ydyydm}"/>
            <input type="hidden" id="jhrtysfjid" name="jhrtysfjid" value="${xjydsqb.jhrtysfjid}"/>
            <input type="hidden" id="jsh" name="jsh"/>
            <input type="hidden" id="ids" name="ids"/>
            
            <!-- 隐藏的自定义字段 -->
            <input type="hidden" id="c1_custom" name="c1_custom" value="${xjydsqb.c1}"/>
            <input type="hidden" id="c2_custom" name="c2_custom" value="${xjydsqb.c2}"/>
            <input type="hidden" id="c3_custom" name="c3_custom" value="${xjydsqb.c3}"/>
            <input type="hidden" id="c4_custom" name="c4_custom" value="${xjydsqb.c4}"/>
            <input type="hidden" id="c5_custom" name="c5_custom" value="${xjydsqb.c5}"/>
            <input type="hidden" id="c6_custom" name="c6_custom" value="${xjydsqb.c6}"/>
            <input type="hidden" id="c7_custom" name="c7_custom" value="${xjydsqb.c7}"/>
            <input type="hidden" id="c8_custom" name="c8_custom" value="${xjydsqb.c8}"/>
            <input type="hidden" id="c9_custom" name="c9_custom" value="${xjydsqb.c9}"/>
            <input type="hidden" id="c10_custom" name="c10_custom" value="${xjydsqb.c10}"/>
            <input type="hidden" id="customFields" name="customFields" value=""/>
            <input type="hidden" id="xjztdmtemp" name="xjztdm" value="${xsXjbView.codeXjztb.xjztdm}"/>
            
            <!-- 基本信息 -->
            <div class="card-mobile">
                <div class="section-title-mobile">
                    <i class="ace-icon fa fa-info-circle"></i>
                    <span>基本信息</span>
                </div>
                
                <div class="info-grid-mobile">
                    <div class="info-item-mobile">
                        <span class="label">学号：</span>
                        <span class="value">${xsXjbView.xh}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">姓名：</span>
                        <span class="value">${xsXjbView.xm}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">性别：</span>
                        <span class="value">${xsXjbView.xb}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">学院：</span>
                        <span class="value">${xsXjbView.department.departmentName}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">专业：</span>
                        <span class="value">${xsXjbView.subject.subjectName}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">专业方向：</span>
                        <span class="value">${xsXjbView.codeZyfxb.zyfxm}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">年级：</span>
                        <span class="value">${xsXjbView.codeYear.yearName}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">班级：</span>
                        <span class="value">${xsXjbView.gradeAndClass.className}</span>
                    </div>
                    <div class="info-item-mobile">
                        <span class="label">学生类别：</span>
                        <span class="value">${xsXjbView.codeXslb.xslbsm}</span>
                    </div>
                </div>
            </div>
            
            <!-- 申请模板下载 -->
            <div class="template-download" id="templateDownload" style="display: none;">
                <div class="template-title">
                    <i class="ace-icon fa fa-paperclip"></i>
                    申请模板下载
                </div>
                <div id="templateList">
                    <!-- 模板列表将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 申请信息 -->
            <div class="card-mobile">
                <div class="section-title-mobile">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-section-mobile">
                    <div class="form-group-mobile">
                        <label class="form-label-mobile required">异动类别</label>
                        <select id="yddm" name="codeYdlb.yddm" class="form-select-mobile"
                                onchange="querycustomFields(this.value)" required>
                            <option value="">--请选择--</option>
                            <c:forEach items="${codeYdlList}" var="list">
                                <option value="${list.yddm}"
                                        <c:if test="${list.yddm == xjydsqb.codeYdlb.yddm}">selected</c:if>>
                                    ${list.ydlb}
                                </option>
                            </c:forEach>
                        </select>
                        <div class="form-error-mobile"></div>
                    </div>

                    <div class="form-group-mobile">
                        <label class="form-label-mobile required">异动原因</label>
                        <select id="ydyydm" name="codeYdyyb.ydyydm" class="form-select-mobile"
                                onchange="queryCustomFjsmOfYdyyd(this.value)" required>
                            <option value="">--请选择--</option>
                            <c:forEach items="${codeYdyyList}" var="list">
                                <option value="${list.ydyydm}"
                                        <c:if test="${list.ydyydm == xjydsqb.codeYdyyb.ydyydm}">selected</c:if>>
                                    ${list.ydyysm}
                                </option>
                            </c:forEach>
                        </select>
                        <div class="form-error-mobile"></div>
                    </div>

                    <!-- 自定义字段容器 -->
                    <div id="customs" class="custom-fields-mobile" style="display: none;">
                        <!-- 自定义字段将通过JavaScript动态生成 -->
                    </div>

                    <!-- 特殊自定义字段容器 -->
                    <div id="specialcustoms" class="custom-fields-mobile" style="display: none;">
                        <!-- 特殊自定义字段将通过JavaScript动态生成 -->
                    </div>

                    <div class="form-group-mobile">
                        <label class="form-label-mobile required">异动原因说明</label>
                        <div id="ydyytssmid" class="form-error-mobile" style="display: block; margin-bottom: 8px;"></div>
                        <textarea id="ydyy" name="ydyy" class="form-input-mobile form-textarea-mobile"
                                  placeholder="请详细说明异动原因" maxlength="200" required>${xjydsqb.ydyy}</textarea>
                        <div class="form-error-mobile"></div>
                    </div>

                    <!-- 附件上传 -->
                    <div class="form-group-mobile" id="fileUploadGroup" <c:if test="${'100027' == schoolId}">style="display:none"</c:if>>
                        <label class="form-label-mobile" id="fjrequired">附件</label>
                        <div id="unifyfjsm" class="form-error-mobile" style="display: block; margin-bottom: 8px;"></div>
                        <div id="ydyyfjsm" class="form-error-mobile" style="display: block; margin-bottom: 8px;"></div>

                        <div class="file-upload-mobile">
                            <input type="file" name="file" id="fjnr" class="file-input-mobile"
                                   accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg"/>
                            <label for="fjnr" class="file-label-mobile" id="fileLabel">
                                <i class="ace-icon fa fa-upload"></i>
                                <span>点击选择文件</span>
                            </label>
                            <input type="hidden" id="fjmc" name="fjmc" value="${sysSqfjb.fjmc}"/>
                        </div>

                        <div class="file-info-mobile">
                            支持格式：PDF、DOC、DOCX、XLS、XLSX、PNG、JPG
                        </div>

                        <c:if test="${not empty sysSqfjb.fjid}">
                            <div style="margin-top: 8px;">
                                <a href="javascript:void(0);" onclick="doDownload('${sysSqfjb.fjid}');"
                                   class="template-download-btn">
                                    <i class="ace-icon fa fa-download"></i>
                                    ${sysSqfjb.fjmc}
                                </a>
                            </div>
                        </c:if>

                        <div id="xsscfjsm" class="form-error-mobile" style="display: block; margin-top: 8px;"></div>
                    </div>

                    <!-- 家长同意书上传 -->
                    <c:choose>
                        <c:when test="${'100027' == schoolId}">
                            <c:set var="jhrtysfiletitle" value="本科生学籍异动申请表" />
                        </c:when>
                        <c:otherwise>
                            <c:set var="jhrtysfiletitle" value="家长(监护人)知情同意书" />
                        </c:otherwise>
                    </c:choose>

                    <div class="form-group-mobile" id="sfcsxjydtystemp" style="display:none">
                        <label class="form-label-mobile required">${jhrtysfiletitle}</label>
                        <div id="jhrtysfjsm" class="form-error-mobile" style="display: block; margin-bottom: 8px;"></div>

                        <div class="file-upload-mobile">
                            <input type="file" id="sfcsxjydtysfjnr" name="tysfile" class="file-input-mobile"
                                   accept=".pdf,.doc,.docx,.png,.jpg"/>
                            <label for="sfcsxjydtysfjnr" class="file-label-mobile" id="tysFileLabel">
                                <i class="ace-icon fa fa-upload"></i>
                                <span>点击选择${jhrtysfiletitle}</span>
                            </label>
                            <input type="hidden" id="sfcsxjydtysfjmc" name="tysmc" value="${syssqfjtys.fjmc}"/>
                        </div>

                        <div class="file-info-mobile">
                            支持格式：PDF、DOC、DOCX、PNG、JPG
                        </div>

                        <c:if test="${not empty syssqfjtys.fjid}">
                            <div style="margin-top: 8px;">
                                <a href="javascript:void(0);" onclick="doDownload('${syssqfjtys.fjid}');"
                                   class="template-download-btn">
                                    <i class="ace-icon fa fa-download"></i>
                                    ${syssqfjtys.fjmc}
                                </a>
                            </div>
                        </c:if>
                    </div>

                    <div class="form-group-mobile">
                        <label class="form-label-mobile">备注</label>
                        <textarea id="bz" name="bz" class="form-input-mobile form-textarea-mobile"
                                  placeholder="其他需要说明的信息" maxlength="500">${xjydsqb.bz}</textarea>
                        <div class="form-error-mobile"></div>
                    </div>
                </div>
            </div>

            <!-- 保留成绩课程表 -->
            <c:if test="${'100027' != schoolId}">
                <div class="card-mobile" id="courseCard" style="display: none;">
                    <div class="section-title-mobile">
                        <i class="ace-icon fa fa-book"></i>
                        <span>保留成绩课程</span>
                        <div style="margin-left: auto; display: flex; gap: 8px;">
                            <button type="button" class="btn-mobile btn-success btn-sm" onclick="addCurriculum();">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>选择课程</span>
                            </button>
                            <button type="button" class="btn-mobile btn-danger btn-sm" onclick="deleteCurriculum('batch','');">
                                <i class="ace-icon fa fa-trash"></i>
                                <span>删除</span>
                            </button>
                        </div>
                    </div>

                    <div class="course-table-container">
                        <table class="course-table-mobile" id="blcjtable">
                            <thead>
                                <tr>
                                    <th width="40px">
                                        <input type="checkbox" id="batch_id" class="checkbox-mobile"/>
                                    </th>
                                    <th width="60px">操作</th>
                                    <th>学年学期</th>
                                    <th>课程号</th>
                                    <th>课程名</th>
                                    <th>课序号</th>
                                    <th>学分</th>
                                </tr>
                            </thead>
                            <tbody id="blcjtbody">
                                <c:if test="${!empty xsXjydblkccjbList}">
                                    <c:forEach var="xsXjydblkccjb" items="${xsXjydblkccjbList}">
                                        <tr>
                                            <td>
                                                <input type="checkbox"
                                                       value="${xsXjydblkccjb.zxjxjhh}_${xsXjydblkccjb.kch}_${xsXjydblkccjb.kxh}"
                                                       id="${xsXjydblkccjb.zxjxjhh}_${xsXjydblkccjb.kch}_${xsXjydblkccjb.kxh}"
                                                       name="kcId" class="checkbox-mobile"/>
                                            </td>
                                            <td>
                                                <a href="javascript:void(0);" onclick="deleteCurriculum('one',this);"
                                                   style="color: var(--error-color);">
                                                    <i class="ace-icon fa fa-trash"></i>
                                                </a>
                                            </td>
                                            <td>
                                                <cache:get var="view" region="jh_zxjxjhb_view" key="${xsXjydblkccjb.zxjxjhh}"
                                                          keyName="zxjxjhh" targetprop="zxjxjhm" out="true"/>
                                            </td>
                                            <td>${xsXjydblkccjb.kch}</td>
                                            <td>
                                                <cache:get var="kcb" region="code_kcb" key="${xsXjydblkccjb.kch}"
                                                          keyName="kch" targetprop="kcm" out="true"/>
                                            </td>
                                            <td>${xsXjydblkccjb.kxh}</td>
                                            <td>
                                                <cache:get var="kcb" region="code_kcb" key="${xsXjydblkccjb.kch}"
                                                          keyName="kch" targetprop="xf" out="true"/>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </c:if>
                            </tbody>
                        </table>
                    </div>
                </div>
            </c:if>

            <!-- 确认声明 -->
            <div class="checkbox-mobile">
                <input id="showsave" type="checkbox" onclick="showsavebutton()"/>
                <label for="showsave">
                    本人保证上述所填信息及提供的证明材料真实有效。如有不实，本人愿意承担一切责任。
                </label>
            </div>

            <!-- 固定底部操作按钮 -->
            <div class="form-actions-mobile">
                <button id="zcsavebutton" type="button" class="btn-mobile btn-secondary flex-1" onclick="doSave('0');">
                    <i class="ace-icon fa fa-save"></i>
                    <span>暂存</span>
                </button>
                <button id="savebutton" type="button" class="btn-mobile btn-primary flex-1" onclick="doSave('1');" disabled>
                    <i class="ace-icon fa fa-check"></i>
                    <span>提交</span>
                </button>
                <button type="button" class="btn-mobile btn-secondary flex-1" onclick="backWard();">
                    <i class="ace-icon fa fa-arrow-left"></i>
                    <span>返回</span>
                </button>
            </div>
        </form>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>处理中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        var kxggrxx = "";
        var sfbxscfj = "0";
        var applytype = "${applytype}";
        var schoolId = "${schoolId}";
        var totalmessage = "";

        $(function() {
            initPage();
            initFileUpload();
            initFormValidation();

            // 如果是编辑模式，触发相关事件
            if ("${xjydsqb.sqbh}" != "") {
                $("#yddm").trigger("change");
                $("#ydyydm").trigger("change");
            } else {
                $("#courseCard").hide();
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化文件上传
        function initFileUpload() {
            // 主附件上传
            $('#fjnr').on('change', function() {
                handleFileSelect(this, 'fileLabel', '文件格式为doc，docx，xls，xlsx，pdf，png，jpg');
            });

            // 家长同意书上传
            $('#sfcsxjydtysfjnr').on('change', function() {
                handleFileSelect(this, 'tysFileLabel', '文件格式为doc，docx，pdf，png，jpg');
            });

            // 设置已有文件显示
            var fjmc = $("#fjmc").val();
            if (fjmc != "") {
                updateFileLabel('fileLabel', fjmc, true);
            }

            var sfcsxjydtysfjmc = $("#sfcsxjydtysfjmc").val();
            if (sfcsxjydtysfjmc != "") {
                updateFileLabel('tysFileLabel', sfcsxjydtysfjmc, true);
            }
        }

        // 处理文件选择
        function handleFileSelect(input, labelId, allowedFormats) {
            var file = input.files[0];
            if (file) {
                var fileName = file.name;
                var fileExt = fileName.split('.').pop().toLowerCase();
                var allowedExts = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'png', 'jpg'];

                if (allowedExts.indexOf(fileExt) === -1) {
                    showToast('文件格式不正确，请重新上传！');
                    input.value = '';
                    updateFileLabel(labelId, '点击选择文件', false);
                    return;
                }

                // 检查文件大小（10MB限制）
                if (file.size > 10 * 1024 * 1024) {
                    showToast('附件大小超过限制的10MB，请重新选择。');
                    input.value = '';
                    updateFileLabel(labelId, '点击选择文件', false);
                    return;
                }

                updateFileLabel(labelId, fileName, true);
            } else {
                updateFileLabel(labelId, '点击选择文件', false);
            }
        }

        // 更新文件标签显示
        function updateFileLabel(labelId, text, hasFile) {
            var label = $('#' + labelId);
            var icon = hasFile ? 'fa-file' : 'fa-upload';

            label.html(`
                <i class="ace-icon fa ${icon}"></i>
                <span>${text}</span>
            `);

            if (hasFile) {
                label.addClass('has-file');
            } else {
                label.removeClass('has-file');
            }
        }

        // 查询自定义字段
        function querycustomFields(yddm) {
            totalmessage = "";
            if (yddm == null || yddm == "") {
                $("#ydyydm").empty();
                $("#ydyydm").append("<option value=''>--请选择--</option>");
                $("#specialcustoms").html("").hide();
                $("#customs").html("").hide();
                $("#templateDownload").hide();
                $("#courseCard").hide();
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/studentChange/querycustomFields",
                cache: false,
                type: "post",
                data: "yddm=" + yddm,
                dataType: "json",
                success: function(d) {
                    // 更新异动原因选项
                    $("#ydyydm").empty();
                    $("#ydyydm").append("<option value=''>--请选择--</option>");

                    if (d.data && d.data.codeYdyyList) {
                        $.each(d.data.codeYdyyList, function(i, item) {
                            var selected = item.ydyydm == "${xjydsqb.codeYdyyb.ydyydm}" ? "selected" : "";
                            $("#ydyydm").append(`<option value="${item.ydyydm}" ${selected}>${item.ydyysm}</option>`);
                        });
                    }

                    // 处理自定义字段
                    if (d.data && d.data.customFields) {
                        renderCustomFields(d.data.customFields);
                    }

                    // 处理模板下载
                    if (d.data && d.data.templates) {
                        renderTemplates(d.data.templates);
                    }

                    // 处理保留成绩课程表显示
                    if (d.data && d.data.showCourseTable) {
                        $("#courseCard").show();
                    } else {
                        $("#courseCard").hide();
                    }
                },
                error: function() {
                    showToast("查询异动类别信息失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 查询异动原因的附件说明
        function queryCustomFjsmOfYdyyd(ydyydm) {
            if (!ydyydm) {
                $("#ydyytssmid").text("").hide();
                $("#ydyyfjsm").text("").hide();
                $("#jhrtysfjsm").text("").hide();
                $("#sfcsxjydtystemp").hide();
                return;
            }

            $.ajax({
                url: "/student/personalManagement/studentChange/queryCustomFjsmOfYdyyd",
                cache: false,
                type: "post",
                data: "ydyydm=" + ydyydm,
                dataType: "json",
                success: function(d) {
                    if (d.data) {
                        // 显示异动原因提示说明
                        if (d.data.ydyytssmid) {
                            $("#ydyytssmid").text(d.data.ydyytssmid).show();
                        } else {
                            $("#ydyytssmid").hide();
                        }

                        // 显示附件说明
                        if (d.data.ydyyfjsm) {
                            $("#ydyyfjsm").text(d.data.ydyyfjsm).show();
                        } else {
                            $("#ydyyfjsm").hide();
                        }

                        // 显示家长同意书说明
                        if (d.data.jhrtysfjsm) {
                            $("#jhrtysfjsm").text(d.data.jhrtysfjsm).show();
                            $("#sfcsxjydtystemp").show();
                        } else {
                            $("#jhrtysfjsm").hide();
                            $("#sfcsxjydtystemp").hide();
                        }

                        // 处理附件必填标识
                        if (d.data.fjrequired) {
                            $("#fjrequired").addClass('required');
                        } else {
                            $("#fjrequired").removeClass('required');
                        }
                    }
                },
                error: function() {
                    showToast("查询异动原因信息失败！");
                }
            });
        }

        // 渲染自定义字段
        function renderCustomFields(customFields) {
            var html = '';
            if (customFields && customFields.length > 0) {
                customFields.forEach(function(field) {
                    html += `
                        <div class="form-group-mobile">
                            <label class="form-label-mobile ${field.required ? 'required' : ''}">${field.label}</label>
                    `;

                    if (field.type === 'select') {
                        html += `<select id="${field.id}" name="${field.name}" class="form-select-mobile" ${field.required ? 'required' : ''}>`;
                        html += `<option value="">--请选择--</option>`;
                        if (field.options) {
                            field.options.forEach(function(option) {
                                var selected = option.value == field.value ? 'selected' : '';
                                html += `<option value="${option.value}" ${selected}>${option.text}</option>`;
                            });
                        }
                        html += `</select>`;
                    } else if (field.type === 'textarea') {
                        html += `<textarea id="${field.id}" name="${field.name}" class="form-input-mobile form-textarea-mobile"
                                           placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}>${field.value || ''}</textarea>`;
                    } else {
                        html += `<input type="${field.type || 'text'}" id="${field.id}" name="${field.name}"
                                       class="form-input-mobile" placeholder="${field.placeholder || ''}"
                                       value="${field.value || ''}" ${field.required ? 'required' : ''}/>`;
                    }

                    html += `<div class="form-error-mobile"></div></div>`;
                });

                $("#customs").html(html).show();
            } else {
                $("#customs").hide();
            }
        }

        // 渲染模板下载
        function renderTemplates(templates) {
            var html = '';
            if (templates && templates.length > 0) {
                templates.forEach(function(template) {
                    html += `
                        <div class="template-item">
                            <div class="template-name">${template.name}</div>
                            <button type="button" class="template-download-btn" onclick="downloadTemplate('${template.id}');">
                                <i class="ace-icon fa fa-download"></i>
                                下载
                            </button>
                        </div>
                    `;
                });

                $("#templateList").html(html);
                $("#templateDownload").show();
            } else {
                $("#templateDownload").hide();
            }
        }

        // 下载模板
        function downloadTemplate(templateId) {
            window.open('/student/personalManagement/studentChange/downloadTemplate?id=' + templateId);
        }

        // 下载附件
        function doDownload(fjid) {
            window.open('/student/personalManagement/studentChange/downloadFile?fjid=' + fjid);
        }

        // 显示保存按钮
        function showsavebutton() {
            var checked = $("#showsave").is(':checked');
            $("#savebutton").prop('disabled', !checked);
            $("#submitBtn").toggle(checked);
        }

        // 初始化表单验证
        function initFormValidation() {
            // 实时验证
            $(document).on('blur', '.form-input-mobile[required], .form-select-mobile[required], .form-textarea-mobile[required]', function() {
                validateField($(this));
            });

            // 字符长度验证
            $(document).on('input', 'textarea[maxlength]', function() {
                var maxLength = parseInt($(this).attr('maxlength'));
                var currentLength = getStringLength($(this).val());
                var remaining = maxLength - currentLength;

                var errorDiv = $(this).siblings('.form-error-mobile');
                if (remaining < 0) {
                    errorDiv.text(`超出${Math.abs(remaining)}个字符，一个汉字为两个字符`).show();
                    $(this).addClass('error');
                } else {
                    errorDiv.hide();
                    $(this).removeClass('error');
                }
            });
        }

        // 验证单个字段
        function validateField(field) {
            var value = field.val().trim();
            var errorDiv = field.siblings('.form-error-mobile');
            var isValid = true;
            var errorMsg = '';

            // 必填验证
            if (field.prop('required') && !value) {
                isValid = false;
                errorMsg = '此字段为必填项';
            } else if (value) {
                // 字符长度验证
                var maxLength = field.attr('maxlength');
                if (maxLength && getStringLength(value) > parseInt(maxLength)) {
                    isValid = false;
                    errorMsg = '最大长度不能超过' + maxLength + '个字符，一个汉字为两个字符';
                }
            }

            if (isValid) {
                errorDiv.hide();
                field.removeClass('error');
            } else {
                errorDiv.text(errorMsg).show();
                field.addClass('error');
            }

            return isValid;
        }

        // 计算字符串长度（中文算2个字符）
        function getStringLength(str) {
            var length = 0;
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) > 19967) {
                    length += 2;
                } else {
                    length++;
                }
            }
            return length;
        }

        // 保存数据
        function doSave(sqztdm) {
            // 验证必填字段
            var isValid = true;
            $('.form-input-mobile[required], .form-select-mobile[required], .form-textarea-mobile[required]').each(function() {
                if (!validateField($(this))) {
                    isValid = false;
                }
            });

            if (!isValid) {
                showToast("请完善必填信息！");
                return false;
            }

            // 检查确认声明
            if (sqztdm == '1' && !$("#showsave").is(':checked')) {
                showToast("请先确认声明！");
                return false;
            }

            $("#sqztdm").val(sqztdm);

            // 收集自定义字段数据
            var customFieldsData = {};
            $("#customs input, #customs select, #customs textarea").each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();
                if (name && value) {
                    customFieldsData[name] = value;
                }
            });
            $("#customFields").val(JSON.stringify(customFieldsData));

            showLoading(true);

            var formData = new FormData(document.getElementById('updateRollInfo'));

            $.ajax({
                url: "/student/personalManagement/studentChange/save",
                cache: false,
                type: "post",
                data: formData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function(d) {
                    if (d["result"].indexOf("/") != -1) {
                        window.location.href = d["result"];
                    } else {
                        if (d["result"] == "success") {
                            var message = sqztdm == '1' ? "提交成功！" : "暂存成功！";
                            showSuccess(message, function() {
                                backWard();
                            });
                        } else {
                            showToast(d["result"]);
                        }
                    }
                },
                error: function(xhr) {
                    showToast("错误代码[" + xhr.readyState + "-" + xhr.status + "]:保存失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 添加课程
        function addCurriculum() {
            // 这里应该打开课程选择页面
            if (parent && parent.addTab) {
                parent.addTab('选择保留成绩课程', '/student/personalManagement/studentChange/addCurriculum');
            } else {
                window.location.href = '/student/personalManagement/studentChange/addCurriculum';
            }
        }

        // 删除课程
        function deleteCurriculum(type, element) {
            if (type === 'batch') {
                var checkedBoxes = $('input[name="kcId"]:checked');
                if (checkedBoxes.length === 0) {
                    showToast('请选择要删除的课程！');
                    return;
                }

                if (confirm('确定要删除选中的课程吗？')) {
                    checkedBoxes.each(function() {
                        $(this).closest('tr').remove();
                    });
                }
            } else if (type === 'one') {
                if (confirm('确定要删除这门课程吗？')) {
                    $(element).closest('tr').remove();
                }
            }
        }

        // 返回上一页
        function backWard() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                window.history.back();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                if (typeof urp !== 'undefined' && urp.showLoading) {
                    urp.showLoading();
                } else {
                    $('#loadingState').show();
                }
            } else {
                if (typeof urp !== 'undefined' && urp.hideLoading) {
                    urp.hideLoading();
                } else {
                    $('#loadingState').hide();
                }
            }
        }

        // 显示提示信息
        function showToast(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) {
                    callback();
                }
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 全选/取消全选课程
        $('#batch_id').on('change', function() {
            var checked = $(this).is(':checked');
            $('input[name="kcId"]').prop('checked', checked);
        });

        // 单个课程选择时更新全选状态
        $(document).on('change', 'input[name="kcId"]', function() {
            var total = $('input[name="kcId"]').length;
            var checked = $('input[name="kcId"]:checked').length;
            $('#batch_id').prop('checked', total > 0 && total === checked);
        });
    </script>
</body>
</html>
