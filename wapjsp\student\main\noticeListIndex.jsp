<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>通知公告</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 移动端优化样式 */
        .page-mobile {
            background: var(--bg-secondary);
            min-height: 100vh;
        }
        
        .notice-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
        }
        
        .notice-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
            line-height: var(--line-height-base);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .notice-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .notice-time {
            display: flex;
            align-items: center;
        }
        
        .notice-time i {
            margin-right: 4px;
        }
        
        .notice-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            background: var(--primary-color);
            color: white;
        }
        
        .loading-container {
            text-align: center;
            padding: var(--padding-lg);
            color: var(--text-secondary);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xxl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        /* 下拉刷新样式 */
        .pull-refresh {
            text-align: center;
            padding: var(--padding-md);
            color: var(--text-secondary);
            font-size: var(--font-size-small);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-times"></i>
            </div>
            <div class="navbar-title">通知公告</div>
            <div class="navbar-action" onclick="refreshNotices();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 下拉刷新提示 -->
        <div class="pull-refresh" id="pullRefresh" style="display: none;">
            <i class="ace-icon fa fa-refresh fa-spin"></i>
            <span>正在刷新...</span>
        </div>
        
        <!-- 内容区域 -->
        <div class="container-mobile">
            <!-- 通知列表 -->
            <div id="noticeList">
                <!-- 通知项将通过JavaScript动态填充 -->
            </div>
            
            <!-- 加载更多 -->
            <div class="loading-container" id="loadingMore" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-bell-slash-o"></i>
                <div>暂无通知公告</div>
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div id="urppagebar" style="display: none;"></div>
    </div>

    <!-- 通知详情模态框 -->
    <div class="modal fade" id="noticeDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                    <h4 class="modal-title" id="noticeDetailTitle"></h4>
                </div>
                <div class="modal-body">
                    <div class="notice-detail-meta" id="noticeDetailMeta"></div>
                    <div class="notice-detail-content" id="noticeDetailContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let isLoading = false;
        let hasMore = true;
        let notices = [];

        $(function() {
            initPage();
            loadNotices(1, true);
            initPullRefresh();
        });

        // 初始化页面
        function initPage() {
            // 设置页面高度
            adjustPageHeight();
            
            // 绑定滚动事件
            $(window).scroll(function() {
                if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                    if (!isLoading && hasMore) {
                        loadMoreNotices();
                    }
                }
            });
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 加载通知列表
        function loadNotices(page, reset = false) {
            if (isLoading) return;
            
            isLoading = true;
            showLoading(true);

            $.ajax({
                url: "/main/noticeList/search",
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=10",
                dataType: "json",
                success: function(d) {
                    const data = d[0];
                    const records = data.records || [];
                    const totalCount = data.pageContext.totalCount;
                    
                    if (reset) {
                        notices = records;
                        currentPage = 1;
                    } else {
                        notices = notices.concat(records);
                    }
                    
                    hasMore = notices.length < totalCount;
                    renderNotices(reset);
                    
                    // 更新分页信息
                    urp.pagebar("urppagebar", "10_sl", page, totalCount, getTzlbList);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    isLoading = false;
                    showLoading(false);
                }
            });
        }

        // 兼容原有的分页函数
        function getTzlbList(page, pageSize, conditionChanged) {
            loadNotices(page, conditionChanged);
        }

        // 渲染通知列表
        function renderNotices(reset = false) {
            const container = $('#noticeList');
            
            if (reset) {
                container.empty();
            }
            
            if (notices.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            notices.forEach(function(notice, index) {
                if (reset || index >= notices.length - 10) {
                    const noticeHtml = createNoticeItem(notice, index);
                    if (reset) {
                        container.append(noticeHtml);
                    } else {
                        container.append(noticeHtml);
                    }
                }
            });
        }

        // 创建通知项HTML
        function createNoticeItem(notice, index) {
            const title = notice[0] || '无标题';
            const publishTime = notice[2] || '';
            const publisher = notice[1] || '';
            const noticeId = notice[5] + '-' + notice[6];
            
            return `
                <div class="notice-item" onclick="showNoticeDetail('${noticeId}')">
                    <div class="notice-title">${title}</div>
                    <div class="notice-meta">
                        <div class="notice-time">
                            <i class="ace-icon fa fa-clock-o"></i>
                            <span>${publishTime}</span>
                        </div>
                        <div class="notice-status">查看</div>
                    </div>
                </div>
            `;
        }

        // 显示通知详情
        function showNoticeDetail(noticeId) {
            $.ajax({
                url: "/main/showNoticeDetail",
                type: "post",
                data: { msgId: noticeId },
                dataType: "json",
                success: function(d) {
                    const data = d.data;
                    const content = d.tznr;
                    
                    if (data === "error") {
                        showError("通知不存在或已被删除");
                        return;
                    }
                    
                    $('#noticeDetailTitle').text(data[1]);
                    $('#noticeDetailMeta').html(`
                        <div style="color: var(--text-secondary); font-size: var(--font-size-small); margin-bottom: var(--margin-md);">
                            发布时间：${data[4]}
                        </div>
                    `);
                    $('#noticeDetailContent').html(content);
                    $('#noticeDetailModal').modal('show');
                },
                error: function() {
                    showError("加载详情失败");
                }
            });
        }

        // 加载更多通知
        function loadMoreNotices() {
            if (!hasMore || isLoading) return;
            
            currentPage++;
            loadNotices(currentPage, false);
        }

        // 刷新通知
        function refreshNotices() {
            currentPage = 1;
            hasMore = true;
            loadNotices(1, true);
        }

        // 初始化下拉刷新
        function initPullRefresh() {
            let startY = 0;
            let pullDistance = 0;
            const threshold = 60;
            
            $(document).on('touchstart', function(e) {
                if ($(window).scrollTop() === 0) {
                    startY = e.originalEvent.touches[0].pageY;
                }
            });
            
            $(document).on('touchmove', function(e) {
                if ($(window).scrollTop() === 0 && startY > 0) {
                    pullDistance = e.originalEvent.touches[0].pageY - startY;
                    if (pullDistance > 0) {
                        e.preventDefault();
                        if (pullDistance > threshold) {
                            $('#pullRefresh').show();
                        }
                    }
                }
            });
            
            $(document).on('touchend', function() {
                if (pullDistance > threshold) {
                    refreshNotices();
                }
                $('#pullRefresh').hide();
                startY = 0;
                pullDistance = 0;
            });
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingMore').show();
            } else {
                $('#loadingMore').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
