<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实验成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实验成绩页面样式 */
        .experiment-header {
            background: linear-gradient(135deg, var(--warning-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .experiment-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .experiment-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--warning-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .form-row {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            font-size: var(--font-size-base);
            background: var(--bg-tertiary);
        }
        
        .form-select {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            font-size: var(--font-size-base);
            background: var(--bg-tertiary);
        }
        
        .search-button {
            background: var(--warning-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            margin-top: var(--margin-sm);
        }
        
        .search-button:hover {
            background: var(--warning-dark);
        }
        
        .experiment-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-header i {
            color: var(--warning-color);
        }
        
        .experiment-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .experiment-item:last-child {
            border-bottom: none;
        }
        
        .experiment-item:hover {
            background: var(--bg-tertiary);
        }
        
        .experiment-header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-sm);
        }
        
        .experiment-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .experiment-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .experiment-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .experiment-score {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            height: 30px;
            border-radius: 15px;
            font-size: var(--font-size-base);
            font-weight: 500;
            flex-shrink: 0;
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .experiment-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .meta-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .meta-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .view-button {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 4px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            margin-top: var(--margin-sm);
        }
        
        .view-button:hover {
            background: var(--info-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            padding: var(--padding-md);
            text-align: center;
        }
        
        .load-more-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md) var(--padding-lg);
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .load-more-button:hover {
            background: var(--primary-dark);
        }
        
        .load-more-button:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .experiment-header-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .experiment-meta {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .form-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实验成绩</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 实验成绩头部 -->
        <div class="experiment-header">
            <div class="experiment-title">实验成绩查询</div>
            <div class="experiment-desc">查看实验课程成绩和实验项目详情</div>
        </div>
        
        <!-- 查询条件 -->
        <div class="search-container">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <div class="search-form">
                <div class="form-row">
                    <div class="form-label">学年学期</div>
                    <select class="form-select" id="zxjxjhh">
                        <cache:query var="codeZxjxjhxnxqList" region="code_zxjxjhxnxq" orderby="zxjxjhh desc" />
                        <c:forEach items="${codeZxjxjhxnxqList}" var="xnxq">
                            <option value="${xnxq.zxjxjhh}" <c:if test="${xnxq.zxjxjhh == zxjxjhh}"> selected </c:if>>${xnxq.zxjxjhm}</option>
                        </c:forEach>
                    </select>
                </div>
                
                <div class="form-row">
                    <div class="form-label">课程号</div>
                    <input type="text" class="form-input" id="kch" name="kch" placeholder="请输入课程号">
                </div>
                
                <div class="form-row">
                    <div class="form-label">课程名</div>
                    <input type="text" class="form-input" id="kcm" name="kcm" placeholder="请输入课程名">
                </div>
                
                <button class="search-button" onclick="search();">
                    <i class="ace-icon fa fa-search"></i>
                    查询
                </button>
            </div>
        </div>
        
        <!-- 实验成绩列表 -->
        <div class="experiment-list" id="experimentList" style="display: none;">
            <div class="list-header">
                <i class="ace-icon fa fa-list"></i>
                实验成绩列表
            </div>
            
            <div id="experimentItems">
                <!-- 动态加载实验成绩项 -->
            </div>
            
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <button class="load-more-button" id="loadMoreButton" onclick="loadMore();">
                    加载更多
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-flask"></i>
            <div class="empty-state-text">暂无实验成绩</div>
            <div class="empty-state-desc">请调整查询条件后重试</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let isLoading = false;

        $(function() {
            initPage();
            search();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 查询实验成绩
        function search() {
            currentPage = 1;
            getResultsList(currentPage, true);
        }

        // 加载更多
        function loadMore() {
            if (isLoading) return;
            currentPage++;
            getResultsList(currentPage, false);
        }

        // 获取结果列表
        function getResultsList(page, isNewSearch) {
            if (isLoading) return;

            isLoading = true;
            showLoading(true);

            const zxjxjhh = $('#zxjxjhh').val();
            const xsh = '';
            const kch = $('#kch').val();
            const kcm = $('#kcm').val();

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/experimentScores/search",
                cache: false,
                type: "post",
                data: "zxjxjhh=" + zxjxjhh + "&xsh=" + xsh + "&kch=" + kch + "&kcm=" + kcm +
                      "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    totalCount = data["pageContext"].totalCount;

                    if (data["records"] != null) {
                        fillResultsTable(data["records"], !isNewSearch, page);
                    } else {
                        fillResultsTable(null, !isNewSearch, page);
                    }

                    updatePagination();
                },
                error: function(xhr) {
                    showError("获取数据失败，请重试");
                },
                complete: function() {
                    isLoading = false;
                    showLoading(false);
                }
            });
        }

        // 填充结果表格
        function fillResultsTable(data, isAppend, page) {
            let html = '';

            if (data != null && data.length > 0) {
                data.forEach(function(item, index) {
                    const tableId = isAppend ? (page - 1) * pageSize + 1 + index : index + 1;
                    html += createExperimentItem(item, tableId);
                });

                if (isAppend) {
                    $('#experimentItems').append(html);
                } else {
                    $('#experimentItems').html(html);
                }

                $('#experimentList').show();
                $('#emptyState').hide();
            } else {
                if (!isAppend) {
                    $('#experimentItems').html('');
                    $('#experimentList').hide();
                    $('#emptyState').show();
                }
            }
        }

        // 创建实验成绩项HTML
        function createExperimentItem(item, index) {
            return `
                <div class="experiment-item" onclick="lookExperiment('${item.SYRWID}', '${item.KCH}', '${item.KXH}')">
                    <div class="experiment-header-row">
                        <div class="experiment-info">
                            <div class="experiment-course">${item.KCM || ''}</div>
                            <div class="experiment-details">${item.KCH || ''} | 课序号: ${item.KXH || ''}</div>
                        </div>
                        <div class="experiment-score">
                            ${item.BFZCJ || item.DJCJ || ''}
                        </div>
                    </div>

                    <div class="experiment-meta">
                        <div class="meta-item">
                            <div class="meta-label">课程学时</div>
                            <div class="meta-value">${item.KCXS || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">理论学时</div>
                            <div class="meta-value">${item.LLXS || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">实验学时</div>
                            <div class="meta-value">${item.SYXS || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">独立实验课</div>
                            <div class="meta-value">${item.DLSYK || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">百分制成绩</div>
                            <div class="meta-value">${item.BFZCJ || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">等级成绩</div>
                            <div class="meta-value">${item.DJCJ || ''}</div>
                        </div>
                    </div>

                    <button class="view-button" onclick="event.stopPropagation(); lookExperiment('${item.SYRWID}', '${item.KCH}', '${item.KXH}');">
                        <i class="ace-icon fa fa-eye"></i>
                        查看实验项目成绩
                    </button>
                </div>
            `;
        }

        // 更新分页
        function updatePagination() {
            const hasMore = currentPage * pageSize < totalCount;

            if (hasMore) {
                $('#paginationContainer').show();
                $('#loadMoreButton').prop('disabled', false);
            } else {
                $('#paginationContainer').hide();
            }
        }

        // 查看实验成绩
        function lookExperiment(syrwid, kch, kxh) {
            showLoading(true);

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/experimentScores/look",
                type: "post",
                data: "syrwid=" + syrwid + "&kch=" + kch + "&kxh=" + kxh,
                dataType: "json",
                success: function(d) {
                    if (d["cjflag"] == true) {
                        const url = "/student/integratedQuery/scoreQuery/experimentScores/mxcjIndex/" +
                                   syrwid + "/" + kch + "/" + kxh;
                        window.location.href = url;
                    } else {
                        showError("当前实验项目暂无成绩！");
                    }
                },
                error: function(xhr) {
                    showError("获取实验成绩失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 刷新数据
        function refreshData() {
            search();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight() || 0;
            const availableHeight = windowHeight - navbarHeight;

            $('.page-mobile').css('min-height', availableHeight + 'px');
        }
    </script>
</body>
</html>
