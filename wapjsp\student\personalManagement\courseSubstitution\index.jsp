<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>课程替代</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 课程替代页面样式 */
        .substitution-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .substitution-notice {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            margin-right: var(--margin-xs);
            color: var(--info-color);
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .notice-list {
            margin-top: var(--margin-sm);
            padding-left: var(--padding-md);
        }
        
        .notice-item {
            margin-bottom: 4px;
        }
        
        .substitution-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-apply {
            background: var(--success-color);
            color: white;
        }
        
        .btn-search {
            background: var(--info-color);
            color: white;
        }
        
        .my-substitutions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .substitutions-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .substitutions-title {
            display: flex;
            align-items: center;
        }
        
        .substitutions-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .substitutions-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .substitution-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .substitution-item:last-child {
            border-bottom: none;
        }
        
        .substitution-item:active {
            background: var(--bg-color-active);
        }
        
        .substitution-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .substitution-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .substitution-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .substitution-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .substitution-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .substitution-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .course-mapping {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
        }
        
        .mapping-row {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-xs);
        }
        
        .mapping-row:last-child {
            margin-bottom: 0;
        }
        
        .course-info {
            flex: 1;
            font-size: var(--font-size-small);
        }
        
        .course-name {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .course-code {
            color: var(--text-secondary);
        }
        
        .mapping-arrow {
            margin: 0 var(--margin-sm);
            color: var(--primary-color);
        }
        
        .substitution-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .substitution-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .substitution-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .substitution-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .course-selector {
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .course-selector:hover {
            border-color: var(--primary-color);
        }
        
        .course-selector.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .selector-placeholder {
            color: var(--text-disabled);
            font-style: italic;
        }
        
        .selected-course {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .course-detail {
            flex: 1;
        }
        
        .course-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .course-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .remove-course {
            color: var(--error-color);
            cursor: pointer;
            margin-left: var(--margin-sm);
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">课程替代</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="substitution-header">
            <div class="header-title">课程替代</div>
            <div class="header-subtitle">申请用已修课程替代培养方案中的课程</div>
        </div>

        <!-- 申请须知 -->
        <div class="substitution-notice">
            <div class="notice-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>申请须知</span>
            </div>
            <div class="notice-content">
                课程替代申请需要满足以下条件：
                <div class="notice-list">
                    <div class="notice-item">• 替代课程与被替代课程内容相近</div>
                    <div class="notice-item">• 替代课程学分不低于被替代课程</div>
                    <div class="notice-item">• 替代课程成绩不低于70分</div>
                    <div class="notice-item">• 需要提供课程大纲等证明材料</div>
                    <div class="notice-item">• 每门课程只能申请一次替代</div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="substitution-actions">
            <button class="btn-mobile btn-apply flex-1" onclick="showSubstitutionForm();">
                <i class="ace-icon fa fa-plus"></i>
                <span>申请替代</span>
            </button>
            <button class="btn-mobile btn-search flex-1" onclick="searchCourses();">
                <i class="ace-icon fa fa-search"></i>
                <span>查找课程</span>
            </button>
        </div>

        <!-- 我的申请 -->
        <div class="my-substitutions">
            <div class="substitutions-header">
                <div class="substitutions-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的申请</span>
                </div>
                <div class="substitutions-count" id="substitutionsCount">0</div>
            </div>

            <div id="substitutionsList">
                <!-- 替代申请列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-exchange"></i>
            <div id="emptyMessage">暂无课程替代申请</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 课程替代申请表单 -->
    <div class="substitution-form" id="substitutionForm">
        <div class="form-header">
            <div class="form-back" onclick="closeSubstitutionForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">课程替代申请</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业班级</div>
                    <input type="text" class="form-input" id="majorClass" readonly>
                </div>
            </div>

            <!-- 被替代课程 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-book"></i>
                    <span>被替代课程</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">选择课程</div>
                    <div class="course-selector" id="targetCourseSelector" onclick="selectTargetCourse()">
                        <div class="selector-placeholder">点击选择需要被替代的课程</div>
                    </div>
                </div>
            </div>

            <!-- 替代课程 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-exchange"></i>
                    <span>替代课程</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">选择课程</div>
                    <div class="course-selector" id="sourceCourseSelector" onclick="selectSourceCourse()">
                        <div class="selector-placeholder">点击选择用于替代的课程</div>
                    </div>
                </div>
            </div>

            <!-- 替代理由 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>替代理由</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">申请理由</div>
                    <textarea class="form-input form-textarea" id="reason"
                              placeholder="请详细说明申请课程替代的理由，包括两门课程的相似性、学习内容对比等..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">课程对比</div>
                    <textarea class="form-input form-textarea" id="comparison"
                              placeholder="请对比两门课程的教学内容、知识点、学分等方面的相似性..."></textarea>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮箱地址</div>
                    <input type="email" class="form-input" id="email" placeholder="请输入邮箱地址">
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeSubstitutionForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitSubstitution();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let mySubstitutions = [];
        let studentInfo = {};
        let availableCourses = [];
        let completedCourses = [];
        let selectedTargetCourse = null;
        let selectedSourceCourse = null;
        let currentSubstitution = null;

        $(function() {
            initPage();
            loadStudentInfo();
            loadAvailableCourses();
            loadCompletedCourses();
            loadMySubstitutions();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/personalManagement/courseSubstitution/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 加载可选课程
        function loadAvailableCourses() {
            $.ajax({
                url: "/student/personalManagement/courseSubstitution/getAvailableCourses",
                type: "post",
                dataType: "json",
                success: function(data) {
                    availableCourses = data.courses || [];
                },
                error: function() {
                    console.log('加载可选课程失败');
                }
            });
        }

        // 加载已修课程
        function loadCompletedCourses() {
            $.ajax({
                url: "/student/personalManagement/courseSubstitution/getCompletedCourses",
                type: "post",
                dataType: "json",
                success: function(data) {
                    completedCourses = data.courses || [];
                },
                error: function() {
                    console.log('加载已修课程失败');
                }
            });
        }

        // 加载我的申请
        function loadMySubstitutions() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/courseSubstitution/getMySubstitutions",
                type: "post",
                dataType: "json",
                success: function(data) {
                    mySubstitutions = data.substitutions || [];
                    renderSubstitutionsList();
                    updateSubstitutionsCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载申请列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderSubstitutionsList() {
            const container = $('#substitutionsList');
            container.empty();

            if (mySubstitutions.length === 0) {
                showEmptyState('暂无课程替代申请');
                return;
            } else {
                hideEmptyState();
            }

            mySubstitutions.forEach(substitution => {
                const substitutionHtml = createSubstitutionItem(substitution);
                container.append(substitutionHtml);
            });
        }

        // 创建申请项
        function createSubstitutionItem(substitution) {
            const statusClass = getSubstitutionStatusClass(substitution.status);
            const statusText = getSubstitutionStatusText(substitution.status);

            return `
                <div class="substitution-item ${statusClass}" onclick="showSubstitutionDetail('${substitution.id}')">
                    <div class="substitution-basic">
                        <div class="substitution-title">课程替代申请</div>
                        <div class="substitution-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="course-mapping">
                        <div class="mapping-row">
                            <div class="course-info">
                                <div class="course-name">${substitution.sourceCourse.name}</div>
                                <div class="course-code">${substitution.sourceCourse.code} (${substitution.sourceCourse.credits}学分)</div>
                            </div>
                            <div class="mapping-arrow">
                                <i class="ace-icon fa fa-arrow-right"></i>
                            </div>
                            <div class="course-info">
                                <div class="course-name">${substitution.targetCourse.name}</div>
                                <div class="course-code">${substitution.targetCourse.code} (${substitution.targetCourse.credits}学分)</div>
                            </div>
                        </div>
                    </div>
                    <div class="substitution-details">
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(substitution.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>学分差异:</span>
                            <span>${substitution.sourceCourse.credits - substitution.targetCourse.credits}学分</span>
                        </div>
                        <div class="detail-item">
                            <span>替代课程成绩:</span>
                            <span>${substitution.sourceCourse.grade || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span>更新时间:</span>
                            <span>${formatDate(substitution.updateTime)}</span>
                        </div>
                    </div>
                    <div class="substitution-actions">
                        ${createSubstitutionActions(substitution)}
                    </div>
                </div>
            `;
        }

        // 创建申请操作按钮
        function createSubstitutionActions(substitution) {
            const canEdit = substitution.status === 'draft';

            let actions = [];

            actions.push(`<button class="btn-mobile btn-view" onclick="showSubstitutionDetail('${substitution.id}')">查看</button>`);

            if (canEdit) {
                actions.push(`<button class="btn-mobile btn-edit" onclick="editSubstitution('${substitution.id}')">编辑</button>`);
                actions.push(`<button class="btn-mobile btn-delete" onclick="deleteSubstitution('${substitution.id}')">删除</button>`);
            }

            return actions.join('');
        }

        // 获取申请状态样式类
        function getSubstitutionStatusClass(status) {
            switch(status) {
                case 'pending': return 'pending';
                case 'approved': return 'approved';
                case 'rejected': return 'rejected';
                default: return 'pending';
            }
        }

        // 获取申请状态文本
        function getSubstitutionStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示课程替代申请表单
        function showSubstitutionForm() {
            currentSubstitution = null;

            // 设置表单标题
            $('#formTitle').text('课程替代申请');

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 重置表单
            resetForm();

            // 显示表单
            $('#substitutionForm').addClass('show');
        }

        // 重置表单
        function resetForm() {
            selectedTargetCourse = null;
            selectedSourceCourse = null;
            updateCourseSelector('#targetCourseSelector', null, '点击选择需要被替代的课程');
            updateCourseSelector('#sourceCourseSelector', null, '点击选择用于替代的课程');
            $('#reason').val('');
            $('#comparison').val('');
            $('#contactPhone').val('');
            $('#email').val('');
        }

        // 更新课程选择器
        function updateCourseSelector(selector, course, placeholder) {
            const container = $(selector);
            container.removeClass('selected');

            if (course) {
                container.addClass('selected');
                container.html(`
                    <div class="selected-course">
                        <div class="course-detail">
                            <div class="course-name">${course.name}</div>
                            <div class="course-meta">${course.code} | ${course.credits}学分 | ${course.grade || '未知成绩'}</div>
                        </div>
                        <i class="remove-course ace-icon fa fa-times" onclick="removeCourse('${selector}')"></i>
                    </div>
                `);
            } else {
                container.html(`<div class="selector-placeholder">${placeholder}</div>`);
            }
        }

        // 选择被替代课程
        function selectTargetCourse() {
            if (availableCourses.length === 0) {
                showError('暂无可选课程');
                return;
            }

            showCourseSelectionDialog(availableCourses, '选择被替代课程', function(course) {
                selectedTargetCourse = course;
                updateCourseSelector('#targetCourseSelector', course, '点击选择需要被替代的课程');
            });
        }

        // 选择替代课程
        function selectSourceCourse() {
            if (completedCourses.length === 0) {
                showError('暂无已修课程');
                return;
            }

            showCourseSelectionDialog(completedCourses, '选择替代课程', function(course) {
                selectedSourceCourse = course;
                updateCourseSelector('#sourceCourseSelector', course, '点击选择用于替代的课程');
            });
        }

        // 显示课程选择对话框
        function showCourseSelectionDialog(courses, title, callback) {
            let options = courses.map(course =>
                `${course.name} (${course.code}) - ${course.credits}学分`
            );

            // 简化版选择对话框，实际应用中可以使用更复杂的UI
            let message = `${title}\n\n`;
            courses.forEach((course, index) => {
                message += `${index + 1}. ${course.name} (${course.code}) - ${course.credits}学分\n`;
            });
            message += '\n请输入序号选择课程：';

            const input = prompt(message);
            if (input) {
                const index = parseInt(input) - 1;
                if (index >= 0 && index < courses.length) {
                    callback(courses[index]);
                } else {
                    showError('选择无效，请重新选择');
                }
            }
        }

        // 移除课程选择
        function removeCourse(selector) {
            if (selector === '#targetCourseSelector') {
                selectedTargetCourse = null;
                updateCourseSelector(selector, null, '点击选择需要被替代的课程');
            } else if (selector === '#sourceCourseSelector') {
                selectedSourceCourse = null;
                updateCourseSelector(selector, null, '点击选择用于替代的课程');
            }
        }

        // 关闭课程替代申请表单
        function closeSubstitutionForm() {
            $('#substitutionForm').removeClass('show');
        }

        // 编辑申请
        function editSubstitution(substitutionId) {
            const substitution = mySubstitutions.find(s => s.id === substitutionId);
            if (!substitution) return;

            currentSubstitution = substitution;

            // 设置表单标题
            $('#formTitle').text('编辑课程替代申请');

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 填充申请数据
            selectedTargetCourse = substitution.targetCourse;
            selectedSourceCourse = substitution.sourceCourse;
            updateCourseSelector('#targetCourseSelector', selectedTargetCourse, '点击选择需要被替代的课程');
            updateCourseSelector('#sourceCourseSelector', selectedSourceCourse, '点击选择用于替代的课程');
            $('#reason').val(substitution.reason);
            $('#comparison').val(substitution.comparison);
            $('#contactPhone').val(substitution.contactPhone);
            $('#email').val(substitution.email);

            // 显示表单
            $('#substitutionForm').addClass('show');
        }

        // 删除申请
        function deleteSubstitution(substitutionId) {
            const substitution = mySubstitutions.find(s => s.id === substitutionId);
            if (!substitution) return;

            const message = `确定要删除课程替代申请吗？\n\n${substitution.sourceCourse.name} → ${substitution.targetCourse.name}\n\n删除后无法恢复。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doDeleteSubstitution(substitutionId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doDeleteSubstitution(substitutionId);
                }
            }
        }

        // 执行删除申请
        function doDeleteSubstitution(substitutionId) {
            $.ajax({
                url: "/student/personalManagement/courseSubstitution/deleteSubstitution",
                type: "post",
                data: { substitutionId: substitutionId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('申请删除成功');
                        loadMySubstitutions();
                    } else {
                        showError(data.message || '删除失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 保存草稿
        function saveDraft() {
            if (!validateForm(false)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交课程替代申请
        function submitSubstitution() {
            if (!validateForm(true)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = false;

            const message = `确定要提交课程替代申请吗？\n\n${selectedSourceCourse.name} → ${selectedTargetCourse.name}\n\n提交后将进入审核流程，请确保信息准确无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitFormData(formData, '课程替代申请提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    submitFormData(formData, '课程替代申请提交成功');
                }
            }
        }

        // 收集表单数据
        function collectFormData() {
            return {
                id: currentSubstitution ? currentSubstitution.id : null,
                targetCourseId: selectedTargetCourse ? selectedTargetCourse.id : null,
                sourceCourseId: selectedSourceCourse ? selectedSourceCourse.id : null,
                reason: $('#reason').val(),
                comparison: $('#comparison').val(),
                contactPhone: $('#contactPhone').val(),
                email: $('#email').val()
            };
        }

        // 验证表单
        function validateForm(isSubmit) {
            if (!selectedTargetCourse) {
                showError('请选择被替代课程');
                return false;
            }

            if (!selectedSourceCourse) {
                showError('请选择替代课程');
                return false;
            }

            if (selectedTargetCourse.id === selectedSourceCourse.id) {
                showError('替代课程和被替代课程不能相同');
                return false;
            }

            if (!$('#reason').val().trim()) {
                showError('请填写申请理由');
                return false;
            }

            if (!$('#contactPhone').val().trim()) {
                showError('请填写联系电话');
                return false;
            }

            // 检查学分要求
            if (selectedSourceCourse.credits < selectedTargetCourse.credits) {
                const message = `替代课程学分(${selectedSourceCourse.credits})低于被替代课程学分(${selectedTargetCourse.credits})，是否继续？`;
                if (isSubmit) {
                    if (typeof urp !== 'undefined' && urp.confirm) {
                        return new Promise(resolve => {
                            urp.confirm(message, resolve);
                        });
                    } else {
                        return confirm(message);
                    }
                }
            }

            return true;
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            $.ajax({
                url: "/student/personalManagement/courseSubstitution/submitSubstitution",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        closeSubstitutionForm();
                        loadMySubstitutions();
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示申请详情
        function showSubstitutionDetail(substitutionId) {
            const substitution = mySubstitutions.find(s => s.id === substitutionId);
            if (!substitution) return;

            let message = `课程替代申请详情\n\n`;
            message += `申请时间：${formatDate(substitution.createTime)}\n`;
            message += `当前状态：${getSubstitutionStatusText(substitution.status)}\n\n`;
            message += `被替代课程：\n`;
            message += `  ${substitution.targetCourse.name}\n`;
            message += `  课程代码：${substitution.targetCourse.code}\n`;
            message += `  学分：${substitution.targetCourse.credits}\n\n`;
            message += `替代课程：\n`;
            message += `  ${substitution.sourceCourse.name}\n`;
            message += `  课程代码：${substitution.sourceCourse.code}\n`;
            message += `  学分：${substitution.sourceCourse.credits}\n`;
            message += `  成绩：${substitution.sourceCourse.grade || '未知'}\n\n`;
            message += `申请理由：\n${substitution.reason}\n`;

            if (substitution.comparison) {
                message += `\n课程对比：\n${substitution.comparison}\n`;
            }

            if (substitution.reviewComment) {
                message += `\n审核意见：${substitution.reviewComment}\n`;
            }

            if (substitution.reviewTime) {
                message += `审核时间：${formatDate(substitution.reviewTime)}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 搜索课程
        function searchCourses() {
            const keyword = prompt('请输入课程名称或课程代码：');
            if (!keyword) return;

            const allCourses = [...availableCourses, ...completedCourses];
            const results = allCourses.filter(course =>
                course.name.toLowerCase().includes(keyword.toLowerCase()) ||
                course.code.toLowerCase().includes(keyword.toLowerCase())
            );

            if (results.length === 0) {
                showError('未找到相关课程');
                return;
            }

            let message = `搜索结果 (${results.length}门课程)：\n\n`;
            results.forEach((course, index) => {
                message += `${index + 1}. ${course.name}\n`;
                message += `   课程代码：${course.code}\n`;
                message += `   学分：${course.credits}\n`;
                message += `   类型：${course.type === 'available' ? '可选课程' : '已修课程'}\n\n`;
            });

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 更新申请数量
        function updateSubstitutionsCount() {
            $('#substitutionsCount').text(mySubstitutions.length);
        }

        // 刷新数据
        function refreshData() {
            loadAvailableCourses();
            loadCompletedCourses();
            loadMySubstitutions();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.my-substitutions').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.my-substitutions').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.my-substitutions').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#substitutionForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#substitutionForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeSubstitutionForm();
            }
        });

        $('#substitutionForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
