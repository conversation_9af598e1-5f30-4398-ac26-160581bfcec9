<%@ page language="java" contentType="text/html; charset=UTF-8"
		 pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page
		import="java.util.*,educationalAdministration.student.scoreSearch.entity.*" %>


<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>


<head>
	<title>全部及格成绩</title>
	<link rel="stylesheet" href="/assets/css/jquery-ui.custom.min.css"/>
	<link rel="stylesheet" href="/assets/css/fullcalendar.css"/>
	<script src="/assets/js/date-time/moment.min.js"></script>
	<script src="/assets/js/fullcalendar.min.js"></script>
	<script src="/assets/js/date-time/moment.min.js"></script>
	<script src="/assets/js/fullcalendar.min.js"></script>
	<script src="/assets/js/bootbox.min.js"></script>
	<!---->
	<script src="/assets/js/jquery.gritter.min.js"></script>
	<link rel="stylesheet" href="/assets/css/jquery.gritter.css"/>
	<link rel="stylesheet" href="/assets/css/ace.min.css" id="main-ace-style"/>
	<style type="text/css">
		.grid5 {
			width: 30%;
			height: 95px;
			-webkit-box-sizing: border-box;
			-moz-box-sizing: border-box;
			box-sizing: border-box;
			display: block;
			margin: 0 1%;
			padding: 0 2%;
			float: left;
			border-left: 1px solid #E3E3E3;
			vertical-align: middle;
		}

		.grid5:first-child {
			border-left: none;
		}

		.widget-color-blue.widget-header {
			background: #bce8f1;
			border-color: #bce8f1;
		}

		.scrollspy-example {
			height: calc(100vh - 150px);
			overflow: auto;
			position: relative;
		}

		.timeline-container:before {
			content: "";
			display: block;
			position: absolute;
			left: 28px;
			top: 0;
			bottom: 0;
			border: 0 solid #E2E3E7;
			background-color: #E7EAEF;
			width: 4px;
			border-width: 0 1px;
		}
	</style>
	<script type="text/javascript">
		var index;
		$(function () {
			index = layer.load(0, {
				shade: [0.2, '#000'] //0.1透明度的白色背景
			});
			var url = "/student/integratedQuery/scoreQuery/${url_check_code}/allPassingScores/callback";
			$.get(url, function (data) {
				if (data.result && data.result == "error") {
					var message = "<div class='alert alert-danger'>非法请求！！！</div>";
					$("#showMessage").html(message);
					$("#showMessage").show();
					layer.close(index);
				} else {
					if (data.lnList.length == 0) {
						var message = "<div class='alert alert-block alert-success'><button type='button' class='close' data-dismiss='alert'><i class='ace-icon fa fa-times'></i>	</button>没有信息</div>";
						$("#showMessage").html(message);
						$("#showMessage").show();
						layer.close(index);
					} else {
						$("#showMessage").show();
						useScoreExtension(data);
					}
				}
			});
		});

		function useScoreExtension(lnList) {
			$.ajax({
				url: "/student/integratedQuery/scoreQuery/coursePropertyScores/useScoreExtension",
				type: "post",
				data: "",
				dataType: "json",
				beforeSend: function () {
					index = layer.load(0, {
						shade: [0.2, '#000']
						//0.1透明度的白色背景
					});
				},
				success: function (d) {
					$("#param").val(d["param"]);
					$("#schoolName").val(d["schoolName"]);
					fillScoreTable(lnList);
				},
				error: function (xhr) {
					layer.close(index);
					urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status
							+ "]:操作失败！");
				},
				complete: function () {
					layer.close(index);
				}
			});
		}
		function fillScoreTable(data) {
			var lnList = data["lnList"];
			if (lnList != null && lnList.length > 0) {
				var htmlStrings = "";
				var htmlNavbar = " ";
				var param = $("#param").val();
				var schoolName = $("#schoolName").val();
				var showScoreDetail = $("#showScoreDetail").val();
				for (var i = lnList.length - 1; i >= 0; i--) {
					var str = lnList[i].cjbh;
					var a = "'ace-icon fa fa-nrl fa-2x '";
					var aa;
					if (lnList[i].yxxf / lnList[i].yqxf >= 1) {
						if (str.indexOf("秋") >= 0) {
							a = " ace-icon fa  fa-calendar btn btn-warning no-hover";
							aa = "btn-warning";
						} else {
							a = " ace-icon fa  fa-calendar btn btn-success no-hover";
							aa = "btn-success";
						}
					}
					var active = "";
					if (i == lnList.length - 1)
						active = "active";
					htmlNavbar += "<li class='" + active + "'><a href='#tab" + (i + 1) + "' >" + lnList[i].cjbh.split("(")[0] + "</a></li>";
					var htmlString = "<div class='tab-pane active' id='tab" + (i + 1) + "' >"
							+ "<h4 class='header smaller lighter grey'><i class='menu-icon fa fa-calendar'></i>   " + lnList[i].cjlx
							+ "  <span class='label label-yellow' style='border-radius: 10px;'><font style='color:black;'>已修" + lnList[i].zms + "门，" + lnList[i].yxxf + "学分</font></span>"
							+ "  <span class='label label-success' style='border-radius: 10px;'><font style='color:black;'>通过" + lnList[i].tgms + "门</font></span>"
							+ "</h4>"
							+ "<div class='row'>"
							+ "<div class='col-sm-12'>"
							+ "<table class='table table-striped table-bordered table-hover' style='margin-bottom: 0px;'>"
							+ "<thead>"
							+ "<tr>"
							+ "<th>序号</th>"
							+ "<th>课程号</th>"
							+ "<th>课序号</th>"
							+ "<th>课程名</th>"
							+ "<th>课程属性</th>"
							+ "<th>学分</th>"
							+ "<th>成绩</th>";
					if (schoolName == "100027") {
						htmlString += "<th>选课课组名</th>";
					}
					//htmlString += "<th>绩点成绩</th>";
					htmlString += "<th>英文课程名</th>"
							+ "</tr>"
							+ "</thead>"
							+ "<tbody>";

					var htmlString1 = "";
					var htmlString2 = "";
					if (lnList[i].cjList != null) {
						for (var j = 0; j < lnList[i].cjList.length; j++) {
							htmlString1 = "<tr>" + "<td>" + (j + 1) + "</td>"
									+ "<td >" + lnList[i].cjList[j].id.courseNumber + "" + "</td>"
									+ "<td >" + (lnList[i].cjList[j].id.coureSequenceNumber == null ? "" : (lnList[i].cjList[j].id.coureSequenceNumber == 'NONE' ? "" : lnList[i].cjList[j].id.coureSequenceNumber)) + "" + "</td>"
									+ "<td >" + lnList[i].cjList[j].courseName + "" + "</td>"
									+ "<td >" + lnList[i].cjList[j].courseAttributeName + "" + "</td>"
									+ "<td >" + lnList[i].cjList[j].credit + "" + "</td>";
							if (schoolName == "100010") {
								if (showScoreDetail != "0") {
									if (lnList[i].cjList[j].id.courseNumber == "58000001" || lnList[i].cjList[j].id.courseNumber == "58000002" || lnList[i].cjList[j].id.courseNumber == "58000003" || lnList[i].cjList[j].id.courseNumber == "58000004" || lnList[i].cjList[j].id.courseNumber == "58000005") {
										htmlString1 += "<td><a style='cursor: pointer;text-decoration: underline;' title='" + (param == "1" ? "查看分项成绩" : "查看明细成绩") + "' onclick='lookSubitemScore(\"" + lnList[i].cjList[j].id.executiveEducationPlanNumber + "\",\"" + lnList[i].cjList[j].id.courseNumber + "\",\"" + lnList[i].cjList[j].id.coureSequenceNumber + "\",\"" + lnList[i].cjList[j].id.startTime + "\",\"" + lnList[i].cjList[j].courseAttributeCode + "\")'>" + lnList[i].cjList[j].courseScore + "" + "</a></td>";
									} else {
										htmlString1 += "<td><a style='cursor: pointer;text-decoration: underline;' title='" + (param == "1" ? "查看分项成绩" : "查看明细成绩") + "' onclick='lookSubitemScore(\"" + lnList[i].cjList[j].id.executiveEducationPlanNumber + "\",\"" + lnList[i].cjList[j].id.courseNumber + "\",\"" + lnList[i].cjList[j].id.coureSequenceNumber + "\",\"" + lnList[i].cjList[j].id.startTime + "\",\"" + lnList[i].cjList[j].courseAttributeCode + "\")'>" + lnList[i].cjList[j].gradeName + "" + "</a></td>";
									}
								} else {
									if (lnList[i].cjList[j].id.courseNumber == "58000001" || lnList[i].cjList[j].id.courseNumber == "58000002" || lnList[i].cjList[j].id.courseNumber == "58000003" || lnList[i].cjList[j].id.courseNumber == "58000004" || lnList[i].cjList[j].id.courseNumber == "58000005") {
										htmlString1 += "<td>" + lnList[i].cjList[j].courseScore + "" + "</td>";
									} else {
										htmlString1 += "<td>" + lnList[i].cjList[j].gradeName + "" + "</td>";
									}
								}
							} else {
								if (lnList[i].cjList[j].scoreEntryModeCode == "001") {
									if (showScoreDetail != "0") {
										htmlString1 += "<td><a style='cursor: pointer;text-decoration: underline;' title='" + (param == "1" ? "查看分项成绩" : "查看明细成绩") + "' onclick='lookSubitemScore(\"" + lnList[i].cjList[j].id.executiveEducationPlanNumber + "\",\"" + lnList[i].cjList[j].id.courseNumber + "\",\"" + lnList[i].cjList[j].id.coureSequenceNumber + "\",\"" + lnList[i].cjList[j].id.startTime + "\",\"" + lnList[i].cjList[j].courseAttributeCode + "\")'>" + lnList[i].cjList[j].courseScore + "" + "</a></td>";
									} else {
										htmlString1 += "<td>" + lnList[i].cjList[j].courseScore + "" + "</td>";
									}
								} else if (lnList[i].cjList[j].scoreEntryModeCode == "002") {
									if (showScoreDetail != "0") {
										htmlString1 += "<td><a style='cursor: pointer;text-decoration: underline;' title='" + (param == "1" ? "查看分项成绩" : "查看明细成绩") + "' onclick='lookSubitemScore(\"" + lnList[i].cjList[j].id.executiveEducationPlanNumber + "\",\"" + lnList[i].cjList[j].id.courseNumber + "\",\"" + lnList[i].cjList[j].id.coureSequenceNumber + "\",\"" + lnList[i].cjList[j].id.startTime + "\",\"" + lnList[i].cjList[j].courseAttributeCode + "\")'>" + lnList[i].cjList[j].gradeName + "" + "</a></td>";
									} else {
										htmlString1 += "<td>" + lnList[i].cjList[j].gradeName + "" + "</td>";
									}
								} else {
									if (showScoreDetail != "0") {
										htmlString1 += "<td><a style='cursor: pointer;text-decoration: underline;' title='" + (param == "1" ? "查看分项成绩" : "查看明细成绩") + "' onclick='lookSubitemScore(\"" + lnList[i].cjList[j].id.executiveEducationPlanNumber + "\",\"" + lnList[i].cjList[j].id.courseNumber + "\",\"" + lnList[i].cjList[j].id.coureSequenceNumber + "\",\"" + lnList[i].cjList[j].id.startTime + "\",\"" + lnList[i].cjList[j].courseAttributeCode + "\")'>" + lnList[i].cjList[j].courseScore + "" + "</a></td>";
									} else {
										htmlString1 += "<td>" + lnList[i].cjList[j].courseScore + "" + "</td>";
									}
								}
							}
							if (schoolName == "100027") {
								htmlString1 += "<td>" + lnList[i].cjList[j].xkkzm + "</td>";
							}
				/* 			if (schoolName == "100010" && (lnList[i].cjList[j].gradeName == "P" || lnList[i].cjList[j].gradeName == "N" || lnList[i].cjList[j].gradeName == "EX")) {
								htmlString1 += "<td ></td>";
							} else {
								htmlString1 += "<td >" + lnList[i].cjList[j].gradePointScore + "" + "</td>";
							} */
							htmlString1 += "<td >" + lnList[i].cjList[j].englishCourseName + "" + "</td>"
//										+"<td >"+lnList[i].cjList[j].courseAttributeName+""
//										+"</td>"
									+ "</tr>"
							htmlString2 += htmlString1;
						}
					}
					htmlString += htmlString2;
					var b = Math.round((100 * lnList[i].yxxf / lnList[i].yqxf) * Math.pow(10, 1)) / Math.pow(10, 1);
					var col;
					if (parseInt(b) == 100) {
						col = '#68BC31';
					} else if (parseInt(b) > 100) {
						col = '#9585BF';
					} else if (parseInt(b) < 100) {
						col = '#DA5430';
					}
					var htmlString3 = "</tbody>"
							+ "</table>"
								//+ "<div class='clearfix'  style='border: 1px solid #E2E3E7;'>"

								/*  + "<div class='grid5 center'><br/><span class='bigger-175 blue '>"
								 + lnList[i].yqxf + "</span><br/><br/>最低修读学分</div>" */

								//	+ "<div class='grid5 center' ><br/><span class='bigger-175 blue'>"
								//	+ lnList[i].yxxf + "</span><br/><br/>已修读课程总学分</div>"
								//	+ "<div class='grid5 center' ><br/><span class='bigger-175 blue'>"
								//	+ lnList[i].zms + "</span><br/><br/>已修读课程门数</div>"
								//	+ "<div class='grid5 center' ><br/><span class='bigger-175 blue'>"
								//	+ lnList[i].tgms + "</span><br/><br/>通过课程门数</div>"
								/*   + "<div class='grid5 center'>"
								 //							+"<div class='infobox infobox-green infobox-small infobox-dark' style='width:100%'>"
								 //								+"<div class='infobox-progress'>"
								 //									+"<div class='easy-pie-chart percentage' data-percent='"+parseInt(b)+"' data-size='46'>"
								 //										+"<span class='percent'>"+parseInt(b)+"</span>%"
								 //									+"</div>"
								 //								+"</div>"
								 //								+"<div class='infobox-data'>"
								 //									+"<div class='infobox-content' style='width: 200%'>学分完成率</div>"
								 //								+"</div>"
								 //							+"</div>"
								 + "<div class='center easy-pie-chart percentage' data-percent='" + parseInt(b) + "' data-color=" + col + ">"
								 + "<span class='percent'>" + b + "</span>%</div>"
								 + "<div class='space-2'></div>学分完成率"
								 + "</div>"*/
								//	+ "</div>"
							+ "</div>"
							+ "</div>"
							+ "</div>"
							+ "</div>";
					htmlString += htmlString3;
					if (data["state"] == "1" && data["zxjxjhh"] == lnList[i].zxjxjhh) {
						htmlStrings += "<div class='tab-pane active' id='tab" + (i + 1) + "' >"
								+ "<h4 class='header smaller lighter grey'><i class='menu-icon fa fa-calendar'></i>   " + lnList[i].cjlx + "</h4>"
								+ "<div class='alert alert-block alert-success'><button type='button' class='close' data-dismiss='alert'><i class='ace-icon fa fa-times'></i>"
								+ "</button>该学期成绩查询已关闭，暂时不能查看成绩！</div></di>";
					} else {
						htmlStrings += htmlString;
					}
					htmlStrings = htmlStrings.replace(new RegExp(/(null)/g), '');
				}
				//
				$("#navbar_ul").html(htmlNavbar);
				$("#timeline").html(htmlStrings);
				$('#div-scroll').scrollspy({target: '.navbar-example'});
				layer.close(index);

				$('.easy-pie-chart.percentage').each(function () {
					var barColor = $(this).data('color') || '#555';
					var trackColor = '#E2E2E2';
					var size = parseInt($(this).data('size')) || 72;
					$(this).easyPieChart({
						barColor: barColor,
						trackColor: trackColor,
						scaleColor: false,
						lineCap: 'butt',
						lineWidth: parseInt(size / 10),
						animate: false,
						size: size
					}).css('color', barColor);
				});

			} else {
				var svg = '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="170px" height="170px" viewBox="0 0 200 200" enable-background="new 0 0 200 200" xml:space="preserve" p-id="422"><g p-id="423"><path fill="#EEEEEE" d="M18.224,35.559c0,0,0.38-0.042,0.592,0.211s0.465,3.804,1.395,4.776s4.692,0.423,4.692,1.691\
					c0,1.014-3.496,1.124-4.68,2.096c-1.184,0.972-0.507,5.072-1.957,4.921c-1.135-0.119-0.338-3.381-1.733-4.692\
					s-4.776-1.057-4.776-2.24s3.466-0.465,4.65-1.818C17.59,39.152,17.083,35.559,18.224,35.559z" p-id="424"></path><path fill="#B1AFAE" d="M7.726,77.11c0,0,0.23-0.026,0.357,0.128c0.128,0.153,0.281,2.296,0.842,2.883s2.832,0.255,2.832,1.02\
					c0,0.612-2.11,0.678-2.824,1.265c-0.714,0.587-0.306,3.061-1.181,2.97c-0.685-0.072-0.204-2.041-1.046-2.832\
					c-0.842-0.791-2.883-0.638-2.883-1.352s2.092-0.281,2.806-1.097C7.343,79.279,7.037,77.11,7.726,77.11z" p-id="425"></path><path fill="#EEEEEE" d="M190.447,56.933c0,0,0.261-0.029,0.406,0.145s0.319,2.608,0.956,3.274c0.637,0.666,3.216,0.29,3.216,1.159\
					c0,0.695-2.396,0.77-3.208,1.437c-0.811,0.666-0.348,3.477-1.341,3.373c-0.778-0.081-0.232-2.318-1.188-3.216\
					c-0.956-0.898-3.274-0.724-3.274-1.536s2.376-0.319,3.187-1.246C190.013,59.396,189.665,56.933,190.447,56.933z" p-id="426"></path><path fill="#B1AFAE" d="M154.66,25.617c0,0,0.261-0.029,0.406,0.145c0.145,0.174,0.319,2.608,0.956,3.274\
					c0.637,0.666,3.216,0.29,3.216,1.159c0,0.695-2.396,0.77-3.208,1.437c-0.811,0.666-0.348,3.477-1.341,3.373\
					c-0.778-0.081-0.232-2.318-1.188-3.216c-0.956-0.898-3.274-0.724-3.274-1.536s2.376-0.319,3.187-1.246\
					C154.226,28.08,153.878,25.617,154.66,25.617z" p-id="427"></path><circle fill="#EEEEEE" cx="56.234" cy="19.989" r="3.79" p-id="428"></circle><circle fill="#EEEEEE" cx="178.362" cy="75.509" r="2.376" p-id="429"></circle></g><circle fill="#EEEEEE" cx="95.662" cy="104.843" r="77.333" p-id="430"></circle><path fill="#FDFDFD" d="M145.856,131.98c-0.023,3.196-2.633,5.769-5.829,5.746l-89.136-0.146c-3.196-0.023-5.769-2.633-5.746-5.829\
					l0.431-56.782c0.023-3.196,2.633-5.769,5.829-5.746l72.81,0.029c5.893,5.294,13.625,12.765,21.971,19.869L145.856,131.98z" p-id="431"></path><path fill="#D8D8D8" d="M146.469,87.616c-0.026,1.112-0.949,1.992-2.061,1.966l-19.059-0.448c-1.112-0.026-1.992-0.949-1.966-2.061\
					l0.381-16.217c0.026-1.112,0.949-1.992,2.061-1.966L146.469,87.616z" p-id="432"></path><circle fill="#EEEEEE" cx="117.299" cy="128.428" r="18.247" p-id="433"></circle><path fill="#FFFFFF" d="M117.412,148.245c2.241,0,4.352-0.653,6.209-1.801l-0.006-2.304c0,0-0.31-3.921-3.169-4.83\
					c-0.044-0.014-0.76,0.77-2.055,0.699l-0.831-0.262c-0.085,0.004-0.178,0.127-0.262,0.131c-0.085-0.004-0.395-0.433-0.481-0.437\
					l-0.437,0.219c-1.294,0.071-2.054-0.403-2.098-0.389c-2.859,0.909-3.073,4.869-3.073,4.869l-0.006,2.304\
					C113.06,147.592,115.171,148.245,117.412,148.245z" p-id="434"></path><path fill="#FFFFFF" d="M126.565,131.668c-0.091-4.974-4.313-8.929-9.431-8.836c-5.117,0.094-9.192,4.202-9.1,9.175\
					c0.059,3.23,1.95,6.365,4.669,8.141l9.773-0.179c2.294-1.693,3.83-4.47,4.06-7.374C126.561,132.288,126.57,131.978,126.565,131.668z\
					M121.961,139.026l-9.001,0.165c-2.103-1.47-3.536-3.873-3.581-6.347c-0.074-4.03,3.384-7.361,7.723-7.441\
					c4.339-0.08,7.917,3.123,7.991,7.153C125.137,135.032,123.914,137.482,121.961,139.026z" p-id="435"></path><path fill="#B1AFAE" d="M113.09,139.511l8.674-0.159c1.881-1.543,3.058-3.992,3.013-6.467c-0.074-4.029-3.523-7.233-7.705-7.157\
					c-4.181,0.077-7.511,3.405-7.437,7.434C109.68,135.636,111.063,138.04,113.09,139.511z" p-id="436"></path><linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="213.0032" y1="105.5631" x2="213.0032" y2="105.5631" gradientTransform="matrix(0.9989 0.0478 -0.0478 0.9989 -99.1255 22.5725)" p-id="437"><stop offset="0.2225" style="stop-color:#FFFFFF" p-id="438"></stop><stop offset="1" style="stop-color:#D1D3D4" p-id="439"></stop></linearGradient><path fill="url(#SVGID_1_)" d="M108.588,138.199" p-id="440"></path><path fill="#B1AFAE" d="M122.101,140.456c-1.196,0.936-3.021,0.947-4.737,0.969c-1.752,0.023-3.397,0.04-4.644-0.756\
					c-0.398-0.254-0.581-0.843-0.41-0.847l10.184-0.231C122.665,139.587,122.402,140.221,122.101,140.456z" p-id="441"></path><path fill="#C8C7C6" d="M109.59,133.167c-0.074-4.049,3.268-7.393,7.465-7.47c4.197-0.077,7.659,3.143,7.734,7.191\
					c0.03,1.624-0.464,3.237-1.336,4.592c1.06-1.425,1.672-3.18,1.639-4.947c-0.074-4.049-3.665-7.267-8.02-7.187\
					c-4.355,0.08-7.826,3.427-7.752,7.477c0.027,1.493,0.558,2.96,1.434,4.214C110.041,135.862,109.615,134.525,109.59,133.167z" p-id="442"></path><path fill="#FFFFFF" d="M122.199,140.266c-1.218,0.535-3.07,0.508-4.805,0.538c-1.771,0.031-3.424,0.109-4.676-0.323\
					c-0.399-0.138-0.578-0.465-0.406-0.469l10.293-0.234C122.778,139.775,122.506,140.132,122.199,140.266z" p-id="443"></path><linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="221.3779" y1="106.4216" x2="221.3779" y2="106.4216" gradientTransform="matrix(0.9989 0.0478 -0.0478 0.9989 -99.1255 22.5725)" p-id="444"><stop offset="0.2225" style="stop-color:#FFFFFF" p-id="445"></stop><stop offset="1" style="stop-color:#D1D3D4" p-id="446"></stop></linearGradient><path fill="url(#SVGID_2_)" d="M116.912,139.457" p-id="447"></path><path fill="#C1C1C1" d="M122.63,139.791c0,0.241-0.196,0.437-0.437,0.437h-9.617c-0.241,0-0.437-0.196-0.437-0.437l0,0\
					c0-0.241,0.196-0.437,0.437-0.437h9.617C122.434,139.354,122.63,139.549,122.63,139.791L122.63,139.791z" p-id="448"></path><path fill="#B1AFAE" d="M119.922,145.551c0,0.108-0.088,0.196-0.196,0.196l-4.626,0.003c-0.108,0-0.196-0.088-0.196-0.196\
					l-0.002-3.131c0-0.108,0.088-0.196,0.196-0.196l4.626-0.003c0.108,0,0.196,0.088,0.196,0.196L119.922,145.551z" p-id="449"></path><path fill="#CCCCCC" d="M119.374,145.166c0,0.082-0.069,0.148-0.153,0.148l-3.616,0.002c-0.085,0-0.154-0.066-0.154-0.148\
					l-0.001-2.36c0-0.082,0.069-0.148,0.153-0.148l3.616-0.002c0.085,0,0.154,0.066,0.154,0.148L119.374,145.166z" p-id="450"></path><rect x="115.894" y="143.119" fill="#FFFFFF" width="1.315" height="0.527" p-id="451"></rect><rect x="117.613" y="143.118" fill="#FFFFFF" width="1.315" height="0.527" p-id="452"></rect><rect x="115.895" y="144.042" fill="#FFFFFF" width="3.034" height="0.813" p-id="453"></rect><g p-id="454"><path fill="#D8D8D8" d="M111.976,131.974c-0.82-0.543,0.176-2.081,1.023-2.932s1.519-1.188,2.189-1.014\
					c0.469,0.122,1.102,1.168-0.015,2.077C113.919,131.126,113.369,132.898,111.976,131.974z" p-id="455"></path><circle fill="#D8D8D8" cx="111.865" cy="133.908" r="0.962" p-id="456"></circle></g><path fill="#D8D8D8" d="M112.247,85.099c0,1.057-0.857,1.913-1.913,1.913H59.158c-1.057,0-1.913-0.857-1.913-1.913l0,0\
					c0-1.057,0.857-1.913,1.913-1.913h51.175C111.39,83.186,112.247,84.042,112.247,85.099L112.247,85.099z" p-id="457"></path><path fill="#D8D8D8" d="M83.201,98.717c0,1.057-0.857,1.913-1.913,1.913H59.158c-1.057,0-1.913-0.857-1.913-1.913l0,0\
					c0-1.057,0.857-1.913,1.913-1.913h22.129C82.344,96.804,83.201,97.66,83.201,98.717L83.201,98.717z" p-id="458"></path><path fill="#D8D8D8" d="M83.201,112.335c0,1.057-0.857,1.913-1.913,1.913H59.158c-1.057,0-1.913-0.857-1.913-1.913l0,0\
					c0-1.057,0.857-1.913,1.913-1.913h22.129C82.344,110.422,83.201,111.278,83.201,112.335L83.201,112.335z" p-id="459"></path><path fill="#D8D8D8" d="M141.451,148.653c-0.669-0.798-1.858-0.902-2.656-0.234l-0.003,0.003l-2.983-3.559\
					c3.835-4.361,6.162-10.08,6.162-16.344c0-13.675-11.086-24.76-24.76-24.76c-13.675,0-24.76,11.086-24.76,24.76\
					c0,13.675,11.086,24.76,24.76,24.76c5.177,0,9.983-1.59,13.957-4.307l2.876,3.43l-0.003,0.003c-0.798,0.669-0.902,1.858-0.234,2.656\
					l9.153,10.918c2.63-2.047,5.132-4.249,7.475-6.612L141.451,148.653z M117.149,146.768c-10.078,0-18.247-8.17-18.247-18.248\
					c0-10.078,8.17-18.247,18.247-18.247c10.078,0,18.248,8.17,18.248,18.247C135.397,138.598,127.227,146.768,117.149,146.768z" p-id="460"></path></svg>';
				var message = "<div style='text-align: center;'>" + svg + "<br><font>暂时木有内容呀~~</font></div>";
				$("#timeline").html(message);
			}
		}
		function lookSubitemScore(zxjxjhh, kch, kxh, kssj, kcsxdm) {
			var param = $("#param").val();
			$.ajax({
				url: "/student/integratedQuery/scoreQuery/subitemScore/look",
				type: "post",
				data: "zxjxjhh=" + zxjxjhh + "&kch=" + kch + "&kxh=" + kxh
				+ "&kssj=" + kssj + "&param=" + param,
				dataType: "json",
				beforeSend: function () {
					index = layer.load(0, {
						shade: [0.2, '#000']
						//0.1透明度的白色背景
					});
				},
				success: function (d) {
					if (d["scoreDetailList"].length > 0) {
						if (param == "1") {
							window.location.href = "/student/integratedQuery/scoreQuery/subitemScore/fxcjIndex/"
									+ zxjxjhh
									+ "/"
									+ kch
									+ "/"
									+ kxh
									+ "/"
									+ kssj + "/" + kcsxdm;
						} else {
							window.location.href = "/student/integratedQuery/scoreQuery/subitemScore/mxcjIndex/"
									+ zxjxjhh
									+ "/"
									+ kch
									+ "/"
									+ kxh
									+ "/"
									+ kssj;

						}
					} else {
						if (param == "1") {
							urp.alert("当前课程暂无分项成绩！");
						} else {
							urp.alert("当前课程暂无明细成绩！");
						}
					}
				},
				error: function (xhr) {
					layer.close(index);
					urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status
							+ "]:操作失败！");
				},
				complete: function () {
					layer.close(index);
				}
			});
		}
	</script>
</head>

<body>
<input type="hidden" id="param" name="param">
<input type="hidden" id="schoolName" name="schoolName">
<input type="hidden" id="showScoreDetail" name="showScoreDetail" value="${showScoreDetail }">

<div class="row">
	<div class="col-sm-12 self-margin">
		<div class="navbar-example" id="showMessage">
			<div id="navbarExample" class="navbar navbar-static" style="background: #ffffff">
				<div class="navbar-inner">
					<div class="container" style="width: auto;margin-left: -11px">
						<ul id="navbar_ul" class="nav nav-tabs">

						</ul>
					</div>
				</div>
			</div>
		</div>
		<div id="div-scroll" data-offset="50" class="scrollspy-example" style="margin-top: -9px;">
			<div class="row">
				<div class="col-xs-12">
					<div id="timeline-1">
						<div class="row">
							<div class="col-xs-12 ">
								<div class="" style="border: 0 ;">
									<div id="timeline" class="col-xs-12">

									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</body>
</html>
