<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>选课公告详情</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 选课公告详情页面样式 */
        .notice-detail-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }
        
        .notice-title {
            color: var(--primary-color);
            text-align: center;
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-lg);
            line-height: 1.4;
            font-family: "宋体", serif;
        }
        
        .notice-meta {
            color: var(--text-secondary);
            text-align: right;
            font-size: var(--font-size-small);
            margin-bottom: var(--margin-lg);
            font-family: "宋体", serif;
        }
        
        .notice-content {
            color: var(--text-primary);
            line-height: 1.8;
            font-size: var(--font-size-base);
            margin: var(--margin-xl) 0;
            text-indent: 2em;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .notice-attachment {
            margin-top: var(--margin-xl);
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }
        
        .attachment-label {
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            margin-bottom: var(--margin-sm);
            font-family: "宋体", serif;
        }
        
        .attachment-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: var(--padding-sm) var(--padding-md);
            background: var(--bg-tertiary);
            border-radius: 6px;
            color: var(--primary-color);
            text-decoration: none;
            font-size: var(--font-size-base);
            transition: all var(--transition-base);
            cursor: pointer;
        }
        
        .attachment-link:hover {
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }
        
        .attachment-link i {
            font-size: var(--font-size-base);
        }
        
        .action-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
            z-index: 100;
        }
        
        .btn-return {
            background: var(--secondary-color);
            color: white;
        }
        
        .page-content {
            padding-bottom: 80px; /* 为底部按钮留出空间 */
        }
        
        /* 隐藏表单 */
        .hidden-form {
            display: none;
        }
        
        @media (max-width: 480px) {
            .notice-detail-container {
                margin: var(--margin-xs);
                padding: var(--padding-md);
            }
            
            .notice-title {
                font-size: var(--font-size-h4);
            }
            
            .notice-content {
                font-size: var(--font-size-small);
                text-indent: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnNotice();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">选课公告详情</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <div class="page-content">
            <!-- 公告详情 -->
            <div class="notice-detail-container">
                <!-- 公告标题 -->
                <h1 class="notice-title">${xxfbTzView.title}</h1>
                
                <!-- 发布时间 -->
                <div class="notice-meta">
                    发布时间：${xxfbTzView.fbrq}
                </div>
                
                <!-- 公告内容 -->
                <div class="notice-content">${xxfbTzView.tznr}</div>
                
                <!-- 附件 -->
                <c:if test="${not empty xxfbTzView.fjmc}">
                    <div class="notice-attachment">
                        <div class="attachment-label">附件：</div>
                        <a class="attachment-link" onclick="downloadFile('${xxfbTzView.tzid}');">
                            <i class="ace-icon fa fa-download"></i>
                            <span>${xxfbTzView.fjmc}</span>
                        </a>
                    </div>
                </c:if>
            </div>
        </div>
        
        <!-- 底部操作按钮 -->
        <div class="action-buttons">
            <button class="btn-mobile btn-return flex-1" onclick="returnNotice();">
                <i class="ace-icon fa fa-reply"></i>
                <span>返回</span>
            </button>
        </div>
        
        <!-- 隐藏的下载表单 -->
        <form id="frm" name="frm" action="" method="get" class="hidden-form">
        </form>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            
            // 隐藏body滚动条（保持与PC端一致）
            document.body.style.overflow = "hidden";
        }

        // 返回公告列表
        function returnNotice() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                window.location.href = '/student/courseSelect/courseSelectNotice/index';
            }
        }

        // 下载附件
        function downloadFile(tzid) {
            if (!tzid) {
                showError('附件ID不存在');
                return;
            }
            
            const form = document.getElementById('frm');
            form.action = '/student/courseSelect/courseSelectNotice/downloadFile/' + tzid;
            form.submit();
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
