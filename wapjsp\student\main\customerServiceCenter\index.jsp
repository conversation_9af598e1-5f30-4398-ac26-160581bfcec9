<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>客服中心</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 客服中心页面样式 */
        .service-header {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .service-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .service-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .greeting-card {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .greeting-user {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .greeting-text {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .greeting-welcome {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .my-questions-link {
            color: var(--info-color);
            font-size: var(--font-size-small);
            cursor: pointer;
            text-decoration: none;
            float: right;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .search-input-wrapper {
            position: relative;
            border: 2px solid var(--info-color);
            border-radius: 25px;
            padding: 0 50px 0 20px;
            background: var(--bg-primary);
        }
        
        .search-input {
            width: 100%;
            padding: 12px 0;
            border: none;
            outline: none;
            font-size: var(--font-size-base);
            background: transparent;
            color: var(--text-primary);
        }
        
        .search-input::placeholder {
            color: var(--text-disabled);
        }
        
        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--info-color);
            font-size: 18px;
        }
        
        .suggestions-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border: 2px solid var(--info-color);
            border-top: 1px solid var(--divider-color);
            border-radius: 0 0 15px 15px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 100;
            display: none;
        }
        
        .suggestion-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
        
        .suggestion-item:hover {
            background: var(--bg-secondary);
        }
        
        .suggestion-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: 1.4;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .suggestion-stats {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            white-space: nowrap;
            margin-left: var(--margin-sm);
        }
        
        .suggestion-badge {
            background: var(--info-color);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            margin-left: 4px;
        }
        
        .suggestion-badge.new {
            background: var(--success-color);
        }
        
        .suggestion-badge.recommend {
            background: var(--warning-color);
        }
        
        .question-detail {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .question-title {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
        }
        
        .question-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .question-content {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            line-height: 1.6;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .question-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            margin-bottom: var(--margin-lg);
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            transition: color var(--transition-base);
        }
        
        .action-btn.liked {
            color: var(--error-color);
        }
        
        .action-btn.disliked {
            color: var(--success-color);
        }
        
        .action-btn:hover {
            color: var(--text-primary);
        }
        
        .feedback-section {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .feedback-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .feedback-textarea {
            width: 100%;
            min-height: 80px;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            resize: vertical;
            margin-bottom: var(--margin-sm);
        }
        
        .feedback-textarea:focus {
            border-color: var(--info-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(23, 162, 184, 0.2);
        }
        
        .feedback-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .anonymous-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .btn-submit-feedback {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-submit-feedback:hover {
            background: var(--info-dark);
        }
        
        .contact-section {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .contact-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .contact-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--margin-sm);
        }
        
        .submit-question {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .submit-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .submit-link {
            color: var(--info-color);
            cursor: pointer;
            text-decoration: none;
        }
        
        .my-questions-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            width: 100%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-body {
            flex: 1;
            overflow-y: auto;
            padding: var(--padding-md);
        }
        
        .question-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            font-size: var(--font-size-small);
        }
        
        .question-item:last-child {
            margin-bottom: 0;
        }
        
        .question-item-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .question-item-meta {
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-mini);
        }
        
        @media (max-width: 480px) {
            .question-meta {
                flex-direction: column;
                gap: var(--spacing-sm);
            }
            
            .question-actions {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-md);
            }
            
            .feedback-actions {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-sm);
            }
            
            .submit-question {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-sm);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">客服中心</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 客服中心头部 -->
        <div class="service-header">
            <div class="service-title">客服中心</div>
            <div class="service-desc">智能问答与在线客服</div>
        </div>
        
        <c:if test="${not empty msg}">
            <div class="alert-warning" style="margin: var(--margin-sm) var(--margin-md); padding: var(--padding-md); background: var(--warning-light); border: 1px solid var(--warning-color); border-radius: 8px; color: var(--warning-dark);">
                <strong>警告!</strong> ${msg}
            </div>
        </c:if>
        
        <c:if test="${empty msg}">
            <!-- 问候卡片 -->
            <div class="greeting-card">
                <div class="greeting-user">${username}</div>
                <div class="greeting-text" id="greetingText">您好！</div>
                <div class="greeting-welcome">
                    欢迎来访，请问有什么能够帮助您的吗？
                    <a class="my-questions-link" onclick="showMyQuestions();">>>我的提问</a>
                </div>
            </div>
            
            <!-- 搜索容器 -->
            <div class="search-container">
                <div class="search-input-wrapper">
                    <input type="text" class="search-input" id="questionInput" 
                           placeholder="请输入您的问题..." 
                           oninput="searchSimilarQuestions();" 
                           onfocus="searchSimilarQuestions();" 
                           autocomplete="off">
                    <i class="search-icon ace-icon fa fa-search"></i>
                    
                    <div class="suggestions-list" id="suggestionsList">
                        <!-- 动态加载建议问题 -->
                    </div>
                </div>
            </div>
            
            <!-- 问题详情 -->
            <div class="question-detail" id="questionDetail">
                <!-- 动态加载问题详情 -->
            </div>
        </c:if>
        
        <!-- 我的提问模态框 -->
        <div class="my-questions-modal" id="myQuestionsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">我的提问</div>
                    <button class="modal-close" onclick="closeMyQuestions();">×</button>
                </div>
                <div class="modal-body" id="myQuestionsList">
                    <!-- 动态加载我的提问列表 -->
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentQuestionId = '';
        let searchTimeout = null;

        $(function() {
            initPage();
            setGreeting();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            
            // 点击外部关闭建议列表
            $(document).click(function(e) {
                if (!$(e.target).closest('.search-container').length) {
                    hideSuggestions();
                }
            });
        }

        // 设置问候语
        function setGreeting() {
            const date = new Date();
            const hour = date.getHours();
            let greeting = "";

            if (hour >= 23 || hour < 5) {
                greeting = "夜已经深了，早些休息吧！";
            } else if (hour >= 5 && hour < 8) {
                greeting = "早上好，又是美好的一天。";
            } else if (hour >= 8 && hour < 10) {
                greeting = "精神饱满迎接新的挑战。";
            } else if (hour >= 11 && hour < 12) {
                greeting = "快到中午了，奋斗了一上午是否收获满满。";
            } else if (hour >= 12 && hour < 13) {
                greeting = "中午了，小憩一会。";
            } else if (hour >= 13 && hour < 16) {
                greeting = "抖擞精神，深耕细作。";
            } else if (hour >= 16 && hour < 18) {
                greeting = "马上下班了，还有未做完的工作吗？";
            } else if (hour >= 18 && hour < 19) {
                greeting = "晚饭要吃少，入睡要趁早！";
            } else if (hour >= 19 && hour < 23) {
                greeting = "去睡吧，好梦！";
            }

            $('#greetingText').text(greeting);
        }

        // 搜索相似问题
        function searchSimilarQuestions() {
            const question = $('#questionInput').val().trim();

            // 清除之前的搜索定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            if (question === '') {
                hideSuggestions();
                return;
            }

            // 延迟搜索，避免频繁请求
            searchTimeout = setTimeout(() => {
                showLoading(true);

                $.ajax({
                    url: "/main/customerServiceCenter/querySameQuestion",
                    type: "post",
                    data: { question: question },
                    dataType: "json",
                    success: function(data) {
                        renderSuggestions(data);
                    },
                    error: function(xhr) {
                        showError("搜索失败，请稍后重试");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }, 300);
        }

        // 渲染建议列表
        function renderSuggestions(data) {
            const container = $('#suggestionsList');
            container.empty();

            if (data && data.length > 0) {
                let maxLike = "";
                let tempLike = 0;

                // 找出点赞最多的问题
                data.forEach(item => {
                    if (maxLike === "" || tempLike < item[5]) {
                        maxLike = item[0];
                        tempLike = item[5];
                    }
                });

                data.forEach((item, index) => {
                    const [id, title, viewCount, isNew, , likeCount] = item;

                    // 格式化查看次数
                    let formattedViewCount = viewCount;
                    if (viewCount > 10000) {
                        const wan = viewCount / 10000;
                        formattedViewCount = (wan % 1 === 0 ? wan : wan.toFixed(2)) + "万";
                    }

                    const isRecommended = id === maxLike;

                    const suggestionHtml = `
                        <div class="suggestion-item" onclick="showQuestionDetail('${id}', 1);">
                            <div class="suggestion-text">
                                <span>${title}${isNew > 0 ? '<span class="suggestion-badge new">新</span>' : ''}${isRecommended ? '<span class="suggestion-badge recommend">荐</span>' : ''}</span>
                                <span class="suggestion-stats">${formattedViewCount}查看</span>
                            </div>
                        </div>
                    `;
                    container.append(suggestionHtml);
                });

                showSuggestions();
            } else {
                const noResultHtml = `
                    <div class="suggestion-item" onclick="submitNewQuestion(0);">
                        <div class="suggestion-text">
                            <span>您问的我还不知道呢，点击此处提交我吧！</span>
                        </div>
                    </div>
                `;
                container.html(noResultHtml);
                showSuggestions();
            }
        }

        // 显示建议列表
        function showSuggestions() {
            $('#suggestionsList').show();
            $('.search-input-wrapper').css('border-radius', '25px 25px 0 0');
        }

        // 隐藏建议列表
        function hideSuggestions() {
            $('#suggestionsList').hide();
            $('.search-input-wrapper').css('border-radius', '25px');
        }

        // 显示问题详情
        function showQuestionDetail(questionId, from) {
            hideSuggestions();
            $('#questionDetail').hide();
            currentQuestionId = questionId;

            showLoading(true);

            $.ajax({
                url: "/main/customerServiceCenter/queryQuestionDetail",
                type: "post",
                data: {
                    from: from,
                    questionId: questionId
                },
                dataType: "json",
                success: function(data) {
                    if (data.question) {
                        renderQuestionDetail(data);
                        $('#questionDetail').show();
                    }
                },
                error: function(xhr) {
                    showError("获取问题详情失败");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染问题详情
        function renderQuestionDetail(data) {
            const question = data.question;
            const [id, category, title, content, viewCount, , createTime, , likeCount, userLikeStatus, contactPerson, contactInfo] = question;

            const detailHtml = `
                <div class="question-title">${title}</div>
                <div class="question-meta">
                    <div class="meta-item">
                        <i class="ace-icon fa fa-th-list"></i>
                        <span>${category}</span>
                    </div>
                    <div class="meta-item">
                        <i class="ace-icon fa fa-clock-o"></i>
                        <span>${createTime}</span>
                    </div>
                    <div class="meta-item">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>${viewCount}</span>
                    </div>
                    <div class="meta-item">
                        <i class="ace-icon fa fa-comments"></i>
                        <span>${data.questionEvaluate.length}评论</span>
                    </div>
                </div>
                <div class="question-content">${content.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&').replace(/&quot;/g, '"')}</div>
                <div class="question-actions">
                    <button class="action-btn ${userLikeStatus === '1' ? 'liked' : ''}" onclick="toggleLike(this, true);">
                        <i class="ace-icon fa fa-thumbs-o-up"></i>
                        <span>（${likeCount}）</span>
                    </button>
                    <button class="action-btn ${userLikeStatus === '0' ? 'disliked' : ''}" onclick="toggleLike(this, false);">
                        <i class="ace-icon fa fa-thumbs-o-down"></i>
                        <span>踩</span>
                    </button>
                </div>
                <div class="feedback-section">
                    <div class="feedback-title">是否解决了您的疑惑，请留下您的宝贵意见~</div>
                    <textarea class="feedback-textarea" id="feedbackText" placeholder="请输入您的意见..."></textarea>
                    <div class="feedback-actions">
                        <label class="anonymous-checkbox">
                            <input type="checkbox" id="anonymousCheck">
                            <span>匿名评价</span>
                        </label>
                        <button class="btn-submit-feedback" onclick="submitFeedback();">
                            <i class="ace-icon fa fa-edit"></i>
                            <span>评价</span>
                        </button>
                    </div>
                </div>
                <div class="contact-section">
                    <div class="contact-title">如未解决：</div>
                    <div class="contact-info">1、请与${contactPerson}联系，联系方式（${contactInfo}）</div>
                    <div class="submit-question">
                        <span>2、</span>
                        <input type="text" class="submit-input" id="newQuestionInput" value="${$('#questionInput').val()}" placeholder="请输入您的问题">
                        <a class="submit-link" onclick="submitNewQuestion(1);">提交</a>
                        <span>此问题！</span>
                    </div>
                </div>
            `;

            $('#questionDetail').html(detailHtml);
        }

        // 切换点赞/踩
        function toggleLike(button, isLike) {
            if ($(button).hasClass('liked') || $(button).hasClass('disliked')) {
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/main/customerServiceCenter/showMyLike",
                type: "post",
                data: {
                    questionId: currentQuestionId,
                    flag: isLike ? "1" : "0"
                },
                success: function() {
                    showQuestionDetail(currentQuestionId, 0);
                },
                error: function(xhr) {
                    showError("操作失败");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 提交反馈
        function submitFeedback() {
            const feedback = $('#feedbackText').val().trim();
            if (feedback === '') {
                showError('请填写意见！');
                return;
            }

            // 检查字符长度（中文算2个字符）
            let length = feedback.length;
            for (let i = 0; i < feedback.length; i++) {
                if (feedback.charCodeAt(i) > 19967) {
                    length++;
                }
            }

            if (length > 500) {
                showError('意见最大长度不能超过500个字符，一个汉字为两个字符！');
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/main/customerServiceCenter/giveYourIdea",
                type: "post",
                data: {
                    questionId: currentQuestionId,
                    nmpj: $('#anonymousCheck').is(':checked') ? "1" : "0",
                    ideaCont: feedback
                },
                success: function() {
                    showSuccess('已评价！');
                    showQuestionDetail(currentQuestionId, 0);
                },
                error: function(xhr) {
                    showError("评价失败");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 提交新问题
        function submitNewQuestion(flag) {
            let question = '';
            if (flag === 0) {
                question = $('#questionInput').val().trim();
            } else {
                question = $('#newQuestionInput').val().trim();
            }

            if (question === '') {
                showError('请填写您的问题！');
                return;
            }

            // 检查字符长度
            let length = question.length;
            for (let i = 0; i < question.length; i++) {
                if (question.charCodeAt(i) > 19967) {
                    length++;
                }
            }

            if (length > 200) {
                showError('问题最大长度不能超过200个字符，一个汉字为两个字符！');
                return;
            }

            if (confirm(`是否提交问题：\n${question}`)) {
                showLoading(true);

                $.ajax({
                    url: "/main/customerServiceCenter/reportMyQuestion",
                    type: "post",
                    data: { myQuestion: question },
                    success: function() {
                        showSuccess('已提交！');
                        $('#questionInput').val('');
                        $('#newQuestionInput').val('');
                        hideSuggestions();
                    },
                    error: function(xhr) {
                        showError("提交失败");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 显示我的提问
        function showMyQuestions() {
            showLoading(true);

            $.ajax({
                url: "/main/customerServiceCenter/viewMyQuestion",
                type: "post",
                data: "pageNum=1&pageSize=30",
                dataType: "json",
                success: function(data) {
                    if (data.data && data.data.records) {
                        renderMyQuestions(data.data.records);
                    } else {
                        $('#myQuestionsList').html('<div class="question-item"><div class="question-item-title">暂无提问记录</div></div>');
                    }
                    $('#myQuestionsModal').show();
                },
                error: function(xhr) {
                    showError("获取提问记录失败");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染我的提问列表
        function renderMyQuestions(questions) {
            const container = $('#myQuestionsList');
            container.empty();

            if (questions.length === 0) {
                container.html('<div class="question-item"><div class="question-item-title">暂无提问记录</div></div>');
                return;
            }

            questions.forEach((question, index) => {
                const questionHtml = `
                    <div class="question-item">
                        <div class="question-item-title">${question.WTNR || '未知问题'}</div>
                        <div class="question-item-meta">
                            <span>${question.TJSJ || '-'}</span>
                            <span>${question.SFRK === '1' ? '已入库' : '未入库'}</span>
                        </div>
                    </div>
                `;
                container.append(questionHtml);
            });
        }

        // 关闭我的提问模态框
        function closeMyQuestions() {
            $('#myQuestionsModal').hide();
        }

        // 刷新数据
        function refreshData() {
            $('#questionInput').val('');
            $('#questionDetail').hide();
            hideSuggestions();
            setGreeting();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
