<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="GROUP_BY_SEVERITY" value="true" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="31185448-0471-4d80-ace4-c6f06b5d1664" name="Default" comment="修改参数获取方式">
      <change beforePath="$PROJECT_DIR$/.project" beforeDir="false" afterPath="$PROJECT_DIR$/.project" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.settings/org.eclipse.core.resources.prefs" beforeDir="false" afterPath="$PROJECT_DIR$/.settings/org.eclipse.core.resources.prefs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/urpSoft/core/license/LicenseManger.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/urpSoft/core/license/LicenseManger.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/urpSoft/core/security/springSecurity/metadataSource/metadataSourceProvider/UrpFilterInvocationSecurityMetadataSourceProvider.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/urpSoft/core/security/springSecurity/metadataSource/metadataSourceProvider/UrpFilterInvocationSecurityMetadataSourceProvider.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/urpSoft/core/security/springSecurity/web/filter/MPasswordEncoder.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/urpSoft/core/security/springSecurity/web/filter/MPasswordEncoder.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/urpSoft/core/security/springSecurity/web/handler/impl/LoginSuccessHandlerImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/urpSoft/core/security/springSecurity/web/handler/impl/LoginSuccessHandlerImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/educationalAdministration/system/captcha/filter/CaptchaFilter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/educationalAdministration/system/captcha/filter/CaptchaFilter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/applicationContext.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/applicationContext.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/hostWhiteList.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/hostWhiteList.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/jdbc.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/jdbc.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/redis.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/redis.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/system.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/system.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/mobileindex.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/mobileindex.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/urpSoft/WEB-INF/reportConfig.xml" beforeDir="false" afterPath="$PROJECT_DIR$/urpSoft/WEB-INF/reportConfig.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/urpSoft/WEB-INF/web.xml" beforeDir="false" afterPath="$PROJECT_DIR$/urpSoft/WEB-INF/web.xml" afterDir="false" />
    </list>
    <list id="eb21f0ea-7f19-4e6d-9e33-64697086e421" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Html5" />
        <option value="Class" />
        <option value="Jsp File" />
      </list>
    </option>
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="D:/maven/apache-maven-3.0.5" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.0.5\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="#JAVA_HOME" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedIndex" value="2" />
  </component>
  <component name="ProjectId" id="1rpmBHN8MIkmeJZjRQq3vBldSYW" />
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id />
          </State>
          <State>
            <id>Gradle</id>
          </State>
          <State>
            <id>Java</id>
          </State>
          <State>
            <id>Maven</id>
          </State>
          <State>
            <id>OSGi</id>
          </State>
          <State>
            <id>Probable bugsGradle</id>
          </State>
          <State>
            <id>Serialization issuesJava</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>SerializableHasSerialVersionUIDField</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="DefaultHtmlFileTemplate" value="Html5" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="SearchEverywhereHistoryKey" value="basedao&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0_branches_20200609/urpSoft6.0/src/main/java/com/urpSoft/core/data/orm/dao/impl/BaseDao.java&#10;/student/integratedQuery/planCompletion/index&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0_branches_20200609/urpSoft6.0/urpSoft/WEB-INF/jsp/student/integratedQuery/planCompletion/index.jsp&#10;student/teachingResources/freeClassroomQuery/today&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/teachingResources/freeClassroomQuery/today.jsp&#10;student/teachingResources/teacherCurriculum/index&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/teachingResources/teacherCurriculum/index.jsp&#10;/student/integratedQuery/scoreQuery/coursePropertyScores/index&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/coursePropertyScores/index.jsp&#10;/student/integratedQuery/scoreQuery/allPassingScores/index&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/allPassingScores/index.jsp&#10;/student/integratedQuery/scoreQuery/schemeScores/index&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/integratedQuery/scoreQuery/schemeScores/index.jsp&#10;/student/integratedQuery/course/courseBasicInformation/basicInf&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/integratedQuery/course/courseBasicInformation/basicInf.jsp&#10;student/integratedQuery/planCompletion/index&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/integratedQuery/planCompletion/index.jsp&#10;jdbc&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/src/main/resources/jdbc.properties&#10;/student/personalManagement/individualApplication/dropCourseApplication/index&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/dropCourseApplication/index.jsp&#10;/login&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/system/admin/auth/login.jsp&#10;tem&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/template/template.jsp&#10;/login.jsp&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/system/admin/auth/login.jsp&#10;login&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/system/admin/auth/login.jsp&#10;/student/personalManagement/personalInfoUpdate/xjInfo&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/personalInfoUpdate/xjInfo.jsp&#10;/student/personalManagement/personalInfoUpdate/modifyPass&#9;FILE&#9;file://D:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/personalInfoUpdate/modifyPass.jsp&#10;XsXjydsqb&#9;PSI&#9;JAVA://educationalAdministration.student.personalManagement.entity.XsXjydsqb&#10;SpecialtiesApplyServiceImpl&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/src/main/java/educationalAdministration/student/personalManagement/service/impl/SpecialtiesApplyServiceImpl.java&#10;CaptchaFilter&#9;PSI&#9;JAVA://educationalAdministration.system.captcha.filter.CaptchaFilter&#10;/assets/js/jquery.validate.min.js&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/assets/js/jquery.validate.min.js&#10;WEB-INF/jsp/student/personalManagement/studentChange/specialtiesApply.jsp&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/specialtiesApply.jsp&#10;educationalAdministration.student.personalManagement.controller.SpecialtiesApplyController&#9;PSI&#9;JAVA://educationalAdministration.student.personalManagement.controller.SpecialtiesApplyController&#10;educationalAdministration.dictionary.entity.XsZzySqb&#9;PSI&#9;JAVA://educationalAdministration.dictionary.entity.XsZzySqb&#10;student/courseTableOfThisSemester/index&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/courseTableOfThisSemester/index.jsp&#10;jquery&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/assets/js/jquery.validate.methods.js&#10;/student/calendarSemesterCurriculum/index&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/calendarSemesterCurriculum/index.jsp&#10;/student/personalManagement/individualApplication/entranceRegistrationForm/index&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/entranceRegistrationForm/index.jsp&#10;jqu&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/assets/js/jquery.validate.methods.js&#10;/student/personalManagement/studentChange/add&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/add.jsp&#10;RedisCacheTool&#9;PSI&#9;JAVA://com.urpSoft.core.cache.redis.RedisCacheTool&#10;educationalAdministration.student.personalManagement.service.impl.SpecialtiesApplyServiceImpl&#9;PSI&#9;JAVA://educationalAdministration.student.personalManagement.service.impl.SpecialtiesApplyServiceImpl&#10;SpecialtiesApplyController&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/src/main/java/educationalAdministration/student/personalManagement/controller/SpecialtiesApplyController.java&#10;urprol&#9;PSI&#9;JAVA://com.urpSoft.core.security.enums.UrpRole&#10;/student/examinationManagement/examPlan/index&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/examinationManagement/examPlan/index.jsp&#10;student/personalManagement/individualApplication/exemptionApplication/editInfo&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/exemptionApplication/editInfo.jsp&#10;student/personalManagement/individualApplication/dropCourseApplication/editInfo&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/dropCourseApplication/editInfo.jsp&#10;jquery.v&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/assets/js/jquery.validate.methods.js&#10;commonu&#9;PSI&#9;JAVA://educationalAdministration.student.common.utils.CommonUtils&#10;/student/integratedQuery/instructionPlanQuery/detail/index&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/integratedQuery/instructionPlanQuery/detail/index.jsp&#10;/student/personalManagement/studentChange/xjyddy&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/studentChange/xjyddy.jsp&#10;/student/personalManagement/individualApplication/approval&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/approval.jsp&#10;student/personalManagement/individualApplication/entranceRegistrationForm/index&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/individualApplication/entranceRegistrationForm/index.jsp&#10;UrpRole&#9;PSI&#9;JAVA://com.urpSoft.core.security.enums.UrpRole&#10;XkHksqb&#9;PSI&#9;JAVA://educationalAdministration.dictionary.entity.XkHksqb&#10;/student/personalManagement/rollInfo/xjInfo&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/rollInfo/xjInfo.jsp&#10;XsJcb&#9;PSI&#9;JAVA://educationalAdministration.student.personalManagement.entity.XsJcb&#10;Us&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/src/main/java/educationalAdministration/system/admin/entity/user/User.java&#10;user&#9;PSI&#9;JAVA://educationalAdministration.system.admin.entity.user.User&#10;/student/personalManagement/myRollCard/index&#9;FILE&#9;file://E:/IdeaWorks/newjw2.0/urpSoft6.0/urpSoft/WEB-INF/jsp/student/personalManagement/myRollCard/index.jsp" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/urpSoft/WEB-INF/jsp/student/thesis/stageddocuments/uploadsub" />
    <property name="project.structure.last.edited" value="Project" />
    <property name="project.structure.proportion" value="0.15429688" />
    <property name="project.structure.side.proportion" value="0.2" />
    <property name="settings.editor.selected.configurable" value="reference.projectsettings.compiler.javacompiler" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Workspaces\idea2020\branches\urpSoft60_branches\urpSoft\WEB-INF\jsp\student\thesis\stageddocuments\uploadsub" />
      <recent name="C:\Workspaces\idea2020\branches\urpSoft60_branches\urpSoft\WEB-INF\jsp\student\thesis" />
      <recent name="C:\Workspaces\idea2020\branches\urpSoft60_branches\src\main\java\educationalAdministration\student\thesis" />
      <recent name="C:\Workspaces\idea2020\branches\urpSoft60_branches\urpSoft\WEB-INF\assets\iconfont" />
      <recent name="C:\Workspaces\idea2020\branches\urpSoft60_branches\urpSoft\WEB-INF\jsp\student\innovationCredits\innovationProject" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\IdeaWorks\newjw2.0\urpSoft6.0\urpSoft\WEB-INF\jsp\report" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="educationalAdministration.student.personalManagement.entity" />
      <recent name="educationalAdministration.student.myAttention.entity" />
      <recent name="educationalAdministration.student.individualApplication.baccalaureateApplication.entity" />
      <recent name="educationalAdministration.student.individualApplication.baccalaureateApplication.controller" />
      <recent name="com.urpSoft.core.security.springSecurity.web.filter" />
    </key>
  </component>
  <component name="RunManager" selected="Resin.Resin 4.0.55">
    <configuration default="true" type="AndroidRunConfigurationType" factoryName="Android Application">
      <module name="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="DEPLOY" value="true" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="TARGET_SELECTION_MODE" value="EMULATOR" />
      <option name="USE_LAST_SELECTED_DEVICE" value="false" />
      <option name="PREFERRED_AVD" value="" />
      <option name="USE_COMMAND_LINE" value="true" />
      <option name="COMMAND_LINE" value="" />
      <option name="WIPE_USER_DATA" value="false" />
      <option name="DISABLE_BOOT_ANIMATION" value="false" />
      <option name="NETWORK_SPEED" value="full" />
      <option name="NETWORK_LATENCY" value="none" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="true" />
      <option name="FILTER_LOGCAT_AUTOMATICALLY" value="true" />
      <method />
    </configuration>
    <configuration default="true" type="AndroidTestRunConfigurationType" factoryName="Android Tests">
      <module name="" />
      <option name="TESTING_TYPE" value="0" />
      <option name="INSTRUMENTATION_RUNNER_CLASS" value="" />
      <option name="METHOD_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="PACKAGE_NAME" value="" />
      <option name="TARGET_SELECTION_MODE" value="EMULATOR" />
      <option name="USE_LAST_SELECTED_DEVICE" value="false" />
      <option name="PREFERRED_AVD" value="" />
      <option name="USE_COMMAND_LINE" value="true" />
      <option name="COMMAND_LINE" value="" />
      <option name="WIPE_USER_DATA" value="false" />
      <option name="DISABLE_BOOT_ANIMATION" value="false" />
      <option name="NETWORK_SPEED" value="full" />
      <option name="NETWORK_LATENCY" value="none" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="true" />
      <option name="FILTER_LOGCAT_AUTOMATICALLY" value="true" />
      <method />
    </configuration>
    <configuration default="true" type="Applet" factoryName="Applet">
      <option name="WIDTH" value="400" />
      <option name="HEIGHT" value="300" />
      <option name="POLICY_FILE" value="$APPLICATION_HOME_DIR$/bin/appletviewer.policy" />
      <module />
      <method />
    </configuration>
    <configuration default="true" type="ArquillianJUnit" factoryName="" nameIsGenerated="true">
      <option name="arquillianRunConfiguration">
        <value>
          <option name="containerStateName" value="" />
        </value>
      </option>
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="FlashRunConfigurationType" factoryName="Flash App">
      <option name="BCName" value="" />
      <option name="IOSSimulatorSdkPath" value="" />
      <option name="adlOptions" value="" />
      <option name="airProgramParameters" value="" />
      <option name="appDescriptorForEmulator" value="Android" />
      <option name="debugTransport" value="USB" />
      <option name="debuggerSdkRaw" value="BC SDK" />
      <option name="emulator" value="NexusOne" />
      <option name="emulatorAdlOptions" value="" />
      <option name="fastPackaging" value="true" />
      <option name="fullScreenHeight" value="0" />
      <option name="fullScreenWidth" value="0" />
      <option name="launchUrl" value="false" />
      <option name="launcherParameters">
        <LauncherParameters>
          <option name="browser" value="a7bb68e0-33c0-4d6f-a81a-aac1fdb870c8" />
          <option name="launcherType" value="OSDefault" />
          <option name="newPlayerInstance" value="false" />
          <option name="playerPath" value="FlashPlayerDebugger.exe" />
        </LauncherParameters>
      </option>
      <option name="mobileRunTarget" value="Emulator" />
      <option name="moduleName" value="" />
      <option name="overriddenMainClass" value="" />
      <option name="overriddenOutputFileName" value="" />
      <option name="overrideMainClass" value="false" />
      <option name="runTrusted" value="true" />
      <option name="screenDpi" value="0" />
      <option name="screenHeight" value="0" />
      <option name="screenWidth" value="0" />
      <option name="url" value="http://" />
      <option name="usbDebugPort" value="7936" />
      <method />
    </configuration>
    <configuration default="true" type="FlexUnitRunConfigurationType" factoryName="FlexUnit" appDescriptorForEmulator="Android" class_name="" emulatorAdlOptions="" method_name="" package_name="" scope="Class">
      <option name="BCName" value="" />
      <option name="launcherParameters">
        <LauncherParameters>
          <option name="browser" value="a7bb68e0-33c0-4d6f-a81a-aac1fdb870c8" />
          <option name="launcherType" value="OSDefault" />
          <option name="newPlayerInstance" value="false" />
          <option name="playerPath" value="FlashPlayerDebugger.exe" />
        </LauncherParameters>
      </option>
      <option name="moduleName" value="" />
      <option name="trusted" value="true" />
      <method />
    </configuration>
    <configuration default="true" type="GrailsRunConfigurationType" factoryName="Grails">
      <setting name="vmparams" value="" />
      <setting name="cmdLine" value="run-app" />
      <setting name="passParentEnv" value="true" />
      <setting name="grailsApplicationRoot" value="" />
      <setting name="launchBrowser" value="false" />
      <setting name="launchBrowserUrl" value="" />
      <setting name="depsClasspath" value="false" />
      <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="osgi.bnd.run" factoryName="Run Launcher">
      <method />
    </configuration>
    <configuration default="true" type="osgi.bnd.run" factoryName="Test Launcher (JUnit)">
      <method />
    </configuration>
    <configuration name="MD5Util" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.urpSoft.core.util.MD5Util" />
      <module name="UrpSoft6.0_branches" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.urpSoft.core.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="CucumberJavaRunConfigurationType" factoryName="Cucumber java">
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <option name="TEST_OBJECT" value="class" />
      <option name="WORKING_DIRECTORY" value="$MODULE_DIR$" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Resin 4.0.55" type="ResinConfigurationType" factoryName="Local" APPLICATION_SERVER_NAME="D:Resin 4.0.55" ALTERNATIVE_JRE_ENABLED="false">
      <option name="PORT" value="80" />
      <option name="COMMON_VM_ARGUMENTS" value="-Xms1024m -Xmx2048m -XX:PermSize=256m -XX:MaxPermSize=1024m" />
      <option name="UPDATING_POLICY" value="redeploy-artifacts" />
      <deployment>
        <file path="$PROJECT_DIR$/urpSoft">
          <settings DEPLOYMENT_METHOD="resin.xml">
            <option name="defaultContextPath" value="false" />
          </settings>
        </file>
      </deployment>
      <server-settings>
        <option name="additionalParameters" value="-verbose -server app-0 console" />
        <option name="baseDirectoryName" value="Resin_4_0_55_UrpSoft6_0_branches" />
        <option name="jmxPort" value="9995" />
        <option name="port" value="8180" />
        <port>8180</port>
        <mbean-port>9995</mbean-port>
      </server-settings>
      <ConfigurationWrapper VM_VAR="" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="" RunnerId="JRebel Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="" RunnerId="JRebel Executor">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="TestNG">
      <option name="TEST_OBJECT" value="CLASS" />
      <option name="VM_PARAMETERS" />
      <option name="WORKING_DIRECTORY" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="#org.jetbrains.idea.devkit.run.PluginConfigurationType">
      <module name="" />
      <option name="VM_PARAMETERS" value="-Xmx512m -Xms256m -XX:MaxPermSize=250m -ea" />
      <option name="PROGRAM_PARAMETERS" />
      <predefined_log_file enabled="true" id="idea.log" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="js.build_tools.gulp">
      <node-interpreter>project</node-interpreter>
      <node-options />
      <gulpfile />
      <tasks />
      <arguments />
      <pass-parent-envs>true</pass-parent-envs>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Application.MD5Util" />
      <item itemvalue="Resin.Resin 4.0.55" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.MD5Util" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" myAutoUpdateAfterCommit="true" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
    <option name="UPDATE_DEPTH" value="infinity" />
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Workspaces\idea2020\branches\urpSoft60_branches" />
          <option name="myCopyRoot" value="C:\Workspaces\idea2020\branches\urpSoft60_branches" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Workspaces\idea2020\branches\urpSoft60_branches" />
          <option name="myCopyRoot" value="C:\Workspaces\idea2020\branches\urpSoft60_branches" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="31185448-0471-4d80-ace4-c6f06b5d1664" name="Default" comment="" />
      <created>1534233991507</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1534233991507</updated>
      <workItem from="1534233996310" duration="40948000" />
      <workItem from="1534728626639" duration="4380000" />
      <workItem from="1534823316510" duration="8007000" />
      <workItem from="1534904273895" duration="2058000" />
      <workItem from="1534907415506" duration="3003000" />
      <workItem from="1535010518094" duration="1284000" />
      <workItem from="1536107241487" duration="798000" />
      <workItem from="1536200373008" duration="1625000" />
      <workItem from="1539305383712" duration="5769000" />
      <workItem from="1539996141421" duration="13541000" />
      <workItem from="1552355743771" duration="1555000" />
      <workItem from="1552457628290" duration="16941000" />
      <workItem from="1552520791165" duration="28212000" />
      <workItem from="1552555879247" duration="15994000" />
      <workItem from="1552892063994" duration="13698000" />
      <workItem from="1554096944652" duration="959000" />
      <workItem from="1554098474429" duration="6962000" />
      <workItem from="1554106474928" duration="7343000" />
      <workItem from="1554176242079" duration="5338000" />
      <workItem from="1554345673443" duration="1787000" />
      <workItem from="1556504470163" duration="13116000" />
      <workItem from="1557123481298" duration="1022000" />
      <workItem from="1557726898017" duration="1184000" />
      <workItem from="1557747598903" duration="1865000" />
      <workItem from="1557791615175" duration="9598000" />
      <workItem from="1557978424618" duration="1493000" />
      <workItem from="1558321058149" duration="631000" />
      <workItem from="1558659173217" duration="5795000" />
      <workItem from="1558773366668" duration="598000" />
      <workItem from="1559724264245" duration="1567000" />
      <workItem from="1559779148485" duration="2543000" />
      <workItem from="1560989224440" duration="546000" />
      <workItem from="1560993945687" duration="71000" />
      <workItem from="1560999291810" duration="615000" />
      <workItem from="1561617669853" duration="733000" />
      <workItem from="1561689593865" duration="1384000" />
      <workItem from="1561976874992" duration="1116000" />
      <workItem from="1561979320263" duration="3512000" />
      <workItem from="1562064504756" duration="2500000" />
      <workItem from="1562138691836" duration="3267000" />
      <workItem from="1562222546928" duration="3453000" />
      <workItem from="1562565136519" duration="1471000" />
      <workItem from="1562737202343" duration="35000" />
      <workItem from="1563248583482" duration="3368000" />
      <workItem from="1563450650202" duration="5673000" />
      <workItem from="1563582787215" duration="4239000" />
      <workItem from="1563932066885" duration="684000" />
      <workItem from="1563952230789" duration="155000" />
      <workItem from="1563953561070" duration="13308000" />
      <workItem from="1564104996149" duration="5654000" />
      <workItem from="1564358927564" duration="1113000" />
      <workItem from="1564469747759" duration="1138000" />
      <workItem from="1564473039169" duration="3152000" />
      <workItem from="1564712686669" duration="34000" />
      <workItem from="1564792685257" duration="1565000" />
      <workItem from="1564992898539" duration="21719000" />
      <workItem from="1565312140569" duration="62000" />
      <workItem from="1565319810156" duration="7480000" />
      <workItem from="1565849092785" duration="2230000" />
      <workItem from="1565886777790" duration="710000" />
      <workItem from="1566195599377" duration="6703000" />
      <workItem from="1566559176276" duration="1280000" />
      <workItem from="1566613116299" duration="3631000" />
      <workItem from="1566635047627" duration="2163000" />
      <workItem from="1566971209351" duration="5003000" />
      <workItem from="1567045780947" duration="7183000" />
      <workItem from="1567404953924" duration="1151000" />
      <workItem from="1567408528887" duration="2858000" />
      <workItem from="1567413351419" duration="1419000" />
      <workItem from="1568168673951" duration="4641000" />
      <workItem from="1568685818389" duration="8712000" />
      <workItem from="1569028270262" duration="595000" />
      <workItem from="1569031786330" duration="2888000" />
      <workItem from="1569225783424" duration="1657000" />
      <workItem from="1569373669537" duration="1833000" />
      <workItem from="1569564237285" duration="1988000" />
      <workItem from="1569718049436" duration="1761000" />
      <workItem from="1570516709149" duration="2655000" />
      <workItem from="1570581006147" duration="2202000" />
      <workItem from="1570610669243" duration="671000" />
      <workItem from="1570688596026" duration="3522000" />
      <workItem from="1570753793404" duration="732000" />
      <workItem from="1570854505988" duration="1243000" />
      <workItem from="1571020580924" duration="2770000" />
      <workItem from="1571027797897" duration="2540000" />
      <workItem from="1571275458480" duration="1835000" />
      <workItem from="1571361096392" duration="128000" />
      <workItem from="1571368251496" duration="2475000" />
      <workItem from="1571446313571" duration="836000" />
      <workItem from="1571450610341" duration="688000" />
      <workItem from="1571798776882" duration="2273000" />
      <workItem from="1571990761929" duration="30000" />
      <workItem from="1571990845801" duration="870000" />
      <workItem from="1572230754431" duration="340000" />
      <workItem from="1572414954151" duration="5633000" />
      <workItem from="1572502664454" duration="853000" />
      <workItem from="1572578552158" duration="1195000" />
      <workItem from="1572835863856" duration="1590000" />
      <workItem from="1573867162707" duration="6166000" />
      <workItem from="1573902729233" duration="632000" />
      <workItem from="1574726526599" duration="2130000" />
      <workItem from="1574938225072" duration="277000" />
      <workItem from="1575270041797" duration="5683000" />
      <workItem from="1575331427090" duration="3789000" />
      <workItem from="1575338849509" duration="8000000" />
      <workItem from="1575441751951" duration="5833000" />
      <workItem from="1575590126945" duration="1423000" />
      <workItem from="1575593112900" duration="1262000" />
      <workItem from="1575678119417" duration="626000" />
      <workItem from="1575968948315" duration="677000" />
      <workItem from="1576132975116" duration="232000" />
      <workItem from="1576134552848" duration="693000" />
      <workItem from="1576198714891" duration="668000" />
      <workItem from="1590114273950" duration="1029000" />
      <workItem from="1590116924029" duration="4015000" />
      <workItem from="1591921867255" duration="5189000" />
      <workItem from="1592292726480" duration="4355000" />
      <workItem from="1593998413297" duration="1981000" />
      <workItem from="1594708057489" duration="1517000" />
      <workItem from="1595132602157" duration="1129000" />
      <workItem from="1595140771799" duration="885000" />
      <workItem from="1595209815767" duration="822000" />
      <workItem from="1619681272369" duration="676000" />
      <workItem from="1650591168511" duration="52000" />
      <workItem from="1652324754749" duration="2638000" />
      <workItem from="1652659097354" duration="2676000" />
      <workItem from="1652931470888" duration="3874000" />
      <workItem from="1654755516424" duration="36000" />
      <workItem from="1655194827758" duration="1557000" />
      <workItem from="1655272859543" duration="1405000" />
      <workItem from="1655350136229" duration="1538000" />
      <workItem from="1655692230042" duration="4167000" />
      <workItem from="1655716979642" duration="273000" />
      <workItem from="1656309485799" duration="788000" />
      <workItem from="1656640792708" duration="1715000" />
      <workItem from="1657768048153" duration="1120000" />
      <workItem from="1658718756011" duration="591000" />
      <workItem from="1660534549631" duration="247000" />
      <workItem from="1661906977328" duration="817000" />
      <workItem from="1662429104005" duration="2143000" />
      <workItem from="1662600812500" duration="653000" />
      <workItem from="1662601493286" duration="748000" />
      <workItem from="1662604021549" duration="9938000" />
      <workItem from="1663122782094" duration="7524000" />
      <workItem from="1663209937886" duration="3863000" />
      <workItem from="1663286386770" duration="5374000" />
      <workItem from="1663294246924" duration="6103000" />
      <workItem from="1663550487404" duration="486000" />
      <workItem from="1663554916209" duration="503000" />
      <workItem from="1663571288695" duration="753000" />
      <workItem from="1663573110844" duration="2166000" />
      <workItem from="1664265705440" duration="407000" />
      <workItem from="1664438701729" duration="4826000" />
      <workItem from="1665207754563" duration="12186000" />
      <workItem from="1665272418923" duration="3646000" />
      <workItem from="1665969189883" duration="1022000" />
      <workItem from="1666255403703" duration="1711000" />
      <workItem from="1666313835876" duration="2295000" />
      <workItem from="1666753576015" duration="1142000" />
      <workItem from="1666943020301" duration="1075000" />
      <workItem from="1667183173324" duration="1269000" />
      <workItem from="1667271824012" duration="1467000" />
      <workItem from="1667288297867" duration="1649000" />
      <workItem from="1667347789727" duration="7101000" />
      <workItem from="1667446447720" duration="8208000" />
      <workItem from="1667618945717" duration="1195000" />
      <workItem from="1668409949313" duration="3516000" />
      <workItem from="1668581853501" duration="650000" />
      <workItem from="1668820384336" duration="4896000" />
      <workItem from="1669765158903" duration="6066000" />
      <workItem from="1669856687200" duration="1091000" />
      <workItem from="1670285467147" duration="24680000" />
      <workItem from="1670369188765" duration="6133000" />
      <workItem from="1673428929896" duration="1022000" />
      <workItem from="1676964602212" duration="909000" />
      <workItem from="1679040936106" duration="625000" />
      <workItem from="1681779887673" duration="701000" />
      <workItem from="1681987455232" duration="622000" />
      <workItem from="1682037962341" duration="1870000" />
      <workItem from="1682414301224" duration="2214000" />
      <workItem from="1684977920463" duration="669000" />
      <workItem from="1685148770456" duration="1896000" />
      <workItem from="1685516351111" duration="3393000" />
      <workItem from="1685520096154" duration="2923000" />
      <workItem from="1685601043518" duration="9188000" />
      <workItem from="1685664277972" duration="22085000" />
      <workItem from="1685752496953" duration="12836000" />
      <workItem from="1685772288590" duration="11000" />
      <workItem from="1685772327435" duration="6880000" />
      <workItem from="1685925216964" duration="11964000" />
      <workItem from="1686010478625" duration="2980000" />
      <workItem from="1686032455276" duration="1013000" />
      <workItem from="1686096623740" duration="2102000" />
      <workItem from="1686635644410" duration="1926000" />
      <workItem from="1686705974058" duration="2013000" />
      <workItem from="1686729611364" duration="2898000" />
      <workItem from="1686794351539" duration="4344000" />
      <workItem from="1686906998278" duration="483000" />
      <workItem from="1686962504046" duration="2795000" />
      <workItem from="1686982509615" duration="653000" />
      <workItem from="1687316778943" duration="1937000" />
      <workItem from="1687834196935" duration="1389000" />
      <workItem from="1688115760378" duration="138000" />
      <workItem from="1688174487573" duration="45000" />
      <workItem from="1688176884404" duration="4191000" />
      <workItem from="1688960475121" duration="1373000" />
      <workItem from="1690333650041" duration="1548000" />
      <workItem from="1690340257330" duration="12806000" />
      <workItem from="1690524216811" duration="609000" />
      <workItem from="1690534906760" duration="1463000" />
      <workItem from="1690761085630" duration="5453000" />
      <workItem from="1691113237655" duration="2887000" />
      <workItem from="1691737755244" duration="4914000" />
      <workItem from="1691974156966" duration="1319000" />
      <workItem from="1693358706175" duration="1948000" />
      <workItem from="1693443848143" duration="886000" />
      <workItem from="1695091193495" duration="515000" />
      <workItem from="1695259132703" duration="781000" />
      <workItem from="1697076805666" duration="798000" />
      <workItem from="1697867221660" duration="2567000" />
      <workItem from="1698115066226" duration="1966000" />
      <workItem from="1698196450465" duration="1511000" />
      <workItem from="1698277254602" duration="812000" />
      <workItem from="1698299893241" duration="7050000" />
      <workItem from="1698367312415" duration="8398000" />
      <workItem from="1698639622695" duration="3195000" />
      <workItem from="1698646137223" duration="5718000" />
      <workItem from="1698655681471" duration="2595000" />
      <workItem from="1698800592501" duration="6867000" />
      <workItem from="1699085355536" duration="1365000" />
      <workItem from="1699231958490" duration="6876000" />
      <workItem from="1699585192740" duration="16257000" />
      <workItem from="1699661265182" duration="11253000" />
      <workItem from="1699691047570" duration="1173000" />
      <workItem from="1699843643116" duration="12899000" />
      <workItem from="1699922083780" duration="6352000" />
      <workItem from="1699933279449" duration="2202000" />
      <workItem from="1699947010869" duration="1265000" />
      <workItem from="1700013978439" duration="790000" />
      <workItem from="1700028733564" duration="1553000" />
      <workItem from="1700093279494" duration="1789000" />
      <workItem from="1700120026347" duration="2683000" />
      <workItem from="1701155977777" duration="2820000" />
      <workItem from="1701393565586" duration="304000" />
      <workItem from="1702100747695" duration="3151000" />
      <workItem from="1702285847884" duration="1042000" />
      <workItem from="1703492449137" duration="1616000" />
      <workItem from="1704696666639" duration="891000" />
      <workItem from="1704787028274" duration="5065000" />
      <workItem from="1704848368610" duration="2043000" />
      <workItem from="1704850452044" duration="4071000" />
      <workItem from="1705281079501" duration="4234000" />
      <workItem from="1705557986651" duration="113000" />
      <workItem from="1706085388325" duration="1199000" />
      <workItem from="1708917994611" duration="2361000" />
      <workItem from="1709082890622" duration="2685000" />
      <workItem from="1709519703202" duration="1782000" />
      <workItem from="1709716757414" duration="160000" />
      <workItem from="1709793209912" duration="2834000" />
      <workItem from="1709796610670" duration="1477000" />
      <workItem from="1710144455354" duration="2662000" />
      <workItem from="1710292569839" duration="3555000" />
      <workItem from="1710721349083" duration="284000" />
      <workItem from="1710722077334" duration="2096000" />
      <workItem from="1710894464085" duration="682000" />
      <workItem from="1710987307371" duration="680000" />
      <workItem from="1711435702088" duration="5804000" />
      <workItem from="1711505324798" duration="2697000" />
      <workItem from="1711682417056" duration="4370000" />
      <workItem from="1712020868289" duration="155000" />
      <workItem from="1712041554776" duration="1259000" />
      <workItem from="1712124151244" duration="7314000" />
      <workItem from="1712544354025" duration="806000" />
      <workItem from="1713149817528" duration="3717000" />
      <workItem from="1713163159424" duration="4878000" />
      <workItem from="1713250094438" duration="2328000" />
      <workItem from="1713943970406" duration="2757000" />
      <workItem from="1714018300685" duration="2055000" />
      <workItem from="1714380642287" duration="74000" />
      <workItem from="1715329556511" duration="2359000" />
      <workItem from="1717653286465" duration="18524000" />
      <workItem from="1717719133498" duration="16709000" />
      <workItem from="1718258495867" duration="2330000" />
      <workItem from="1718265273763" duration="1850000" />
      <workItem from="1718332315965" duration="586000" />
      <workItem from="1718344666714" duration="548000" />
      <workItem from="1722414637881" duration="383000" />
      <workItem from="1722415930339" duration="1120000" />
      <workItem from="1722417641077" duration="286000" />
      <workItem from="1722470790564" duration="1169000" />
      <workItem from="1722473611400" duration="3440000" />
      <workItem from="1722561032535" duration="5716000" />
      <workItem from="1723097411967" duration="3477000" />
      <workItem from="1723164356583" duration="1431000" />
      <workItem from="1723172653965" duration="1264000" />
      <workItem from="1723174080852" duration="1782000" />
      <workItem from="1723434356537" duration="2597000" />
      <workItem from="1723596329004" duration="1904000" />
      <workItem from="1723603758166" duration="169000" />
      <workItem from="1723604644861" duration="1274000" />
      <workItem from="1724634004487" duration="7341000" />
      <workItem from="1725337180243" duration="1402000" />
      <workItem from="1725602702565" duration="1365000" />
      <workItem from="1725860857819" duration="2392000" />
      <workItem from="1725864124784" duration="3160000" />
      <workItem from="1726124820907" duration="3483000" />
      <workItem from="1726210276120" duration="2836000" />
      <workItem from="1726217056776" duration="1280000" />
      <workItem from="1726297506352" duration="951000" />
      <workItem from="1727249704294" duration="641000" />
      <workItem from="1728374279040" duration="473000" />
      <workItem from="1728439097401" duration="863000" />
      <workItem from="1729221486868" duration="675000" />
      <workItem from="1730440602507" duration="2309000" />
      <workItem from="1730875444939" duration="1460000" />
      <workItem from="1731487774149" duration="1363000" />
      <workItem from="1731931488503" duration="967000" />
      <workItem from="1731986674746" duration="783000" />
      <workItem from="1732235799730" duration="645000" />
      <workItem from="1732859209666" duration="2276000" />
      <workItem from="1733120444007" duration="1827000" />
      <workItem from="1733128393960" duration="2981000" />
      <workItem from="1733184975781" duration="732000" />
      <workItem from="1733188393503" duration="17862000" />
      <workItem from="1733274961720" duration="3282000" />
      <workItem from="1733279415243" duration="3925000" />
      <workItem from="1733290769859" duration="3868000" />
      <workItem from="1733357750383" duration="5362000" />
      <workItem from="1733383057729" duration="1783000" />
      <workItem from="1733389798101" duration="746000" />
      <workItem from="1733704084721" duration="9096000" />
      <workItem from="1733726264964" duration="6510000" />
      <workItem from="1733788362148" duration="11176000" />
      <workItem from="1733809727943" duration="1355000" />
      <workItem from="1733812576852" duration="7881000" />
      <workItem from="1733875109593" duration="1982000" />
      <workItem from="1734059167006" duration="2218000" />
      <workItem from="1734070588941" duration="111000" />
      <workItem from="1734144946485" duration="151000" />
      <workItem from="1734145577656" duration="697000" />
      <workItem from="1734155460731" duration="563000" />
      <workItem from="1736565363372" duration="9144000" />
      <workItem from="1736994429001" duration="424000" />
      <workItem from="1737081551919" duration="255000" />
      <workItem from="1739155644950" duration="112000" />
      <workItem from="1741573950444" duration="120000" />
      <workItem from="1741921132778" duration="30000" />
      <workItem from="1741940599018" duration="756000" />
      <workItem from="1745398571127" duration="643000" />
      <workItem from="1745404963516" duration="4566000" />
      <workItem from="1745452625279" duration="2403000" />
      <workItem from="1745464159039" duration="1232000" />
      <workItem from="1745550693918" duration="4620000" />
      <workItem from="1749018351727" duration="87000" />
      <workItem from="1749459188606" duration="2008000" />
      <workItem from="1749515042693" duration="20535000" />
      <workItem from="1749600490627" duration="1271000" />
      <workItem from="1749606698567" duration="653000" />
      <workItem from="1750205285071" duration="1002000" />
    </task>
    <task id="LOCAL-00555" summary="http://*************:8090/x/GwQFBg&#10;pageId=100992027&#10;1、增加“论文批次”概念&#10;--论文批次表&#10;create table LW_PCB&#10;(&#10;   PCH                  VARCHAR2(20)         not null,&#10;   PCMC                 VARCHAR2(100),&#10;   ZXJXJHH              VARCHAR2(20),&#10;   PCZT                 CHAR(1), --批次状态(0未开始;1进行中;2已结束)&#10;   CZR                  VARCHAR2(20),&#10;   CZSJ                 VARCHAR2(20),&#10;   CZIP                 VARCHAR2(60),&#10;   constraint PK_LW_PCB primary key (PCH)&#10;);&#10;管理端--毕业设计题目管理，增加“毕业设计/论文批次”功能，其中“批次号”为当前学年学期+“-”+两位序号&#10;2、可选题学生白名单，修改为“批次学生管理”&#10;“增加”按钮的页面，查询条件中增加“学制”和“预计毕业日期”；&#10;学生端–学生选题时，需要检查学生是否在批次学生名单中，不再按照原来的判断学生是否为毕业生的方式检查。&#10;ALTER TABLE LW_KXTBMD ADD FAJHH VARCHAR2(12);--增加方案号并作为主键&#10;ALTER TABLE LW_KXTBMD ADD PCH  VARCHAR2(20);--增加方案号并作为主键&#10; &#10;update LW_KXTBMD x&#10;set x.fajhh = (select a.fajhh from xs_pyfab a,jh_fajhb b where a.fajhh = b.fajhh and b.xdlxdm='00001' and x.xh = a.xh)&#10;where x.fajhh is null;&#10; &#10;--修改表主键为zxjxjhh/xh/fajhh&#10;ALTER TABLE LW_KXTBMD DROP CONSTRAINT PK_LW_KXTBMD DROP INDEX;&#10;create index PK_LW_KXTBMD on LW_KXTBMD (PCH, XH, FAJHH);&#10;3、学年学期修改为“论文批次”&#10;学年学期下拉和表格中的学年学期，都要从LW_PCB中读取名称。不再使用jh_zxjxjhb的学年学期名称。">
      <created>1726211324293</created>
      <option name="number" value="00555" />
      <option name="presentableId" value="LOCAL-00555" />
      <option name="project" value="LOCAL" />
      <updated>1726211324293</updated>
    </task>
    <task id="LOCAL-00556" summary="http://*************:8090/x/GwQFBg&#10;pageId=100992027&#10;1、增加“论文批次”概念&#10;--论文批次表&#10;create table LW_PCB&#10;(&#10;   PCH                  VARCHAR2(20)         not null,&#10;   PCMC                 VARCHAR2(100),&#10;   ZXJXJHH              VARCHAR2(20),&#10;   PCZT                 CHAR(1), --批次状态(0未开始;1进行中;2已结束)&#10;   CZR                  VARCHAR2(20),&#10;   CZSJ                 VARCHAR2(20),&#10;   CZIP                 VARCHAR2(60),&#10;   constraint PK_LW_PCB primary key (PCH)&#10;);&#10;管理端--毕业设计题目管理，增加“毕业设计/论文批次”功能，其中“批次号”为当前学年学期+“-”+两位序号&#10;2、可选题学生白名单，修改为“批次学生管理”&#10;“增加”按钮的页面，查询条件中增加“学制”和“预计毕业日期”；&#10;学生端–学生选题时，需要检查学生是否在批次学生名单中，不再按照原来的判断学生是否为毕业生的方式检查。&#10;ALTER TABLE LW_KXTBMD ADD FAJHH VARCHAR2(12);--增加方案号并作为主键&#10;ALTER TABLE LW_KXTBMD ADD PCH  VARCHAR2(20);--增加方案号并作为主键&#10; &#10;update LW_KXTBMD x&#10;set x.fajhh = (select a.fajhh from xs_pyfab a,jh_fajhb b where a.fajhh = b.fajhh and b.xdlxdm='00001' and x.xh = a.xh)&#10;where x.fajhh is null;&#10; &#10;--修改表主键为zxjxjhh/xh/fajhh&#10;ALTER TABLE LW_KXTBMD DROP CONSTRAINT PK_LW_KXTBMD DROP INDEX;&#10;create index PK_LW_KXTBMD on LW_KXTBMD (PCH, XH, FAJHH);&#10;3、学年学期修改为“论文批次”&#10;学年学期下拉和表格中的学年学期，都要从LW_PCB中读取名称。不再使用jh_zxjxjhb的学年学期名称。">
      <created>1726211664201</created>
      <option name="number" value="00556" />
      <option name="presentableId" value="LOCAL-00556" />
      <option name="project" value="LOCAL" />
      <updated>1726211664201</updated>
    </task>
    <task id="LOCAL-00557" summary="http://*************:8090/x/GwQFBg&#10;pageId=100992027&#10;1、增加“论文批次”概念&#10;--论文批次表&#10;create table LW_PCB&#10;(&#10;   PCH                  VARCHAR2(20)         not null,&#10;   PCMC                 VARCHAR2(100),&#10;   ZXJXJHH              VARCHAR2(20),&#10;   PCZT                 CHAR(1), --批次状态(0未开始;1进行中;2已结束)&#10;   CZR                  VARCHAR2(20),&#10;   CZSJ                 VARCHAR2(20),&#10;   CZIP                 VARCHAR2(60),&#10;   constraint PK_LW_PCB primary key (PCH)&#10;);&#10;管理端--毕业设计题目管理，增加“毕业设计/论文批次”功能，其中“批次号”为当前学年学期+“-”+两位序号&#10;2、可选题学生白名单，修改为“批次学生管理”&#10;“增加”按钮的页面，查询条件中增加“学制”和“预计毕业日期”；&#10;学生端–学生选题时，需要检查学生是否在批次学生名单中，不再按照原来的判断学生是否为毕业生的方式检查。&#10;ALTER TABLE LW_KXTBMD ADD FAJHH VARCHAR2(12);--增加方案号并作为主键&#10;ALTER TABLE LW_KXTBMD ADD PCH  VARCHAR2(20);--增加方案号并作为主键&#10; &#10;update LW_KXTBMD x&#10;set x.fajhh = (select a.fajhh from xs_pyfab a,jh_fajhb b where a.fajhh = b.fajhh and b.xdlxdm='00001' and x.xh = a.xh)&#10;where x.fajhh is null;&#10; &#10;--修改表主键为zxjxjhh/xh/fajhh&#10;ALTER TABLE LW_KXTBMD DROP CONSTRAINT PK_LW_KXTBMD DROP INDEX;&#10;create index PK_LW_KXTBMD on LW_KXTBMD (PCH, XH, FAJHH);&#10;3、学年学期修改为“论文批次”&#10;学年学期下拉和表格中的学年学期，都要从LW_PCB中读取名称。不再使用jh_zxjxjhb的学年学期名称。">
      <created>1726211670963</created>
      <option name="number" value="00557" />
      <option name="presentableId" value="LOCAL-00557" />
      <option name="project" value="LOCAL" />
      <updated>1726211670964</updated>
    </task>
    <task id="LOCAL-00558" summary="http://*************:8090/x/GwQFBg&#10;pageId=100992027&#10;1、增加“论文批次”概念&#10;--论文批次表&#10;create table LW_PCB&#10;(&#10;   PCH                  VARCHAR2(20)         not null,&#10;   PCMC                 VARCHAR2(100),&#10;   ZXJXJHH              VARCHAR2(20),&#10;   PCZT                 CHAR(1), --批次状态(0未开始;1进行中;2已结束)&#10;   CZR                  VARCHAR2(20),&#10;   CZSJ                 VARCHAR2(20),&#10;   CZIP                 VARCHAR2(60),&#10;   constraint PK_LW_PCB primary key (PCH)&#10;);&#10;管理端--毕业设计题目管理，增加“毕业设计/论文批次”功能，其中“批次号”为当前学年学期+“-”+两位序号&#10;2、可选题学生白名单，修改为“批次学生管理”&#10;“增加”按钮的页面，查询条件中增加“学制”和“预计毕业日期”；&#10;学生端–学生选题时，需要检查学生是否在批次学生名单中，不再按照原来的判断学生是否为毕业生的方式检查。&#10;ALTER TABLE LW_KXTBMD ADD FAJHH VARCHAR2(12);--增加方案号并作为主键&#10;ALTER TABLE LW_KXTBMD ADD PCH  VARCHAR2(20);--增加方案号并作为主键&#10; &#10;update LW_KXTBMD x&#10;set x.fajhh = (select a.fajhh from xs_pyfab a,jh_fajhb b where a.fajhh = b.fajhh and b.xdlxdm='00001' and x.xh = a.xh)&#10;where x.fajhh is null;&#10; &#10;--修改表主键为zxjxjhh/xh/fajhh&#10;ALTER TABLE LW_KXTBMD DROP CONSTRAINT PK_LW_KXTBMD DROP INDEX;&#10;create index PK_LW_KXTBMD on LW_KXTBMD (PCH, XH, FAJHH);&#10;3、学年学期修改为“论文批次”&#10;学年学期下拉和表格中的学年学期，都要从LW_PCB中读取名称。不再使用jh_zxjxjhb的学年学期名称。">
      <created>1726217682859</created>
      <option name="number" value="00558" />
      <option name="presentableId" value="LOCAL-00558" />
      <option name="project" value="LOCAL" />
      <updated>1726217682859</updated>
    </task>
    <task id="LOCAL-00559" summary="http://*************:8090/x/GwQFBg&#10;pageId=100992027&#10;1、增加“论文批次”概念&#10;--论文批次表&#10;create table LW_PCB&#10;(&#10;   PCH                  VARCHAR2(20)         not null,&#10;   PCMC                 VARCHAR2(100),&#10;   ZXJXJHH              VARCHAR2(20),&#10;   PCZT                 CHAR(1), --批次状态(0未开始;1进行中;2已结束)&#10;   CZR                  VARCHAR2(20),&#10;   CZSJ                 VARCHAR2(20),&#10;   CZIP                 VARCHAR2(60),&#10;   constraint PK_LW_PCB primary key (PCH)&#10;);&#10;管理端--毕业设计题目管理，增加“毕业设计/论文批次”功能，其中“批次号”为当前学年学期+“-”+两位序号&#10;2、可选题学生白名单，修改为“批次学生管理”&#10;“增加”按钮的页面，查询条件中增加“学制”和“预计毕业日期”；&#10;学生端–学生选题时，需要检查学生是否在批次学生名单中，不再按照原来的判断学生是否为毕业生的方式检查。&#10;ALTER TABLE LW_KXTBMD ADD FAJHH VARCHAR2(12);--增加方案号并作为主键&#10;ALTER TABLE LW_KXTBMD ADD PCH  VARCHAR2(20);--增加方案号并作为主键&#10; &#10;update LW_KXTBMD x&#10;set x.fajhh = (select a.fajhh from xs_pyfab a,jh_fajhb b where a.fajhh = b.fajhh and b.xdlxdm='00001' and x.xh = a.xh)&#10;where x.fajhh is null;&#10; &#10;--修改表主键为zxjxjhh/xh/fajhh&#10;ALTER TABLE LW_KXTBMD DROP CONSTRAINT PK_LW_KXTBMD DROP INDEX;&#10;create index PK_LW_KXTBMD on LW_KXTBMD (PCH, XH, FAJHH);&#10;3、学年学期修改为“论文批次”&#10;学年学期下拉和表格中的学年学期，都要从LW_PCB中读取名称。不再使用jh_zxjxjhb的学年学期名称。">
      <created>1726217688071</created>
      <option name="number" value="00559" />
      <option name="presentableId" value="LOCAL-00559" />
      <option name="project" value="LOCAL" />
      <updated>1726217688071</updated>
    </task>
    <task id="LOCAL-00560" summary="修改页面样式代码">
      <created>1726298153285</created>
      <option name="number" value="00560" />
      <option name="presentableId" value="LOCAL-00560" />
      <option name="project" value="LOCAL" />
      <updated>1726298153285</updated>
    </task>
    <task id="LOCAL-00561" summary="http://*************:8090/x/c4MpBg&#10;实习任务精细化管理">
      <created>1731932443522</created>
      <option name="number" value="00561" />
      <option name="presentableId" value="LOCAL-00561" />
      <option name="project" value="LOCAL" />
      <updated>1731932443522</updated>
    </task>
    <task id="LOCAL-00562" summary="http://*************:8090/x/c4MpBg&#10;实习任务精细化管理">
      <created>1731987159226</created>
      <option name="number" value="00562" />
      <option name="presentableId" value="LOCAL-00562" />
      <option name="project" value="LOCAL" />
      <updated>1731987159226</updated>
    </task>
    <task id="LOCAL-00563" summary="设置完自动提交为“否”后，使用完，改回去默认“是”">
      <created>1733215051523</created>
      <option name="number" value="00563" />
      <option name="presentableId" value="LOCAL-00563" />
      <option name="project" value="LOCAL" />
      <updated>1733215051523</updated>
    </task>
    <task id="LOCAL-00564" summary="设置完自动提交为“否”后，使用完，改回去默认“是”">
      <created>1733215068194</created>
      <option name="number" value="00564" />
      <option name="presentableId" value="LOCAL-00564" />
      <option name="project" value="LOCAL" />
      <updated>1733215068194</updated>
    </task>
    <task id="LOCAL-00565" summary="设置完自动提交为“否”后，使用完，改回去默认“是”">
      <created>1733215169884</created>
      <option name="number" value="00565" />
      <option name="presentableId" value="LOCAL-00565" />
      <option name="project" value="LOCAL" />
      <updated>1733215169884</updated>
    </task>
    <task id="LOCAL-00566" summary="优化">
      <created>1733277322364</created>
      <option name="number" value="00566" />
      <option name="presentableId" value="LOCAL-00566" />
      <option name="project" value="LOCAL" />
      <updated>1733277322364</updated>
    </task>
    <task id="LOCAL-00567" summary="优化">
      <created>1733280500482</created>
      <option name="number" value="00567" />
      <option name="presentableId" value="LOCAL-00567" />
      <option name="project" value="LOCAL" />
      <updated>1733280500482</updated>
    </task>
    <task id="LOCAL-00568" summary="优化">
      <created>1733280508326</created>
      <option name="number" value="00568" />
      <option name="presentableId" value="LOCAL-00568" />
      <option name="project" value="LOCAL" />
      <updated>1733280508326</updated>
    </task>
    <task id="LOCAL-00569" summary="优化">
      <created>1733280516507</created>
      <option name="number" value="00569" />
      <option name="presentableId" value="LOCAL-00569" />
      <option name="project" value="LOCAL" />
      <updated>1733280516507</updated>
    </task>
    <task id="LOCAL-00570" summary="优化">
      <created>1733282460192</created>
      <option name="number" value="00570" />
      <option name="presentableId" value="LOCAL-00570" />
      <option name="project" value="LOCAL" />
      <updated>1733282460192</updated>
    </task>
    <task id="LOCAL-00571" summary="优化">
      <created>1733282965879</created>
      <option name="number" value="00571" />
      <option name="presentableId" value="LOCAL-00571" />
      <option name="project" value="LOCAL" />
      <updated>1733282965879</updated>
    </task>
    <task id="LOCAL-00572" summary="添加了一个方法">
      <created>1733365331111</created>
      <option name="number" value="00572" />
      <option name="presentableId" value="LOCAL-00572" />
      <option name="project" value="LOCAL" />
      <updated>1733365331111</updated>
    </task>
    <task id="LOCAL-00573" summary="修改参数获取方式">
      <created>1733365431449</created>
      <option name="number" value="00573" />
      <option name="presentableId" value="LOCAL-00573" />
      <option name="project" value="LOCAL" />
      <updated>1733365431449</updated>
    </task>
    <task id="LOCAL-00574" summary="修改参数获取方式">
      <created>1733366411998</created>
      <option name="number" value="00574" />
      <option name="presentableId" value="LOCAL-00574" />
      <option name="project" value="LOCAL" />
      <updated>1733366411998</updated>
    </task>
    <task id="LOCAL-00575" summary="设置查询的事务为只读">
      <created>1733383527066</created>
      <option name="number" value="00575" />
      <option name="presentableId" value="LOCAL-00575" />
      <option name="project" value="LOCAL" />
      <updated>1733383527066</updated>
    </task>
    <task id="LOCAL-00576" summary="设置查询的事务为只读">
      <created>1733383599681</created>
      <option name="number" value="00576" />
      <option name="presentableId" value="LOCAL-00576" />
      <option name="project" value="LOCAL" />
      <updated>1733383599681</updated>
    </task>
    <task id="LOCAL-00577" summary="加只读事务">
      <created>1733794741259</created>
      <option name="number" value="00577" />
      <option name="presentableId" value="LOCAL-00577" />
      <option name="project" value="LOCAL" />
      <updated>1733794741260</updated>
    </task>
    <task id="LOCAL-00578" summary="手机端添加参数校验">
      <created>1733809817861</created>
      <option name="number" value="00578" />
      <option name="presentableId" value="LOCAL-00578" />
      <option name="project" value="LOCAL" />
      <updated>1733809817862</updated>
    </task>
    <task id="LOCAL-00579" summary="修改内容">
      <created>1733810357743</created>
      <option name="number" value="00579" />
      <option name="presentableId" value="LOCAL-00579" />
      <option name="project" value="LOCAL" />
      <updated>1733810357743</updated>
    </task>
    <task id="LOCAL-00580" summary="修改校验">
      <created>1733810987540</created>
      <option name="number" value="00580" />
      <option name="presentableId" value="LOCAL-00580" />
      <option name="project" value="LOCAL" />
      <updated>1733810987540</updated>
    </task>
    <task id="LOCAL-00581" summary=".5格式化">
      <created>1733814058985</created>
      <option name="number" value="00581" />
      <option name="presentableId" value="LOCAL-00581" />
      <option name="project" value="LOCAL" />
      <updated>1733814058985</updated>
    </task>
    <task id="LOCAL-00582" summary="设置为不允许更新和插入">
      <created>1733814449567</created>
      <option name="number" value="00582" />
      <option name="presentableId" value="LOCAL-00582" />
      <option name="project" value="LOCAL" />
      <updated>1733814449567</updated>
    </task>
    <task id="LOCAL-00583" summary="添加日志打印">
      <created>1733818463931</created>
      <option name="number" value="00583" />
      <option name="presentableId" value="LOCAL-00583" />
      <option name="project" value="LOCAL" />
      <updated>1733818463931</updated>
    </task>
    <task id="LOCAL-00584" summary="优化">
      <created>1733818599648</created>
      <option name="number" value="00584" />
      <option name="presentableId" value="LOCAL-00584" />
      <option name="project" value="LOCAL" />
      <updated>1733818599648</updated>
    </task>
    <task id="LOCAL-00585" summary="优化">
      <created>1733818750404</created>
      <option name="number" value="00585" />
      <option name="presentableId" value="LOCAL-00585" />
      <option name="project" value="LOCAL" />
      <updated>1733818750404</updated>
    </task>
    <task id="LOCAL-00586" summary="优化">
      <created>1733819012805</created>
      <option name="number" value="00586" />
      <option name="presentableId" value="LOCAL-00586" />
      <option name="project" value="LOCAL" />
      <updated>1733819012807</updated>
    </task>
    <task id="LOCAL-00587" summary="优化">
      <created>1733819106909</created>
      <option name="number" value="00587" />
      <option name="presentableId" value="LOCAL-00587" />
      <option name="project" value="LOCAL" />
      <updated>1733819106909</updated>
    </task>
    <task id="LOCAL-00588" summary="找回密码时，如果手机号大于11位，则认为是被加密了，进行解密">
      <created>1734060299425</created>
      <option name="number" value="00588" />
      <option name="presentableId" value="LOCAL-00588" />
      <option name="project" value="LOCAL" />
      <updated>1734060299425</updated>
    </task>
    <task id="LOCAL-00589" summary="添加图标urp-chengyuanbiangeng">
      <created>1734145093785</created>
      <option name="number" value="00589" />
      <option name="presentableId" value="LOCAL-00589" />
      <option name="project" value="LOCAL" />
      <updated>1734145093785</updated>
    </task>
    <task id="LOCAL-00590" summary="添加图标urp-chengyuanbiangeng1和urp-chengyuanbiangeng2">
      <created>1734145682644</created>
      <option name="number" value="00590" />
      <option name="presentableId" value="LOCAL-00590" />
      <option name="project" value="LOCAL" />
      <updated>1734145682644</updated>
    </task>
    <task id="LOCAL-00591" summary="http://*************:8090/x/TItqBg&#10;pageId=107645772&#10;5、成绩组成-评分标准，修改成按『论文批次+院系+专业』设置，不按『年级』设置&#10;&#10;&#10;论文-分项成绩-组成表，增加论文批次列&#10;1&#10;2&#10;--表内现有的 年级 列作废。&#10;ALTER TABLE lw_fxcj_zcb ADD pch VARCHAR2(20);&#10;因现实中一个论文批次内存在同一专业不同年级一起参加论文考核（延期或提前或“重修”），但系统专业考核标准相同，为此需要把现有论文分项成绩评分标准修改成按 论文批次+院系+专业 设置。&#10;&#10;连带的，需要录入分项成绩评分标准得分的相关功能，按年级院系专业确定论文成绩组成及评分标准的逻辑，需要调整为按 批次+院系+专业 确定论文成绩组成和评分标准。&#10;&#10;成绩组成-评分标准的复制，需支持在批次间（纵向）的复制，以及批次内不同院系专业间（横向）的复制，复制操作时，纵向复制，可以支持整个批次复制到新的目标批次；横向复制，应支持多选目标 院系/院系专业 。">
      <created>1736994841769</created>
      <option name="number" value="00591" />
      <option name="presentableId" value="LOCAL-00591" />
      <option name="project" value="LOCAL" />
      <updated>1736994841769</updated>
    </task>
    <task id="LOCAL-00592" summary="http://*************:8090/x/kAOwBg&#10;pageId=112198544&#10;11.2、学生提交论文过程文档&#10;学生按选题方案所属院系，找到专业负责人在『论文-论文子阶段表lw_lwzjdb』定义里设置的由学生上传的过程文档，依据各子阶段分别需要上传的文档，在阶段起止时间内上传相应的过程文档。&#10;&#10;同指导教师提交子阶段文档，因学生在选定论文批次下可能不止一个专业存在有效选题(lw_xtb.xtztdm= ’02‘)方案，需要参加毕设/论文，建议同指导教师提交子阶段文档一样，选题方案按标签页，先显示在该方案下的选题信息（题目编号/题目名称/一导/二导），然后显示论文子阶段设置，显示该专业需要学生提交文档的子阶段，选中子阶段后，提交相应文档。&#10;&#10;学生提交文档时，如果该文档需要审批：&#10;&#10;lw_zjdwdb.csrlx上传人类型 写入S学生&#10;&#10;lw_zjdwdb.sprlx审批人类型 写入 G指导教师&#10;&#10;lw_zjdwdb.spr审批人 写入 当前选题的指导教师工号&#10;&#10;若学生提交的文档无需审批，则文档状态代码lw_zjdwdb.wdztdm直接设置为 9-无需审批（同时，将同学生选题下同阶段文档此前上传的lw_zjdwdb.wdztdm=9文档设置为-9）， lw_zjdwdb.jcr检查人 列写入当前选题所属方案专业的专业负责人工号，以便后续专业负责人抽查。">
      <created>1745455039236</created>
      <option name="number" value="00592" />
      <option name="presentableId" value="LOCAL-00592" />
      <option name="project" value="LOCAL" />
      <updated>1745455039236</updated>
    </task>
    <task id="LOCAL-00593" summary="http://*************:8090/x/kAOwBg&#10;pageId=112198544&#10;11.2、学生提交论文过程文档&#10;学生按选题方案所属院系，找到专业负责人在『论文-论文子阶段表lw_lwzjdb』定义里设置的由学生上传的过程文档，依据各子阶段分别需要上传的文档，在阶段起止时间内上传相应的过程文档。&#10;&#10;同指导教师提交子阶段文档，因学生在选定论文批次下可能不止一个专业存在有效选题(lw_xtb.xtztdm= ’02‘)方案，需要参加毕设/论文，建议同指导教师提交子阶段文档一样，选题方案按标签页，先显示在该方案下的选题信息（题目编号/题目名称/一导/二导），然后显示论文子阶段设置，显示该专业需要学生提交文档的子阶段，选中子阶段后，提交相应文档。&#10;&#10;学生提交文档时，如果该文档需要审批：&#10;&#10;lw_zjdwdb.csrlx上传人类型 写入S学生&#10;&#10;lw_zjdwdb.sprlx审批人类型 写入 G指导教师&#10;&#10;lw_zjdwdb.spr审批人 写入 当前选题的指导教师工号&#10;&#10;若学生提交的文档无需审批，则文档状态代码lw_zjdwdb.wdztdm直接设置为 9-无需审批（同时，将同学生选题下同阶段文档此前上传的lw_zjdwdb.wdztdm=9文档设置为-9）， lw_zjdwdb.jcr检查人 列写入当前选题所属方案专业的专业负责人工号，以便后续专业负责人抽查。">
      <created>1745455439162</created>
      <option name="number" value="00593" />
      <option name="presentableId" value="LOCAL-00593" />
      <option name="project" value="LOCAL" />
      <updated>1745455439162</updated>
    </task>
    <task id="LOCAL-00594" summary="http://*************:8090/x/kAOwBg&#10;pageId=112198544&#10;11.2、学生提交论文过程文档&#10;学生按选题方案所属院系，找到专业负责人在『论文-论文子阶段表lw_lwzjdb』定义里设置的由学生上传的过程文档，依据各子阶段分别需要上传的文档，在阶段起止时间内上传相应的过程文档。&#10;&#10;同指导教师提交子阶段文档，因学生在选定论文批次下可能不止一个专业存在有效选题(lw_xtb.xtztdm= ’02‘)方案，需要参加毕设/论文，建议同指导教师提交子阶段文档一样，选题方案按标签页，先显示在该方案下的选题信息（题目编号/题目名称/一导/二导），然后显示论文子阶段设置，显示该专业需要学生提交文档的子阶段，选中子阶段后，提交相应文档。&#10;&#10;学生提交文档时，如果该文档需要审批：&#10;&#10;lw_zjdwdb.csrlx上传人类型 写入S学生&#10;&#10;lw_zjdwdb.sprlx审批人类型 写入 G指导教师&#10;&#10;lw_zjdwdb.spr审批人 写入 当前选题的指导教师工号&#10;&#10;若学生提交的文档无需审批，则文档状态代码lw_zjdwdb.wdztdm直接设置为 9-无需审批（同时，将同学生选题下同阶段文档此前上传的lw_zjdwdb.wdztdm=9文档设置为-9）， lw_zjdwdb.jcr检查人 列写入当前选题所属方案专业的专业负责人工号，以便后续专业负责人抽查。">
      <created>1745455446011</created>
      <option name="number" value="00594" />
      <option name="presentableId" value="LOCAL-00594" />
      <option name="project" value="LOCAL" />
      <updated>1745455446011</updated>
    </task>
    <task id="LOCAL-00595" summary="http://*************:8090/x/kAOwBg&#10;pageId=112198544&#10;11.2、学生提交论文过程文档&#10;学生按选题方案所属院系，找到专业负责人在『论文-论文子阶段表lw_lwzjdb』定义里设置的由学生上传的过程文档，依据各子阶段分别需要上传的文档，在阶段起止时间内上传相应的过程文档。&#10;&#10;同指导教师提交子阶段文档，因学生在选定论文批次下可能不止一个专业存在有效选题(lw_xtb.xtztdm= ’02‘)方案，需要参加毕设/论文，建议同指导教师提交子阶段文档一样，选题方案按标签页，先显示在该方案下的选题信息（题目编号/题目名称/一导/二导），然后显示论文子阶段设置，显示该专业需要学生提交文档的子阶段，选中子阶段后，提交相应文档。&#10;&#10;学生提交文档时，如果该文档需要审批：&#10;&#10;lw_zjdwdb.csrlx上传人类型 写入S学生&#10;&#10;lw_zjdwdb.sprlx审批人类型 写入 G指导教师&#10;&#10;lw_zjdwdb.spr审批人 写入 当前选题的指导教师工号&#10;&#10;若学生提交的文档无需审批，则文档状态代码lw_zjdwdb.wdztdm直接设置为 9-无需审批（同时，将同学生选题下同阶段文档此前上传的lw_zjdwdb.wdztdm=9文档设置为-9）， lw_zjdwdb.jcr检查人 列写入当前选题所属方案专业的专业负责人工号，以便后续专业负责人抽查。">
      <created>1745551100616</created>
      <option name="number" value="00595" />
      <option name="presentableId" value="LOCAL-00595" />
      <option name="project" value="LOCAL" />
      <updated>1745551100616</updated>
    </task>
    <task id="LOCAL-00596" summary="http://*************:8090/x/kAOwBg&#10;pageId=112198544&#10;11.2、学生提交论文过程文档&#10;学生按选题方案所属院系，找到专业负责人在『论文-论文子阶段表lw_lwzjdb』定义里设置的由学生上传的过程文档，依据各子阶段分别需要上传的文档，在阶段起止时间内上传相应的过程文档。&#10;&#10;同指导教师提交子阶段文档，因学生在选定论文批次下可能不止一个专业存在有效选题(lw_xtb.xtztdm= ’02‘)方案，需要参加毕设/论文，建议同指导教师提交子阶段文档一样，选题方案按标签页，先显示在该方案下的选题信息（题目编号/题目名称/一导/二导），然后显示论文子阶段设置，显示该专业需要学生提交文档的子阶段，选中子阶段后，提交相应文档。&#10;&#10;学生提交文档时，如果该文档需要审批：&#10;&#10;lw_zjdwdb.csrlx上传人类型 写入S学生&#10;&#10;lw_zjdwdb.sprlx审批人类型 写入 G指导教师&#10;&#10;lw_zjdwdb.spr审批人 写入 当前选题的指导教师工号&#10;&#10;若学生提交的文档无需审批，则文档状态代码lw_zjdwdb.wdztdm直接设置为 9-无需审批（同时，将同学生选题下同阶段文档此前上传的lw_zjdwdb.wdztdm=9文档设置为-9）， lw_zjdwdb.jcr检查人 列写入当前选题所属方案专业的专业负责人工号，以便后续专业负责人抽查。">
      <created>1745560397367</created>
      <option name="number" value="00596" />
      <option name="presentableId" value="LOCAL-00596" />
      <option name="project" value="LOCAL" />
      <updated>1745560397367</updated>
    </task>
    <task id="LOCAL-00597" summary="http://*************:8090/x/kAOwBg&#10;pageId=112198544&#10;11.2、学生提交论文过程文档&#10;学生按选题方案所属院系，找到专业负责人在『论文-论文子阶段表lw_lwzjdb』定义里设置的由学生上传的过程文档，依据各子阶段分别需要上传的文档，在阶段起止时间内上传相应的过程文档。&#10;&#10;同指导教师提交子阶段文档，因学生在选定论文批次下可能不止一个专业存在有效选题(lw_xtb.xtztdm= ’02‘)方案，需要参加毕设/论文，建议同指导教师提交子阶段文档一样，选题方案按标签页，先显示在该方案下的选题信息（题目编号/题目名称/一导/二导），然后显示论文子阶段设置，显示该专业需要学生提交文档的子阶段，选中子阶段后，提交相应文档。&#10;&#10;学生提交文档时，如果该文档需要审批：&#10;&#10;lw_zjdwdb.csrlx上传人类型 写入S学生&#10;&#10;lw_zjdwdb.sprlx审批人类型 写入 G指导教师&#10;&#10;lw_zjdwdb.spr审批人 写入 当前选题的指导教师工号&#10;&#10;若学生提交的文档无需审批，则文档状态代码lw_zjdwdb.wdztdm直接设置为 9-无需审批（同时，将同学生选题下同阶段文档此前上传的lw_zjdwdb.wdztdm=9文档设置为-9）， lw_zjdwdb.jcr检查人 列写入当前选题所属方案专业的专业负责人工号，以便后续专业负责人抽查。">
      <created>1745561166170</created>
      <option name="number" value="00597" />
      <option name="presentableId" value="LOCAL-00597" />
      <option name="project" value="LOCAL" />
      <updated>1745561166170</updated>
    </task>
    <task id="LOCAL-00598" summary="http://*************:8090/x/kAOwBg&#10;pageId=112198544&#10;11.2、学生提交论文过程文档&#10;学生按选题方案所属院系，找到专业负责人在『论文-论文子阶段表lw_lwzjdb』定义里设置的由学生上传的过程文档，依据各子阶段分别需要上传的文档，在阶段起止时间内上传相应的过程文档。&#10;&#10;同指导教师提交子阶段文档，因学生在选定论文批次下可能不止一个专业存在有效选题(lw_xtb.xtztdm= ’02‘)方案，需要参加毕设/论文，建议同指导教师提交子阶段文档一样，选题方案按标签页，先显示在该方案下的选题信息（题目编号/题目名称/一导/二导），然后显示论文子阶段设置，显示该专业需要学生提交文档的子阶段，选中子阶段后，提交相应文档。&#10;&#10;学生提交文档时，如果该文档需要审批：&#10;&#10;lw_zjdwdb.csrlx上传人类型 写入S学生&#10;&#10;lw_zjdwdb.sprlx审批人类型 写入 G指导教师&#10;&#10;lw_zjdwdb.spr审批人 写入 当前选题的指导教师工号&#10;&#10;若学生提交的文档无需审批，则文档状态代码lw_zjdwdb.wdztdm直接设置为 9-无需审批（同时，将同学生选题下同阶段文档此前上传的lw_zjdwdb.wdztdm=9文档设置为-9）， lw_zjdwdb.jcr检查人 列写入当前选题所属方案专业的专业负责人工号，以便后续专业负责人抽查。">
      <created>1745564787860</created>
      <option name="number" value="00598" />
      <option name="presentableId" value="LOCAL-00598" />
      <option name="project" value="LOCAL" />
      <updated>1745564787860</updated>
    </task>
    <task id="LOCAL-00599" summary="本学期课程安排 修改为手机端样式">
      <created>1749516043003</created>
      <option name="number" value="00599" />
      <option name="presentableId" value="LOCAL-00599" />
      <option name="project" value="LOCAL" />
      <updated>1749516043004</updated>
    </task>
    <task id="LOCAL-00600" summary="格式化">
      <created>1749517593439</created>
      <option name="number" value="00600" />
      <option name="presentableId" value="LOCAL-00600" />
      <option name="project" value="LOCAL" />
      <updated>1749517593439</updated>
    </task>
    <task id="LOCAL-00601" summary="添加方法：根据sql获取数组格式数据">
      <created>1749536688137</created>
      <option name="number" value="00601" />
      <option name="presentableId" value="LOCAL-00601" />
      <option name="project" value="LOCAL" />
      <updated>1749536688138</updated>
    </task>
    <task id="LOCAL-00602" summary="本学期课表，手机端样式优化">
      <created>1749536726673</created>
      <option name="number" value="00602" />
      <option name="presentableId" value="LOCAL-00602" />
      <option name="project" value="LOCAL" />
      <updated>1749536726673</updated>
    </task>
    <task id="LOCAL-00603" summary="本学期课表，手机端样式优化&#10;优化版本1">
      <created>1749539526588</created>
      <option name="number" value="00603" />
      <option name="presentableId" value="LOCAL-00603" />
      <option name="project" value="LOCAL" />
      <updated>1749539526588</updated>
    </task>
    <option name="localTasksCounter" value="604" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.UiProperties">
    <option name="RECENTLY_FILTERED_USER_GROUPS">
      <collection />
    </option>
    <option name="RECENTLY_FILTERED_BRANCH_GROUPS">
      <collection />
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="SHOW_DIRTY_RECURSIVELY" value="true" />
    <MESSAGE value="http://*************:8090/x/GwQFBg&#10;pageId=100992027&#10;1、增加“论文批次”概念&#10;--论文批次表&#10;create table LW_PCB&#10;(&#10;   PCH                  VARCHAR2(20)         not null,&#10;   PCMC                 VARCHAR2(100),&#10;   ZXJXJHH              VARCHAR2(20),&#10;   PCZT                 CHAR(1), --批次状态(0未开始;1进行中;2已结束)&#10;   CZR                  VARCHAR2(20),&#10;   CZSJ                 VARCHAR2(20),&#10;   CZIP                 VARCHAR2(60),&#10;   constraint PK_LW_PCB primary key (PCH)&#10;);&#10;管理端--毕业设计题目管理，增加“毕业设计/论文批次”功能，其中“批次号”为当前学年学期+“-”+两位序号&#10;2、可选题学生白名单，修改为“批次学生管理”&#10;“增加”按钮的页面，查询条件中增加“学制”和“预计毕业日期”；&#10;学生端–学生选题时，需要检查学生是否在批次学生名单中，不再按照原来的判断学生是否为毕业生的方式检查。&#10;ALTER TABLE LW_KXTBMD ADD FAJHH VARCHAR2(12);--增加方案号并作为主键&#10;ALTER TABLE LW_KXTBMD ADD PCH  VARCHAR2(20);--增加方案号并作为主键&#10; &#10;update LW_KXTBMD x&#10;set x.fajhh = (select a.fajhh from xs_pyfab a,jh_fajhb b where a.fajhh = b.fajhh and b.xdlxdm='00001' and x.xh = a.xh)&#10;where x.fajhh is null;&#10; &#10;--修改表主键为zxjxjhh/xh/fajhh&#10;ALTER TABLE LW_KXTBMD DROP CONSTRAINT PK_LW_KXTBMD DROP INDEX;&#10;create index PK_LW_KXTBMD on LW_KXTBMD (PCH, XH, FAJHH);&#10;3、学年学期修改为“论文批次”&#10;学年学期下拉和表格中的学年学期，都要从LW_PCB中读取名称。不再使用jh_zxjxjhb的学年学期名称。" />
    <MESSAGE value="修改页面样式代码" />
    <MESSAGE value="http://*************:8090/x/c4MpBg&#10;实习任务精细化管理" />
    <MESSAGE value="设置完自动提交为“否”后，使用完，改回去默认“是”" />
    <MESSAGE value="添加了一个方法" />
    <MESSAGE value="修改参数获取方式" />
    <MESSAGE value="设置查询的事务为只读" />
    <MESSAGE value="加只读事务" />
    <MESSAGE value="手机端添加参数校验" />
    <MESSAGE value="修改内容" />
    <MESSAGE value="修改校验" />
    <MESSAGE value=".5格式化" />
    <MESSAGE value="设置为不允许更新和插入" />
    <MESSAGE value="添加日志打印" />
    <MESSAGE value="优化" />
    <MESSAGE value="找回密码时，如果手机号大于11位，则认为是被加密了，进行解密" />
    <MESSAGE value="添加图标urp-chengyuanbiangeng" />
    <MESSAGE value="添加图标urp-chengyuanbiangeng1和urp-chengyuanbiangeng2" />
    <MESSAGE value="http://*************:8090/x/TItqBg&#10;pageId=107645772&#10;5、成绩组成-评分标准，修改成按『论文批次+院系+专业』设置，不按『年级』设置&#10;&#10;&#10;论文-分项成绩-组成表，增加论文批次列&#10;1&#10;2&#10;--表内现有的 年级 列作废。&#10;ALTER TABLE lw_fxcj_zcb ADD pch VARCHAR2(20);&#10;因现实中一个论文批次内存在同一专业不同年级一起参加论文考核（延期或提前或“重修”），但系统专业考核标准相同，为此需要把现有论文分项成绩评分标准修改成按 论文批次+院系+专业 设置。&#10;&#10;连带的，需要录入分项成绩评分标准得分的相关功能，按年级院系专业确定论文成绩组成及评分标准的逻辑，需要调整为按 批次+院系+专业 确定论文成绩组成和评分标准。&#10;&#10;成绩组成-评分标准的复制，需支持在批次间（纵向）的复制，以及批次内不同院系专业间（横向）的复制，复制操作时，纵向复制，可以支持整个批次复制到新的目标批次；横向复制，应支持多选目标 院系/院系专业 。" />
    <MESSAGE value="http://*************:8090/x/kAOwBg&#10;pageId=112198544&#10;11.2、学生提交论文过程文档&#10;学生按选题方案所属院系，找到专业负责人在『论文-论文子阶段表lw_lwzjdb』定义里设置的由学生上传的过程文档，依据各子阶段分别需要上传的文档，在阶段起止时间内上传相应的过程文档。&#10;&#10;同指导教师提交子阶段文档，因学生在选定论文批次下可能不止一个专业存在有效选题(lw_xtb.xtztdm= ’02‘)方案，需要参加毕设/论文，建议同指导教师提交子阶段文档一样，选题方案按标签页，先显示在该方案下的选题信息（题目编号/题目名称/一导/二导），然后显示论文子阶段设置，显示该专业需要学生提交文档的子阶段，选中子阶段后，提交相应文档。&#10;&#10;学生提交文档时，如果该文档需要审批：&#10;&#10;lw_zjdwdb.csrlx上传人类型 写入S学生&#10;&#10;lw_zjdwdb.sprlx审批人类型 写入 G指导教师&#10;&#10;lw_zjdwdb.spr审批人 写入 当前选题的指导教师工号&#10;&#10;若学生提交的文档无需审批，则文档状态代码lw_zjdwdb.wdztdm直接设置为 9-无需审批（同时，将同学生选题下同阶段文档此前上传的lw_zjdwdb.wdztdm=9文档设置为-9）， lw_zjdwdb.jcr检查人 列写入当前选题所属方案专业的专业负责人工号，以便后续专业负责人抽查。" />
    <MESSAGE value="本学期课程安排 修改为手机端样式" />
    <MESSAGE value="格式化" />
    <MESSAGE value="添加方法：根据sql获取数组格式数据" />
    <MESSAGE value="本学期课表，手机端样式优化" />
    <MESSAGE value="本学期课表，手机端样式优化&#10;优化版本1" />
    <option name="LAST_COMMIT_MESSAGE" value="本学期课表，手机端样式优化&#10;优化版本1" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/personalManagement/ordertransfer/controller/TransferApplicationController.java</url>
          <line>212</line>
          <option name="timeStamp" value="82" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/main/controller/MainController.java</url>
          <line>535</line>
          <option name="timeStamp" value="83" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/urpSoft/WEB-INF/jsp/student/teachingEvaluation/coursemessageboard/messageboard.jsp</url>
          <line>148</line>
          <option name="timeStamp" value="84" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/teachingEvaluation/coursemessageboard/controller/CourseMessageBoardController.java</url>
          <line>257</line>
          <option name="timeStamp" value="85" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/teachingEvaluation/coursemessageboard/service/impl/CourseMessageBoardServiceImpl.java</url>
          <line>55</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/teachingEvaluation/coursemessageboard/service/impl/CourseMessageBoardServiceImpl.java</url>
          <line>62</line>
          <option name="timeStamp" value="88" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/individualApplication/applyCommon/service/impl/ApplyCommonServiceImpl.java</url>
          <line>2104</line>
          <option name="timeStamp" value="92" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/individualApplication/applyCommon/service/impl/ApplyCommonServiceImpl.java</url>
          <line>2153</line>
          <option name="timeStamp" value="93" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/personalManagement/controller/LwXtcxController.java</url>
          <line>191</line>
          <option name="timeStamp" value="95" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/personalManagement/service/impl/LwXtcxServiceImpl.java</url>
          <line>175</line>
          <option name="timeStamp" value="96" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/internship/internshipExecutionPlanCompletion/service/impl/InternshipExecutionPlanCompletionServiceImpl.java</url>
          <line>62</line>
          <option name="timeStamp" value="97" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/internship/internshipExecutionPlanCompletion/service/impl/InternshipExecutionPlanCompletionServiceImpl.java</url>
          <line>68</line>
          <option name="timeStamp" value="98" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/common/service/impl/CommonServiceImpl.java</url>
          <line>796</line>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/planCompletion/service/impl/PlanCompletionServiceImpl.java</url>
          <line>185</line>
          <option name="timeStamp" value="104" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/planCompletion/service/impl/PlanCompletionServiceImpl.java</url>
          <line>108</line>
          <option name="timeStamp" value="106" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/planCompletion/service/impl/PlanCompletionServiceImpl.java</url>
          <line>143</line>
          <option name="timeStamp" value="107" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/urpSoft/core/security/springSecurity/userDetailService/impl/UrpUserDetailServiceImpl.java</url>
          <line>66</line>
          <option name="timeStamp" value="108" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/thesis/stageddocuments/service/impl/StagedDocumentsUploadServiceImpl.java</url>
          <line>56</line>
          <option name="timeStamp" value="111" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/educationalAdministration/student/courseSelectMangement/controller/CurriculumTableConroller.java</url>
          <line>1302</line>
          <option name="timeStamp" value="112" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="ResinConfigurationType">
        <watch expression="xrxcjbList" />
        <watch expression="cjbList" />
        <watch expression="kwKsapViewlist" />
      </configuration>
    </watches-manager>
  </component>
  <component name="antWorkspaceConfiguration">
    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false" />
    <option name="FILTER_TARGETS" value="false" />
  </component>
</project>