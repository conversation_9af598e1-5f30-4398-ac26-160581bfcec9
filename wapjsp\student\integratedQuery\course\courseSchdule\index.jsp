<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>本学期课程安排</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 课程安排查询页面样式 */
        .course-header {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .course-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .course-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--info-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--info-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-select {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-select:focus {
            outline: none;
            border-color: var(--info-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .btn-search {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            grid-column: 1 / -1;
        }
        
        .btn-search:hover {
            background: var(--info-dark);
        }
        
        .course-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-header i {
            color: var(--info-color);
        }
        
        .course-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .course-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .course-item:last-child {
            border-bottom: none;
        }
        
        .course-header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .course-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--info-light);
            color: var(--info-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .course-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .course-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .course-capacity {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            height: 30px;
            border-radius: 15px;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .capacity-full {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .capacity-warning {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .course-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .meta-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .meta-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .course-schedule {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .schedule-icon {
            color: var(--info-color);
            font-size: var(--font-size-base);
        }
        
        .schedule-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--info-light);
            border-color: var(--info-color);
            color: var(--info-dark);
        }
        
        .btn-page.active {
            background: var(--info-color);
            border-color: var(--info-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .course-header-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .course-meta {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    <input type="hidden" name="oldTime" id="oldTime" value="">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">本学期课程安排</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 课程安排头部 -->
        <div class="course-header">
            <div class="course-title">本学期课程安排</div>
            <div class="course-desc">查询本学期所有课程的安排信息</div>
        </div>
        
        <!-- 搜索条件 -->
        <div class="search-container">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form class="search-form" id="searchForm" name="inf">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">学年学期</label>
                        <select class="form-select" name="zxjxjhh" id="zxjxjhh">
                            <c:forEach items="${list}" var="zxjxjhh">
                                <option value="${zxjxjhh[0]}" <c:if test="${zxjxjhh[0]==xnxq}">selected</c:if>>${zxjxjhh[1]}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">开课院系</label>
                        <select class="form-select" name="kkxsh" id="kkxsh">
                            <option value="">全部</option>
                            <cache:query var="Xsb" region="code_xsb_jxdw" orderby="xsh asc"/>
                            <c:forEach items="${Xsb}" var="xsb">
                                <option value="${xsb.xsh}">${empty xsb.xsjc ? xsb.xsm : xsb.xsjc}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">校区</label>
                        <select class="form-select" name="kkxqh" id="kkxqh" onchange="queryCodeJxlb(this.value)">
                            <option value="">全部</option>
                            <cache:query var="xaqb" region="code_xaqb"/>
                            <c:forEach items="${xaqb}" var="xaqb">
                                <option value="${xaqb.xqh}">${xaqb.xqm}</option>
                            </c:forEach>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">教学楼</label>
                        <select class="form-select" name="jxlh" id="jxlh" onchange="queryCodeJasb(this.value);">
                            <option value="">全部</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">教室</label>
                        <select class="form-select" name="jash" id="jash">
                            <option value="">全部</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">上课星期</label>
                        <select class="form-select" name="skxq" id="skxq">
                            <c:choose>
                                <c:when test="${skxq!=null}">
                                    <option value="${skxq}" selected="selected">${xqname}</option>
                                </c:when>
                                <c:otherwise>
                                    <option value="">全部</option>
                                </c:otherwise>
                            </c:choose>
                            <option value="1">星期一</option>
                            <option value="2">星期二</option>
                            <option value="3">星期三</option>
                            <option value="4">星期四</option>
                            <option value="5">星期五</option>
                            <option value="6">星期六</option>
                            <option value="7">星期日</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">上课节次</label>
                        <select class="form-select" name="skjc" id="skjc">
                            <c:choose>
                                <c:when test="${skjc!=null}">
                                    <option value="${skjc}" selected="selected">${jcname}</option>
                                </c:when>
                                <c:otherwise>
                                    <option value="">全部</option>
                                </c:otherwise>
                            </c:choose>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                            <option value="11">11</option>
                            <option value="12">12</option>
                            <option value="13">13</option>
                            <option value="14">14</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">课程号</label>
                        <input type="text" class="form-input" name="kch" id="kch" placeholder="请输入课程号">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">课程名</label>
                        <input type="text" class="form-input" name="kcm" id="kcm" placeholder="请输入课程名">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">课程类别</label>
                        <select class="form-select" name="kclb" id="kclb">
                            <option value="">全部</option>
                            <cache:query var="kclb" region="code_kclb"/>
                            <c:forEach items="${kclb}" var="kclb">
                                <option value="${kclb.kclbdm}">${kclb.kclbmc}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">教师名</label>
                        <input type="text" class="form-input" name="skjs" id="skjs" placeholder="请输入教师名">
                    </div>
                    
                    <div class="form-group">
                        <button type="button" class="btn-search" onclick="search();">
                            <i class="ace-icon fa fa-search"></i>
                            查询
                        </button>
                    </div>
                </div>
                
                <input type="hidden" name="xqname">
                <input type="hidden" name="jcname">
                <input type="hidden" name="jxlname">
                <input type="hidden" name="jasname">
            </form>
        </div>
        
        <!-- 课程列表 -->
        <div class="course-container">
            <div class="container-header">
                <i class="ace-icon fa fa-list"></i>
                课程安排列表
            </div>
            
            <div class="course-list" id="courseList">
                <!-- 动态加载课程列表 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let params = '';

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索课程
        function search() {
            var oldTime = $("#oldTime").val();
            var myDate = new Date();
            var nowTime = myDate.getTime();
            if (oldTime != "") {
                if ((nowTime - oldTime) / 1000 < 5) {
                    showError("请勿频繁查询！");
                    return;
                }
            }
            $("#oldTime").val(nowTime);
            searchCourseInf(1, "30_sl", true);
        }

        // 获取课程信息列表
        function searchCourseInf(page, pageSizeVal, conditionChanged) {
            if (pageSizeVal == undefined) {
                pageSizeVal = "30_sl";
                page = "1";
            }
            if (conditionChanged) {
                params = $(document.inf).serialize();
            }
            var parr = (pageSizeVal + "").split("_");
            var pageSize = parseInt(parr[0]);
            var url = "/student/integratedQuery/course/courseSchdule/courseInfo";

            showLoading(true);

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: params + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(d) {
                    if (d["pfcx"] == "0") {
                        totalCount = d["list"]["pageContext"].totalCount;
                        var isScroll = (pageSizeVal + "").indexOf("_") != -1 && page != 1 ? true : false;
                        fillTable(d["list"]["records"], isScroll, page, pageSize);
                        updatePagination(page, pageSize, totalCount);
                    } else {
                        showError("请勿频繁查询！");
                    }
                },
                error: function(xhr) {
                    showError("获取数据失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 填充课程表格
        function fillTable(data, isScroll, page, pageSize) {
            const container = $('#courseList');

            if (!isScroll) {
                container.empty();
            }

            if (!data || data.length === 0) {
                if (!isScroll) {
                    container.html(`
                        <div class="empty-state">
                            <i class="ace-icon fa fa-calendar-o"></i>
                            <div class="empty-state-text">暂无课程安排</div>
                            <div class="empty-state-desc">请调整查询条件后重试</div>
                        </div>
                    `);
                }
                return;
            }

            data.forEach(function(course, index) {
                var tableId = "";
                if (isScroll) {
                    tableId = (page - 1) * pageSize + 1 + index;
                } else {
                    tableId = index + 1;
                }

                const courseHtml = createCourseItem(course, tableId);
                container.append(courseHtml);
            });
        }

        // 创建课程项HTML
        function createCourseItem(course, index) {
            const capacity = course.bkskrl || 0;
            const enrolled = course.xss || 0;
            const capacityPercent = capacity > 0 ? (enrolled / capacity * 100) : 0;

            let capacityClass = 'course-capacity';
            if (capacityPercent >= 100) {
                capacityClass += ' capacity-full';
            } else if (capacityPercent >= 80) {
                capacityClass += ' capacity-warning';
            }

            const weekName = getWeekName(course.skxq);
            const timeRange = getTimeRange(course.skjc, course.cxjc);
            const scheduleText = `${weekName} ${timeRange}`;

            return `
                <div class="course-item">
                    <div class="course-header-row">
                        <div class="course-number">${index}</div>
                        <div class="course-info">
                            <div class="course-name">${course.kcm || ''}</div>
                            <div class="course-details">${course.kch || ''} | ${course.kxh || ''}</div>
                        </div>
                        <div class="${capacityClass}">${enrolled}/${capacity}</div>
                    </div>

                    <div class="course-meta">
                        <div class="meta-item">
                            <div class="meta-label">开课院系</div>
                            <div class="meta-value">${course.kkxsjc || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">学分</div>
                            <div class="meta-value">${course.xf ? (course.xf * 10 / 10) : ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">课程类别</div>
                            <div class="meta-value">${course.kclbmc || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">考试类型</div>
                            <div class="meta-value">${course.kslxmc || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">教师</div>
                            <div class="meta-value">${course.skjs || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">周次</div>
                            <div class="meta-value">${course.zcsm || ''}</div>
                        </div>
                    </div>

                    <div class="course-schedule">
                        <i class="ace-icon fa fa-clock-o schedule-icon"></i>
                        <div class="schedule-text">${scheduleText}</div>
                    </div>

                    <div class="course-meta">
                        <div class="meta-item">
                            <div class="meta-label">校区</div>
                            <div class="meta-value">${course.xqm || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">教学楼</div>
                            <div class="meta-value">${course.jxlm || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">教室</div>
                            <div class="meta-value">${course.jasm || ''}</div>
                        </div>
                    </div>

                    ${course.xkxzsm ? `
                        <div class="course-schedule">
                            <i class="ace-icon fa fa-info-circle schedule-icon"></i>
                            <div class="schedule-text">选课限制：${dealfh(course.xkxzsm)}</div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 获取星期名称
        function getWeekName(weekNum) {
            const weekNames = ['', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
            return weekNames[weekNum] || '';
        }

        // 获取时间范围
        function getTimeRange(startTime, duration) {
            if (!startTime) return '';

            const start = parseInt(startTime);
            if (!duration) {
                return `第${start}节`;
            }

            const end = start + parseInt(duration) - 1;
            return `第${start}-${end}节`;
        }

        // 处理分号
        function dealfh(val) {
            if (val == ";") {
                return "";
            } else {
                return val;
            }
        }

        // 查询教学楼
        function queryCodeJxlb(xqh) {
            if (!xqh) {
                $("#jxlh").html("<option value=''>全部</option>");
                $("#jash").html("<option value=''>全部</option>");
                return;
            }

            $.ajax({
                url: "/student/integratedQuery/course/courseSchdule/queryCodeJxlb",
                type: "post",
                data: "xqh=" + xqh,
                dataType: "json",
                success: function(data) {
                    jxlbOption(data);
                },
                error: function() {
                    showError("获取教学楼信息失败");
                }
            });
        }

        // 填充教学楼选项
        function jxlbOption(data) {
            var sOption = "<option value=''>全部</option>";
            $.each(data, function(i, v) {
                sOption += "<option xqh='" + v.id.xqh + "' value='" + v.id.jxlh + "'>" + v.jxlm + "</option>";
            });
            $("#jxlh").html(sOption);
            $("#jash").html("<option value=''>全部</option>");
        }

        // 查询教室
        function queryCodeJasb(jxlh) {
            if (!jxlh) {
                $("#jash").html("<option value=''>全部</option>");
                return;
            }

            var xqh = $("#jxlh option[value='" + jxlh + "']").attr("xqh");
            $.ajax({
                url: "/student/integratedQuery/course/courseSchdule/queryCodeJasb",
                type: "post",
                data: "jxlh=" + jxlh + "&xqh=" + xqh,
                dataType: "json",
                success: function(data) {
                    jasbOption(data);
                },
                error: function() {
                    showError("获取教室信息失败");
                }
            });
        }

        // 填充教室选项
        function jasbOption(data) {
            var sOption = "<option value=''>全部</option>";
            $.each(data, function(i, v) {
                sOption += "<option value='" + v.jash + "'>" + v.jasm + "</option>";
            });
            $("#jash").html(sOption);
        }

        // 更新分页信息
        function updatePagination(page, pageSize, totalCount) {
            if (totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const totalPages = Math.ceil(totalCount / pageSize);
            const startRecord = (page - 1) * pageSize + 1;
            const endRecord = Math.min(page * pageSize, totalCount);

            $('#paginationInfo').text(`显示 ${startRecord}-${endRecord} 条，共 ${totalCount} 条记录`);

            let buttonsHtml = '';

            // 上一页
            if (page > 1) {
                buttonsHtml += `<button class="btn-page" onclick="searchCourseInf(${page - 1}, '${pageSize}_sl', false)">上一页</button>`;
            } else {
                buttonsHtml += `<button class="btn-page" disabled>上一页</button>`;
            }

            // 页码
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(totalPages, page + 2);

            for (let i = startPage; i <= endPage; i++) {
                if (i === page) {
                    buttonsHtml += `<button class="btn-page active">${i}</button>`;
                } else {
                    buttonsHtml += `<button class="btn-page" onclick="searchCourseInf(${i}, '${pageSize}_sl', false)">${i}</button>`;
                }
            }

            // 下一页
            if (page < totalPages) {
                buttonsHtml += `<button class="btn-page" onclick="searchCourseInf(${page + 1}, '${pageSize}_sl', false)">下一页</button>`;
            } else {
                buttonsHtml += `<button class="btn-page" disabled>下一页</button>`;
            }

            $('#paginationButtons').html(buttonsHtml);
            $('#paginationContainer').show();
        }

        // 刷新数据
        function refreshData() {
            search();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight() || 0;
            const availableHeight = windowHeight - navbarHeight;

            $('.page-mobile').css('min-height', availableHeight + 'px');
        }
    </script>
</body>
</html>
