<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学分认定申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学分认定申请页面样式 */
        .credit-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .credit-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .credit-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .standards-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .standards-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .standards-title i {
            color: var(--primary-color);
        }
        
        .standards-tree {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            background: var(--bg-tertiary);
        }
        
        .tree-search {
            padding: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .search-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .tree-content {
            padding: var(--padding-sm);
        }
        
        .tree-node {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 4px;
            transition: all var(--transition-base);
            font-size: var(--font-size-small);
        }
        
        .tree-node:hover {
            background: var(--bg-secondary);
        }
        
        .tree-node.selected {
            background: var(--primary-color);
            color: white;
        }
        
        .tree-node.highlight {
            background: var(--warning-light);
            color: var(--warning-dark);
            font-weight: 600;
        }
        
        .tree-node.category {
            font-weight: 500;
            color: var(--text-primary);
            background: var(--bg-primary);
            border-left: 3px solid var(--primary-color);
        }
        
        .tree-node.standard {
            margin-left: 20px;
            color: var(--text-secondary);
            border-left: 2px solid var(--success-color);
            padding-left: 16px;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .applications-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-title i {
            color: var(--success-color);
        }
        
        .btn-add-application {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
            transform: translateY(-1px);
        }
        
        .btn-add-application:active {
            transform: translateY(0);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
            transform: none;
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:hover {
            background: var(--bg-tertiary);
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-standard {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .application-activity {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-cancelled {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-college-approved {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-application-action {
            flex: 1;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-reapply {
            background: var(--success-color);
            color: white;
        }
        
        .selected-standard {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin: var(--margin-sm) 0;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .selected-label {
            color: var(--info-color);
            font-weight: 500;
        }
        
        @media (max-width: 480px) {
            .application-details {
                grid-template-columns: 1fr;
            }
            
            .application-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .application-status {
                align-self: flex-end;
                margin-top: var(--margin-sm);
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    <input type="hidden" id="bzid" name="bzid" value=""/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学分认定申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学分认定头部 -->
        <div class="credit-header">
            <div class="credit-title">学分认定申请</div>
            <div class="credit-desc">申请劳动教育学分认定</div>
        </div>
        
        <!-- 认定标准选择 -->
        <div class="standards-section">
            <div class="standards-title">
                <i class="ace-icon fa fa-sitemap"></i>
                认定标准选择
            </div>
            
            <div class="standards-tree">
                <div class="tree-search">
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索认定标准...">
                </div>
                <div class="tree-content" id="treeContent">
                    <!-- 动态加载树形结构 -->
                </div>
            </div>
            
            <div class="selected-standard" id="selectedStandard" style="display: none;">
                <span class="selected-label">已选择标准：</span>
                <span id="selectedStandardName"></span>
            </div>
        </div>
        
        <!-- 申请列表 -->
        <div class="applications-section">
            <div class="applications-header">
                <div class="applications-title">
                    <i class="ace-icon fa fa-list"></i>
                    我的申请
                </div>
                <c:if test="${yxsq=='1'}">
                    <button class="btn-add-application" id="addApplicationBtn" onclick="addApplication();" disabled>
                        <i class="ace-icon fa fa-plus"></i>
                        <span>添加申请</span>
                    </button>
                </c:if>
            </div>
            
            <div id="applicationsList">
                <!-- 动态加载申请列表 -->
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-graduation-cap"></i>
            <div>暂无申请记录</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let treeData = [];
        let applicationData = [];
        let selectedStandardId = '';
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let allowApplication = '${yxsq}' === '1';

        $(function() {
            initPage();
            loadTreeData();
            loadApplications(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 绑定搜索框事件
            $('#searchInput').on('input', function() {
                searchTree($(this).val());
            });
        }

        // 加载树形数据
        function loadTreeData() {
            showLoading(true);

            $.ajax({
                url: "/student/laborEducation/laborEducationApply/queryTree",
                cache: false,
                type: "post",
                data: null,
                dataType: "json",
                success: function(data) {
                    if (data && data.length > 0) {
                        treeData = data;
                        renderTree(data);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染树形结构
        function renderTree(data) {
            const container = $('#treeContent');
            container.empty();

            data.forEach(function(node) {
                const nodeHtml = createTreeNode(node);
                container.append(nodeHtml);
            });
        }

        // 创建树节点HTML
        function createTreeNode(node) {
            const isStandard = node.type === 'xfrd';
            const nodeClass = isStandard ? 'tree-node standard' : 'tree-node category';

            return `
                <div class="${nodeClass}"
                     data-id="${node.nodeId || ''}"
                     data-type="${node.type || ''}"
                     onclick="selectTreeNode(this, '${node.nodeId || ''}', '${node.type || ''}', '${node.name || ''}');">
                    ${node.name || ''}
                </div>
            `;
        }

        // 选择树节点
        function selectTreeNode(element, nodeId, nodeType, nodeName) {
            // 清除之前的选择
            $('.tree-node').removeClass('selected');

            if (nodeType === 'xfrd') {
                // 选择学分认定标准
                $(element).addClass('selected');
                selectedStandardId = nodeId;
                $('#bzid').val(nodeId);

                // 显示选择的标准
                $('#selectedStandardName').text(nodeName);
                $('#selectedStandard').show();

                // 启用添加申请按钮
                if (allowApplication) {
                    $('#addApplicationBtn').prop('disabled', false);
                }
            } else {
                showError("请选择学分认定标准！");
            }
        }

        // 搜索树节点
        function searchTree(keyword) {
            $('.tree-node').removeClass('highlight');

            if (!keyword.trim()) {
                return;
            }

            $('.tree-node').each(function() {
                const text = $(this).text();
                if (text.toLowerCase().includes(keyword.toLowerCase())) {
                    $(this).addClass('highlight');
                }
            });
        }

        // 加载申请列表
        function loadApplications(page, conditionChanged) {
            if (conditionChanged) {
                currentPage = 1;
            }

            showLoading(true);

            const url = "/student/laborEducation/laborEducationApply/queryPage";

            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    if (data && data.records) {
                        applicationData = data.records;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (applicationData.length > 0) {
                            renderApplications();
                        } else {
                            showEmptyState();
                        }
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications() {
            const container = $('#applicationsList');
            container.empty();
            $('#emptyState').hide();

            applicationData.forEach(function(application, index) {
                const applicationHtml = createApplicationItem(application, index + 1);
                container.append(applicationHtml);
            });
        }

        // 创建申请项目HTML
        function createApplicationItem(application, index) {
            const status = getApplicationStatus(application);
            const actions = getApplicationActions(application);

            return `
                <div class="application-item">
                    <div class="application-header">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index}</div>
                            <div class="application-content">
                                <div class="application-standard">${application.BZMC || ''}</div>
                                <div class="application-activity">${application.HDMC || ''}</div>
                            </div>
                        </div>
                        <div class="application-status ${status.class}">${status.text}</div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">申请时间：</span>
                            <span>${application.SQSJSTR || ''}</span>
                        </div>
                    </div>

                    ${actions ? `
                        <div class="application-actions">
                            ${actions}
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 获取申请状态
        function getApplicationStatus(application) {
            if (application.CXFS && application.CXFS !== "") {
                return { text: "已撤销", class: "status-cancelled" };
            } else if (application.YXSPJL === "empty" && application.JWCSPJL === "empty") {
                return { text: "待审批", class: "status-pending" };
            } else if (application.YXSPJL === "20" || application.JWCSPJL === "20") {
                return { text: "审批不通过", class: "status-rejected" };
            } else if (application.YXSPJL === "10" && application.JWCSPJL === "10") {
                return { text: "教务审批通过", class: "status-approved" };
            } else {
                return { text: "院系已通过", class: "status-college-approved" };
            }
        }

        // 获取申请操作按钮
        function getApplicationActions(application) {
            if (!allowApplication) {
                return `
                    <button class="btn-application-action btn-view" onclick="viewApplication('${application.ID}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `;
            }

            let actions = '';

            // 可编辑和删除的条件
            if (application.CXFS === "" && application.YXSPJL === "empty" && application.JWCSPJL === "empty") {
                actions += `
                    <button class="btn-application-action btn-edit" onclick="editApplication('${application.ID}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                    <button class="btn-application-action btn-delete" onclick="deleteApplication('${application.ID}');">
                        <i class="ace-icon fa fa-trash"></i>
                        <span>删除</span>
                    </button>
                `;
            } else {
                // 可重新申请的条件
                if (application.YXSPJL === "20" && application.CNT === "0") {
                    actions += `
                        <button class="btn-application-action btn-reapply" onclick="reapplyApplication('${application.ID}');">
                            <i class="ace-icon fa fa-plus"></i>
                            <span>再次提交</span>
                        </button>
                    `;
                }

                actions += `
                    <button class="btn-application-action btn-view" onclick="viewApplication('${application.ID}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `;
            }

            return actions;
        }

        // 添加申请
        function addApplication() {
            const bzid = $('#bzid').val();
            if (!bzid) {
                showError("请选择一个学分认定标准！");
                return;
            }

            $.ajax({
                url: "/student/laborEducation/laborEducationApply/beforAdd",
                type: "post",
                data: "",
                dataType: "json",
                success: function(data) {
                    if (data.result === "ok") {
                        const url = "/student/laborEducation/laborEducationApply/addApply?bzid=" + bzid;

                        if (parent && parent.addTab) {
                            parent.addTab('添加申请', url);
                        } else {
                            window.location.href = url;
                        }
                    } else {
                        showError(data.result);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                }
            });
        }

        // 编辑申请
        function editApplication(id) {
            const url = "/student/laborEducation/laborEducationApply/editApply?type=edit&id=" + id;

            if (parent && parent.addTab) {
                parent.addTab('修改申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看申请
        function viewApplication(id) {
            const url = "/student/laborEducation/laborEducationApply/editApply?type=view&id=" + id;

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 删除申请
        function deleteApplication(id) {
            if (confirm("是否确认删除?删除后将不可撤销。")) {
                showLoading(true);

                $.ajax({
                    url: "/student/laborEducation/laborEducationApply/" + id + "?tokenValue=" + $("#tokenValue").val(),
                    cache: false,
                    type: "delete",
                    data: null,
                    dataType: "json",
                    success: function(data) {
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/") !== -1) {
                            window.location.href = data.result;
                        } else {
                            if (data.result === "ok") {
                                showSuccess("删除成功！");
                                loadApplications(1, true);
                            } else {
                                showError(data.msg);
                            }
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 重新申请
        function reapplyApplication(id) {
            if (confirm("再次提交当前申请数据后，将复制当前数据进行二次申请，是否继续？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/laborEducation/laborEducationApply/doReapply",
                    type: "post",
                    data: "tokenValue=" + $("#tokenValue").val() + "&id=" + id,
                    dataType: "json",
                    success: function(data) {
                        $("#tokenValue").val(data.token);

                        if (data.result === "ok") {
                            showSuccess("操作成功！");
                            loadApplications(1, true);
                        } else {
                            showError(data.result);
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 显示空状态
        function showEmptyState() {
            $('#applicationsList').empty();
            $('#emptyState').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 刷新数据
        function refreshData() {
            loadTreeData();
            loadApplications(1, true);
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
