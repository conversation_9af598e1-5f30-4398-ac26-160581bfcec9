# JAR包清理脚本和指南

## 🧹 清理步骤

### 第一阶段：备份现有lib目录
```bash
# 创建备份目录
mkdir urpSoft/WEB-INF/lib_backup_$(date +%Y%m%d)

# 备份现有lib目录
cp -r urpSoft/WEB-INF/lib/* urpSoft/WEB-INF/lib_backup_$(date +%Y%m%d)/
```

### 第二阶段：移除重复的jar包

#### 1. Spring框架相关
```bash
# 保留3.2.12.RELEASE版本，如果存在3.1.3版本则移除
# 当前lib目录已经是3.2.12版本，无需操作
```

#### 2. Jackson相关清理
```bash
# 移除Jackson 1.x版本
rm urpSoft/WEB-INF/lib/jackson-core-asl-1.9.9.jar
rm urpSoft/WEB-INF/lib/jackson-mapper-asl-1.9.9.jar

# 移除重复的Jackson 2.x版本，保留2.5.4
rm urpSoft/WEB-INF/lib/jackson-all-2.0.1.jar
rm urpSoft/WEB-INF/lib/jackson-module-jaxb-annotations-2.0.6.jar

# 保留以下Jackson 2.5.4版本：
# - jackson-core-2.5.4.jar
# - jackson-databind-2.5.4.jar  
# - jackson-annotations-2.5.4.jar
```

#### 3. POI相关清理
```bash
# 移除旧版本POI
rm urpSoft/WEB-INF/lib/poi-3.8.jar
rm urpSoft/WEB-INF/lib/poi-3.9.jar
rm urpSoft/WEB-INF/lib/poi2.jar
rm urpSoft/WEB-INF/lib/poi-ooxml-3.9.jar
rm urpSoft/WEB-INF/lib/poi-ooxml-schemas-3.9.jar

# 保留POI 3.13版本：
# - poi-3.13.jar
# - poi-ooxml-3.13.jar
# - poi-ooxml-schemas-3.13.jar
# - poi-scratchpad-3.13.jar
```

#### 4. Commons组件清理
```bash
# 移除旧版本commons-codec
rm urpSoft/WEB-INF/lib/commons-codec-1.5.jar
# 保留 commons-codec-1.9.jar

# 根据项目需要选择commons-pool版本
# 如果使用新版本，移除旧版本：
# rm urpSoft/WEB-INF/lib/commons-pool-1.6.jar
# 保留 commons-pool2-2.4.2.jar
```

#### 5. HTTP客户端清理
```bash
# 移除旧版本httpclient
rm urpSoft/WEB-INF/lib/httpclient-4.5.2.jar
# 保留 httpclient-4.5.6.jar
```

#### 6. Gson清理
```bash
# 移除旧版本gson
rm urpSoft/WEB-INF/lib/gson-2.2.4.jar
# 保留 gson-2.8.6.jar
```

### 第三阶段：验证清理结果

#### 1. 检查重复jar包
```bash
# 创建检查脚本
cat > check_duplicates.sh << 'EOF'
#!/bin/bash
echo "检查重复的jar包..."
cd urpSoft/WEB-INF/lib/

echo "=== Jackson相关 ==="
ls -la | grep jackson

echo "=== POI相关 ==="
ls -la | grep poi

echo "=== Commons相关 ==="
ls -la | grep commons

echo "=== HTTP客户端相关 ==="
ls -la | grep http

echo "=== Gson相关 ==="
ls -la | grep gson

echo "=== Spring相关 ==="
ls -la | grep spring | head -5
EOF

chmod +x check_duplicates.sh
./check_duplicates.sh
```

#### 2. 统计jar包数量
```bash
echo "清理前jar包数量: $(ls urpSoft/WEB-INF/lib_backup_*/  | wc -l)"
echo "清理后jar包数量: $(ls urpSoft/WEB-INF/lib/ | wc -l)"
```

## 📋 清理清单

### 需要移除的jar包列表
```
jackson-core-asl-1.9.9.jar
jackson-mapper-asl-1.9.9.jar
jackson-all-2.0.1.jar
jackson-module-jaxb-annotations-2.0.6.jar
poi-3.8.jar
poi-3.9.jar
poi2.jar
poi-ooxml-3.9.jar
poi-ooxml-schemas-3.9.jar
commons-codec-1.5.jar
httpclient-4.5.2.jar
gson-2.2.4.jar
```

### 需要保留的jar包列表
```
jackson-core-2.5.4.jar
jackson-databind-2.5.4.jar
jackson-annotations-2.5.4.jar
poi-3.13.jar
poi-ooxml-3.13.jar
poi-ooxml-schemas-3.13.jar
poi-scratchpad-3.13.jar
commons-codec-1.9.jar
httpclient-4.5.6.jar
gson-2.8.6.jar
fastjson-1.2.83.jar
```

## ⚠️ 注意事项

1. **测试环境先行**: 在生产环境执行前，请在测试环境完整验证
2. **功能测试**: 重点测试JSON处理、Excel导入导出、HTTP请求等功能
3. **回滚准备**: 保留备份，确保可以快速回滚
4. **分步执行**: 建议分批次清理，每次清理后进行测试
5. **更新配置**: 清理完成后需要同步更新pom.xml和.classpath文件

## 🔄 回滚方案

如果清理后出现问题，可以快速回滚：
```bash
# 停止应用服务
# 恢复备份
rm -rf urpSoft/WEB-INF/lib/*
cp urpSoft/WEB-INF/lib_backup_*/  urpSoft/WEB-INF/lib/
# 重启应用服务
```
