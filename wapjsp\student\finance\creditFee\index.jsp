<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学分收费</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学分收费页面样式 */
        .fee-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .fee-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .fee-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .fee-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .fee-amount {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .fee-amount.total {
            color: var(--primary-color);
        }
        
        .fee-amount.paid {
            color: var(--success-color);
        }
        
        .fee-amount.unpaid {
            color: var(--error-color);
        }
        
        .fee-amount.refund {
            color: var(--warning-color);
        }
        
        .fee-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .semester-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .selector-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .semester-tabs {
            display: flex;
            gap: var(--spacing-xs);
            overflow-x: auto;
            padding-bottom: var(--padding-xs);
        }
        
        .semester-tab {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            white-space: nowrap;
            transition: all var(--transition-base);
        }
        
        .semester-tab:hover {
            background: var(--primary-light);
        }
        
        .semester-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .fee-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .fee-list.show {
            display: block;
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-total {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .fee-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .fee-item:last-child {
            border-bottom: none;
        }
        
        .fee-item:active {
            background: var(--bg-color-active);
        }
        
        .fee-item.paid {
            border-left: 4px solid var(--success-color);
        }
        
        .fee-item.unpaid {
            border-left: 4px solid var(--error-color);
        }
        
        .fee-item.partial {
            border-left: 4px solid var(--warning-color);
        }
        
        .fee-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .fee-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .fee-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-paid {
            background: var(--success-color);
            color: white;
        }
        
        .status-unpaid {
            background: var(--error-color);
            color: white;
        }
        
        .status-partial {
            background: var(--warning-color);
            color: white;
        }
        
        .fee-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .fee-amount-display {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .amount-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .amount-value {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .fee-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-pay {
            background: var(--success-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .payment-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .payment-modal.show {
            display: flex;
        }
        
        .payment-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .payment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .payment-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .payment-close {
            color: var(--text-secondary);
            cursor: pointer;
            font-size: var(--font-size-h4);
        }
        
        .payment-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .payment-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .payment-methods {
            margin-bottom: var(--margin-md);
        }
        
        .method-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .method-item {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .method-item:hover {
            border-color: var(--primary-color);
        }
        
        .method-item.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .method-item:last-child {
            margin-bottom: 0;
        }
        
        .method-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .method-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .payment-actions {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-confirm {
            background: var(--success-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学分收费</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="fee-header">
            <div class="header-title">学分收费</div>
            <div class="header-subtitle">查看和缴纳学分相关费用</div>
        </div>

        <!-- 费用汇总 -->
        <div class="fee-summary">
            <div class="summary-title">
                <i class="ace-icon fa fa-money"></i>
                <span>费用汇总</span>
            </div>

            <div class="fee-cards">
                <div class="fee-card">
                    <div class="fee-amount total" id="totalAmount">¥0.00</div>
                    <div class="fee-label">总费用</div>
                </div>
                <div class="fee-card">
                    <div class="fee-amount paid" id="paidAmount">¥0.00</div>
                    <div class="fee-label">已缴费</div>
                </div>
                <div class="fee-card">
                    <div class="fee-amount unpaid" id="unpaidAmount">¥0.00</div>
                    <div class="fee-label">待缴费</div>
                </div>
                <div class="fee-card">
                    <div class="fee-amount refund" id="refundAmount">¥0.00</div>
                    <div class="fee-label">可退费</div>
                </div>
            </div>
        </div>

        <!-- 学期选择 -->
        <div class="semester-selector">
            <div class="selector-title">
                <i class="ace-icon fa fa-calendar"></i>
                <span>学期费用</span>
            </div>

            <div class="semester-tabs" id="semesterTabs">
                <!-- 学期标签将动态填充 -->
            </div>
        </div>

        <!-- 费用列表 -->
        <div class="fee-list" id="feeList">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span id="semesterTitle">学期费用明细</span>
                </div>
                <div class="list-total" id="semesterTotal">¥0.00</div>
            </div>

            <div id="feeItems">
                <!-- 费用列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-money"></i>
            <div id="emptyMessage">暂无费用记录</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 缴费模态框 -->
    <div class="payment-modal" id="paymentModal">
        <div class="payment-content">
            <div class="payment-header">
                <div class="payment-title" id="paymentTitle">缴费确认</div>
                <div class="payment-close" onclick="closePaymentModal();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="payment-body">
                <div class="payment-info" id="paymentInfo">
                    <!-- 缴费信息将动态填充 -->
                </div>

                <div class="payment-methods">
                    <div class="method-title">选择支付方式</div>

                    <div class="method-item" data-method="alipay" onclick="selectPaymentMethod('alipay')">
                        <div class="method-name">支付宝</div>
                        <div class="method-desc">使用支付宝扫码支付</div>
                    </div>

                    <div class="method-item" data-method="wechat" onclick="selectPaymentMethod('wechat')">
                        <div class="method-name">微信支付</div>
                        <div class="method-desc">使用微信扫码支付</div>
                    </div>

                    <div class="method-item" data-method="bank" onclick="selectPaymentMethod('bank')">
                        <div class="method-name">银行卡</div>
                        <div class="method-desc">使用银行卡在线支付</div>
                    </div>
                </div>

                <div class="payment-actions">
                    <button class="btn-mobile btn-cancel flex-1" onclick="closePaymentModal();">取消</button>
                    <button class="btn-mobile btn-confirm flex-1" onclick="confirmPayment();">确认缴费</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let feeData = {};
        let semestersData = [];
        let currentSemester = '';
        let currentFeeItem = null;
        let selectedPaymentMethod = '';

        $(function() {
            initPage();
            loadFeeData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载费用数据
        function loadFeeData() {
            showLoading(true);

            $.ajax({
                url: "/student/finance/creditFee/getFeeData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    feeData = data.feeData || {};
                    semestersData = data.semesters || [];

                    updateFeeSummary();
                    renderSemesterTabs();

                    // 默认选择第一个学期
                    if (semestersData.length > 0) {
                        selectSemester(semestersData[0].id);
                    }

                    showLoading(false);
                },
                error: function() {
                    showError('加载费用数据失败');
                    showLoading(false);
                }
            });
        }

        // 更新费用汇总
        function updateFeeSummary() {
            $('#totalAmount').text('¥' + (feeData.totalAmount || 0).toFixed(2));
            $('#paidAmount').text('¥' + (feeData.paidAmount || 0).toFixed(2));
            $('#unpaidAmount').text('¥' + (feeData.unpaidAmount || 0).toFixed(2));
            $('#refundAmount').text('¥' + (feeData.refundAmount || 0).toFixed(2));
        }

        // 渲染学期标签
        function renderSemesterTabs() {
            const container = $('#semesterTabs');
            container.empty();

            semestersData.forEach((semester, index) => {
                const isActive = index === 0 ? 'active' : '';
                const tabHtml = `
                    <div class="semester-tab ${isActive}" data-semester="${semester.id}" onclick="selectSemester('${semester.id}')">
                        ${semester.name}
                    </div>
                `;
                container.append(tabHtml);
            });
        }

        // 选择学期
        function selectSemester(semesterId) {
            currentSemester = semesterId;

            // 更新标签状态
            $('.semester-tab').removeClass('active');
            $(`.semester-tab[data-semester="${semesterId}"]`).addClass('active');

            // 加载学期费用
            loadSemesterFees(semesterId);
        }

        // 加载学期费用
        function loadSemesterFees(semesterId) {
            $.ajax({
                url: "/student/finance/creditFee/getSemesterFees",
                type: "post",
                data: { semesterId: semesterId },
                dataType: "json",
                success: function(data) {
                    const semester = semestersData.find(s => s.id === semesterId);
                    const fees = data.fees || [];

                    renderSemesterFees(semester, fees);
                    showFeeList();
                },
                error: function() {
                    console.log('加载学期费用失败');
                    showEmptyState('加载学期费用失败');
                }
            });
        }

        // 渲染学期费用
        function renderSemesterFees(semester, fees) {
            $('#semesterTitle').text(`${semester.name} 费用明细`);

            const totalAmount = fees.reduce((sum, fee) => sum + (fee.amount || 0), 0);
            $('#semesterTotal').text('¥' + totalAmount.toFixed(2));

            const container = $('#feeItems');
            container.empty();

            if (fees.length === 0) {
                showEmptyState('该学期暂无费用记录');
                return;
            } else {
                hideEmptyState();
            }

            fees.forEach(fee => {
                const feeHtml = createFeeItem(fee);
                container.append(feeHtml);
            });
        }

        // 创建费用项
        function createFeeItem(fee) {
            const status = getFeeStatus(fee);
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);

            return `
                <div class="fee-item ${status}" onclick="showFeeDetail('${fee.id}')">
                    <div class="fee-basic">
                        <div class="fee-course">${fee.courseName}</div>
                        <div class="fee-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="fee-details">
                        <div class="detail-item">
                            <span>课程代码:</span>
                            <span>${fee.courseCode}</span>
                        </div>
                        <div class="detail-item">
                            <span>学分:</span>
                            <span>${fee.credits}</span>
                        </div>
                        <div class="detail-item">
                            <span>单价:</span>
                            <span>¥${fee.unitPrice}/学分</span>
                        </div>
                        <div class="detail-item">
                            <span>费用类型:</span>
                            <span>${getFeeTypeText(fee.type)}</span>
                        </div>
                    </div>
                    <div class="fee-amount-display">
                        <span class="amount-label">应缴金额:</span>
                        <span class="amount-value">¥${fee.amount.toFixed(2)}</span>
                    </div>
                    <div class="fee-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showFeeDetail('${fee.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'unpaid' || status === 'partial' ? `
                            <button class="btn-mobile btn-pay" onclick="event.stopPropagation(); showPaymentModal('${fee.id}');">
                                <i class="ace-icon fa fa-credit-card"></i>
                                <span>缴费</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-check"></i>
                                <span>已缴费</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取费用状态
        function getFeeStatus(fee) {
            if (fee.paidAmount >= fee.amount) {
                return 'paid';
            } else if (fee.paidAmount > 0) {
                return 'partial';
            } else {
                return 'unpaid';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'paid': return '已缴费';
                case 'partial': return '部分缴费';
                case 'unpaid': return '未缴费';
                default: return '未知';
            }
        }

        // 获取费用类型文本
        function getFeeTypeText(type) {
            switch(type) {
                case 'tuition': return '学费';
                case 'credit': return '学分费';
                case 'retake': return '重修费';
                case 'makeup': return '补考费';
                case 'material': return '教材费';
                default: return '其他费用';
            }
        }

        // 显示费用详情
        function showFeeDetail(feeId) {
            // 从当前学期费用中查找
            $.ajax({
                url: "/student/finance/creditFee/getFeeDetail",
                type: "post",
                data: { feeId: feeId },
                dataType: "json",
                success: function(data) {
                    const fee = data.fee;
                    if (fee) {
                        let message = `费用详情\n\n`;
                        message += `课程名称：${fee.courseName}\n`;
                        message += `课程代码：${fee.courseCode}\n`;
                        message += `学分：${fee.credits}\n`;
                        message += `费用类型：${getFeeTypeText(fee.type)}\n`;
                        message += `单价：¥${fee.unitPrice}/学分\n`;
                        message += `应缴金额：¥${fee.amount.toFixed(2)}\n`;
                        message += `已缴金额：¥${fee.paidAmount.toFixed(2)}\n`;
                        message += `剩余金额：¥${(fee.amount - fee.paidAmount).toFixed(2)}\n`;
                        message += `缴费状态：${getStatusText(getFeeStatus(fee))}\n`;

                        if (fee.dueDate) {
                            message += `缴费截止：${formatDate(fee.dueDate)}\n`;
                        }

                        if (fee.note) {
                            message += `\n备注：${fee.note}\n`;
                        }

                        if (typeof urp !== 'undefined' && urp.alert) {
                            urp.alert(message);
                        } else {
                            alert(message);
                        }
                    }
                },
                error: function() {
                    showError('加载费用详情失败');
                }
            });
        }

        // 显示缴费模态框
        function showPaymentModal(feeId) {
            $.ajax({
                url: "/student/finance/creditFee/getFeeDetail",
                type: "post",
                data: { feeId: feeId },
                dataType: "json",
                success: function(data) {
                    const fee = data.fee;
                    if (fee) {
                        currentFeeItem = fee;
                        renderPaymentInfo(fee);
                        $('#paymentModal').addClass('show');
                    }
                },
                error: function() {
                    showError('加载费用信息失败');
                }
            });
        }

        // 渲染缴费信息
        function renderPaymentInfo(fee) {
            const remainingAmount = fee.amount - fee.paidAmount;

            const infoHtml = `
                <div class="detail-item">
                    <span class="detail-label">课程名称:</span>
                    <span class="detail-value">${fee.courseName}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">费用类型:</span>
                    <span class="detail-value">${getFeeTypeText(fee.type)}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">应缴金额:</span>
                    <span class="detail-value">¥${fee.amount.toFixed(2)}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">已缴金额:</span>
                    <span class="detail-value">¥${fee.paidAmount.toFixed(2)}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">本次缴费:</span>
                    <span class="detail-value" style="color: var(--error-color); font-weight: 600;">¥${remainingAmount.toFixed(2)}</span>
                </div>
            `;

            $('#paymentInfo').html(infoHtml);
            $('#paymentTitle').text(`缴费确认 - ${fee.courseName}`);

            // 重置支付方式选择
            selectedPaymentMethod = '';
            $('.method-item').removeClass('selected');
        }

        // 选择支付方式
        function selectPaymentMethod(method) {
            selectedPaymentMethod = method;
            $('.method-item').removeClass('selected');
            $(`.method-item[data-method="${method}"]`).addClass('selected');
        }

        // 关闭缴费模态框
        function closePaymentModal() {
            $('#paymentModal').removeClass('show');
            currentFeeItem = null;
            selectedPaymentMethod = '';
        }

        // 确认缴费
        function confirmPayment() {
            if (!currentFeeItem) {
                showError('请选择要缴费的项目');
                return;
            }

            if (!selectedPaymentMethod) {
                showError('请选择支付方式');
                return;
            }

            const remainingAmount = currentFeeItem.amount - currentFeeItem.paidAmount;
            const message = `确定要缴费吗？\n\n课程：${currentFeeItem.courseName}\n金额：¥${remainingAmount.toFixed(2)}\n支付方式：${getPaymentMethodText(selectedPaymentMethod)}`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doPayment();
                    }
                });
            } else {
                if (confirm(message)) {
                    doPayment();
                }
            }
        }

        // 执行缴费
        function doPayment() {
            $.ajax({
                url: "/student/finance/creditFee/makePayment",
                type: "post",
                data: {
                    feeId: currentFeeItem.id,
                    paymentMethod: selectedPaymentMethod,
                    amount: (currentFeeItem.amount - currentFeeItem.paidAmount).toFixed(2)
                },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('缴费成功');
                        closePaymentModal();
                        loadFeeData(); // 重新加载数据
                        if (currentSemester) {
                            loadSemesterFees(currentSemester);
                        }
                    } else {
                        showError(data.message || '缴费失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 获取支付方式文本
        function getPaymentMethodText(method) {
            switch(method) {
                case 'alipay': return '支付宝';
                case 'wechat': return '微信支付';
                case 'bank': return '银行卡';
                default: return '未知';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示费用列表
        function showFeeList() {
            $('#feeList').addClass('show');
        }

        // 刷新数据
        function refreshData() {
            loadFeeData();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('#feeList').removeClass('show');
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('#feeList').addClass('show');
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#paymentModal').click(function(e) {
            if (e.target === this) {
                closePaymentModal();
            }
        });
    </script>
</body>
</html>
