<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实习日志</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实习日志页面样式 */
        .log-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .internship-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .log-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }
        
        .stat-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .log-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-add-log {
            background: var(--success-color);
            color: white;
        }
        
        .btn-export {
            background: var(--info-color);
            color: white;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .filter-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .filter-row {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .filter-row:last-child {
            margin-bottom: 0;
        }
        
        .filter-item {
            flex: 1;
        }
        
        .filter-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .filter-select {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .log-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .log-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .log-item.draft {
            border-left-color: var(--warning-color);
        }
        
        .log-item.submitted {
            border-left-color: var(--success-color);
        }
        
        .log-item.reviewed {
            border-left-color: var(--info-color);
        }
        
        .log-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .log-date {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .log-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .status-submitted {
            background: var(--success-color);
            color: white;
        }
        
        .status-reviewed {
            background: var(--info-color);
            color: white;
        }
        
        .log-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .log-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .log-actions-item {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .log-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .log-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实习日志</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="log-header">
            <div class="header-title">实习日志</div>
            <div class="header-subtitle">记录实习过程，总结实践经验</div>
        </div>

        <!-- 实习信息 -->
        <div class="internship-info">
            <div class="info-title">当前实习信息</div>
            <div class="info-grid" id="internshipInfo">
                <!-- 实习信息将动态填充 -->
            </div>
        </div>

        <!-- 日志统计 -->
        <div class="log-stats">
            <div class="stats-title">日志统计</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalLogs">0</div>
                    <div class="stat-label">总日志数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="submittedLogs">0</div>
                    <div class="stat-label">已提交</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="draftLogs">0</div>
                    <div class="stat-label">草稿</div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="log-actions">
            <button class="btn-mobile btn-add-log flex-1" onclick="showLogForm();">
                <i class="ace-icon fa fa-plus"></i>
                <span>写日志</span>
            </button>
            <button class="btn-mobile btn-export flex-1" onclick="exportLogs();">
                <i class="ace-icon fa fa-download"></i>
                <span>导出</span>
            </button>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="ace-icon fa fa-filter"></i>
                <span>筛选条件</span>
            </div>

            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">时间范围</div>
                    <select class="filter-select" id="timeRangeSelect">
                        <option value="">全部时间</option>
                        <option value="week">最近一周</option>
                        <option value="month">最近一月</option>
                        <option value="quarter">最近三月</option>
                    </select>
                </div>
                <div class="filter-item">
                    <div class="filter-label">日志状态</div>
                    <select class="filter-select" id="statusSelect">
                        <option value="">全部状态</option>
                        <option value="draft">草稿</option>
                        <option value="submitted">已提交</option>
                        <option value="reviewed">已评阅</option>
                    </select>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">搜索内容</div>
                    <input type="text" class="filter-select" id="searchInput" placeholder="输入关键词..." onkeyup="searchLogs()">
                </div>
            </div>
        </div>

        <!-- 日志列表 -->
        <div class="container-mobile">
            <div id="logList">
                <!-- 日志列表将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-file-text"></i>
                <div id="emptyMessage">暂无实习日志</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <!-- 日志表单 -->
    <div class="log-form" id="logForm">
        <div class="form-header">
            <div class="form-back" onclick="closeLogForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">写实习日志</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-calendar"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">日志日期</div>
                    <input type="date" class="form-input" id="logDate">
                </div>

                <div class="form-group">
                    <div class="form-label">天气情况</div>
                    <select class="form-input" id="weather">
                        <option value="">请选择天气</option>
                        <option value="sunny">晴天</option>
                        <option value="cloudy">多云</option>
                        <option value="rainy">雨天</option>
                        <option value="snowy">雪天</option>
                        <option value="foggy">雾天</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">工作地点</div>
                    <input type="text" class="form-input" id="workLocation" placeholder="请输入工作地点">
                </div>
            </div>

            <!-- 工作内容 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-tasks"></i>
                    <span>工作内容</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">今日工作内容</div>
                    <textarea class="form-input form-textarea" id="workContent"
                              placeholder="请详细描述今天的工作内容和完成情况..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">工作成果</div>
                    <textarea class="form-input form-textarea" id="workResult"
                              placeholder="请描述今天的工作成果和收获..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">遇到的问题</div>
                    <textarea class="form-input form-textarea" id="problems"
                              placeholder="请描述工作中遇到的问题和困难..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">解决方案</div>
                    <textarea class="form-input form-textarea" id="solutions"
                              placeholder="请描述问题的解决方案或处理方法..."></textarea>
                </div>
            </div>

            <!-- 学习心得 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-lightbulb-o"></i>
                    <span>学习心得</span>
                </div>

                <div class="form-group">
                    <div class="form-label">今日收获</div>
                    <textarea class="form-input form-textarea" id="learnings"
                              placeholder="请总结今天的学习收获和体会..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">改进建议</div>
                    <textarea class="form-input form-textarea" id="improvements"
                              placeholder="请提出对工作或学习的改进建议..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">明日计划</div>
                    <textarea class="form-input form-textarea" id="tomorrowPlan"
                              placeholder="请简述明天的工作计划..."></textarea>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeLogForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitLog();">提交日志</button>
        </div>
    </div>

    <script>
        // 全局变量
        let allLogs = [];
        let filteredLogs = [];
        let currentLog = null;
        let internshipInfo = {};
        let logStats = {};

        $(function() {
            initPage();
            loadInternshipInfo();
            loadLogStats();
            loadLogs();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFilterEvents();
            setDefaultDate();
        }

        // 绑定筛选事件
        function bindFilterEvents() {
            $('#timeRangeSelect, #statusSelect').change(function() {
                applyFilters();
            });
        }

        // 设置默认日期
        function setDefaultDate() {
            const today = new Date().toISOString().split('T')[0];
            $('#logDate').val(today);
        }

        // 加载实习信息
        function loadInternshipInfo() {
            $.ajax({
                url: "/student/personalManagement/internshipLog/getInternshipInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    internshipInfo = data || {};
                    renderInternshipInfo();
                },
                error: function() {
                    console.log('加载实习信息失败');
                }
            });
        }

        // 渲染实习信息
        function renderInternshipInfo() {
            const container = $('#internshipInfo');

            if (!internshipInfo.companyName) {
                container.html('<div style="text-align: center; color: var(--text-secondary);">暂无实习信息</div>');
                return;
            }

            const infoHtml = `
                <div class="info-item">
                    <span>实习单位:</span>
                    <span>${internshipInfo.companyName}</span>
                </div>
                <div class="info-item">
                    <span>实习岗位:</span>
                    <span>${internshipInfo.position}</span>
                </div>
                <div class="info-item">
                    <span>开始时间:</span>
                    <span>${formatDate(internshipInfo.startDate)}</span>
                </div>
                <div class="info-item">
                    <span>结束时间:</span>
                    <span>${formatDate(internshipInfo.endDate)}</span>
                </div>
                <div class="info-item">
                    <span>指导教师:</span>
                    <span>${internshipInfo.supervisor}</span>
                </div>
                <div class="info-item">
                    <span>实习状态:</span>
                    <span>${getInternshipStatusText(internshipInfo.status)}</span>
                </div>
            `;
            container.html(infoHtml);
        }

        // 获取实习状态文本
        function getInternshipStatusText(status) {
            switch(status) {
                case 'in-progress': return '实习中';
                case 'completed': return '已完成';
                case 'suspended': return '已暂停';
                default: return '未知';
            }
        }

        // 加载日志统计
        function loadLogStats() {
            $.ajax({
                url: "/student/personalManagement/internshipLog/getLogStats",
                type: "post",
                dataType: "json",
                success: function(data) {
                    logStats = data || {};
                    updateLogStats();
                },
                error: function() {
                    console.log('加载日志统计失败');
                }
            });
        }

        // 更新日志统计
        function updateLogStats() {
            $('#totalLogs').text(logStats.totalLogs || 0);
            $('#submittedLogs').text(logStats.submittedLogs || 0);
            $('#draftLogs').text(logStats.draftLogs || 0);
        }

        // 加载日志数据
        function loadLogs() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/internshipLog/getLogs",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allLogs = data.logs || [];
                    applyFilters();
                    showLoading(false);
                },
                error: function() {
                    showError('加载日志数据失败');
                    showLoading(false);
                }
            });
        }

        // 应用筛选条件
        function applyFilters() {
            const filters = {
                timeRange: $('#timeRangeSelect').val(),
                status: $('#statusSelect').val(),
                search: $('#searchInput').val().toLowerCase()
            };

            filteredLogs = allLogs.filter(log => {
                if (filters.timeRange && !isInTimeRange(log.logDate, filters.timeRange)) return false;
                if (filters.status && log.status !== filters.status) return false;
                if (filters.search && !logContainsSearch(log, filters.search)) return false;
                return true;
            });

            renderLogList();
        }

        // 检查是否在时间范围内
        function isInTimeRange(logDate, timeRange) {
            const now = new Date();
            const logDateTime = new Date(logDate);

            switch(timeRange) {
                case 'week':
                    return (now - logDateTime) <= 7 * 24 * 60 * 60 * 1000;
                case 'month':
                    return (now - logDateTime) <= 30 * 24 * 60 * 60 * 1000;
                case 'quarter':
                    return (now - logDateTime) <= 90 * 24 * 60 * 60 * 1000;
                default:
                    return true;
            }
        }

        // 检查日志是否包含搜索关键词
        function logContainsSearch(log, search) {
            return log.workContent.toLowerCase().includes(search) ||
                   log.workResult.toLowerCase().includes(search) ||
                   log.learnings.toLowerCase().includes(search) ||
                   (log.problems && log.problems.toLowerCase().includes(search));
        }

        // 搜索日志
        function searchLogs() {
            applyFilters();
        }

        // 渲染日志列表
        function renderLogList() {
            const container = $('#logList');
            container.empty();

            if (filteredLogs.length === 0) {
                showEmptyState('暂无符合条件的日志');
                return;
            } else {
                hideEmptyState();
            }

            filteredLogs.forEach(log => {
                const logHtml = createLogItem(log);
                container.append(logHtml);
            });
        }

        // 创建日志项
        function createLogItem(log) {
            const statusClass = getLogStatusClass(log.status);
            const statusText = getLogStatusText(log.status);

            return `
                <div class="log-item ${statusClass}" onclick="showLogDetail('${log.id}')">
                    <div class="log-header-info">
                        <div class="log-date">${formatDate(log.logDate)}</div>
                        <div class="log-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="log-content">${log.workContent}</div>
                    <div class="log-details">
                        <div class="detail-item">
                            <span>天气:</span>
                            <span>${getWeatherText(log.weather)}</span>
                        </div>
                        <div class="detail-item">
                            <span>地点:</span>
                            <span>${log.workLocation || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span>创建时间:</span>
                            <span>${formatDateTime(log.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>更新时间:</span>
                            <span>${formatDateTime(log.updateTime)}</span>
                        </div>
                    </div>
                    <div class="log-actions-item">
                        ${createLogActions(log)}
                    </div>
                </div>
            `;
        }

        // 创建日志操作按钮
        function createLogActions(log) {
            const canEdit = log.status === 'draft';

            let actions = [];

            if (canEdit) {
                actions.push(`<button class="btn-mobile btn-edit" onclick="editLog('${log.id}')">编辑</button>`);
                actions.push(`<button class="btn-mobile btn-delete" onclick="deleteLog('${log.id}')">删除</button>`);
            } else {
                actions.push(`<button class="btn-mobile btn-view" onclick="showLogDetail('${log.id}')">查看</button>`);
            }

            return actions.join('');
        }

        // 获取日志状态样式类
        function getLogStatusClass(status) {
            switch(status) {
                case 'draft': return 'draft';
                case 'submitted': return 'submitted';
                case 'reviewed': return 'reviewed';
                default: return 'draft';
            }
        }

        // 获取日志状态文本
        function getLogStatusText(status) {
            switch(status) {
                case 'draft': return '草稿';
                case 'submitted': return '已提交';
                case 'reviewed': return '已评阅';
                default: return '未知';
            }
        }

        // 获取天气文本
        function getWeatherText(weather) {
            switch(weather) {
                case 'sunny': return '晴天';
                case 'cloudy': return '多云';
                case 'rainy': return '雨天';
                case 'snowy': return '雪天';
                case 'foggy': return '雾天';
                default: return weather || '-';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 格式化日期时间
        function formatDateTime(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleString();
        }

        // 显示日志表单
        function showLogForm(logId = null) {
            currentLog = logId ? allLogs.find(log => log.id === logId) : null;

            if (currentLog) {
                // 编辑模式
                $('#formTitle').text('编辑实习日志');
                fillLogForm(currentLog);
            } else {
                // 新建模式
                $('#formTitle').text('写实习日志');
                resetLogForm();
            }

            $('#logForm').addClass('show');
        }

        // 填充日志表单
        function fillLogForm(log) {
            $('#logDate').val(log.logDate);
            $('#weather').val(log.weather);
            $('#workLocation').val(log.workLocation);
            $('#workContent').val(log.workContent);
            $('#workResult').val(log.workResult);
            $('#problems').val(log.problems);
            $('#solutions').val(log.solutions);
            $('#learnings').val(log.learnings);
            $('#improvements').val(log.improvements);
            $('#tomorrowPlan').val(log.tomorrowPlan);
        }

        // 重置日志表单
        function resetLogForm() {
            setDefaultDate();
            $('#weather').val('');
            $('#workLocation').val('');
            $('#workContent').val('');
            $('#workResult').val('');
            $('#problems').val('');
            $('#solutions').val('');
            $('#learnings').val('');
            $('#improvements').val('');
            $('#tomorrowPlan').val('');
        }

        // 关闭日志表单
        function closeLogForm() {
            $('#logForm').removeClass('show');
        }

        // 编辑日志
        function editLog(logId) {
            showLogForm(logId);
        }

        // 删除日志
        function deleteLog(logId) {
            const log = allLogs.find(l => l.id === logId);
            if (!log) return;

            const message = `确定要删除${formatDate(log.logDate)}的日志吗？\n\n删除后无法恢复。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doDeleteLog(logId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doDeleteLog(logId);
                }
            }
        }

        // 执行删除日志
        function doDeleteLog(logId) {
            $.ajax({
                url: "/student/personalManagement/internshipLog/deleteLog",
                type: "post",
                data: { logId: logId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('日志删除成功');
                        loadLogStats();
                        loadLogs();
                    } else {
                        showError(data.message || '删除失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 保存草稿
        function saveDraft() {
            if (!validateLogForm(false)) {
                return;
            }

            const formData = collectLogFormData();
            formData.isDraft = true;

            submitLogFormData(formData, '草稿保存成功');
        }

        // 提交日志
        function submitLog() {
            if (!validateLogForm(true)) {
                return;
            }

            const formData = collectLogFormData();
            formData.isDraft = false;

            const message = `确定要提交${formatDate(formData.logDate)}的实习日志吗？\n\n提交后将无法修改。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitLogFormData(formData, '日志提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    submitLogFormData(formData, '日志提交成功');
                }
            }
        }

        // 收集日志表单数据
        function collectLogFormData() {
            return {
                id: currentLog ? currentLog.id : null,
                logDate: $('#logDate').val(),
                weather: $('#weather').val(),
                workLocation: $('#workLocation').val(),
                workContent: $('#workContent').val(),
                workResult: $('#workResult').val(),
                problems: $('#problems').val(),
                solutions: $('#solutions').val(),
                learnings: $('#learnings').val(),
                improvements: $('#improvements').val(),
                tomorrowPlan: $('#tomorrowPlan').val()
            };
        }

        // 验证日志表单
        function validateLogForm(isSubmit) {
            if (!$('#logDate').val()) {
                showError('请选择日志日期');
                return false;
            }

            if (!$('#workContent').val().trim()) {
                showError('请填写工作内容');
                return false;
            }

            return true;
        }

        // 提交日志表单数据
        function submitLogFormData(formData, successMessage) {
            $.ajax({
                url: "/student/personalManagement/internshipLog/saveLog",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        closeLogForm();
                        loadLogStats();
                        loadLogs();
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示日志详情
        function showLogDetail(logId) {
            const log = allLogs.find(l => l.id === logId);
            if (!log) return;

            let message = `实习日志详情\n\n`;
            message += `日期：${formatDate(log.logDate)}\n`;
            message += `天气：${getWeatherText(log.weather)}\n`;
            message += `地点：${log.workLocation || '-'}\n`;
            message += `状态：${getLogStatusText(log.status)}\n\n`;
            message += `工作内容：\n${log.workContent}\n\n`;

            if (log.workResult) {
                message += `工作成果：\n${log.workResult}\n\n`;
            }

            if (log.problems) {
                message += `遇到问题：\n${log.problems}\n\n`;
            }

            if (log.solutions) {
                message += `解决方案：\n${log.solutions}\n\n`;
            }

            if (log.learnings) {
                message += `学习收获：\n${log.learnings}\n\n`;
            }

            if (log.improvements) {
                message += `改进建议：\n${log.improvements}\n\n`;
            }

            if (log.tomorrowPlan) {
                message += `明日计划：\n${log.tomorrowPlan}\n\n`;
            }

            message += `创建时间：${formatDateTime(log.createTime)}\n`;
            message += `更新时间：${formatDateTime(log.updateTime)}`;

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 导出日志
        function exportLogs() {
            if (allLogs.length === 0) {
                showError('暂无日志可导出');
                return;
            }

            const message = `确定要导出所有实习日志吗？\n\n将生成PDF格式的日志文件。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doExportLogs();
                    }
                });
            } else {
                if (confirm(message)) {
                    doExportLogs();
                }
            }
        }

        // 执行导出日志
        function doExportLogs() {
            $.ajax({
                url: "/student/personalManagement/internshipLog/exportLogs",
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data.success && data.downloadUrl) {
                        window.open(data.downloadUrl, '_blank');
                        showSuccess('日志导出成功');
                    } else {
                        showError(data.message || '导出失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 刷新数据
        function refreshData() {
            loadInternshipInfo();
            loadLogStats();
            loadLogs();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#logList').hide();
            } else {
                $('#loadingState').hide();
                $('#logList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#logForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#logForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeLogForm();
            }
        });

        $('#logForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
