<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实验成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实验成绩页面样式 */
        .score-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .summary-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-row {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .filter-row:last-child {
            margin-bottom: 0;
        }
        
        .filter-item {
            flex: 1;
        }
        
        .filter-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .filter-select {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .score-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .score-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .score-excellent {
            border-left-color: var(--success-color);
        }
        
        .score-good {
            border-left-color: var(--info-color);
        }
        
        .score-average {
            border-left-color: var(--warning-color);
        }
        
        .score-poor {
            border-left-color: var(--error-color);
        }
        
        .score-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .experiment-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .score-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 500;
            min-width: 60px;
            text-align: center;
        }
        
        .score-excellent-badge {
            background: var(--success-color);
            color: white;
        }
        
        .score-good-badge {
            background: var(--info-color);
            color: white;
        }
        
        .score-average-badge {
            background: var(--warning-color);
            color: white;
        }
        
        .score-poor-badge {
            background: var(--error-color);
            color: white;
        }
        
        .score-pending-badge {
            background: var(--text-disabled);
            color: white;
        }
        
        .score-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .score-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .score-breakdown {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
        }
        
        .breakdown-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .breakdown-items {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .breakdown-item {
            color: var(--text-secondary);
        }
        
        .score-comment {
            background: rgba(24, 144, 255, 0.1);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--primary-color);
            line-height: var(--line-height-base);
        }
        
        .comment-title {
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .chart-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .score-chart {
            height: 200px;
            display: flex;
            align-items: end;
            justify-content: space-around;
            padding: var(--padding-md);
            border-bottom: 2px solid var(--divider-color);
            position: relative;
        }
        
        .chart-bar {
            width: 30px;
            background: var(--primary-color);
            border-radius: 4px 4px 0 0;
            position: relative;
            transition: all var(--transition-base);
            cursor: pointer;
        }
        
        .chart-bar:hover {
            background: var(--primary-dark);
        }
        
        .bar-label {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            text-align: center;
            width: 60px;
        }
        
        .bar-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: var(--font-size-mini);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .grade-distribution {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .distribution-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .distribution-items {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .distribution-item {
            text-align: center;
            padding: var(--padding-md);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .distribution-grade {
            font-size: var(--font-size-h4);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .distribution-grade.excellent {
            color: var(--success-color);
        }
        
        .distribution-grade.good {
            color: var(--info-color);
        }
        
        .distribution-grade.average {
            color: var(--warning-color);
        }
        
        .distribution-grade.poor {
            color: var(--error-color);
        }
        
        .distribution-count {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .export-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .export-title {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .export-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-export {
            flex: 1;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-export:hover {
            background: var(--primary-dark);
        }
        
        .btn-export i {
            margin-right: var(--margin-xs);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实验成绩</div>
            <div class="navbar-action" onclick="refreshScores();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 成绩统计 -->
        <div class="score-summary">
            <div class="summary-title">实验成绩统计</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalExperiments">0</div>
                    <div class="stat-label">总实验</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="averageScore">0</div>
                    <div class="stat-label">平均分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="passedExperiments">0</div>
                    <div class="stat-label">已通过</div>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">选择学期</div>
                    <select class="filter-select" id="semesterSelect">
                        <option value="">全部学期</option>
                    </select>
                </div>
                <div class="filter-item">
                    <div class="filter-label">选择课程</div>
                    <select class="filter-select" id="courseSelect">
                        <option value="">全部课程</option>
                    </select>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">成绩等级</div>
                    <select class="filter-select" id="gradeSelect">
                        <option value="">全部等级</option>
                        <option value="excellent">优秀(90-100)</option>
                        <option value="good">良好(80-89)</option>
                        <option value="average">中等(70-79)</option>
                        <option value="poor">及格(60-69)</option>
                        <option value="fail">不及格(<60)</option>
                    </select>
                </div>
                <div class="filter-item">
                    <div class="filter-label">状态</div>
                    <select class="filter-select" id="statusSelect">
                        <option value="">全部状态</option>
                        <option value="graded">已评分</option>
                        <option value="pending">待评分</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 成绩趋势图 -->
        <div class="chart-section">
            <div class="chart-title">成绩趋势</div>
            <div class="score-chart" id="scoreChart">
                <!-- 图表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 成绩分布 -->
        <div class="grade-distribution">
            <div class="distribution-title">成绩分布</div>
            <div class="distribution-items">
                <div class="distribution-item">
                    <div class="distribution-grade excellent" id="excellentCount">0</div>
                    <div class="distribution-count">优秀</div>
                </div>
                <div class="distribution-item">
                    <div class="distribution-grade good" id="goodCount">0</div>
                    <div class="distribution-count">良好</div>
                </div>
                <div class="distribution-item">
                    <div class="distribution-grade average" id="averageCount">0</div>
                    <div class="distribution-count">中等</div>
                </div>
                <div class="distribution-item">
                    <div class="distribution-grade poor" id="poorCount">0</div>
                    <div class="distribution-count">及格</div>
                </div>
            </div>
        </div>

        <!-- 实验成绩列表 -->
        <div class="container-mobile">
            <div id="scoreList">
                <!-- 成绩列表将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-flask"></i>
                <div>暂无实验成绩</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>

        <!-- 导出功能 -->
        <div class="export-section">
            <div class="export-title">导出成绩单</div>
            <div class="export-buttons">
                <button class="btn-export" onclick="exportScores('pdf');">
                    <i class="ace-icon fa fa-file-pdf-o"></i>
                    <span>导出PDF</span>
                </button>
                <button class="btn-export" onclick="exportScores('excel');">
                    <i class="ace-icon fa fa-file-excel-o"></i>
                    <span>导出Excel</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allScores = [];
        let filteredScores = [];
        let semesters = [];
        let courses = [];
        let currentFilter = {};

        $(function() {
            initPage();
            loadInitialData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 筛选条件变化事件
            $('#semesterSelect, #courseSelect, #gradeSelect, #statusSelect').on('change', function() {
                applyFilter();
            });
        }

        // 加载初始数据
        function loadInitialData() {
            showLoading(true);

            Promise.all([
                loadSemesters(),
                loadCourses(),
                loadScores()
            ]).then(() => {
                showLoading(false);
            }).catch(() => {
                showLoading(false);
                showError('加载数据失败');
            });
        }

        // 加载学期列表
        function loadSemesters() {
            return $.ajax({
                url: "/student/experiment/scores/getSemesters",
                type: "post",
                dataType: "json",
                success: function(data) {
                    semesters = data || [];
                    renderSemesterOptions();
                }
            });
        }

        // 渲染学期选项
        function renderSemesterOptions() {
            const select = $('#semesterSelect');
            select.find('option:not(:first)').remove();

            semesters.forEach(semester => {
                select.append(`<option value="${semester.id}">${semester.name}</option>`);
            });
        }

        // 加载课程列表
        function loadCourses() {
            return $.ajax({
                url: "/student/experiment/scores/getCourses",
                type: "post",
                dataType: "json",
                success: function(data) {
                    courses = data || [];
                    renderCourseOptions();
                }
            });
        }

        // 渲染课程选项
        function renderCourseOptions() {
            const select = $('#courseSelect');
            select.find('option:not(:first)').remove();

            courses.forEach(course => {
                select.append(`<option value="${course.id}">${course.name}</option>`);
            });
        }

        // 加载实验成绩
        function loadScores() {
            return $.ajax({
                url: "/student/experiment/scores/getScores",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allScores = data.scores || [];
                    updateStatistics(data.statistics);
                    updateDistribution(data.distribution);
                    renderChart(data.chartData);
                    applyFilter();
                }
            });
        }

        // 应用筛选
        function applyFilter() {
            const filter = {
                semester: $('#semesterSelect').val(),
                course: $('#courseSelect').val(),
                grade: $('#gradeSelect').val(),
                status: $('#statusSelect').val()
            };

            currentFilter = filter;

            filteredScores = allScores.filter(score => {
                if (filter.semester && score.semesterId !== filter.semester) return false;
                if (filter.course && score.courseId !== filter.course) return false;
                if (filter.grade && getScoreGrade(score.score) !== filter.grade) return false;
                if (filter.status && score.status !== filter.status) return false;
                return true;
            });

            renderScoreList();
        }

        // 渲染成绩列表
        function renderScoreList() {
            const container = $('#scoreList');
            container.empty();

            if (filteredScores.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            filteredScores.forEach(score => {
                const scoreHtml = createScoreItem(score);
                container.append(scoreHtml);
            });
        }

        // 创建成绩项HTML
        function createScoreItem(score) {
            const grade = getScoreGrade(score.score);
            const gradeClass = getGradeClass(grade);
            const badgeClass = getBadgeClass(grade);
            const gradeText = getGradeText(grade, score.score);

            let breakdownHtml = '';
            if (score.breakdown) {
                breakdownHtml = `
                    <div class="score-breakdown">
                        <div class="breakdown-title">成绩构成</div>
                        <div class="breakdown-items">
                            <span class="breakdown-item">实验操作: ${score.breakdown.operation}分</span>
                            <span class="breakdown-item">实验报告: ${score.breakdown.report}分</span>
                            <span class="breakdown-item">课堂表现: ${score.breakdown.performance}分</span>
                        </div>
                    </div>
                `;
            }

            let commentHtml = '';
            if (score.comment) {
                commentHtml = `
                    <div class="score-comment">
                        <div class="comment-title">教师评语</div>
                        <div>${score.comment}</div>
                    </div>
                `;
            }

            return `
                <div class="score-item ${gradeClass}" onclick="showScoreDetail('${score.id}')">
                    <div class="score-header">
                        <div class="experiment-name">${score.experimentName}</div>
                        <div class="score-badge ${badgeClass}">${gradeText}</div>
                    </div>
                    <div class="score-details">
                        <div class="score-detail-item">
                            <span>课程名称:</span>
                            <span>${score.courseName}</span>
                        </div>
                        <div class="score-detail-item">
                            <span>指导教师:</span>
                            <span>${score.teacher}</span>
                        </div>
                        <div class="score-detail-item">
                            <span>实验时间:</span>
                            <span>${score.experimentDate}</span>
                        </div>
                        <div class="score-detail-item">
                            <span>评分时间:</span>
                            <span>${score.gradeDate || '待评分'}</span>
                        </div>
                    </div>
                    ${breakdownHtml}
                    ${commentHtml}
                </div>
            `;
        }

        // 获取成绩等级
        function getScoreGrade(score) {
            if (score === null || score === undefined) return 'pending';
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 70) return 'average';
            if (score >= 60) return 'poor';
            return 'fail';
        }

        // 获取等级样式类
        function getGradeClass(grade) {
            switch(grade) {
                case 'excellent': return 'score-excellent';
                case 'good': return 'score-good';
                case 'average': return 'score-average';
                case 'poor': return 'score-poor';
                default: return '';
            }
        }

        // 获取徽章样式类
        function getBadgeClass(grade) {
            switch(grade) {
                case 'excellent': return 'score-excellent-badge';
                case 'good': return 'score-good-badge';
                case 'average': return 'score-average-badge';
                case 'poor': return 'score-poor-badge';
                case 'pending': return 'score-pending-badge';
                default: return 'score-poor-badge';
            }
        }

        // 获取等级文本
        function getGradeText(grade, score) {
            if (grade === 'pending') return '待评分';
            return score + '分';
        }

        // 显示成绩详情
        function showScoreDetail(scoreId) {
            const score = allScores.find(s => s.id === scoreId);
            if (!score) return;

            let message = `实验成绩详情\n\n`;
            message += `实验名称：${score.experimentName}\n`;
            message += `课程名称：${score.courseName}\n`;
            message += `指导教师：${score.teacher}\n`;
            message += `实验时间：${score.experimentDate}\n`;

            if (score.score !== null && score.score !== undefined) {
                message += `总成绩：${score.score}分\n`;

                if (score.breakdown) {
                    message += `\n成绩构成：\n`;
                    message += `- 实验操作：${score.breakdown.operation}分\n`;
                    message += `- 实验报告：${score.breakdown.report}分\n`;
                    message += `- 课堂表现：${score.breakdown.performance}分\n`;
                }

                message += `评分时间：${score.gradeDate}\n`;

                if (score.comment) {
                    message += `\n教师评语：\n${score.comment}`;
                }
            } else {
                message += `状态：待评分\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;

            $('#totalExperiments').text(statistics.totalExperiments || 0);
            $('#averageScore').text(statistics.averageScore || 0);
            $('#passedExperiments').text(statistics.passedExperiments || 0);
        }

        // 更新成绩分布
        function updateDistribution(distribution) {
            if (!distribution) return;

            $('#excellentCount').text(distribution.excellent || 0);
            $('#goodCount').text(distribution.good || 0);
            $('#averageCount').text(distribution.average || 0);
            $('#poorCount').text(distribution.poor || 0);
        }

        // 渲染图表
        function renderChart(chartData) {
            if (!chartData || chartData.length === 0) return;

            const container = $('#scoreChart');
            container.empty();

            const maxScore = Math.max(...chartData.map(item => item.score || 0));
            const chartHeight = 160; // 图表高度

            chartData.forEach((item, index) => {
                const score = item.score || 0;
                const height = maxScore > 0 ? (score / maxScore) * chartHeight : 0;

                const barHtml = `
                    <div class="chart-bar" style="height: ${height}px;" onclick="showChartDetail('${item.experimentName}', ${score})">
                        <div class="bar-value">${score}</div>
                        <div class="bar-label">${item.experimentName.length > 6 ? item.experimentName.substring(0, 6) + '...' : item.experimentName}</div>
                    </div>
                `;
                container.append(barHtml);
            });
        }

        // 显示图表详情
        function showChartDetail(experimentName, score) {
            const message = `${experimentName}\n成绩：${score}分`;

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 导出成绩
        function exportScores(format) {
            const params = new URLSearchParams(currentFilter);
            params.append('format', format);

            const url = `/student/experiment/scores/export?${params.toString()}`;
            window.open(url);
        }

        // 刷新成绩
        function refreshScores() {
            loadInitialData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#scoreList').hide();
            } else {
                $('#loadingState').hide();
                $('#scoreList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
