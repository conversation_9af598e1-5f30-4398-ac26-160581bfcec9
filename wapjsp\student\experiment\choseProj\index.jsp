<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实验项目选择</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实验项目选择页面样式 */
        .experiment-header {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .experiment-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .experiment-stage {
            font-size: var(--font-size-small);
            opacity: 0.9;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
        }
        
        .stage-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .stage-highlight {
            color: #ffeb3b;
            font-weight: 600;
        }
        
        .selection-mode {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .mode-button {
            flex: 1;
            padding: var(--padding-sm);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .mode-button.active {
            background: var(--success-color);
            color: white;
        }
        
        .mode-button:not(.active) {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--success-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--success-color);
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .course-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--success-color);
            position: relative;
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
            margin-right: var(--margin-sm);
        }
        
        .course-code {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        .btn-select-project {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            min-width: 80px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .btn-select-project:hover {
            background: var(--success-dark);
            transform: translateY(-1px);
        }
        
        .btn-select-project:active {
            transform: translateY(0);
        }
        
        .btn-select-project:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
            transform: none;
        }
        
        .course-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--margin-sm);
        }
        
        .stat-item {
            text-align: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: 2px;
        }
        
        .stat-label {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .progress-section {
            margin-top: var(--margin-sm);
        }
        
        .progress-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .progress-percentage {
            font-weight: 600;
            color: var(--success-color);
        }
        
        .progress-bar-container {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), #4caf50);
            border-radius: 10px;
            transition: width 0.6s ease;
            position: relative;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .semester-info {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin: var(--margin-sm) var(--margin-md);
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .semester-label {
            color: var(--info-color);
            font-weight: 500;
        }
        
        .warning-notice {
            background: var(--warning-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        @media (max-width: 480px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .course-stats {
                grid-template-columns: 1fr;
            }
            
            .course-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .btn-select-project {
                margin-top: var(--margin-sm);
                align-self: flex-end;
                width: auto;
            }
            
            .experiment-stage {
                flex-direction: column;
                gap: 8px;
            }
        }
        
        @media (min-width: 481px) {
            .search-form {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实验项目选择</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 实验阶段信息 -->
        <div class="experiment-header">
            <div class="experiment-title">实验项目选择</div>
            <div class="experiment-stage">
                <div class="stage-item">
                    <i class="ace-icon fa fa-flag"></i>
                    <span>当前阶段：<span class="stage-highlight">${xjkd.id.xkjd}</span></span>
                </div>
                <c:if test="${xjkd.sfkzxxmsl eq '是'}">
                    <div class="stage-item">
                        <i class="ace-icon fa fa-limit"></i>
                        <span>最多选择：<span class="stage-highlight">${xjkd.kzxxmsl}</span>个项目</span>
                    </div>
                </c:if>
            </div>
        </div>
        
        <!-- 学期信息 -->
        <div class="semester-info">
            <span class="semester-label">学年学期：</span>
            <cache:get var="zxjxjhh" region="jh_zxjxjhb_view" key="${xnxq}" keyName="zxjxjhh" targetprop="zxjxjhm" out="true"/>
        </div>
        
        <!-- 消息提示 -->
        <c:if test="${not empty msg}">
            <div class="warning-notice">
                <i class="ace-icon fa fa-info-circle"></i>
                ${msg}
            </div>
        </c:if>
        
        <c:if test="${empty msg}">
            <!-- 选择模式 -->
            <div class="selection-mode">
                <button class="mode-button active" id="planBtn" onclick="switchMode('faxz');">
                    <i class="ace-icon fa fa-sitemap"></i>
                    <span>按培养方案选择</span>
                </button>
                <button class="mode-button" id="freeBtn" onclick="switchMode('zyxz');">
                    <i class="ace-icon fa fa-hand-paper-o"></i>
                    <span>自由选择</span>
                </button>
            </div>
            
            <!-- 查询条件 -->
            <div class="search-container">
                <div class="search-title">
                    <i class="ace-icon fa fa-search"></i>
                    查询条件
                </div>
                
                <form id="queryInfo" name="queryInfo" class="search-form">
                    <input type="hidden" name="tabType" id="tabType" value="faxz"/>
                    <input type="hidden" name="zxjxjhh" value="${xnxq}" />
                    
                    <div class="form-group">
                        <label class="form-label">课程号</label>
                        <input type="text" name="kch" id="kch" class="form-input" placeholder="请输入课程号">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">课程名</label>
                        <input type="text" name="kcm" id="kcm" class="form-input" placeholder="请输入课程名">
                    </div>
                </form>
                
                <div class="search-actions">
                    <button class="btn-mobile btn-search" onclick="searchCourses();">
                        <i class="ace-icon fa fa-search"></i>
                        <span>查询</span>
                    </button>
                </div>
            </div>
            
            <!-- 课程列表 -->
            <div id="courseList">
                <!-- 动态加载内容 -->
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-flask"></i>
            <div>暂无实验课程</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentMode = 'faxz';
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let searchParams = '';
        let kzxxmsl = '${xjkd.kzxxmsl}';

        $(function() {
            initPage();
            if ("${msg}" == "" || "${msg}" == null || "${msg}" == undefined) {
                loadCourseList(1, true);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换选择模式
        function switchMode(mode) {
            currentMode = mode;
            $('#tabType').val(mode);

            // 更新按钮状态
            $('.mode-button').removeClass('active');
            if (mode === 'faxz') {
                $('#planBtn').addClass('active');
            } else {
                $('#freeBtn').addClass('active');
            }

            // 重新加载数据
            loadCourseList(1, true);
        }

        // 搜索课程
        function searchCourses() {
            loadCourseList(1, true);
        }

        // 加载课程列表
        function loadCourseList(page, conditionChanged) {
            if (conditionChanged) {
                searchParams = $(document.queryInfo).serialize();
                currentPage = 1;
                hasMore = true;
            }

            if (!hasMore && page > currentPage) {
                return;
            }

            showLoading(true);

            const url = "/student/experiment/choseProj/findChoseCourses";

            $.ajax({
                url: url,
                cache: false,
                type: "get",
                data: searchParams + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    if (response && response.data) {
                        const data = response.data;
                        totalCount = data.pageContext ? data.pageContext.totalCount : 0;

                        if (data.records && data.records.length > 0) {
                            renderCourseList(data.records, page === 1);
                            currentPage = page;
                            hasMore = (page * pageSize) < totalCount;
                        } else {
                            if (page === 1) {
                                showEmptyState();
                            }
                        }
                    } else {
                        if (page === 1) {
                            showEmptyState();
                        }
                    }
                },
                error: function(xhr) {
                    showError("获取数据失败，请重试");
                    console.error("Error:", xhr);
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染课程列表
        function renderCourseList(courses, isFirstPage) {
            const container = $('#courseList');

            if (isFirstPage) {
                container.empty();
                $('#emptyState').hide();
            }

            courses.forEach(function(course, index) {
                const courseHtml = createCourseItem(course, index);
                container.append(courseHtml);
            });
        }

        // 创建课程项目HTML
        function createCourseItem(course, index) {
            const canSelect = parseInt(course.ZXMS) > 0;
            const progressPercent = getProgressPercent(course.YXXMS, course.ZXMS);

            return `
                <div class="course-item">
                    <div class="course-header">
                        <div class="course-title">
                            ${course.KCM || ''}
                            <div class="course-code">课程号：${course.KCH || ''} | 课序号：${course.KXH || ''}</div>
                        </div>
                        <button class="btn-select-project"
                                ${canSelect ? '' : 'disabled'}
                                onclick="${canSelect ? `selectProject('${course.ID}','${course.ZXMS}','${course.ZSXMS}','${course.YXXMS}','${course.KCH}','${course.KCM}','${course.KXH}')` : 'void(0)'}">
                            <i class="ace-icon fa fa-check"></i>
                            <span>${canSelect ? '选择项目' : '无可选项目'}</span>
                        </button>
                    </div>

                    <div class="course-stats">
                        <div class="stat-item">
                            <div class="stat-value">${course.ZXMS || 0}</div>
                            <div class="stat-label">可选项目</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${course.YXXMS || 0}</div>
                            <div class="stat-label">已选项目</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${progressPercent}</div>
                            <div class="stat-label">完成进度</div>
                        </div>
                    </div>

                    <div class="progress-section">
                        <div class="progress-title">
                            <span>选择进度</span>
                            <span class="progress-percentage">${progressPercent}</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: ${progressPercent}">
                                <div class="progress-text">${course.YXXMS || 0}/${course.ZXMS || 0}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 计算进度百分比
        function getProgressPercent(selected, total) {
            const num = parseFloat(selected || 0);
            const totalNum = parseFloat(total || 0);

            if (isNaN(num) || isNaN(totalNum) || totalNum <= 0) {
                return "0%";
            }

            return Math.round(num / totalNum * 10000) / 100.00 + "%";
        }

        // 选择项目
        function selectProject(id, zxms, zsxms, yxxms, kch, kcm, kxh) {
            const xxxms = "${xjkd.kzxxmsl}";
            const xkjd = "${xjkd.id.xkjd}";

            const url = "/student/experiment/choseProj/choseView?" +
                       "syrwId=" + encode64(id) +
                       "&zxms=" + encode64(zxms) +
                       "&zsxms=" + encode64(zsxms) +
                       "&yxxms=" + encode64(yxxms) +
                       "&xxxms=" + encode64(xxxms) +
                       "&kch=" + encode64(kch) +
                       "&kxh=" + encode64(kxh);

            if (parent && parent.addTab) {
                parent.addTab('选择实验项目', url);
            } else {
                window.location.href = url;
            }
        }

        // Base64编码
        function encode64(input) {
            const keyStr = "ABCDEFGHIJKLMNOP" + "QRSTUVWXYZabcdef" + "ghijklmnopqrstuv" + "wxyz0123456789+/" + "=";
            let output = "";
            let chr1, chr2, chr3 = "";
            let enc1, enc2, enc3, enc4 = "";
            let i = 0;

            do {
                chr1 = input.charCodeAt(i++);
                chr2 = input.charCodeAt(i++);
                chr3 = input.charCodeAt(i++);
                enc1 = chr1 >> 2;
                enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
                enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
                enc4 = chr3 & 63;

                if (isNaN(chr2)) {
                    enc3 = enc4 = 64;
                } else if (isNaN(chr3)) {
                    enc4 = 64;
                }

                output = output +
                        keyStr.charAt(enc1) +
                        keyStr.charAt(enc2) +
                        keyStr.charAt(enc3) +
                        keyStr.charAt(enc4);
                chr1 = chr2 = chr3 = "";
                enc1 = enc2 = enc3 = enc4 = "";
            } while (i < input.length);

            return output;
        }

        // 显示空状态
        function showEmptyState() {
            $('#courseList').empty();
            $('#emptyState').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 刷新数据
        function refreshData() {
            loadCourseList(1, true);
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 滚动加载更多
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    loadCourseList(currentPage + 1, false);
                }
            }
        });
    </script>
</body>
</html>
