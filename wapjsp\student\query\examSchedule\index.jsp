<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>考试安排查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 考试安排查询页面样式 */
        .exam-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .exam-filter {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .filter-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .filter-tabs {
            display: flex;
            gap: var(--spacing-xs);
            overflow-x: auto;
            padding-bottom: var(--padding-xs);
        }
        
        .filter-tab {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            white-space: nowrap;
            transition: all var(--transition-base);
        }
        
        .filter-tab:hover {
            background: var(--primary-light);
        }
        
        .filter-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .exam-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .exam-summary.show {
            display: block;
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }
        
        .stat-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-h4);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-number.total {
            color: var(--primary-color);
        }
        
        .stat-number.upcoming {
            color: var(--warning-color);
        }
        
        .stat-number.completed {
            color: var(--success-color);
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .exam-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .exam-list.show {
            display: block;
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .sort-selector {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .exam-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .exam-item:last-child {
            border-bottom: none;
        }
        
        .exam-item:active {
            background: var(--bg-color-active);
        }
        
        .exam-item.upcoming {
            border-left: 4px solid var(--warning-color);
        }
        
        .exam-item.today {
            border-left: 4px solid var(--error-color);
        }
        
        .exam-item.completed {
            border-left: 4px solid var(--success-color);
        }
        
        .exam-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .exam-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-upcoming {
            background: var(--warning-color);
            color: white;
        }
        
        .status-today {
            background: var(--error-color);
            color: white;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .exam-time {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .time-icon {
            color: var(--primary-color);
            margin-right: var(--margin-sm);
            font-size: var(--font-size-base);
        }
        
        .time-info {
            flex: 1;
        }
        
        .time-date {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .time-period {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-location {
            background: var(--info-light);
            color: var(--info-color);
            border-radius: 6px;
            padding: var(--padding-xs) var(--padding-sm);
            font-size: var(--font-size-small);
            font-weight: 500;
            text-align: center;
            margin-bottom: var(--margin-md);
        }
        
        .exam-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-remind {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .exam-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .exam-detail-modal.show {
            display: flex;
        }
        
        .exam-detail-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .exam-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .exam-detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .exam-detail-close {
            color: var(--text-secondary);
            cursor: pointer;
            font-size: var(--font-size-h4);
        }
        
        .exam-detail-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .detail-section {
            margin-bottom: var(--margin-md);
        }
        
        .detail-section:last-child {
            margin-bottom: 0;
        }
        
        .detail-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .countdown-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .countdown-container.show {
            display: block;
        }
        
        .countdown-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .countdown-display {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .countdown-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .countdown-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .countdown-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .countdown-exam {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .countdown-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .countdown-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">考试安排查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="exam-header">
            <div class="header-title">考试安排查询</div>
            <div class="header-subtitle">查看考试时间、地点和相关信息</div>
        </div>

        <!-- 筛选器 -->
        <div class="exam-filter">
            <div class="filter-title">
                <i class="ace-icon fa fa-filter"></i>
                <span>考试筛选</span>
            </div>

            <div class="filter-tabs">
                <div class="filter-tab active" data-filter="all" onclick="filterExams('all')">全部考试</div>
                <div class="filter-tab" data-filter="upcoming" onclick="filterExams('upcoming')">即将考试</div>
                <div class="filter-tab" data-filter="today" onclick="filterExams('today')">今日考试</div>
                <div class="filter-tab" data-filter="completed" onclick="filterExams('completed')">已完成</div>
                <div class="filter-tab" data-filter="midterm" onclick="filterExams('midterm')">期中考试</div>
                <div class="filter-tab" data-filter="final" onclick="filterExams('final')">期末考试</div>
            </div>
        </div>

        <!-- 考试统计 -->
        <div class="exam-summary" id="examSummary">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>考试统计</span>
            </div>

            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-number total" id="totalExams">0</div>
                    <div class="stat-label">总考试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number upcoming" id="upcomingExams">0</div>
                    <div class="stat-label">即将考试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number completed" id="completedExams">0</div>
                    <div class="stat-label">已完成</div>
                </div>
            </div>
        </div>

        <!-- 倒计时 -->
        <div class="countdown-container" id="countdownContainer">
            <div class="countdown-title">距离下次考试</div>

            <div class="countdown-display">
                <div class="countdown-item">
                    <div class="countdown-number" id="countdownDays">0</div>
                    <div class="countdown-label">天</div>
                </div>
                <div class="countdown-item">
                    <div class="countdown-number" id="countdownHours">0</div>
                    <div class="countdown-label">时</div>
                </div>
                <div class="countdown-item">
                    <div class="countdown-number" id="countdownMinutes">0</div>
                    <div class="countdown-label">分</div>
                </div>
                <div class="countdown-item">
                    <div class="countdown-number" id="countdownSeconds">0</div>
                    <div class="countdown-label">秒</div>
                </div>
            </div>

            <div class="countdown-exam">
                <div class="countdown-course" id="nextExamCourse">-</div>
                <div class="countdown-time" id="nextExamTime">-</div>
            </div>
        </div>

        <!-- 考试列表 -->
        <div class="exam-list" id="examList">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span>考试安排</span>
                </div>
                <div class="sort-selector" onclick="toggleSort();">
                    <i class="ace-icon fa fa-sort"></i>
                    <span id="sortText">按时间排序</span>
                </div>
            </div>

            <div id="examItems">
                <!-- 考试列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-calendar-o"></i>
            <div id="emptyMessage">暂无考试安排</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 考试详情模态框 -->
    <div class="exam-detail-modal" id="examDetailModal">
        <div class="exam-detail-content">
            <div class="exam-detail-header">
                <div class="exam-detail-title" id="examDetailTitle">考试详情</div>
                <div class="exam-detail-close" onclick="closeExamDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="exam-detail-body" id="examDetailBody">
                <!-- 考试详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allExams = [];
        let filteredExams = [];
        let currentFilter = 'all';
        let currentSort = 'time';
        let countdownInterval = null;

        $(function() {
            initPage();
            loadExamData();
            startCountdown();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载考试数据
        function loadExamData() {
            showLoading(true);

            $.ajax({
                url: "/student/query/examSchedule/getExamData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allExams = data.exams || [];
                    filteredExams = [...allExams];

                    updateExamSummary();
                    renderExamList();
                    showResults();
                    updateCountdown();

                    showLoading(false);
                },
                error: function() {
                    showError('加载考试数据失败');
                    showLoading(false);
                }
            });
        }

        // 更新考试统计
        function updateExamSummary() {
            const total = allExams.length;
            const upcoming = allExams.filter(exam => getExamStatus(exam) === 'upcoming').length;
            const completed = allExams.filter(exam => getExamStatus(exam) === 'completed').length;

            $('#totalExams').text(total);
            $('#upcomingExams').text(upcoming);
            $('#completedExams').text(completed);
        }

        // 筛选考试
        function filterExams(filter) {
            currentFilter = filter;

            // 更新标签状态
            $('.filter-tab').removeClass('active');
            $(`.filter-tab[data-filter="${filter}"]`).addClass('active');

            // 应用筛选
            applyFilter();
        }

        // 应用筛选
        function applyFilter() {
            if (currentFilter === 'all') {
                filteredExams = [...allExams];
            } else if (currentFilter === 'midterm' || currentFilter === 'final') {
                filteredExams = allExams.filter(exam => exam.type === currentFilter);
            } else {
                filteredExams = allExams.filter(exam => getExamStatus(exam) === currentFilter);
            }

            applySorting();
            renderExamList();
        }

        // 切换排序
        function toggleSort() {
            if (currentSort === 'time') {
                currentSort = 'course';
                $('#sortText').text('按课程排序');
            } else {
                currentSort = 'time';
                $('#sortText').text('按时间排序');
            }

            applySorting();
            renderExamList();
        }

        // 应用排序
        function applySorting() {
            if (currentSort === 'time') {
                filteredExams.sort((a, b) => new Date(a.examTime) - new Date(b.examTime));
            } else {
                filteredExams.sort((a, b) => a.courseName.localeCompare(b.courseName));
            }
        }

        // 渲染考试列表
        function renderExamList() {
            const container = $('#examItems');
            container.empty();

            if (filteredExams.length === 0) {
                showEmptyState('暂无符合条件的考试');
                return;
            } else {
                hideEmptyState();
            }

            filteredExams.forEach(exam => {
                const examHtml = createExamItem(exam);
                container.append(examHtml);
            });
        }

        // 创建考试项
        function createExamItem(exam) {
            const status = getExamStatus(exam);
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);

            return `
                <div class="exam-item ${status}" onclick="showExamDetail('${exam.id}')">
                    <div class="exam-basic">
                        <div class="exam-course">${exam.courseName}</div>
                        <div class="exam-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="exam-time">
                        <div class="time-icon">
                            <i class="ace-icon fa fa-clock-o"></i>
                        </div>
                        <div class="time-info">
                            <div class="time-date">${formatDate(exam.examTime)}</div>
                            <div class="time-period">${formatTime(exam.examTime)} - ${formatTime(exam.endTime)}</div>
                        </div>
                    </div>
                    <div class="exam-details">
                        <div class="detail-item">
                            <span>考试类型:</span>
                            <span>${getExamTypeText(exam.type)}</span>
                        </div>
                        <div class="detail-item">
                            <span>考试形式:</span>
                            <span>${exam.format || '笔试'}</span>
                        </div>
                        <div class="detail-item">
                            <span>座位号:</span>
                            <span>${exam.seatNumber || '待安排'}</span>
                        </div>
                        <div class="detail-item">
                            <span>考试时长:</span>
                            <span>${exam.duration || 120}分钟</span>
                        </div>
                    </div>
                    <div class="exam-location">
                        <i class="ace-icon fa fa-map-marker"></i>
                        ${exam.location || '待安排'}
                    </div>
                    <div class="exam-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showExamDetail('${exam.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'upcoming' ? `
                            <button class="btn-mobile btn-remind" onclick="event.stopPropagation(); setReminder('${exam.id}');">
                                <i class="ace-icon fa fa-bell"></i>
                                <span>提醒</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-bell-slash"></i>
                                <span>已过期</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取考试状态
        function getExamStatus(exam) {
            const now = new Date();
            const examTime = new Date(exam.examTime);
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const examDate = new Date(examTime.getFullYear(), examTime.getMonth(), examTime.getDate());

            if (examTime < now) {
                return 'completed';
            } else if (examDate.getTime() === today.getTime()) {
                return 'today';
            } else {
                return 'upcoming';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'upcoming': return '即将考试';
                case 'today': return '今日考试';
                case 'completed': return '已完成';
                default: return '未知';
            }
        }

        // 获取考试类型文本
        function getExamTypeText(type) {
            switch(type) {
                case 'midterm': return '期中考试';
                case 'final': return '期末考试';
                case 'makeup': return '补考';
                case 'retake': return '重修考试';
                default: return '普通考试';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()];
            return `${year}-${month}-${day} ${weekDay}`;
        }

        // 格式化时间
        function formatTime(dateStr) {
            const date = new Date(dateStr);
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${hours}:${minutes}`;
        }

        // 显示考试详情
        function showExamDetail(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            let detailHtml = `
                <div class="detail-section">
                    <div class="detail-section-title">考试信息</div>
                    <div class="detail-item">
                        <span class="detail-label">课程名称:</span>
                        <span class="detail-value">${exam.courseName}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">课程代码:</span>
                        <span class="detail-value">${exam.courseCode}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试类型:</span>
                        <span class="detail-value">${getExamTypeText(exam.type)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试形式:</span>
                        <span class="detail-value">${exam.format || '笔试'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试时长:</span>
                        <span class="detail-value">${exam.duration || 120}分钟</span>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-section-title">时间地点</div>
                    <div class="detail-item">
                        <span class="detail-label">考试日期:</span>
                        <span class="detail-value">${formatDate(exam.examTime)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试时间:</span>
                        <span class="detail-value">${formatTime(exam.examTime)} - ${formatTime(exam.endTime)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">考试地点:</span>
                        <span class="detail-value">${exam.location || '待安排'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">座位号:</span>
                        <span class="detail-value">${exam.seatNumber || '待安排'}</span>
                    </div>
                </div>
            `;

            if (exam.teacher) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">监考信息</div>
                        <div class="detail-item">
                            <span class="detail-label">监考教师:</span>
                            <span class="detail-value">${exam.teacher}</span>
                        </div>
                    </div>
                `;
            }

            if (exam.note) {
                detailHtml += `
                    <div class="detail-section">
                        <div class="detail-section-title">考试须知</div>
                        <div style="line-height: 1.6;">${exam.note}</div>
                    </div>
                `;
            }

            $('#examDetailTitle').text(`${exam.courseName} - 考试详情`);
            $('#examDetailBody').html(detailHtml);
            $('#examDetailModal').addClass('show');
        }

        // 关闭考试详情
        function closeExamDetail() {
            $('#examDetailModal').removeClass('show');
        }

        // 设置提醒
        function setReminder(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            const message = `确定要为"${exam.courseName}"考试设置提醒吗？\n\n考试时间：${formatDate(exam.examTime)} ${formatTime(exam.examTime)}`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSetReminder(examId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSetReminder(examId);
                }
            }
        }

        // 执行设置提醒
        function doSetReminder(examId) {
            $.ajax({
                url: "/student/query/examSchedule/setReminder",
                type: "post",
                data: { examId: examId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('考试提醒设置成功');
                    } else {
                        showError(data.message || '设置提醒失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 开始倒计时
        function startCountdown() {
            updateCountdown();
            countdownInterval = setInterval(updateCountdown, 1000);
        }

        // 更新倒计时
        function updateCountdown() {
            const nextExam = getNextExam();

            if (!nextExam) {
                hideCountdown();
                return;
            }

            const now = new Date();
            const examTime = new Date(nextExam.examTime);
            const timeDiff = examTime - now;

            if (timeDiff <= 0) {
                hideCountdown();
                return;
            }

            const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

            $('#countdownDays').text(days);
            $('#countdownHours').text(hours);
            $('#countdownMinutes').text(minutes);
            $('#countdownSeconds').text(seconds);

            $('#nextExamCourse').text(nextExam.courseName);
            $('#nextExamTime').text(`${formatDate(nextExam.examTime)} ${formatTime(nextExam.examTime)}`);

            showCountdown();
        }

        // 获取下次考试
        function getNextExam() {
            const now = new Date();
            const upcomingExams = allExams.filter(exam => new Date(exam.examTime) > now);

            if (upcomingExams.length === 0) return null;

            upcomingExams.sort((a, b) => new Date(a.examTime) - new Date(b.examTime));
            return upcomingExams[0];
        }

        // 显示倒计时
        function showCountdown() {
            $('#countdownContainer').addClass('show');
        }

        // 隐藏倒计时
        function hideCountdown() {
            $('#countdownContainer').removeClass('show');
        }

        // 显示结果
        function showResults() {
            $('#examSummary').addClass('show');
            $('#examList').addClass('show');
        }

        // 刷新数据
        function refreshData() {
            loadExamData();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('#examList').removeClass('show');
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('#examList').addClass('show');
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#examDetailModal').click(function(e) {
            if (e.target === this) {
                closeExamDetail();
            }
        });

        // 页面卸载时清除定时器
        $(window).on('beforeunload', function() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
        });
    </script>
</body>
</html>
