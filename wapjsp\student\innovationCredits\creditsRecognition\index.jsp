<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>认定学分申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 认定学分申请页面样式 */
        .warning-notice {
            background: var(--error-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .standards-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            max-height: 300px;
            overflow-y: auto;
        }
        
        .section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .section-header i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .standard-item {
            padding: var(--padding-sm) var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .standard-item:hover {
            background: var(--bg-tertiary);
        }
        
        .standard-item.selected {
            background: var(--primary-color);
            color: white;
        }
        
        .standard-item:last-child {
            border-bottom: none;
        }
        
        .standard-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .standard-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .standard-item.selected .standard-desc {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .application-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .application-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
            padding-top: var(--padding-sm);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-action {
            flex: 1;
            min-height: 36px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-edit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-upload {
            background: var(--info-color);
            color: white;
        }
        
        .btn-view {
            background: var(--success-color);
            color: white;
        }
        
        .fab-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            z-index: 100;
            transition: all var(--transition-base);
        }
        
        .fab-button:active {
            transform: scale(0.95);
        }
        
        .description-text {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .tabs-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            display: flex;
            background: var(--bg-tertiary);
        }
        
        .tab-button {
            flex: 1;
            padding: var(--padding-md);
            background: transparent;
            border: none;
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .tab-button.active {
            background: var(--bg-primary);
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: var(--padding-md);
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            width: 100%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: var(--font-size-h4);
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: var(--padding-md);
        }

        .modal-footer {
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }

        .form-group {
            margin-bottom: var(--margin-md);
        }

        .form-group label {
            display: block;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .form-value {
            padding: 8px 12px;
            background: var(--bg-tertiary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .radio-group {
            display: flex;
            gap: var(--spacing-md);
        }

        .radio-label {
            display: flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }

        .radio-label input[type="radio"] {
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">认定学分申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 警告提示 -->
        <c:if test="${schoolCode == '100006'}">
            <div class="warning-notice">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <strong>注意：</strong>申请完成后请及时上传附件，否则审批不通过！
            </div>
        </c:if>
        
        <!-- 选项卡 -->
        <div class="tabs-container">
            <div class="tabs-header">
                <button class="tab-button active" onclick="switchTab('standards')">
                    <i class="ace-icon fa fa-list"></i>
                    认定标准
                </button>
                <button class="tab-button" onclick="switchTab('applications')">
                    <i class="ace-icon fa fa-file-text"></i>
                    我的申请
                </button>
            </div>
            
            <div class="tab-content">
                <!-- 认定标准 -->
                <div class="tab-pane active" id="standardsTab">
                    <!-- 搜索框 -->
                    <div class="search-section">
                        <input type="text" class="search-input" id="searchInput" 
                               placeholder="请输入关键词搜索认定标准..." 
                               onkeypress="handleSearchKeyPress(event)">
                    </div>
                    
                    <!-- 认定标准列表 -->
                    <div class="standards-section">
                        <div class="section-header">
                            <i class="ace-icon fa fa-list"></i>
                            认定标准列表
                        </div>
                        <div id="standardsList">
                            <!-- 动态加载认定标准 -->
                        </div>
                    </div>
                </div>
                
                <!-- 我的申请 -->
                <div class="tab-pane" id="applicationsTab">
                    <div id="applicationsList">
                        <!-- 动态加载申请列表 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div id="emptyMessage">暂无数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 浮动添加按钮 -->
        <button class="fab-button" onclick="addApplication();" id="fabButton">
            <i class="ace-icon fa fa-plus"></i>
        </button>
    </div>

    <script>
        // 全局变量
        let standardsData = [];
        let applicationsData = [];
        let currentTab = 'standards';
        let selectedStandard = null;
        let currentPage = 1;
        let hasMore = true;
        let tokenValue = '${token_in_session}';
        let schoolCode = '${schoolCode}';
        let xnxq = '${xnxq}';

        $(function() {
            initPage();
            loadStandards();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换选项卡
        function switchTab(tab) {
            currentTab = tab;

            // 更新按钮状态
            $('.tab-button').removeClass('active');
            $(`.tab-button:contains('${tab === 'standards' ? '认定标准' : '我的申请'}')`).addClass('active');

            // 更新内容显示
            $('.tab-pane').removeClass('active');
            if (tab === 'standards') {
                $('#standardsTab').addClass('active');
                showFabButton(false);
                if (standardsData.length === 0) {
                    loadStandards();
                }
            } else {
                $('#applicationsTab').addClass('active');
                showFabButton(true);
                if (applicationsData.length === 0) {
                    loadApplications();
                }
            }
        }

        // 加载认定标准
        function loadStandards() {
            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/creditsRecognitionApply/queryTree",
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data && data.length > 0) {
                        standardsData = data;
                        renderStandardsList();
                        showEmptyState(false);
                    } else {
                        standardsData = [];
                        renderStandardsList();
                        showEmptyState(true, '暂无认定标准');
                    }
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染认定标准列表
        function renderStandardsList() {
            const container = $('#standardsList');
            container.empty();

            if (standardsData.length === 0) {
                showEmptyState(true, '暂无认定标准');
                return;
            }

            // 过滤和渲染标准
            const searchTerm = $('#searchInput').val().toLowerCase();
            const filteredStandards = standardsData.filter(item => {
                if (item.type !== 'xfrd') return false; // 只显示学分认定标准
                if (!searchTerm) return true;
                return item.name.toLowerCase().includes(searchTerm);
            });

            filteredStandards.forEach(function(item, index) {
                const itemHtml = createStandardItem(item, index);
                container.append(itemHtml);
            });

            if (filteredStandards.length === 0) {
                container.append('<div class="empty-state" style="padding: 40px; text-align: center; color: var(--text-secondary);">未找到匹配的认定标准</div>');
            }
        }

        // 创建认定标准项目HTML
        function createStandardItem(item, index) {
            return `
                <div class="standard-item" data-id="${item.nodeId}" onclick="selectStandard('${item.nodeId}', '${item.name}', this);">
                    <div class="standard-name">${item.name}</div>
                    ${item.title ? `<div class="standard-desc">${item.title}</div>` : ''}
                </div>
            `;
        }

        // 选择认定标准
        function selectStandard(id, name, element) {
            // 移除之前的选中状态
            $('.standard-item').removeClass('selected');

            // 设置当前选中状态
            $(element).addClass('selected');

            selectedStandard = {
                id: id,
                name: name
            };

            // 提示用户可以添加申请
            showSuccess('已选择认定标准：' + name + '，点击右下角按钮添加申请');

            // 切换到申请页面并显示添加按钮
            switchTab('applications');
        }

        // 搜索处理
        function handleSearchKeyPress(event) {
            if (event.keyCode === 13) {
                renderStandardsList();
            }
        }

        // 加载申请列表
        function loadApplications(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/creditsRecognitionApply/queryPage",
                type: "post",
                data: "pageNum=" + page + "&pageSize=20",
                dataType: "json",
                success: function(data) {
                    if (data.records && data.records.length > 0) {
                        if (reset) {
                            applicationsData = data.records;
                        } else {
                            applicationsData = applicationsData.concat(data.records);
                        }

                        hasMore = applicationsData.length < data.pageContext.totalCount;
                        renderApplicationsList();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            applicationsData = [];
                            renderApplicationsList();
                        }
                        showEmptyState(true, '暂无申请记录');
                    }
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationsList() {
            const container = $('#applicationsList');
            container.empty();

            applicationsData.forEach(function(item, index) {
                const itemHtml = createApplicationItem(item, index);
                container.append(itemHtml);
            });
        }

        // 创建申请项目HTML
        function createApplicationItem(item, index) {
            const canEdit = item.YXSPJL === "empty" && item.JWCSPJL === "empty";
            const hasAttachment = item.FJNUM > 0;
            const creditType = item.KCSXDM === "001" ? "必修" : (item.KCSXDM === "003" ? "任选" : "");

            return `
                <div class="application-item">
                    <div class="application-header">
                        <div class="application-title">申请 #${index + 1}</div>
                        <div class="status-badge ${canEdit ? 'status-pending' : 'status-approved'}">
                            ${canEdit ? '待审批' : '已审批'}
                        </div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item full-width">
                            <span class="detail-label">认定标准</span>
                            <span class="detail-value">${item.BZMC || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">成绩</span>
                            <span class="detail-value">${item.CJ || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">学分属性</span>
                            <span class="detail-value">${creditType}</span>
                        </div>
                        <div class="detail-item full-width">
                            <span class="detail-label">申请说明</span>
                            <span class="detail-value">${item.SQSM || '-'}</span>
                        </div>
                    </div>

                    ${item.SQSM ? `
                        <div class="description-text">
                            ${item.SQSM}
                        </div>
                    ` : ''}

                    <div class="action-buttons">
                        ${canEdit ? `
                            <button class="btn-action btn-edit" onclick="editApplication('${item.ID}');">
                                <i class="ace-icon fa fa-edit"></i>
                                <span>修改</span>
                            </button>
                            <button class="btn-action btn-delete" onclick="deleteApplication('${item.ID}');">
                                <i class="ace-icon fa fa-trash"></i>
                                <span>删除</span>
                            </button>
                        ` : ''}

                        <button class="btn-action btn-upload" onclick="manageAttachment('${item.ID}');">
                            <i class="ace-icon fa fa-${hasAttachment ? 'eye' : 'upload'}"></i>
                            <span>${hasAttachment ? '查看附件' : '上传附件'}</span>
                        </button>

                        ${!canEdit ? `
                            <button class="btn-action btn-view" onclick="viewApprovalDetails('${item.ID}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>审批详情</span>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 添加申请
        function addApplication() {
            if (!selectedStandard) {
                showError('请先在认定标准页面选择一个认定标准');
                switchTab('standards');
                return;
            }

            // 检查申请权限
            $.ajax({
                url: "/student/innovationCredits/creditsRecognitionApply/beforAdd",
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data.result === "ok") {
                        openAddDialog();
                    } else if (data.result === "no2") {
                        showError("不在申请时间范围，不能申请");
                    } else {
                        showError("申请开关已关闭，不能申请");
                    }
                },
                error: function() {
                    showError("检查申请权限失败");
                }
            });
        }

        // 打开添加对话框
        function openAddDialog() {
            const dialogHtml = `
                <div class="modal-overlay" id="addDialog" onclick="closeAddDialog();">
                    <div class="modal-content" onclick="event.stopPropagation();">
                        <div class="modal-header">
                            <h3>添加认定学分申请</h3>
                            <button class="modal-close" onclick="closeAddDialog();">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>认定标准</label>
                                <div class="form-value">${selectedStandard.name}</div>
                            </div>
                            <div class="form-group">
                                <label>成绩</label>
                                <input type="text" id="scoreInput" class="form-input" placeholder="请输入成绩">
                            </div>
                            <div class="form-group">
                                <label>学分属性</label>
                                <div class="radio-group">
                                    <label class="radio-label">
                                        <input type="radio" name="creditType" value="001" checked>
                                        <span>必修</span>
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="creditType" value="003">
                                        <span>任选</span>
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>申请说明</label>
                                <textarea id="descriptionInput" class="form-textarea" placeholder="请输入申请说明（最多200字符）" maxlength="200"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn-mobile btn-secondary" onclick="closeAddDialog();">取消</button>
                            <button class="btn-mobile btn-primary" onclick="submitApplication();">提交</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(dialogHtml);
        }

        // 关闭添加对话框
        function closeAddDialog() {
            $('#addDialog').remove();
        }

        // 提交申请
        function submitApplication() {
            const score = $('#scoreInput').val().trim();
            const creditType = $('input[name="creditType"]:checked').val();
            const description = $('#descriptionInput').val().trim();

            if (!score) {
                showError('请输入成绩');
                return;
            }

            if (!description) {
                showError('请输入申请说明');
                return;
            }

            if (description.length > 200) {
                showError('申请说明不能超过200个字符');
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/creditsRecognitionApply/doSave",
                type: "post",
                data: {
                    tokenValue: tokenValue,
                    bzid: selectedStandard.id,
                    cj: score,
                    kcsxdm: creditType,
                    sqsm: description,
                    type: 'add'
                },
                dataType: "json",
                success: function(data) {
                    tokenValue = data.token;
                    if (data.result === "ok") {
                        showSuccess("申请提交成功！");
                        closeAddDialog();
                        loadApplications(1, true);
                    } else {
                        showError(data.result || "提交失败");
                    }
                },
                error: function() {
                    showError("提交失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 编辑申请
        function editApplication(id) {
            // 找到对应的申请数据
            const application = applicationsData.find(item => item.ID === id);
            if (!application) {
                showError('申请数据不存在');
                return;
            }

            const dialogHtml = `
                <div class="modal-overlay" id="editDialog" onclick="closeEditDialog();">
                    <div class="modal-content" onclick="event.stopPropagation();">
                        <div class="modal-header">
                            <h3>修改认定学分申请</h3>
                            <button class="modal-close" onclick="closeEditDialog();">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>认定标准</label>
                                <div class="form-value">${application.BZMC || '-'}</div>
                            </div>
                            <div class="form-group">
                                <label>成绩</label>
                                <input type="text" id="editScoreInput" class="form-input" value="${application.CJ || ''}" placeholder="请输入成绩">
                            </div>
                            <div class="form-group">
                                <label>学分属性</label>
                                <div class="radio-group">
                                    <label class="radio-label">
                                        <input type="radio" name="editCreditType" value="001" ${application.KCSXDM === '001' ? 'checked' : ''}>
                                        <span>必修</span>
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="editCreditType" value="003" ${application.KCSXDM === '003' ? 'checked' : ''}>
                                        <span>任选</span>
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>申请说明</label>
                                <textarea id="editDescriptionInput" class="form-textarea" placeholder="请输入申请说明（最多200字符）" maxlength="200">${application.SQSM || ''}</textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn-mobile btn-secondary" onclick="closeEditDialog();">取消</button>
                            <button class="btn-mobile btn-primary" onclick="updateApplication('${id}');">保存</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(dialogHtml);
        }

        // 关闭编辑对话框
        function closeEditDialog() {
            $('#editDialog').remove();
        }

        // 更新申请
        function updateApplication(id) {
            const score = $('#editScoreInput').val().trim();
            const creditType = $('input[name="editCreditType"]:checked').val();
            const description = $('#editDescriptionInput').val().trim();

            if (!score) {
                showError('请输入成绩');
                return;
            }

            if (!description) {
                showError('请输入申请说明');
                return;
            }

            if (description.length > 200) {
                showError('申请说明不能超过200个字符');
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/creditsRecognitionApply/doSave",
                type: "post",
                data: {
                    tokenValue: tokenValue,
                    id: id,
                    cj: score,
                    kcsxdm: creditType,
                    sqsm: description,
                    type: 'edit'
                },
                dataType: "json",
                success: function(data) {
                    tokenValue = data.token;
                    if (data.result === "ok") {
                        showSuccess("修改成功！");
                        closeEditDialog();
                        loadApplications(1, true);
                    } else {
                        showError(data.result || "修改失败");
                    }
                },
                error: function() {
                    showError("修改失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 删除申请
        function deleteApplication(id) {
            if (!confirm('确认删除当前申请？')) {
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/innovationCredits/creditsRecognitionApply/doDel",
                type: "post",
                data: {
                    tokenValue: tokenValue,
                    id: id
                },
                dataType: "json",
                success: function(data) {
                    tokenValue = data.token;
                    if (data.result === "ok") {
                        showSuccess("删除成功！");
                        loadApplications(1, true);
                    } else {
                        showError("删除失败！");
                    }
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 管理附件
        function manageAttachment(id) {
            if (parent && parent.addTab) {
                parent.addTab('附件管理', '/student/innovationCredits/creditsRecognitionApply/attachment?id=' + id);
            } else {
                window.location.href = '/student/innovationCredits/creditsRecognitionApply/attachment?id=' + id;
            }
        }

        // 查看审批详情
        function viewApprovalDetails(id) {
            if (parent && parent.addTab) {
                parent.addTab('审批详情', '/student/innovationCredits/creditsRecognitionApply/approvalDetails?id=' + id);
            } else {
                window.location.href = '/student/innovationCredits/creditsRecognitionApply/approvalDetails?id=' + id;
            }
        }

        // 刷新数据
        function refreshData() {
            if (currentTab === 'standards') {
                loadStandards();
            } else {
                loadApplications(1, true);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show, message = '暂无数据') {
            if (show) {
                $('#emptyMessage').text(message);
                $('#emptyState').show();
            } else {
                $('#emptyState').hide();
            }
        }

        // 显示浮动按钮
        function showFabButton(show) {
            if (show) {
                $('#fabButton').show();
            } else {
                $('#fabButton').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 无限滚动加载
        $(window).scroll(function() {
            if (currentTab === 'applications' && $(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    currentPage++;
                    loadApplications(currentPage, false);
                }
            }
        });
    </script>
</body>
</html>
