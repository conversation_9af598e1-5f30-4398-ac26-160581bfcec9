<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>查看论文指导过程</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 论文指导过程页面样式 */
        .guidance-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .guidance-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .guidance-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .actions-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .actions-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-export {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .guidance-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .guidance-section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .guidance-section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .guidance-section-title i {
            color: var(--primary-color);
        }
        
        .guidance-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .guidance-item:last-child {
            border-bottom: none;
        }
        
        .guidance-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .guidance-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .guidance-content {
            flex: 1;
        }
        
        .guidance-title-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .guidance-teacher {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .guidance-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            align-items: flex-start;
        }
        
        .detail-label {
            font-weight: 500;
            min-width: 80px;
            flex-shrink: 0;
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
            word-break: break-word;
            line-height: 1.4;
        }
        
        .detail-value.long-text {
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .btn-operation {
            flex: 1;
            min-width: 60px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .guidance-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .guidance-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .guidance-modal-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .guidance-modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .guidance-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .guidance-modal-body {
            padding: var(--padding-md);
            max-height: 60vh;
            overflow-y: auto;
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .guidance-details {
                grid-template-columns: 1fr;
            }
            
            .guidance-modal-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">查看论文指导过程</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 论文指导过程头部 -->
        <div class="guidance-header">
            <div class="guidance-title">查看论文指导过程</div>
            <div class="guidance-desc">查看论文指导过程记录</div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions-section">
            <div class="actions-title">
                <i class="ace-icon fa fa-cogs"></i>
                操作
            </div>
            <div class="action-buttons">
                <button class="btn-mobile btn-export" onclick="exportGuidance();">
                    <i class="ace-icon fa fa-file-excel-o"></i>
                    <span>导出指导过程</span>
                </button>
            </div>
        </div>
        
        <!-- 指导过程列表 -->
        <div class="guidance-section">
            <div class="guidance-section-header">
                <div class="guidance-section-title">
                    <i class="ace-icon fa fa-list"></i>
                    指导过程列表
                </div>
            </div>
            
            <div id="guidanceList">
                <!-- 动态加载指导过程列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreGuidance();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无指导过程数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 指导详情模态框 -->
        <div class="guidance-modal" id="guidanceModal">
            <div class="guidance-modal-content">
                <div class="guidance-modal-header">
                    <div class="guidance-modal-title">指导详情</div>
                    <button class="guidance-close" onclick="closeGuidanceModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="guidance-modal-body" id="guidanceModalBody">
                    <!-- 动态加载指导详情内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let guidanceData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let csz = '${csz}';

        $(function() {
            initPage();
            loadGuidance(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载更多指导过程
        function loadMoreGuidance() {
            if (hasMore) {
                loadGuidance(currentPage + 1, false);
            }
        }

        // 加载指导过程数据
        function loadGuidance(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/thesisGuidanceProcess/index/queryAllGuidanceByStudents",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            guidanceData = data.records;
                        } else {
                            guidanceData = guidanceData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = guidanceData.length < totalCount;

                        renderGuidanceList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            guidanceData = [];
                            renderGuidanceList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染指导过程列表
        function renderGuidanceList(reset = false) {
            const container = $('#guidanceList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : guidanceData.length - pageSize;
            const endIndex = guidanceData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (guidanceData[i]) {
                    const itemHtml = createGuidanceItem(guidanceData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建指导过程项目HTML
        function createGuidanceItem(item, index) {
            // 构建详情项目
            let detailsHtml = '';

            // 指导地点
            if (csz.indexOf("ZDDD:1") !== -1) {
                detailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">指导地点</span>
                        <span class="detail-value">${item.ZDDD || ''}</span>
                    </div>
                `;
            }

            // 检查内容
            if (csz.indexOf("JCNR:1") !== -1) {
                detailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">检查内容</span>
                        <span class="detail-value long-text" title="${item.JCNR || ''}">${item.JCNR || ''}</span>
                    </div>
                `;
            }

            // 指导意见
            if (csz.indexOf("ZDNR:1") !== -1) {
                detailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">指导意见</span>
                        <span class="detail-value long-text" title="${item.ZDNR || ''}">${item.ZDNR || ''}</span>
                    </div>
                `;
            }

            detailsHtml += `
                <div class="detail-item">
                    <span class="detail-label">指导时间</span>
                    <span class="detail-value">${item.ZDSJ || ''}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">提交时间</span>
                    <span class="detail-value">${item.CZSJ || ''}</span>
                </div>
            `;

            return `
                <div class="guidance-item">
                    <div class="guidance-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="guidance-index">${index + 1}</div>
                            <div class="guidance-content">
                                <div class="guidance-title-text">${item.TMMC || ''}</div>
                                <div class="guidance-teacher">指导教师：${item.ZDJS || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="guidance-details">
                        ${detailsHtml}
                    </div>

                    <div class="operation-buttons">
                        <button class="btn-operation btn-view" onclick="viewGuidance('${item.ID}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看详情</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && guidanceData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (guidanceData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.guidance-section').hide();
            } else {
                $('#emptyState').hide();
                $('.guidance-section').show();
            }
        }

        // 查看指导详情
        function viewGuidance(id) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/thesisGuidanceProcess/index/seeGuidance/" + id,
                type: "get",
                success: function(response) {
                    $('#guidanceModalBody').html(response);
                    $('#guidanceModal').fadeIn(300);
                },
                error: function(xhr) {
                    showError("获取指导详情失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 关闭指导详情模态框
        function closeGuidanceModal() {
            $('#guidanceModal').fadeOut(300);
        }

        // 导出指导过程
        function exportGuidance() {
            const url = "/student/personalManagement/thesisGuidanceProcess/index/printZdgc";
            $('<div style="display:none;"><iframe src="' + url + '" frameborder="0" scrolling="no" id="myFrame"></iframe></div>').appendTo('body');
        }

        // 刷新数据
        function refreshData() {
            loadGuidance(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.guidance-modal', function(e) {
            if (e.target === this) {
                closeGuidanceModal();
            }
        });
    </script>
</body>
</html>
