package educationalAdministration.student.individualApplication.applyCommon.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Types;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Session;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import com.urpSoft.core.data.orm.jpql.param.Param;
import com.urpSoft.core.service.BaseService;
import com.urpSoft.core.util.AuthUtil;
import com.urpSoft.core.util.JSONUtils;

import educationalAdministration.dictionary.entity.CjMksqb;
import educationalAdministration.dictionary.entity.CodeCcqjb;
import educationalAdministration.dictionary.entity.CodeKcb;
import educationalAdministration.dictionary.entity.CodeTdlxb;
import educationalAdministration.dictionary.entity.EaApplys;
import educationalAdministration.dictionary.entity.EaProcess;
import educationalAdministration.dictionary.entity.EaProcessLink;
import educationalAdministration.dictionary.entity.EaResult;
import educationalAdministration.dictionary.entity.EaResultPK;
import educationalAdministration.dictionary.entity.SysColConfig;
import educationalAdministration.dictionary.entity.SysSqfjb;
import educationalAdministration.dictionary.entity.SysYwhdkzb;
import educationalAdministration.dictionary.entity.XkBxksqb;
import educationalAdministration.dictionary.entity.XkHksqb;
import educationalAdministration.dictionary.entity.XkMtsqb;
import educationalAdministration.dictionary.entity.XkTksqb;
import educationalAdministration.dictionary.entity.XkZxcxsqb;
import educationalAdministration.dictionary.entity.XsMxsqb;
import educationalAdministration.dictionary.entity.XsYwsqxzb;
import educationalAdministration.dictionary.entity.approve.CjCjrdsqb;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.courseSelectMangement.entity.XkCxXnxqView;
import educationalAdministration.student.individualApplication.applyCommon.dao.ApplyCommonDao;
import educationalAdministration.student.individualApplication.applyCommon.entity.CodeMxflb;
import educationalAdministration.student.individualApplication.applyCommon.entity.EaApplysQu;
import educationalAdministration.student.individualApplication.applyCommon.entity.EaResultQu;
import educationalAdministration.student.individualApplication.applyCommon.service.ApplyCommonService;
import educationalAdministration.student.individualApplication.creditCertification.entity.CodeXfrzlxb;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzJlxmb;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzXscjd;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzXskccj;
import educationalAdministration.student.individualApplication.creditCertification.entity.XfrzXssqb;
import educationalAdministration.student.individualApplication.routineWork.entity.CodeXsrcswb;
import educationalAdministration.student.individualApplication.routineWork.entity.XsRcswsqb;
import educationalAdministration.student.individualApplication.studentsInnovation.entity.ChxCodeChxxmlxb;
import educationalAdministration.student.individualApplication.studentsInnovation.entity.ChxCodeXmlyb;
import educationalAdministration.student.individualApplication.studentsInnovation.entity.ChxTmyqsqb;
import educationalAdministration.student.individualApplication.studentsInnovation.entity.ChxTmzzsqb;
import educationalAdministration.student.individualApplication.studentsInnovation.entity.ChxXmsqb;
import educationalAdministration.student.innovationCredits.creditsRecognition.entity.ChxCodeXfrdb;
import educationalAdministration.student.innovationCredits.creditsRecognition.entity.ChxXfrdfjb;
import educationalAdministration.student.innovationCredits.creditsRecognition.entity.ChxXfrdsqb;
import educationalAdministration.student.personalApplication.curriculumReplacement.entity.CjKctdSqb;
import educationalAdministration.student.personalApplication.curriculumReplacement.entity.CjKctdTdyyb;
import educationalAdministration.student.personalManagement.entity.PxCsb;
import educationalAdministration.student.personalManagement.entity.PxCsbPk;
import educationalAdministration.student.personalManagement.entity.TermView;
import educationalAdministration.student.personalManagement.entity.XsFaxdsqb;
import educationalAdministration.student.personalManagement.entity.XsXjb;
import educationalAdministration.student.personalManagement.entity.XsXjbView;

@Service("applyCommonService")
public class ApplyCommonServiceImpl extends BaseService implements ApplyCommonService {

	@Resource
	private ApplyCommonDao applyCommonDao;

	@PersistenceContext
	private EntityManager em;

	@Override
	public String querySqbhByApply() {
		return applyCommonDao.querySqbhByApply();
	}

	@Override
	public int queryEaProcessCount(String apply_type) {
		Object count = applyCommonDao.queryEaProcessCount(apply_type);
		return Integer.parseInt(count.toString());
	}

	@Override
	public List<SysColConfig> querySysColConfigByTabName(String tabname) {
		return applyCommonDao.querySysColConfigByTabName(tabname);
	}

	@Override
	public void doSaveEaResult(String apply_type, String sqbh) {
		List<EaProcess> list = applyCommonDao.queryEaProcess(apply_type);
		EaProcess eaProcess = list.get(0);
		List<EaProcessLink> listLink = applyCommonDao.queryEaProcessLink(eaProcess.getEapCode());
		for (EaProcessLink eaProcessLink : listLink) {
			EaResult eaResult = new EaResult();
			EaResultPK eaResultPK = new EaResultPK();
			eaResultPK.setApplyId(sqbh);
			eaResultPK.setEapCode(eaProcessLink.getId().getEapCode());
			eaResultPK.setEalCode(eaProcessLink.getId().getEalCode());
			eaResult.setId(eaResultPK);
			eaResult.setEalOrder(eaProcessLink.getEalOrder());
			eaResult.setOverEnabled(eaProcessLink.getOverEnabled());
			eaResult.setEalRslt("0");
			applyCommonDao.save(eaResult);
			if ((eaProcess.getOverEnabled().equals("0") && eaProcessLink.getOverEnabled().equals("1")) || eaProcessLink.getOverEnabled().equals("0")) {
				break;
			}
		}

	}

	@Override
	public void doSaveEaResult(String apply_type, String sqbh, String jsh) {
		List<EaProcess> list = applyCommonDao.queryEaProcess(apply_type);
		EaProcess eaProcess = list.get(0);
		List<EaProcessLink> listLink = applyCommonDao.queryEaProcessLink(eaProcess.getEapCode());
		for (int i = 0; i < listLink.size(); i++) {
			EaProcessLink eaProcessLink = listLink.get(i);
			EaResult eaResult = new EaResult();
			EaResultPK eaResultPK = new EaResultPK();
			eaResultPK.setApplyId(sqbh);
			eaResultPK.setEapCode(eaProcessLink.getId().getEapCode());
			eaResultPK.setEalCode(eaProcessLink.getId().getEalCode());
			eaResult.setId(eaResultPK);
			eaResult.setEalOrder(eaProcessLink.getEalOrder());
			eaResult.setOverEnabled(eaProcessLink.getOverEnabled());
			eaResult.setEalRslt("0");
			if (i == 0) {
				eaResult.setEalUser(jsh);
			}
			applyCommonDao.save(eaResult);
			if ((eaProcess.getOverEnabled().equals("0") && eaProcessLink.getOverEnabled().equals("1")) || eaProcessLink.getOverEnabled().equals("0")) {
				break;
			}
		}
	}

	/**
	 * 执行sql语句
	 *
	 * @param sql
	 * @return
	 */
	private int executeUpdateSql(String sql) {
		Query query = em.createNativeQuery(sql);
		int i = query.executeUpdate();
		return i;
	}

	@Override
	public SysYwhdkzb querySysYwhdkzbById(String id) {
		return applyCommonDao.querySysYwhdkzbById(id);
	}

	@Override
	public int queryTimeFrame(String id) {
		String count = applyCommonDao.queryTimeFrame(id);
		return Integer.parseInt(count);
	}

	@Override
	public boolean checkXsYwsqxzbByYwid(String id) {
		boolean check = true;
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		List<XsYwsqxzb> list = applyCommonDao.queryXsYwsqxzbByYwid(id, "kc");
		for (XsYwsqxzb ywsqxzb : list) {
			String sql = ywsqxzb.getJszqsql();
			Param[] params = {new Param("xh", xh)};
			List<Object> val = applyCommonDao.findEntitiesBySQL(sql, params);
			if ("1".equals(val.get(0))) {
				check = false;
				break;
			}
		}
		return check;
	}

	@Override
	public List<XkCxXnxqView> queryXkCxXnxqViewByXh(String xh) {
		List<XkCxXnxqView> list = applyCommonDao.queryXkCxXnxqViewByXh(xh);
		List<XkCxXnxqView> newList = new ArrayList<>();
		for(XkCxXnxqView view : list){
			boolean flag = true;
			for(XkCxXnxqView newWiew : newList){
				if(view.getId().getZxjxjhh().equals(newWiew.getId().getZxjxjhh())){
					flag = false;
					break;
				}
			}
			if(flag){
				newList.add(view);
			}
		}
		return newList;
	}

	/**
	 * 根据申请id获取申请单的详情html信息
	 *
	 * @param
	 * @return
	 * @author: gdx
	 * @date: 2018/5/26 8:59
	 */
	@Override
	public String queryKctdInfoHtml(String applyId, boolean mobile) {
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		CjKctdSqb sqb = applyCommonDao.findById(CjKctdSqb.class, applyId);
		XsXjbView xjb = applyCommonDao.findById(XsXjbView.class, xh);
		CodeTdlxb tdlxb = applyCommonDao.findById(CodeTdlxb.class, sqb.getTdlx());
		String tdyy = sqb.getSqyy();
		CjKctdTdyyb tdyyb = new CjKctdTdyyb();
		if (StringUtils.isNotBlank(sqb.getTdyym())) {
			tdyyb = applyCommonDao.findById(CjKctdTdyyb.class, sqb.getTdyym());
			if ("1".equals(tdyyb.getSgtx())) {
				tdyy = tdyyb.getTdyy() + "：" + sqb.getSqyy();
			} else {
				tdyy = tdyyb.getTdyy() + (StringUtils.isNotBlank(tdyyb.getSm()) ? (":" + tdyyb.getSm()) : "");
			}
		}
		String row_div = "profile-info-row";
		String name_div = "profile-info-name";
		String value_div = "profile-info-value";
		if(mobile){
			row_div = "phone-profile-info-row";
			name_div = "col-xs-3 phone-row-title";
			value_div = "col-xs-9 phone-row-value";
		}
		String sql = "select a.kch||' | '||pn.kcm(a.kch)||'【'|| pn.xs_kcsx('" + xjb.getXh() + "',a.kch)||' | '||pkg_com.f_NNF(pn.kc_xf(a.kch))||'学分'||" +
				"decode(pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj('" + xjb.getXh() + "', a.kch, 1)),null,'',' | ' || pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj('" + xjb.getXh() + "', a.kch, 1)))" +
				"||(select decode(min(t.kssj),null,'',' | '||min(t.kssj)) from (select * from xs_cj_all where xh='" + xjb.getXh() + "' and (kch,kccj) in(select kch,max(kccj) from xs_cj_all where xh='" + xjb.getXh() + "' group by kch)) t where t.xh='" +
				xjb.getXh() + "' and t.kch=a.kch)||'】' kcm," +
				"a.tdkch||' | '||pn.kcm(a.tdkch)||'【'|| pn.xs_kcsx('" + xjb.getXh() + "',a.tdkch)||' | '||pkg_com.f_NNF(pn.kc_xf(a.tdkch))||'学分'||" +
				"decode(pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj('" + xjb.getXh() + "', a.tdkch, 1)),null,'',' | ' || pkg_cj.f_GradeName(pkg_cjtj.f_get_xszgcj('" + xjb.getXh() + "', a.tdkch, 1)))" +
				"||(select decode(min(t.kssj),null,'',' | '||min(t.kssj)) from (select * from xs_cj_all where xh='" + xjb.getXh() + "' and (kch,kccj) in(select kch,max(kccj) from xs_cj_all where xh='" + xjb.getXh() + "' group by kch)) t where t.xh='" +
				xjb.getXh() + "' and t.kch=a.tdkch)||'】' tdkcm from cj_kctd_sqkcb a where a.sqbh='" + applyId + "'";
		List<Object[]> list = applyCommonDao.findEntitiesBySQL(sql);

		StringBuffer infoHt = new StringBuffer();
		if(mobile){
			infoHt.append("<div class=\"col-xs-12\">");
			infoHt.append("<div class=\"widget-box\">");
			infoHt.append("    <div class='widget-header widget-header-flat'>");
			infoHt.append("        <h4 class=\"widget-title lighter\"><i class=\"ace-icon fa fa-info-circle\"></i> 申请信息</h4>");
			infoHt.append("        <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
			infoHt.append("    </div>");
			infoHt.append("    <div class='widget-body'><div class='widget-main' style='padding: 0px;'><div class='dialogs ace-scroll scroll-active'>");
		}else{
			infoHt.append("<div class=\"col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\">");
			infoHt.append("    <h4 class=\"header smaller lighter grey\"><i class=\"ace-icon fa fa-info-circle\"></i> 申请信息</h4>");
			infoHt.append("    <div class=\"widget-content\" id=\"kctd_scroll\" >");
			infoHt.append("        <div class=\"profile-user-info profile-user-info-striped self\">");
		}
		if(mobile){
			infoHt.append("            <div class=\"" + row_div + "\">");
			infoHt.append("                <div class=\"" + name_div + "\">姓名</div>");
			infoHt.append("                <div class=\"" + value_div + "\">" + xjb.getXh() + " | " + xjb.getXm() + " | " + xjb.getGradeAndClass().getClassName() + "</div>");
			infoHt.append("            </div>");
			infoHt.append("            <div class=\"" + row_div + "\">");
			infoHt.append("                <div class=\"" + name_div + "\">专业</div>");
			infoHt.append("                <div class=\"" + value_div + "\">" + xjb.getCodeYear().getYearName()
					+ " | " + xjb.getDepartment().getDepartmentName() + " | " + xjb.getSubject().getSubjectName() + "</div>");
			infoHt.append("            </div>");
		}else {
			infoHt.append("            <div class=\"" + row_div + "\">");
			infoHt.append("                <div class=\"" + name_div + "\">学号</div>");
			infoHt.append("                <div class=\"" + value_div + "\">" + xjb.getXh() + "</div>");

			infoHt.append("                <div class=\"" + name_div + "\">姓名</div>");
			infoHt.append("                <div class=\"" + value_div + "\">" + xjb.getXm() + "</div>");

			infoHt.append("                <div class=\"" + name_div + "\">年级</div>");
			infoHt.append("                <div class=\"" + value_div + "\">" + xjb.getCodeYear().getYearName() + "</div>");
			infoHt.append("            </div>");

			infoHt.append("            <div class=\"" + row_div + "\">");
			infoHt.append("                <div class=\"" + name_div + "\">院系</div>");
			infoHt.append("                <div class=\"" + value_div + "\">" + xjb.getDepartment().getDepartmentName() + "</div>");

			infoHt.append("                <div class=\"" + name_div + "\">专业</div>");
			infoHt.append("                <div class=\"" + value_div + "\">" + xjb.getSubject().getSubjectName() + "</div>");

			infoHt.append("                <div class=\"" + name_div + "\">班级</div>");
			infoHt.append("                <div class=\"" + value_div + "\">" + xjb.getGradeAndClass().getClassName() + "</div>");
			infoHt.append("            </div>");
		}

		infoHt.append("            <div class=\"" + row_div + "\">");
		infoHt.append("                <div class=\"" + name_div + "\">替代类型</div>");
		infoHt.append("                <div class=\"" + value_div + "\">" + tdlxb.getTdlxsm() + "</div>");
		if(mobile){
			infoHt.append("        </div>");
			infoHt.append("        <div class=\"" + row_div + "\">");
		}
		infoHt.append("                <div class=\"" + name_div + "\">申请时间</div>");
		infoHt.append("                <div class=\"" + value_div + "\">" + sqb.getSqrq() + "</div>");
		infoHt.append("            </div>");

		if(!mobile) {
			infoHt.append("        </div>");
			infoHt.append("        <div class=\"profile-user-info profile-user-info-striped self\">");
		}

		infoHt.append("            <div class=\"" + row_div + "\">");
		infoHt.append("                <div class=\"" + name_div + "\">课程</div>");
		infoHt.append("                <div class=\"" + value_div + "\">");
		for (Object[] obj : list) {
			infoHt.append(obj[0] + "<br>");
			if (!"03".equals(sqb.getTdlx())) {
				break;
			}
		}
		infoHt.append("                </div>");
		infoHt.append("            </div>");

		infoHt.append("            <div class=\"" + row_div + "\">");
		infoHt.append("                <div class=\"" + name_div + "\">被替代课程</div>");
		infoHt.append("                <div class=\"" + value_div + "\">");
		for (Object[] obj : list) {
			infoHt.append(obj[1] + "<br>");
			if (!"02".equals(sqb.getTdlx())) {
				break;
			}
		}
		infoHt.append("                </div>");
		infoHt.append("            </div>");

		infoHt.append("            <div class=\"" + row_div + "\">");
		infoHt.append("                <div class=\"" + name_div + "\">替代原因</div>");
		infoHt.append("                <div class=\"" + value_div + "\">" + tdyy + "</div>");
		infoHt.append("            </div>");

		if(mobile) {
			infoHt.append("    </div></div></div>");
		}else{
			infoHt.append("    </div>");
		}
		infoHt.append("    </div>");
		infoHt.append("</div>");

		return infoHt.toString();
	}

	/**
	 * 获取字段中文解释
	 *
	 * @param tableName
	 * @param fieldName
	 * @param fieldCode
	 * @param fildValue
	 * @return
	 */
	/*  private String queryExplainByCode(String tableName, String fieldName, String fieldCode, String fildValue) {
		String sql = "select " + fieldName + " from " + tableName + " where " + fieldCode + " = '" + fildValue + "'";
        Query query = em.createNativeQuery(sql);
        String result = (String) query.getSingleResult();
        return result;
    }*/


	/**
	 * 根据申请id获取学生退课申请单的详情html信息
	 *
	 * @param
	 * @return
	 * @author: gdx
	 * @date: 2018/8/17 8:26
	 */
	@Override
	public String queryStudentRetreatClassInfoHtml(String applyId, String applyType) {
		/*
		 * 1. 获取申请单
		 * 2. 构建代码
		 */
		StringBuffer infoHt = new StringBuffer();
		if ("10002".equals(applyType)) {
			XkTksqb xkTksqb = applyCommonDao.findById(XkTksqb.class, applyId);
			EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
			XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
			CodeKcb codeKcb = applyCommonDao.findById(CodeKcb.class, xkTksqb.getKch());

			StringBuffer otherHt = new StringBuffer();
			JSONObject jsonObject = JSONObject.fromObject(xkTksqb);
			List<SysColConfig> sysColConfigs = applyCommonDao.querySysColConfigByTabName("xk_tksqb");
			if (sysColConfigs != null && sysColConfigs.size() > 0) {
				otherHt.append("<div class=\"profile-info-row\">");
				for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
					SysColConfig sysColConfig = sysColConfigs.get(i);
					if (i != 0 && i % 3 == 0) {
						otherHt.append("</div><div class=\"profile-info-row\">");
					}
					otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
				}
				otherHt.append("</div>");
			}

			infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
					"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 退课申请 </h4> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请人</div> " +
					"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
					"            <div class=\"profile-info-name\">申请时间</div> " +
					"            <div class=\"profile-info-value\">" + xkTksqb.getCzsj() + "</div> " +
					"            <div class=\"profile-info-name\">审批状态</div> " +
					"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
					"        </div> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">课程号</div> " +
					"            <div class=\"profile-info-value\">" + xkTksqb.getKch() + "</div> " +
					"            <div class=\"profile-info-name\">课程名</div> " +
					"            <div class=\"profile-info-value\">" + codeKcb.getKcm() + "</div> " +
					"            <div class=\"profile-info-name\">课序号</div> " +
					"            <div class=\"profile-info-value\">" + xkTksqb.getKxh() + "</div> " +
					"        </div> " + otherHt +
					"    </div> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">退课原因</div> " +
					"            <div class=\"profile-info-value\">" + xkTksqb.getTkyy() + "</div> " +
					"        </div> " +
					"    </div> " +
					"</div>");

		}
		if ("10010".equals(applyType)) {
			XkZxcxsqb xkZxcxsqb = applyCommonDao.findById(XkZxcxsqb.class, applyId);
			EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
			XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
			CodeKcb codeKcb = applyCommonDao.findById(CodeKcb.class, xkZxcxsqb.getKch());

			StringBuffer otherHt = new StringBuffer();
			JSONObject jsonObject = JSONObject.fromObject(xkZxcxsqb);
			List<SysColConfig> sysColConfigs = applyCommonDao.querySysColConfigByTabName("xk_zxcxsqb");
			if (sysColConfigs != null && sysColConfigs.size() > 0) {
				otherHt.append("<div class=\"profile-info-row\">");
				for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
					SysColConfig sysColConfig = sysColConfigs.get(i);
					if (i != 0 && i % 3 == 0) {
						otherHt.append("</div><div class=\"profile-info-row\">");
					}
					otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
				}
				otherHt.append("</div>");
			}

			infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
					"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 自学重修 </h4> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请人</div> " +
					"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
					"            <div class=\"profile-info-name\">申请时间</div> " +
					"            <div class=\"profile-info-value\">" + xkZxcxsqb.getCzsj() + "</div> " +
					"            <div class=\"profile-info-name\">审批状态</div> " +
					"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
					"        </div> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">课程号</div> " +
					"            <div class=\"profile-info-value\">" + xkZxcxsqb.getKch() + "</div> " +
					"            <div class=\"profile-info-name\">课程名</div> " +
					"            <div class=\"profile-info-value\">" + codeKcb.getKcm() + "</div> " +
					"            <div class=\"profile-info-name\">课序号</div> " +
					"            <div class=\"profile-info-value\">" + xkZxcxsqb.getKxh() + "</div> " +
					"        </div> " + otherHt +
					"    </div> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请原因</div> " +
					"            <div class=\"profile-info-value\">" + xkZxcxsqb.getSqyy() + "</div> " +
					"        </div> " +
					"    </div> " +
					"</div>");
		}
		if ("10011".equals(applyType)) {
			XkHksqb xkHksqb = applyCommonDao.findById(XkHksqb.class, applyId);
			EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
			XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
			CodeKcb codeKcb = applyCommonDao.findById(CodeKcb.class, xkHksqb.getKch());

			//HKSQ,HKYYZT,学生申请是否需要维护缓考原因
			PxCsb pxCsb = CommonUtils.queryPxCsbById("HKSQ", "HKYYZT");
			String hkyyzt = (pxCsb!=null&&StringUtils.isNotBlank(pxCsb.getCsz())) ? pxCsb.getCsz() : "0";

			StringBuffer otherHt = new StringBuffer();
			JSONObject jsonObject = JSONObject.fromObject(xkHksqb);
			List<SysColConfig> sysColConfigs = applyCommonDao.querySysColConfigByTabName("xk_hksqb");
			if (sysColConfigs != null && sysColConfigs.size() > 0) {
				otherHt.append("<div class=\"profile-info-row\">");
				for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
					SysColConfig sysColConfig = sysColConfigs.get(i);
					if (i != 0 && i % 3 == 0) {
						otherHt.append("</div><div class=\"profile-info-row\">");
					}
					otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
				}
				otherHt.append("</div>");
			}

			String xxbm = CommonUtils.queryParamValue();
			infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
					"    <h4 class=\"header smaller lighter grey\"><i class=\"ace-icon fa fa-user\"></i> 缓考申请 </h4> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请人</div> " +
					"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
					"            <div class=\"profile-info-name\">申请时间</div> " +
					"            <div class=\"profile-info-value\">" + xkHksqb.getCzsj() + "</div> " +
					"            <div class=\"profile-info-name\">审批状态</div> " +
					"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
					"        </div> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">课程号</div> " +
					"            <div class=\"profile-info-value\">" + xkHksqb.getKch() + "</div> " +
					"            <div class=\"profile-info-name\">课程名</div> " +
					"            <div class=\"profile-info-value\">" + codeKcb.getKcm() + "</div> " +
					"            <div class=\"profile-info-name\">课序号</div> " +
					"            <div class=\"profile-info-value\">" + xkHksqb.getKxh() + "</div> " +
					"        </div> " + otherHt +
					"    </div> ");
			infoHt.append("<div class=\"profile-user-info profile-user-info-striped self\"> ");
			if(!"0".equals(hkyyzt)) {
				infoHt.append("  <div class=\"profile-info-row\"> " +
						"            <div class=\"profile-info-name\">缓考原因</div> " +
						"            <div class=\"profile-info-value\">" + ((xkHksqb.getCodeHkyyb()!=null && StringUtils.isNotBlank(xkHksqb.getCodeHkyyb().getHkyymc())) ? xkHksqb.getCodeHkyyb().getHkyymc() : "") + "</div> " +
						"        </div> ");
			}				
			infoHt.append("<div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">" + ("100006".equals(xxbm) ? "情况说明" : "申请原因") + "</div> " +
					"            <div class=\"profile-info-value\">" + xkHksqb.getSqyy() + "</div> " +
					"        </div> " +
					"    </div> " +
					"</div>");
		}


		return infoHt.toString();
	}

	/**
	 * 根据申请id获取学生补选课申请单的详情html信息
	 *
	 * @param
	 * @return
	 * @author: gdx
	 * @date: 2018/8/20 9:51
	 */
	@Override
	public String queryStudentByElectionClassInfoHtml(String applyId) {

		List<String> weeks = Arrays.asList("一", "二", "三", "四", "五", "六", "日");

		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);

		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		XkBxksqb xkBxksqb = applyCommonDao.findById(XkBxksqb.class, applyId);

		List<Object[]> kcinfos = applyCommonDao.queryBxksqbKcinfo(applyId, applys.getUser_code());

		StringBuffer otherHt = new StringBuffer();
		JSONObject jsonObject = JSONObject.fromObject(xkBxksqb);
		List<SysColConfig> sysColConfigs = applyCommonDao.querySysColConfigByTabName("xk_bxksqb");
		if (sysColConfigs != null && sysColConfigs.size() > 0) {
			otherHt.append("<div class=\"profile-info-row\">");
			for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
				SysColConfig sysColConfig = sysColConfigs.get(i);
				if (i != 0 && i % 3 == 0) {
					otherHt.append("</div><div class=\"profile-info-row\">");
				}
				otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
			}
			otherHt.append("</div>");
		}

		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 补选课申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + xkBxksqb.getCzsj() + "</div> " +
				"            <div class=\"profile-info-name\">审批状态</div> " +
				"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
				"        </div> ");
		infoHt.append(otherHt);
		infoHt.append("    </div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">补选课原因</div> " +
				"            <div class=\"profile-info-value\">" + xkBxksqb.getBxkyy() + "</div> " +
				"        </div> " +
				"    </div> " +
				"</div>");

		infoHt.append("<h4 class=\"header smaller lighter grey\"> " +
				"    <i class=\"glyphicon glyphicon-list\"></i> " +
				"    补选课程列表 " +
				"</h4> " +
				"<table class=\"table table-hover table-bordered table-striped table_td\" id=\"tkkctable\"> " +
				"    <thead> " +
				"    <tr> " +
				"        <th>学年学期</th> " +
				"        <th>课程号</th> " +
				"        <th>课程名</th> " +
				"        <th>课序号</th> " +
				"        <th>替代课程</th> " +
				"        <th>修读方式</th> " +
				"        <th>课容量</th> " +
				"        <th>上课时间</th> " +
				"        <th>上课地点</th> " +
				"    </tr> " +
				"    </thead> " +
				"    <tbody >");


		int rownum = 1;
		for (int i = 0, ten = kcinfos.size(); i < ten; i++) {
			Object[] kcinfo = kcinfos.get(i);
			String kcid = kcinfo[4] + "_" + kcinfo[0] + "_" + kcinfo[2];
			while (kcinfos.size() > i + rownum) {
				Object[] nextkcinfo = kcinfos.get(i + rownum);
				String nextid = nextkcinfo[4] + "_" + nextkcinfo[0] + "_" + nextkcinfo[2];
				if (nextid.equals(kcid)) {
					rownum++;
				} else {
					break;
				}
			}


			int num = i;
			for (int j = 1; j <= rownum; j++) {
				num = i + j - 1;
				Object[] info = kcinfos.get(num);
				if (j == 1) {
					String rowHt = "rowspan = '" + rownum + "'";
					infoHt.append("<tr><td " + rowHt + ">" + info[16] + "</td><td " + rowHt + ">" + info[0] + "</td><td " + rowHt + ">" + info[1] + "</td>" +
							"<td " + rowHt + ">" + info[2] + "</td><td " + rowHt + ">" + (info[19] == null ? "" : info[19]) + "</td><td " + rowHt + ">" + info[18] + "</td><td " + rowHt + ">" + info[3] + "</td><td>" + info[6] + " 周" + weeks.get(Integer.valueOf(info[7].toString()) - 1) + " " + info[8] + "~" + (Integer.valueOf(info[8].toString()) + Integer.valueOf(info[9].toString()) - 1) + "节</td><td>" + info[11] + " " + info[13] + " " + info[15] + "</td>");
					infoHt.append("</tr>");
				} else {
					infoHt.append("<tr><td>" + info[6] + " 周" + weeks.get(Integer.valueOf(info[7].toString()) - 1) + " " + info[8] + "~" + (Integer.valueOf(info[8].toString()) + Integer.valueOf(info[9].toString()) - 1) + "节</td><td>" + info[11] + " " + info[13] + " " + info[15] + "</td>");
					infoHt.append("</tr>");
				}

			}
			i = num;
			rownum = 1;
		}
		infoHt.append("</tbody></table>");

		//其他课堂
		List<Object[]> otherKcinfos = applyCommonDao.queryOtherClassPlaceAndTime(applyId, applys.getUser_code());
		infoHt.append("<h4 class=\"header smaller lighter grey\"> " +
				"    <i class=\"glyphicon glyphicon-list\"></i> " +
				"    补选课程其他课堂列表 " +
				"</h4> <div style=\"overflow:auto;max-height:450px;\">" +
				"<table class=\"table table-hover table-bordered table-striped table_td\" id=\"tkkctable\"> " +
				"    <thead> " +
				"    <tr> " +
				"        <th>学年学期</th> " +
				"        <th>课程号</th> " +
				"        <th>课程名</th> " +
				"        <th>课序号</th> " +
				"        <th>替代课程</th> " +
				"        <th>课余量</th> " +
				"        <th>上课时间</th> " +
				"        <th>上课地点</th> " +
				"        <th>是否冲突</th> ");
		infoHt.append("    </tr> " +
				"    </thead> " +
				"    <tbody >");

		rownum = 1;
		int num = 0;
		String kcid_flag = "";
		for (int i = 0, ten = otherKcinfos.size(); i < ten; i++) {
			Object[] kcinfo = otherKcinfos.get(i);
			String kcid = kcinfo[0] + "_" + kcinfo[2] + "_" + kcinfo[4];
			if (!kcid.equals(kcid_flag)) {
				while (otherKcinfos.size() > i + rownum) {
					Object[] nextkcinfo = otherKcinfos.get(i + rownum);
					String nextid = nextkcinfo[0] + "_" + nextkcinfo[2] + "_" + nextkcinfo[4];
					if (nextid.equals(kcid)) {
						rownum++;
						kcid_flag = kcid;
					} else {
						break;
					}
				}

				for (int j = 1; j <= rownum; j++) {
					num = i + j - 1;
					Object[] info = otherKcinfos.get(num);
					if (j == 1) {
						String rowHt = "rowspan = '" + rownum + "'";
						infoHt.append("<tr><td " + rowHt + ">" + info[1] + "</td><td " + rowHt + ">" + info[2] + "</td><td " + rowHt + ">" + info[3] + "</td><td " + rowHt + ">" + info[4] + "</td>" +
								"<td " + rowHt + ">" + info[5] + "</td><td " + rowHt + ">" + (info[18] == null ? "" : info[19] + "("+info[18]+")") + "</td><td>" + info[8] + " 周" + weeks.get(Integer.valueOf(info[9].toString()) - 1) + " "
								+ info[10] + "~" + (Integer.valueOf(info[10].toString()) + Integer.valueOf(info[11].toString()) - 1) + "节</td><td>" + info[13] + " " + info[15] + " " + info[17] + "</td>");
						infoHt.append("<td " + rowHt + ">" + ("1".equals(info[6].toString()) ? "是" : "否") + "</td>");
						infoHt.append("</tr>");
					} else {
						infoHt.append("<tr><td>" + info[8] + " 周" + weeks.get(Integer.valueOf(info[9].toString()) - 1) + " " + info[10] + "~" + (Integer.valueOf(info[10].toString()) + Integer.valueOf(info[11].toString()) - 1) + "节</td><td>" + info[13] + " " + info[15] + " " + info[17] + "</td>");
						/*infoHt.append("<td>" + ("1".equals(info[6].toString()) ? "是" : "否") + "</td>");*/
						infoHt.append("</tr>");
					}
				}
				rownum = 1;
			}
		}
		infoHt.append("</tbody></table></div>");


		return infoHt.toString();
	}

	/**
	 * 根据申请id获取学生免听申请单的详情html信息
	 *
	 * @param
	 * @return
	 * @author: gdx
	 * @date: 2018/8/22 10:25
	 */
	@Override
	public String queryStudentListenFreeInfoHtml(String applyId) {
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		XkMtsqb xkMtsqb = applyCommonDao.findById(XkMtsqb.class, applyId);
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		CodeKcb codeKcb = applyCommonDao.findById(CodeKcb.class, xkMtsqb.getKch());

		StringBuffer otherHt = new StringBuffer();
		JSONObject jsonObject = JSONObject.fromObject(xkMtsqb);
		List<SysColConfig> sysColConfigs = applyCommonDao.querySysColConfigByTabName("xk_mtsqb");
		if (sysColConfigs != null && sysColConfigs.size() > 0) {
			otherHt.append("<div class=\"profile-info-row\">");
			for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
				SysColConfig sysColConfig = sysColConfigs.get(i);
				if (i != 0 && i % 3 == 0) {
					otherHt.append("</div><div class=\"profile-info-row\">");
				}
				otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
			}
			otherHt.append("</div>");
		}

		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 免听申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + xkMtsqb.getCzsj() + "</div> " +
				"            <div class=\"profile-info-name\">审批状态</div> " +
				"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
				"        </div> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">课程号</div> " +
				"            <div class=\"profile-info-value\">" + xkMtsqb.getKch() + "</div> " +
				"            <div class=\"profile-info-name\">课程名</div> " +
				"            <div class=\"profile-info-value\">" + codeKcb.getKcm() + "</div> " +
				"            <div class=\"profile-info-name\">课序号</div> " +
				"            <div class=\"profile-info-value\">" + xkMtsqb.getKxh() + "</div> " +
				"        </div> " + otherHt +
				"    </div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请原因</div> " +
				"            <div class=\"profile-info-value\">" + xkMtsqb.getSqyy() + "</div> " +
				"        </div> " +
				"    </div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">学习大纲</div> " +
				"            <div class=\"profile-info-value\"><pre style=\"max-height:  200px;overflow:  auto;background-color: white;\">" + xkMtsqb.getXxdg() + "</pre></div> " +
				"        </div> " +
				"    </div> " +
				"</div>");

		return infoHt.toString();
	}

	/**
	 * 根据申请id获取学生免修申请单的详情html信息
	 *
	 * @param
	 * @return
	 * @author: gdx
	 * @date: 2018/8/22 10:28
	 */
	@Override
	public String queryStudentExemptionInfoHtml(String applyId) {
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		XsMxsqb xsMxsqb = applyCommonDao.findById(XsMxsqb.class, applyId);
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		List<Object[]> kcinfos = applyCommonDao.queryXsMxsqkcinfo(applyId);
		PxCsb pxCsb = CommonUtils.queryPxCsbById("MX", "MXFL");
		String mx_mxfl = (pxCsb != null && StringUtils.isNotBlank(pxCsb.getCsz()) ? pxCsb.getCsz() : "0");
		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> " +
				" 免修申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + xsMxsqb.getCzsj() + "</div> " +
				"            <div class=\"profile-info-name\">联系方式</div> " +
				"            <div class=\"profile-info-value\">" + xsMxsqb.getLxfs() + "</div> " +
				"            <div class=\"profile-info-name\">审批状态</div> " +
				"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
				"        </div>" +
				"    </div> ");
		infoHt.append("    <div class=\"profile-user-info profile-user-info-striped self\"> ");
		if ("1".equals(mx_mxfl)) {
			CodeMxflb codeMxflb = applyCommonDao.findById(CodeMxflb.class, xsMxsqb.getMxfldm());
			infoHt.append("<div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">免修分类</div> " +
					"            <div class=\"profile-info-value\">" + codeMxflb.getMxflmc() + "</div> " +
					"        </div> ");
		}
		infoHt.append("<div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请原因</div> " +
				"            <div class=\"profile-info-value\">" + xsMxsqb.getSqyy() + "</div> " +
				"        </div> " +
				"    </div> " +
				"</div>");
		infoHt.append("<h4 class=\"header smaller lighter grey\"> " +
				"    <i class=\"glyphicon glyphicon-list\"></i> " +
				"    免修课程列表 " +
				"</h4> " +
				"<table class=\"table table-hover table-bordered table-striped table_td\" id=\"tkkctable\"> " +
				"    <thead> " +
				"    <tr> " +
				"        <th>序号</th> " +
				"        <th>学年学期</th> " +
				"        <th>课程号</th> " +
				"        <th>课程名</th> " +
				"    </tr> " +
				"    </thead> " +
				"    <tbody >");


		for (int i = 0, ten = kcinfos.size(); i < ten; i++) {
			Object[] info = kcinfos.get(i);
			infoHt.append("<tr><td>" + (i + 1) + "</td><td>" + info[1] + "</td><td>" + info[2] + "</td><td>" + info[3] + "</td></tr>");
		}
		infoHt.append("</tbody></table>");

		return infoHt.toString();
	}

	/**
	 * 根据申请单的申请状态获取中文翻译
	 *
	 * @param applyStatus
	 * @return
	 */
	public String getApplyStatusTran(String applyStatus) {
		switch (applyStatus) {
		case "-1":
			return "已撤销";
		case "0":
			return "待提交";
		case "1":
			return "已提交";
		case "2":
			return "审批中";
		case "3":
			return "审批结束";
		default:
			return "待审批";
		}

	}

	/**
	 * 获取审批信息
	 *
	 * @param
	 * @return
	 * @author: gdx
	 * @date: 2018/5/25 15:02
	 */
	@Override
	public List<EaResultQu> queryEaResultByApplyId(String applyId, Model model) {
		String sql = "SELECT DISTINCT to_char(c.eal_order) " +
				"  FROM EA_RESULT c " +
				" WHERE c.apply_id = '" + applyId + "' " +
				" AND c.eal_rslt in  " + "('1','2','3')" +
				"  order by c.eal_order desc";
		Param param = null;
		List<String> orderList = applyCommonDao.findEntitiesBySQL(sql, param);

		if (orderList.size() > 0) {
			model.addAttribute("ealOrder", orderList.get(0).toString());
			return applyCommonDao.findEntitiesByJPQL(" from EaResultQu a where a.id.applyId = '" + applyId + "' and a.ealOrder " + "<=" + "  " + orderList.get(0).toString() + " order by a.ealOrder");
		} else {
			return new ArrayList<EaResultQu>();
		}

	}

	@Override
	public void doDeleteAllCurriculum(String sqbh) {
		String sql = "delete from ea_result where apply_id = '" + sqbh + "'";
		executeUpdateSql(sql);
	}

	@Override
	public List<XsYwsqxzb> queryXsYwsqxzbByYwid(String ywid, String type) {
		return applyCommonDao.queryXsYwsqxzbByYwid(ywid, "xs");
	}

	@Override
	public List<SysYwhdkzb> queryAllSysYwhdkzb(String csmxqt) {
		String schoolCode = CommonUtils.queryParamValue();
		if ("100008".equals(schoolCode) || "100017".equals(schoolCode)) {
			String hql = " from SysYwhdkzb a where csmxqt = '" + csmxqt + "' and qyf = '1' order by a.id ";
			return applyCommonDao.findEntitiesByJPQL(hql);
		}

		return applyCommonDao.queryAllSysYwhdkzb(csmxqt);
	}

	@Override
	public SysSqfjb querySysSqfjbBySqbh(String sqbh) {
		return applyCommonDao.querySysSqfjbBySqbh(sqbh);
	}

	/**
	 * 根据申请id获取用户上传的附件表列表
	 *
	 * @param applyId
	 * @author: gdx
	 * @date: 2019/8/7 8:41:18
	 */
	@Override
	public List<SysSqfjb> querySysSqfjbByApplyId(String applyId) {
		return applyCommonDao.querySysSqfjbByApplyId(applyId);
	}

	/**
	 * 根据SQBH和applyType获取申请表的Html信息
	 *
	 * @param applyId
	 * @param applyType
	 * @return
	 * @author: gdx
	 * @date: 2020/2/18 17:29
	 */
	@Override
	public String queryStudentJoinTheArmyInfoHtml(String applyId, String applyType) {

		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		CjMksqb cjMksqb = applyCommonDao.findById(CjMksqb.class, applyId);
		TermView termView = applyCommonDao.findById(TermView.class, cjMksqb.getZxjxjhh());
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		CodeKcb codeKcb = applyCommonDao.findById(CodeKcb.class, cjMksqb.getKch());

		StringBuffer otherHt = new StringBuffer();
		JSONObject jsonObject = JSONObject.fromObject(cjMksqb);
		List<SysColConfig> sysColConfigs = applyCommonDao.querySysColConfigByTabName("cj_mksqb_tw");
		if (sysColConfigs != null && sysColConfigs.size() > 0) {
			otherHt.append("<div class=\"profile-info-row\">");
			for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
				SysColConfig sysColConfig = sysColConfigs.get(i);
				if (i != 0 && i % 3 == 0) {
					otherHt.append("</div><div class=\"profile-info-row\">");
				}
				otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
			}
			otherHt.append("</div>");
		}

		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 退伍复学免考申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + cjMksqb.getCzsjstr() + "</div> " +
				"            <div class=\"profile-info-name\">审批状态</div> " +
				"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
				"        </div> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">学年学期</div> " +
				"            <div class=\"profile-info-value\">" + termView.getTerm() + "</div> " +
				"            <div class=\"profile-info-name\">课程号</div> " +
				"            <div class=\"profile-info-value\">" + cjMksqb.getKch() + "</div> " +
				"            <div class=\"profile-info-name\">课程名</div> " +
				"            <div class=\"profile-info-value\">" + codeKcb.getKcm() + "</div> " +
				"        </div> " + otherHt +
				"    </div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">备注</div> " +
				"            <div class=\"profile-info-value\">" + cjMksqb.getBz() + "</div> " +
				"        </div> " +
				"    </div> " +
				"</div>");

		return infoHt.toString();
	}

	@Override
	public String queryStudentCompetitionExemInfoHtml(String applyId,
			String applyType) {

		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		CjMksqb cjMksqb = applyCommonDao.findById(CjMksqb.class, applyId);
		TermView termView = applyCommonDao.findById(TermView.class, cjMksqb.getZxjxjhh());
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		CodeKcb codeKcb = applyCommonDao.findById(CodeKcb.class, cjMksqb.getKch());

		StringBuffer otherHt = new StringBuffer();
		JSONObject jsonObject = JSONObject.fromObject(cjMksqb);
		List<SysColConfig> sysColConfigs = applyCommonDao.querySysColConfigByTabName("cj_mksqb_js");
		if (sysColConfigs != null && sysColConfigs.size() > 0) {
			otherHt.append("<div class=\"profile-info-row\">");
			for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
				SysColConfig sysColConfig = sysColConfigs.get(i);
				if (i != 0 && i % 3 == 0) {
					otherHt.append("</div><div class=\"profile-info-row\">");
				}
				otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
			}
			otherHt.append("</div>");
		}
		String jsm = applyCommonDao.queryJsm(cjMksqb.getJsddjs());

		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 竞赛免考申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + cjMksqb.getCzsjstr() + "</div> " +
				"            <div class=\"profile-info-name\">审批状态</div> " +
				"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
				"        </div> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">学年学期</div> " +
				"            <div class=\"profile-info-value\">" + termView.getTerm() + "</div> " +
				"            <div class=\"profile-info-name\">课程号</div> " +
				"            <div class=\"profile-info-value\">" + cjMksqb.getKch() + "</div> " +
				"            <div class=\"profile-info-name\">课程名</div> " +
				"            <div class=\"profile-info-value\">" + codeKcb.getKcm() + "</div> " +
				"        </div> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">竞赛带队教师</div> " +
				"            <div class=\"profile-info-value\">" + jsm + "（" + cjMksqb.getJsddjs() + "）" + "</div> " +
				"            <div class=\"profile-info-name\">竞赛开始日期</div> " +
				"            <div class=\"profile-info-value\">" + cjMksqb.getJsksrqstr() + "</div> " +
				"            <div class=\"profile-info-name\">竞赛截止日期</div> " +
				"            <div class=\"profile-info-value\">" + cjMksqb.getJsjzrqstr() + "</div> " +
				"        </div> " + otherHt +
				"    </div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">备注</div> " +
				"            <div class=\"profile-info-value\">" + cjMksqb.getBz() + "</div> " +
				"        </div> " +
				"    </div> " +
				"</div>");

		return infoHt.toString();
	}

	@Override
	public String queryBusSectionInfoHtml(String applyId, String applyType) {
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		XsRcswsqb rcswsqb = applyCommonDao.findById(XsRcswsqb.class, applyId);
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		CodeXsrcswb rcswb = applyCommonDao.findById(CodeXsrcswb.class, applyType.substring(2));
		PxCsb csb = CommonUtils.queryPxCsbById("system", "ccqj_zm");
		String ccqj_zm = csb != null && StringUtils.isNotBlank(csb.getCsz()) ? csb.getCsz() : "北京";
		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> " +
				" " + rcswb.getRcsw() + "申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + rcswsqb.getSqsj() + "</div> " +
				"            <div class=\"profile-info-name\">审批状态</div> " +
				"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
				"        </div>");
		if ("11001".equals(applyType)) {
			CodeCcqjb ccqjb = applyCommonDao.findById(CodeCcqjb.class, rcswsqb.getC1());
			infoHt.append("<div class=\"profile-info-row\">" +
					"          <div class=\"profile-info-name\">乘车区间</div>" +
					"          <div class=\"profile-info-value\">" + ccqj_zm + "--" + ccqjb.getZm() + "</div>" +
					"      </div>");
		} else {
			List<SysColConfig> colList = applyCommonDao.querySysColConfigByTabName("xs_rcswsqb", applyType.substring(2));
			if (colList.size() > 3) {
				infoHt.append("<div class=\"profile-info-row\">");
				for (int i = 0; i < 3; i++) {
					String colname = colList.get(i).getColname();
					String colid = colList.get(i).getColid().toLowerCase();
					infoHt.append("<div class=\"profile-info-name\">" + colname + "</div>");
					if ("c1".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC1() + "</div>");
					} else if ("c2".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC2() + "</div>");
					} else if ("c3".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC3() + "</div>");
					} else if ("c4".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC4() + "</div>");
					} else if ("c5".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC5() + "</div>");
					}
				}
				infoHt.append("</div>");
				infoHt.append("<div class=\"profile-info-row\">");
				for (int i = 3; i < colList.size(); i++) {
					String colname = colList.get(i).getColname();
					String colid = colList.get(i).getColid().toLowerCase();
					infoHt.append("<div class=\"profile-info-name\">" + colname + "</div>");
					if ("c1".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC1() + "</div>");
					} else if ("c2".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC2() + "</div>");
					} else if ("c3".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC3() + "</div>");
					} else if ("c4".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC4() + "</div>");
					} else if ("c5".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC5() + "</div>");
					}
				}
				infoHt.append("</div>");
			} else {
				infoHt.append("<div class=\"profile-info-row\">");
				for (int i = 0; i < colList.size(); i++) {
					String colname = colList.get(i).getColname();
					String colid = colList.get(i).getColid().toLowerCase();
					infoHt.append("<div class=\"profile-info-name\">" + colname + "</div>");
					if ("c1".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC1() + "</div>");
					} else if ("c2".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC2() + "</div>");
					} else if ("c3".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC3() + "</div>");
					} else if ("c4".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC4() + "</div>");
					} else if ("c5".equals(colid)) {
						infoHt.append("<div class=\"profile-info-value\">" + rcswsqb.getC5() + "</div>");
					}
				}
				infoHt.append("</div>");
			}
		}
		infoHt.append("</div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请原因</div> " +
				"            <div class=\"profile-info-value\">" + rcswsqb.getSqyy() + "</div> " +
				"        </div> " +
				"    </div> " +
				"</div>");

		return infoHt.toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public String queryFaxdsqInfoHtml(String applyId, boolean mobile) {
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, xh);
		XsFaxdsqb sqb = applyCommonDao.queryXsFaxdsqb(applyId);
		//01待审批   02通过  03 拒绝
		StringBuffer infoHt = new StringBuffer();
		if (mobile) {
			infoHt.append("<div class='col-xs-12'><div class='widget-box'>");
			infoHt.append("  <div class='widget-header widget-header-flat'>");
			infoHt.append("      <h4 class=\"widget-title lighter\"><i class=\"glyphicon glyphicon-list\"></i> "+sqb.getCodeSqlxb().getSqlxmc()+"申请</h4>");
			infoHt.append("      <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
			infoHt.append("  </div>");
			infoHt.append("  <div class='widget-body'><div class='widget-main' style='padding: 0px;'><div class='dialogs ace-scroll scroll-active'>");
			infoHt.append("<div id=\"customs\"> " +
					"        <div class=\"phone-profile-info-row\"> " +
					"            <div class=\"col-xs-3 phone-row-title\">申请人</div> " +
					"            <div class=\"col-xs-9 phone-row-value\">" + xsXjb.getXm() + "</div> " +
					"        </div> " +
					"        <div class=\"phone-profile-info-row\"> " +
					"            <div class=\"col-xs-3 phone-row-title\">申请时间</div> " +
					"            <div class=\"col-xs-9 phone-row-value\">" + sqb.getSqrq() + "</div> " +
					"        </div> " +
					"        <div class=\"phone-profile-info-row\"> " +
					"            <div class=\"col-xs-3 phone-row-title\">审批状态</div> " +
					"            <div class=\"col-xs-9 phone-row-value\">" + (applys != null ? getApplyStatusTran(applys.getApply_status()) : sqb.getCodeSqztb().getSqztsm()) + "</div> " +
					"        </div> " +
					"        <div class=\"phone-profile-info-row\"> " +
					"            <div class=\"col-xs-3 phone-row-title\">方案</div> " +
					"            <div class=\"col-xs-9 phone-row-value\">" + sqb.getId().getJhfajhb().getFamc() + "</div> " +
					"        </div> ");
			if ("01".equals(sqb.getCodeSqlxb().getSqlxdm())) {
				infoHt.append("        <div class=\"phone-profile-info-row\"> " +
						"            <div class=\"col-xs-3 phone-row-title\">GPA</div> " +
						"            <div class=\"col-xs-9 phone-row-value\">" + (sqb.getGpa_val() == null ? "" : sqb.getGpa_val()) + "</div> " +
						"        </div> " +
						"        <div class=\"phone-profile-info-row\"> " +
						"            <div class=\"col-xs-3 phone-row-title\">GPA值</div> " +
						"            <div class=\"col-xs-9 phone-row-value\">" + (sqb.getGpa_type() == null ? "" : applyCommonDao.queryGpaName(sqb.getGpa_type())) + "</div> " +
						"        </div> ");
			}

			infoHt.append("    </div> " +
					"</div>");
			infoHt.append("</div></div></div></div></div>");
			if ("07".equals(sqb.getCodeSqlxb().getSqlxdm())) {
				PxCsb csb = em.find(PxCsb.class, new PxCsbPk("fxfazx", "xsxzsckc"));
				if (csb != null && StringUtils.isNotBlank(csb.getCsz()) && "1".equals(csb.getCsz())) {
					List<Object[]> cjlist = em.createNativeQuery("SELECT pn.xnxqmc(a.zxjxjhh), b.famc, '【'||a.kch||'】'||b.kcm, b.kcsxmc, b.kccj " +
							"FROM XS_FXFAZXKCB a, XS_QBCJ_VIEW b where a.zxjxjhh=b.zxjxjhh and a.kch=b.kch and a.fajhh=b.fajhh and a.xh=b.xh and a.kcsjly='CJ' and a.sqbh='" + applyId + "'").getResultList();
					if (CollectionUtils.isNotEmpty(cjlist)) {
						infoHt.append("<div class='col-xs-12'><div class='widget-box'>");
						infoHt.append("  <div class='widget-header widget-header-flat'>");
						infoHt.append("      <h4 class=\"widget-title lighter\"><i class=\"glyphicon glyphicon-list\"></i> 注销后删除成绩列表</h4>");
						infoHt.append("      <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
						infoHt.append("  </div>");
						infoHt.append("  <div class='widget-body'><div class='widget-main' style='padding: 0px;'><div class='dialogs ace-scroll scroll-active'>");
						infoHt.append("<table class='table table-bordered table-hover table-striped'> " +
								"		<thead> " +
								"			<tr> " +
								"				<th>序号</th> " +
								"				<th>课程号</th> " +
								"				<th>课程名</th> " +
								"				<th>成绩</th> " +
								"			</tr> " +
								"		</thead> " +
								"		<tbody>");
						int length = cjlist.size();
						for (int i = 0; i < length; i++) {
							Object[] cjTemp = cjlist.get(i);
							infoHt.append("<tr>" +
									"<td>" + (i + 1) + "</td>" +
									"<td>" + cjTemp[0] + "</td>" +
									"<td>" + cjTemp[1] + "</td>" +
									"<td>" + cjTemp[2] + "</td>" +
									"</tr>");
						}
						infoHt.append("</tbody> " +
								"	</table> " +
								"</div>");
						infoHt.append("</div></div></div></div></div>");
					}
					List<Object[]> xklist = em.createNativeQuery("SELECT pn.xnxqmc(a.zxjxjhh), pn.famc(a.fajhh), '【'||a.kch||'】'||pn.kcm(a.kch), pn.kcsxmc(b.kcsxdm) " +
							"FROM XS_FXFAZXKCB a, xk_xkb b where a.zxjxjhh=b.zxjxjhh and a.kch=b.kch and a.fajhh=b.fajhh and a.xh=b.xh and a.kcsjly='XK' and a.sqbh='" + applyId + "'").getResultList();
					if (CollectionUtils.isNotEmpty(xklist)) {
						infoHt.append("<div class='col-xs-12'><div class='widget-box'>");
						infoHt.append("  <div class='widget-header widget-header-flat'>");
						infoHt.append("      <h4 class=\"widget-title lighter\"><i class=\"glyphicon glyphicon-list\"></i> 注销后删除选课列表</h4>");
						infoHt.append("      <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
						infoHt.append("  </div>");
						infoHt.append("  <div class='widget-body'><div class='widget-main' style='padding: 0px;'><div class='dialogs ace-scroll scroll-active'>");
						infoHt.append("<table class='table table-bordered table-hover table-striped'> " +
								"		<thead> " +
								"			<tr> " +
								"				<th>序号</th> " +
								"				<th>学年学期</th> " +
								"				<th>方案名称</th> " +
								"				<th>课程</th> " +
								"				<th>课程属性</th> " +
								"			</tr> " +
								"		</thead> " +
								"		<tbody>");
						int length = xklist.size();
						for (int i = 0; i < length; i++) {
							Object[] xkTemp = xklist.get(i);
							infoHt.append("<tr>" +
									"<td>" + (i + 1) + "</td>" +
									"<td>" + xkTemp[0] + "</td>" +
									"<td>" + xkTemp[1] + "</td>" +
									"<td>" + xkTemp[2] + "</td>" +
									"<td>" + xkTemp[3] + "</td>" +
									"</tr>");
						}
						infoHt.append("</tbody> " +
								"	</table> " +
								"</div>");
						infoHt.append("</div></div></div></div></div>");
					}
				}
			}
		} else {
			infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
					"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> " +
					" " + sqb.getCodeSqlxb().getSqlxmc() + "申请 </h4> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请人</div> " +
					"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
					"            <div class=\"profile-info-name\">申请时间</div> " +
					"            <div class=\"profile-info-value\">" + sqb.getSqrq() + "</div> " +
					"            <div class=\"profile-info-name\">审批状态</div> " +
					"            <div class=\"profile-info-value\">" + (applys != null ? getApplyStatusTran(applys.getApply_status()) : sqb.getCodeSqztb().getSqztsm()) + "</div> " +
					"        </div>");
			infoHt.append("<div class=\"profile-info-row\">");
			infoHt.append("<div class=\"profile-info-name\">方案名称</div><div class=\"profile-info-value\">" + sqb.getId().getJhfajhb().getFamc() + "</div>");
			if ("01".equals(sqb.getCodeSqlxb().getSqlxdm())) {
				infoHt.append("<div class=\"profile-info-name\">GPA类型</div><div class=\"profile-info-value\">" + (sqb.getGpa_type() == null ? "" : applyCommonDao.queryGpaName(sqb.getGpa_type())) + "</div>");
				infoHt.append("<div class=\"profile-info-name\">GPA</div><div class=\"profile-info-value\">" + (sqb.getGpa_val() == null ? "" : sqb.getGpa_val()) + "</div>");

			}
			infoHt.append("</div>");
			infoHt.append("</div> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请原因</div> " +
					"            <div class=\"profile-info-value\">" + sqb.getSqyy() + "</div> " +
					"        </div> " +
					"    </div> " +
					"</div>");
			if ("07".equals(sqb.getCodeSqlxb().getSqlxdm())) {
				PxCsb csb = em.find(PxCsb.class, new PxCsbPk("fxfazx", "xsxzsckc"));
				if (csb != null && StringUtils.isNotBlank(csb.getCsz()) && "1".equals(csb.getCsz())) {
					List<Object[]> cjlist = em.createNativeQuery("SELECT pn.xnxqmc(a.zxjxjhh), b.famc, '【'||a.kch||'】'||b.kcm, b.kcsxmc, b.kccj " +
							"FROM XS_FXFAZXKCB a, XS_QBCJ_VIEW b where a.zxjxjhh=b.zxjxjhh and a.kch=b.kch and a.fajhh=b.fajhh and a.xh=b.xh and a.kcsjly='CJ' and a.sqbh='" + applyId + "'").getResultList();
					if (CollectionUtils.isNotEmpty(cjlist)) {
						infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
								"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 注销后删除成绩列表</h4> " +
								"    <table class='table table-bordered table-hover table-striped'> " +
								"		<thead> " +
								"			<tr> " +
								"				<th>序号</th> " +
								"				<th>学年学期</th> " +
								"				<th>方案名称</th> " +
								"				<th>课程</th> " +
								"				<th>课程属性</th> " +
								"				<th>成绩</th> " +
								"			</tr> " +
								"		</thead> " +
								"		<tbody>");
						int length = cjlist.size();
						for (int i = 0; i < length; i++) {
							Object[] cjTemp = cjlist.get(i);
							infoHt.append("<tr>" +
									"<td>" + (i + 1) + "</td>" +
									"<td>" + cjTemp[0] + "</td>" +
									"<td>" + cjTemp[1] + "</td>" +
									"<td>" + cjTemp[2] + "</td>" +
									"<td>" + cjTemp[3] + "</td>" +
									"<td>" + cjTemp[4] + "</td>" +
									"</tr>");
						}
						infoHt.append("</tbody> " +
								"	</table> " +
								"</div>");

						List<Object[]> xklist = em.createNativeQuery("SELECT pn.xnxqmc(a.zxjxjhh), pn.famc(a.fajhh), '【'||a.kch||'】'||pn.kcm(a.kch), pn.kcsxmc(b.kcsxdm) " +
								"FROM XS_FXFAZXKCB a, xk_xkb b where a.zxjxjhh=b.zxjxjhh and a.kch=b.kch and a.fajhh=b.fajhh and a.xh=b.xh and a.kcsjly='XK' and a.sqbh='" + applyId + "'").getResultList();
						if (CollectionUtils.isNotEmpty(xklist)) {
							infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
									"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 注销后删除选课列表</h4> " +
									"    <table class='table table-bordered table-hover table-striped'> " +
									"		<thead> " +
									"			<tr> " +
									"				<th>序号</th> " +
									"				<th>学年学期</th> " +
									"				<th>方案名称</th> " +
									"				<th>课程</th> " +
									"				<th>课程属性</th> " +
									"			</tr> " +
									"		</thead> " +
									"		<tbody>");
							length = xklist.size();
							for (int i = 0; i < length; i++) {
								Object[] xkTemp = xklist.get(i);
								infoHt.append("<tr>" +
										"<td>" + (i + 1) + "</td>" +
										"<td>" + xkTemp[0] + "</td>" +
										"<td>" + xkTemp[1] + "</td>" +
										"<td>" + xkTemp[2] + "</td>" +
										"<td>" + xkTemp[3] + "</td>" +
										"</tr>");
							}
							infoHt.append("</tbody> " +
									"	</table> " +
									"</div>");
						}
					}
				}
			}
		}
		return infoHt.toString();
	}

	@Override
	public String queryInnovationProjectInfoHtml(String applyId, String applyType) {
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		ChxXfrdsqb sqb = applyCommonDao.findById(ChxXfrdsqb.class, applyId);
		String schoolCode = CommonUtils.queryParamValue();
		String cxxdm = "";
		String sql = "";
		if ("100048".equals(schoolCode)) {
			cxxdm = sqb.getCglxdm();
			sql = "select b.cxxmc,a.tdcyrs,c.pxmc,decode(a.dyzzdwsflndx,'1','是','0','否',a.dyzzdwsflndx) dyzzdwsflndx,decode(a.sflyzswt,'1','是','0','否',a.sflyzswt) sflyzswt,decode(a.zswtsflykgw,'1','是','0','否',a.zswtsflykgw) zswtsflykgw," +
					"to_char(to_date(a.zswtfssj, 'yyyymmdd'), 'yyyy-mm-dd') zswtfssj,a.zswtfsdd,a.zswtms,a.zswtjjcx,";
			if("100".equals(cxxdm)){
				sql += "(select t.ssjbmc from chx_ssjbb t where t.cglxdm=a.cglxdm and t.ssjbdm=a.xkjs_ssjbdm) xkjs_ssjbmc,a.xkjs_dsmc,a.xkjs_szsd,a.xkjs_szzb," +
						"(select t.hjjbmc from chx_xkjs_hjjbb t where t.hjjbdm=a.xkjs_hjjbdm) xkjs_hjjbmc,(select t.hjdjmc from chx_xkjs_hjdjb t where t.hjdjdm=a.xkjs_hjdjdm) xkjs_hjdjmc," +
						"to_char(to_date(a.xkjs_hjrq, 'yyyymmdd'), 'yyyy-mm-dd') xkjs_hjrq,a.xkjs_hjxmmc ";
			}else if("101".equals(cxxdm)){
				sql += "(select t.xmlxmc from chx_cxcy_xmlxb t where t.xmlxdm=a.cxcy_xmlxdm) cxcy_xmlxmc,(select t.lxjbmc from chx_cxcy_lxjbb t where t.lxjbdm=a.cxcy_lxjbdm) cxcy_lxjbmc," +
						"(select t.lxpcmc from chx_cxcy_lxpcb t where t.lxpcdm=a.cxcy_lxpcdm) cxcy_lxpcmc,to_char(to_date(a.cxcy_xmsbrq, 'yyyymmdd'), 'yyyy-mm-dd') cxcy_xmsbrq," +
						"to_char(to_date(a.cxcy_xmdjrq, 'yyyymmdd'), 'yyyy-mm-dd') cxcy_xmdjrq,decode(a.cxcy_sfjx,'1','是','0','否',a.cxcy_sfjx) cxcy_sfjx," +
						"to_char(to_date(a.cxcy_xmjxrq, 'yyyymmdd'), 'yyyy-mm-dd') cxcy_xmjxrq,a.cxcy_xmmc ";
			}else if("102".equals(cxxdm)){
				sql += "decode(a.cysj_qysfrzkjy,'1','是','0','否',a.cysj_qysfrzkjy) cysj_qysfrzkjy,a.cysj_qymc,a.cysj_qydm,to_char(to_date(a.cysj_qyzcrq, 'yyyymmdd'), 'yyyy-mm-dd') cysj_qyzcrq," +
						"a.cysj_qyzczb,a.cysj_brcgbl,(select t.zwmc from chx_code_gszwb t where t.zwdm=a.cysj_gszwdm) cysj_zwmc,a.cysj_qyjysj ";
			}else if("103".equals(cxxdm)){
				sql += "a.lwzz_cglx,decode(a.lwzz_cglx,'1','论文','2','研究报告',a.lwzz_cglx) lwzz_cglxsm,a.lwzz_lw_lwmc,a.lwzz_lw_fbkwmc,to_char(to_date(a.lwzz_lw_lwfbsj, 'yyyymmdd'), 'yyyy-mm-dd') lwzz_lw_lwfbsj," +
						"a.lwzz_lw_lwjbmc,a.lwzz_lw_lwsymc,a.lwzz_yjbg_bgmc,a.lwzz_yjbg_yyqd,'' lwzz_yjbg_yyqdsm," +
						"(select t.ncjbmc from chx_code_ncjbb t where t.ncjbdm=a.lwzz_yjbg_fbncjbdm) lwzz_yjbg_ncjbmc,a.lwzz_yjbg_fbncmc,to_char(to_date(a.lwzz_yjbg_fbsj, 'yyyymmdd'), 'yyyy-mm-dd') lwzz_yjbg_fbsj," +
						"(select t.ldpsjbmc from chx_code_ldpsjbb t where t.ldpsjbdm=a.lwzz_yjbg_ldpsjbdm) lwzz_yjbg_ldpsjbmc,a.lwzz_yjbg_psqksm,to_char(to_date(a.lwzz_yjbg_pssj, 'yyyymmdd'), 'yyyy-mm-dd') lwzz_yjbg_pssj," +
						"(select t.yydwjbmc from chx_code_yydwjbb t where t.yydwjbdm=a.lwzz_yjbg_yydwjbdm) lwzz_yjbg_yydwjbmc,a.lwzz_yjbg_yydw,a.lwzz_yjbg_yyqksm,to_char(to_date(a.lwzz_yjbg_yysj, 'yyyymmdd'), 'yyyy-mm-dd') lwzz_yjbg_yysj ";
			}else if("104".equals(cxxdm)){
				sql += "a.zlfm_zlmc,(select t.zllxmc from chx_code_zllxb t where t.zllxdm=a.zlfm_zllxdm) zlfm_zllxmc,(select t.zlztmc from chx_code_zlztb t where t.zlztdm=a.zlfm_zlztdm) zlfm_zlztmc," +
						"to_char(to_date(a.zlfm_hdqsztsj, 'yyyymmdd'), 'yyyy-mm-dd') zlfm_hdqsztsj,a.zlfm_sqgj,a.zlfm_zlr ";
			}else if("105".equals(cxxdm)){
				sql += "a.kycx_xmmc,decode(a.kycx_xmfl,'1','纵向','2','横向',a.kycx_xmfl) kycx_xmfl,(select t.xmjbmc from chx_kycx_xmjbb t where t.xmjbdm=a.kycx_xmjbdm) kycx_xmjbmc," +
						"a.kycx_xmlymc,a.kycx_xmlxmc,a.kycx_xmbh,to_char(to_date(a.kycx_lxsj, 'yyyymmdd'), 'yyyy-mm-dd') kycx_lxsj,decode(a.kycx_sfjx,'1','是','0','否',a.kycx_sfjx) kycx_sfjx," +
						"to_char(to_date(a.kycx_jxsj, 'yyyymmdd'), 'yyyy-mm-dd') kycx_jxsj,decode(a.kycx_xmfzrsfbxls,'1','是','0','否',a.kycx_xmfzrsfbxls) kycx_xmfzrsfbxls,a.kycx_xmfzrszdw," +
						"decode(a.kycx_zzcllx,'1','立项证明与立项申报书','2','结项证书',a.kycx_zzcllx) kycx_zzcllx ";
			}else if("107".equals(cxxdm)){
				sql += "a.xsjl_hdmc,to_char(to_date(a.xsjl_hdsj, 'yyyymmdd'), 'yyyy-mm-dd') xsjl_hdsj,decode(a.xsjl_hdddlx,'1','境内','2','境外', a.xsjl_hdddlx) xsjl_hdddlx,a.xsjl_hddd," +
						"(select t.hdjbmc from chx_code_xsjlhdjbb t where t.hdjbdm=a.xsjl_hdjbdm) xsjl_hdjbmc,(select t.hdnrmc from chx_code_xsjlhdnrb t where t.hdnrdm=a.xsjl_hdnrdm) xsjl_hdnrmc," +
						"a.xsjl_hdnr,a.xsjl_hdtjcglx,a.xsjl_hdtjcgmc,a.xsjl_hdqdcj,a.xsjl_zzcllx,a.xsjl_zzcllxmc ";
			}
			sql += " from chx_xfrdsqb a, chx_code_cxxdmb b, chx_code_xspxb c where a.cglxdm=b.cxxdm and a.cglxdm=c.cglxdm(+) and a.tdpx=c.pxdm(+) and a.id='" + applyId + "'";
		}else{
			ChxCodeXfrdb chxCodeXfrdb = applyCommonDao.findById(ChxCodeXfrdb.class, sqb.getBzid());
			cxxdm = chxCodeXfrdb.getCxxdm();
			sql = "select d.cxxmc || ' > ' || e.khnrmc || ' > ' || b.khbzmc,pkg_com.f_NNF(b.xf),pn.kcsxmc(a.kcsxdm),pkg_cj.f_GradeName(b.cj) from chx_xfrdsqb a,chx_code_xfrdb b, " +
					"chx_code_cxxdmb d, chx_code_khnrb e where b.cxxdm = d.cxxdm and b.cxxdm = e.cxxdm and b.khnrdm = e.khnrdm and b.id = a.bzid and a.id='" + applyId + "'";
		}
		Object[] obj = applyCommonDao.findEntityBySQL(sql);
		StringBuffer infoHt = new StringBuffer();
		try {
			infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
					"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> " + obj[0] + "申请 </h4> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请人</div> " +
					"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
					"            <div class=\"profile-info-name\">申请时间</div> " +
					"            <div class=\"profile-info-value\">" + applys.getcommit_dtStr() + "</div> " +
					"            <div class=\"profile-info-name\">审批状态</div> " +
					"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
					"        </div>");
			if ("100006".equals(schoolCode)) {
				infoHt.append("<div class=\"profile-info-row\"> " +
						"          <div class=\"profile-info-name\">学分</div> " +
						"          <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1]) + "</div> " +
						"          <div class=\"profile-info-name\">成绩</div> " +
						"          <div class=\"profile-info-value\">" + (obj[3] == null ? "" : obj[3]) + "</div> " +
						"          <div class=\"profile-info-name\">学分属性</div> " +
						"          <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> " +
						"      </div>");

			}else if ("100048".equals(schoolCode)) {
				if("100".equals(cxxdm)){
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">赛事级别</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[10] == null ? "" : obj[10]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">赛事名称</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[11] == null ? "" : obj[11]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">所在赛道</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[12] == null ? "" : obj[12]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">所在组别</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[13] == null ? "" : obj[13]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">获奖级别</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[14] == null ? "" : obj[14]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">获奖等级</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[15] == null ? "" : obj[15]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">获奖时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[16] == null ? "" : obj[16]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">获奖项目名称</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[17] == null ? "" : obj[17]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">团队成员数</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">本人排序（含主持人）</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("</div>");

				}else if("101".equals(cxxdm)){
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">项目类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[10] == null ? "" : obj[10]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">立项级别</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[11] == null ? "" : obj[11]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">立项批次</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[12] == null ? "" : obj[12]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">项目申报时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[13] == null ? "" : obj[13]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">项目定级时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[14] == null ? "" : obj[14]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">是否结项</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[15] == null ? "" : obj[15]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">项目结项时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[16] == null ? "" : obj[16]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">项目名称</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[17] == null ? "" : obj[17]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">团队成员数</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">本人排序（含主持人）</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("</div>");
				}else if("102".equals(cxxdm)){
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">企业是否入驻学校科技园</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[10] == null ? "" : obj[10]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">企业名称</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[11] == null ? "" : obj[11]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">企业社会信用统一代码</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[12] == null ? "" : obj[12]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">企业注册时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[13] == null ? "" : obj[13]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">企业注册资本</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[14] == null ? "" : obj[14]) + "万</div> ");
					infoHt.append("    <div class=\"profile-info-name\">本人拥有公司的股份比例</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[15] == null ? "" : new BigDecimal(obj[15].toString()).multiply(new BigDecimal(100))) + "%</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">本人在公司职务</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[16] == null ? "" : obj[16]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">企业经营时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[17] == null ? "" : obj[17]) + "月</div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("</div>");
				}else if("103".equals(cxxdm)){
					String yyqd_ncfb = "";
					String yyqd_ldps = "";
					String yyqd_dwyy = "";
					String yyqdsm = "";
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">成果类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[11] == null ? "" : obj[11]) + "</div> ");
					if("1".equals(obj[10]+"")){
						infoHt.append("    <div class=\"profile-info-name\">论文名称</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[12] == null ? "" : obj[12]) + "</div> ");
						infoHt.append("    <div class=\"profile-info-name\">发表刊物名称</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[13] == null ? "" : obj[13]) + "</div> ");
					}else if("2".equals(obj[10]+"")){
						if((obj[18]+",").contains("1,")){
							yyqd_ncfb = "内参发表";
							yyqdsm = yyqd_ncfb;
						}
						if((obj[18]+",").contains("2,")){
							yyqd_ldps = "领导批示";
							if(StringUtils.isNotBlank(yyqdsm)){
								yyqdsm += "," + yyqd_ldps;
							}else{
								yyqdsm = yyqd_ldps;
							}
						}
						if((obj[18]+",").contains("3,")){
							yyqd_dwyy = "单位应用";
							if(StringUtils.isNotBlank(yyqdsm)){
								yyqdsm += "," + yyqd_dwyy;
							}else{
								yyqdsm = yyqd_dwyy;
							}
						}
						infoHt.append("    <div class=\"profile-info-name\">报告名称</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[17] == null ? "" : obj[17]) + "</div> ");
						infoHt.append("    <div class=\"profile-info-name\">应用渠道</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + yyqdsm + "</div>");
					}
					infoHt.append("</div>");

					if("1".equals(obj[10]+"")){
						infoHt.append("<div class=\"profile-info-row\">");
						infoHt.append("    <div class=\"profile-info-name\">论文发表时间</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[14] == null ? "" : obj[14]) + "</div> ");
						infoHt.append("    <div class=\"profile-info-name\">论文级别</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[15] == null ? "" : obj[15]) + "</div> ");
						infoHt.append("    <div class=\"profile-info-name\">论文索引</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[16] == null ? "" : obj[16]) + "</div> ");
						infoHt.append("</div>");

						infoHt.append("<div class=\"profile-info-row\">");
						infoHt.append("    <div class=\"profile-info-name\">作者人数</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1]) + "</div> ");
						infoHt.append("    <div class=\"profile-info-name\">本人排序</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> ");
						infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
						infoHt.append("    <div class=\"profile-info-value\"></div> ");
						infoHt.append("</div>");
					}else if("2".equals(obj[10]+"")){

						if(StringUtils.isNotBlank(yyqd_ncfb)){
							infoHt.append("<div class=\"profile-info-row\">");
							infoHt.append("    <div class=\"profile-info-name\">发表内参级别</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[20] == null ? "" : obj[20]) + "</div> ");
							infoHt.append("    <div class=\"profile-info-name\">发表内参名称</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[21] == null ? "" : obj[21]) + "</div> ");
							infoHt.append("    <div class=\"profile-info-name\">发表内参时间</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[22] == null ? "" : obj[22]) + "</div> ");
							infoHt.append("</div>");
						}
						if(StringUtils.isNotBlank(yyqd_ldps)){
							infoHt.append("<div class=\"profile-info-row\">");
							infoHt.append("    <div class=\"profile-info-name\">领导批示级别</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[23] == null ? "" : obj[23]) + "</div> ");
							infoHt.append("    <div class=\"profile-info-name\">批示情况说明</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[24] == null ? "" : obj[24]) + "</div> ");
							infoHt.append("    <div class=\"profile-info-name\">批示时间</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[25] == null ? "" : obj[25]) + "</div> ");
							infoHt.append("</div>");
						} 
						if(StringUtils.isNotBlank(yyqd_dwyy)){
							infoHt.append("<div class=\"profile-info-row\">");
							infoHt.append("    <div class=\"profile-info-name\">应用单位级别</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[26] == null ? "" : obj[26]) + "</div> ");
							infoHt.append("    <div class=\"profile-info-name\">应用单位</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[27] == null ? "" : obj[27]) + "</div> ");
							infoHt.append("    <div class=\"profile-info-name\">应用情况说明</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[28] == null ? "" : obj[28]) + "</div> ");
							infoHt.append("</div>");
						}

						infoHt.append("<div class=\"profile-info-row\">");
						if(StringUtils.isNotBlank(yyqd_dwyy)){
							infoHt.append("    <div class=\"profile-info-name\">应用时间</div> ");
							infoHt.append("    <div class=\"profile-info-value\">" + (obj[29] == null ? "" : obj[29]) + "</div> ");
						}
						infoHt.append("    <div class=\"profile-info-name\">作者人数</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1]) + "</div> ");
						infoHt.append("    <div class=\"profile-info-name\">本人排序</div> ");
						infoHt.append("    <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> ");
						if(StringUtils.isBlank(yyqd_dwyy)){
							infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
							infoHt.append("    <div class=\"profile-info-value\"></div> ");
						}
						infoHt.append("</div>");
					}
				}else if("104".equals(cxxdm)){
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">专利或软件著作名称</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[10] == null ? "" : obj[10]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">专利类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[11] == null ? "" : obj[11]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">专利状态</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[12] == null ? "" : obj[12]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">获得前述状态时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[13] == null ? "" : obj[13]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">申请国家或地区</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[14] == null ? "" : obj[14]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">专利/软件著作权人</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[15] == null ? "" : obj[15]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">作者人数</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">本人排序</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("</div>");
				}else if("105".equals(cxxdm)){
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">项目名称</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[10] == null ? "" : obj[10]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">项目分类</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[11] == null ? "" : obj[11]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">项目级别</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[12] == null ? "" : obj[12]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">项目来源</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[13] == null ? "" : obj[13]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">项目类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[14] == null ? "" : obj[14]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">项目编号</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[15] == null ? "" : obj[15]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">立项时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[16] == null ? "" : obj[16]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">是否结项</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[17] == null ? "" : obj[17]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">结项时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[18] == null ? "" : obj[18]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">项目负责人是否本校教师</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[19] == null ? "" : obj[19]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">项目负责人所在单位</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[20] == null ? "" : obj[20]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">佐证材料类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[21] == null ? "" : obj[21]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">团队人数</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">本人排序（含负责人）</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("</div>");
				}else if("107".equals(cxxdm)){
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">学术交流活动名称</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[10] == null ? "" : obj[10]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">学术交流活动时间</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[11] == null ? "" : obj[11]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">活动地点类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[12] == null ? "" : obj[12]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">学术交流活动地点</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[13] == null ? "" : obj[13]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">学术交流活动级别</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[14] == null ? "" : obj[14]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">学术交流活动内容类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[15] == null ? "" : obj[15]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">本人学术交流活动主要内容</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[16] == null ? "" : obj[16]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">本人学术交流活动提交成果类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[17] == null ? "" : obj[17]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">本人学术交流活动提交成果名称</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[18] == null ? "" : obj[18]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">学术交流活动取得成绩</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[19] == null ? "" : obj[19]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">佐证材料类型</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[21] == null ? "" : obj[21]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\">提交成果作者人数</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1]) + "</div> ");
					infoHt.append("</div>");
					infoHt.append("<div class=\"profile-info-row\">");
					infoHt.append("    <div class=\"profile-info-name\">本人排序</div> ");
					infoHt.append("    <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("    <div class=\"profile-info-name\" style=\"background-color: white;\"></div> ");
					infoHt.append("    <div class=\"profile-info-value\"></div> ");
					infoHt.append("</div>");
				}
			}
			if (!"100048".equals(schoolCode)) {
				List<SysColConfig> colList = applyCommonDao.querySysColConfigByTabName("chx_xfrdsqb", cxxdm);
				if (colList.size() > 3) {
					for (int i = 0; i < colList.size(); i++) {
						if ((i + 1) % 3 == 1) {
							infoHt.append("<div class=\"profile-info-row\">");
						}
						String colname = colList.get(i).getColname();
						String colid = colList.get(i).getColid().toLowerCase();
						infoHt.append("<div class=\"profile-info-name\">" + colname + "</div>");
						if ("c1".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC1()) ? sqb.getC1() : "") + "</div>");
						} else if ("c2".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC2()) ? sqb.getC2() : "") + "</div>");
						} else if ("c3".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC3()) ? sqb.getC3() : "") + "</div>");
						} else if ("c4".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC4()) ? sqb.getC4() : "") + "</div>");
						} else if ("c5".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC5()) ? sqb.getC5() : "") + "</div>");
						} else if ("c6".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC6()) ? sqb.getC6() : "") + "</div>");
						} else if ("c7".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC7()) ? sqb.getC7() : "") + "</div>");
						} else if ("c8".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC8()) ? sqb.getC8() : "") + "</div>");
						} else if ("c9".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC9()) ? sqb.getC9() : "") + "</div>");
						} else if ("c10".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC10()) ? sqb.getC10() : "") + "</div>");
						} else if ("c11".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC11()) ? sqb.getC11() : "") + "</div>");
						} else if ("c12".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC12()) ? sqb.getC12() : "") + "</div>");
						} else if ("c13".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC13()) ? sqb.getC13() : "") + "</div>");
						} else if ("c14".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC14()) ? sqb.getC14() : "") + "</div>");
						} else if ("c15".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC15()) ? sqb.getC15() : "") + "</div>");
						} else if ("c16".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC16()) ? sqb.getC16() : "") + "</div>");
						} else if ("c17".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC17()) ? sqb.getC17() : "") + "</div>");
						} else if ("c18".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC18()) ? sqb.getC18() : "") + "</div>");
						} else if ("c19".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC19()) ? sqb.getC19() : "") + "</div>");
						} else if ("c20".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC20()) ? sqb.getC20() : "") + "</div>");
						}
						if ((i + 1) % 3 == 0 || i == (colList.size() - 1)) {
							infoHt.append("</div>");
						}
					}
				} else {
					infoHt.append("<div class=\"profile-info-row\">");
					for (int i = 0; i < colList.size(); i++) {
						String colname = colList.get(i).getColname();
						String colid = colList.get(i).getColid().toLowerCase();
						infoHt.append("<div class=\"profile-info-name\">" + colname + "</div>");
						if ("c1".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC1()) ? sqb.getC1() : "") + "</div>");
						} else if ("c2".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC2()) ? sqb.getC2() : "") + "</div>");
						} else if ("c3".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC3()) ? sqb.getC3() : "") + "</div>");
						} else if ("c4".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC4()) ? sqb.getC4() : "") + "</div>");
						} else if ("c5".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC5()) ? sqb.getC5() : "") + "</div>");
						} else if ("c6".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC6()) ? sqb.getC6() : "") + "</div>");
						} else if ("c7".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC7()) ? sqb.getC7() : "") + "</div>");
						} else if ("c8".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC8()) ? sqb.getC8() : "") + "</div>");
						} else if ("c9".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC9()) ? sqb.getC9() : "") + "</div>");
						} else if ("c10".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC10()) ? sqb.getC10() : "") + "</div>");
						} else if ("c11".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC11()) ? sqb.getC11() : "") + "</div>");
						} else if ("c12".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC12()) ? sqb.getC12() : "") + "</div>");
						} else if ("c13".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC13()) ? sqb.getC13() : "") + "</div>");
						} else if ("c14".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC14()) ? sqb.getC14() : "") + "</div>");
						} else if ("c15".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC15()) ? sqb.getC15() : "") + "</div>");
						} else if ("c16".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC16()) ? sqb.getC16() : "") + "</div>");
						} else if ("c17".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC17()) ? sqb.getC17() : "") + "</div>");
						} else if ("c18".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC18()) ? sqb.getC18() : "") + "</div>");
						} else if ("c19".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC19()) ? sqb.getC19() : "") + "</div>");
						} else if ("c20".equals(colid)) {
							infoHt.append("<div class=\"profile-info-value\">" + (StringUtils.isNotBlank(sqb.getC20()) ? sqb.getC20() : "") + "</div>");
						}
					}
					infoHt.append("</div>");
				}
			}
			infoHt.append("</div> ");
			if ("100048".equals(schoolCode)) {
				if("100".equals(cxxdm)||"101".equals(cxxdm)||"103".equals(cxxdm)||"104".equals(cxxdm)||"107".equals(cxxdm)){
					sql = "select tdcyxm,cyxx,xsm,zym from chx_xfrdsq_tdcyb where sqid='"+applyId+"'";
					List<Object[]> tdcy_list = applyCommonDao.findEntitiesBySQL(sql);
					if(tdcy_list!=null&&tdcy_list.size()>0){
						infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\">");
						infoHt.append("    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i>");
						if("100".equals(cxxdm)||"101".equals(cxxdm)){
							infoHt.append("    团队成员名单");
						}else if("103".equals(cxxdm)||"104".equals(cxxdm)){
							infoHt.append("    作者名单");
						}if("107".equals(cxxdm)){
							infoHt.append("    成果作者名单");
						}
						infoHt.append("    </h4>");
						infoHt.append("    <table class='table table-bordered table-hover table-striped'> ");
						infoHt.append("        <thead> ");
						infoHt.append("			   <tr><th>序号</th><th>姓名</th><th>学校</th><th>学院</th><th>所在专业</th></tr> ");
						infoHt.append("		   </thead> ");
						infoHt.append("		   <tbody>");
						for (int i = 0; i < tdcy_list.size(); i++) {
							Object[] temp = tdcy_list.get(i);
							infoHt.append("        <tr>");
							infoHt.append("            <td>" + (i + 1) + "</td>");
							infoHt.append("            <td>" + temp[0] + "</td>");
							infoHt.append("            <td>" + temp[1] + "</td>");
							infoHt.append("            <td>" + temp[2] + "</td>");
							infoHt.append("            <td>" + temp[3] + "</td>");
							infoHt.append("        </tr>");
						}
						infoHt.append("        </tbody> ");
						infoHt.append("	   </table> ");
						infoHt.append("</div>");
					}
				}
				if("100".equals(cxxdm)||"101".equals(cxxdm)||"103".equals(cxxdm)||"104".equals(cxxdm)||"107".equals(cxxdm)){
					sql = "select jsm,jsyx from chx_xfrdsq_zdjsb where sqid='"+applyId+"'";
					List<Object[]> zdjs_list = applyCommonDao.findEntitiesBySQL(sql);
					if(zdjs_list!=null&&zdjs_list.size()>0){
						infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\">");
						infoHt.append("    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i>");
						infoHt.append("        指导教师");
						infoHt.append("    </h4>");
						infoHt.append("    <table class='table table-bordered table-hover table-striped'> ");
						infoHt.append("        <thead> ");
						infoHt.append("			   <tr><th>序号</th><th>教师姓名</th><th>所在部门</th></tr> ");
						infoHt.append("		   </thead> ");
						infoHt.append("		   <tbody>");
						for (int i = 0; i < zdjs_list.size(); i++) {
							Object[] temp = zdjs_list.get(i);
							infoHt.append("        <tr>");
							infoHt.append("            <td>" + (i + 1) + "</td>");
							infoHt.append("            <td>" + temp[0] + "</td>");
							infoHt.append("            <td>" + temp[1] + "</td>");
							infoHt.append("        </tr>");
						}
						infoHt.append("        </tbody> ");
						infoHt.append("	   </table> ");
						infoHt.append("</div>");
					}
				}
				if("101".equals(cxxdm)){
					sql = "select a.cgmc,b.cglxmc,a.cggkqkmc,cggkxq from chx_xfrdsq_jxcgb a, chx_cxcy_cglxb b,chx_cxcy_cggkqkb c where a.cglxdm=b.cglxdm(+) and a.cglxdm=c.cglxdm(+) " +
							"and a.cggkqkdm=c.gkqkdm(+) and sqid='"+applyId+"'";
					List<Object[]> jxcg_list = applyCommonDao.findEntitiesBySQL(sql);
					if(jxcg_list!=null&&jxcg_list.size()>0){
						infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\">");
						infoHt.append("    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i>");
						infoHt.append("        结项成果");
						infoHt.append("    </h4>");
						infoHt.append("    <table class='table table-bordered table-hover table-striped'> ");
						infoHt.append("        <thead> ");
						infoHt.append("			   <tr><th>序号</th><th>成果名称</th><th>成果类型</th><th>	成果公开情况</th><th>成果公开详情</th></tr> ");
						infoHt.append("		   </thead> ");
						infoHt.append("		   <tbody>");
						for (int i = 0; i < jxcg_list.size(); i++) {
							Object[] temp = jxcg_list.get(i);
							infoHt.append("        <tr>");
							infoHt.append("            <td>" + (i + 1) + "</td>");
							infoHt.append("            <td>" + temp[0] + "</td>");
							infoHt.append("            <td>" + temp[1] + "</td>");
							infoHt.append("            <td>" + temp[2] + "</td>");
							infoHt.append("            <td>" + temp[3] + "</td>");
							infoHt.append("        </tr>");
						}
						infoHt.append("        </tbody> ");
						infoHt.append("	   </table> ");
						infoHt.append("</div>");
					}
				}

				infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\">");
				infoHt.append("    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i>");
				infoHt.append("        成果来源信息");
				infoHt.append("    </h4>");
				infoHt.append("    <div class=\"profile-user-info profile-user-info-striped self\">");

				infoHt.append("        <div class=\"profile-info-row\">");
				infoHt.append("            <div class=\"profile-info-name\">佐证材料</div> ");
				infoHt.append("            <div class=\"profile-info-value\">");
				sql = "select * from CHX_XFRDFJB where rdid='" + applyId + "'";
				List<ChxXfrdfjb> xfrdfjbList = applyCommonDao.findEntitiesBySQL(sql, ChxXfrdfjb.class);
				for (ChxXfrdfjb chxXfrdfjb : xfrdfjbList) {
					String fjmc = chxXfrdfjb.getFjmc();
					String fjlx = chxXfrdfjb.getFjlx();
					infoHt.append("            <div>");
					infoHt.append(fjmc+"."+fjlx);
					infoHt.append("                <a style='cursor: pointer;' class='blue' title='下载查看已上传文件' onclick=\"downFile('"+chxXfrdfjb.getFileurl()+"','"
							+fjmc+"."+fjlx+"');return false;\">" +
							"                          <i class='ace-icon fa fa-cloud-download bigger-130'></i>" +
							"                      </a>");
					if("pdf".equals(chxXfrdfjb.getFjlx().toLowerCase())||"png".equals(chxXfrdfjb.getFjlx().toLowerCase())||"jpg".equals(chxXfrdfjb.getFjlx().toLowerCase())){
						infoHt.append("            <a style='cursor: pointer;' class='blue' title='查看已上传文件' onclick=\"getApplyImageUrl('"+chxXfrdfjb.getId()+"','"+fjmc+"','"+fjlx+"','"+chxXfrdfjb.getFileurl()+"');return false;\">" +
								"                      <i class='ace-icon fa fa-eye bigger-130'></i>" +
								"                  </a>");
					}
					infoHt.append("            </div>");
				}
				infoHt.append("            </div>");
				infoHt.append("        </div>");
				infoHt.append("    </div> ");

				infoHt.append("    <div class=\"profile-user-info profile-user-info-striped self\">");
				infoHt.append("        <div class=\"profile-info-row\">");
				infoHt.append("            <div class=\"profile-info-name\">本成果的第一作者（主持人）单位是否为辽宁大学？</div> ");
				infoHt.append("            <div class=\"profile-info-value\">" + (obj[3] == null ? "" : obj[3]) + "</div> ");
				infoHt.append("            <div class=\"profile-info-name\">本成果是否来源于真实问题？</div> ");
				infoHt.append("            <div class=\"profile-info-value\">" + (obj[4] == null ? "" : obj[4]) + "</div> ");
				if("是".equals(obj[4]+"")){
					infoHt.append("        <div class=\"profile-info-name\">真实问题是否来自于“砍瓜网”？</div> ");
					infoHt.append("        <div class=\"profile-info-value\">" + (obj[5] == null ? "" : obj[5]) + "</div> ");
				}
				infoHt.append("        </div>");
				infoHt.append("    </div> ");

				if("是".equals(obj[4]+"")){
					infoHt.append("<div class=\"profile-user-info profile-user-info-striped self\">");
					infoHt.append("    <div class=\"profile-info-row\">");
					infoHt.append("        <div class=\"profile-info-name\">真实问题发生时间</div> ");
					infoHt.append("        <div class=\"profile-info-value\">" + (obj[6] == null ? "" : obj[6]) + "</div> ");
					infoHt.append("        <div class=\"profile-info-name\">真实问题发生地点</div> ");
					infoHt.append("        <div class=\"profile-info-value\">" + (obj[7] == null ? "" : obj[7]) + "</div> ");
					infoHt.append("    </div>");
					infoHt.append("</div>");

					infoHt.append("<div class=\"profile-user-info profile-user-info-striped self\">");
					infoHt.append("    <div class=\"profile-info-row\">");
					infoHt.append("        <div class=\"profile-info-name\">真实问题描述</div> ");
					infoHt.append("        <div class=\"profile-info-value\">" + (obj[8] == null ? "" : obj[8]) + "</div> ");
					infoHt.append("    </div>");
					infoHt.append("    <div class=\"profile-info-row\">");
					infoHt.append("        <div class=\"profile-info-name\">真实问题解决成效</div> ");
					infoHt.append("        <div class=\"profile-info-value\">" + (obj[9] == null ? "" : obj[9]) + "</div> ");
					infoHt.append("    </div>");
					infoHt.append("</div>");
				}


				infoHt.append("</div>");
			}else{
				infoHt.append("<div class=\"profile-user-info profile-user-info-striped self\"> " +
						"          <div class=\"profile-info-row\"> " +
						"              <div class=\"profile-info-name\">申请原因</div> " +
						"              <div class=\"profile-info-value\">" + sqb.getSqsm() + "</div> " +
						"          </div> ");
				infoHt.append("</div> ");
			}

			infoHt.append("</div>");
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return infoHt.toString();
	}

	@Override
	public String queryGraduateStudentHtml(String applyId) {
		EaApplysQu eaApplys = applyCommonDao.findById(EaApplysQu.class, applyId);
		String applyType = eaApplys == null ? "10022" : eaApplys.getApplyType();
		String sql = "select b.pcm,a.zxf,to_char(to_date(a.bmsj, 'YYYYMMDDHH24MISS'), 'YYYY-MM-DD HH24:MI:SS'),c.lxdh,a.xh,pn.xm(a.xh) from kw_jys_ksbmb a,kw_jys_ksbmpcb b, " +
				"kw_jys_jysmdb c where a.pch=b.pch(+) and a.pch = c.pch(+) and a.xh=c.xh(+) and a.sqbh='" + applyId + "'";
		Object[] obj = applyCommonDao.findEntityBySQL(sql);

		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> " + ("10023".equals(applyType) ? "结业生选课" : "结业生考试") + "申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">学号</div> " +
				"            <div class=\"profile-info-value\">" + (obj[4] == null ? "" : obj[4].toString()) + "</div> " +
				"            <div class=\"profile-info-name\">姓名</div> " +
				"            <div class=\"profile-info-value\">" + (obj[5] == null ? "" : obj[5].toString()) + "</div> " +
				"            <div class=\"profile-info-name\">批次</div> " +
				"            <div class=\"profile-info-value\">" + (obj[0] == null ? "" : obj[0].toString()) + "</div> " +
				"        </div>" +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">总学分</div> " +
				"            <div class=\"profile-info-value\">" + (obj[1] == null ? "" : obj[1].toString()) + "</div> " +
				"            <div class=\"profile-info-name\">联系电话</div> " +
				"            <div class=\"profile-info-value\">" + (obj[3] == null ? "" : obj[3].toString()) + "</div> " +
				"            <div class=\"profile-info-name\">报名时间</div> " +
				"            <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2].toString()) + "</div> " +
				"        </div>");
		infoHt.append("</div> " +
				"</div>");

		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"         <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 课程明细 </h4> ");
		infoHt.append("<div style=\"overflow-y:auto;\"> ");
		infoHt.append("<table class=\"table table-hover table-bordered table-striped\"> ");
		infoHt.append("  <thead> ");
		if ("10023".equals(applyType)) {
			infoHt.append(" <tr> ");
			infoHt.append("      <th style='text-align: center;' rowspan='2'>序号</th> ");
			infoHt.append("      <th style='text-align: center;' colspan='7'>已选课程信息</th> ");
			infoHt.append("      <th style='text-align: center;' colspan='6'>对应未通过课程信息</th> ");
			infoHt.append(" </tr> ");
		}
		infoHt.append("     <tr> ");
		if ("10022".equals(applyType)) {
			infoHt.append("      <th style='text-align: center;'>序号</th> ");
		}
		infoHt.append("          <th style='text-align: center;'>课程号</th> " +
				"                <th style='text-align: center;'>课程名</th>");
		infoHt.append("          <th style='text-align: center;'>课序号</th>");
		infoHt.append("         <th style='text-align: center;'>开课院系</th>");
		if ("10023".equals(applyType)) {
			infoHt.append("      <th style='text-align: center;'>课程类别</th>");
		}
		infoHt.append("          <th style='text-align: center;'>学分</th>" +
				"                <th style='text-align: center;'>学时</th>");
		if ("10023".equals(applyType)) {
			infoHt.append("      <th style='text-align: center;'>课程号</th> " +
					"            <th style='text-align: center;'>课程名</th>" +
					"            <th style='text-align: center;'>开课院系</th>" +
					"            <th style='text-align: center;'>学分</th>" +
					"            <th style='text-align: center;'>学时</th>" +
					"            <th style='text-align: center;'>是否学分替换</th>");
		}
		infoHt.append("      </tr> ");
		infoHt.append("   </thead> ");
		infoHt.append("   <tbody> ");
		sql = "select kch,pn.kcm(kch),kxh,pn.kc_kkxs(kch),pn.kc_xf(kch),pn.kc_xs(kch),pn.kc_kclb(kch),kcsxdm,dybjgkc,pn.kcm(dybjgkc),pn.kc_kkxs(dybjgkc),pn.kc_xf(dybjgkc)," +
				"pn.kc_xs(dybjgkc),decode(dybjgkc,null,'',decode(sfxfth, 1, '是', '否')) from kw_jys_ksbmmxb where sqbh='" + applyId + "' ORDER BY kch,kxh";
		List<Object[]> list = applyCommonDao.findEntitiesBySQL(sql);

		if (list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				infoHt.append("<tr>");
				infoHt.append("  <td style='text-align: center;'>" + (i + 1) + "</td>");
				infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[0] == null ? "" : list.get(i)[0]) + "</td>");
				infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[1] == null ? "" : list.get(i)[1]) + "</td>");
				infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[2] == null ? "" : list.get(i)[2]) + "</td>");
				infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[3] == null ? "" : list.get(i)[3]) + "</td>");
				if ("10023".equals(applyType)) {
					infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[6] == null ? "" : list.get(i)[6]) + "</td>");
				}
				infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[4] == null ? "" : list.get(i)[4]) + "</td>");
				infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[5] == null ? "" : list.get(i)[5]) + "</td>");
				if ("10023".equals(applyType)) {
					infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[8] == null ? "" : list.get(i)[8]) + "</td>");
					infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[9] == null ? "" : list.get(i)[9]) + "</td>");
					infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[10] == null ? "" : list.get(i)[10]) + "</td>");
					infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[11] == null ? "" : list.get(i)[11]) + "</td>");
					infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[12] == null ? "" : list.get(i)[12]) + "</td>");
					infoHt.append("  <td style='text-align: center;'>" + (list.get(i)[13] == null ? "" : list.get(i)[13]) + "</td>");
				}
				infoHt.append("</tr>");
			}
		}
		infoHt.append("  </tbody>");
		infoHt.append("</table>");

		infoHt.append("</div>");
		infoHt.append("</div>");

		return infoHt.toString();
	}

	@Override
	public String queryChangeStudentInfoHtml(String applyId) {

		List<Object[]> xsGgxjXssqmxbList = applyCommonDao.queryXsGgxjXssqmxbByApplyId(applyId);
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		StringBuffer infoHt = new StringBuffer();
		try {
			infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
					"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> " +
					" 申请更改学籍信息 </h4> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请人</div> " +
					"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
					"            <div class=\"profile-info-name\">申请时间</div> " +
					"            <div class=\"profile-info-value\">" + applys.getcommit_dtStr() + "</div> " +
					"            <div class=\"profile-info-name\">审批状态</div> " +
					"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
					"        </div></div>");
			infoHt.append("<div class=\"profile-user-info profile-user-info-striped self\"> ");
			for (Object[] objs : xsGgxjXssqmxbList) {
				infoHt.append("<div class=\"profile-info-row\"> " +
						"            <div class=\"profile-info-name\">" + (objs[3] == null ? "" : objs[3]) + "</div> " +
						"            <div class=\"profile-info-value\">" + (objs[1] == null ? "" : objs[1]) + "</div> " +
						"            <div class=\"profile-info-name\">" + (objs[4] == null ? "" : objs[4]) + "</div> " +
						"            <div class=\"profile-info-value\">" + (objs[2] == null ? "" : objs[2]) + "</div> " +
						"        </div> ");
			}
			infoHt.append("</div>");
			infoHt.append("</div>");
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return infoHt.toString();
	}


	@Override
	public List<Object[]> queryProcessLinkByApplyId(String applyId) {
		return applyCommonDao.queryProcessLinkByApplyId(applyId);
	}

	@Override
	public List<Object[]> queryEaResultsByApplyId(String applyId) {
		String schoolCode = CommonUtils.queryParamValue();
		String sql = "select * from (select a.eal_code,(select c.eal_name from ea_links c where a.eal_code = c.eal_code) eal_name,a.eal_rslt,a.eal_desc,a.eal_user,";
		if ("100006".equals(schoolCode)) {
			sql += "substr(nvl(pn.jsm(a.eal_user), a.eal_user),0,1)||decode(a.eal_user,null,'','**'),";
		} else {
			sql += "nvl(pn.jsm(a.eal_user),a.eal_user),";
		}
		sql += "to_char(to_date(a.eal_time, 'yyyymmddhh24miss'),'yyyy-mm-dd hh24:mi:ss'),a.eal_ip,a.eal_order from ea_result a where a.apply_id = '" + applyId + "' " +
				"union select a.eal_code,(select c.eal_name from ea_links c where a.eal_code = c.eal_code) eal_name,null,'','','','','',a.eal_order " +
				"from ea_process_link a where a.in_use='1' and a.eap_code in (select eap_code from ea_result where apply_id = '" + applyId + "') and not exists" +
				"(select 1 from ea_result c where c.apply_id = '" + applyId + "' and a.eap_code=c.eap_code and a.eal_code=c.eal_code and a.eal_order=c.eal_order)) order by eal_order";
		return applyCommonDao.findEntitiesBySQL(sql);
	}

	@Override
	public List<Object[]> queryEaRsltParallelByApplyId(String applyId) {
		return applyCommonDao.queryEaRsltParallelByApplyId(applyId);
	}

	@Override
	public String queryXm(String rollbackUser) {
		return applyCommonDao.queryXm(rollbackUser);
	}

	@Override
	public String queryJsm(String rollbackUser) {
		return applyCommonDao.queryJsm(rollbackUser);
	}

	@Override
	public List<Object[]> queryEalByApplyType(String applyType) {
		return applyCommonDao.queryEalByApplyType(applyType);
	}

	@Override
	public List<EaProcess> queryEaProcess(String apply_type) {
		return applyCommonDao.queryEaProcess(apply_type);
	}

	@Override
	public List<EaProcessLink> queryEaProcessLink(String eapCode) {
		return applyCommonDao.queryEaProcessLink(eapCode);
	}

	@SuppressWarnings("deprecation")
	@Override
	public Map<String, Object> queryApprovers(String sql, String apply_type,
			Object[] names, String sqbh, String zxjxjhh, String others) {
		Map<String, Object> map = new HashMap<String, Object>();
		Session session = (Session) em.getDelegate();
		SessionFactoryImplementor sf = (SessionFactoryImplementor) session.getSessionFactory();
		Connection conn = null;
		CallableStatement cs = null;
		PreparedStatement ps = null;
		String czr = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
		//String zxjxjhh = CommonUtils.queryCurrentXnxq();
		String czip = CommonUtils.getRemoteHostOnService();
		String czsj = CommonUtils.queryCurrentTimeMinBySql();
		try {
			conn = sf.getConnectionProvider().getConnection();
			conn.setAutoCommit(false);//把自动提交方式变为人工
			ps = conn.prepareStatement(sql);
			ps.executeUpdate();
			ps.close();
			if (StringUtils.isNotBlank(others) && "10003".equals(apply_type)) {
				String[] kc = others.split(",");
				for (int i = 0; i < kc.length; i++) {
					String[] id = kc[i].split("_");
					sql = "insert into xk_bxksqkcb (id, sqbh, zxjxjhh, kch, kxh) values (sys_guid(), '" + sqbh + "', '" + id[2] + "','" + id[0] + "', '" + id[1] + "')";
					ps = conn.prepareStatement(sql);
					ps.executeUpdate();
					ps.close();
				}
			}
			sql = "insert into ea_applys (apply_id, apply_type, user_code, commit_dt, rollback_dt, apply_status, ea_rslt, note, " +
					"zxjxjhh)values ('" + sqbh + "', '" + apply_type + "', '" + czr + "', '" + czsj + "', '', 1, null, null, '" + zxjxjhh + "')";
			ps = conn.prepareStatement(sql);
			ps.executeUpdate();
			ps.close();
			sql = "insert into ea_result (apply_id, eap_code, eal_code, eal_order, over_enabled, eal_rslt) values ('" + sqbh + "'," +
					"'" + names[2] + "','" + names[3] + "','" + names[4] + "','0','0')";
			ps = conn.prepareStatement(sql);
			ps.executeUpdate();
			ps.close();
			/*sql = "select c.jsh,c.jsm,pn.xsm(c.org_id) as xsm from urp_user_role a,urp_user b,code_jsb c where c.jsh = b.idnumber " +
					"and a.user_id = b.id and pkg_others.f_approvable('"+sqbh+"', '"+names[3]+"', c.jsh) = 1 and role_id = '"+names[1]+"'";*/

			sql = "SELECT i.* " +
					"  FROM (SELECT c.jsh, c.jsm,pn.xsm(c.org_id) AS xsm," +
					"       pkg_others.f_approvable('" + sqbh + "', '" + names[3] + "', c.jsh) AS passable " +
					"       FROM urp_user_role a, urp_user b, code_jsb c" +
					"      WHERE c.jsh = b.idnumber" +
					"        AND a.user_id = b.id" +
					"        AND a.role_id = '" + names[1] + "'" +
					"        AND b.enabled = '1'" +
					"        AND c.zzztdm = '01') i  where i.passable = '1'";
			ps = conn.prepareStatement(sql);
			ResultSet rs = ps.executeQuery();
			List<Object[]> approvers = convertList(rs);
			rs.close();
			map.put("approvers", approvers);
			conn.rollback();//出现异常进行回滚；
		} catch (Exception e) {
			try {
				conn.rollback();//出现异常进行回滚；
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		} finally {
			try {
				conn.setAutoCommit(true);
			} catch (SQLException e) {
				e.printStackTrace();
			}
			CommonUtils.closeDB(conn, cs, ps);

		}
		return map;
	}


	@SuppressWarnings("deprecation")
	@Override
	public Map<String, Object> queryApproversByApplyId(String sql,String sqbh,String apply_type, String zxjxjhh) {

		Map<String, Object> map = new HashMap<String, Object>();

		Session session = (Session) em.getDelegate();
		SessionFactoryImplementor sf = (SessionFactoryImplementor) session.getSessionFactory();
		Connection conn = null;
		CallableStatement cs = null;
		PreparedStatement ps = null;
		String czr = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
		String czip = CommonUtils.getRemoteHostOnService();
		String czsj = CommonUtils.queryCurrentTimeMinBySql();
		String approvers = "";
		String msg = "";

		try {
			conn = sf.getConnectionProvider().getConnection();
			conn.setAutoCommit(false);//把自动提交方式变为人工
			ps = conn.prepareStatement(sql);
			ps.executeUpdate();
			ps.close();

			sql = "insert into ea_applys (apply_id, apply_type, user_code, commit_dt, rollback_dt, apply_status, ea_rslt, note, " +
					"zxjxjhh)values ('" + sqbh + "', '" + apply_type + "', '" + czr + "', '" + czsj + "', '', 1, null, null, '" + zxjxjhh + "')";
			ps = conn.prepareStatement(sql);
			ps.executeUpdate();
			ps.close();

			/**
			 *   --    数据错误，返回e,拼接错误信息
				 --    无需指定审批人或当前环节尚有并行审批的其他环节尚未完成审批，返回N
				 --    当前环节为终审环节，返回E
				 --    正常取得后续环节列表，返回0,拼接HTML<TABLE>格式表格
			 */
			sql = "{call pkg_ea.p_plpInitial(?, ?)}";
			cs = conn.prepareCall(sql);
			cs.setString(1, sqbh);
			cs.registerOutParameter(2, Types.CLOB);
			cs.executeUpdate();
			Clob clob = cs.getClob(2);
			String result = clobToString(clob);
			if (result.startsWith("e,")) {
				msg = result.substring(2);
				approvers = "error";
			} else if ("N".equals(result)) {
				msg = "无需指定审批人或当前环节尚有并行审批的其他环节尚未完成审批";
			} else if ("E".equals(result)) {
				msg = "当前环节为终审环节";
			} else if (result.startsWith("0,")) {
				approvers = result.substring(2);
			}

			map.put("approvers", approvers);
			map.put("msg", msg);

			conn.rollback();//出现异常进行回滚；
		} catch (Exception e) {
			try {
				conn.rollback();//出现异常进行回滚；
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		} finally {
			try {
				conn.setAutoCommit(true);
			} catch (SQLException e) {
				e.printStackTrace();
			}
			CommonUtils.closeDB(conn, cs, ps);

		}
		return map;

	}

	public String clobToString(Clob clob) throws SQLException, IOException {
		if (clob == null) {
			return null;
		}

		StringBuilder sb = new StringBuilder();
		Reader reader = clob.getCharacterStream();
		BufferedReader br = new BufferedReader(reader);

		String line;
		while ((line = br.readLine()) != null) {
			sb.append(line);
		}
		br.close();

		return sb.toString();
	}

	private static List<Object[]> convertList(ResultSet rs) throws SQLException {
		List<Object[]> list = new ArrayList<Object[]>();
		ResultSetMetaData md = rs.getMetaData();//获取键名
		int columnCount = md.getColumnCount();//获取行的数量
		while (rs.next()) {
			Object[] obj = new Object[4];
			if ("1".equals(rs.getObject(4))) {
				for (int i = 1; i <= columnCount; i++) {

					obj[i - 1] = rs.getObject(i);//获取键名及值
				}
				list.add(obj);
			}
		}
		return list;
	}


	@Override
	public String achievementRecognitionHtml(String applyId) {
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);

		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		CjCjrdsqb cjCjrdsqb = applyCommonDao.findById(CjCjrdsqb.class, applyId);

		List<Object[]> cjCjrdmxbList = applyCommonDao.queryCjCjrdmxbBySqbh(applyId);

		StringBuffer otherHt = new StringBuffer();
		JSONObject jsonObject = JSONObject.fromObject(cjCjrdsqb);
		List<SysColConfig> sysColConfigs = applyCommonDao.querySysColConfigByTabName("cj_cjrdsqb");
		if (sysColConfigs != null && sysColConfigs.size() > 0) {
			otherHt.append("<div class=\"profile-info-row\">");
			for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
				SysColConfig sysColConfig = sysColConfigs.get(i);
				if (i != 0 && i % 3 == 0) {
					otherHt.append("</div><div class=\"profile-info-row\">");
				}
				otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
				if (i + 1 == sysColConfigs.size()) {
					if (sysColConfigs.size() == 1 || sysColConfigs.size() == 4) {
						otherHt.append("<div class=\"profile-info-name\" style=\"background-color: white;\"></div> " +
								"<div class=\"profile-info-value\"></div> " +
								"<div class=\"profile-info-name\" style=\"background-color: white;\"></div> " +
								"<div class=\"profile-info-value\"></div> ");
					}
					if (sysColConfigs.size() == 2 || sysColConfigs.size() == 5) {
						otherHt.append("<div class=\"profile-info-name\" style=\"background-color: white;\"></div> " +
								"<div class=\"profile-info-value\"></div> ");
					}
				}
			}
			otherHt.append("</div>");
		}

		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"fa fa-user\"></i> 成绩认定申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + cjCjrdsqb.getCzsj() + "</div> " +
				"            <div class=\"profile-info-name\">审批状态</div> " +
				"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
				"        </div> ");
		infoHt.append(otherHt);
		infoHt.append("    </div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">备注</div> " +
				"            <div class=\"profile-info-value\">" + (cjCjrdsqb.getBz() == null ? "" : cjCjrdsqb.getBz()) + "</div> " +
				"        </div> " +
				"    </div> " +
				"</div>");

		infoHt.append("<h4 class=\"header smaller lighter grey\"> " +
				"    <i class=\"glyphicon glyphicon-list\"></i> " +
				"    成绩认定课程列表 " +
				"</h4> " +
				"<table class=\"table table-hover table-bordered table-striped table_td\" id=\"tkkctable\"> " +
				"    <thead> " +
				"    <tr> " +
				"        <th>学年学期</th> " +
				"        <th>开课院系</th> " +
				"        <th>课程号</th> " +
				"        <th>课程名</th> " +
				"        <th>课序号</th> " +
				"        <th>考试时间</th> " +
				"        <th>课程成绩</th> " +
				"        <th>等级成绩</th> " +
				"        <th>学分</th> " +
				"    </tr> " +
				"    </thead> " +
				"    <tbody>");
		for (int i = 0; i < cjCjrdmxbList.size(); i++) {
			Object[] obj = cjCjrdmxbList.get(i);
			infoHt.append("<tr>");
			infoHt.append("<td>" + (obj[0] == null ? "" : obj[0]) + "</td>");
			infoHt.append("<td>" + (obj[1] == null ? "" : obj[1]) + "</td>");
			infoHt.append("<td>" + (obj[2] == null ? "" : obj[2]) + "</td>");
			infoHt.append("<td>" + (obj[3] == null ? "" : obj[3]) + "</td>");
			infoHt.append("<td>" + (obj[4] == null ? "" : obj[4]) + "</td>");
			infoHt.append("<td>" + (obj[5] == null ? "" : obj[5]) + "</td>");
			infoHt.append("<td>" + (obj[6] == null ? "" : obj[6]) + "</td>");
			infoHt.append("<td>" + (obj[7] == null ? "" : obj[7]) + "</td>");
			infoHt.append("<td>" + (obj[8] == null ? "" : obj[8]) + "</td>");
			infoHt.append("</tr>");
		}
		infoHt.append("</tbody></table></div>");
		return infoHt.toString();
	}

	@Override
	public String reviewScoreReductionHtml(String applyId) {
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);

		Object[] byScjfsqb = applyCommonDao.queryByScjfsqbBySqbh(applyId);
		List<Object[]> byScjfsqmxbs = applyCommonDao.queryByScjfsqmxbBySqbh(applyId);
		String xslbs = applyCommonDao.queryYxXslbs(byScjfsqb[2] + "");
		String tsxslbs = applyCommonDao.queryYxTsxslbs(byScjfsqb[2] + "");
		String xsbqs = applyCommonDao.queryYxTags(byScjfsqb[2] + "");
		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> 审查降分申请 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + byScjfsqb[7] + "（" + byScjfsqb[1] + "）" + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + byScjfsqb[3] + "</div> " +
				"            <div class=\"profile-info-name\">审批状态</div> " +
				"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
				"        </div> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">学生类别</div> " +
				"            <div class=\"profile-info-value\">" + (byScjfsqb[8] == null ? "" : byScjfsqb[8]) + "</div> " +
				"            <div class=\"profile-info-name\">特殊学生类型</div> " +
				"            <div class=\"profile-info-value\">" + (byScjfsqb[9] == null ? "" : byScjfsqb[9]) + "</div> " +
				"            <div class=\"profile-info-name\">学生标签</div> " +
				"            <div class=\"profile-info-value\">" + (byScjfsqb[10] == null ? "" : byScjfsqb[10]) + "</div> " +
				"        </div> " +
				"    </div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请政策</div> " +
				"            <div class=\"profile-info-value\">" + byScjfsqb[6] + "。<br>" + (StringUtils.isNotBlank(xslbs) ? "允许申请学生类别" + xslbs + "<br>" : "") +
				(StringUtils.isNotBlank(tsxslbs) ? "允许申请特殊学生类型" + tsxslbs + "<br>" : "") + (StringUtils.isNotBlank(xsbqs) ? "允许申请学生标签" + xsbqs + "<br>" : "") + "</div> " +
				"        </div> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">备注</div> " +
				"            <div class=\"profile-info-value\">" + byScjfsqb[5] + "</div> " +
				"        </div> " +
				"    </div> " +
				"</div>");
		infoHt.append("      <h4 class=\"header smaller lighter grey\"> " +
				"        <i class=\"glyphicon glyphicon-list\"></i>申请课程</h4> " +
				"      <table class=\"table table-hover table-bordered table-striped table_td\"> " +
				"        <thead> " +
				"          <tr> " +
				"            <th>课程号</th> " +
				"            <th>课程名</th> " +
				"            <th>课程成绩</th></tr> " +
				"        </thead> " +
				"        <tbody > ");
		for (int i = 0, ten = byScjfsqmxbs.size(); i < ten; i++) {
			Object[] obj = byScjfsqmxbs.get(i);
			infoHt.append("<tr><td>" + obj[1] + "</td><td>" + obj[3] + "</td><td>" + obj[2] + "</td></tr>");
		}
		infoHt.append("</tbody></table> ");
		return infoHt.toString();
	}


	@Override
	public List<Object[]> queryEaRsltByApplyId(String tableName, String applyId, String applyStatus) {
		String schoolCode = CommonUtils.queryParamValue();
		String sql = "select * from (SELECT r.eal_code,DECODE(r.eal_code, '00000', '课程替代自动审批', l.eal_name) AS eal_name, r.eal_rslt,r.eal_desc,r.eal_user, ";
		if ("100006".equals(schoolCode)) {
			sql += "DECODE(r.eal_code, '00000', '课程替代自动审批', substr(nvl(pn.jsm(r.eal_user), r.eal_user),0,1)||decode(r.eal_user,null,'','**')) AS jsm,";
		} else {
			sql += "DECODE(r.eal_code, '00000', '课程替代自动审批', pn.jsm(r.eal_user)) AS jsm,";
		}
		sql += "TO_CHAR(TO_DATE(r.eal_time, 'YYYYMMDDHH24MISS'), 'YYYY-MM-DD HH24:MI:SS') AS eal_time," +
				"r.eal_ip,r.eal_org, DECODE(r.eal_code, '00000', '课程替代自动审批', DECODE(r.eal_org, '00', '', pn.xsm(r.eal_org))) AS xsm,r.eal_order FROM " + tableName + " r, ea_links l " +
				"WHERE r.eal_code = l.eal_code(+) AND " + applyStatus + " NOT IN (1, 2) AND r.apply_id = '" + applyId + "' " +
				"UNION SELECT d.eal_code, l.eal_name AS eal_name, r.eal_rslt, r.eal_desc, r.eal_user, pn.jsm(r.eal_user) AS jsm," +
				"TO_CHAR(TO_DATE(r.eal_time, 'YYYYMMDDHH24MISS'), 'YYYY-MM-DD HH24:MI:SS') AS eal_time, r.eal_ip, r.eal_org, " +
				"DECODE(r.eal_org, '00', '', pn.xsm(r.eal_org)) AS xsm,d.eal_order FROM ea_process_link d, " + tableName + " r, ea_links l WHERE d.eal_code = l.eal_code " +
				"AND d.eap_code = (SELECT p.eap_code FROM ea_applys a, ea_process p WHERE a.apply_type = p.apply_type AND p.in_use = 1 AND a.apply_id = '" + applyId + "')" +
				"AND d.eal_code = r.eal_code(+) AND " + applyStatus + " IN (1, 2) AND r.apply_id(+) = '" + applyId + "' ) order by eal_order";
		return applyCommonDao.findEntitiesBySQL(sql);
	}

	@Override
	public String destinationApplyHtml(String applyId) {
		Object[] obj = applyCommonDao.queryTmXstmqxsqbByApplyId(applyId);
		EaApplys applys = applyCommonDao.findById(EaApplys.class, applyId);
		XsXjb xsXjb = applyCommonDao.findById(XsXjb.class, applys.getUser_code());
		StringBuffer infoHt = new StringBuffer();
		try {
			infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
					"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> " +
					" 推免去向申请 </h4> " +
					"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">申请人</div> " +
					"            <div class=\"profile-info-value\">" + xsXjb.getXm() + "</div> " +
					"            <div class=\"profile-info-name\">申请时间</div> " +
					"            <div class=\"profile-info-value\">" + applys.getcommit_dtStr() + "</div> " +
					"            <div class=\"profile-info-name\">审批状态</div> " +
					"            <div class=\"profile-info-value\">" + getApplyStatusTran(applys.getApply_status()) + "</div> " +
					"        </div>" +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">接收院校</div> " +
					"            <div class=\"profile-info-value\">" + (obj[2] == null ? "" : obj[2]) + "</div> " +
					"            <div class=\"profile-info-name\">接收专业</div> " +
					"            <div class=\"profile-info-value\">" + (obj[3] == null ? "" : obj[3]) + "</div> " +
					"            <div class=\"profile-info-name\">接收类别</div> " +
					"            <div class=\"profile-info-value\">" + (obj[5] == null ? "" : obj[5]) + "</div> " +
					"        </div>" +
					"        <div class=\"profile-info-row\"> " +
					"            <div class=\"profile-info-name\">接收学院类别1</div> " +
					"            <div class=\"profile-info-value\">" + (obj[7] == null ? "" : obj[7]) + "</div> " +
					"            <div class=\"profile-info-name\">接收学院类别2</div> " +
					"            <div class=\"profile-info-value\">" + (obj[9] == null ? "" : obj[9]) + "</div> " +
					"            <div class=\"profile-info-name\">备注</div> " +
					"            <div class=\"profile-info-value\">" + (obj[13] == null ? "" : obj[13]) + "</div> " +
					"        </div></div>");

			infoHt.append("</div>");
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return infoHt.toString();
	}

	@Override
	public String queryCreditCertificationInfoHtml(Model model, String applyId, boolean mobile) {

		StringBuffer infoHt = new StringBuffer();
		try {
			String xh = AuthUtil.getCurrentUser().getIdNumber();
			XfrzXssqb xssqb = applyCommonDao.findById(XfrzXssqb.class, applyId);
			if (xssqb != null && xh.equals(xssqb.getXh())) {
				String cjdid = xssqb.getCjdid();
				String xfrzlxm = xssqb.getXfrzlxm();
				CodeXfrzlxb xfrzlxb = applyCommonDao.findById(CodeXfrzlxb.class, xfrzlxm);
				XfrzXscjd xscjd = applyCommonDao.findById(XfrzXscjd.class, cjdid);
				String kcrdfs = xfrzlxb.getKcrdfs();
				String xzjlxm = xfrzlxb.getXzjlxm();
				String xsxqf = xfrzlxb.getXsxqf();
				String cjrdfs = xfrzlxb.getCjrdfs();
				model.addAttribute("xfrzlxb", xfrzlxb);

				String row_div = "profile-info-row";
				String name_div = "profile-info-name";
				String value_div = "profile-info-value";
				if(mobile){
					row_div = "phone-profile-info-row";
					name_div = "col-xs-3 phone-row-title";
					value_div = "col-xs-9 phone-row-value";
				}

				infoHt.append("<div class=\"col-xs-12\" " + (mobile ? "" : "style=\"padding-left: 0px;padding-right: 0px;\"") + " >");
				if(mobile){
					infoHt.append("<div class=\"widget-box\">");
					infoHt.append("    <div class='widget-header widget-header-flat'>");
					infoHt.append("        <h4 class=\"widget-title lighter\"><i class=\"ace-icon fa fa-info-circle\"></i> 申请信息</h4>");
					infoHt.append("        <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
					infoHt.append("    </div>");
					infoHt.append("    <div class='widget-body'><div class='widget-main' style='padding: 0px;'><div class='dialogs ace-scroll scroll-active'>");
				}else{
					infoHt.append("<h4 class=\"header smaller lighter grey\"><i class=\"ace-icon fa fa-info-circle\"></i> 申请信息</h4>");
					infoHt.append("    <div class=\"profile-user-info profile-user-info-striped self\">");
				}

				infoHt.append("        <div class=\"" + row_div + "\"> ");
				infoHt.append("            <div class=\"" + name_div + "\"> 交流学校</div>");
				infoHt.append("  	       <div class=\"" + value_div + "\">" + (xssqb.getCodeJlxxb() != null ? xssqb.getCodeJlxxb().getJlxxmc() : "") + "</div>");
				if(mobile){
					infoHt.append("    </div>");
					infoHt.append("    <div class=\"" + row_div + "\">");
				}
				infoHt.append("            <div class=\"" + name_div + "\"> 开始学年学期</div>");
				infoHt.append("  	       <div class=\"" + value_div + "\">" + (StringUtils.isNotBlank(xssqb.getKsxnxq()) ? CommonUtils.getZxjxjhmByzxjxjhh(xssqb.getKsxnxq()) : "") + "</div>");
				if(mobile){
					infoHt.append("    </div>");
					infoHt.append("    <div class=\"" + row_div + "\">");
				}
				infoHt.append("            <div class=\"" + name_div + "\"> 结束学年学期</div>");
				infoHt.append("  	       <div class=\"" + value_div + "\">" + (StringUtils.isNotBlank(xssqb.getJsxnxq()) ? CommonUtils.getZxjxjhmByzxjxjhh(xssqb.getJsxnxq()) : "") + "</div>");
				infoHt.append("        </div>");

				infoHt.append("        <div class=\"" + row_div + "\"> ");
				infoHt.append("            <div class=\"" + name_div + "\"> 申请日期</div> " +
						"       	       <div class=\"" + value_div + "\">" + xssqb.getCzsj_dtStr() + "</div> ");
				if(mobile){
					infoHt.append("    </div>");
					infoHt.append("    <div class=\"" + row_div + "\">");
				}
				infoHt.append("	 	       <div class=\"" + name_div + "\"> 申请状态</div>");
				infoHt.append("            <div class=\"" + value_div + "\">" + getApplyStatusTran(xssqb.getSqzt()) + "</div> ");
				if(mobile){
					infoHt.append("    </div>");
					infoHt.append("    <div class=\"" + row_div + "\">");
				}
				infoHt.append(" 	       <div class=\"" + name_div + "\"> 成绩单</div>");
				infoHt.append(" 	       <div class=\"" + value_div + "\">" + xscjd.getFilename());
				infoHt.append("      	       <a style='cursor: pointer;' class='blue' title='下载成绩单' onclick=\"downFile('" + xscjd.getFileurl() + "','" + xscjd.getFilename() + "');return false;\"><i class='ace-icon fa fa-cloud-download bigger-130'></i></a>");
				if (xscjd.getFilename().toLowerCase().endsWith("pdf") || xscjd.getFilename().toLowerCase().endsWith("png") || xscjd.getFilename().toLowerCase().endsWith("jpg") || xscjd.getFilename().toLowerCase().endsWith("jpeg")) {
					infoHt.append("            <a style='cursor: pointer;' class='blue' title='预览成绩单' onclick=\"getApplyImageUrl('" + xscjd.getCjdid() + "','" + xscjd.getFilename() + "','other','" + xscjd.getFileurl() + "');return false;\"><i class='ace-icon fa fa-eye bigger-130'></i></a>");
				}
				infoHt.append("            </div>");
				infoHt.append("        </div>");

				infoHt.append("        <div class=\"" + row_div + "\"> ");
				if ("1".equals(xzjlxm)) {
					XfrzJlxmb jlxmb = applyCommonDao.findById(XfrzJlxmb.class, xscjd.getJlxmm());
					infoHt.append("        <div class=\"" + name_div + "\"> 交流项目</div>");
					infoHt.append("        <div class=\"" + value_div + "\">" + (jlxmb != null ? jlxmb.getJlxmmc() : "") + "</div>");
				}
				if(mobile){
					infoHt.append("    </div>");
					infoHt.append("    <div class=\"" + row_div + "\">");
				}
				if ("G".equals(kcrdfs)) {
					infoHt.append("        <div class=\"" + name_div + "\"> 课组类别</div>");
					infoHt.append("        <div class=\"" + value_div + "\">" + (xssqb.getJhKzlbb() != null ? xssqb.getJhKzlbb().getKzlbmc() : "") + "</div>");
				}
				infoHt.append("        </div>");

				if(mobile){
					infoHt.append("</div></div></div></div>");
				}else{
					infoHt.append("    </div>");
				}
				infoHt.append("</div>");


				infoHt.append("<div class=\"col-xs-12\" " + (mobile ? "" : "style=\"padding-left: 0px;padding-right: 0px;\"") + ">");
				if ("1".equals(kcrdfs) || "G".equals(kcrdfs)) {
					if(mobile){
						infoHt.append("<div class=\"widget-box\">");
						infoHt.append("    <div class='widget-header widget-header-flat'>");
						infoHt.append("        <h4 class=\"widget-title lighter\"><i class=\"glyphicon glyphicon-list\"></i> 课程信息</h4>");
						infoHt.append("        <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
						infoHt.append("    </div>");
						infoHt.append("    <div class='widget-body'><div class='widget-main' style='padding: 0px;'>");
						infoHt.append("        <div class='dialogs ace-scroll scroll-active' style='overflow: auto;'>");
					}else{
						infoHt.append("<h4 class='header smaller lighter grey'><i class='glyphicon glyphicon-list'></i> 课程信息</h4> ");
						infoHt.append("<div class='widget-content widget-box' style='max-height: calc(100vh - 300px);overflow: auto;'> ");
					}
					infoHt.append("    <table class=\"table table-hover table-bordered table-striped\"> ");
					infoHt.append("        <thead> ");
					if ("1".equals(kcrdfs)) {
						infoHt.append("        <tr> ");
						infoHt.append("            <th rowspan='2' style='width: 50px;white-space: nowrap;text-align: center;'>序号</th>");
						infoHt.append("            <th colspan='" + ("1".equals(xsxqf) ? 5 : 4) + "' style='white-space: nowrap;text-align: center;'>校外课程</th>");
						infoHt.append("            <th colspan='" + ("ST".equals(cjrdfs) ? 7 : 6) + "' style='white-space: nowrap;text-align: center;'>校内课程</th>");
						infoHt.append("        </tr>");
						infoHt.append("        <tr>");
						if("1".equals(xsxqf)){
							infoHt.append("        <th style='white-space: nowrap;text-align: center;'>学年学期</th>");
						}
						infoHt.append("            <th style='white-space: nowrap;text-align: center;'>课程名</th>");
						infoHt.append("            <th style='white-space: nowrap;text-align: center;'>英文课程名</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>学分</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>成绩</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>学年学期</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>课程号</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>课程名</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>课程属性</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>学分</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>学时</th>");
						if ("ST".equals(cjrdfs)) {
							infoHt.append("  	   <th style='white-space: nowrap;text-align: center;'>对应校内成绩</th>");
						}
						infoHt.append("        </tr> ");
					} else {
						infoHt.append("        <tr> ");
						infoHt.append("            <th style='width: 50px;white-space: nowrap;text-align: center;'>序号</th>");
						if("1".equals(xsxqf)){
							infoHt.append("        <th style='white-space: nowrap;text-align: center;'>学年学期</th>");
						}
						infoHt.append("            <th style='white-space: nowrap;text-align: center;'>课程名</th> ");
						infoHt.append("            <th style='white-space: nowrap;text-align: center;'>英文课程名</th> ");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>学分</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>成绩</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>所属课组</th>");
						if ("ST".equals(cjrdfs)) {
							infoHt.append("  	   <th style='white-space: nowrap;text-align: center;'>对应校内成绩</th>");
						}
						infoHt.append("        </tr> ");
					}
					infoHt.append("        </thead> ");
					infoHt.append("        <tbody id='kccj_tbody'> ");

					String sql = "select pn.xnxqmc(b.zxjxjhh) xnxqmc,b.kcm,b.ywkcm,b.xf,b.cj,pn.xnxqmc(a.zxjxjhh),a.kch,pn.kcm(a.kch),pn.xs_kcsx('" + xssqb.getXh() + "',a.kch),pkg_com.f_NNF(pn.kc_xf(a.kch))," +
							"pkg_com.f_NNF(pn.kc_xs(a.kch)),nvl(a.xghkccj,a.kccj),pkg_cj.f_GradeName(nvl(a.xghkccj,a.kccj)),b.kzh,b.wbkccjid,a.id ";
					if ("G".equals(kcrdfs)) {
						sql += ",c.kzm ";
					}
					sql += " from XFRZ_RDXSXNKCCJ a,XFRZ_XSKCCJ b ";
					if ("G".equals(kcrdfs)) {
						sql += ",jh_mkkzxxb_all c ";
					}
					sql += " where a.wbkccjid=b.wbkccjid and a.sqbh='" + applyId + "'";
					if ("G".equals(kcrdfs)) {
						sql += " and b.kzh=c.kzh(+)";
					}
					List<Object[]> list = applyCommonDao.findEntitiesBySQL(sql);
					if (list != null && list.size() > 0) {
						for (int i = 0; i < list.size(); i++) {
							infoHt.append("    <tr>");
							infoHt.append("        <td style='text-align: center;'>" + (i + 1) + "</td>");
							if("1".equals(xsxqf)){
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[0] == null ? "" : list.get(i)[0]) + "</td>");
							}
							infoHt.append("        <td style='text-align: center;' class='kcm'>" + (list.get(i)[1] == null ? "" : list.get(i)[1]) + "</td>");
							infoHt.append("        <td style='text-align: center;'>" + (list.get(i)[2] == null ? "" : list.get(i)[2]) + "</td>");
							infoHt.append("        <td style='text-align: center;'>" + (list.get(i)[3] == null ? "" : list.get(i)[3]) + "</td>");
							infoHt.append("        <td style='text-align: center;'>" + (list.get(i)[4] == null ? "" : list.get(i)[4]) + "</td>");
							if ("1".equals(kcrdfs)) {
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[5] == null ? "" : list.get(i)[5]) + "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[6] == null ? "" : list.get(i)[6]) + "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[7] == null ? "" : list.get(i)[7]) + "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[8] == null ? "" : list.get(i)[8]) + "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[9] == null ? "" : list.get(i)[9]) + "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[10] == null ? "" : list.get(i)[10]) + "</td>");
							} else {

								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[16] == null ? "" : list.get(i)[16]) + "</td>");
							}
							if ("ST".equals(cjrdfs)) {
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[12] == null ? "" : list.get(i)[12]) + "</td>");
							}
							infoHt.append("     </tr>");
						}
					}
					infoHt.append("         </tbody>");
					infoHt.append("     </table>");
					if(mobile){
						infoHt.append("</div></div></div>");
						infoHt.append(" </div>");
					}else{
						infoHt.append(" </div>");
					}
				} else {
					if(mobile){
						infoHt.append("<div class=\"widget-box\">");
						infoHt.append("    <div class='widget-header widget-header-flat'>");
						infoHt.append("        <h4 class=\"widget-title lighter\"><i class=\"glyphicon glyphicon-list\"></i> 校外课程</h4>");
						infoHt.append("        <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
						infoHt.append("    </div>");
						infoHt.append("    <div class='widget-body'><div class='widget-main' style='padding: 0px;'>");
						infoHt.append("        <div class='dialogs ace-scroll scroll-active' style='overflow: auto;'>");
					}else{
						infoHt.append("<h4 class='header smaller lighter grey'><i class='glyphicon glyphicon-list'></i> 校外课程</h4>");
						infoHt.append("<div class='widget-content widget-box' style='max-height: calc(100vh - 520px);overflow: auto;'>");
					}
					infoHt.append("    <table class='table table-striped table-bordered'>" +
							"              <thead>" +
							"                  <tr class='center' style='background-color: #f9f9f9;'>" +
							"                      <th style='width: 50px;white-space: nowrap;text-align: center;'>序号</th>");
					if("1".equals(xsxqf)){
						infoHt.append(" 		   <th style='white-space: nowrap;text-align: center;'>学年学期</th>");
					}
					if(mobile){
						infoHt.append("            <th style='white-space: nowrap;text-align: center;'>课程</th>");
					}else{
						infoHt.append("            <th style='white-space: nowrap;text-align: center;'>课程名</th>" +
								"                  <th style='white-space: nowrap;text-align: center;'>英文课程名</th>");
					}
					infoHt.append("                <th style='white-space: nowrap;text-align: center;'>学分</th>" +
							"                      <th style='white-space: nowrap;text-align: center;'>成绩</th>" +
							"                   </tr>" +
							"              </thead> " +
							"              <tbody>");
					String sql = "select * from XFRZ_XSKCCJ where cjdid='" + cjdid + "' ";
					List<XfrzXskccj> xwkcList = applyCommonDao.findEntitiesBySQL(sql, XfrzXskccj.class);
					if (xwkcList != null && xwkcList.size() > 0) {
						for (int i = 0; i < xwkcList.size(); i++) {
							infoHt.append("    <tr>");
							infoHt.append("        <td style='text-align: center;'>" + (i + 1) + "</td>");
							if("1".equals(xsxqf)){
								infoHt.append("    <td style='text-align: center;'>" + (StringUtils.isNotBlank(xwkcList.get(i).getZxjxjhh()) ? CommonUtils.getZxjxjhmByzxjxjhh(xwkcList.get(i).getZxjxjhh()) : "") + "</td>");
							}
							if(mobile){
								infoHt.append("        <td style='text-align: center;'>" + (StringUtils.isNotBlank(xwkcList.get(i).getKcm()) ? xwkcList.get(i).getKcm() : "")
										+ "<br>" +(StringUtils.isNotBlank(xwkcList.get(i).getYwkcm()) ? xwkcList.get(i).getYwkcm() : "") + "</td>");
							}else{
								infoHt.append("        <td style='text-align: center;'>" + (StringUtils.isNotBlank(xwkcList.get(i).getKcm()) ? xwkcList.get(i).getKcm() : "") + "</td>");
								infoHt.append("        <td style='text-align: center;'>" + (StringUtils.isNotBlank(xwkcList.get(i).getYwkcm()) ? xwkcList.get(i).getYwkcm() : "") + "</td>");
							}
							infoHt.append("        <td style='text-align: center;'>" + (xwkcList.get(i).getXf() != null ? xwkcList.get(i).getXf() : "") + "</td>");
							infoHt.append("        <td style='text-align: center;'>" + (xwkcList.get(i).getCj() != null ? xwkcList.get(i).getCj() : "") + "</td>");
							infoHt.append("    </tr>");
						}
					}
					infoHt.append("        </tbody>" +
							"          </table>");
					if(mobile){
						infoHt.append("</div></div></div>");
						infoHt.append(" </div>");
					}else{
						infoHt.append(" </div>");
					}
					if(mobile){
						infoHt.append("<div class=\"widget-box\">");
						infoHt.append("    <div class='widget-header widget-header-flat'>");
						infoHt.append("        <h4 class=\"widget-title lighter\"><i class=\"glyphicon glyphicon-list\"></i> 校内课程</h4>");
						infoHt.append("        <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
						infoHt.append("    </div>");
						infoHt.append("    <div class='widget-body'><div class='widget-main' style='padding: 0px;'>");
						infoHt.append("        <div class='dialogs ace-scroll scroll-active' style='overflow: auto;'>");
					}else{
						infoHt.append("<h4 class='header smaller lighter grey'><i class='glyphicon glyphicon-list'></i> 校内课程</h4>");
						infoHt.append("<div class='widget-content widget-box' style='max-height: calc(100vh - 520px);overflow: auto;'>");
					}
					infoHt.append("<table class='table table-striped table-bordered'>" +
							"              <thead>" +
							"                  <tr class='center' style='background-color: #f9f9f9;'>" +
							"                      <th style='width: 50px;white-space: nowrap;text-align: center;'>序号</th>" +
							"                      <th style='white-space: nowrap;text-align: center;'>学年学期</th>");
					if(mobile){
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>课程</th>");
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>属性</th>");
					}else{
						infoHt.append("            <th style='white-space: nowrap;text-align: center;'>课程号</th>" +
								"                  <th style='white-space: nowrap;text-align: center;'>课程名</th>" +
								"                  <th style='white-space: nowrap;text-align: center;'>课程属性</th>" +
								"                  <th style='white-space: nowrap;text-align: center;'>学分</th>" +
								"                  <th style='white-space: nowrap;text-align: center;'>学时</th>");
					}
					if ("ST".equals(cjrdfs)) {
						infoHt.append("  	       <th style='white-space: nowrap;text-align: center;'>对应校内成绩</th>");
					}
					infoHt.append("            </tr>" +
							"              </thead> " +
							"              <tbody id='kccj_tbody'>");
					sql = "select pn.xnxqmc(a.zxjxjhh),a.kch,pn.kcm(a.kch),pn.xs_kcsx('" + xssqb.getXh() + "',a.kch),pkg_com.f_NNF(pn.kc_xf(a.kch)),pkg_com.f_NNF(pn.kc_xs(a.kch))," +
							"nvl(a.xghkccj,a.kccj),pkg_cj.f_GradeName(nvl(a.xghkccj,a.kccj)),a.id from XFRZ_RDXSXNKCCJ a where a.sqbh='" + applyId + "'";
					List<Object[]> list = applyCommonDao.findEntitiesBySQL(sql);
					if (list != null && list.size() > 0) {
						for (int i = 0; i < list.size(); i++) {
							infoHt.append("    <tr>");
							infoHt.append("        <td style='text-align: center;'>" + (i + 1) + "</td>");
							infoHt.append("        <td style='text-align: center;'>" + (list.get(i)[0] == null ? "" : list.get(i)[0]) + "</td>");
							if(mobile){
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[1] == null ? "" : list.get(i)[1])
										+ "<br><span class='kcm'>" + (list.get(i)[2] == null ? "" : list.get(i)[2]) + "</span>"+ "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[3] == null ? "" : list.get(i)[3])
										+ "<br>" + (list.get(i)[4] == null ? "" : (list.get(i)[4]) + "学分")
										+ "<br>" + (list.get(i)[5] == null ? "" : (list.get(i)[5]) + "学时") + "</td>");
							}else{
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[1] == null ? "" : list.get(i)[1]) + "</td>");
								infoHt.append("    <td style='text-align: center;' class='kcm'>" + (list.get(i)[2] == null ? "" : list.get(i)[2]) + "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[3] == null ? "" : list.get(i)[3]) + "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[4] == null ? "" : list.get(i)[4]) + "</td>");
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[5] == null ? "" : list.get(i)[5]) + "</td>");
							}
							if ("ST".equals(cjrdfs)) {
								infoHt.append("    <td style='text-align: center;'>" + (list.get(i)[7] == null ? "" : list.get(i)[7]) + "</td>");
							}
							infoHt.append("</tr>");
						}
					}
					infoHt.append("    </tbody>" +
							"      </table>");
					if(mobile){
						infoHt.append("</div></div></div>");
						infoHt.append(" </div>");
					}else{
						infoHt.append(" </div>");
					}
				}

				infoHt.append("</div>");
			}
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return infoHt.toString();
	}

	@Override
	public String queryClassIdentificationInfoHtml(String applyId) {
		String sql = "select nvl(pn.xm(a.sqrzjh),pn.jsm(a.sqrzjh)),to_char(to_date(a.sqrq, 'yyyyMMdd'), 'yyyy-MM-dd'),pn.xnxqmc(a.xnxq),pn.kzlb(kzlbdm),b.statusname,a.sqyy " +
				"from CJ_KZRD_SQB a,ea_code_applystatus b where a.sqzt=b.statuscode(+) and a.sqbh='" + applyId + "'";
		Object[] obj = applyCommonDao.findEntityBySQL(sql);
		sql = "select a.kch,c.kcm,pkg_com.f_NNF(c.xf) xf,pkg_com.f_NNF(c.xs) xs,b.kzm from CJ_KZRD_KCRDKZLBB a,jh_mkkzxxb_all b,code_kcb c where a.kzh=b.kzh and a.kch=c.kch and a.sqbh='" + applyId + "'";
		List<Object[]> kcinfos = applyCommonDao.findEntitiesBySQL(sql);

		StringBuffer infoHt = new StringBuffer();
		infoHt.append("<div class=\" col-xs-12\" style=\"padding-left: 0px;padding-right: 0px;\"> " +
				"    <h4 class=\"header smaller lighter grey\"><i class=\"glyphicon glyphicon-list\"></i> " +
				" 申请信息 </h4> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\" id=\"customs\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请人</div> " +
				"            <div class=\"profile-info-value\">" + obj[0] + "</div> " +
				"            <div class=\"profile-info-name\">申请时间</div> " +
				"            <div class=\"profile-info-value\">" + obj[1] + "</div> " +
				"            <div class=\"profile-info-name\">申请学期</div> " +
				"            <div class=\"profile-info-value\">" + obj[2] + "</div> " +
				"        </div>" +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">课组类别</div> " +
				"            <div class=\"profile-info-value\">" + obj[3] + "</div> " +
				"            <div class=\"profile-info-name\">申请状态</div> " +
				"            <div class=\"profile-info-value\">" + obj[4] + "</div> " +
				"        </div>" +
				"    </div> " +
				"    <div class=\"profile-user-info profile-user-info-striped self\"> " +
				"        <div class=\"profile-info-row\"> " +
				"            <div class=\"profile-info-name\">申请原因</div> " +
				"            <div class=\"profile-info-value\">" + obj[5] + "</div> " +
				"        </div> " +
				"    </div> " +
				"</div>");

		infoHt.append("<h4 class=\"header smaller lighter grey\"> " +
				"    <i class=\"glyphicon glyphicon-list\"></i> " +
				"    课程列表 " +
				"</h4> " +
				"<table class=\"table table-hover table-bordered table-striped table_td\" id=\"tkkctable\"> " +
				"    <thead> " +
				"    <tr> " +
				"        <th>序号</th> " +
				"        <th>课程号</th> " +
				"        <th>课程名</th> " +
				"        <th>学分</th> " +
				"        <th>学时</th> " +
				"        <th>所属课组</th> " +
				"    </tr> " +
				"    </thead> " +
				"    <tbody >");


		for (int i = 0, ten = kcinfos.size(); i < ten; i++) {
			Object[] info = kcinfos.get(i);
			infoHt.append("<tr><td>" + (i + 1) + "</td><td>" + info[0] + "</td><td>" + info[1] + "</td><td>" + info[2] + "</td><td>" + info[3] + "</td><td>" + info[4] + "</td></tr>");
		}
		infoHt.append("</tbody></table>");

		return infoHt.toString();
	}

	/*
	 * 申请指定审批人查询
	 */
	@SuppressWarnings("deprecation")
	@Override
	public Map<String, Object> queryApprovers(String sql, String apply_type,
			Object[] names, String sqbh) {
		Map<String, Object> map = new HashMap<String, Object>();
		Session session = (Session) em.getDelegate();
		SessionFactoryImplementor sf = (SessionFactoryImplementor) session.getSessionFactory();
		Connection conn = null;
		CallableStatement cs = null;
		PreparedStatement ps = null;
		String czr = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
		String zxjxjhh = CommonUtils.queryCurrentXnxq();
		String czip = CommonUtils.getRemoteHostOnService();
		String czsj = CommonUtils.queryCurrentTimeMinBySql();
		try {
			conn = sf.getConnectionProvider().getConnection();
			conn.setAutoCommit(false);//把自动提交方式变为人工
			if (StringUtils.isNotBlank(sql)) {
				ps = conn.prepareStatement(sql);
				ps.executeUpdate();
				ps.close();
			}
			sql = "insert into ea_applys (apply_id, apply_type, user_code, commit_dt, rollback_dt, apply_status, ea_rslt, note, " +
					"zxjxjhh)values ('" + sqbh + "', '" + apply_type + "', '" + czr + "', '" + czsj + "', '', 1, null, null, '" + zxjxjhh + "')";
			ps = conn.prepareStatement(sql);
			ps.executeUpdate();
			ps.close();
			sql = "insert into ea_result (apply_id, eap_code, eal_code, eal_order, over_enabled, eal_rslt) values ('" + sqbh + "'," +
					"'" + names[2] + "','" + names[3] + "','" + names[4] + "','0','0')";
			ps = conn.prepareStatement(sql);
			ps.executeUpdate();
			ps.close();
			sql = "SELECT i.*, pkg_others.f_approvable('" + sqbh + "', '" + names[3] + "', i.jsh) AS passable" +
					"  FROM (SELECT c.jsh, c.jsm, pn.xsm(c.org_id) AS xsm" +
					"       FROM urp_user_role a, urp_user b, code_jsb c" +
					"      WHERE c.jsh = b.idnumber" +
					"        AND a.user_id = b.id" +
					"        AND a.role_id = '" + names[1] + "'" +
					"        AND b.enabled = '1'" +
					"        AND c.zzztdm = '01') i";
			ps = conn.prepareStatement(sql);
			ResultSet rs = ps.executeQuery();
			List<Object[]> approvers = convertList(rs);
			rs.close();
			ps.close();
			map.put("approvers", approvers);
			conn.rollback();//出现异常进行回滚；
		} catch (Exception e) {
			try {
				conn.rollback();//出现异常进行回滚；
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		} finally {
			try {
				conn.setAutoCommit(true);
			} catch (SQLException e) {
				e.printStackTrace();
			}
			CommonUtils.closeDB(conn, cs, ps);

		}
		return map;
	}

	@Override
	public String queryProcessApplyWhiteList(String tabName, String xh) {
		return applyCommonDao.queryProcessApplyWhiteList(tabName, xh);
	}

	@Override
	public String queryTerminationInnovationInfoHtml(String applyId) {
		ChxTmzzsqb sqb = this.applyCommonDao.findById(ChxTmzzsqb.class, applyId);
		ChxXmsqb xmsqb = null;
		XsXjb xsXjb = null;
		if (sqb != null) {
			if (StringUtils.isNotEmpty(sqb.getXmsqid())) {
				xmsqb = this.applyCommonDao.findById(ChxXmsqb.class, sqb.getXmsqid());
			}
			if (StringUtils.isNotEmpty(sqb.getXh())) {
				xsXjb = this.applyCommonDao.findById(XsXjb.class, sqb.getXh());
			}
		} else {
			sqb = new ChxTmzzsqb();
		}

		String xmlx = "";
		String xmly = "";
		if (xmsqb == null) {
			xmsqb = new ChxXmsqb();
		} else {
			if (StringUtils.isNotEmpty(xmsqb.getXmlxm())) {
				xmlx = xmsqb.getXmlxm();
				ChxCodeChxxmlxb xmlxb = this.applyCommonDao.findById(ChxCodeChxxmlxb.class, xmlx);
				if (xmlxb != null) {
					xmlx = xmlxb.getXmlx();
				}
			}
			if (StringUtils.isNotEmpty(xmsqb.getXmlym())) {
				xmly = xmsqb.getXmlym();
				ChxCodeXmlyb xmlyb = this.applyCommonDao.findById(ChxCodeXmlyb.class, xmly);
				if (xmlyb != null) {
					xmly = xmlyb.getXmly();
				}
			}
		}
		JSONObject jsonObject = JSONObject.fromObject(sqb);
		StringBuffer otherHt = new StringBuffer();
		otherHt.append("    <h4 class=\"header smaller lighter grey\"> " +
				"         <i class=\"glyphicon glyphicon-list\"></i> " +
				"         终止项目基础信息 " +
				"        </h4> " +
				"        <div class=\" profile-user-info profile-user-info-striped self\"> " +
				"         <div class=\"profile-info-row\"> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">项目名称</div> " +
				"          <div class=\"profile-info-value\"> " + xmsqb.getXmmc() +
				"          </div> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">申请年份</div> " +
				"          <div class=\"profile-info-value\"> " + xmsqb.getSqnf() +
				"          </div> " +
				"         </div> " +
				"         <div class=\"profile-info-row\"> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">项目类型</div> " +
				"          <div class=\"profile-info-value\"> " + xmlx +
				"          </div> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">项目来源</div> " +
				"          <div class=\"profile-info-value\"> " + xmly +
				"          </div> " +
				"         </div> " +
				"         <div class=\"profile-info-row\"> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">联系电话</div> " +
				"          <div class=\"profile-info-value\"> " + (StringUtils.isNotEmpty(xmsqb.getLxdh()) ? xmsqb.getLxdh() : "") +
				"          </div> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">指导教师</div> " +
				"          <div class=\"profile-info-value\"> " + (StringUtils.isNotEmpty(xmsqb.getZdjs()) ? xmsqb.getZdjs() : "") +
				"          </div> " +
				"         </div> " +
				"        </div>");
		otherHt.append("<h4 class=\"header smaller lighter grey\"> " +
				"     <i class=\"glyphicon glyphicon-list\"></i> " +
				"     申请信息 " +
				"  </h4>");
		otherHt.append("<div class=\" profile-user-info profile-user-info-striped self\"> ");
		otherHt.append("<div class=\"profile-info-row\"> " +
				"   <div class=\"profile-info-name\"> 终止原因</div> " +
				"   <div class=\"profile-info-value\"> " + (StringUtils.isNotEmpty(sqb.getZzyy()) ? sqb.getZzyy() : "") +
				"   </div> " +
				" </div>");
		List<SysColConfig> sysColConfigs = this.querySysColConfigByTabName("chx_tmzzsqb");
		if (sysColConfigs != null && sysColConfigs.size() > 0) {
			for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
				SysColConfig sysColConfig = sysColConfigs.get(i);
				otherHt.append("<div class=\"profile-info-row\">");
				otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
				otherHt.append("</div>");

			}
		}
		otherHt.append("</div>");

		String approvalHtml = "<h4 class=\"header smaller lighter grey\"> " +
				"    <i class=\"fa fa-gavel\"></i> " +
				"    审批过程 " +
				"</h4> " +
				"<div class=\"timeline-container\"> " +
				"    <div class=\"timeline-items\"> " +
				"        <div class=\"timeline-item clearfix\"> " +
				"            <div class=\"timeline-info\" style=\"z-index: 1;\"> " +
				"                <i class=\"timeline-indicator ace-icon fa fa-star btn btn-success no-hover\"></i> " +
				"            </div> " +
				" " +
				"            <div class=\"widget-box transparent\"> " +
				"                <div class=\"widget-body\"> " +
				"                    <div class=\"widget-main\"> " +
				"                            " + xsXjb.getXm() + "&nbsp; &nbsp; 发起申请 " +
				"                        <div class=\"pull-right\"> " +
				"                            <i class=\"ace-icon fa fa-clock-o bigger-110\"></i> " + (getTimeFormat(sqb.getSqsj())) +
				"                        </div> " +
				"                    </div> " +
				"                </div> " +
				"            </div> " +
				"        </div> " +
				"    </div> " +
				"    <div class=\"timeline-items\"> " +
				"        <div class=\"timeline-item clearfix\"> " +
				"            <div class=\"timeline-info\" style=\"z-index: 1;\"> " +
				"                <i class=\"timeline-indicator ace-icon fa fa-leaf btn btn-primary no-hover green\"></i> " +
				"                <span class=\"label label-yellow label-sm\">教师审批</span> " +
				"            </div> " +
				"            <div class=\"widget-box transparent\"> " +
				"                <div class=\"widget-body\"> " +
				"                    <div class=\"widget-main\" style=\"height: 43px;\"> " +
				(this.getJsmByJsh(sqb.getJsspr())) + "&nbsp;&nbsp;" +
				"                            <span class=\"green bolder\">" + sqb.getJsspjl() + "</span> " +
				(StringUtils.isNotEmpty(sqb.getJsspyj()) ? "(" + sqb.getJsspyj() + ")" : "") +
				"                            <div class=\"pull-right\"> " +
				"                                <i class=\"ace-icon fa fa-clock-o bigger-110\"></i> " + this.getTimeFormat(sqb.getJsspsj()) +
				"                            </div> " +
				"                    </div> " +
				"                </div> " +
				"            </div> " +
				"        </div> " +
				"    </div> " +
				"    <div class=\"timeline-items\"> " +
				"        <div class=\"timeline-item clearfix\"> " +
				"            <div class=\"timeline-info\" style=\"z-index: 1;\"> " +
				"                <i class=\"timeline-indicator ace-icon fa fa-leaf btn btn-primary no-hover green\"></i> " +
				"                <span class=\"label label-yellow label-sm\">院系审批</span> " +
				"            </div> " +
				"            <div class=\"widget-box transparent\"> " +
				"                <div class=\"widget-body\"> " +
				"                    <div class=\"widget-main\" style=\"height: 43px;\"> " +
				(this.getJsmByJsh(sqb.getYxspr())) + "&nbsp;&nbsp; " +
				"                            <span class=\"green bolder\">" + sqb.getYxspjl() + "</span> " +
				(StringUtils.isNotEmpty(sqb.getYxspyj()) ? "(" + sqb.getYxspyj() + ")" : "") +
				"                            <div class=\"pull-right\"> " +
				"                                <i class=\"ace-icon fa fa-clock-o bigger-110\"></i> " + sqb.getYxspsj() +
				"                            </div> " +
				"                    </div> " +
				"                </div> " +
				"            </div> " +
				"        </div> " +
				"    </div> " +
				"    <div class=\"timeline-items\"> " +
				"        <div class=\"timeline-item clearfix\"> " +
				"            <div class=\"timeline-info\" style=\"z-index: 1;\"> " +
				"                <i class=\"timeline-indicator ace-icon fa fa-leaf btn btn-primary no-hover green\"></i> " +
				"                <span class=\"label label-yellow label-sm\">教务处审批</span> " +
				"            </div> " +
				"            <div class=\"widget-box transparent\"> " +
				"                <div class=\"widget-body\"> " +
				"                    <div class=\"widget-main\" style=\"height: 43px;\"> " +
				(this.getJsmByJsh(sqb.getJwcspr())) + "&nbsp;&nbsp; " +
				"                            <span class=\"green bolder\">" + (sqb.getJwcspjl()) + "</span> " +
				(StringUtils.isNotEmpty(sqb.getJwcspyj()) ? "(" + sqb.getJwcspyj() + ")" : "") +
				"                            <div class=\"pull-right\"> " +
				"                                <i class=\"ace-icon fa fa-clock-o bigger-110\"></i> " + this.getTimeFormat(sqb.getJwcspsj()) +
				"                            </div> " +
				"                    </div> " +
				"                </div> " +
				"            </div> " +
				"        </div> " +
				"    </div> " +
				"</div>";
		return otherHt.append(approvalHtml).toString();
	}


	@Override
	public String queryExtensionInnovationInfoHtml(String applyId) {
		ChxTmyqsqb sqb = this.applyCommonDao.findById(ChxTmyqsqb.class, applyId);
		ChxXmsqb xmsqb = null;
		XsXjb xsXjb = null;
		if (sqb != null) {
			if (StringUtils.isNotEmpty(sqb.getXmsqid())) {
				xmsqb = this.applyCommonDao.findById(ChxXmsqb.class, sqb.getXmsqid());
			}
			if (StringUtils.isNotEmpty(sqb.getXh())) {
				xsXjb = this.applyCommonDao.findById(XsXjb.class, sqb.getXh());
			}
		} else {
			sqb = new ChxTmyqsqb();
		}

		String xmlx = "";
		String xmly = "";
		if (xmsqb == null) {
			xmsqb = new ChxXmsqb();
		} else {
			if (StringUtils.isNotEmpty(xmsqb.getXmlxm())) {
				xmlx = xmsqb.getXmlxm();
				ChxCodeChxxmlxb xmlxb = this.applyCommonDao.findById(ChxCodeChxxmlxb.class, xmlx);
				if (xmlxb != null) {
					xmlx = xmlxb.getXmlx();
				}
			}
			if (StringUtils.isNotEmpty(xmsqb.getXmlym())) {
				xmly = xmsqb.getXmlym();
				ChxCodeXmlyb xmlyb = this.applyCommonDao.findById(ChxCodeXmlyb.class, xmly);
				if (xmlyb != null) {
					xmly = xmlyb.getXmly();
				}
			}
		}
		JSONObject jsonObject = JSONObject.fromObject(sqb);
		StringBuffer otherHt = new StringBuffer();
		otherHt.append("    <h4 class=\"header smaller lighter grey\"> " +
				"         <i class=\"glyphicon glyphicon-list\"></i> " +
				"         延期申请基础信息 " +
				"        </h4> " +
				"        <div class=\" profile-user-info profile-user-info-striped self\"> " +
				"         <div class=\"profile-info-row\"> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">项目名称</div> " +
				"          <div class=\"profile-info-value\"> " + xmsqb.getXmmc() +
				"          </div> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">申请年份</div> " +
				"          <div class=\"profile-info-value\"> " + xmsqb.getSqnf() +
				"          </div> " +
				"         </div> " +
				"         <div class=\"profile-info-row\"> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">项目类型</div> " +
				"          <div class=\"profile-info-value\"> " + xmlx +
				"          </div> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">项目来源</div> " +
				"          <div class=\"profile-info-value\"> " + xmly +
				"          </div> " +
				"         </div> " +
				"         <div class=\"profile-info-row\"> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">联系电话</div> " +
				"          <div class=\"profile-info-value\"> " + (StringUtils.isNotEmpty(xmsqb.getLxdh()) ? xmsqb.getLxdh() : "") +
				"          </div> " +
				"          <div class=\"profile-info-name\" style=\"width: 150px;\">指导教师</div> " +
				"          <div class=\"profile-info-value\"> " + (StringUtils.isNotEmpty(xmsqb.getZdjs()) ? xmsqb.getZdjs() : "") +
				"          </div> " +
				"         </div> " +
				"        </div>");
		otherHt.append("<h4 class=\"header smaller lighter grey\"> " +
				"     <i class=\"glyphicon glyphicon-list\"></i> " +
				"     申请信息 " +
				"  </h4>");
		otherHt.append("<div class=\" profile-user-info profile-user-info-striped self\"> ");
		otherHt.append("<div class=\"profile-info-row\"> " +
				"   <div class=\"profile-info-name\"> 延期时间</div> " +
				"   <div class=\"profile-info-value\"> " + (StringUtils.isNotEmpty(sqb.getJzsj()) ? sqb.getJzsj() : "") +
				"   </div> " +
				" </div>");
		otherHt.append("<div class=\"profile-info-row\"> " +
				"   <div class=\"profile-info-name\"> 延期原因</div> " +
				"   <div class=\"profile-info-value\"> " + (StringUtils.isNotEmpty(sqb.getZzyy()) ? sqb.getZzyy() : "") +
				"   </div> " +
				" </div>");
		List<SysColConfig> sysColConfigs = this.querySysColConfigByTabName("CHX_TMYQSQB");
		if (sysColConfigs != null && sysColConfigs.size() > 0) {
			for (int i = 0, ten = sysColConfigs.size(); i < ten; i++) {
				SysColConfig sysColConfig = sysColConfigs.get(i);
				otherHt.append("<div class=\"profile-info-row\">");
				otherHt.append("<div class=\"profile-info-name\">" + sysColConfig.getColname() + "</div><div class=\"profile-info-value\">" + jsonObject.get(sysColConfig.getColid().toLowerCase()) + "</div>");
				otherHt.append("</div>");
			}
		}
		otherHt.append("</div>");

		String approvalHtml = "<h4 class=\"header smaller lighter grey\"> " +
				"    <i class=\"fa fa-gavel\"></i> " +
				"    审批过程 " +
				"</h4> " +
				"<div class=\"timeline-container\"> " +
				"    <div class=\"timeline-items\"> " +
				"        <div class=\"timeline-item clearfix\"> " +
				"            <div class=\"timeline-info\" style=\"z-index: 1;\"> " +
				"                <i class=\"timeline-indicator ace-icon fa fa-star btn btn-success no-hover\"></i> " +
				"            </div> " +
				" " +
				"            <div class=\"widget-box transparent\"> " +
				"                <div class=\"widget-body\"> " +
				"                    <div class=\"widget-main\"> " +
				"                            " + xsXjb.getXm() + "&nbsp; &nbsp; 发起申请 " +
				"                        <div class=\"pull-right\"> " +
				"                            <i class=\"ace-icon fa fa-clock-o bigger-110\"></i> " + (getTimeFormat(sqb.getSqsj())) +
				"                        </div> " +
				"                    </div> " +
				"                </div> " +
				"            </div> " +
				"        </div> " +
				"    </div> " +
				"    <div class=\"timeline-items\"> " +
				"        <div class=\"timeline-item clearfix\"> " +
				"            <div class=\"timeline-info\" style=\"z-index: 1;\"> " +
				"                <i class=\"timeline-indicator ace-icon fa fa-leaf btn btn-primary no-hover green\"></i> " +
				"                <span class=\"label label-yellow label-sm\">教师审批</span> " +
				"            </div> " +
				"            <div class=\"widget-box transparent\"> " +
				"                <div class=\"widget-body\"> " +
				"                    <div class=\"widget-main\" style=\"height: 43px;\"> " +
				(this.getJsmByJsh(sqb.getJsspr())) + "&nbsp;&nbsp;" +
				"                            <span class=\"green bolder\">" + sqb.getJsspjl() + "</span> " +
				(StringUtils.isNotEmpty(sqb.getJsspyj()) ? "(" + sqb.getJsspyj() + ")" : "") +
				"                            <div class=\"pull-right\"> " +
				"                                <i class=\"ace-icon fa fa-clock-o bigger-110\"></i> " + this.getTimeFormat(sqb.getJsspsj()) +
				"                            </div> " +
				"                    </div> " +
				"                </div> " +
				"            </div> " +
				"        </div> " +
				"    </div> " +
				"    <div class=\"timeline-items\"> " +
				"        <div class=\"timeline-item clearfix\"> " +
				"            <div class=\"timeline-info\" style=\"z-index: 1;\"> " +
				"                <i class=\"timeline-indicator ace-icon fa fa-leaf btn btn-primary no-hover green\"></i> " +
				"                <span class=\"label label-yellow label-sm\">院系审批</span> " +
				"            </div> " +
				"            <div class=\"widget-box transparent\"> " +
				"                <div class=\"widget-body\"> " +
				"                    <div class=\"widget-main\" style=\"height: 43px;\"> " +
				(this.getJsmByJsh(sqb.getYxspr())) + "&nbsp;&nbsp; " +
				"                            <span class=\"green bolder\">" + sqb.getYxspjl() + "</span> " +
				(StringUtils.isNotEmpty(sqb.getYxspyj()) ? "(" + sqb.getYxspyj() + ")" : "") +
				"                            <div class=\"pull-right\"> " +
				"                                <i class=\"ace-icon fa fa-clock-o bigger-110\"></i> " + this.getTimeFormat(sqb.getYxspsj()) +
				"                            </div> " +
				"                    </div> " +
				"                </div> " +
				"            </div> " +
				"        </div> " +
				"    </div> " +
				"    <div class=\"timeline-items\"> " +
				"        <div class=\"timeline-item clearfix\"> " +
				"            <div class=\"timeline-info\" style=\"z-index: 1;\"> " +
				"                <i class=\"timeline-indicator ace-icon fa fa-leaf btn btn-primary no-hover green\"></i> " +
				"                <span class=\"label label-yellow label-sm\">教务处审批</span> " +
				"            </div> " +
				"            <div class=\"widget-box transparent\"> " +
				"                <div class=\"widget-body\"> " +
				"                    <div class=\"widget-main\" style=\"height: 43px;\"> " +
				(this.getJsmByJsh(sqb.getJwcspr())) + "&nbsp;&nbsp; " +
				"                            <span class=\"green bolder\">" + (sqb.getJwcspjl()) + "</span> " +
				(StringUtils.isNotEmpty(sqb.getJwcspyj()) ? "(" + sqb.getJwcspyj() + ")" : "") +
				"                            <div class=\"pull-right\"> " +
				"                                <i class=\"ace-icon fa fa-clock-o bigger-110\"></i> " + this.getTimeFormat(sqb.getJwcspsj()) +
				"                            </div> " +
				"                    </div> " +
				"                </div> " +
				"            </div> " +
				"        </div> " +
				"    </div> " +
				"</div>";
		return otherHt.append(approvalHtml).toString();
	}

	@Override
	public List<XkCxXnxqView> queryXkCxXnxqViewByOne(String xh, String zxjxjhh) {
		List<XkCxXnxqView> list = applyCommonDao.queryXkCxXnxqViewByXh(xh);
		List<XkCxXnxqView> newList = new ArrayList<>();
		for(XkCxXnxqView view : list){
			boolean flag = true;
			if(zxjxjhh.equals(view.getId().getZxjxjhh())){
				for(XkCxXnxqView newWiew : newList){
					if(view.getId().getZxjxjhh().equals(newWiew.getId().getZxjxjhh())){
						flag = false;
						break;
					}
				}
				if(flag){
					newList.add(view);
				}
			}
		}
		return newList;
	}

	@Override
	public String queryStudentsDeclareGraduationHtml(String applyId,boolean mobile) {
		String sql = " SELECT lw.sqbh, " +
				"       lw.pch, " +
				"       lw.tmbh,pc.pcmc, " +
				"       pn.xnxqmc(pc.zxjxjhh) zxjxjhm, " +
				"       lw.tmmc, " +
				"       lw.tmywmc, " +
				"       lw.tmlydm, " +
				"       (SELECT t.tmlysm FROM code_tmlyb t WHERE lw.tmlydm = t.tmlydm) AS tmlysm, " +
				"       lw.lwndm, " +
				"       (SELECT f.lwnd FROM code_lwndb f WHERE f.lwndm = lw.lwndm) lwnd, " +
				"       lw.lwgzxsdm, " +
				"       (SELECT e.lwgzxssm FROM code_lwgzxsb e WHERE e.lwgzxsdm = lw.lwgzxsdm) lwgzxssm, " +
				"       lw.ktlbdm, " +
				"       (SELECT k.ktlbsm FROM code_ktlbb k WHERE lw.ktlbdm = k.ktlbdm) AS ktlbsm, " +
				"       lw.sfzdfcxm, " +
				"       lw.lwyjfx, " +
				"       lw.tmjj, " +
				"       lw.jnfw, " +
				"       lw.bz, " +
				"       c.apply_status, " +
				"       decode(c.apply_status, " +
				"              '-1', " +
				"              '撤销', " +
				"              '0', " +
				"              '待提交', " +
				"              '1', " +
				"              '已提交', " +
				"              '2', " +
				"              '审批中', " +
				"              '3', " +
				"              '审批结束') ea_status_name, " +
				"       c.ea_rslt, " +
				"       decode(c.ea_rslt, 1, '已批准', 0, '未批准', '') ea_rsltname, " +
				"       decode(lw.zsjzwc,1,'是','否') zsjzwcsm, " +
				"       lw.zsjzwc, " +
				"       decode(lw.sfxqhz,1,'是','否') sfxqhzsm, " +
				"       sfxqhz, " +
				"       lw.qydsxm, " +
				"       (select zcb.zcsm from code_zcb zcb where zcb.zcdm = lw.qydszcdm) qydszcmc, " +
				"       lw.qydszcdm, " +
				"       lw.hzqymc, " +
				"       lw.dezdjs, " +
				"       pn.jsm(lw.dezdjs) dezdjsm, " +
				"       nvl(lw.tmmc_xs,lw.tmmc) as tmmc_xs, " +
				"       nvl(lw.tmywmc_xs,lw.tmywmc) as tmywmc_xs, " +
				"       lw.tmsxbwz, " +
				"       lw.tmsxbwz_yw, " +
				"       lw.xtlydm, " +
				"       (select l.xtlysm from code_xtlyb l where l.xtlydm = lw.xtlydm) as xtlysm, " +
				"       lw.ktlxm, " +
				"       (select x.ktlx from code_lwtmlxb x where x.ktlxm = lw.ktlxm) as ktlx, " +
				"       lw.lwsycsdm, " +
				"       (select c.lwsycssm from code_lwsycsb c where c.lwsycsdm = lw.lwsycsdm) as lwsycssm, " +
				"       lw.mdjyy, " +
				"       lw.zyyjnr, " +
				"       lw.fajhh, " +
				"       lw.xh " +
				"  FROM lw_tmxx_xssqb lw, ea_applys c,lw_pcb pc " +
				" WHERE lw.sqbh = c.apply_id  AND lw.pch = pc.pch " +
				"   AND lw.sqbh = '" + applyId + "' ";
		List<Object[]> sqbInfos = applyCommonDao.findEntitiesBySQL(sql);
		if (sqbInfos == null && sqbInfos.size() < 1) {
			return "";
		}
		JSONUtils jsonUtils = new JSONUtils();
		JSONArray jsonArray = jsonUtils.fromListToJson(Arrays.asList("sqbh", "zxjxjhh", "tmbh", "pcmc", "zxjxjhm", "tmmc", "tmywmc", "tmlydm", "tmlysm", "lwndm", "lwnd", "lwgzxsdm", "lwgzxssm",
				"ktlbdm", "ktlbsm", "sfzdfcxm", "lwyjfx", "tmjj", "jnfw", "bz", "apply_status", "ea_status_name", "ea_rslt", "ea_rsltname", "zsjzwcsm", "zsjzwc",
				"sfxqhzsm", "sfxqhz", "qydsxm", "qydszcmc", "qydszcdm", "hzqymc", "dezdjs", "dezdjsm", "tmmc_xs", "tmywmc_xs", "tmsxbwz", "tmsxbwz_yw", "xtlydm", "xtlysm", "ktlxm", "ktlx",
				"lwsycsdm", "lwsycssm", "mdjyy", "zyyjnr", "fajhh", "xh"), sqbInfos);
		JSONObject sqbInfo = (JSONObject) jsonArray.get(0);
		sql = "select p.nj," +
				"       p.xsh," +
				"       pn.xsm(p.xsh) as xsm," +
				"       p.zyh," +
				"       pn.zym(p.xsh, p.zyh) as zym," +
				"       p.zyfxh," +
				"       pn.zyfxm(p.xsh, p.zyh, p.zyfxh) as zyfxm," +
				"       p.fajhh," +
				"       p.famc," +
				"       g.xh," +
				"       x.xm," +
				"       pn.bjm(x.bjh) as bjm" +
				"  from lw_kxtbmd g, jh_fajhb p, xs_xjb x" +
				" where g.fajhh = p.fajhh" +
				"   and g.xh = x.xh " +
				"   and x.xh = '" + sqbInfo.get("xh") + "' " +
				"   and p.fajhh = '" + sqbInfo.get("fajhh") + "' ";
		List<com.alibaba.fastjson.JSONObject> xsList = CommonUtils.queryJsonDataBySql(sql);
		sql = " SELECT columncode FROM urp_table_btxdyb where tablecode='LW_TMXXB' and bdzkj='1' ";
		List<String> btxs = applyCommonDao.findEntitiesBySQL(sql);
		Map<String, String> btxmap = new HashMap<>();
		for (int i = 0, teni = btxs.size(); i < teni; i++) {
			btxmap.put(btxs.get(i).toLowerCase(Locale.ROOT), "1");
		}


		StringBuffer infoHt = new StringBuffer();
		if (mobile) {
			infoHt.append("<div class='col-sm-12'><div class='widget-box'>");
			infoHt.append("  <div class='widget-header widget-header-flat'>");
			infoHt.append("      <h4 class=\"widget-title lighter\"><i class=\"glyphicon glyphicon-list\"></i> 申请信息</h4>");
			infoHt.append("      <div class=\"widget-toolbar\"><a href=\"#\" data-action=\"collapse\"><i class=\"ace-icon fa fa-chevron-up\"></i></a></div>");
			infoHt.append("  </div>");
			infoHt.append("  <div class='widget-body'><div class='widget-main' style='padding: 0px;'><div class='dialogs ace-scroll scroll-active'>");
			infoHt.append("<div id=\"customs\">");
			infoHt.append("<div class=\"phone-profile-info-row\">" +
					"     <div class=\"col-xs-3 phone-row-title\">论文批次</div>" +
					"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("pcmc") + "</div>" +
					"    </div>");
			infoHt.append("<div class=\"phone-profile-info-row\">" +
					"     <div class=\"col-xs-3 phone-row-title\">题目名称</div>" +
					"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("tmmc") + "</div>" +
					"    </div>");
			if (btxmap.containsKey("tmywmc")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">英文题目名</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("tmywmc") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("tmlydm")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">题目来源</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("tmlysm") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("xtlydm")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">选题来源</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("xtlysm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("nddm")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">题目难度</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("lwnd") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("lwgzxsdm")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">论文工作形式</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("lwgzxssm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("ktlbdm")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">课题类别</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("ktlbsm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("ktlxm")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">课题类型</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("ktlx") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("lwsycsdm")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">重复使用次数</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("lwsycssm") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("kssj")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">开始时间</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("kssj") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("jssj")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">结束时间</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("jssj") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("sfzdfcxm")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">重点扶持项目</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("sfzdfcxm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("tmrl")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">题目容量</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("tmrl") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("lwyjfx")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">论文研究方向</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("lwyjfx") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("dezdjs")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">第二指导教师</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("dezdjsm") + "（" + sqbInfo.get("dezdjs") + "）" + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("zsjzwc")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">在社会实践中完成</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("zsjzwc") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("sfxqhz")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">校企合作</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("sfxqhz") + "</div>" +
						"    </div>");
				if ("是".equals(sqbInfo.get("sfxqhz"))) {
					if (btxmap.containsKey("qydsxm")) {
						infoHt.append("<div class=\"phone-profile-info-row\">" +
								"     <div class=\"col-xs-3 phone-row-title\">企业导师姓名</div>" +
								"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("qydsxm") + "</div>" +
								"    </div>");
					}
					if (btxmap.containsKey("qydszcdm")) {
						infoHt.append("<div class=\"phone-profile-info-row\">" +
								"     <div class=\"col-xs-3 phone-row-title\">企业导师职称</div>" +
								"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("qydszcdm") + "</div>" +
								"    </div>");
					}
					if (btxmap.containsKey("hzqymc")) {
						infoHt.append("<div class=\"phone-profile-info-row\">" +
								"     <div class=\"col-xs-3 phone-row-title\">合作企业名称</div>" +
								"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("hzqymc") + "</div>" +
								"    </div>");
					}
				}
			}
			if (btxmap.containsKey("tmjj")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">题目简介</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("tmjj") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("jnfw")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">接纳范围</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("jnfw") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("bz")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">难点及要点</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("bz") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("mdjyy")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">目的及意义</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("mdjyy") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("zyyhnr")) {
				infoHt.append("<div class=\"phone-profile-info-row\">" +
						"     <div class=\"col-xs-3 phone-row-title\">主要研究内容</div>" +
						"     <div class=\"col-xs-9 phone-row-value\">" + sqbInfo.get("zyyjnr") + "</div>" +
						"    </div>");
			}
			infoHt.append("<div class=\"phone-profile-info-row\">" +
					"     <div class=\"col-xs-3 phone-row-title\"> 选定学生</div>" +
					"     <div class=\"col-xs-9 phone-row-value\">" +
					"      <div class=\"widget-content navbar-example\"" +
					"        style=\"overflow: auto;height-max: 200px;\">" +
					"       <table class=\"table table-striped table-bordered table-hover\">" +
					"        <thead>" +
					"        <tr class=\"center\">" +
					"         <th>方案信息</th>" +
					"         <th>培养方案</th>" +
					"         <th>学生</th>" +
					"         <th>班级</th>" +
					"        </tr>" +
					"        </thead>" +
					"        <tbody id=\"scoreintbody\" style=\"overflow: auto;\">");
			for (int i = 0, teni = xsList.size(); i < teni; i++) {
				com.alibaba.fastjson.JSONObject xss = xsList.get(i);
				infoHt.append("<tr class=\"center\"> <td>" + xss.get("nj") + "<br>" + xss.get("xsm") + "<br>" + xss.get("zym") + "<br>" +
						xss.get("zyfxm") + "</td> <td>" + xss.get("famc") + "</td> <td>" + xss.get("xm") + "（" + xss.get("xh") + "）" + "</td> " +
						"<td>" + xss.get("bjm") + "</td></tr>");
			}
			infoHt.append("        </tbody>" +
					"       </table>" +
					"      </div>" +
					"     </div>" +
					"    </div>");
			infoHt.append("   </div></div></div></div></div>");

		} else {
			infoHt.append("<div class=\"profile-user-info profile-user-info-striped self\">" +
					"    <div class=\"profile-info-row\">" +
					"     <div class=\"profile-info-name\">论文批次</div>" +
					"     <div class=\"profile-info-value\">" + sqbInfo.get("pcmc") + "</div>" +
					"    </div>");
			infoHt.append("<div class=\"profile-info-row\">" +
					"     <div class=\"profile-info-name\">题目名称</div>" +
					"     <div class=\"profile-info-value\">" + sqbInfo.get("tmmc") + "</div>" +
					"    </div>");
			if (btxmap.containsKey("tmywmc")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">英文题目名</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("tmywmc") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("tmlydm")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">题目来源</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("tmlysm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("xtlydm")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">选题来源</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("xtlysm") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("nddm")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">题目难度</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("lwnd") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("lwgzxsdm")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">论文工作形式</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("lwgzxssm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("ktlbdm")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">课题类别</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("ktlbsm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("ktlxm")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">课题类型</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("ktlx") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("lwsycsdm")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">重复使用次数</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("lwsycssm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("kssj")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">开始时间</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("kssj") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("jssj")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">结束时间</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("jssj") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("sfzdfcxm")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">重点扶持项目</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("sfzdfcxm") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("tmrl")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">题目容量</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("tmrl") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("lwyjfx")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">论文研究方向</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("lwyjfx") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("dezdjs")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">第二指导教师</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("dezdjsm") + "（" + sqbInfo.get("dezdjs") + "）" + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("zsjzwc")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">在社会实践中完成</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("zsjzwc") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("sfxqhz")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">校企合作</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("sfxqhz") + "</div>" +
						"    </div>");
				if ("是".equals(sqbInfo.get("sfxqhz"))) {
					if (btxmap.containsKey("qydsxm")) {
						infoHt.append("<div class=\"profile-info-row\">" +
								"     <div class=\"profile-info-name\">企业导师姓名</div>" +
								"     <div class=\"profile-info-value\">" + sqbInfo.get("qydsxm") + "</div>" +
								"    </div>");
					}
					if (btxmap.containsKey("qydszcdm")) {
						infoHt.append("<div class=\"profile-info-row\">" +
								"     <div class=\"profile-info-name\">企业导师职称</div>" +
								"     <div class=\"profile-info-value\">" + sqbInfo.get("qydszcdm") + "</div>" +
								"    </div>");
					}
					if (btxmap.containsKey("hzqymc")) {
						infoHt.append("<div class=\"profile-info-row\">" +
								"     <div class=\"profile-info-name\">合作企业名称</div>" +
								"     <div class=\"profile-info-value\">" + sqbInfo.get("hzqymc") + "</div>" +
								"    </div>");
					}
				}
			}
			if (btxmap.containsKey("tmjj")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">题目简介</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("tmjj") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("jnfw")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">接纳范围</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("jnfw") + "</div>" +
						"    </div>");
			}
			if (btxmap.containsKey("bz")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">难点及要点</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("bz") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("mdjyy")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">目的及意义</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("mdjyy") + "</div>" +
						"    </div>");
			}

			if (btxmap.containsKey("zyyhnr")) {
				infoHt.append("<div class=\"profile-info-row\">" +
						"     <div class=\"profile-info-name\">主要研究内容</div>" +
						"     <div class=\"profile-info-value\">" + sqbInfo.get("zyyjnr") + "</div>" +
						"    </div>");
			}
			infoHt.append("<div class=\"profile-info-row\">" +
					"     <div class=\"profile-info-name\"> 选定学生</div>" +
					"     <div class=\"profile-info-value\">" +
					"      <div class=\"widget-content navbar-example\"" +
					"        style=\"overflow: auto;height-max: 200px;\">" +
					"       <table class=\"table table-striped table-bordered table-hover\">" +
					"        <thead>" +
					"        <tr class=\"center\">" +
					"         <th>方案年级</th>" +
					"         <th>方案院系</th>" +
					"         <th>方案专业</th>" +
					"         <th>方案专业方向</th>" +
					"         <th>培养方案</th>" +
					"         <th>学号</th>" +
					"         <th>姓名</th>" +
					"         <th>班级</th>" +
					"        </tr>" +
					"        </thead>" +
					"        <tbody id=\"scoreintbody\" style=\"overflow: auto;\">");
			for (int i = 0, teni = xsList.size(); i < teni; i++) {
				com.alibaba.fastjson.JSONObject xss = xsList.get(i);
				infoHt.append("<tr class=\"center\"> <td>" + xss.get("nj") + "</td> <td>" + xss.get("xsm") + "</td> <td>" + xss.get("zym") + "</td>" +
						" <td>" + xss.get("zyfxm") + "</td> <td>" + xss.get("famc") + "</td> <td>" + xss.get("xh") + "</td><td>" + xss.get("xm") + "</td> " +
						"<td>" + xss.get("bjm") + "</td></tr>");
			}

			infoHt.append("        </tbody>" +
					"       </table>" +
					"      </div>" +
					"     </div>" +
					"    </div>");
			infoHt.append("   </div>");
		}
		return infoHt.toString().replace("null", "");
	}

	private String getJsmByJsh(String jsh) {
		String sql = "SELECT a.jsm FROM code_jsb a where a.jsh = :jsh ";
		Query query = em.createNativeQuery(sql);
		query.setParameter("jsh", jsh);
		List<Object> reslist = query.getResultList();
		String jsm = "";
		if (reslist.size() > 0) {
			jsm = reslist.get(0).toString();
		}
		return jsm;
	}

	private String getTimeFormat(String time) {
		if (StringUtils.isNotBlank(time)) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
			formatter.setLenient(false);
			Date newDate = null;
			try {
				newDate = formatter.parse(time);
			} catch (ParseException e) {
				e.printStackTrace();
				return "";
			}
			formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			return formatter.format(newDate);
		} else {
			return "";
		}
	}


}