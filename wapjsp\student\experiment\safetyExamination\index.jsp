<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实验安全考试</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实验安全考试页面样式 */
        .safety-header {
            background: linear-gradient(135deg, var(--error-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(245, 34, 45, 0.3);
        }
        
        .safety-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .safety-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .exam-types-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-title i {
            color: var(--error-color);
        }
        
        .exam-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
        }
        
        .exam-type-card {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid var(--border-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .exam-type-card:hover {
            background: var(--error-light);
            border-color: var(--error-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(245, 34, 45, 0.3);
        }
        
        .exam-type-card:active {
            transform: translateY(0);
        }
        
        .exam-type-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--error-light);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .exam-type-icon i {
            font-size: 24px;
            color: var(--error-color);
        }
        
        .exam-type-info {
            flex: 1;
        }
        
        .exam-type-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .exam-type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .exam-type-arrow {
            color: var(--text-disabled);
            font-size: 18px;
            transition: all var(--transition-base);
        }
        
        .exam-type-card:hover .exam-type-arrow {
            color: var(--error-color);
            transform: translateX(4px);
        }
        
        .safety-notice {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
            font-size: var(--font-size-small);
        }
        
        .notice-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: var(--margin-sm);
            font-weight: 500;
        }
        
        .notice-header i {
            color: var(--warning-color);
        }
        
        .notice-content {
            line-height: 1.6;
        }
        
        .notice-list {
            margin: var(--margin-sm) 0;
            padding-left: var(--padding-md);
        }
        
        .notice-list li {
            margin-bottom: 4px;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        @media (max-width: 480px) {
            .exam-types-grid {
                grid-template-columns: 1fr;
            }
            
            .exam-type-card {
                flex-direction: column;
                text-align: center;
                gap: var(--spacing-sm);
            }
            
            .exam-type-arrow {
                transform: rotate(90deg);
            }
            
            .exam-type-card:hover .exam-type-arrow {
                transform: rotate(90deg) translateX(4px);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实验安全考试</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 实验安全考试头部 -->
        <div class="safety-header">
            <div class="safety-title">实验安全考试</div>
            <div class="safety-desc">确保实验室安全，提升安全意识</div>
        </div>
        
        <!-- 安全须知 -->
        <div class="safety-notice">
            <div class="notice-header">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>安全考试须知</span>
            </div>
            <div class="notice-content">
                <div>请认真阅读以下注意事项：</div>
                <ul class="notice-list">
                    <li>考试前请仔细阅读相关安全规范和操作指南</li>
                    <li>考试过程中请保持网络连接稳定</li>
                    <li>每种类型的考试只有有限次数的机会</li>
                    <li>考试通过后方可进入相应实验室</li>
                    <li>如有疑问请及时联系实验室管理员</li>
                </ul>
            </div>
        </div>
        
        <!-- 试题类型 -->
        <div class="exam-types-container">
            <div class="container-title">
                <i class="ace-icon fa fa-list"></i>
                试题类型
            </div>
            
            <div class="exam-types-grid" id="examTypesGrid">
                <c:choose>
                    <c:when test="${not empty list}">
                        <c:forEach items="${list}" var="examType" varStatus="status">
                            <div class="exam-type-card" onclick="startExam('${examType.stlbdm}', '${examType.stlbmc}');">
                                <div class="exam-type-icon">
                                    <i class="ace-icon fa fa-shield"></i>
                                </div>
                                <div class="exam-type-info">
                                    <div class="exam-type-name">${examType.stlbmc}</div>
                                    <div class="exam-type-desc">点击开始安全考试</div>
                                </div>
                                <div class="exam-type-arrow">
                                    <i class="ace-icon fa fa-chevron-right"></i>
                                </div>
                            </div>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="empty-state">
                            <i class="ace-icon fa fa-file-text-o"></i>
                            <div class="empty-state-text">暂无可用考试</div>
                            <div class="empty-state-desc">请联系管理员了解详情</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 开始考试
        function startExam(examTypeCode, examTypeName) {
            if (!examTypeCode) {
                showError('考试类型代码无效');
                return;
            }
            
            if (confirm(`确定要开始"${examTypeName}"安全考试吗？\n\n请确保您已经：\n1. 仔细阅读了相关安全规范\n2. 网络连接稳定\n3. 有充足的时间完成考试`)) {
                showLoading(true);
                
                // 跳转到考试承诺页面
                window.location.href = `/student/experiment/safetyExamination/index/jumpPromise/${examTypeCode}`;
            }
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
