<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!-- 缓存 -->
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>

    <title>历年成绩变更申请</title>
    <%--tablesorter 表格排序--%>
    <link rel="stylesheet" href="/css/tablesorter/theme.default.css"/>

    <style type="text/css">
        td {
            white-space: nowrap;
        }

        th {
            white-space: nowrap;
            text-align: center
        }
    </style>
</head>

<body>
<div class="row">
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    <div class="col-xs-12 self-margin">
        <h4 class="header smaller lighter grey">
            <i class="ace-icon fa fa-search"></i>
            查询条件
            <c:if test="${flag == 'nonparametric'}">（申请参数未维护，请联系管理员处理）</c:if>
            <c:if test="${flag == 'notenabled'  }">（申请未启用，请联系管理员处理）</c:if>
            <c:if test="${flag == 'nottime'  }">（不在申请时间范围或申请开关关闭）</c:if>
            <c:if test="${flag == 'showAdd'  }">（申请时间：${fn:substring(kzkg.kssj, 0, 4)}-${fn:substring(kzkg.kssj, 4, 6)}-${fn:substring(kzkg.kssj, 6, 8)} ${fn:substring(kzkg.kssj, 8, 10)}:${fn:substring(kzkg.kssj, 10, 12)}:${fn:substring(kzkg.kssj, 12, 14)}~${fn:substring(kzkg.jssj, 0, 4)}-${fn:substring(kzkg.jssj, 4, 6)}-${fn:substring(kzkg.jssj, 6, 8)} ${fn:substring(kzkg.jssj, 8, 10)}:${fn:substring(kzkg.jssj, 10, 12)}:${fn:substring(kzkg.jssj, 12, 14)}）</c:if>
            <span class="right_top_oper">
					<button class="btn btn-info btn-round btn-xs" id="queryStudent" onclick="getApplysList(1,'30_sl',true);return false;">
						<i class="ace-icon fa fa-search white"></i> 查询
					</button>
					<c:if test="${flag == 'showAdd' }">
						<button title="新增申请" class="btn btn-success btn-xs btn-round" onclick="addApply();return false;">
							<i class="ace-icon fa fa-plus bigger-120"></i> 新增申请
						</button>
                    </c:if>
				</span>
        </h4>
        <form id="queryInfo" name="queryInfo">
            <div class=" profile-user-info profile-user-info-striped self" id="queryVal_id">
                <div class="profile-info-row">
                    <div class="profile-info-name">学年学期</div>
                    <div class="profile-info-value">
                        <select id="zxjxjhh_cx" name="zxjxjhh_cx" class="chosen-select form-control value_element">
                            <cache:query var="xnxq" region="jh_zxjxjhb_view" orderby="zxjxjhh desc"/>
                            <c:forEach items="${xnxq}" var="xnxq" varStatus="index">
                                <option value="${xnxq.zxjxjhh}"
                                        <c:if test="${xnxq.zxjxjhh == zxjxjhh}"> selected </c:if>>${xnxq.zxjxjhm}</option>
                            </c:forEach>
                        </select>
                    </div>
                    <div class="profile-info-name"> 开课院系</div>
                    <div class="profile-info-value">
                        <select name="kkxsh_cx" id="kkxsh_cx"  class="chosen-select form-control value_element">
                            <option value="">全部</option>
                            <cache:query var="xsb" region="code_xsb_jxdw" orderby="xsh asc"/>
                            <c:forEach items="${xsb}" var="xsb">
                                <option value="${xsb.xsh}">${xsb.xsm}</option>
                            </c:forEach>
                        </select>
                    </div>
                    <div class="profile-info-name"> 课程号</div>
                    <div class="profile-info-value">
                        <input type="text" name="kch_cx" id="kch_cx" >
                    </div>
                    <div class="profile-info-name"> 课程名</div>
                    <div class="profile-info-value">
                        <input type="text" name="kcm_cx" id="kcm_cx" >
                    </div>
                </div>
            </div>
        </form>
        <h4 class="header smaller lighter grey">
            <i class="glyphicon glyphicon-list"></i> 申请信息
        </h4>

        <div class="row">
            <div class="col-xs-12">
                <div style="overflow: auto; height: calc(100vh - 275px);" id="xsxk_scroll">
                    <table class="table table-bordered table-hover" id="xsXkTable">
                        <thead>
                        <tr class="center" style="background-color: #f9f9f9;">
                            <th style="border-top:3px solid #ddd;background-color:transparent;">操作</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">序号</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">申请编号</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">课程号</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">课程名</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">课序号</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">开课院系</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">学年学期</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">申请原因</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">附件</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">申请状态</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">审批结果</th>
                            <th style="border-top:3px solid #ddd;background-color:transparent;">备注</th>
                        </tr>
                        </thead>
                        <tbody id="xsXktbody"></tbody>
                    </table>
                </div>
                <div id="urppagebar-1" style="margin: 5px 0 0 0;width: 99%"></div>
            </div>
        </div>
    </div>
</div>

<script src="/js/tablesorter/jquery.tablesorter.js"></script>
<%--移动鼠标批量选中 --%>
<script src="/js/drag-check-js-2.0.2/dist/jquery.dragcheck.js"></script>
<script type="text/javascript">
    //带查询下拉框加载
    $(function () {
        /*固定表头用*/
        urp.fixedheader("xsxk_scroll", "450");//分页divid，加高度
        $('.chosen-select').chosen({allow_single_deselect: true});
        $(window).off('resize.chosen').on('resize.chosen', function () {
            $('.chosen-select').each(function () {
                var $this = $(this);
                $this.next().css({'width': 200});
            });
        }).trigger('resize.chosen');
        getApplysList(1, "30_sl", true);
    });

    var params;
    function getApplysList(page, pageSizeVal, conditionChanged) {
        var before_html = $("#queryStudent").html();
        if (pageSizeVal == undefined) {
            pageSizeVal = "30_sl";
            page = "1";
        }
        if (conditionChanged) {
            params = $(document.queryInfo).serialize();
        }
        var parr = (pageSizeVal + "").split("_");
        var pageSize = parseInt(parr[0]);
        var url = "/student/application/gradeChange/getApplyList";

        var index;
        $.ajax({
            url: url,
            cache: false,
            type: "post",
            data: params + "&pageNum=" + page + "&pageSize=" + pageSize,
            dataType: "json",
            beforeSend: function () {
                index = layer.load(0, {
                    shade: [0.2, "#000"] //0.1透明度的白色背景
                });
            },
            complete: function () {
                layer.close(index);
            },
            success: function (d) {
                urp.pagebar("urppagebar-1", pageSizeVal, page, d.data["pageContext"].totalCount, getApplysList, "on", "xsxk_scroll");
                var isScroll = (pageSizeVal + "").indexOf("_") != -1 && page != 1 ? true : false;
                if (d.data["records"] != null) {
                    fillPyfaTable(d.data["records"], isScroll, page, pageSize);
                } else {
                    fillPyfaTable(null, isScroll, page, pageSize);
                }
            },
            error: function (xhr) {
                urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
            }
        });
    }

    function fillPyfaTable(data, isScroll, page, pageSize) {
        var tcont = "";
        if (data != null) {
            $.each(data, function (i, v) {
                var tableId = "";
                if (isScroll) {
                    tableId = (page - 1) * pageSize + 1 + i;
                } else {
                    tableId = i + 1;
                }
                tcont += "<tr>";
                tcont += "<td>";
                if (v.APPLY_STATUS == 1 || v.APPLY_STATUS == 2 || v.APPLY_STATUS == 3 || v.APPLY_STATUS == -1) {
                    tcont += "<i title='查看' onclick='seeApply(\"" + v.APPLY_ID + "\");' class='ace-icon fa fa-eye iinfo bigger-130'></i>&nbsp;";
                }
                if (v.APPLY_STATUS == 0) {
                    tcont += "<i title='修改' onclick='openEditPage(\"" + v.APPLY_ID + "\");' class='ace-icon fa fa-pencil-square-o iinfo bigger-130'></i>&nbsp;";
                    tcont += "<i title='撤回' onclick='revokeApply(\"" + v.APPLY_ID + "\");' class='ace-icon fa fa-reply idanger bigger-130'></i>&nbsp;";
                }
                tcont += "</td>";
                tcont += "<td>" + tableId + "</td>";
                tcont += "<td>" + (v.APPLY_ID == null ? "" : v.APPLY_ID) + "</td>";
                tcont += "<td>" + (v.KCH == null ? "" : v.KCH) + "</td>";
                tcont += "<td>" + (v.KCM == null ? "" : v.KCM) + "</td>";
                tcont += "<td>" + (v.KXH == null ? "" : v.KXH) + "</td>";
                tcont += "<td>" + (v.KKXSM == null ? "" : v.KKXSM) + "</td>";
                tcont += "<td>" + (v.XNXQ == null ? "" : v.XNXQ) + "</td>";
                tcont += "<td>" + (v.SQYY == null ? "" : v.SQYY) + "</td>";
                tcont += "<td>" + (v.FJ == null ? "" : v.FJ.replace(/,/g, "<br>")) + "</td>";
                var status = "";
                //申请状态(-1撤销0待提交1已提交2审批中3审批结束)
                if (v.APPLY_STATUS == -1) {
                    status = "撤销";
                }
                if (v.APPLY_STATUS == 0) {
                    status = "待提交";
                }
                if (v.APPLY_STATUS == 1) {
                    status = "已提交";
                }
                if (v.APPLY_STATUS == 2) {
                    status = "审批中";
                }
                if (v.APPLY_STATUS == 3) {
                    status = "审批结束";
                }
                tcont += "<td>" + status + "</td>";
                //审批结果(0否决1批准)
                if (v.EA_RSLT == "0") {
                    tcont += "<td>拒绝</td>";
                }
                if (v.EA_RSLT == "1") {
                    tcont += "<td>批准</td>";
                }
                if (v.EA_RSLT == "") {
                    tcont += "<td></td>";
                }
                tcont += "<td>" + (v.NOTE == null ? "" : v.NOTE) + "</td>";
                tcont += "</tr>";
            });
        }
        if (isScroll) {
            $("#xsXktbody").append(tcont);
        } else {
            $("#xsXktbody").html(tcont);
        }
        urp.trhighlight("xsXkTable");//table表格id
        $(".tablesorter").trigger("update");
        $("#xsXkTable").tablesorter({headers: {0: {sorter: false}}});
        /*排除第一列*/
        $("#xsXkTable").tablesorter();//table表格id
    }

    function seeApply(sqbh) {
        var modal = urp.addModelHt("60%", "seeApply");
        var url = "/student/application/scoreCheck/seeApply?sqbh=" + sqbh;
        modal.modal({
            remote: url,
            backdrop: "static",
            keyboard: false
        }).on('hide.bs.modal', function () {
            modal.remove();
            $('.modal-backdrop').remove();
            return false;
        });
    }

    //撤回申请
    function revokeApply(sqbh) {
        urp.confirm("确定要撤回申请？", callback);
        function callback(f) {
            if (!f) {
                return false;
            } else {
                $.ajax({
                    url: "/student/application/scoreCheck/revokeApply",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    beforeSend: function () {
                        index = layer.load(0, {
                            shade: [0.2, "#000"]
                        });
                    },
                    complete: function () {
                        layer.close(index);
                    },
                    success: function (data) {
                        if (data.status != 200) {
                            layer.alert(data.msg, {icon: 5, time: 3000});
                        } else {
                            $("#tokenValue").val(data.data["token"]);
                            if (data.data["result"].indexOf("/") != -1) {
                                layer.alert("页面已过期，请刷新页面！", {skin: "layui-layer-molv", closeBtn: 0});
                            } else {
                                if (data.data["result"] == "ok") {
                                    urp.alert("撤销成功！");
                                    getApplysList(1, "30_sl", true);
                                }
                            }
                        }
                    },
                    error: function () {
                        urp.alert("撤销失败！");
                    }
                });
            }
        }
    }

    function addApply() {
        var url = "/student/application/gradeChange/checkaddApply";
        var index;
        $.ajax({
            url : url,
            cache : false,
            type : "post",
            dataType : "json",
            beforeSend: function () {
                index = layer.load(0, {
                    shade: [0.2, "#000"]
                });
            },
            complete: function () {
                layer.close(index);
            },
            success : function(d) {
                if(d.msg) {
                    urp.alert(d.msg);
                } else {
                    if(d.sfxyd == "1") {
                        $("#readMsg").html(d.ydnr);
                        $('#showReadMsg').modal({
                            backdrop: "static",
                            keyboard: false
                        }).on("hidden.bs.modal", function() {
                            $(this).removeData("bs.modal");
                            $("#view-table .modal-dialog").css("width", "60%");
                            $("#view-table .modal-content").html('<div class="center">\
					                <img src="/img/icon/pageloading.gif" style="width:28px;height:28px;">\
					            </div>');
                        });
                        var interval = "";
                        var qzydms = d.qzydms;
                        if(qzydms > 0) {
                            $("#djs").html("（"+ qzydms +"s）");
                            interval = setInterval(function() {
                                qzydms--;
                                if(qzydms == 0) {
                                    clearInterval(interval);
                                    $("#djs").html("");
                                    $("#djs").parents("button").attr("disabled", false);
                                    $("#djs").parents("button").attr("onclick", "$('#showReadMsgClose').click(); setTimeout(function() {openEditPage();}, 500)");
                                } else {
                                    $("#djs").parent("button").attr("disabled", true);
                                    $("#djs").html("（"+ qzydms +"s）");
                                }
                            }, 1000);
                        } else {
                            $("#djs").parent("button").attr("onclick", "$('#showReadMsgClose').click(); setTimeout(function() {openEditPage();}, 500)");
                        }
                    } else {
                        openEditPage();
                    }
                }
            },
            error : function(xhr) {
                urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
            }
        });
    }

    function openEditPage(sqbh) {
        var url =  "/student/application/gradeChange/addApply?sqbh=" + (sqbh ? sqbh : "");
        var modal = urp.addModelHt("60%", "addApply");
        modal.modal({
            remote: url
        }).on('hide.bs.modal', function () {
            modal.remove();
            $('.modal-backdrop').remove();
            return false;
        });
    }
</script>
<div class="modal fade" id="showReadMsg" tabindex="1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width:80%;">
        <div class="modal-content">
            <div class="modal-header no-padding">
                <div class="table-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        <span class="white">×</span>
                    </button>
                    <i class="glyphicon glyphicon-file"></i> 申请需知
                </div>
            </div>
            <div class="modal-body" style="overflow: auto; height: calc(100vh - 160px); padding: 0 12px;">
                <div id="readMsg"></div>
            </div>

            <div class="modal-footer no-margin-top">
                <button type="button" class="btn btn-info btn-xs btn-round">
                    <i class="ace-icon fa fa-arrow-right bigger-130"></i> 继续<span id="djs"></span>
                </button>
                <button class="btn btn-xs btn-default pull-right btn-round" data-dismiss="modal" id="showReadMsgClose">
                    <i class="ace-icon fa fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>
</div>
</body>
</html>
