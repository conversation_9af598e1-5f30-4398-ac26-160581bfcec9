<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!-- 缓存 -->
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>指定审批人</title>
</head>
<body>
	<div class="modal-header no-padding">
    	<div class="table-header">
			<button type="button" class="close" data-dismiss="modal" aria-hidden="true" id="closeZdspr">
               <span class="white">×</span>
           </button>
			<i class="fa  fa-plus-circle"></i> 指定审批人（<span id="ealName"></span>）
	    </div>
	</div>
	<input type="hidden" id="eap_code" name="eap_code"/>
    <input type="hidden" id="eal_code" name="eal_code"/>
	<div class="modal-body">
		<div class="widget-body">
			<div class="widget-content widget-box" id="curriculum_scroll" style="max-height: calc(100vh - 230px);overflow: auto;">
				<table class="table table-bordered table-hover"  id="curriculumTable">
					<tbody id="curriculumtbody" style="overflow: auto;">
					</tbody>
					<thead >
						<tr class="center">
							<th>选择</th>
							<th>序号</th>
							<th>教师名</th> 
							<th>院系</th>    
						</tr>
					</thead>
				</table>
			</div>
		</div>
	</div>
    <div class="modal-footer no-margin-top ">
	    <div class="center">
	        <button title='确认选择' id="loading-btn" class="btn btn-purple btn-xs btn-round" onclick="save();">
	            <i class="ace-icon fa fa-check bigger-120"></i> 确认选择
	        </button>
	    </div>
	</div>
    <script type="text/javascript">
    	$(function(){
    		queryApprover();
    		urp.fixedheader("curriculum_scroll");
    	});
    	
    	function queryApprover(){
    		var index;
    		var formData = new FormData($("#addTeacherInfoForm")[0]);
			formData.append("apply_status", "1");
	        $.ajax({
	            url: "/student/application/scoreCheck/queryApprover",
	            type: "post",
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
	            beforeSend: function () {
                	index = layer.load(0, {
	                    shade: [0.2, "#000"] //0.1透明度的白色背景
	                });
	            },
	            complete: function () {
	                layer.close(index);
	            },
	            success: function (data) {
	            	$("#eap_code").val(data.data["eap_code"]);
	            	$("#eal_code").val(data.data["eal_code"]);
	            	$("#ealName").html(data.data["eal_name"]);
					var tcont = "";
					$.each(data.data["approvers"],function(i,v){
						tcont += "<tr>";
						tcont += "<td class='center'><label><input name='choose' type='radio' class='ace' value='"+v[0]+"'><span class='lbl'></span></label></td>";
						tcont += "<td>"+(i+1)+"</td>";
						tcont += "<td>"+v[1]+"</td>";
						tcont += "<td>"+v[2]+"</td>";
						tcont += "<tr>";
					});
					$("#curriculumtbody").html(tcont);
	            },
	            error: function () {
	                urp.alert("错误代码["+xhr.readyState+"-"+xhr.status+"]:查询审批人失败！");
	            }
	        });
    	}
		
		function save(){
			var jsh = $("input[name='choose']:checked").val();
			if(jsh == "" || jsh == undefined || jsh == null){
				urp.alert("请指定审批人！");
				return false;
			}
			$("#spjsh").val(jsh);
			saveInfo("1");
		}
    </script>
</body>
</html>