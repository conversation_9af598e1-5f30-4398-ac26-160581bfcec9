<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>历年成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 移动端成绩查询样式 */
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-row {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .search-item {
            flex: 1;
            min-width: 120px;
        }
        
        .search-item:last-child {
            flex: 0 0 80px;
        }
        
        .score-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .score-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .score-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 500;
            min-width: 50px;
            text-align: center;
        }
        
        .score-pass {
            background: var(--success-color);
            color: white;
        }
        
        .score-fail {
            background: var(--error-color);
            color: white;
        }
        
        .score-excellent {
            background: var(--warning-color);
            color: white;
        }
        
        .score-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .score-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .score-detail-label {
            color: var(--text-secondary);
        }
        
        .score-detail-value {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .warning-notice {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: var(--line-height-base);
        }
        
        .warning-notice i {
            margin-right: var(--margin-xs);
        }
        
        .stats-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stats-item {
            padding: var(--padding-sm);
        }
        
        .stats-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stats-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        /* 筛选器样式 */
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
            margin-top: var(--margin-sm);
        }
        
        .filter-chip {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">历年成绩</div>
            <div class="navbar-action" onclick="showSearchModal();">
                <i class="ace-icon fa fa-search"></i>
            </div>
        </nav>
        
        <!-- 四川大学特别提示 -->
        <c:if test="${school_id == '100006'}">
            <div class="warning-notice">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <strong>特别提示：</strong>根据《四川大学本科生学业预警管理办法》（川大教〔2020〕13号）文件规定，在校学习期间，不及格必修课程（含缺考）累计超过30学分（已经重修并且及格的课程不再计入），且已获得总学分未达到所在专业毕业规定总学分的75%者，作退学处理。此管理办法自2019级起执行，请各位同学认真学习，避免达到退学红线。
            </div>
        </c:if>
        
        <!-- 统计摘要 -->
        <div class="stats-summary" id="statsSummary" style="display: none;">
            <div class="stats-grid">
                <div class="stats-item">
                    <div class="stats-number" id="totalCourses">0</div>
                    <div class="stats-label">总课程</div>
                </div>
                <div class="stats-item">
                    <div class="stats-number" id="passedCourses">0</div>
                    <div class="stats-label">已通过</div>
                </div>
                <div class="stats-item">
                    <div class="stats-number" id="totalCredits">0</div>
                    <div class="stats-label">总学分</div>
                </div>
            </div>
        </div>
        
        <!-- 快速筛选 -->
        <div class="search-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterByResult('all')">全部</button>
                <button class="filter-chip" onclick="filterByResult('pass')">已通过</button>
                <button class="filter-chip" onclick="filterByResult('fail')">未通过</button>
                <button class="filter-chip" onclick="filterByResult('excellent')">优秀</button>
            </div>
        </div>
        
        <!-- 成绩列表 -->
        <div class="container-mobile">
            <div id="scoreList">
                <!-- 成绩项将通过JavaScript动态填充 -->
            </div>
            
            <!-- 加载更多 -->
            <div class="loading-container" id="loadingMore" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-file-text-o"></i>
                <div>暂无成绩记录</div>
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div id="urppagebar" style="display: none;"></div>
    </div>

    <!-- 搜索模态框 -->
    <div class="modal fade" id="searchModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                    <h4 class="modal-title">筛选条件</h4>
                </div>
                <div class="modal-body">
                    <div class="form-mobile">
                        <div class="form-group">
                            <label class="form-label">学年学期</label>
                            <select class="form-control" id="zxjxjhh">
                                <option value="">全部</option>
                                <c:forEach items="${list}" var="list">
                                    <option value="${list[0]}" <c:if test="${list[0]==nowXnxq}">selected</c:if>>${list[1]}</option>
                                </c:forEach>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">课程号</label>
                            <input type="text" class="form-control" id="kch" placeholder="请输入课程号">
                        </div>
                        <div class="form-group">
                            <label class="form-label">课程名</label>
                            <input type="text" class="form-control" id="kcm" placeholder="请输入课程名">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-mobile btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn-mobile btn-primary" onclick="searchScores();">查询</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let isLoading = false;
        let hasMore = true;
        let allScores = [];
        let filteredScores = [];
        let currentFilter = 'all';

        $(function() {
            initPage();
            search();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            
            // 绑定滚动事件
            $(window).scroll(function() {
                if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                    if (!isLoading && hasMore) {
                        loadMoreScores();
                    }
                }
            });
        }

        // 搜索成绩
        function search() {
            getResults${method_mark_code}List(1, "30_sl", true);
        }

        // 获取成绩列表（保持原有函数名以兼容）
        function getResults${method_mark_code}List(page, pageSizeVal, conditionChanged) {
            if (isLoading) return;
            
            isLoading = true;
            showLoading(true);

            if (pageSizeVal == undefined) {
                pageSizeVal = "30_sl";
                page = "1";
            }
            
            var parr = (pageSizeVal + "").split("_");
            var pageSize = parseInt(parr[0]);
            var url = "/student/integratedQuery/scoreQuery/${url_check_code}/allTermScores/data";
            var zxjxjhh = $("#zxjxjhh").val();
            var kch = $("#kch").val();
            var kcm = $("#kcm").val();
            
            $.ajax({
                url: url,
                cache: false,
                type: "post",
                data: "zxjxjhh=" + zxjxjhh + "&kch=" + kch + "&kcm=" + kcm + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    var records = data["list"]["records"];
                    var totalCount = data["list"]["pageContext"].totalCount;
                    
                    if (conditionChanged) {
                        allScores = records || [];
                        currentPage = 1;
                    } else {
                        allScores = allScores.concat(records || []);
                    }
                    
                    hasMore = allScores.length < totalCount;
                    applyFilter();
                    updateStats();
                    
                    // 更新分页信息
                    urp.pagebar("urppagebar", pageSizeVal, page, totalCount, getResults${method_mark_code}List, "on", "pager_scroll");
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    isLoading = false;
                    showLoading(false);
                }
            });
        }

        // 渲染成绩列表
        function renderScores(scores) {
            const container = $('#scoreList');
            container.empty();
            
            if (scores.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            scores.forEach(function(score, index) {
                const scoreHtml = createScoreItem(score, index);
                container.append(scoreHtml);
            });
        }

        // 创建成绩项HTML
        function createScoreItem(score, index) {
            const courseCode = score[1] || '';
            const courseSeq = score[2] || '';
            const courseName = score[11] || '';
            const courseNameEn = score[12] || '';
            const credits = score[13] || '';
            const hours = score[14] || '';
            const courseType = score[15] || '';
            const examType = score[16] || '';
            const scoreValue = score[18] || '';
            const failReason = score[19] || '';
            
            // 判断成绩等级
            let scoreClass = 'score-pass';
            let scoreText = scoreValue;
            
            if (scoreValue === '' || scoreValue === '缺考' || scoreValue === '不及格') {
                scoreClass = 'score-fail';
                scoreText = failReason || '未通过';
            } else if (parseFloat(scoreValue) >= 90 || scoreValue === '优秀') {
                scoreClass = 'score-excellent';
            }
            
            return `
                <div class="score-item">
                    <div class="score-header">
                        <div class="course-name">${courseName}</div>
                        <div class="score-badge ${scoreClass}">${scoreText}</div>
                    </div>
                    <div class="score-details">
                        <div class="score-detail-item">
                            <span class="score-detail-label">课程号:</span>
                            <span class="score-detail-value">${courseCode}</span>
                        </div>
                        <div class="score-detail-item">
                            <span class="score-detail-label">学分:</span>
                            <span class="score-detail-value">${credits}</span>
                        </div>
                        <div class="score-detail-item">
                            <span class="score-detail-label">课程属性:</span>
                            <span class="score-detail-value">${courseType}</span>
                        </div>
                        <div class="score-detail-item">
                            <span class="score-detail-label">考试类型:</span>
                            <span class="score-detail-value">${examType}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'pass':
                    filteredScores = allScores.filter(score => {
                        const scoreValue = score[18] || '';
                        return scoreValue !== '' && scoreValue !== '缺考' && scoreValue !== '不及格';
                    });
                    break;
                case 'fail':
                    filteredScores = allScores.filter(score => {
                        const scoreValue = score[18] || '';
                        return scoreValue === '' || scoreValue === '缺考' || scoreValue === '不及格';
                    });
                    break;
                case 'excellent':
                    filteredScores = allScores.filter(score => {
                        const scoreValue = score[18] || '';
                        return parseFloat(scoreValue) >= 90 || scoreValue === '优秀';
                    });
                    break;
                default:
                    filteredScores = allScores;
            }
            
            renderScores(filteredScores);
        }

        // 按结果筛选
        function filterByResult(type) {
            currentFilter = type;
            
            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');
            
            applyFilter();
        }

        // 更新统计信息
        function updateStats() {
            const totalCourses = allScores.length;
            const passedCourses = allScores.filter(score => {
                const scoreValue = score[18] || '';
                return scoreValue !== '' && scoreValue !== '缺考' && scoreValue !== '不及格';
            }).length;
            
            const totalCredits = allScores.reduce((sum, score) => {
                const credits = parseFloat(score[13]) || 0;
                return sum + credits;
            }, 0);
            
            $('#totalCourses').text(totalCourses);
            $('#passedCourses').text(passedCourses);
            $('#totalCredits').text(totalCredits.toFixed(1));
            
            if (totalCourses > 0) {
                $('#statsSummary').show();
            }
        }

        // 显示搜索模态框
        function showSearchModal() {
            $('#searchModal').modal('show');
        }

        // 执行搜索
        function searchScores() {
            $('#searchModal').modal('hide');
            search();
        }

        // 加载更多成绩
        function loadMoreScores() {
            if (!hasMore || isLoading) return;
            
            currentPage++;
            getResults${method_mark_code}List(currentPage, "30_sl", false);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingMore').show();
            } else {
                $('#loadingMore').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
