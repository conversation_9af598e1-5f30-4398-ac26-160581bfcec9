<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教室使用查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教室使用查询页面样式 */
        .usage-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .search-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .search-row {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .search-item {
            flex: 1;
        }
        
        .search-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .search-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-search {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-reset {
            background: var(--text-disabled);
            color: white;
        }
        
        .classroom-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .classroom-info.show {
            display: block;
        }
        
        .info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .info-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .classroom-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .classroom-icon {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-h3);
            margin-right: var(--margin-md);
        }
        
        .classroom-details {
            flex: 1;
        }
        
        .classroom-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .classroom-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .usage-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .usage-container.show {
            display: block;
        }
        
        .usage-header-bar {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .usage-title {
            display: flex;
            align-items: center;
        }
        
        .usage-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .date-selector {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .date-nav {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .date-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            min-width: 120px;
            text-align: center;
        }
        
        .usage-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .usage-table th,
        .usage-table td {
            border: 1px solid var(--divider-color);
            padding: 8px 4px;
            text-align: center;
            font-size: var(--font-size-small);
            vertical-align: middle;
        }
        
        .usage-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .time-header {
            width: 80px;
            min-width: 80px;
        }
        
        .status-header {
            width: calc(100% - 80px);
        }
        
        .time-cell {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .status-cell {
            height: 50px;
            position: relative;
            cursor: pointer;
        }
        
        .status-free {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .status-occupied {
            background: var(--error-light);
            color: var(--error-color);
        }
        
        .status-maintenance {
            background: var(--warning-light);
            color: var(--warning-color);
        }
        
        .status-reserved {
            background: var(--info-light);
            color: var(--info-color);
        }
        
        .usage-detail {
            font-size: var(--font-size-mini);
            line-height: 1.2;
            padding: 4px;
        }
        
        .course-name {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .course-teacher {
            opacity: 0.8;
        }
        
        .no-usage {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .no-usage i {
            font-size: var(--font-size-h1);
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .usage-legend {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .usage-legend.show {
            display: block;
        }
        
        .legend-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .legend-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .legend-items {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            font-size: var(--font-size-small);
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: var(--margin-xs);
        }
        
        .legend-color.free {
            background: var(--success-light);
        }
        
        .legend-color.occupied {
            background: var(--error-light);
        }
        
        .legend-color.maintenance {
            background: var(--warning-light);
        }
        
        .legend-color.reserved {
            background: var(--info-light);
        }
        
        .usage-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .usage-detail-modal.show {
            display: flex;
        }
        
        .usage-detail-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .usage-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .usage-detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .usage-detail-close {
            color: var(--text-secondary);
            cursor: pointer;
            font-size: var(--font-size-h4);
        }
        
        .usage-detail-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .detail-item {
            margin-bottom: var(--margin-sm);
            display: flex;
            justify-content: space-between;
        }
        
        .detail-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-right: var(--margin-sm);
        }
        
        .detail-value {
            flex: 1;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教室使用查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="usage-header">
            <div class="header-title">教室使用查询</div>
            <div class="header-subtitle">查询教室的使用情况和课程安排</div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                <span>搜索教室</span>
            </div>

            <div class="search-form">
                <div class="search-row">
                    <div class="search-item">
                        <div class="search-label">教室名称</div>
                        <input type="text" class="search-input" id="classroomName" placeholder="请输入教室名称">
                    </div>
                    <div class="search-item">
                        <div class="search-label">教学楼</div>
                        <select class="search-input" id="building">
                            <option value="">请选择教学楼</option>
                        </select>
                    </div>
                </div>

                <div class="search-row">
                    <div class="search-item">
                        <div class="search-label">教室类型</div>
                        <select class="search-input" id="classroomType">
                            <option value="">请选择教室类型</option>
                            <option value="normal">普通教室</option>
                            <option value="multimedia">多媒体教室</option>
                            <option value="lab">实验室</option>
                            <option value="computer">机房</option>
                            <option value="language">语音室</option>
                        </select>
                    </div>
                    <div class="search-item">
                        <div class="search-label">容量</div>
                        <select class="search-input" id="capacity">
                            <option value="">不限容量</option>
                            <option value="small">小型(≤50人)</option>
                            <option value="medium">中型(51-100人)</option>
                            <option value="large">大型(101-200人)</option>
                            <option value="xlarge">超大型(>200人)</option>
                        </select>
                    </div>
                </div>

                <div class="search-actions">
                    <button class="btn-mobile btn-search flex-1" onclick="searchClassroom();">
                        <i class="ace-icon fa fa-search"></i>
                        <span>搜索</span>
                    </button>
                    <button class="btn-mobile btn-reset flex-1" onclick="resetSearch();">
                        <i class="ace-icon fa fa-refresh"></i>
                        <span>重置</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 教室信息 -->
        <div class="classroom-info" id="classroomInfo">
            <div class="info-title">
                <i class="ace-icon fa fa-home"></i>
                <span>教室信息</span>
            </div>

            <div class="classroom-card" id="classroomCard">
                <!-- 教室信息将动态填充 -->
            </div>
        </div>

        <!-- 使用情况容器 -->
        <div class="usage-container" id="usageContainer">
            <div class="usage-header-bar">
                <div class="usage-title">
                    <i class="ace-icon fa fa-calendar"></i>
                    <span>使用情况</span>
                </div>

                <div class="date-selector">
                    <button class="date-nav" onclick="changeDate(-1);">
                        <i class="ace-icon fa fa-chevron-left"></i>
                    </button>
                    <div class="date-info" id="dateInfo">今天</div>
                    <button class="date-nav" onclick="changeDate(1);">
                        <i class="ace-icon fa fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="usage-content" id="usageContent">
                <!-- 使用情况内容将动态填充 -->
            </div>
        </div>

        <!-- 图例说明 -->
        <div class="usage-legend" id="usageLegend">
            <div class="legend-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>状态说明</span>
            </div>

            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color free"></div>
                    <span>空闲</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color occupied"></div>
                    <span>占用</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color maintenance"></div>
                    <span>维护</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color reserved"></div>
                    <span>预约</span>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-search"></i>
            <div id="emptyMessage">请输入搜索条件查询教室使用情况</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 使用详情模态框 -->
    <div class="usage-detail-modal" id="usageDetailModal">
        <div class="usage-detail-content">
            <div class="usage-detail-header">
                <div class="usage-detail-title" id="usageDetailTitle">使用详情</div>
                <div class="usage-detail-close" onclick="closeUsageDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="usage-detail-body" id="usageDetailBody">
                <!-- 使用详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentClassroom = null;
        let currentDate = new Date();
        let usageData = {};
        let buildings = [];

        $(function() {
            initPage();
            loadBuildings();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            updateDateInfo();
        }

        // 更新日期信息
        function updateDateInfo() {
            const today = new Date();
            const diffTime = currentDate.getTime() - today.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            let dateText = '';
            if (diffDays === 0) {
                dateText = '今天';
            } else if (diffDays === 1) {
                dateText = '明天';
            } else if (diffDays === -1) {
                dateText = '昨天';
            } else {
                dateText = formatDate(currentDate);
            }

            $('#dateInfo').text(dateText);
        }

        // 格式化日期
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // 加载教学楼列表
        function loadBuildings() {
            $.ajax({
                url: "/student/query/classroomUsage/getBuildings",
                type: "post",
                dataType: "json",
                success: function(data) {
                    buildings = data.buildings || [];
                    renderBuildingOptions();
                },
                error: function() {
                    console.log('加载教学楼列表失败');
                }
            });
        }

        // 渲染教学楼选项
        function renderBuildingOptions() {
            const select = $('#building');
            select.find('option:not(:first)').remove();

            buildings.forEach(building => {
                select.append(`<option value="${building.id}">${building.name}</option>`);
            });
        }

        // 搜索教室
        function searchClassroom() {
            const classroomName = $('#classroomName').val().trim();
            const building = $('#building').val();
            const classroomType = $('#classroomType').val();
            const capacity = $('#capacity').val();

            if (!classroomName && !building) {
                showError('请输入教室名称或选择教学楼');
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/query/classroomUsage/searchClassroom",
                type: "post",
                data: {
                    classroomName: classroomName,
                    building: building,
                    classroomType: classroomType,
                    capacity: capacity
                },
                dataType: "json",
                success: function(data) {
                    if (data.success && data.classroom) {
                        currentClassroom = data.classroom;
                        showClassroomInfo(data.classroom);
                        loadClassroomUsage(data.classroom.id, formatDate(currentDate));
                    } else {
                        showError(data.message || '未找到匹配的教室');
                        hideClassroomInfo();
                        hideUsage();
                    }
                    showLoading(false);
                },
                error: function() {
                    showError('搜索失败，请重试');
                    showLoading(false);
                }
            });
        }

        // 显示教室信息
        function showClassroomInfo(classroom) {
            const classroomHtml = `
                <div class="classroom-icon">
                    <i class="ace-icon fa fa-home"></i>
                </div>
                <div class="classroom-details">
                    <div class="classroom-name">${classroom.name}</div>
                    <div class="classroom-meta">
                        教学楼：${classroom.buildingName}<br>
                        楼层：${classroom.floor}楼<br>
                        类型：${getClassroomTypeText(classroom.type)}<br>
                        容量：${classroom.capacity}人<br>
                        设备：${classroom.equipment || '基础设备'}
                    </div>
                </div>
            `;

            $('#classroomCard').html(classroomHtml);
            $('#classroomInfo').addClass('show');
            hideEmptyState();
        }

        // 获取教室类型文本
        function getClassroomTypeText(type) {
            switch(type) {
                case 'normal': return '普通教室';
                case 'multimedia': return '多媒体教室';
                case 'lab': return '实验室';
                case 'computer': return '机房';
                case 'language': return '语音室';
                default: return '普通教室';
            }
        }

        // 隐藏教室信息
        function hideClassroomInfo() {
            $('#classroomInfo').removeClass('show');
        }

        // 加载教室使用情况
        function loadClassroomUsage(classroomId, date) {
            $.ajax({
                url: "/student/query/classroomUsage/getClassroomUsage",
                type: "post",
                data: {
                    classroomId: classroomId,
                    date: date
                },
                dataType: "json",
                success: function(data) {
                    usageData = data.usage || {};
                    renderUsage();
                    showUsage();
                    showLegend();
                },
                error: function() {
                    showError('加载使用情况失败');
                    hideUsage();
                    hideLegend();
                }
            });
        }

        // 渲染使用情况
        function renderUsage() {
            if (!usageData || Object.keys(usageData).length === 0) {
                $('#usageContent').html(`
                    <div class="no-usage">
                        <i class="ace-icon fa fa-calendar-o"></i>
                        <div>该教室当天暂无使用安排</div>
                    </div>
                `);
                return;
            }

            const timeSlots = [
                { id: 1, name: '第1-2节', time: '08:00-09:40' },
                { id: 2, name: '第3-4节', time: '10:00-11:40' },
                { id: 3, name: '第5-6节', time: '14:00-15:40' },
                { id: 4, name: '第7-8节', time: '16:00-17:40' },
                { id: 5, name: '第9-10节', time: '19:00-20:40' }
            ];

            let tableHtml = '<table class="usage-table">';

            // 表头
            tableHtml += '<tr>';
            tableHtml += '<th class="time-header">时间段</th>';
            tableHtml += '<th class="status-header">使用状态</th>';
            tableHtml += '</tr>';

            // 表格内容
            timeSlots.forEach(slot => {
                const usage = usageData[slot.id] || { status: 'free' };
                const statusClass = getStatusClass(usage.status);
                const statusText = getStatusText(usage.status);

                tableHtml += '<tr>';
                tableHtml += `<td class="time-cell">${slot.name}<br>${slot.time}</td>`;
                tableHtml += `<td class="status-cell ${statusClass}" onclick="showUsageDetail(${slot.id})">`;

                if (usage.status === 'occupied' && usage.course) {
                    tableHtml += `
                        <div class="usage-detail">
                            <div class="course-name">${usage.course.name}</div>
                            <div class="course-teacher">${usage.course.teacher}</div>
                        </div>
                    `;
                } else {
                    tableHtml += `<div class="usage-detail">${statusText}</div>`;
                }

                tableHtml += '</td>';
                tableHtml += '</tr>';
            });

            tableHtml += '</table>';
            $('#usageContent').html(tableHtml);
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'free': return 'status-free';
                case 'occupied': return 'status-occupied';
                case 'maintenance': return 'status-maintenance';
                case 'reserved': return 'status-reserved';
                default: return 'status-free';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'free': return '空闲';
                case 'occupied': return '占用';
                case 'maintenance': return '维护中';
                case 'reserved': return '已预约';
                default: return '空闲';
            }
        }

        // 显示使用详情
        function showUsageDetail(timeSlotId) {
            const usage = usageData[timeSlotId];

            if (!usage || usage.status === 'free') {
                showError('该时段教室空闲');
                return;
            }

            let detailHtml = `
                <div class="detail-item">
                    <span class="detail-label">状态:</span>
                    <span class="detail-value">${getStatusText(usage.status)}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">时间:</span>
                    <span class="detail-value">${usage.timeRange || '未知'}</span>
                </div>
            `;

            if (usage.status === 'occupied' && usage.course) {
                detailHtml += `
                    <div class="detail-item">
                        <span class="detail-label">课程名称:</span>
                        <span class="detail-value">${usage.course.name}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">课程代码:</span>
                        <span class="detail-value">${usage.course.code}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">任课教师:</span>
                        <span class="detail-value">${usage.course.teacher}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">上课班级:</span>
                        <span class="detail-value">${usage.course.className}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">学生人数:</span>
                        <span class="detail-value">${usage.course.studentCount || '未知'}人</span>
                    </div>
                `;
            } else if (usage.status === 'maintenance') {
                detailHtml += `
                    <div class="detail-item">
                        <span class="detail-label">维护类型:</span>
                        <span class="detail-value">${usage.maintenanceType || '设备维护'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">维护人员:</span>
                        <span class="detail-value">${usage.maintenanceStaff || '未知'}</span>
                    </div>
                `;
            } else if (usage.status === 'reserved') {
                detailHtml += `
                    <div class="detail-item">
                        <span class="detail-label">预约人:</span>
                        <span class="detail-value">${usage.reservedBy || '未知'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">预约用途:</span>
                        <span class="detail-value">${usage.reservedPurpose || '未知'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">联系方式:</span>
                        <span class="detail-value">${usage.reservedContact || '未公开'}</span>
                    </div>
                `;
            }

            if (usage.note) {
                detailHtml += `
                    <div class="detail-item">
                        <span class="detail-label">备注:</span>
                        <span class="detail-value">${usage.note}</span>
                    </div>
                `;
            }

            $('#usageDetailTitle').text('使用详情');
            $('#usageDetailBody').html(detailHtml);
            $('#usageDetailModal').addClass('show');
        }

        // 关闭使用详情
        function closeUsageDetail() {
            $('#usageDetailModal').removeClass('show');
        }

        // 切换日期
        function changeDate(direction) {
            currentDate.setDate(currentDate.getDate() + direction);
            updateDateInfo();

            if (currentClassroom) {
                loadClassroomUsage(currentClassroom.id, formatDate(currentDate));
            }
        }

        // 重置搜索
        function resetSearch() {
            $('#classroomName').val('');
            $('#building').val('');
            $('#classroomType').val('');
            $('#capacity').val('');

            currentClassroom = null;
            usageData = {};
            currentDate = new Date();
            updateDateInfo();

            hideClassroomInfo();
            hideUsage();
            hideLegend();
            showEmptyState('请输入搜索条件查询教室使用情况');
        }

        // 显示使用情况
        function showUsage() {
            $('#usageContainer').addClass('show');
        }

        // 隐藏使用情况
        function hideUsage() {
            $('#usageContainer').removeClass('show');
        }

        // 显示图例
        function showLegend() {
            $('#usageLegend').addClass('show');
        }

        // 隐藏图例
        function hideLegend() {
            $('#usageLegend').removeClass('show');
        }

        // 刷新数据
        function refreshData() {
            if (currentClassroom) {
                loadClassroomUsage(currentClassroom.id, formatDate(currentDate));
            }
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            hideClassroomInfo();
            hideUsage();
            hideLegend();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#usageDetailModal').click(function(e) {
            if (e.target === this) {
                closeUsageDetail();
            }
        });

        // 初始显示空状态
        showEmptyState('请输入搜索条件查询教室使用情况');
    </script>
</body>
</html>
