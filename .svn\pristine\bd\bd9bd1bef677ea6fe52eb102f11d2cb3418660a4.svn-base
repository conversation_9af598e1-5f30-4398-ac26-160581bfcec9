<?xml version="1.0" encoding="UTF-8"?>
<decorators defaultdir="/WEB-INF/template/">
    <excludes>
		<pattern>/wx/*</pattern>
        <pattern>/druid/*</pattern>

        <pattern>/*/*/decoration</pattern>
        <pattern>/WEB-INF/css/*</pattern>
        <pattern>/WEB-INF/js/*</pattern>
        <pattern>/WEB-INF/img/*</pattern>
        <pattern>/WEB-INF/assets/*</pattern>
        <pattern>/WEB-INF/ueditor/*</pattern>
        <pattern>/ueditor/*</pattern>
        <pattern>/kindeditor/*</pattern>
        <pattern>/WEB-INF/kindeditor/*</pattern>
        <pattern>/reportServlet*</pattern>
        <pattern>/WEB-INF/pdf/web/**</pattern>

        <pattern>/personalManagement/paperSubmit/upload/*</pattern>
        <pattern>/student/personalManagement/showFile/*</pattern>
        <pattern>/student/overview/attention/myAttention/*/showInfo</pattern>
        <pattern>/student/teachingEvaluation/teachingEvaluation/evaluation</pattern>
        <pattern>/student/examinationManagement/examSignUp/show*</pattern>
        <!--考试报名核对信息-->
        <pattern>/student/examinationManagement/exam/registration/check*</pattern>
        <pattern>/student/teachingResources/freeClassroomQuery/**/chooseWS</pattern>
        <pattern>/student/rollManagement/myRollCard/myInfoEdit</pattern>
        <pattern>/student/rollManagement/rollChanges/modal</pattern>
        <pattern>/student/rollManagement/rewardsAndPenalties/modal</pattern>
        <pattern>/student/rollManagement/project/*/*/detail</pattern>
        <pattern>/student/rollManagement/minorProgramRegistration/courseProjectApply/*</pattern>
        <pattern>/student/rollManagement/minorProgramRegistration/look/*</pattern>
        <pattern>/student/rollManagement/minorProgramRegistration/lookKc*</pattern>
        <pattern>/student/rollManagement/minorProgramRegistration/regNotice</pattern>
        <pattern>/student/personalManagement/showFile/*</pattern>
        <pattern>/student/rollManagement/personalInfoUpdate/updatePassword*</pattern>
        <pattern>/student/personalManagement/studentChange/specialtiesApply/updateSpecialties*</pattern>
        <pattern>/student/personalManagement/studentChange/chose*</pattern>
        <pattern>/student/personalManagement/studentChange/show*</pattern>
        <pattern>/student/practicing/independentPractice/addInfo</pattern>
        <pattern>/student/practicing/independentPractice/editInfo*</pattern>
        <!--课程替代-->
        <pattern>/student/personalManagement/personalApplication/curriculumReplacement/index/addCurriculum/*</pattern>
        <pattern>/student/personalManagement/personalApplication/curriculumReplacement/addSqyy*</pattern>

        <!--创新学分-->
        <pattern>/student/innovationCredits/creditsRecognitionApply/openUpload*</pattern>
        <pattern>/student/innovationCredits/creditsRecognitionApply/showSpjl*</pattern>
        <pattern>/student/innovationCredits/innovationProject/openUpload*</pattern>
        <pattern>/student/innovationCredits/creditCourseReplace/add*</pattern>
        <pattern>/student/innovationCredits/creditCourseReplace/view*</pattern>
        
        <!--四川大学 推免生-->
        <pattern>/student/exemptsExam/ExemptExamController/addSq*</pattern>
        <pattern>/student/exemptsExam/ExemptExamController/look*</pattern>
        <pattern>/student/exemptsExam/ExemptExamController/openUpload*</pattern>
        
        <pattern>/student/practicing/practice/showApply*</pattern>
        <pattern>/student/practicing/independentPractice/showApply*</pattern>

        <pattern>/student/integratedQuery/scoreQuery/subitemScores/fxcjIndex*</pattern>
        <pattern>/student/integratedQuery/scoreQuery/subitemScores/mxcjIndex*</pattern>
        <pattern>/student/integratedQuery/scoreQuery/experimentScores/fxcjIndex*</pattern>
        <pattern>/student/integratedQuery/scoreQuery/experimentScores/mxcjIndex*</pattern>
        
        <pattern>/student/courseSelect/specialCourse/toSaveCourse*</pattern>
        <pattern>/student/courseSelect/currentWeeklyCourse/toSaveCourse*</pattern>
        <!-- 石油大学V1.8 密码修改，信息采集-->
        <pattern>/student/rollManagement/personalInfoUpdate/informationCollection</pattern>
        <pattern>/student/rollManagement/informationCollection/qztx*</pattern>
        <pattern>/template/forcereset*</pattern>

        <!--个人申请查看信息-->
        <pattern>/student/application/index/seeInfo*</pattern>
        <!-- 石油大学V1.8  学生证信息维护 -->
        <pattern>/student/personalManagement/studentIdCard/add</pattern>
        <pattern>/student/personalManagement/studentIdCard/edit*</pattern>

        <pattern>/main/queryRestSchedule*</pattern>

        <!--成绩认定申请查看数据-->
        <pattern>/student/personalManagement/achievementDetermination/index/addCourseList</pattern>

        <!--农大转专业独立版本-->
        <pattern>/student/personalManagement/transfer/major/chose*</pattern>
        <pattern>/student/personalManagement/transfer/major/print/confirm/*</pattern>

        <!-- 劳动教育-->
        <pattern>/student/laborEducation/eventRegistration/view*</pattern>
        <pattern>/student/laborEducation/eventRegistration/add*</pattern>
        
        
        <!-- 衡水学院，劳动教育-->
        <pattern>/student/laborEducation/laborEducationApply/addApply*</pattern>
        <pattern>/student/laborEducation/laborEducationApply/editApply*</pattern>


        <!-- 论文管理 -->
        <pattern>/student/personalManagement/processManagement/update*</pattern>
        <pattern>/student/personalManagement/processManagement/writingPlan/history*</pattern>
        <pattern>/student/personalManagement/processManagement/paperDraftApply/addInfo*</pattern>
        <pattern>/student/personalManagement/processManagement/paperDraftApply/editInfo*</pattern>
        <pattern>/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/toAdd*</pattern>
        <pattern>/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/toEdit*</pattern>
        <pattern>/student/personalManagement/processManagement/submissionOfMaterialsOverdueMgmt/selectApprover*</pattern>
       
        <!--顺序志愿-转专业申请（川大）-->
        <pattern>/student/ordertransfer/specialty/application/index/chose</pattern>
        
        <pattern>/student/courseSelect/calendarSemesterCurriculum/canlendar_show*</pattern>
        <pattern>/student/courseSelect/calendarSemesterCurriculum/syllabus_show*</pattern>

		<pattern>/prove/*</pattern>
        <pattern>/student/studentCertificatePrinting/index/prove/*</pattern>
        <!--专业分方向申请-->
        <pattern>/student/personalManagement/majorsSplit/index/chose*</pattern>
        <pattern>/student/personalManagement/majorsSplitOther/index/chose*</pattern>
        
        <!--退课申请-->
        <pattern>/student/personalManagement/individualApplication/dropCourseApplication/addCurriculum*</pattern>
        <!--补选课申请-->
        <pattern>/student/personalManagement/individualApplication/byElectionApplication/addCurriculum</pattern>
        <!--免听申请-->
        <pattern>/student/personalManagement/individualApplication/listenFreeApplication/addCurriculum</pattern>
        <!--免修申请-->
        <pattern>/student/personalManagement/individualApplication/exemptionApplication/addCurriculum</pattern>
        <!--退伍复学免考课程-->
        <pattern>/student/personalManagement/individualApplication/joinTheArmyApplication/index/addCurriculum</pattern>
        <!--缓考申请-->
        <pattern>/student/exam/ApplicationDelayedExam/doAdd/*/*</pattern>
        <pattern>/student/exam/ApplicationDelayedExam/doUpdate/*/*</pattern>
        <!--竞赛免考申请-->
        <pattern>/student/personalManagement/individualApplication/competitionExemApplication/index/changeTeacher</pattern>
        <pattern>/student/personalManagement/individualApplication/competitionExemApplication/index/addCurriculum</pattern>
        <!--课程换班申请-->
        <pattern>/student/personalManagement/individualApplication/changeCourseClassApplication/addCurriculum*</pattern>
        
        <!-- 书院 -->
        <pattern>/students/personalManagement/applyTeacher/addApplyInfo*</pattern>
        <pattern>/students/personalManagement/applyTeacher/seeApply*</pattern>
        <!--学籍异动查看-->
        <pattern>/student/personalManagement/studentChange/view*</pattern>
        <pattern>/student/personalManagement/studentChange/addCurriculum*</pattern>
        <pattern>/student/scoreSearch/coursesGiveUp/addInfo</pattern>
        <pattern>/student/personalManagement/studentChange/selectApprover*</pattern>
        <pattern>/student/personalManagement/individualApplication/routineWork/busSection/index/selectApprover*</pattern>
        <pattern>/student/application/dropCourseApplication/index/selectApprover*</pattern>
        <pattern>/student/personalManagement/individualApplication/competitionExemApplication/index/selectApprover*</pattern>
   		<pattern>/student/personalManagement/studentChange/index/applyPrintTys*</pattern>
   		
   		<!--大类分流申请-->
        <pattern>/student/personalManagement/largeClassDiversion/index/chose*</pattern>
   		
   		<!--实习-->
        <pattern>/student/internship/daily/add*</pattern>
        <pattern>/student/internship/daily/view*</pattern>
   		
   		<!--课组认定申请-->
        <pattern>/student/personalManagement/individualApplication/classIdentification/editInfo*</pattern>
        <pattern>/student/personalManagement/individualApplication/classIdentification/index/showPyfa*</pattern>
   		
   		<!--学分认证-->
        <pattern>/student/personalManagement/individualApplication/creditCertification/selectKc*</pattern>
        <pattern>/student/personalManagement/individualApplication/creditCertification/showPyfa*</pattern>
        <pattern>/student/personalManagement/individualApplication/creditCertification/selectCourse*</pattern>
        <pattern>/student/personalManagement/individualApplication/creditCertification/selectApprover*</pattern>
   		
   		<!--重修选课-->
        <pattern>/student/personalManagement/individualApplication/rebuildCourseSelection/addApply*</pattern>
        <pattern>/student/personalManagement/individualApplication/rebuildCourseSelection/selectCourse*</pattern>
        <pattern>/student/personalManagement/individualApplication/rebuildCourseSelection/print*</pattern>
        <pattern>/student/personalManagement/individualApplication/rebuildCourseSelection/fjIndex*</pattern>
        
         <!-- 烟台大学结业生返校重修 -->
         <pattern>/student/jysfxcx/cxbm/planCompletion</pattern>
        <pattern>/student/jysfxcx/cxbm/bxqkkView*</pattern>
        <pattern>/student/jysfxcx/cxbmMore/bxqkkView*</pattern>
        <pattern>/student/graduatesManagement/graduatesExamManagement/graduatesApply/addApply*</pattern>
        <pattern>/student/graduatesManagement/graduatesCourse/kxkc*</pattern>
        <pattern>/student/graduatesManagement/graduatesExamManagement/graduatesApply/modifyByKs*</pattern>
        <pattern>/student/graduatesManagement/graduatesExamManagement/billUpload/doUpload</pattern>

        <pattern>/student/application/scoreCheck/addApply*</pattern>
        <pattern>/student/application/scoreCheck/selectScore/page*</pattern>
        <pattern>/student/application/scoreCheck/seeApply*</pattern>
        <pattern>/student/application/scoreRecheck/addApply*</pattern>
        <pattern>/student/application/scoreRecheck/seeApply*</pattern>
        <pattern>/student/application/scoreCheck/selectApprover*</pattern>
        <pattern>/student/application/scoreRecheck/selectApprover*</pattern>
        
        <pattern>/student/examinationManagement/exam/registration/toApply*</pattern>

        <pattern>/student/application/achievementRecognition/index/selectApprover*</pattern>
        <pattern>/student/personalManagement/individualApplication/achievementRecognition/index/addCurriculum*</pattern>
        <pattern>/student/personalManagement/individualApplication/reviewScoreReduction/index/addCurriculum</pattern>
        
        <!-- 天津工业V1.8  选课 -->
        <pattern>/student/courseSelect/kzwcmxck/kzwcmxckBody*</pattern>
        <pattern>/student/courseSelect/kzwcmxck/kcwcmxckBody*</pattern>
        
        <pattern>/student/personalManagement/processManagement/selectApprover*</pattern>
        <pattern>/student/personalManagement/projectSelect/index/*/*/view</pattern>
        
        <pattern>/student/personalManagement/individualApplication/changepapertitle/addApply*</pattern>
        <pattern>/student/personalManagement/individualApplication/changepapertitle/seeApply*</pattern>
        <pattern>/student/personalManagement/individualApplication/changepapertitle/selectApprover*</pattern>
        <pattern>/student/personalManagement/pushexemptionmgt/destinationApply/index/addApply*</pattern>
        <pattern>/student/personalManagement/pushexemptionmgt/destinationApply/index/selectApprover*</pattern>
        <pattern>/student/personalManagement/thesisGuidanceProcess/index/seeGuidance*</pattern>
        <pattern>/personalManagement/paperSubmit/download/*</pattern>
        <pattern>/student/application/byElectionApplication/index/selectApprover*</pattern>
        <pattern>/student/personalManagement/individualApplication/scholarshipApplication/index/edit/openToEdit*</pattern>
        <pattern>/student/personalManagement/individualApplication/baccalaureateapplication/get/add*</pattern>
        <pattern>/student/personalManagement/individualApplication/baccalaureateapplication/get/approver*</pattern>
        <pattern>/student/personalManagement/individualApplication/baccalaureateapplication/get/show*</pattern>
        
        <!-- 大创 -->
        <pattern>/students/studentsInnovation/apply/beforeBudget*</pattern>
        <pattern>/student/students/innovation/application/management/projectTitleChange/apply/openEditPage*</pattern>
        <pattern>/student/students/innovation/application/management/projectTitleChange/apply/openViewPage*</pattern>
        <pattern>/student/students/innovation/application/management/projectTitleChange/apply/openHistoricalPage*</pattern>
        <pattern>/student/certification/exam/online/registration/edit/openToEnroll*</pattern>
        <pattern>/students/studentsInnovation/terminationInnovationApplication/toEdit*</pattern>
        <pattern>/students/studentsInnovation/terminationInnovationApplication/xmsqIndex*</pattern>
        <pattern>/students/studentsInnovation/extensionInnovation/toEdit*</pattern>
        <pattern>/students/studentsInnovation/extensionInnovation/xmsqIndex*</pattern>
        <pattern>/students/studentsInnovation/apply/openUpload*</pattern>
        <pattern>/students/studentsInnovation/apply/openJscgUpload*</pattern>
        
        <pattern>/student/personalManagement/individualApplication/courseGroupReplace/addApply*</pattern>
        <pattern>/student/personalManagement/individualApplication/courseGroupReplace/seeApply*</pattern>
        <pattern>/student/personalManagement/individualApplication/courseGroupReplace/selectApprover*</pattern>
        <pattern>/student/personalManagement/individualApplication/courseGroupReplace/selectScore/page*</pattern>
        <pattern>/phone/student/personalManagement/minorProgramRegistration/queryFas*</pattern>
        <pattern>/student/personalManagement/individualApplication/changeStudentInfo/index/queryApprover*</pattern>
        <pattern>/student/personalManagement/projectSelect/index/openToSelectionPage*</pattern>
        <pattern>/student/personalManagement/individualApplication/joinTheArmyApplication/index/selectApprover*</pattern>
        <pattern>/student/subjectCompetition/to/tjhjxs*</pattern>
        <pattern>/student/subjectCompetition/to/tjzdjs*</pattern>
        <pattern>/student/subjectCompetition/views*</pattern>
        <pattern>/students/studentsInnovation/apply/changeTeacher*</pattern>
        <pattern>/students/studentsInnovation/apply/changeStudent*</pattern>
        <pattern>/students/studentsInnovation/apply/changingTeamMembers*</pattern>
        <pattern>/students/studentsInnovation/apply/changingTeamMembers/openToAddStudent*</pattern>
        <pattern>/student/personalManagement/thesis/guidanceRecord/edit/openToWriteGuidance*</pattern>
        <pattern>/student/personalManagement/thesis/guidanceRecord/edit/openToViewGuidance*</pattern>
        <pattern>/student/personalManagement/processManagement/recordOfTheDefence/addApply*</pattern>
        <pattern>/student/creditTuition/searchPayment/index*</pattern>
        <pattern>/student/personalManagement/individualApplication/microspecialtyMgt/microprofessional/registration/regNotice*</pattern>
        <pattern>/student/personalManagement/individualApplication/microspecialtyMgt/microprofessional/selectApprover*</pattern>
        <pattern>/student/personalManagement/paperPublicationManagement/add*</pattern>
        <pattern>/student/personalManagement/paperPublicationManagement/selectApprover*</pattern>
        <pattern>/student/thesis/stageddocuments/uploadsub/main/get/update*</pattern>
        <pattern>/student/personalManagement/individual/application/project/apply/toadd*</pattern>
        <pattern>/student/personalManagement/individual/application/project/apply/changeTeacher*</pattern>
        <pattern>/student/personalManagement/individual/application/project/apply/selectApprover*</pattern>
        <pattern>/student/thesis/stageddocuments/uploadsub/show/get/index*</pattern>
        <pattern>/student/courseSelect/courseSelectNotice/index*</pattern>
        <pattern>/student/courseSelect/courseSelect/index*</pattern>
        <pattern>/student/courseSelect/selectCourse/index*</pattern>
        <pattern>/student/courseSelect/gotoSelect/index*</pattern>
        <pattern>/student/courseSelect/*/waitingfor*</pattern>
        <pattern>/student/courseSelect/planCourse*</pattern>
        <pattern>/student/courseSelect/quitCourse/index*</pattern>
        <pattern>/student/courseSelect/freeCourse*</pattern>
        <pattern>/student/courseSelect/schoolCourse*</pattern>
        <pattern>/student/courseSelect/departCourse*</pattern>
        <pattern>/student/courseSelect/relearnCourse*</pattern>
        <pattern>/student/courseSelect/intentCourse*</pattern>
    </excludes>
    <decorator name="login" page="/WEB-INF/jsp/system/admin/auth/login.jsp">
        <pattern>/login</pattern>
    </decorator>
    <decorator name="casLogin" page="/WEB-INF/jsp/system/admin/auth/casLogin.jsp">
        <pattern>/casLogin</pattern>
    </decorator>
    <decorator name="gotoLogin" page="/WEB-INF/jsp/system/admin/auth/zgsyLogin.jsp">
        <pattern>/gotoLogin</pattern>
    </decorator>
    <decorator name="officeSigin" page="/WEB-INF/jsp/system/admin/auth/officeSigin.jsp">
        <pattern>/officeSigin</pattern>
    </decorator>
    <decorator name="iframe" page="iframetemplate.jsp">
        <pattern>/student/integratedQuery/course/courseSchdule/*/detail</pattern>
        <pattern>/student/rollManagement/minorProgramRegistration/projectView/*</pattern>
        <pattern>/indexCalendar</pattern>
        <pattern>/student/rollManagement/minorProgramRegistration/applyView*</pattern>
        <pattern>/forgetPassword**</pattern>
        <pattern>/pdf/web/**</pattern>
        <pattern>/forcereset</pattern>
        <pattern>**/mobile/**</pattern>
        
        <pattern>/student/examinationManagement/notPayCost/selectCourse/index</pattern>
        <pattern>/student/examinationManagement/notPayCost/gradeExamination/index</pattern>
    </decorator>

    <decorator name="decoratormain" page="decoratormain.jsp">
        <pattern>/index</pattern>
        <pattern>/index.jsp</pattern>
    </decorator>

    <decorator name="xkiframe" page="xkiframe.jsp">
        <pattern>/student/courseSelect/planRecommendCourse*</pattern>
        <pattern>/student/courseSelect/reViewCourse*</pattern>
        <pattern>/student/courseSelect/stopCourse*</pattern>
        <pattern>/student/teachingEvaluation/industrial/messageInteraction/toEdit*</pattern>
        <pattern>/student/courseSelectManagement/tsxk/currentWeeklyCourse/*</pattern>
        <pattern>/student/courseSelectManagement/tsxk/specialCourse/index*</pattern>
        <pattern>/student/courseSelectManagement/tsxk/currentWeeklyDropCourse/*</pattern>
    </decorator>

    <decorator name="default" page="template.jsp">
        <pattern>/*</pattern>
    </decorator>
    
    <decorator name="mainP" page="main.jsp">
    </decorator>

</decorators>