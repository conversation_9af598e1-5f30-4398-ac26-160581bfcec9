<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>课程属性成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 课程属性成绩页面样式 */
        .property-header {
            background: linear-gradient(135deg, var(--info-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .property-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .property-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .tab-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tab-header {
            display: flex;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .tab-header::-webkit-scrollbar {
            display: none;
        }
        
        .tab-item {
            flex: 0 0 auto;
            padding: var(--padding-md);
            text-align: center;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
            border-bottom: 2px solid transparent;
            white-space: nowrap;
            min-width: 100px;
        }
        
        .tab-item.active {
            color: var(--info-color);
            border-bottom-color: var(--info-color);
            background: var(--bg-primary);
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .property-stats {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stats-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .stats-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--info-dark);
        }
        
        .stats-label {
            font-size: var(--font-size-small);
            color: var(--info-dark);
        }
        
        .score-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .score-item {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            border-left: 4px solid var(--info-color);
        }
        
        .score-item.failed {
            border-left-color: var(--error-color);
        }
        
        .score-header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-sm);
        }
        
        .score-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .score-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .score-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .score-grade {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 50px;
            height: 30px;
            border-radius: 15px;
            font-size: var(--font-size-base);
            font-weight: 500;
            flex-shrink: 0;
            cursor: pointer;
        }
        
        .grade-excellent {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .grade-good {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .grade-pass {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .grade-fail {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .grade-not-evaluated {
            background: var(--text-disabled);
            color: white;
        }
        
        .score-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .meta-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .meta-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .reason-tag {
            background: var(--warning-light);
            color: var(--warning-dark);
            border-radius: 12px;
            padding: 4px 8px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            display: inline-block;
            margin-top: var(--margin-sm);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        @media (max-width: 480px) {
            .score-header-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .score-meta {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .property-stats {
                flex-direction: column;
                gap: var(--spacing-md);
            }
        }
    </style>
</head>
<body>
    <select id="bfcj_complete" style="display: none;">
        <cache:query var="codeXfjbList" region="CODE_XFJB"/>
        <c:forEach items="${codeXfjbList}" var="obj">
            <option value="${obj.djcj}" xxf="${obj.xxf}" sxf="${obj.sxf}" djcj="${obj.djcj}" jds="${obj.jds}">${obj.djcj}</option>
        </c:forEach>
    </select>
    <select id="djcj_complete" style="display: none;">
        <cache:query var="codeCjdjList" region="CODE_CJDJ"/>
        <c:forEach items="${codeCjdjList}" var="codeCjdj">
            <option value="${codeCjdj.djcj}">${codeCjdj.djm}</option>
        </c:forEach>
    </select>
    <input type="hidden" id="param" name="param">
    <input type="hidden" id="schoolName" name="schoolName">
    <input type="hidden" id="showScoreDetail" name="showScoreDetail" value="${showScoreDetail}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">课程属性成绩</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 课程属性成绩头部 -->
        <div class="property-header">
            <div class="property-title">课程属性成绩</div>
            <div class="property-desc">按课程属性分类查看成绩</div>
        </div>
        
        <!-- 消息显示区域 -->
        <div id="showMessage" style="display: none;"></div>
        
        <!-- 选项卡容器 -->
        <div class="tab-container" id="tabContainer" style="display: none;">
            <div class="tab-header" id="tabHeader">
                <!-- 动态生成选项卡 -->
            </div>
            
            <div class="tab-content" id="tabContent">
                <!-- 动态生成内容 -->
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let scoreData = null;
        let currentTab = 0;
        let param = '';
        let schoolName = '';
        let showScoreDetail = '';

        $(function() {
            initPage();
            loadCoursePropertyScores();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载课程属性成绩
        function loadCoursePropertyScores() {
            showLoading(true);

            const url = "/student/integratedQuery/scoreQuery/${url_check_code}/coursePropertyScores/callback";

            $.get(url, function(data) {
                if (data.result && data.result == "error") {
                    showError("非法请求！");
                    return;
                }

                const lnList = data.lnList;
                if (lnList.length == 0) {
                    showEmptyMessage();
                } else {
                    useScoreExtension(lnList);
                }
            }).fail(function() {
                showError("获取数据失败，请重试");
            }).always(function() {
                showLoading(false);
            });
        }

        // 使用成绩扩展
        function useScoreExtension(lnList) {
            showLoading(true);

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/coursePropertyScores/useScoreExtension",
                type: "post",
                data: "",
                dataType: "json",
                success: function(d) {
                    param = d["param"];
                    schoolName = d["schoolName"];
                    showScoreDetail = $('#showScoreDetail').val();

                    $('#param').val(param);
                    $('#schoolName').val(schoolName);

                    renderScoreData(lnList);
                },
                error: function(xhr) {
                    showError("获取配置失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示空消息
        function showEmptyMessage() {
            $('#showMessage').html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-info-circle"></i>
                    <div class="empty-state-text">暂无成绩信息</div>
                    <div class="empty-state-desc">请联系教务处查询</div>
                </div>
            `).show();
        }

        // 渲染成绩数据
        function renderScoreData(lnList) {
            if (!lnList || lnList.length === 0) return;

            let tabHeaderHtml = '';
            let tabContentHtml = '';

            for (let i = 0; i < lnList.length; i++) {
                const propertyData = lnList[i];
                const isActive = i === 0 ? 'active' : '';
                const tabTitle = propertyData.cjlx == null ? '无课程属性' : propertyData.cjlx;

                // 生成选项卡头部
                tabHeaderHtml += `
                    <div class="tab-item ${isActive}" onclick="switchTab(${i})">
                        ${tabTitle}
                    </div>
                `;

                // 生成选项卡内容
                tabContentHtml += `
                    <div id="tab-${i}" class="tab-pane ${isActive}">
                        ${renderTabContent(propertyData, i)}
                    </div>
                `;
            }

            $('#tabHeader').html(tabHeaderHtml);
            $('#tabContent').html(tabContentHtml);
            $('#tabContainer').show();
            $('#showMessage').show();
        }

        // 渲染选项卡内容
        function renderTabContent(propertyData, index) {
            const title = propertyData.cjlx == null ? '无课程属性' : propertyData.cjlx;

            let html = `
                <!-- 课程属性统计 -->
                <div class="property-stats">
                    <div class="stats-item">
                        <div class="stats-number">${propertyData.zms}</div>
                        <div class="stats-label">已修门数</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-number">${propertyData.yxxf}</div>
                        <div class="stats-label">已修学分</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-number">${propertyData.tgms}</div>
                        <div class="stats-label">通过门数</div>
                    </div>
                </div>

                <!-- 成绩列表 -->
                <div class="score-list">
            `;

            if (propertyData.cjList && propertyData.cjList.length > 0) {
                propertyData.cjList.forEach(function(score, idx) {
                    html += createScoreItem(score, idx);
                });
            } else {
                html += `
                    <div class="empty-state">
                        <i class="ace-icon fa fa-info-circle"></i>
                        <div class="empty-state-text">暂无成绩</div>
                        <div class="empty-state-desc">该课程属性暂无成绩</div>
                    </div>
                `;
            }

            html += '</div>';
            return html;
        }

        // 创建成绩项HTML
        function createScoreItem(score, index) {
            const gradeClass = getGradeClass(score);
            const displayScore = getDisplayScore(score);
            const hasDetail = showScoreDetail != "0" && canShowDetail(score);
            const isFailed = isFailedScore(score);

            return `
                <div class="score-item ${isFailed ? 'failed' : ''}">
                    <div class="score-header-row">
                        <div class="score-info">
                            <div class="score-course">${score.courseName || ''}</div>
                            <div class="score-details">${score.id.courseNumber || ''} | ${score.courseAttributeName || ''}</div>
                        </div>
                        <div class="score-grade ${gradeClass}" ${hasDetail ? `onclick="lookSubitemScore('${score.id.executiveEducationPlanNumber}','${score.id.courseNumber}','${score.id.coureSequenceNumber}','${score.id.startTime}','${score.courseAttributeCode}')"` : ''}>
                            ${displayScore}
                        </div>
                    </div>

                    <div class="score-meta">
                        <div class="meta-item">
                            <div class="meta-label">学分</div>
                            <div class="meta-value">${parseFloat(score.credit) || ''}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">课序号</div>
                            <div class="meta-value">${getSequenceNumber(score)}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">英文课程名</div>
                            <div class="meta-value">${score.englishCourseName || ''}</div>
                        </div>
                        ${schoolName == "100027" ? `
                            <div class="meta-item">
                                <div class="meta-label">选课课组名</div>
                                <div class="meta-value">${score.xkkzm || ''}</div>
                            </div>
                        ` : ''}
                    </div>

                    ${score.notByReasonName ? `
                        <div class="reason-tag">${score.notByReasonName}</div>
                    ` : ''}
                </div>
            `;
        }

        // 获取成绩等级样式
        function getGradeClass(score) {
            if (score.courseScore == "-999.999" || score.cj == -999.999) {
                return 'grade-not-evaluated';
            }

            const scoreValue = parseFloat(score.courseScore);
            if (scoreValue < 60) {
                return 'grade-fail';
            } else if (scoreValue >= 90) {
                return 'grade-excellent';
            } else if (scoreValue >= 80) {
                return 'grade-good';
            } else {
                return 'grade-pass';
            }
        }

        // 获取显示成绩
        function getDisplayScore(score) {
            if (score.courseScore == "-999.999" || score.cj == -999.999) {
                return '未评估';
            }

            if (schoolName == "100010") {
                // 特殊课程显示分数
                const specialCourses = ["58000001", "58000002", "58000003", "58000004", "58000005"];
                if (specialCourses.includes(score.id.courseNumber)) {
                    return score.wclyscj != null && score.wclyscj != "" ? score.courseScore : '';
                } else {
                    return score.gradeName || '';
                }
            } else {
                // 根据录入方式显示
                if (score.scoreEntryModeCode == "001") {
                    return score.wclyscj != null && score.wclyscj != "" ? score.cj : '';
                } else if (score.scoreEntryModeCode == "002") {
                    return score.gradeName || '';
                } else {
                    return score.wclyscj != null && score.wclyscj != "" ? score.cj : '';
                }
            }
        }

        // 获取课序号
        function getSequenceNumber(score) {
            const seqNum = score.id.coureSequenceNumber;
            if (seqNum == null || seqNum == 'NONE') {
                return '';
            }
            return seqNum;
        }

        // 判断是否不及格
        function isFailedScore(score) {
            if (score.courseScore == "-999.999" || score.cj == -999.999) {
                return false;
            }
            return parseFloat(score.courseScore) < 60;
        }

        // 判断是否可以显示详情
        function canShowDetail(score) {
            return true; // 简化判断，实际可根据需要调整
        }

        // 切换选项卡
        function switchTab(tabIndex) {
            // 更新选项卡状态
            $('.tab-item').removeClass('active');
            $(`.tab-item:eq(${tabIndex})`).addClass('active');

            // 更新内容显示
            $('.tab-pane').removeClass('active');
            $(`#tab-${tabIndex}`).addClass('active');

            currentTab = tabIndex;
        }

        // 查看分项成绩
        function lookSubitemScore(zxjxjhh, kch, kxh, kssj, kcsxdm) {
            showLoading(true);

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/subitemScore/look",
                type: "post",
                data: "zxjxjhh=" + zxjxjhh + "&kch=" + kch + "&kxh=" + kxh + "&kssj=" + kssj + "&param=" + param,
                dataType: "json",
                success: function(d) {
                    if (d["scoreDetailList"].length > 0) {
                        if (param == "1") {
                            window.location.href = "/student/integratedQuery/scoreQuery/subitemScore/fxcjIndex/" +
                                zxjxjhh + "/" + kch + "/" + kxh + "/" + kssj + "/" + kcsxdm;
                        } else {
                            window.location.href = "/student/integratedQuery/scoreQuery/subitemScore/mxcjIndex/" +
                                zxjxjhh + "/" + kch + "/" + kxh + "/" + kssj;
                        }
                    } else {
                        if (param == "1") {
                            showError("当前课程暂无分项成绩！");
                        } else {
                            showError("当前课程暂无明细成绩！");
                        }
                    }
                },
                error: function(xhr) {
                    showError("获取成绩详情失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 刷新数据
        function refreshData() {
            loadCoursePropertyScores();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight() || 0;
            const availableHeight = windowHeight - navbarHeight;

            $('.page-mobile').css('min-height', availableHeight + 'px');
        }
    </script>
</body>
</html>
