<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>个人申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 个人申请页面样式 */
        .application-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .application-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .application-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .application-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            padding: var(--padding-md);
        }
        
        .application-item {
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            text-decoration: none;
            color: var(--text-primary);
            transition: all var(--transition-base);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
            min-height: 100px;
            position: relative;
            overflow: hidden;
        }
        
        .application-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: var(--text-primary);
        }
        
        .application-item:active {
            transform: translateY(0);
        }
        
        .application-item.active {
            background: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-dark);
        }
        
        .application-item.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-color);
        }
        
        .application-icon {
            font-size: 32px;
            color: var(--primary-color);
            margin-bottom: var(--margin-sm);
        }
        
        .application-item.active .application-icon {
            color: var(--primary-dark);
        }
        
        .application-name {
            font-size: var(--font-size-small);
            font-weight: 500;
            text-align: center;
            line-height: 1.3;
            word-break: break-all;
        }
        
        .application-status {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
        }
        
        .application-status.inactive {
            background: var(--text-disabled);
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .loading-container i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: var(--margin-md);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-md);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: var(--margin-sm);
        }
        
        .error-container {
            background: var(--error-light);
            border: 1px solid var(--error-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--error-dark);
        }
        
        .error-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .error-title i {
            color: var(--error-color);
        }
        
        .info-tips {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
        }
        
        .tips-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tips-title i {
            color: var(--info-color);
        }
        
        .tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .tips-list li {
            padding: 4px 0;
            font-size: var(--font-size-small);
            position: relative;
            padding-left: 16px;
        }
        
        .tips-list li::before {
            content: "•";
            color: var(--info-color);
            position: absolute;
            left: 0;
        }
        
        @media (max-width: 480px) {
            .application-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--spacing-sm);
                padding: var(--padding-sm);
            }
            
            .application-item {
                padding: var(--padding-md);
                min-height: 80px;
            }
            
            .application-icon {
                font-size: 24px;
            }
            
            .application-name {
                font-size: var(--font-size-mini);
            }
        }
        
        @media (max-width: 360px) {
            .application-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">个人申请</div>
            <div class="navbar-action" onclick="refreshApplications();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 申请头部 -->
        <div class="application-header">
            <div class="application-title">个人申请</div>
            <div class="application-desc">查看和申请各类个人业务</div>
        </div>
        
        <!-- 使用提示 -->
        <div class="info-tips">
            <div class="tips-title">
                <i class="ace-icon fa fa-info-circle"></i>
                申请说明
            </div>
            <ul class="tips-list">
                <li>蓝色图标表示当前可申请的业务</li>
                <li>灰色图标表示暂未开放的业务</li>
                <li>请在规定时间内完成申请</li>
                <li>如有问题请联系相关部门</li>
            </ul>
        </div>
        
        <!-- 申请列表容器 -->
        <div class="card-mobile">
            <div class="card-header">
                <i class="ace-icon fa fa-list"></i>
                <span>可申请业务列表</span>
            </div>
            <div class="card-body" style="padding: 0;">
                <div id="personalApplication">
                    <div class="loading-container">
                        <i class="ace-icon fa fa-spinner fa-spin"></i>
                        <div>正在加载申请列表...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
            showApplication();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 显示申请列表
        function showApplication() {
            $.ajax({
                url: "/student/application/index/showApplication",
                type: "post",
                dataType: "json",
                success: function(data) {
                    renderApplications(data);
                },
                error: function() {
                    showError("开放的可申请业务查询失败！");
                }
            });
        }

        // 渲染申请列表
        function renderApplications(data) {
            var applications = [];
            var todate = data.data["todate"];

            // 异动申请
            if ("${schoolCode}" != "100041") {
                applications.push({
                    name: "异动申请",
                    url: "/student/personalManagement/studentChange/index",
                    icon: "fa-exchange",
                    active: true
                });
            }

            // 转专业申请
            if ("${schoolCode}" != "100052" && "${schoolCode}" != "100041") {
                if (data.data["zzykgcount"] == 1) {
                    var url = "${schoolCode}" != "100014"
                        ? "/student/ordertransfer/specialty/application/index"
                        : "/student/personalManagement/studentChange/specialtiesApply";
                    applications.push({
                        name: "转专业申请",
                        url: url,
                        icon: "fa-share-square-o",
                        active: true
                    });
                }

                // 大类分流
                if (data.data["dlflkgcount"] == 1) {
                    applications.push({
                        name: "大类分流",
                        url: "/student/personalManagement/largeClassDiversion/index",
                        icon: "fa-share-square-o",
                        active: data.data["dlflcount"] > 0
                    });
                }

                // 专业分方向
                if (data.data["zyffxkgcount"] == 1) {
                    applications.push({
                        name: "专业分方向",
                        url: "/student/personalManagement/majorsSplitOther/index",
                        icon: "fa-share-square-o",
                        active: data.data["zyffxcount"] > 0
                    });
                }
            }

            // 奖学金申请
            if ("${schoolCode}" != "100041" && "${schoolCode}" != "100053") {
                applications.push({
                    name: "奖学金申请",
                    url: "/student/personalManagement/individualApplication/scholarshipApplication/index",
                    icon: "fa-share-square-o",
                    active: data.data["jxjcount"] > 0
                });
            }

            // 动态申请列表
            $.each(data.data["list"], function(i, v) {
                if (v.id != "10006" && v.id != "10007" && v.id != "10008" &&
                    v.id != "10012" && v.id != "10041" && v.id != "10042" && v.qyf == "1") {
                    applications.push({
                        name: v.ywsm,
                        url: v.url,
                        icon: v.icon,
                        active: v.kssj <= todate && v.jssj >= todate && v.kg == '1'
                    });
                }

                // 辅修方案注册/注销
                if (v.id == "10008" && v.qyf == "1") {
                    applications.push({
                        name: "辅修方案注册/注销",
                        url: v.url,
                        icon: v.icon,
                        active: v.kssj <= todate && v.jssj >= todate && v.kg == '1'
                    });
                }

                // 微专业方案注册/注销
                if (v.id == "10041" && v.qyf == "1") {
                    applications.push({
                        name: "微专业方案注册/注销",
                        url: v.url,
                        icon: v.icon,
                        active: v.kssj <= todate && v.jssj >= todate && v.kg == '1'
                    });
                }
            });

            // 创新/创业
            if ("${schoolCode}" != "100052" && "${schoolCode}" != "100041" &&
                "${schoolCode}" != "100018" && data.data["xsdkffcount"] > 0) {
                applications.push({
                    name: "创新/创业",
                    url: "/students/studentsInnovation/apply/countindex",
                    icon: "fa-lightbulb-o",
                    active: true
                });
            }

            renderApplicationGrid(applications);
        }

        // 渲染申请网格
        function renderApplicationGrid(applications) {
            if (applications.length === 0) {
                $("#personalApplication").html(`
                    <div class="empty-state">
                        <i class="ace-icon fa fa-inbox"></i>
                        <div class="empty-state-text">暂无可申请的业务</div>
                        <div style="font-size: var(--font-size-small); color: var(--text-disabled);">
                            请联系管理员或稍后再试
                        </div>
                    </div>
                `);
                return;
            }

            var html = '<div class="application-grid">';

            applications.forEach(function(app) {
                var displayName = formatApplicationName(app.name);
                var activeClass = app.active ? 'active' : '';
                var statusClass = app.active ? '' : 'inactive';

                html += `
                    <a href="${app.url}" class="application-item ${activeClass}"
                       onclick="return handleApplicationClick('${app.url}', ${app.active});">
                        <div class="application-status ${statusClass}"></div>
                        <i class="ace-icon ${app.icon} application-icon"></i>
                        <div class="application-name">${displayName}</div>
                    </a>
                `;
            });

            html += '</div>';
            $("#personalApplication").html(html);
        }

        // 格式化申请名称（处理长文本换行）
        function formatApplicationName(name) {
            if (!name) return '';

            var nameLen = name.length;
            if (nameLen <= 6) {
                return name;
            } else if (nameLen > 12) {
                return name.substring(0, 6) + '<br>' + name.substring(6, 11) + '...';
            } else {
                var selfNameLen = parseInt(nameLen / 2);
                selfNameLen = selfNameLen % 2 != 0 ? selfNameLen - 1 : selfNameLen;
                return name.substring(0, selfNameLen) + '<br>' + name.substring(selfNameLen, 12);
            }
        }

        // 处理申请点击事件
        function handleApplicationClick(url, isActive) {
            if (!isActive) {
                showToast('该申请业务暂未开放');
                return false;
            }

            if (parent && parent.addTab) {
                // 在父窗口中打开新标签
                var title = $(event.target).closest('.application-item').find('.application-name').text().replace('<br>', '');
                parent.addTab(title, url);
                return false;
            }

            return true; // 允许正常跳转
        }

        // 刷新申请列表
        function refreshApplications() {
            $("#personalApplication").html(`
                <div class="loading-container">
                    <i class="ace-icon fa fa-spinner fa-spin"></i>
                    <div>正在刷新申请列表...</div>
                </div>
            `);
            showApplication();
        }

        // 显示错误信息
        function showError(message) {
            $("#personalApplication").html(`
                <div class="error-container">
                    <div class="error-title">
                        <i class="ace-icon fa fa-exclamation-triangle"></i>
                        加载失败
                    </div>
                    <div>${message}</div>
                    <div style="margin-top: var(--margin-md);">
                        <button class="btn-mobile btn-primary" onclick="refreshApplications();">
                            <i class="ace-icon fa fa-refresh"></i>
                            <span>重新加载</span>
                        </button>
                    </div>
                </div>
            `);
        }

        // 显示提示信息
        function showToast(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
