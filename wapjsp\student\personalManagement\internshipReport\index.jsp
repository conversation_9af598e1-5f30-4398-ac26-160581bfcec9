<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实习报告</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实习报告页面样式 */
        .report-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .report-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .status-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            border-left: 4px solid var(--primary-color);
        }
        
        .status-info {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .status-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .report-templates {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .templates-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .templates-title {
            display: flex;
            align-items: center;
        }
        
        .templates-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .template-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .template-item:last-child {
            border-bottom: none;
        }
        
        .template-item:active {
            background: var(--bg-color-active);
        }
        
        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .template-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .template-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
            background: var(--primary-color);
            color: white;
        }
        
        .template-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
        }
        
        .template-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .template-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-use {
            background: var(--success-color);
            color: white;
        }
        
        .btn-preview {
            background: var(--info-color);
            color: white;
        }
        
        .my-reports {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .reports-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .reports-title {
            display: flex;
            align-items: center;
        }
        
        .reports-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .reports-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .report-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .report-item:last-child {
            border-bottom: none;
        }
        
        .report-item:active {
            background: var(--bg-color-active);
        }
        
        .report-item.draft {
            border-left: 4px solid var(--warning-color);
        }
        
        .report-item.submitted {
            border-left: 4px solid var(--success-color);
        }
        
        .report-item.reviewed {
            border-left: 4px solid var(--info-color);
        }
        
        .report-item.approved {
            border-left: 4px solid var(--primary-color);
        }
        
        .report-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .report-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .report-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .status-submitted {
            background: var(--success-color);
            color: white;
        }
        
        .status-reviewed {
            background: var(--info-color);
            color: white;
        }
        
        .status-approved {
            background: var(--primary-color);
            color: white;
        }
        
        .report-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .report-summary {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .report-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-download {
            background: var(--success-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .report-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .report-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实习报告</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="report-header">
            <div class="header-title">实习报告</div>
            <div class="header-subtitle">撰写实习总结，展示实践成果</div>
        </div>

        <!-- 报告状态 -->
        <div class="report-status">
            <div class="status-title">当前报告状态</div>
            <div class="status-card" id="currentStatus">
                <div class="status-info" id="statusInfo">暂无报告</div>
                <div class="status-desc" id="statusDesc">您可以开始撰写实习报告</div>
            </div>
        </div>

        <!-- 报告模板 -->
        <div class="report-templates">
            <div class="templates-header">
                <div class="templates-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>报告模板</span>
                </div>
            </div>

            <div id="templatesList">
                <!-- 模板列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 我的报告 -->
        <div class="my-reports">
            <div class="reports-header">
                <div class="reports-title">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <span>我的报告</span>
                </div>
                <div class="reports-count" id="reportsCount">0</div>
            </div>

            <div id="reportsList">
                <!-- 报告列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text"></i>
            <div id="emptyMessage">暂无实习报告</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 报告表单 -->
    <div class="report-form" id="reportForm">
        <div class="form-header">
            <div class="form-back" onclick="closeReportForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">撰写实习报告</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">报告标题</div>
                    <input type="text" class="form-input" id="reportTitle" placeholder="请输入报告标题">
                </div>

                <div class="form-group">
                    <div class="form-label">报告类型</div>
                    <select class="form-input" id="reportType">
                        <option value="practice">认识实习报告</option>
                        <option value="graduation">毕业实习报告</option>
                        <option value="professional">专业实习报告</option>
                        <option value="social">社会实践报告</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">实习单位</div>
                    <input type="text" class="form-input" id="companyName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">实习时间</div>
                    <input type="text" class="form-input" id="internshipPeriod" readonly>
                </div>
            </div>

            <!-- 报告摘要 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>报告摘要</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">摘要</div>
                    <textarea class="form-input form-textarea" id="abstract"
                              placeholder="请简要概述实习的主要内容、收获和意义..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">关键词</div>
                    <input type="text" class="form-input" id="keywords" placeholder="请输入关键词，用逗号分隔">
                </div>
            </div>

            <!-- 实习概况 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-building"></i>
                    <span>实习概况</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">实习单位介绍</div>
                    <textarea class="form-input form-textarea" id="companyIntro"
                              placeholder="请介绍实习单位的基本情况、规模、业务范围等..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label required">实习岗位职责</div>
                    <textarea class="form-input form-textarea" id="jobResponsibilities"
                              placeholder="请详细描述实习岗位的主要职责和工作内容..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">实习环境</div>
                    <textarea class="form-input form-textarea" id="workEnvironment"
                              placeholder="请描述实习的工作环境、团队氛围等..."></textarea>
                </div>
            </div>

            <!-- 实习内容 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-tasks"></i>
                    <span>实习内容</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">主要工作内容</div>
                    <textarea class="form-input form-textarea" id="mainWork"
                              placeholder="请详细描述实习期间的主要工作内容和完成情况..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">参与项目</div>
                    <textarea class="form-input form-textarea" id="projects"
                              placeholder="请描述参与的具体项目及您的贡献..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">技能应用</div>
                    <textarea class="form-input form-textarea" id="skillsApplied"
                              placeholder="请描述在实习中应用的专业技能和知识..."></textarea>
                </div>
            </div>

            <!-- 收获体会 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-lightbulb-o"></i>
                    <span>收获体会</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">专业知识收获</div>
                    <textarea class="form-input form-textarea" id="professionalGains"
                              placeholder="请总结在专业知识方面的收获和提升..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">实践能力提升</div>
                    <textarea class="form-input form-textarea" id="practicalSkills"
                              placeholder="请描述实践能力的提升和改进..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">个人成长</div>
                    <textarea class="form-input form-textarea" id="personalGrowth"
                              placeholder="请分享个人成长和心得体会..."></textarea>
                </div>
            </div>

            <!-- 问题与建议 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-exclamation-triangle"></i>
                    <span>问题与建议</span>
                </div>

                <div class="form-group">
                    <div class="form-label">遇到的问题</div>
                    <textarea class="form-input form-textarea" id="problems"
                              placeholder="请描述实习过程中遇到的主要问题和困难..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">解决方案</div>
                    <textarea class="form-input form-textarea" id="solutions"
                              placeholder="请描述问题的解决方案和处理方法..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">改进建议</div>
                    <textarea class="form-input form-textarea" id="suggestions"
                              placeholder="请提出对实习工作或教学的改进建议..."></textarea>
                </div>
            </div>

            <!-- 总结展望 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-flag-checkered"></i>
                    <span>总结展望</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">实习总结</div>
                    <textarea class="form-input form-textarea" id="summary"
                              placeholder="请对整个实习经历进行总结..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">未来规划</div>
                    <textarea class="form-input form-textarea" id="futurePlans"
                              placeholder="请描述对未来学习和职业发展的规划..."></textarea>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeReportForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitReport();">提交报告</button>
        </div>
    </div>

    <script>
        // 全局变量
        let reportTemplates = [];
        let myReports = [];
        let currentReport = null;
        let internshipInfo = {};
        let reportStatus = {};

        $(function() {
            initPage();
            loadInternshipInfo();
            loadReportStatus();
            loadReportTemplates();
            loadMyReports();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载实习信息
        function loadInternshipInfo() {
            $.ajax({
                url: "/student/personalManagement/internshipReport/getInternshipInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    internshipInfo = data || {};
                },
                error: function() {
                    console.log('加载实习信息失败');
                }
            });
        }

        // 加载报告状态
        function loadReportStatus() {
            $.ajax({
                url: "/student/personalManagement/internshipReport/getReportStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    reportStatus = data || {};
                    updateReportStatus();
                },
                error: function() {
                    console.log('加载报告状态失败');
                }
            });
        }

        // 更新报告状态
        function updateReportStatus() {
            if (reportStatus.hasReport) {
                $('#statusInfo').text(`当前报告：${reportStatus.reportTitle}`);
                $('#statusDesc').text(`状态：${getReportStatusText(reportStatus.status)}`);

                // 根据状态设置颜色
                const statusCard = $('.status-card');
                switch(reportStatus.status) {
                    case 'approved':
                        statusCard.css('border-left-color', 'var(--success-color)');
                        break;
                    case 'submitted':
                        statusCard.css('border-left-color', 'var(--info-color)');
                        break;
                    case 'reviewed':
                        statusCard.css('border-left-color', 'var(--primary-color)');
                        break;
                    case 'draft':
                        statusCard.css('border-left-color', 'var(--warning-color)');
                        break;
                }
            } else {
                $('#statusInfo').text('暂无报告');
                $('#statusDesc').text('您可以开始撰写实习报告');
            }
        }

        // 加载报告模板
        function loadReportTemplates() {
            $.ajax({
                url: "/student/personalManagement/internshipReport/getReportTemplates",
                type: "post",
                dataType: "json",
                success: function(data) {
                    reportTemplates = data.templates || [];
                    renderTemplatesList();
                },
                error: function() {
                    console.log('加载报告模板失败');
                }
            });
        }

        // 加载我的报告
        function loadMyReports() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/internshipReport/getMyReports",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myReports = data.reports || [];
                    renderReportsList();
                    updateReportsCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载报告列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染模板列表
        function renderTemplatesList() {
            const container = $('#templatesList');
            container.empty();

            if (reportTemplates.length === 0) {
                container.html('<div style="padding: 20px; text-align: center; color: var(--text-secondary);">暂无可用模板</div>');
                return;
            }

            reportTemplates.forEach(template => {
                const templateHtml = createTemplateItem(template);
                container.append(templateHtml);
            });
        }

        // 创建模板项
        function createTemplateItem(template) {
            return `
                <div class="template-item" onclick="previewTemplate('${template.id}')">
                    <div class="template-header">
                        <div class="template-name">${template.name}</div>
                        <div class="template-type">${getTemplateTypeText(template.type)}</div>
                    </div>
                    <div class="template-desc">${template.description}</div>
                    <div class="template-info">
                        <div class="info-item">
                            <span>适用类型:</span>
                            <span>${getTemplateTypeText(template.type)}</span>
                        </div>
                        <div class="info-item">
                            <span>字数要求:</span>
                            <span>${template.wordCount}字</span>
                        </div>
                        <div class="info-item">
                            <span>更新时间:</span>
                            <span>${formatDate(template.updateTime)}</span>
                        </div>
                        <div class="info-item">
                            <span>使用次数:</span>
                            <span>${template.useCount}次</span>
                        </div>
                    </div>
                    <div class="template-actions">
                        <button class="btn-mobile btn-use" onclick="useTemplate('${template.id}')">使用模板</button>
                        <button class="btn-mobile btn-preview" onclick="previewTemplate('${template.id}')">预览</button>
                    </div>
                </div>
            `;
        }

        // 获取模板类型文本
        function getTemplateTypeText(type) {
            switch(type) {
                case 'practice': return '认识实习';
                case 'graduation': return '毕业实习';
                case 'professional': return '专业实习';
                case 'social': return '社会实践';
                default: return '通用';
            }
        }

        // 渲染报告列表
        function renderReportsList() {
            const container = $('#reportsList');
            container.empty();

            if (myReports.length === 0) {
                showEmptyState('暂无实习报告');
                return;
            } else {
                hideEmptyState();
            }

            myReports.forEach(report => {
                const reportHtml = createReportItem(report);
                container.append(reportHtml);
            });
        }

        // 创建报告项
        function createReportItem(report) {
            const statusClass = getReportStatusClass(report.status);
            const statusText = getReportStatusText(report.status);

            return `
                <div class="report-item ${statusClass}" onclick="showReportDetail('${report.id}')">
                    <div class="report-basic">
                        <div class="report-title">${report.title}</div>
                        <div class="report-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="report-details">
                        <div class="detail-item">
                            <span>报告类型:</span>
                            <span>${getTemplateTypeText(report.type)}</span>
                        </div>
                        <div class="detail-item">
                            <span>创建时间:</span>
                            <span>${formatDate(report.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>更新时间:</span>
                            <span>${formatDate(report.updateTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>字数:</span>
                            <span>${report.wordCount || 0}字</span>
                        </div>
                    </div>
                    <div class="report-summary">${report.abstract || '暂无摘要'}</div>
                    <div class="report-actions">
                        ${createReportActions(report)}
                    </div>
                </div>
            `;
        }

        // 创建报告操作按钮
        function createReportActions(report) {
            const canEdit = report.status === 'draft';
            const canDownload = report.status === 'approved';

            let actions = [];

            if (canEdit) {
                actions.push(`<button class="btn-mobile btn-edit" onclick="editReport('${report.id}')">编辑</button>`);
                actions.push(`<button class="btn-mobile btn-delete" onclick="deleteReport('${report.id}')">删除</button>`);
            } else {
                actions.push(`<button class="btn-mobile btn-view" onclick="showReportDetail('${report.id}')">查看</button>`);
            }

            if (canDownload) {
                actions.push(`<button class="btn-mobile btn-download" onclick="downloadReport('${report.id}')">下载</button>`);
            }

            return actions.join('');
        }

        // 获取报告状态样式类
        function getReportStatusClass(status) {
            switch(status) {
                case 'draft': return 'draft';
                case 'submitted': return 'submitted';
                case 'reviewed': return 'reviewed';
                case 'approved': return 'approved';
                default: return 'draft';
            }
        }

        // 获取报告状态文本
        function getReportStatusText(status) {
            switch(status) {
                case 'draft': return '草稿';
                case 'submitted': return '已提交';
                case 'reviewed': return '已评阅';
                case 'approved': return '已通过';
                default: return '未知';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 使用模板
        function useTemplate(templateId) {
            const template = reportTemplates.find(t => t.id === templateId);
            if (!template) return;

            const message = `确定要使用"${template.name}"模板创建报告吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        showReportForm(null, templateId);
                    }
                });
            } else {
                if (confirm(message)) {
                    showReportForm(null, templateId);
                }
            }
        }

        // 预览模板
        function previewTemplate(templateId) {
            const template = reportTemplates.find(t => t.id === templateId);
            if (!template) return;

            let message = `模板预览\n\n`;
            message += `模板名称：${template.name}\n`;
            message += `适用类型：${getTemplateTypeText(template.type)}\n`;
            message += `字数要求：${template.wordCount}字\n`;
            message += `模板说明：${template.description}\n\n`;
            message += `主要章节：\n`;

            if (template.sections && template.sections.length > 0) {
                template.sections.forEach((section, index) => {
                    message += `${index + 1}. ${section}\n`;
                });
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示报告表单
        function showReportForm(reportId = null, templateId = null) {
            currentReport = reportId ? myReports.find(r => r.id === reportId) : null;

            if (currentReport) {
                // 编辑模式
                $('#formTitle').text('编辑实习报告');
                fillReportForm(currentReport);
            } else {
                // 新建模式
                $('#formTitle').text('撰写实习报告');
                resetReportForm();

                if (templateId) {
                    applyTemplate(templateId);
                }
            }

            // 填充实习信息
            fillInternshipInfo();

            $('#reportForm').addClass('show');
        }

        // 应用模板
        function applyTemplate(templateId) {
            const template = reportTemplates.find(t => t.id === templateId);
            if (!template) return;

            $('#reportType').val(template.type);
            $('#reportTitle').val(template.defaultTitle || '');
        }

        // 填充实习信息
        function fillInternshipInfo() {
            $('#companyName').val(internshipInfo.companyName || '');

            if (internshipInfo.startDate && internshipInfo.endDate) {
                const period = `${formatDate(internshipInfo.startDate)} - ${formatDate(internshipInfo.endDate)}`;
                $('#internshipPeriod').val(period);
            }
        }

        // 填充报告表单
        function fillReportForm(report) {
            $('#reportTitle').val(report.title);
            $('#reportType').val(report.type);
            $('#abstract').val(report.abstract);
            $('#keywords').val(report.keywords);
            $('#companyIntro').val(report.companyIntro);
            $('#jobResponsibilities').val(report.jobResponsibilities);
            $('#workEnvironment').val(report.workEnvironment);
            $('#mainWork').val(report.mainWork);
            $('#projects').val(report.projects);
            $('#skillsApplied').val(report.skillsApplied);
            $('#professionalGains').val(report.professionalGains);
            $('#practicalSkills').val(report.practicalSkills);
            $('#personalGrowth').val(report.personalGrowth);
            $('#problems').val(report.problems);
            $('#solutions').val(report.solutions);
            $('#suggestions').val(report.suggestions);
            $('#summary').val(report.summary);
            $('#futurePlans').val(report.futurePlans);
        }

        // 重置报告表单
        function resetReportForm() {
            $('#reportTitle').val('');
            $('#reportType').val('practice');
            $('#abstract').val('');
            $('#keywords').val('');
            $('#companyIntro').val('');
            $('#jobResponsibilities').val('');
            $('#workEnvironment').val('');
            $('#mainWork').val('');
            $('#projects').val('');
            $('#skillsApplied').val('');
            $('#professionalGains').val('');
            $('#practicalSkills').val('');
            $('#personalGrowth').val('');
            $('#problems').val('');
            $('#solutions').val('');
            $('#suggestions').val('');
            $('#summary').val('');
            $('#futurePlans').val('');
        }

        // 关闭报告表单
        function closeReportForm() {
            $('#reportForm').removeClass('show');
        }

        // 编辑报告
        function editReport(reportId) {
            showReportForm(reportId);
        }

        // 删除报告
        function deleteReport(reportId) {
            const report = myReports.find(r => r.id === reportId);
            if (!report) return;

            const message = `确定要删除报告"${report.title}"吗？\n\n删除后无法恢复。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doDeleteReport(reportId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doDeleteReport(reportId);
                }
            }
        }

        // 执行删除报告
        function doDeleteReport(reportId) {
            $.ajax({
                url: "/student/personalManagement/internshipReport/deleteReport",
                type: "post",
                data: { reportId: reportId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('报告删除成功');
                        loadReportStatus();
                        loadMyReports();
                    } else {
                        showError(data.message || '删除失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 下载报告
        function downloadReport(reportId) {
            const report = myReports.find(r => r.id === reportId);
            if (!report) return;

            $.ajax({
                url: "/student/personalManagement/internshipReport/downloadReport",
                type: "post",
                data: { reportId: reportId },
                dataType: "json",
                success: function(data) {
                    if (data.success && data.downloadUrl) {
                        window.open(data.downloadUrl, '_blank');
                        showSuccess('报告下载成功');
                    } else {
                        showError(data.message || '下载失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 保存草稿
        function saveDraft() {
            if (!validateReportForm(false)) {
                return;
            }

            const formData = collectReportFormData();
            formData.isDraft = true;

            submitReportFormData(formData, '草稿保存成功');
        }

        // 提交报告
        function submitReport() {
            if (!validateReportForm(true)) {
                return;
            }

            const formData = collectReportFormData();
            formData.isDraft = false;

            const message = `确定要提交实习报告"${formData.reportTitle}"吗？\n\n提交后将无法修改。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitReportFormData(formData, '报告提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    submitReportFormData(formData, '报告提交成功');
                }
            }
        }

        // 收集报告表单数据
        function collectReportFormData() {
            return {
                id: currentReport ? currentReport.id : null,
                reportTitle: $('#reportTitle').val(),
                reportType: $('#reportType').val(),
                abstract: $('#abstract').val(),
                keywords: $('#keywords').val(),
                companyIntro: $('#companyIntro').val(),
                jobResponsibilities: $('#jobResponsibilities').val(),
                workEnvironment: $('#workEnvironment').val(),
                mainWork: $('#mainWork').val(),
                projects: $('#projects').val(),
                skillsApplied: $('#skillsApplied').val(),
                professionalGains: $('#professionalGains').val(),
                practicalSkills: $('#practicalSkills').val(),
                personalGrowth: $('#personalGrowth').val(),
                problems: $('#problems').val(),
                solutions: $('#solutions').val(),
                suggestions: $('#suggestions').val(),
                summary: $('#summary').val(),
                futurePlans: $('#futurePlans').val()
            };
        }

        // 验证报告表单
        function validateReportForm(isSubmit) {
            if (!$('#reportTitle').val().trim()) {
                showError('请填写报告标题');
                return false;
            }

            if (isSubmit) {
                if (!$('#abstract').val().trim()) {
                    showError('请填写报告摘要');
                    return false;
                }

                if (!$('#companyIntro').val().trim()) {
                    showError('请填写实习单位介绍');
                    return false;
                }

                if (!$('#jobResponsibilities').val().trim()) {
                    showError('请填写实习岗位职责');
                    return false;
                }

                if (!$('#mainWork').val().trim()) {
                    showError('请填写主要工作内容');
                    return false;
                }

                if (!$('#professionalGains').val().trim()) {
                    showError('请填写专业知识收获');
                    return false;
                }

                if (!$('#summary').val().trim()) {
                    showError('请填写实习总结');
                    return false;
                }
            }

            return true;
        }

        // 提交报告表单数据
        function submitReportFormData(formData, successMessage) {
            $.ajax({
                url: "/student/personalManagement/internshipReport/saveReport",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        closeReportForm();
                        loadReportStatus();
                        loadMyReports();
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示报告详情
        function showReportDetail(reportId) {
            const report = myReports.find(r => r.id === reportId);
            if (!report) return;

            let message = `实习报告详情\n\n`;
            message += `报告标题：${report.title}\n`;
            message += `报告类型：${getTemplateTypeText(report.type)}\n`;
            message += `创建时间：${formatDate(report.createTime)}\n`;
            message += `更新时间：${formatDate(report.updateTime)}\n`;
            message += `当前状态：${getReportStatusText(report.status)}\n`;
            message += `字数统计：${report.wordCount || 0}字\n\n`;

            if (report.abstract) {
                message += `报告摘要：\n${report.abstract}\n\n`;
            }

            if (report.keywords) {
                message += `关键词：${report.keywords}\n\n`;
            }

            if (report.reviewComment) {
                message += `评阅意见：${report.reviewComment}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 更新报告数量
        function updateReportsCount() {
            $('#reportsCount').text(myReports.length);
        }

        // 刷新数据
        function refreshData() {
            loadReportStatus();
            loadReportTemplates();
            loadMyReports();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.my-reports').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.my-reports').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.my-reports').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#reportForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#reportForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeReportForm();
            }
        });

        $('#reportForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
