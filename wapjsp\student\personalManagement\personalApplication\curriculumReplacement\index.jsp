<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>课程替代</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 课程替代页面样式 */
        .curriculum-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .curriculum-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .curriculum-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-title i {
            color: var(--success-color);
        }
        
        .btn-add-application {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            width: 100%;
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .btn-add-application:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .applications-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-header i {
            color: var(--primary-color);
        }
        
        .applications-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .application-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary-light);
            color: var(--primary-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .application-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .application-id {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .application-date {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-reviewing {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-saved {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-cancelled {
            background: var(--text-disabled);
            color: white;
        }
        
        .status-withdrawn {
            background: var(--text-disabled);
            color: white;
        }
        
        .application-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 8px;
        }
        
        .detail-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .detail-icon {
            color: var(--primary-color);
            width: 16px;
            margin-top: 2px;
            flex-shrink: 0;
        }
        
        .detail-content {
            flex: 1;
            word-break: break-word;
        }
        
        .course-list {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .course-item {
            padding: 4px 8px;
            background: var(--bg-tertiary);
            border-radius: 4px;
            font-size: var(--font-size-small);
        }
        
        .replaced-course {
            text-decoration: line-through;
            color: var(--text-disabled);
        }
        
        .replacement-type {
            padding: 4px 8px;
            background: var(--warning-light);
            color: var(--warning-dark);
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            display: inline-block;
            margin-top: 8px;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .action-btn.view {
            color: var(--info-color);
            border-color: var(--info-color);
        }
        
        .action-btn.view:hover {
            background: var(--info-light);
        }
        
        .action-btn.delete {
            color: var(--error-color);
            border-color: var(--error-color);
        }
        
        .action-btn.delete:hover {
            background: var(--error-light);
        }
        
        .action-btn.withdraw {
            color: var(--warning-color);
            border-color: var(--warning-color);
        }
        
        .action-btn.withdraw:hover {
            background: var(--warning-light);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-dark);
        }
        
        .btn-page.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .application-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .application-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    <input type="hidden" id="pxcsb" value="${pxcsb}">
    <input type="hidden" id="schoolCode" value="${schoolCode}">
    <input type="hidden" id="approvalProcess" value="${approvalProcess}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">课程替代</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 课程替代头部 -->
        <div class="curriculum-header">
            <div class="curriculum-title">课程替代</div>
            <div class="curriculum-desc">我申请的课程替代</div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="quick-title">
                <i class="ace-icon fa fa-plus"></i>
                快捷操作
            </div>
            <button class="btn-add-application" id="btnAddApplication" onclick="addInfo();">
                <i class="ace-icon fa fa-plus"></i>
                增加新的申请
            </button>
        </div>
        
        <!-- 申请列表 -->
        <div class="applications-container">
            <div class="container-header">
                <i class="ace-icon fa fa-list"></i>
                申请记录
            </div>
            
            <div class="applications-list" id="applicationsList">
                <!-- 动态加载申请列表 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let pxcsb = '${pxcsb}';
        let schoolCode = '${schoolCode}';
        let approvalProcess = '${approvalProcess}';

        $(function() {
            initPage();
            loadApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 检查申请开关状态
            if (pxcsb !== '1') {
                $('#btnAddApplication').prop('disabled', true);
                $('#btnAddApplication').text('申请开关已关闭');
            }
        }

        // 加载申请列表
        function loadApplications(page = 1) {
            currentPage = page;
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/personalApplication/curriculumReplacement/index/getPage",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    if (response && response.data) {
                        renderApplications(response.data.records);
                        renderPagination(response.data.pageContext);
                    } else {
                        showEmptyState('applicationsList', '暂无申请记录');
                    }
                    $('#tokenValue').val(response.token);
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications(applications) {
            const container = $('#applicationsList');

            if (!applications || applications.length === 0) {
                showEmptyState('applicationsList', '暂无申请记录');
                return;
            }

            let applicationsHtml = '';

            applications.forEach((application, index) => {
                const serialNumber = (currentPage - 1) * pageSize + 1 + index;
                const statusInfo = getStatusInfo(application.SQZT);

                applicationsHtml += `
                    <div class="application-item">
                        <div class="application-header">
                            <div class="application-number">${serialNumber}</div>
                            <div class="application-info">
                                <div class="application-id">申请单号：${application.SQBH || '未设置'}</div>
                                <div class="application-date">申请日期：${application.SQRQ || '未设置'}</div>
                            </div>
                            <div class="application-status ${statusInfo.class}">${statusInfo.text}</div>
                        </div>

                        <div class="application-details">
                            <div class="detail-item">
                                <i class="ace-icon fa fa-check detail-icon"></i>
                                <div class="detail-content">
                                    <strong>课程：</strong>
                                    <div class="course-list">
                                        ${(application.KCM || '').split(';').filter(course => course.trim()).map(course =>
                                            `<div class="course-item">${course}</div>`
                                        ).join('')}
                                    </div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <i class="ace-icon fa fa-mortar-board detail-icon"></i>
                                <div class="detail-content">
                                    <strong>被替代课程：</strong>
                                    <div class="course-list">
                                        ${(application.TDKCM || '').split(';').filter(course => course.trim()).map(course =>
                                            `<div class="course-item replaced-course">${course}</div>`
                                        ).join('')}
                                    </div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <i class="ace-icon fa fa-file-text-o detail-icon"></i>
                                <div class="detail-content">
                                    <strong>申请原因：</strong>
                                    ${application.SQYY || application.TDYY || '未填写'}
                                </div>
                            </div>
                        </div>

                        <div class="replacement-type">${application.TDLXSM || '未设置'}</div>

                        <div class="application-actions">
                            ${getActionButtons(application)}
                        </div>
                    </div>
                `;
            });

            container.html(applicationsHtml);
        }

        // 获取状态信息
        function getStatusInfo(status) {
            if (approvalProcess === '1') {
                switch(status) {
                    case -1:
                        return { text: "拒绝", class: "status-rejected" };
                    case -2:
                        return { text: "取消[终审后]", class: "status-cancelled" };
                    case -3:
                        return { text: "撤回[终审前]", class: "status-withdrawn" };
                    case 0:
                        return { text: "暂存", class: "status-saved" };
                    case 1:
                        return { text: "待审批", class: "status-pending" };
                    case 2:
                        return { text: "审批中", class: "status-reviewing" };
                    case 3:
                        return { text: "批准", class: "status-approved" };
                    default:
                        return { text: "未知", class: "status-pending" };
                }
            } else {
                switch(status) {
                    case -1:
                        return { text: "不批准", class: "status-rejected" };
                    case -2:
                        return { text: "已撤回", class: "status-withdrawn" };
                    case 0:
                        return { text: "待审批", class: "status-pending" };
                    case 1:
                        return { text: "审批中", class: "status-reviewing" };
                    case 2:
                        return { text: "已批准", class: "status-approved" };
                    default:
                        return { text: "未知", class: "status-pending" };
                }
            }
        }

        // 获取操作按钮
        function getActionButtons(application) {
            let buttons = '';

            // 查看按钮
            if (approvalProcess === '1') {
                buttons += `
                    <button class="action-btn view" onclick="seeInfo('${application.SQBH}');">
                        <i class="ace-icon fa fa-eye"></i>
                        查看
                    </button>
                `;
            } else {
                if ([1, 2, -1, -2].includes(application.SQZT)) {
                    buttons += `
                        <button class="action-btn view" onclick="seeInfo('${application.SQBH}');">
                            <i class="ace-icon fa fa-eye"></i>
                            查看
                        </button>
                    `;
                }
            }

            // 删除/撤回按钮
            if (pxcsb === '1') {
                if (application.SQZT === 0) {
                    buttons += `
                        <button class="action-btn delete" onclick="revokeInfo('${application.SQBH}');">
                            <i class="ace-icon fa fa-trash-o"></i>
                            删除
                        </button>
                    `;
                } else if (schoolCode !== "100027" && schoolCode !== "100053") {
                    if (approvalProcess === '1') {
                        if ([1, 2].includes(application.SQZT)) {
                            buttons += `
                                <button class="action-btn withdraw" onclick="doRevoke('${application.SQBH}', '撤回');">
                                    <i class="ace-icon fa fa-reply"></i>
                                    撤回
                                </button>
                            `;
                        } else if (application.SQZT === 3) {
                            buttons += `
                                <button class="action-btn withdraw" onclick="doRevoke('${application.SQBH}', '取消');">
                                    <i class="ace-icon fa fa-reply"></i>
                                    取消
                                </button>
                            `;
                        }
                    } else {
                        if (application.SQZT === 2) {
                            buttons += `
                                <button class="action-btn withdraw" onclick="doRevoke('${application.SQBH}', '撤回');">
                                    <i class="ace-icon fa fa-reply"></i>
                                    撤回
                                </button>
                            `;
                        }
                    }
                }
            }

            return buttons;
        }

        // 显示空状态
        function showEmptyState(containerId, message) {
            const container = $('#' + containerId);
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <div class="empty-state-text">${message}</div>
                    <div class="empty-state-desc">请尝试添加新的申请</div>
                </div>
            `);
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            if (!pageContext || pageContext.totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="loadApplications(${currentPage - 1});">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<button class="btn-page" onclick="loadApplications(1);">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="loadApplications(${i});">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                paginationHtml += `<button class="btn-page" onclick="loadApplications(${totalPages});">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="loadApplications(${currentPage + 1});">下一页</button>`;

            container.html(paginationHtml);
            $('#paginationContainer').show();
        }

        // 添加申请
        function addInfo() {
            if (pxcsb === '1') {
                const url = "/student/personalManagement/personalApplication/curriculumReplacement/addInfo";

                if (parent && parent.addTab) {
                    parent.addTab('添加课程替代申请', url);
                } else {
                    window.location.href = url;
                }
            } else {
                showError("课程替代申请开关已关闭,请打开后申请!");
            }
        }

        // 查看申请详情
        function seeInfo(applyId) {
            let url;

            if (approvalProcess === '1') {
                url = `/student/application/index/seeInfo?applyId=${applyId}&applyType=10001`;
            } else {
                url = `/student/personalManagement/personalApplication/curriculumReplacement/seeInfo?sqbh=${applyId}`;
            }

            if (parent && parent.addTab) {
                parent.addTab('查看申请详情', url);
            } else {
                window.location.href = url;
            }
        }

        // 删除申请
        function revokeInfo(sqbh) {
            if (confirm('确定要删除当前申请？')) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/personalApplication/curriculumReplacement/revokeInfo",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $('#tokenValue').val(),
                    dataType: "json",
                    success: function(response) {
                        if (response.status !== 200) {
                            showError(response.msg);
                        } else {
                            if (response.data.result.indexOf("/") !== -1) {
                                window.location.href = response.data.result;
                            } else {
                                if (response.data.result === "ok") {
                                    showSuccess("删除成功！");
                                    loadApplications(currentPage);
                                } else {
                                    showError(response.data.result);
                                }
                            }
                            $('#tokenValue').val(response.data.token);
                        }
                    },
                    error: function() {
                        showError("删除失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 撤回/取消申请
        function doRevoke(sqbh, action) {
            const reason = prompt(`请输入${action}原因：`);

            if (reason === null) {
                return; // 用户取消
            }

            if (!reason.trim()) {
                showError(`${action}原因不能为空！`);
                return;
            }

            if (confirm(`是否确认${action}当前申请？`)) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/personalApplication/curriculumReplacement/doRevoke",
                    type: "post",
                    data: "sqbh=" + sqbh + "&cxyy=" + reason + "&tokenValue=" + $('#tokenValue').val(),
                    dataType: "json",
                    success: function(response) {
                        if (response.result.indexOf("/") !== -1) {
                            window.location.href = response.result;
                        } else {
                            if (response.result === "0") {
                                showSuccess("操作成功！");
                                loadApplications(currentPage);
                            } else {
                                showError(response.result);
                            }
                        }
                        $('#tokenValue').val(response.token);
                    },
                    error: function() {
                        showError("操作失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 返回首页
        function returnIndex() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                history.back();
            }
        }

        // 刷新数据
        function refreshData() {
            loadApplications(currentPage);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
