<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>免试考试</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 免试考试页面样式 */
        .exempt-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .exempt-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .exempt-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
        }
        
        .exempt-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .exempt-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .exempt-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .exempt-item:last-child {
            border-bottom: none;
        }
        
        .exempt-item:active {
            background: var(--bg-color-active);
        }
        
        .exempt-item.available {
            border-left: 4px solid var(--success-color);
        }
        
        .exempt-item.applied {
            border-left: 4px solid var(--info-color);
        }
        
        .exempt-item.approved {
            border-left: 4px solid var(--primary-color);
        }
        
        .exempt-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .exempt-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exempt-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .exempt-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-applied {
            background: var(--info-color);
            color: white;
        }
        
        .status-approved {
            background: var(--primary-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .exempt-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .exempt-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exempt-requirements {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
        }
        
        .exempt-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-apply {
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .application-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .application-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .file-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .file-upload:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .upload-icon {
            font-size: var(--font-size-h3);
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
        }
        
        .upload-hint {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .file-list {
            margin-top: var(--margin-md);
        }
        
        .file-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .file-icon {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-sm);
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .file-size {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .file-delete {
            width: 28px;
            height: 28px;
            border-radius: 4px;
            background: var(--error-color);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: var(--font-size-small);
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel-form {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">免试考试</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="exempt-header">
            <div class="header-title">免试考试</div>
            <div class="header-subtitle">申请免试考试资格</div>
        </div>

        <!-- 免试标签 -->
        <div class="exempt-tabs">
            <div class="exempt-tab active" data-tab="available" onclick="switchTab('available')">可申请</div>
            <div class="exempt-tab" data-tab="applied" onclick="switchTab('applied')">已申请</div>
            <div class="exempt-tab" data-tab="approved" onclick="switchTab('approved')">已通过</div>
        </div>

        <!-- 免试列表 -->
        <div class="exempt-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span id="listTitle">可申请免试</span>
                </div>
                <div class="list-count" id="exemptCount">0</div>
            </div>

            <div id="exemptItems">
                <!-- 免试列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-graduation-cap"></i>
            <div id="emptyMessage">暂无免试信息</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 申请表单 -->
    <div class="application-form" id="applicationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeApplicationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">免试申请</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-book"></i>
                    <span>课程信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">课程名称</div>
                    <input type="text" class="form-input" id="courseName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">课程代码</div>
                    <input type="text" class="form-input" id="courseCode" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">学分</div>
                    <input type="text" class="form-input" id="credits" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">免试类型</div>
                    <select class="form-input" id="exemptType">
                        <option value="">请选择免试类型</option>
                        <option value="certificate">证书免试</option>
                        <option value="competition">竞赛免试</option>
                        <option value="transfer">转学分免试</option>
                        <option value="other">其他</option>
                    </select>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">申请理由</div>
                    <textarea class="form-input form-textarea" id="reason" placeholder="请详细说明申请免试的理由"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">相关经历</div>
                    <textarea class="form-input form-textarea" id="experience" placeholder="请描述与该课程相关的学习或工作经历"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file"></i>
                    <span>证明材料</span>
                </div>

                <div class="form-group">
                    <div class="form-label">上传文件</div>
                    <div class="file-upload" onclick="selectFiles();">
                        <div class="upload-icon">
                            <i class="ace-icon fa fa-cloud-upload"></i>
                        </div>
                        <div class="upload-text">点击上传证明材料</div>
                        <div class="upload-hint">支持PDF、JPG、PNG格式，单个文件不超过5MB</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png" style="display: none;">
                    <div class="file-list" id="fileList">
                        <!-- 文件列表将动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel-form flex-1" onclick="closeApplicationForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let allExempts = [];
        let currentTab = 'available';
        let currentExempt = null;
        let uploadedFiles = [];

        $(function() {
            initPage();
            loadExemptData();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 文件选择事件
            $('#fileInput').change(function() {
                handleFileSelect(this.files);
            });
        }

        // 加载免试数据
        function loadExemptData() {
            showLoading(true);

            $.ajax({
                url: "/student/exemptsExam/getExemptData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allExempts = data.exempts || [];
                    renderExemptList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载免试数据失败');
                    showLoading(false);
                }
            });
        }

        // 切换标签
        function switchTab(tab) {
            currentTab = tab;

            // 更新标签状态
            $('.exempt-tab').removeClass('active');
            $(`.exempt-tab[data-tab="${tab}"]`).addClass('active');

            // 更新列表标题
            const titles = {
                'available': '可申请免试',
                'applied': '已申请免试',
                'approved': '已通过免试'
            };
            $('#listTitle').text(titles[tab]);

            renderExemptList();
        }

        // 渲染免试列表
        function renderExemptList() {
            const filteredExempts = allExempts.filter(exempt => {
                const status = exempt.status || 'available';
                return currentTab === 'available' ? status === 'available' :
                       currentTab === 'applied' ? (status === 'applied' || status === 'rejected') :
                       status === 'approved';
            });

            $('#exemptCount').text(filteredExempts.length);

            const container = $('#exemptItems');
            container.empty();

            if (filteredExempts.length === 0) {
                const messages = {
                    'available': '暂无可申请的免试课程',
                    'applied': '暂无申请记录',
                    'approved': '暂无通过的免试申请'
                };
                showEmptyState(messages[currentTab]);
                return;
            } else {
                hideEmptyState();
            }

            filteredExempts.forEach(exempt => {
                const exemptHtml = createExemptItem(exempt);
                container.append(exemptHtml);
            });
        }

        // 创建免试项
        function createExemptItem(exempt) {
            const status = exempt.status || 'available';
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);

            return `
                <div class="exempt-item ${status}" onclick="showExemptDetail('${exempt.id}')">
                    <div class="exempt-basic">
                        <div class="exempt-course">${exempt.courseName}</div>
                        <div class="exempt-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="exempt-details">
                        <div class="exempt-detail-item">
                            <span>课程代码:</span>
                            <span>${exempt.courseCode}</span>
                        </div>
                        <div class="exempt-detail-item">
                            <span>学分:</span>
                            <span>${exempt.credits}</span>
                        </div>
                        <div class="exempt-detail-item">
                            <span>开课学期:</span>
                            <span>${exempt.semester}</span>
                        </div>
                        <div class="exempt-detail-item">
                            <span>申请截止:</span>
                            <span>${formatDate(exempt.deadline)}</span>
                        </div>
                    </div>
                    ${exempt.requirements ? `
                        <div class="exempt-requirements">
                            <strong>免试要求：</strong>${exempt.requirements}
                        </div>
                    ` : ''}
                    <div class="exempt-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showExemptDetail('${exempt.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'available' ? `
                            <button class="btn-mobile btn-apply" onclick="event.stopPropagation(); showApplicationForm('${exempt.id}');">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>申请</span>
                            </button>
                        ` : status === 'applied' ? `
                            <button class="btn-mobile btn-cancel" onclick="event.stopPropagation(); cancelApplication('${exempt.id}');">
                                <i class="ace-icon fa fa-times"></i>
                                <span>取消申请</span>
                            </button>
                        ` : status === 'rejected' ? `
                            <button class="btn-mobile btn-apply" onclick="event.stopPropagation(); showApplicationForm('${exempt.id}');">
                                <i class="ace-icon fa fa-refresh"></i>
                                <span>重新申请</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-check"></i>
                                <span>已通过</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return `status-${status}`;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'available': return '可申请';
                case 'applied': return '审核中';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 显示免试详情
        function showExemptDetail(exemptId) {
            const exempt = allExempts.find(e => e.id === exemptId);
            if (!exempt) return;

            let message = `免试详情\n\n`;
            message += `课程名称：${exempt.courseName}\n`;
            message += `课程代码：${exempt.courseCode}\n`;
            message += `学分：${exempt.credits}\n`;
            message += `开课学期：${exempt.semester}\n`;
            message += `申请截止：${formatDate(exempt.deadline)}\n`;
            message += `状态：${getStatusText(exempt.status)}\n`;

            if (exempt.requirements) {
                message += `\n免试要求：${exempt.requirements}\n`;
            }

            if (exempt.applyTime) {
                message += `申请时间：${formatDate(exempt.applyTime)}\n`;
            }

            if (exempt.processTime) {
                message += `处理时间：${formatDate(exempt.processTime)}\n`;
            }

            if (exempt.processComment) {
                message += `处理意见：${exempt.processComment}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示申请表单
        function showApplicationForm(exemptId) {
            const exempt = allExempts.find(e => e.id === exemptId);
            if (!exempt) return;

            currentExempt = exempt;

            // 填充课程信息
            $('#courseName').val(exempt.courseName);
            $('#courseCode').val(exempt.courseCode);
            $('#credits').val(exempt.credits);

            // 清空表单
            $('#exemptType').val('');
            $('#reason').val('');
            $('#experience').val('');
            $('#phone').val('');
            uploadedFiles = [];
            renderFileList();

            $('#formTitle').text(`${exempt.courseName} - 免试申请`);
            $('#applicationForm').addClass('show');
        }

        // 关闭申请表单
        function closeApplicationForm() {
            $('#applicationForm').removeClass('show');
            currentExempt = null;
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件选择
        function handleFileSelect(files) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // 检查文件大小
                if (file.size > 5 * 1024 * 1024) {
                    showError(`文件"${file.name}"超过5MB限制`);
                    continue;
                }

                // 检查文件类型
                const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
                if (!allowedTypes.includes(file.type)) {
                    showError(`文件"${file.name}"格式不支持`);
                    continue;
                }

                uploadedFiles.push(file);
            }

            renderFileList();
        }

        // 渲染文件列表
        function renderFileList() {
            const container = $('#fileList');
            container.empty();

            uploadedFiles.forEach((file, index) => {
                const fileHtml = `
                    <div class="file-item">
                        <div class="file-icon">
                            <i class="ace-icon fa ${getFileIcon(file.type)}"></i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                        <button class="file-delete" onclick="removeFile(${index});">
                            <i class="ace-icon fa fa-times"></i>
                        </button>
                    </div>
                `;
                container.append(fileHtml);
            });
        }

        // 获取文件图标
        function getFileIcon(fileType) {
            if (fileType === 'application/pdf') {
                return 'fa-file-pdf-o';
            } else if (fileType.startsWith('image/')) {
                return 'fa-file-image-o';
            } else {
                return 'fa-file-o';
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 移除文件
        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            renderFileList();
        }

        // 提交申请
        function submitApplication() {
            if (!currentExempt) return;

            const formData = {
                exemptId: currentExempt.id,
                exemptType: $('#exemptType').val(),
                reason: $('#reason').val().trim(),
                experience: $('#experience').val().trim(),
                phone: $('#phone').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要申请"${currentExempt.courseName}"的免试吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitApplication(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitApplication(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.exemptType) {
                showError('请选择免试类型');
                return false;
            }

            if (!formData.reason) {
                showError('请填写申请理由');
                return false;
            }

            if (!formData.experience) {
                showError('请填写相关经历');
                return false;
            }

            if (!formData.phone) {
                showError('请填写联系电话');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            if (uploadedFiles.length === 0) {
                showError('请上传证明材料');
                return false;
            }

            return true;
        }

        // 执行提交申请
        function doSubmitApplication(formData) {
            // 这里应该使用FormData来上传文件
            const submitData = new FormData();
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            uploadedFiles.forEach((file, index) => {
                submitData.append(`file_${index}`, file);
            });

            $.ajax({
                url: "/student/exemptsExam/submitApplication",
                type: "post",
                data: submitData,
                processData: false,
                contentType: false,
                success: function(data) {
                    if (data.success) {
                        showSuccess('申请提交成功，请等待审核');
                        closeApplicationForm();
                        loadExemptData(); // 重新加载数据
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 取消申请
        function cancelApplication(exemptId) {
            const exempt = allExempts.find(e => e.id === exemptId);
            if (!exempt) return;

            const message = `确定要取消"${exempt.courseName}"的免试申请吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doCancelApplication(exemptId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doCancelApplication(exemptId);
                }
            }
        }

        // 执行取消申请
        function doCancelApplication(exemptId) {
            $.ajax({
                url: "/student/exemptsExam/cancelApplication",
                type: "post",
                data: { exemptId: exemptId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('取消申请成功');
                        loadExemptData(); // 重新加载数据
                    } else {
                        showError(data.message || '取消申请失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadExemptData();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
