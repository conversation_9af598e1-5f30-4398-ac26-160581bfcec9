<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学期课程日历</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学期课程日历页面样式 */
        .calendar-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .calendar-controls {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 6px;
            border: 1px solid var(--border-primary);
            background: var(--bg-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--primary-color);
        }
        
        .control-btn:active {
            background: var(--bg-color-active);
        }
        
        .current-month {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .calendar-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .calendar-weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .weekday {
            padding: var(--padding-sm);
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .calendar-days {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
        }
        
        .calendar-day {
            aspect-ratio: 1;
            border-bottom: 1px solid var(--divider-color);
            border-right: 1px solid var(--divider-color);
            position: relative;
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .calendar-day:nth-child(7n) {
            border-right: none;
        }
        
        .calendar-day:active {
            background: var(--bg-color-active);
        }
        
        .calendar-day.other-month {
            background: var(--bg-tertiary);
            opacity: 0.5;
        }
        
        .calendar-day.today {
            background: var(--primary-light);
        }
        
        .calendar-day.has-course {
            background: var(--success-light);
        }
        
        .day-number {
            position: absolute;
            top: 4px;
            left: 4px;
            font-size: var(--font-size-mini);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .day-courses {
            position: absolute;
            bottom: 2px;
            left: 2px;
            right: 2px;
            display: flex;
            flex-direction: column;
            gap: 1px;
        }
        
        .course-dot {
            height: 3px;
            border-radius: 1.5px;
            font-size: 0;
        }
        
        .course-dot.required {
            background: var(--primary-color);
        }
        
        .course-dot.elective {
            background: var(--success-color);
        }
        
        .course-dot.exam {
            background: var(--error-color);
        }
        
        .course-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .course-list.show {
            display: block;
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .course-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .course-item:last-child {
            border-bottom: none;
        }
        
        .course-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .course-type {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .type-required {
            background: var(--primary-color);
            color: white;
        }
        
        .type-elective {
            background: var(--success-color);
            color: white;
        }
        
        .type-exam {
            background: var(--error-color);
            color: white;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .course-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .legend {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .legend-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .legend-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .legend-items {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .legend-dot {
            width: 12px;
            height: 3px;
            border-radius: 1.5px;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学期课程日历</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="calendar-header">
            <div class="header-title">学期课程日历</div>
            <div class="header-subtitle">查看课程安排的日历视图</div>
        </div>
        
        <!-- 日历控制 -->
        <div class="calendar-controls">
            <div class="control-btn" onclick="previousMonth();">
                <i class="ace-icon fa fa-chevron-left"></i>
            </div>
            <div class="current-month" id="currentMonth">2024年3月</div>
            <div class="control-btn" onclick="nextMonth();">
                <i class="ace-icon fa fa-chevron-right"></i>
            </div>
        </div>
        
        <!-- 日历容器 -->
        <div class="calendar-container">
            <div class="calendar-weekdays">
                <div class="weekday">日</div>
                <div class="weekday">一</div>
                <div class="weekday">二</div>
                <div class="weekday">三</div>
                <div class="weekday">四</div>
                <div class="weekday">五</div>
                <div class="weekday">六</div>
            </div>
            <div class="calendar-days" id="calendarDays">
                <!-- 日历日期将动态填充 -->
            </div>
        </div>
        
        <!-- 课程列表 -->
        <div class="course-list" id="courseList">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span id="selectedDate">选中日期课程</span>
                </div>
            </div>
            <div id="courseItems">
                <!-- 课程列表将动态填充 -->
            </div>
        </div>
        
        <!-- 图例 -->
        <div class="legend">
            <div class="legend-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>图例说明</span>
            </div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-dot" style="background: var(--primary-color);"></div>
                    <span>必修课</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background: var(--success-color);"></div>
                    <span>选修课</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background: var(--error-color);"></div>
                    <span>考试</span>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentDate = new Date();
        let courseData = {};
        let selectedDay = null;

        $(function() {
            initPage();
            loadCourseData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            renderCalendar();
        }

        // 加载课程数据
        function loadCourseData() {
            showLoading(true);
            
            $.ajax({
                url: "/student/calendarSemesterCurriculum/getCourseData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    courseData = data.courses || {};
                    renderCalendar();
                    showLoading(false);
                },
                error: function() {
                    showError('加载课程数据失败');
                    showLoading(false);
                }
            });
        }

        // 渲染日历
        function renderCalendar() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();
            
            // 更新月份显示
            $('#currentMonth').text(`${year}年${month + 1}月`);
            
            // 获取当月第一天和最后一天
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            
            // 获取第一天是星期几
            const startWeekday = firstDay.getDay();
            
            // 计算需要显示的天数
            const totalDays = Math.ceil((lastDay.getDate() + startWeekday) / 7) * 7;
            
            const container = $('#calendarDays');
            container.empty();
            
            for (let i = 0; i < totalDays; i++) {
                const dayDate = new Date(firstDay);
                dayDate.setDate(dayDate.getDate() + i - startWeekday);
                
                const dayHtml = createCalendarDay(dayDate, month);
                container.append(dayHtml);
            }
        }

        // 创建日历日期
        function createCalendarDay(date, currentMonth) {
            const day = date.getDate();
            const month = date.getMonth();
            const dateStr = formatDateKey(date);
            const isOtherMonth = month !== currentMonth;
            const isToday = isDateToday(date);
            const courses = courseData[dateStr] || [];
            const hasCourse = courses.length > 0;
            
            let classes = 'calendar-day';
            if (isOtherMonth) classes += ' other-month';
            if (isToday) classes += ' today';
            if (hasCourse) classes += ' has-course';
            
            let courseDots = '';
            courses.slice(0, 3).forEach(course => {
                const type = course.type || 'required';
                courseDots += `<div class="course-dot ${type}"></div>`;
            });
            
            return `
                <div class="${classes}" onclick="selectDay('${dateStr}')">
                    <div class="day-number">${day}</div>
                    <div class="day-courses">${courseDots}</div>
                </div>
            `;
        }

        // 选择日期
        function selectDay(dateStr) {
            selectedDay = dateStr;
            const courses = courseData[dateStr] || [];
            
            if (courses.length > 0) {
                renderCourseList(dateStr, courses);
                showCourseList();
            } else {
                hideCourseList();
            }
        }

        // 渲染课程列表
        function renderCourseList(dateStr, courses) {
            const date = new Date(dateStr);
            $('#selectedDate').text(`${date.getMonth() + 1}月${date.getDate()}日课程`);
            
            const container = $('#courseItems');
            container.empty();
            
            courses.forEach(course => {
                const courseHtml = createCourseItem(course);
                container.append(courseHtml);
            });
        }

        // 创建课程项
        function createCourseItem(course) {
            const type = course.type || 'required';
            const typeClass = `type-${type}`;
            const typeText = getTypeText(type);
            
            return `
                <div class="course-item">
                    <div class="course-basic">
                        <div class="course-name">${course.name}</div>
                        <div class="course-type ${typeClass}">${typeText}</div>
                    </div>
                    <div class="course-details">
                        <div class="course-detail-item">
                            <span>时间:</span>
                            <span>${course.time}</span>
                        </div>
                        <div class="course-detail-item">
                            <span>地点:</span>
                            <span>${course.location}</span>
                        </div>
                        <div class="course-detail-item">
                            <span>教师:</span>
                            <span>${course.teacher}</span>
                        </div>
                        <div class="course-detail-item">
                            <span>学分:</span>
                            <span>${course.credits}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取类型文本
        function getTypeText(type) {
            switch(type) {
                case 'required': return '必修';
                case 'elective': return '选修';
                case 'exam': return '考试';
                default: return '其他';
            }
        }

        // 上一月
        function previousMonth() {
            currentDate.setMonth(currentDate.getMonth() - 1);
            renderCalendar();
        }

        // 下一月
        function nextMonth() {
            currentDate.setMonth(currentDate.getMonth() + 1);
            renderCalendar();
        }

        // 显示课程列表
        function showCourseList() {
            $('#courseList').addClass('show');
        }

        // 隐藏课程列表
        function hideCourseList() {
            $('#courseList').removeClass('show');
        }

        // 格式化日期键
        function formatDateKey(date) {
            return date.toISOString().split('T')[0];
        }

        // 判断是否是今天
        function isDateToday(date) {
            const today = new Date();
            return date.toDateString() === today.toDateString();
        }

        // 刷新数据
        function refreshData() {
            loadCourseData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
