<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>选择培养方案</title>
<style type="text/css">
	.self-margin .header {
	    margin-top: 6px !important;
	    margin-bottom: 10px !important;
	    padding-bottom: 4px !important;
	    border-bottom: 1px solid #CCC;
	    line-height: 28px;
	}
	
	.header.grey {
	    border-bottom-color: #c3c3c3;
	}
	h4.smaller {
	    font-size: 17px;
	}
	.header {
	    line-height: 28px;
	    margin-bottom: 16px;
	    margin-top: 18px;
	    padding-bottom: 4px;
	    border-bottom: 1px solid #CCC;
	}
	.grey {
	    color: #777 !important;
	}
	.lighter {
	    font-weight: lighter;
	}

	.btn.btn-round {
	    border-radius: 4px !important;
	}
	
	.btn.btn-bold, .btn.btn-round {
	    border-bottom-width: 2px;
	}
	
	.btn-group-xs>.btn, .btn-xs {
	    padding-top: 3px;
	    padding-bottom: 3px;
	    border-width: 3px;
	}
	.btn {
	    color: #FFF !important;
	    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
	    background-image: none !important;
	    border: 5px solid #FFF;
	    border-radius: 0;
	    box-shadow: none !important;
	    -webkit-transition: background-color .15s, border-color .15s, opacity .15s;
	    -o-transition: background-color .15s,border-color .15s,opacity .15s;
	    transition: background-color .15s, border-color .15s, opacity .15s;
	    vertical-align: middle;
	    margin: 0;
	    position: relative;
	    font-weight: 400;
	}
	.breadcrumb, .breadcrumb>li>a, .btn {
	    display: inline-block;
	}
	.btn, .dropdown-colorpicker a {
	    cursor: pointer;
	}
	.btn-group-xs>.btn, .btn-xs {
	    padding: 1px 5px;
	    font-size: 12px;
	    line-height: 1.3;
	    border-radius: 3px;
	}
	.btn, .btn-danger.active, .btn-danger:active, .btn-default.active, .btn-default:active, .btn-info.active, .btn-info:active, .btn-primary.active, .btn-primary:active, .btn-warning.active, .btn-warning:active, .btn.active, .btn:active, .dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover, .form-control, .navbar-toggle, .open>.dropdown-toggle.btn-danger, .open>.dropdown-toggle.btn-default, .open>.dropdown-toggle.btn-info, .open>.dropdown-toggle.btn-primary, .open>.dropdown-toggle.btn-warning {
	    background-image: none;
	}
	button, input, select, textarea {
	    font-family: inherit;
	    font-size: inherit;
	    line-height: inherit;
	}
	button, html input[type=button], input[type=reset], input[type=submit] {
	    -webkit-appearance: button;
	    cursor: pointer;
	}
	button, select {
	    text-transform: none;
	}
	button {
	    overflow: visible;
	}
	button, input, optgroup, select, textarea {
	    color: inherit;
	    font: inherit;
	    margin: 0;
	}
	.btn, .btn-default, .btn-default.focus, .btn-default:focus, .btn.focus, .btn:focus {
	    background-color: #ABBAC3 !important;
	    border-color: #ABBAC3;
	}
	.btn-info, .btn-info.focus, .btn-info:focus {
	    background-color: #6FB3E0 !important;
	    border-color: #6FB3E0;
	}
	.btn-success, .btn-success.focus, .btn-success:focus {
	    background-color: #87B87F !important;
	    border-color: #87B87F;
	}

	.right_top_oper1 {
		float:right;
		font-size:14px;
		margin:0 20px;
		position: relative;
		top:-6px;
	}
	.btn-group-lg>.btn, .btn-lg {
	    border-width: 5px;
	    line-height: 1.4;
	    padding: 5px 16px 6px;
	}
</style>
<script src="/js/jQuery/jquery-3.4.1.min.js"></script>
<script type="text/javascript">
	function formsub(fajhh) {
		document.form.fajhh.value = fajhh;
		document.form.submit();
	}
	
	//加载培养方案
	$(function(){
		var pyfaList = eval('(${pyblist})');
		var pyfaCont = "";
		$.each(pyfaList,function(i,v){
			var tCont = "<button type='button' class='btn btn-lg btn-round ";
			if(v.xdlxmc=="主修") {
				tCont += " btn-success' style='margin-right:5px;margin-bottom:5px;' onclick='formsub(\""+v.id.fajhh+"\")'><i class='fa fa-book'></i> 📒"+v.famc+"("+v.xdlxmc+")</button>";
				pyfaCont = tCont + pyfaCont;
			} else {
				tCont += "' style='margin-right:5px;margin-bottom:5px;' onclick='formsub(\""+v.id.fajhh+"\")'><i class='fa fa-book'></i> 📒"+v.famc+"("+v.xdlxmc+")</button>";
				pyfaCont += tCont;
			}
			
		});
		
		$("#div_pyfaList").html(pyfaCont);
	});
</script>
</head>
<body>
	<div class="row">
        <div class="col-sm-12 self-margin">
			<div id="mainDIV">
				<h4 class="header smaller lighter grey" style="margin-top: 6px">
					<i class="fa fa-list"></i> 📒选择方案名称
					<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/student/courseSelect/courseSelectNotice/index'">去选课公告</button>
					<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/student/courseSelect/quitCourse/index'">去退课</button>
					<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/student/courseSelect/courseSelectResult/index'">去看选课结果</button>
					<span class="right_top_oper1" style="top: 0;">
						<button type="button" class="btn btn-round btn-xs btn-info" onclick="location.href='/'">关闭</button>
					</span>
				</h4>
				<div>
					<form name="form"
						action="/student/courseSelect/selectCourse/index"
						method="post">
						<input type="hidden" name="fajhh" value="" />
						<input type="hidden" name="xxbm" value="${xxbm}" />
						<input type="hidden" name="xkjdlx" value="${xkjdlx}" />
						<!-- 方案计划号 -->
						<div id="div_pyfaList"></div>
					</form>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
