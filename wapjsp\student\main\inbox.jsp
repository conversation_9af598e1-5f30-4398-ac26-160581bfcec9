<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="we" uri="/taglib" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>消息展示</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 消息展示页面样式 */
        .inbox-header {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .inbox-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .inbox-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .message-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
        }
        
        .tab-item {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            margin-right: var(--margin-xs);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .tab-item:last-child {
            margin-right: 0;
        }
        
        .tab-item:hover {
            background: var(--info-light);
            color: var(--info-dark);
            border-color: var(--info-color);
        }
        
        .tab-item.active {
            background: var(--info-color);
            color: white;
            border-color: var(--info-color);
        }
        
        .tab-item i {
            font-size: var(--font-size-base);
        }
        
        .messages-container {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .message-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .message-item:last-child {
            border-bottom: none;
        }
        
        .message-item:hover {
            background: var(--bg-tertiary);
        }
        
        .message-item.unread {
            background: var(--info-light);
            border-left: 4px solid var(--info-color);
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .message-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .message-sender {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .message-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .message-status {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .status-icon {
            font-size: var(--font-size-base);
            color: var(--text-disabled);
        }
        
        .status-icon.read {
            color: var(--warning-color);
        }
        
        .status-icon.unread {
            color: var(--info-color);
        }
        
        .message-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .message-expanded {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .expanded-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .expanded-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .btn-close {
            background: none;
            border: none;
            font-size: var(--font-size-large);
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
        }
        
        .expanded-sender {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: var(--margin-sm);
        }
        
        .sender-info {
            flex: 1;
        }
        
        .sender-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .sender-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .expanded-content {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .message-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            z-index: 1000;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .overlay-content {
            background: var(--bg-primary);
            border-radius: 8px;
            max-width: 400px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
        }
        
        @media (max-width: 480px) {
            .message-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .message-status {
                align-self: flex-end;
            }
            
            .expanded-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
            
            .btn-close {
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="choose" value="${choose}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">消息展示</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 消息展示头部 -->
        <div class="inbox-header">
            <div class="inbox-title">消息展示</div>
            <div class="inbox-desc">查看您的系统消息</div>
        </div>
        
        <!-- 消息标签页 -->
        <div class="message-tabs">
            <div class="tabs-header">
                <div class="tab-item ${empty choose || choose == '1' ? 'active' : ''}" onclick="switchTab('1');">
                    <i class="ace-icon fa fa-inbox"></i>
                    <span>未读</span>
                </div>
                <div class="tab-item ${choose == '2' ? 'active' : ''}" onclick="switchTab('2');">
                    <i class="ace-icon fa fa-check"></i>
                    <span>已读</span>
                </div>
                <div class="tab-item ${choose == '3' ? 'active' : ''}" onclick="switchTab('3');">
                    <i class="ace-icon fa fa-list"></i>
                    <span>全部</span>
                </div>
            </div>
            
            <div class="messages-container" id="messagesContainer">
                <c:if test="${not empty unRead}">
                    <c:forEach items="${unRead}" var="tbInfo" varStatus="s">
                        <div class="message-item ${tbInfo.status == '0' ? 'unread' : ''}" 
                             onclick="showMessage('${s.index}', '${tbInfo.id}', '${tbInfo.status}');">
                            <div class="message-header">
                                <div class="message-info">
                                    <div class="message-sender">${tbInfo.sender}</div>
                                    <div class="message-time">${we:getMinusDays(tbInfo.sendTime)}</div>
                                </div>
                                <div class="message-status">
                                    <i class="ace-icon fa fa-star-o status-icon ${tbInfo.status == '1' ? 'read' : 'unread'}"></i>
                                </div>
                            </div>
                            <div class="message-content">${tbInfo.message}</div>
                        </div>
                        
                        <!-- 隐藏的消息详情 -->
                        <div class="message-detail" id="messageDetail_${s.index}" style="display: none;">
                            <div class="sender">${tbInfo.sender}</div>
                            <div class="time">${we:getMinusDays(tbInfo.sendTime)}</div>
                            <div class="content">${tbInfo.message}</div>
                        </div>
                    </c:forEach>
                </c:if>
                
                <c:if test="${empty unRead}">
                    <div class="empty-state">
                        <i class="ace-icon fa fa-inbox"></i>
                        <div class="empty-state-text">暂无消息</div>
                        <div class="empty-state-desc">您目前没有任何消息</div>
                    </div>
                </c:if>
            </div>
        </div>
        
        <!-- 消息详情覆盖层 -->
        <div class="message-overlay" id="messageOverlay">
            <div class="overlay-content">
                <div class="message-expanded" id="messageExpanded" style="display: block; margin: 0; box-shadow: none;">
                    <div class="expanded-header">
                        <div class="expanded-title">消息详情</div>
                        <button class="btn-close" onclick="closeMessage();">
                            <i class="ace-icon fa fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="expanded-sender">
                        <div class="sender-info">
                            <div class="sender-name" id="expandedSender"></div>
                            <div class="sender-time" id="expandedTime"></div>
                        </div>
                    </div>
                    
                    <div class="expanded-content" id="expandedContent"></div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = '${empty choose ? "1" : choose}';

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换标签页
        function switchTab(choose) {
            currentTab = choose;
            showLoading(true);

            // 提交表单切换标签页
            const form = $('<form>', {
                method: 'post',
                action: '/main/show'
            });

            form.append($('<input>', {
                type: 'hidden',
                name: 'choose',
                value: choose
            }));

            $('body').append(form);
            form.submit();
        }

        // 显示消息详情
        function showMessage(index, messageId, status) {
            const messageDetail = $('#messageDetail_' + index);

            if (messageDetail.length > 0) {
                const sender = messageDetail.find('.sender').text();
                const time = messageDetail.find('.time').text();
                const content = messageDetail.find('.content').text();

                $('#expandedSender').text(sender);
                $('#expandedTime').text(time);
                $('#expandedContent').text(content);

                $('#messageOverlay').css('display', 'flex');

                // 如果是未读消息且不是已读标签页，标记为已读
                if (status === '0' && currentTab !== '2') {
                    markAsRead(messageId, index);
                }
            }
        }

        // 关闭消息详情
        function closeMessage() {
            $('#messageOverlay').hide();
        }

        // 点击覆盖层背景关闭
        $('#messageOverlay').click(function(e) {
            if (e.target === this) {
                closeMessage();
            }
        });

        // 标记消息为已读
        function markAsRead(messageId, index) {
            $.ajax({
                url: "/main/updateStatus/" + messageId,
                type: "post",
                success: function(response) {
                    // 更新消息状态显示
                    const messageItem = $('.message-item').eq(index);
                    messageItem.removeClass('unread');
                    messageItem.find('.status-icon').removeClass('unread').addClass('read');
                },
                error: function(xhr) {
                    console.log("更新消息状态失败");
                }
            });
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
