<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>选课通知</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 选课通知页面样式 */
        .notice-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .filter-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 4px;
        }
        
        .filter-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: transparent;
        }
        
        .filter-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .notice-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all var(--transition-base);
            position: relative;
        }
        
        .notice-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .notice-item.unread {
            border-left: 4px solid var(--primary-color);
        }
        
        .notice-item.important {
            border-left: 4px solid var(--error-color);
        }
        
        .notice-item.urgent {
            border-left: 4px solid var(--warning-color);
        }
        
        .notice-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .badge-new {
            background: var(--error-color);
            color: white;
        }
        
        .badge-hot {
            background: var(--warning-color);
            color: white;
        }
        
        .badge-top {
            background: var(--success-color);
            color: white;
        }
        
        .notice-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .notice-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .type-general {
            background: var(--info-color);
            color: white;
        }
        
        .type-course {
            background: var(--success-color);
            color: white;
        }
        
        .type-exam {
            background: var(--warning-color);
            color: white;
        }
        
        .type-system {
            background: var(--error-color);
            color: white;
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-sm);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .notice-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .notice-time {
            display: flex;
            align-items: center;
        }
        
        .notice-time i {
            margin-right: 4px;
        }
        
        .notice-stats {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .stat-item {
            display: flex;
            align-items: center;
        }
        
        .stat-item i {
            margin-right: 2px;
        }
        
        .notice-detail {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .notice-detail.show {
            transform: translateX(0);
        }
        
        .detail-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .detail-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .detail-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .detail-content {
            padding: var(--padding-md);
        }
        
        .detail-notice-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            line-height: var(--line-height-base);
        }
        
        .detail-notice-meta {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .meta-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--margin-xs);
        }
        
        .meta-row:last-child {
            margin-bottom: 0;
        }
        
        .detail-notice-content {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            line-height: var(--line-height-relaxed);
            white-space: pre-wrap;
        }
        
        .detail-notice-content img {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            margin: var(--margin-sm) 0;
        }
        
        .detail-notice-content a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .detail-notice-content a:hover {
            text-decoration: underline;
        }
        
        .detail-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-share {
            background: var(--info-color);
            color: white;
        }
        
        .btn-bookmark {
            background: var(--warning-color);
            color: white;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">选课通知</div>
            <div class="navbar-action" onclick="refreshNotices();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 通知头部 -->
        <div class="notice-header">
            <div class="header-title">选课通知公告</div>
            <div class="header-subtitle">及时了解选课相关信息</div>
        </div>

        <!-- 搜索框 -->
        <div class="search-section">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索通知标题或内容..."
                   onkeyup="searchNotices()" />
        </div>

        <!-- 筛选标签 -->
        <div class="filter-tabs">
            <div class="filter-tab active" onclick="filterNotices('all')">全部</div>
            <div class="filter-tab" onclick="filterNotices('unread')">未读</div>
            <div class="filter-tab" onclick="filterNotices('important')">重要</div>
            <div class="filter-tab" onclick="filterNotices('course')">选课</div>
        </div>

        <!-- 通知列表 -->
        <div class="container-mobile">
            <div id="noticeList">
                <!-- 通知列表将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-bell-o"></i>
                <div id="emptyMessage">暂无通知</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <!-- 通知详情页面 -->
    <div class="notice-detail" id="noticeDetail">
        <div class="detail-header">
            <div class="detail-back" onclick="closeNoticeDetail();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="detail-title">通知详情</div>
        </div>

        <div class="detail-content">
            <div class="detail-notice-title" id="detailTitle"></div>

            <div class="detail-notice-meta" id="detailMeta">
                <!-- 通知元信息 -->
            </div>

            <div class="detail-notice-content" id="detailContent">
                <!-- 通知内容 -->
            </div>
        </div>

        <div class="detail-actions">
            <button class="btn-mobile btn-share flex-1" onclick="shareNotice();">
                <i class="ace-icon fa fa-share"></i> 分享
            </button>
            <button class="btn-mobile btn-bookmark flex-1" onclick="bookmarkNotice();">
                <i class="ace-icon fa fa-bookmark"></i> 收藏
            </button>
        </div>
    </div>

    <script>
        // 全局变量
        let allNotices = [];
        let filteredNotices = [];
        let currentFilter = 'all';
        let currentNotice = null;

        $(function() {
            initPage();
            loadNotices();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载通知列表
        function loadNotices() {
            showLoading(true);

            $.ajax({
                url: "/student/courseSelectManagement/notices/getNotices",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allNotices = data.notices || [];
                    applyFilter();
                    showLoading(false);
                },
                error: function() {
                    showError('加载通知失败');
                    showLoading(false);
                }
            });
        }

        // 筛选通知
        function filterNotices(filter) {
            currentFilter = filter;

            // 更新标签状态
            $('.filter-tab').removeClass('active');
            $(event.target).addClass('active');

            applyFilter();
        }

        // 应用筛选条件
        function applyFilter() {
            const searchText = $('#searchInput').val().toLowerCase();

            filteredNotices = allNotices.filter(notice => {
                // 搜索过滤
                if (searchText && !notice.title.toLowerCase().includes(searchText) &&
                    !notice.content.toLowerCase().includes(searchText)) {
                    return false;
                }

                // 类型过滤
                switch(currentFilter) {
                    case 'unread':
                        return !notice.isRead;
                    case 'important':
                        return notice.isImportant;
                    case 'course':
                        return notice.type === 'course';
                    default:
                        return true;
                }
            });

            renderNoticeList();
        }

        // 搜索通知
        function searchNotices() {
            applyFilter();
        }

        // 渲染通知列表
        function renderNoticeList() {
            const container = $('#noticeList');
            container.empty();

            if (filteredNotices.length === 0) {
                showEmptyState('暂无符合条件的通知');
                return;
            } else {
                hideEmptyState();
            }

            filteredNotices.forEach(notice => {
                const noticeHtml = createNoticeItem(notice);
                container.append(noticeHtml);
            });
        }

        // 创建通知项
        function createNoticeItem(notice) {
            const readClass = notice.isRead ? '' : 'unread';
            const importantClass = notice.isImportant ? 'important' : '';
            const urgentClass = notice.isUrgent ? 'urgent' : '';
            const itemClass = `${readClass} ${importantClass} ${urgentClass}`.trim();

            const badge = getBadge(notice);
            const typeClass = getTypeClass(notice.type);
            const typeText = getTypeText(notice.type);

            return `
                <div class="notice-item ${itemClass}" onclick="showNoticeDetail('${notice.id}')">
                    ${badge}
                    <div class="notice-header-info">
                        <div class="notice-title">${notice.title}</div>
                        <div class="notice-type ${typeClass}">${typeText}</div>
                    </div>
                    <div class="notice-content">${notice.content}</div>
                    <div class="notice-meta">
                        <div class="notice-time">
                            <i class="ace-icon fa fa-clock-o"></i>
                            <span>${formatTime(notice.publishTime)}</span>
                        </div>
                        <div class="notice-stats">
                            <div class="stat-item">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>${notice.viewCount || 0}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取徽章
        function getBadge(notice) {
            if (notice.isNew) {
                return '<div class="notice-badge badge-new">新</div>';
            } else if (notice.isHot) {
                return '<div class="notice-badge badge-hot">热</div>';
            } else if (notice.isTop) {
                return '<div class="notice-badge badge-top">顶</div>';
            }
            return '';
        }

        // 获取类型样式类
        function getTypeClass(type) {
            switch(type) {
                case 'course': return 'type-course';
                case 'exam': return 'type-exam';
                case 'system': return 'type-system';
                default: return 'type-general';
            }
        }

        // 获取类型文本
        function getTypeText(type) {
            switch(type) {
                case 'course': return '选课';
                case 'exam': return '考试';
                case 'system': return '系统';
                default: return '通知';
            }
        }

        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '';

            const time = new Date(timeStr);
            const now = new Date();
            const diff = now - time;

            if (diff < 60000) { // 1分钟内
                return '刚刚';
            } else if (diff < 3600000) { // 1小时内
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) { // 1天内
                return Math.floor(diff / 3600000) + '小时前';
            } else if (diff < 604800000) { // 1周内
                return Math.floor(diff / 86400000) + '天前';
            } else {
                return time.toLocaleDateString();
            }
        }

        // 显示通知详情
        function showNoticeDetail(noticeId) {
            const notice = allNotices.find(n => n.id === noticeId);
            if (!notice) return;

            currentNotice = notice;

            // 填充详情内容
            $('#detailTitle').text(notice.title);
            $('#detailContent').html(notice.content);

            // 填充元信息
            const metaHtml = `
                <div class="meta-row">
                    <span>发布时间:</span>
                    <span>${new Date(notice.publishTime).toLocaleString()}</span>
                </div>
                <div class="meta-row">
                    <span>通知类型:</span>
                    <span>${getTypeText(notice.type)}</span>
                </div>
                <div class="meta-row">
                    <span>发布部门:</span>
                    <span>${notice.department || '教务处'}</span>
                </div>
                <div class="meta-row">
                    <span>浏览次数:</span>
                    <span>${notice.viewCount || 0}次</span>
                </div>
            `;
            $('#detailMeta').html(metaHtml);

            // 显示详情页面
            $('#noticeDetail').addClass('show');

            // 标记为已读
            markAsRead(noticeId);
        }

        // 关闭通知详情
        function closeNoticeDetail() {
            $('#noticeDetail').removeClass('show');
        }

        // 标记为已读
        function markAsRead(noticeId) {
            if (!noticeId) return;

            $.ajax({
                url: "/student/courseSelectManagement/notices/markAsRead",
                type: "post",
                data: { noticeId: noticeId },
                success: function() {
                    // 更新本地数据
                    const notice = allNotices.find(n => n.id === noticeId);
                    if (notice) {
                        notice.isRead = true;
                        notice.viewCount = (notice.viewCount || 0) + 1;
                    }

                    // 重新渲染列表
                    applyFilter();
                },
                error: function() {
                    console.log('标记已读失败');
                }
            });
        }

        // 分享通知
        function shareNotice() {
            if (!currentNotice) return;

            const shareText = `${currentNotice.title}\n\n${currentNotice.content.substring(0, 100)}...`;

            if (navigator.share) {
                navigator.share({
                    title: currentNotice.title,
                    text: shareText,
                    url: window.location.href
                }).catch(err => {
                    console.log('分享失败:', err);
                    fallbackShare(shareText);
                });
            } else {
                fallbackShare(shareText);
            }
        }

        // 备用分享方法
        function fallbackShare(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showSuccess('通知内容已复制到剪贴板');
                }).catch(() => {
                    showError('复制失败');
                });
            } else {
                // 创建临时文本框
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();

                try {
                    document.execCommand('copy');
                    showSuccess('通知内容已复制到剪贴板');
                } catch (err) {
                    showError('复制失败');
                }

                document.body.removeChild(textArea);
            }
        }

        // 收藏通知
        function bookmarkNotice() {
            if (!currentNotice) return;

            $.ajax({
                url: "/student/courseSelectManagement/notices/bookmark",
                type: "post",
                data: { noticeId: currentNotice.id },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(data.bookmarked ? '已收藏' : '已取消收藏');
                        currentNotice.isBookmarked = data.bookmarked;

                        // 更新按钮文本
                        const $btn = $('.btn-bookmark');
                        if (data.bookmarked) {
                            $btn.html('<i class="ace-icon fa fa-bookmark"></i> 已收藏');
                        } else {
                            $btn.html('<i class="ace-icon fa fa-bookmark-o"></i> 收藏');
                        }
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 刷新通知
        function refreshNotices() {
            loadNotices();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#noticeList').hide();
            } else {
                $('#loadingState').hide();
                $('#noticeList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理返回键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' || e.keyCode === 27) {
                if ($('#noticeDetail').hasClass('show')) {
                    closeNoticeDetail();
                }
            }
        });

        // 处理触摸滑动关闭详情页
        let startX = 0;
        let startY = 0;

        $('#noticeDetail').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
            startY = e.originalEvent.touches[0].clientY;
        });

        $('#noticeDetail').on('touchmove', function(e) {
            if (!startX || !startY) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const currentY = e.originalEvent.touches[0].clientY;

            const diffX = currentX - startX;
            const diffY = currentY - startY;

            // 如果是向右滑动且水平距离大于垂直距离
            if (diffX > 50 && Math.abs(diffX) > Math.abs(diffY)) {
                closeNoticeDetail();
            }
        });

        $('#noticeDetail').on('touchend', function() {
            startX = 0;
            startY = 0;
        });
    </script>
</body>
</html>
