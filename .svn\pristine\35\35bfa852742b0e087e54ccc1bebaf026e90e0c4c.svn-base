package educationalAdministration.student.individualApplication.gradeChange.service.impl;

import com.urpSoft.core.service.BaseService;
import com.urpSoft.core.util.AuthUtil;
import educationalAdministration.dictionary.entity.*;
import educationalAdministration.student.common.service.CommonService;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.individualApplication.gradeChange.entity.CjxgXssqb;
import educationalAdministration.student.individualApplication.gradeChange.service.GradeChangeService;
import educationalAdministration.student.individualApplication.scoreCheck.entity.CjHcSqb;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2025-06-18 11:05
 * @Description
 * @Version v1.0
 */
@Service("gradeChangeService")
public class GradeChangeServiceImpl extends BaseService implements GradeChangeService {

    @PersistenceContext
    private EntityManager em;

    @Override
    public Object[] querySqById(String sqbh) {
        return (Object[]) em.createNativeQuery("SELECT a.sqbh, a.zxjxjhh,pn.xnxqmc(a.zxjxjhh) a.kch, pn.kcm(a.kch)," +
                "a.kxh,a.sqyy FROM CJXG_XSSQB  WHERE a.sqbh='"+ sqbh +"'").getSingleResult();
    }

    @Override
    public List<Object[]> queryFjById(String sqbh) {
        return em.createNativeQuery("select fjid, fjmc from SYS_SQFJB where sqbh='"+ sqbh +"'").getResultList();
    }

    @Override
    public Object[] queryFjKz() {
        return (Object[]) em.createNativeQuery("select qzscfj, fjsm, fjdx, fjlx from sys_ywhdkzb where id='10045'").getSingleResult();
    }

    @Override
    public List<Object[]> queryXsCj() {
        String xh = AuthUtil.getCurrentUser().getIdNumber();
        return em.createNativeQuery("SELECT a.zxjxjhh,a.kch,a.kxh,pn.xnxqmc(a.zxjxjhh) AS xnxqmc,pn.kcm(a.kch) AS kcm," +
                "a.kssj,a.kccj,(SELECT djmc FROM code_djcjzhb where id=djcj and DJLX_ID=djlx) djcj, jdcj,pn.xdfs(xdfsdm) xdfs FROM xs_cj_all a " +
                "WHERE a.tdkch IS NULL AND a.kch NOT IN(SELECT b.kch FROM cj_kctd_sqkcb b " +
                "WHERE sqbh IN (SELECT sqbh FROM cj_kctd_sqb WHERE sqzt = '2' AND xh = '"+xh+"')) AND a.kch NOT IN" +
                "(SELECT c.tdkch FROM cj_kctd_sqkcb c WHERE sqbh IN (SELECT sqbh FROM cj_kctd_sqb WHERE sqzt = '2' " +
                "AND xh = '"+xh+"')) AND a.xh = '"+xh+"'").getResultList();

    }

    @Override
    public List<Object[]> queryResult(String sqbh) {
        return em.createNativeQuery("SELECT (select name from urp_role where id=l.eal_role), pn.jsm(t.eal_user), " +
                "decode(eal_rslt, '0', '待审', '1', '拒绝', '2', '跳过', '3', '通过', '其他'), " +
                "eal_desc, to_char(to_date(t.eal_time, 'yyyymmddhh24miss'),'yyyy-mm-dd hh24:mi:ss'), eal_ip " +
                "FROM ea_result t, ea_process_link l where t.eap_code=l.eap_code and t.eal_code=l.eal_code " +
                "and apply_id='"+ sqbh +"' order by t.eal_order").getResultList();
    }

    @Override
    @Transactional
    public String saveInfo(HttpServletRequest request, String sqbh, String xnxq, String kch, String kxh, String sqyy,
                           String fjids, MultipartFile[] sqfj, String apply_status, String spjsh) {
        try {
            List<EaProcess> list = em.createQuery("from EaProcess where apply_type = '10045' and in_use = '1' order by eap_code").getResultList();
            if(list == null || list.size() == 0) {
                return "审批流程未维护！";
            }

            /*用户ip*/
            String czr = AuthUtil.getCurrentUser().getIdNumber();
            String czsj = CommonUtils.getCurrentTime(new Date(), "yyyy-MM-dd HH:mm:ss");

            boolean add = StringUtils.isBlank(sqbh);
            CjxgXssqb xsSqb = null;
            if(add) {
                xsSqb = new CjxgXssqb();
                sqbh = (String) em.createNativeQuery("select pkg_others.f_NewApplySerial from dual").getSingleResult();
                xsSqb.setSqbh(sqbh);
            } else {
                xsSqb = em.find(CjxgXssqb.class, sqbh);
            }

            xsSqb.setZxjxjhh(xnxq);
            xsSqb.setKch(kch);
            xsSqb.setKxh(kxh);
            xsSqb.setSqyy(sqyy);
            xsSqb.setSqlx("xgcj");
            xsSqb.setSqr(czr);
            xsSqb.setSqsj(czsj);
            xsSqb.setSqzt(apply_status);
            EaApplys applys = null;
            if(add) {
                applys = new EaApplys();
                applys.setApply_id(sqbh);
                applys.setApply_type("10045");
            } else {
                applys = em.find(EaApplys.class, sqbh);
            }
            applys.setApply_status(apply_status);
            applys.setUser_code(czr);
            applys.setCommit_dt(czsj.replace("-", "").replace(" ", "").replace(":", ""));
            applys.setZxjxjhh(CommonUtils.queryCurrentXnxq());
            if(!add && StringUtils.isNotBlank(fjids)) {
                em.createNativeQuery("delete from sys_sqfjb where sqbh='"+ sqbh +"' and fjid not in('"+ fjids.replace(",", "','") +"')").executeUpdate();
            }
            if(sqfj != null) {
                int fjLen = sqfj.length;
                for (int i = 0; i < fjLen; i++) {
                    MultipartFile file = sqfj[i];
                    if(!file.isEmpty()) {
                        SysSqfjb fjb = new SysSqfjb();
                        fjb.setFjid(UUID.randomUUID().toString().replace("-", ""));
                        fjb.setSqbh(sqbh);
                        fjb.setFjmc(file.getOriginalFilename());
                        fjb.setFjnr(file.getBytes());
                        em.persist(fjb);
                    }
                }
            }

            if(add) {
                em.persist(xsSqb);
                em.persist(applys);
            } else {
                em.merge(xsSqb);
                em.merge(applys);
            }

            if("1".equals(apply_status)) {
                EaProcess eaProcess = list.get(0);
                List<EaProcessLink> listLink = em.createQuery("from EaProcessLink where inUse = '1' AND id.eapCode = '"+ eaProcess.getEapCode() +"' order by ealOrder").getResultList();
                for (int i = 0; i < listLink.size(); i++) {
                    EaProcessLink eaProcessLink = listLink.get(i);
                    EaResult eaResult = new EaResult();
                    EaResultPK eaResultPK = new EaResultPK();
                    eaResultPK.setApplyId(sqbh);
                    eaResultPK.setEapCode(eaProcessLink.getId().getEapCode());
                    eaResultPK.setEalCode(eaProcessLink.getId().getEalCode());
                    eaResult.setId(eaResultPK);
                    eaResult.setEalOrder(eaProcessLink.getEalOrder());
                    eaResult.setOverEnabled(eaProcessLink.getOverEnabled());
                    eaResult.setEalRslt("0");
                    if(i == 0){
                        eaResult.setEalUser(spjsh);
                    }
                    em.persist(eaResult);
                    if ((eaProcess.getOverEnabled().equals("0") && eaProcessLink.getOverEnabled().equals("1")) || eaProcessLink.getOverEnabled().equals("0")) {
                        break;
                    }
                }
            }
            return "ok";
        } catch (Exception e) {
            e.printStackTrace();
            return "操作失败！";
        }
    }

    @Override
    public String revokeApply(HttpServletRequest request, String sqbh) {
        try {
            String czr = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
            String czsj = CommonUtils.getCurrentTime(new Date(), "yyyyMMddHHmmss");
            EaApplys applys = em.find(EaApplys.class, sqbh);
            if (applys != null) {
                applys.setApply_status("-1");
                applys.setRollback_dt(czsj);
                applys.setUser_code(czr);
                em.merge(applys);
            }
            return "ok";
        } catch (Exception e) {
            e.printStackTrace();
            return "操作失败！";
        }
    }
}
