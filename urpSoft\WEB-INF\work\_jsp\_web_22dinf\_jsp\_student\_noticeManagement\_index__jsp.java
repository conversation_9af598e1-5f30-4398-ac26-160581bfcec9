/*
 * JSP generated by Resin Professional 4.0.55 (built Wed, 29 Nov 2017 03:07:06 PST)
 */

package _jsp._web_22dinf._jsp._student._noticeManagement;
import javax.servlet.*;
import javax.servlet.jsp.*;
import javax.servlet.http.*;

public class _index__jsp extends com.caucho.jsp.JavaPage
{
  private static final java.util.HashMap<String,java.lang.reflect.Method> _jsp_functionMap = new java.util.HashMap<String,java.lang.reflect.Method>();
  private boolean _caucho_isDead;
  private boolean _caucho_isNotModified;
  private com.caucho.jsp.PageManager _jsp_pageManager;
  
  public void
  _jspService(javax.servlet.http.HttpServletRequest request,
              javax.servlet.http.HttpServletResponse response)
    throws java.io.IOException, javax.servlet.ServletException
  {
    javax.servlet.http.HttpSession session = request.getSession(true);
    com.caucho.server.webapp.WebApp _jsp_application = _caucho_getApplication();
    com.caucho.jsp.PageContextImpl pageContext = _jsp_pageManager.allocatePageContext(this, _jsp_application, request, response, null, session, 8192, true, false);

    TagState _jsp_state = new TagState();

    try {
      _jspService(request, response, pageContext, _jsp_application, session, _jsp_state);
    } catch (java.lang.Throwable _jsp_e) {
      pageContext.handlePageException(_jsp_e);
    } finally {
      _jsp_state.release();
      _jsp_pageManager.freePageContext(pageContext);
    }
  }
  
  private void
  _jspService(javax.servlet.http.HttpServletRequest request,
              javax.servlet.http.HttpServletResponse response,
              com.caucho.jsp.PageContextImpl pageContext,
              javax.servlet.ServletContext application,
              javax.servlet.http.HttpSession session,
              TagState _jsp_state)
    throws Throwable
  {
    javax.servlet.jsp.JspWriter out = pageContext.getOut();
    final javax.el.ELContext _jsp_env = pageContext.getELContext();
    javax.servlet.ServletConfig config = getServletConfig();
    javax.servlet.Servlet page = this;
    javax.servlet.jsp.tagext.JspTag _jsp_parent_tag = null;
    com.caucho.jsp.PageContextImpl _jsp_parentContext = pageContext;
    response.setContentType("text/html; charset=UTF-8");
    com.caucho.jsp.IteratorLoopSupportTag _jsp_loop_1 = null;

    out.write(_jsp_string0, 0, _jsp_string0.length);
    if (_caucho_expr_0.evalBoolean(_jsp_env)) {
      out.write(_jsp_string1, 0, _jsp_string1.length);
      _jsp_loop_1 = _jsp_state.get_jsp_loop_1(pageContext, _jsp_parent_tag);
      java.lang.Object _jsp_items_2 = _caucho_expr_1.evalObject(_jsp_env);
      java.util.Iterator _jsp_iter_2 = com.caucho.jstl.rt.CoreForEachTag.getIterator(_jsp_items_2);
      _jsp_loop_1.init(0, Integer.MAX_VALUE, 1, false, false, false);
      Object _jsp_status_2 = pageContext.putAttribute("listIndex", _jsp_loop_1);
      while (_jsp_iter_2.hasNext()) {
        Object _jsp_i_2 = _jsp_iter_2.next();
        _jsp_loop_1.setCurrent(_jsp_i_2, _jsp_iter_2.hasNext());
        pageContext.setAttribute("list", _jsp_i_2);
        out.write(_jsp_string2, 0, _jsp_string2.length);
        _caucho_expr_2.print(out, _jsp_env, false);
        out.write(_jsp_string3, 0, _jsp_string3.length);
        _caucho_expr_3.print(out, _jsp_env, false);
        out.write(_jsp_string4, 0, _jsp_string4.length);
        _caucho_expr_4.print(out, _jsp_env, false);
        out.write(_jsp_string5, 0, _jsp_string5.length);
      }
      pageContext.pageSetOrRemove("list", null);
      if (_jsp_status_2 instanceof javax.servlet.jsp.jstl.core.LoopTagStatus)pageContext.pageSetOrRemove("listIndex", _jsp_status_2);
      else
        pageContext.pageSetOrRemove("listIndex", null);
      out.write(_jsp_string6, 0, _jsp_string6.length);
    }
    out.write(_jsp_string7, 0, _jsp_string7.length);
    if (_caucho_expr_5.evalBoolean(_jsp_env)) {
      out.write(_jsp_string8, 0, _jsp_string8.length);
    }
    out.write(_jsp_string9, 0, _jsp_string9.length);
  }

  private com.caucho.make.DependencyContainer _caucho_depends
    = new com.caucho.make.DependencyContainer();

  public java.util.ArrayList<com.caucho.vfs.Dependency> _caucho_getDependList()
  {
    return _caucho_depends.getDependencies();
  }

  public void _caucho_addDepend(com.caucho.vfs.PersistentDependency depend)
  {
    super._caucho_addDepend(depend);
    _caucho_depends.add(depend);
  }

  protected void _caucho_setNeverModified(boolean isNotModified)
  {
    _caucho_isNotModified = true;
  }

  public boolean _caucho_isModified()
  {
    if (_caucho_isDead)
      return true;

    if (_caucho_isNotModified)
      return false;

    if (com.caucho.server.util.CauchoSystem.getVersionId() != -7019056920836842200L)
      return true;

    return _caucho_depends.isModified();
  }

  public long _caucho_lastModified()
  {
    return 0;
  }

  public void destroy()
  {
      _caucho_isDead = true;
      super.destroy();
    TagState tagState;
  }

  public void init(com.caucho.vfs.Path appDir)
    throws javax.servlet.ServletException
  {
    com.caucho.vfs.Path resinHome = com.caucho.server.util.CauchoSystem.getResinHome();
    com.caucho.vfs.MergePath mergePath = new com.caucho.vfs.MergePath();
    mergePath.addMergePath(appDir);
    mergePath.addMergePath(resinHome);
    com.caucho.loader.DynamicClassLoader loader;
    loader = (com.caucho.loader.DynamicClassLoader) getClass().getClassLoader();
    String resourcePath = loader.getResourcePathSpecificFirst();
    mergePath.addClassPath(resourcePath);
    com.caucho.vfs.Depend depend;
    depend = new com.caucho.vfs.Depend(appDir.lookup("WEB-INF/jsp/student/noticeManagement/index.jsp"), -4895741719126582L, true);
    _caucho_depends.add(depend);
    loader.addDependency(depend);
  }

  static {
    try {
    } catch (Exception e) {
      e.printStackTrace();
      throw new RuntimeException(e);
    }
  }

  final static class TagState {
    private com.caucho.jsp.IteratorLoopSupportTag _jsp_loop_1;

    final com.caucho.jsp.IteratorLoopSupportTag get_jsp_loop_1(PageContext pageContext, javax.servlet.jsp.tagext.JspTag _jsp_parent_tag) throws Throwable
    {
      if (_jsp_loop_1 == null) {
        _jsp_loop_1 = new com.caucho.jsp.IteratorLoopSupportTag();
        _jsp_loop_1.setParent((javax.servlet.jsp.tagext.Tag) null);
      }

      return _jsp_loop_1;
    }

    void release()
    {
    }
  }

  public java.util.HashMap<String,java.lang.reflect.Method> _caucho_getFunctionMap()
  {
    return _jsp_functionMap;
  }

  public void caucho_init(ServletConfig config)
  {
    try {
      com.caucho.server.webapp.WebApp webApp
        = (com.caucho.server.webapp.WebApp) config.getServletContext();
      init(config);
      if (com.caucho.jsp.JspManager.getCheckInterval() >= 0)
        _caucho_depends.setCheckInterval(com.caucho.jsp.JspManager.getCheckInterval());
      _jsp_pageManager = webApp.getJspApplicationContext().getPageManager();
      com.caucho.jsp.TaglibManager manager = webApp.getJspApplicationContext().getTaglibManager();
      manager.addTaglibFunctions(_jsp_functionMap, "spring", "http://www.springframework.org/tags");
      manager.addTaglibFunctions(_jsp_functionMap, "form", "http://www.springframework.org/tags/form");
      manager.addTaglibFunctions(_jsp_functionMap, "c", "http://java.sun.com/jsp/jstl/core");
      manager.addTaglibFunctions(_jsp_functionMap, "pager", "http://www.urpSoft.com/pagination");
      manager.addTaglibFunctions(_jsp_functionMap, "fn", "http://java.sun.com/jsp/jstl/functions");
      com.caucho.jsp.PageContextImpl pageContext = new com.caucho.jsp.InitPageContextImpl(webApp, this);
      _caucho_expr_0 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${list != null}");
      _caucho_expr_1 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${list}");
      _caucho_expr_2 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${list[0]}");
      _caucho_expr_3 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${list[1] }");
      _caucho_expr_4 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${list[2] }");
      _caucho_expr_5 = com.caucho.jsp.JspUtil.createExpr(pageContext.getELContext(), "${fn:length(list) == 0}");
    } catch (Exception e) {
      throw com.caucho.config.ConfigException.create(e);
    }
  }
  private static com.caucho.el.Expr _caucho_expr_0;
  private static com.caucho.el.Expr _caucho_expr_1;
  private static com.caucho.el.Expr _caucho_expr_2;
  private static com.caucho.el.Expr _caucho_expr_3;
  private static com.caucho.el.Expr _caucho_expr_4;
  private static com.caucho.el.Expr _caucho_expr_5;

  private final static char []_jsp_string4;
  private final static char []_jsp_string0;
  private final static char []_jsp_string9;
  private final static char []_jsp_string3;
  private final static char []_jsp_string1;
  private final static char []_jsp_string6;
  private final static char []_jsp_string5;
  private final static char []_jsp_string7;
  private final static char []_jsp_string2;
  private final static char []_jsp_string8;
  static {
    _jsp_string4 = "</a>\r\n						</td>\r\n						<td align=\"right\">".toCharArray();
    _jsp_string0 = "\r\n\r\n\r\n\r\n\r\n\r\n\r\n<html>\r\n<head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=GBK\">\r\n<title>\u9009\u8bfe\u516c\u544a</title>\r\n<style>\r\n	.self-margin .header {\r\n	    margin-top: 6px !important;\r\n	    margin-bottom: 10px !important;\r\n	    padding-bottom: 4px !important;\r\n	    border-bottom: 1px solid #CCC;\r\n	    line-height: 28px;\r\n	}\r\n	\r\n	.header.grey {\r\n	    border-bottom-color: #c3c3c3;\r\n	}\r\n	h4.smaller {\r\n	    font-size: 17px;\r\n	}\r\n	.header {\r\n	    line-height: 28px;\r\n	    margin-bottom: 16px;\r\n	    margin-top: 18px;\r\n	    padding-bottom: 4px;\r\n	    border-bottom: 1px solid #CCC;\r\n	}\r\n	.grey {\r\n	    color: #777 !important;\r\n	}\r\n	.lighter {\r\n	    font-weight: lighter;\r\n	}\r\n          \r\n          .btn.btn-round {\r\n	    border-radius: 4px !important;\r\n	}\r\n	\r\n	.btn.btn-bold, .btn.btn-round {\r\n	    border-bottom-width: 2px;\r\n	}\r\n	.btn, .btn-default, .btn-default.focus, .btn-default:focus, .btn.focus, .btn:focus {\r\n	    background-color: #ABBAC3 !important;\r\n	    border-color: #ABBAC3;\r\n	}\r\n	.btn-group-xs>.btn, .btn-xs {\r\n	    padding-top: 3px;\r\n	    padding-bottom: 3px;\r\n	    border-width: 3px;\r\n	}\r\n	.btn {\r\n	    color: #FFF !important;\r\n	    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);\r\n	    background-image: none !important;\r\n	    border: 5px solid #FFF;\r\n	    border-radius: 0;\r\n	    box-shadow: none !important;\r\n	    -webkit-transition: background-color .15s, border-color .15s, opacity .15s;\r\n	    -o-transition: background-color .15s,border-color .15s,opacity .15s;\r\n	    transition: background-color .15s, border-color .15s, opacity .15s;\r\n	    vertical-align: middle;\r\n	    margin: 0;\r\n	    position: relative;\r\n	    font-weight: 400;\r\n	}\r\n	.breadcrumb, .breadcrumb>li>a, .btn {\r\n	    display: inline-block;\r\n	}\r\n	.btn, .dropdown-colorpicker a {\r\n	    cursor: pointer;\r\n	}\r\n	.btn-group-xs>.btn, .btn-xs {\r\n	    padding: 1px 5px;\r\n	    font-size: 12px;\r\n	    line-height: 1.3;\r\n	    border-radius: 3px;\r\n	}\r\n	.btn, .btn-danger.active, .btn-danger:active, .btn-default.active, .btn-default:active, .btn-info.active, .btn-info:active, .btn-primary.active, .btn-primary:active, .btn-warning.active, .btn-warning:active, .btn.active, .btn:active, .dropdown-menu>.disabled>a:focus, .dropdown-menu>.disabled>a:hover, .form-control, .navbar-toggle, .open>.dropdown-toggle.btn-danger, .open>.dropdown-toggle.btn-default, .open>.dropdown-toggle.btn-info, .open>.dropdown-toggle.btn-primary, .open>.dropdown-toggle.btn-warning {\r\n	    background-image: none;\r\n	}\r\n	button, input, select, textarea {\r\n	    font-family: inherit;\r\n	    font-size: inherit;\r\n	    line-height: inherit;\r\n	}\r\n	button, html input[type=button], input[type=reset], input[type=submit] {\r\n	    -webkit-appearance: button;\r\n	    cursor: pointer;\r\n	}\r\n	button, select {\r\n	    text-transform: none;\r\n	}\r\n	button {\r\n	    overflow: visible;\r\n	}\r\n	button, input, optgroup, select, textarea {\r\n	    color: inherit;\r\n	    font: inherit;\r\n	    margin: 0;\r\n	}\r\n	.btn-info, .btn-info.focus, .btn-info:focus {\r\n	    background-color: #6FB3E0 !important;\r\n	    border-color: #6FB3E0;\r\n	}\r\n	.btn-success, .btn-success.focus, .btn-success:focus {\r\n	    background-color: #87B87F !important;\r\n	    border-color: #87B87F;\r\n	}\r\n	.alert {\r\n	    font-size: 14px;\r\n	}\r\n	\r\n	.alert, .well {\r\n	    border-radius: 0;\r\n	}\r\n	.alert-success {\r\n	    background-color: #dff0d8;\r\n	    border-color: #d6e9c6;\r\n	    color: #3c763d;\r\n	}\r\n	.alert {\r\n	    padding: 15px;\r\n	    border: 1px solid transparent;\r\n	    border-radius: 4px;\r\n	}\r\n	.alert, .thumbnail {\r\n	    margin-bottom: 20px;\r\n	}\r\n	\r\n	.alert .close {\r\n	    font-size: 16px;\r\n	}\r\n	\r\n	button.close {\r\n	    padding: 0;\r\n	    cursor: pointer;\r\n	    background: 0 0;\r\n	    border: 0;\r\n	    -webkit-appearance: none;\r\n	}\r\n	.close {\r\n	    float: right;\r\n	    font-size: 21px;\r\n	    color: #000;\r\n	    text-shadow: 0 1px 0 #fff;\r\n	    opacity: .2;\r\n	    filter: alpha(opacity=20);\r\n	}\r\n	.alert .alert-link, .close {\r\n	    font-weight: 700;\r\n	}\r\n	.badge, .close, .label {\r\n	    line-height: 1;\r\n	}\r\n	.right_top_oper1 {\r\n		float:right;\r\n		font-size:14px;\r\n		margin:0 20px;\r\n		position: relative;\r\n		top:-6px;\r\n	}\r\n</style>\r\n<script type=\"text/javascript\">\r\n	function queryNotice(tzid){\r\n		window.location.href=\"/student/courseSelect/courseSelectNotice/selectCourseNoticeDetail?tzId=\" + tzid;\r\n	}\r\n</script>\r\n</head>\r\n<body>\r\n	<h4 class=\"header smaller lighter grey\">\r\n		<i class=\"fa fa-bell\"></i>\r\n		\u23f0\u9009\u8bfe\u516c\u544a\r\n		<button type=\"button\" class=\"btn btn-round btn-xs btn-info\" onclick=\"location.href='/student/courseSelect/courseSelect/index'\">\u53bb\u9009\u8bfe</button>\r\n		<button type=\"button\" class=\"btn btn-round btn-xs btn-info\" onclick=\"location.href='/student/courseSelect/quitCourse/index'\">\u53bb\u9000\u8bfe</button>\r\n		<button type=\"button\" class=\"btn btn-round btn-xs btn-info\" onclick=\"location.href='/student/courseSelect/courseSelectResult/index'\">\u53bb\u770b\u9009\u8bfe\u7ed3\u679c</button>\r\n		<span class=\"right_top_oper1\">\r\n			<button type=\"button\" class=\"btn btn-round btn-xs btn-info\" onclick=\"location.href='/'\">\u5173\u95ed</button>\r\n		</span>\r\n	</h4>\r\n	".toCharArray();
    _jsp_string9 = "\r\n</body>\r\n</html>\r\n\r\n\r\n\r\n".toCharArray();
    _jsp_string3 = "')\">".toCharArray();
    _jsp_string1 = "\r\n		<div>\r\n			".toCharArray();
    _jsp_string6 = "\r\n		</div>\r\n	".toCharArray();
    _jsp_string5 = "</td>\r\n					</tr>\r\n				</table>\r\n			".toCharArray();
    _jsp_string7 = "\r\n	 ".toCharArray();
    _jsp_string2 = "\r\n				<table width=\"97%\" style=\"font-size:18px;\">\r\n					<tr style=\"cursor:pointer;\">\r\n						<td width=\"10px\" height=\"25\" align=\"right\">\u25cf  </td>\r\n						<td width=\"80%\"><a href='#' onclick=\"queryNotice('".toCharArray();
    _jsp_string8 = "\r\n		 <div class=\"alert alert-block alert-success\">\r\n			 <button type=\"button\" class=\"close\" data-dismiss=\"alert\">\r\n				 <i class=\"ace-icon fa fa-times\"></i>\r\n			 </button>\r\n			 <i class=\"ace-icon fa fa-check green\"></i>\r\n			 \u6682\u65f6\u6728\u6709\u5185\u5bb9\u5440~~\r\n		 </div>\r\n	 ".toCharArray();
  }
}
