<%@ page language="java" import="java.util.*" pageEncoding="utf-8" contentType="text/html; charset=UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>查看论文题目任务书</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 查看任务书页面样式 */
        .taskbook-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .taskbook-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .taskbook-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            background: var(--warning-light);
            color: var(--warning-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-section i {
            color: var(--warning-color);
            margin-right: 8px;
        }
        
        .content-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .content-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .content-title i {
            color: var(--primary-color);
        }
        
        .field-group {
            margin-bottom: var(--margin-lg);
        }
        
        .field-label {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            padding-left: var(--padding-sm);
        }
        
        .field-content {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
        }
        
        .field-input {
            width: 100%;
            padding: var(--padding-sm);
            border: none;
            background: transparent;
            font-size: var(--font-size-base);
            color: var(--text-primary);
            resize: none;
            outline: none;
        }
        
        .field-textarea {
            min-height: 100px;
            line-height: 1.5;
        }
        
        .field-options {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .option-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--padding-sm);
            background: var(--bg-primary);
            border-radius: 6px;
        }
        
        .option-input {
            margin: 0;
        }
        
        .option-label {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            flex: 1;
            line-height: 1.4;
        }
        
        .field-select {
            width: 100%;
            padding: var(--padding-sm);
            border: none;
            background: transparent;
            font-size: var(--font-size-base);
            color: var(--text-primary);
            outline: none;
            appearance: none;
        }
        
        .field-file {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .file-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .file-existing {
            background: var(--info-light);
            color: var(--info-dark);
            padding: var(--padding-sm);
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .file-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            flex: 1;
        }
        
        .file-link:hover {
            text-decoration: underline;
        }
        
        .file-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-file-action {
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: var(--font-size-base);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-file-action:hover {
            background: var(--primary-light);
        }
        
        .attachment-section {
            margin-top: var(--margin-lg);
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }
        
        .attachment-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            padding-left: var(--padding-sm);
        }
        
        .attachment-content {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
        }
        
        .attachment-empty {
            text-align: center;
            color: var(--text-disabled);
            font-size: var(--font-size-small);
            padding: var(--padding-lg);
        }
        
        .file-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .file-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .file-modal-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .file-modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .file-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .file-modal-body {
            height: calc(80vh - 60px);
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .file-viewer {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .file-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        @media (max-width: 480px) {
            .taskbook-header {
                margin: var(--margin-xs) var(--margin-sm);
                padding: var(--padding-md);
            }
            
            .content-section {
                margin: var(--margin-xs) var(--margin-sm);
            }
            
            .field-options {
                gap: var(--spacing-xs);
            }
            
            .file-modal-content {
                width: 95%;
                height: 90vh;
            }
            
            .file-modal-body {
                height: calc(90vh - 60px);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">查看论文题目任务书</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 任务书头部 -->
        <div class="taskbook-header">
            <div class="taskbook-title">查看论文题目任务书</div>
            <div class="taskbook-desc">查看任务书详细信息</div>
        </div>
        
        <!-- 错误信息 -->
        <c:if test="${not empty errorInfo}">
            <div class="notice-section">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                ${errorInfo}
            </div>
        </c:if>
        
        <c:if test="${empty errorInfo}">
            <!-- 任务书内容 -->
            <div class="content-section">
                <div class="content-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    ${tmxxb.tmmc_xs}任务书查看
                </div>
                
                <div id="param_form">
                    <!-- 动态生成字段 -->
                    <c:forEach items="${gcnrzdbList}" var="s" varStatus="status">
                        <div class="field-group">
                            <div class="field-label">${s.lxh}.${s.lmc}</div>
                            
                            <c:choose>
                                <!-- 多行文本 -->
                                <c:when test="${s.sjlx=='04'}">
                                    <div class="field-content">
                                        <textarea class="field-input field-textarea" name="${s.cid}" placeholder="${s.txsm}" readonly></textarea>
                                    </div>
                                </c:when>
                                
                                <!-- 文件上传 -->
                                <c:when test="${s.sjlx=='07'}">
                                    <div class="field-content">
                                        <div class="field-file">
                                            <input type="file" class="file-input" name="${s.cid}" fjlx="${s.fjlxzb.fjlx}" max="${s.fjlxzb.fjdxsx}" min="${s.fjlxzb.fjdxxx}" disabled/>
                                            <input type="hidden" name="fjlx-${s.cid}" value="1">
                                        </div>
                                    </div>
                                </c:when>
                                
                                <c:otherwise>
                                    <!-- 输入框 -->
                                    <c:if test="${s.srlx=='01'}">
                                        <div class="field-content">
                                            <c:if test="${s.xdqzqj=='1'}">
                                                <input class="field-input" type="text" name="${s.cid}" max="${s.qzfwb.qzsx}" min="${s.qzfwb.qzxx}" placeholder="取值范围${s.qzfwb.qzxx}~${s.qzfwb.qzsx}" readonly/>
                                            </c:if>
                                            <c:if test="${s.xdqzqj!='1'}">
                                                <input class="field-input" type="text" name="${s.cid}" readonly/>
                                            </c:if>
                                        </div>
                                    </c:if>
                                    
                                    <!-- 单选框 -->
                                    <c:if test="${s.srlx=='03'}">
                                        <div class="field-content">
                                            <div class="field-options">
                                                <c:forEach items="${s.mjzbList}" var="l">
                                                    <div class="option-item">
                                                        <input class="option-input" type="radio" value="${l.vid}" name="${s.cid}" disabled/>
                                                        <label class="option-label" title="${l.zms}">${l.zfz}</label>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </div>
                                    </c:if>
                                    
                                    <!-- 复选框 -->
                                    <c:if test="${s.srlx=='04'}">
                                        <div class="field-content">
                                            <div class="field-options">
                                                <c:forEach items="${s.mjzbList}" var="l">
                                                    <div class="option-item">
                                                        <input class="option-input" type="checkbox" name="${s.cid}" value="${l.vid}" disabled/>
                                                        <label class="option-label" title="${l.zms}">${l.zfz}</label>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </div>
                                    </c:if>
                                    
                                    <!-- 下拉单选 -->
                                    <c:if test="${s.srlx=='05'}">
                                        <div class="field-content">
                                            <select class="field-select" name="${s.cid}" disabled>
                                                <option value="">请选择</option>
                                                <c:forEach items="${s.mjzbList}" var="l">
                                                    <option value="${l.vid}">${l.zfz}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </c:if>
                                    
                                    <!-- 下拉复选 -->
                                    <c:if test="${s.srlx=='06'}">
                                        <div class="field-content">
                                            <select class="field-select" name="${s.cid}" multiple disabled>
                                                <c:forEach items="${s.mjzbList}" var="l">
                                                    <option value="${l.vid}">${l.zfz}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </c:if>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </c:forEach>
                    
                    <!-- 附件部分 -->
                    <div class="attachment-section">
                        <div class="attachment-title">${fn:length(gcnrzdbList) + 1}.附件</div>
                        <div class="attachment-content">
                            <c:if test="${rwsb != null && rwsb.wjmc != null && rwsb.shzt != '4'}">
                                <div class="file-existing">
                                    <a href="/student/personalManagement/processManagement/viewtaskbook/downLoad/${rwsb.rid}" class="file-link" target="_blank">${rwsb.wjmc}</a>
                                    <div class="file-actions">
                                        <c:if test="${fn:contains('jpg,jpeg,png,pdf', rwshz)}">
                                            <button class="btn-file-action" onclick="viewAppendix('${rwsb.rid}', '${rwsb.wjmc}');" title="查看附件">
                                                <i class="ace-icon fa fa-eye"></i>
                                            </button>
                                        </c:if>
                                    </div>
                                </div>
                            </c:if>
                            <c:if test="${rwsb == null || rwsb.wjmc == null || rwsb.shzt == '4'}">
                                <div class="attachment-empty">暂无附件</div>
                            </c:if>
                        </div>
                    </div>
                </div>
            </div>
        </c:if>
        
        <!-- 文件查看模态框 -->
        <div class="file-modal" id="fileModal">
            <div class="file-modal-content">
                <div class="file-modal-header">
                    <div class="file-modal-title" id="fileModalTitle">查看附件</div>
                    <button class="file-close" onclick="closeFileModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="file-modal-body" id="fileModalBody">
                    <!-- 动态加载文件内容 -->
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
            loadTaskBookData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载任务书数据
        function loadTaskBookData() {
            if ("${rwsb != null}" == "true") {
                try {
                    const gcnrzdbList = JSON.parse('${gcnrzdbList2}'.replace(/\r/g, "\\r").replace(/\n/g, "\\n"));

                    for (let i = 0; i < gcnrzdbList.length; i++) {
                        const gcnrzdb = gcnrzdbList[i];
                        const nrb = gcnrzdbList[i].rwsnrb;

                        if (gcnrzdb.srlx == "01") {
                            // 输入框
                            $("#param_form").find("input[name=" + nrb.cid + "]").val(nrb.lwbz);
                        } else if (gcnrzdb.srlx == "02") {
                            // 格式文本
                            $("#param_form").find("textarea[name=" + nrb.cid + "]").val(nrb.lwbz);
                        } else if (gcnrzdb.srlx == "03" || gcnrzdb.srlx == "07") {
                            // 枚举单选/列表单选
                            if (nrb.lwbz != "") {
                                $("#param_form").find("input[name=" + nrb.cid + "][value=" + nrb.lwbz + "]").prop("checked", true);
                            }
                        } else if (gcnrzdb.srlx == "04" || gcnrzdb.srlx == "08") {
                            // 枚举复选/列表复选
                            if (nrb.lwbz != "") {
                                const arr = nrb.lwbz.split(",");
                                for (let j = 0; j < arr.length; j++) {
                                    $("#param_form").find("input[name=" + nrb.cid + "][value=" + arr[j] + "]").prop("checked", true);
                                }
                            }
                        } else if (gcnrzdb.srlx == "05") {
                            // 下拉单选
                            if (nrb.lwbz != "") {
                                $("#param_form").find("select[name=" + nrb.cid + "]").find("option[value=" + nrb.lwbz + "]").attr("selected", "selected");
                            }
                        } else if (gcnrzdb.srlx == "06") {
                            // 下拉复选
                            if (nrb.lwbz != "") {
                                const arr = nrb.lwbz.split(",");
                                for (let j = 0; j < arr.length; j++) {
                                    $("#param_form").find("select[name=" + nrb.cid + "]").find("option[value=" + arr[j] + "]").attr("selected", "selected");
                                }
                            }
                        } else {
                            // 附件
                            if (nrb.lwbz != "") {
                                let css = "<div class='file-existing'>";
                                css += "<a href='#' class='file-link' onclick='doDownFj(\"" + nrb.nrid + "\");'>" + nrb.lwbz + "</a>";
                                css += "</div>";
                                $("#param_form").find("input[type=file][name=" + nrb.cid + "]").closest(".field-file").append(css);
                            }
                        }
                    }
                } catch (e) {
                    console.error("解析任务书数据失败:", e);
                }
            }
        }

        // 查看附件
        function viewAppendix(rwsid, fwmc) {
            showLoading(true);

            const url = "/student/personalManagement/processManagement/viewtaskbook/viewAppendix/" + rwsid;
            let content = "";

            if ("${rwshz}" == "pdf") {
                content = `<embed class="file-viewer" src="${url}" type="application/pdf">`;
            } else {
                content = `<img class="file-image" src="${url}" alt="${fwmc}">`;
            }

            $('#fileModalTitle').text(fwmc);
            $('#fileModalBody').html(content);
            $('#fileModal').fadeIn(300);

            showLoading(false);
        }

        // 关闭文件查看模态框
        function closeFileModal() {
            $('#fileModal').fadeOut(300);
            $('#fileModalBody').empty();
        }

        // 下载附件
        function doDownFj(nrid) {
            // 这里需要根据实际的下载接口来实现
            console.log("下载附件:", nrid);
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.file-modal', function(e) {
            if (e.target === this) {
                closeFileModal();
            }
        });
    </script>
</body>
</html>
