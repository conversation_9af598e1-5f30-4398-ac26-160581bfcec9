<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证消费记录</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证消费记录页面样式 */
        .consumption-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .consumption-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .summary-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .summary-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: var(--margin-xs);
        }
        
        .summary-number.balance {
            color: var(--success-color);
        }
        
        .summary-number.today {
            color: var(--warning-color);
        }
        
        .summary-number.month {
            color: var(--info-color);
        }
        
        .summary-number.total {
            color: var(--primary-color);
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .filter-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .filter-row {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-md);
        }
        
        .filter-group {
            flex: 1;
        }
        
        .filter-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .filter-input {
            width: 100%;
            min-height: 36px;
            padding: 6px 10px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .filter-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .filter-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-filter {
            background: var(--primary-color);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
        }
        
        .btn-reset {
            background: var(--text-disabled);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
        }
        
        .consumption-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .consumption-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .consumption-item:last-child {
            border-bottom: none;
        }
        
        .consumption-item:active {
            background: var(--bg-color-active);
        }
        
        .consumption-item.expense {
            border-left: 4px solid var(--error-color);
        }
        
        .consumption-item.income {
            border-left: 4px solid var(--success-color);
        }
        
        .consumption-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .consumption-merchant {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .consumption-amount {
            font-size: var(--font-size-base);
            font-weight: 600;
            white-space: nowrap;
        }
        
        .amount-expense {
            color: var(--error-color);
        }
        
        .amount-income {
            color: var(--success-color);
        }
        
        .consumption-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .consumption-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .consumption-time {
            grid-column: 1 / -1;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: var(--margin-xs);
        }
        
        .date-group {
            background: var(--bg-tertiary);
            padding: var(--padding-sm) var(--padding-md);
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .date-text {
            display: flex;
            align-items: center;
        }
        
        .date-text i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .date-summary {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .load-more {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: background-color var(--transition-base);
            border: 1px solid var(--border-primary);
        }
        
        .load-more:active {
            background: var(--bg-color-active);
        }
        
        .load-more-text {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .load-more-hint {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: var(--margin-xs);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">消费记录</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="consumption-header">
            <div class="header-title">消费记录</div>
            <div class="header-subtitle">查看学生证消费明细</div>
        </div>

        <!-- 消费汇总 -->
        <div class="consumption-summary">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>消费统计</span>
            </div>

            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-number balance" id="currentBalance">¥0.00</div>
                    <div class="summary-label">当前余额</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number today" id="todayConsumption">¥0.00</div>
                    <div class="summary-label">今日消费</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number month" id="monthConsumption">¥0.00</div>
                    <div class="summary-label">本月消费</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number total" id="totalConsumption">¥0.00</div>
                    <div class="summary-label">总消费</div>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="ace-icon fa fa-filter"></i>
                <span>筛选条件</span>
            </div>

            <div class="filter-row">
                <div class="filter-group">
                    <div class="filter-label">开始日期</div>
                    <input type="date" class="filter-input" id="startDate">
                </div>
                <div class="filter-group">
                    <div class="filter-label">结束日期</div>
                    <input type="date" class="filter-input" id="endDate">
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-group">
                    <div class="filter-label">消费类型</div>
                    <select class="filter-input" id="consumptionType">
                        <option value="">全部类型</option>
                        <option value="dining">餐饮消费</option>
                        <option value="shopping">购物消费</option>
                        <option value="library">图书馆</option>
                        <option value="transport">交通费用</option>
                        <option value="recharge">充值记录</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="filter-group">
                    <div class="filter-label">商户名称</div>
                    <input type="text" class="filter-input" id="merchantName" placeholder="输入商户名称">
                </div>
            </div>

            <div class="filter-actions">
                <button class="btn-filter" onclick="applyFilter();">筛选</button>
                <button class="btn-reset" onclick="resetFilter();">重置</button>
            </div>
        </div>

        <!-- 消费列表 -->
        <div class="consumption-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span>消费明细</span>
                </div>
                <div class="list-count" id="recordCount">0</div>
            </div>

            <div id="consumptionItems">
                <!-- 消费记录将动态填充 -->
            </div>
        </div>

        <!-- 加载更多 -->
        <div class="load-more" id="loadMore" onclick="loadMoreData();" style="display: none;">
            <div class="load-more-text">加载更多</div>
            <div class="load-more-hint">点击查看更多消费记录</div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-credit-card"></i>
            <div id="emptyMessage">暂无消费记录</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let consumptionData = [];
        let summaryData = {};
        let currentPage = 1;
        let hasMoreData = true;
        let isLoading = false;

        $(function() {
            initPage();
            loadSummaryData();
            loadConsumptionData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            initDateFilter();
        }

        // 初始化日期筛选
        function initDateFilter() {
            const today = new Date();
            const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

            $('#endDate').val(formatDateForInput(today));
            $('#startDate').val(formatDateForInput(oneMonthAgo));
        }

        // 格式化日期为输入框格式
        function formatDateForInput(date) {
            return date.toISOString().split('T')[0];
        }

        // 加载汇总数据
        function loadSummaryData() {
            $.ajax({
                url: "/student/studentCardConsumption/getSummaryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    summaryData = data.summary || {};
                    renderSummaryData();
                },
                error: function() {
                    // 使用模拟数据
                    summaryData = {
                        currentBalance: 156.78,
                        todayConsumption: 23.50,
                        monthConsumption: 456.80,
                        totalConsumption: 2345.60
                    };
                    renderSummaryData();
                }
            });
        }

        // 渲染汇总数据
        function renderSummaryData() {
            $('#currentBalance').text('¥' + (summaryData.currentBalance || 0).toFixed(2));
            $('#todayConsumption').text('¥' + (summaryData.todayConsumption || 0).toFixed(2));
            $('#monthConsumption').text('¥' + (summaryData.monthConsumption || 0).toFixed(2));
            $('#totalConsumption').text('¥' + (summaryData.totalConsumption || 0).toFixed(2));
        }

        // 加载消费数据
        function loadConsumptionData(reset = true) {
            if (isLoading) return;

            if (reset) {
                currentPage = 1;
                consumptionData = [];
                hasMoreData = true;
            }

            isLoading = true;
            showLoading(true);

            const filterData = getFilterData();
            filterData.page = currentPage;
            filterData.pageSize = 20;

            $.ajax({
                url: "/student/studentCardConsumption/getConsumptionData",
                type: "post",
                data: filterData,
                dataType: "json",
                success: function(data) {
                    const newData = data.records || [];

                    if (reset) {
                        consumptionData = newData;
                    } else {
                        consumptionData = consumptionData.concat(newData);
                    }

                    hasMoreData = newData.length >= filterData.pageSize;
                    renderConsumptionData();
                    updateLoadMoreButton();

                    isLoading = false;
                    showLoading(false);
                },
                error: function() {
                    // 使用模拟数据
                    const mockData = generateMockData(currentPage);

                    if (reset) {
                        consumptionData = mockData;
                    } else {
                        consumptionData = consumptionData.concat(mockData);
                    }

                    hasMoreData = mockData.length >= 20;
                    renderConsumptionData();
                    updateLoadMoreButton();

                    isLoading = false;
                    showLoading(false);
                }
            });
        }

        // 生成模拟数据
        function generateMockData(page) {
            const mockRecords = [];
            const merchants = ['食堂一楼', '食堂二楼', '超市', '图书馆', '打印店', '咖啡厅'];
            const types = ['dining', 'shopping', 'library', 'other'];

            for (let i = 0; i < 20; i++) {
                const date = new Date();
                date.setDate(date.getDate() - (page - 1) * 20 - i);

                mockRecords.push({
                    id: `${page}_${i}`,
                    merchant: merchants[Math.floor(Math.random() * merchants.length)],
                    amount: -(Math.random() * 50 + 5).toFixed(2),
                    type: types[Math.floor(Math.random() * types.length)],
                    transactionTime: date.toISOString(),
                    balance: (Math.random() * 200 + 100).toFixed(2),
                    transactionId: `T${Date.now()}${i}`
                });
            }

            return mockRecords;
        }

        // 获取筛选数据
        function getFilterData() {
            return {
                startDate: $('#startDate').val(),
                endDate: $('#endDate').val(),
                consumptionType: $('#consumptionType').val(),
                merchantName: $('#merchantName').val().trim()
            };
        }

        // 渲染消费数据
        function renderConsumptionData() {
            $('#recordCount').text(consumptionData.length);

            const container = $('#consumptionItems');
            container.empty();

            if (consumptionData.length === 0) {
                showEmptyState('暂无消费记录');
                return;
            } else {
                hideEmptyState();
            }

            // 按日期分组
            const groupedData = groupByDate(consumptionData);

            Object.keys(groupedData).forEach(date => {
                const records = groupedData[date];
                const dateHtml = createDateGroup(date, records);
                container.append(dateHtml);

                records.forEach(record => {
                    const recordHtml = createConsumptionItem(record);
                    container.append(recordHtml);
                });
            });
        }

        // 按日期分组
        function groupByDate(data) {
            const grouped = {};

            data.forEach(record => {
                const date = new Date(record.transactionTime).toDateString();
                if (!grouped[date]) {
                    grouped[date] = [];
                }
                grouped[date].push(record);
            });

            return grouped;
        }

        // 创建日期分组
        function createDateGroup(date, records) {
            const totalAmount = records.reduce((sum, record) => sum + parseFloat(record.amount), 0);
            const dateObj = new Date(date);
            const dateText = formatDateGroup(dateObj);

            return `
                <div class="date-group">
                    <div class="date-text">
                        <i class="ace-icon fa fa-calendar"></i>
                        <span>${dateText}</span>
                    </div>
                    <div class="date-summary">
                        ${records.length}笔 ${totalAmount < 0 ? '-' : '+'}¥${Math.abs(totalAmount).toFixed(2)}
                    </div>
                </div>
            `;
        }

        // 格式化日期分组
        function formatDateGroup(date) {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            if (date.toDateString() === today.toDateString()) {
                return '今天';
            } else if (date.toDateString() === yesterday.toDateString()) {
                return '昨天';
            } else {
                return date.toLocaleDateString();
            }
        }

        // 创建消费项
        function createConsumptionItem(record) {
            const amount = parseFloat(record.amount);
            const isExpense = amount < 0;
            const amountClass = isExpense ? 'amount-expense' : 'amount-income';
            const itemClass = isExpense ? 'expense' : 'income';
            const amountText = isExpense ? `-¥${Math.abs(amount).toFixed(2)}` : `+¥${amount.toFixed(2)}`;

            return `
                <div class="consumption-item ${itemClass}" onclick="showConsumptionDetail('${record.id}')">
                    <div class="consumption-basic">
                        <div class="consumption-merchant">${record.merchant}</div>
                        <div class="consumption-amount ${amountClass}">${amountText}</div>
                    </div>
                    <div class="consumption-details">
                        <div class="consumption-detail-item">
                            <span>类型:</span>
                            <span>${getConsumptionTypeText(record.type)}</span>
                        </div>
                        <div class="consumption-detail-item">
                            <span>余额:</span>
                            <span>¥${parseFloat(record.balance).toFixed(2)}</span>
                        </div>
                        <div class="consumption-time">
                            ${formatDateTime(record.transactionTime)}
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取消费类型文本
        function getConsumptionTypeText(type) {
            switch(type) {
                case 'dining': return '餐饮消费';
                case 'shopping': return '购物消费';
                case 'library': return '图书馆';
                case 'transport': return '交通费用';
                case 'recharge': return '充值记录';
                case 'other': return '其他';
                default: return '未知';
            }
        }

        // 显示消费详情
        function showConsumptionDetail(recordId) {
            const record = consumptionData.find(r => r.id === recordId);
            if (!record) return;

            const amount = parseFloat(record.amount);
            const isExpense = amount < 0;
            const amountText = isExpense ? `-¥${Math.abs(amount).toFixed(2)}` : `+¥${amount.toFixed(2)}`;

            let message = `消费详情\n\n`;
            message += `商户名称：${record.merchant}\n`;
            message += `消费金额：${amountText}\n`;
            message += `消费类型：${getConsumptionTypeText(record.type)}\n`;
            message += `交易时间：${formatDateTime(record.transactionTime)}\n`;
            message += `交易后余额：¥${parseFloat(record.balance).toFixed(2)}\n`;

            if (record.transactionId) {
                message += `交易号：${record.transactionId}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 应用筛选
        function applyFilter() {
            loadConsumptionData(true);
        }

        // 重置筛选
        function resetFilter() {
            $('#startDate').val('');
            $('#endDate').val('');
            $('#consumptionType').val('');
            $('#merchantName').val('');

            initDateFilter();
            loadConsumptionData(true);
        }

        // 加载更多数据
        function loadMoreData() {
            if (!hasMoreData || isLoading) return;

            currentPage++;
            loadConsumptionData(false);
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            if (hasMoreData && consumptionData.length > 0) {
                $('#loadMore').show();
            } else {
                $('#loadMore').hide();
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            const date = new Date(dateTimeStr);
            return date.toLocaleString();
        }

        // 刷新数据
        function refreshData() {
            loadSummaryData();
            loadConsumptionData(true);
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
