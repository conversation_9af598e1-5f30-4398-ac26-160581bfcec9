<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>优秀毕设</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 优秀项目页面样式 */
        .project-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .project-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .project-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--primary-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .projects-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .projects-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .projects-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .projects-title i {
            color: var(--success-color);
        }
        
        .project-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .project-item:last-child {
            border-bottom: none;
        }
        
        .project-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .project-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .project-content {
            flex: 1;
        }
        
        .project-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .project-name a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .project-student {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .project-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .batch-badge {
            background: var(--info-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .search-actions {
                flex-direction: column;
            }
            
            .project-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">优秀毕设</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 优秀项目头部 -->
        <div class="project-header">
            <div class="project-title">优秀毕设</div>
            <div class="project-desc">查看优秀毕业设计项目</div>
        </div>
        
        <!-- 查询条件 -->
        <div class="search-section">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form id="yxbsform" name="yxbsform" class="search-form">
                <div class="form-group">
                    <label class="form-label">题目名称</label>
                    <input type="text" name="tmmc" class="form-input" placeholder="请输入题目名称">
                </div>
                
                <div class="form-group">
                    <label class="form-label">优秀论文级别</label>
                    <select name="sfyxlw" class="form-select">
                        <option value="">全部</option>
                        <cache:query var="yxlwjbb" fields="yxlwjbm,yxlwjb" region="code_yxlwjbb" orderby="yxlwjbm asc"/>
                        <c:forEach items="${yxlwjbb}" var="yxlwjbb">
                            <option value="${yxlwjbb.yxlwjbm}">${yxlwjbb.yxlwjb}</option>
                        </c:forEach>
                    </select>
                </div>
            </form>
            
            <div class="search-actions">
                <button class="btn-mobile btn-search" onclick="searchProjects();">
                    <i class="ace-icon fa fa-search"></i>
                    <span>查询</span>
                </button>
            </div>
        </div>
        
        <!-- 优秀项目列表 -->
        <div class="projects-section">
            <div class="projects-header">
                <div class="projects-title">
                    <i class="ace-icon fa fa-list"></i>
                    优秀毕设列表
                </div>
            </div>
            
            <div id="projectsList">
                <!-- 动态加载项目列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreProjects();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无优秀项目数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let projectData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let searchParams = '';

        $(function() {
            initPage();
            loadProjects(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索项目
        function searchProjects() {
            loadProjects(1, true);
        }

        // 加载更多项目
        function loadMoreProjects() {
            if (hasMore) {
                loadProjects(currentPage + 1, false);
            }
        }

        // 加载项目数据
        function loadProjects(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
                searchParams = $('#yxbsform').serialize();
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/excellentProject/search",
                type: "post",
                data: searchParams + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            projectData = data.records;
                        } else {
                            projectData = projectData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = projectData.length < totalCount;

                        renderProjectsList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            projectData = [];
                            renderProjectsList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染项目列表
        function renderProjectsList(reset = false) {
            const container = $('#projectsList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : projectData.length - pageSize;
            const endIndex = projectData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (projectData[i]) {
                    const itemHtml = createProjectItem(projectData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建项目项目HTML
        function createProjectItem(item, index) {
            const titleHtml = item.FJID && item.FJID !== ""
                ? `<a href="/student/personalManagement/projectSelect/download?fjid=${item.FJID}&fjmc=${item.FJMC}" target="_blank" title="${item.FJMC}">${item.TMMC}</a>`
                : item.TMMC;

            return `
                <div class="project-item">
                    <div class="project-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="project-index">${index + 1}</div>
                            <div class="project-content">
                                <div class="project-name">${titleHtml}</div>
                                <div class="project-student">${item.XM || ''}（${item.XH || ''}）</div>
                            </div>
                        </div>
                        <div>
                            <span class="batch-badge">${item.PCMC || ''}</span>
                        </div>
                    </div>

                    <div class="project-details">
                        <div class="detail-item">
                            <span class="detail-label">指导教师</span>
                            <span>${item.JSM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">题目来源</span>
                            <span>${item.TMLYSM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">题目类型</span>
                            <span>${item.KTLBSM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">学生院系</span>
                            <span>${item.XSM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">专业</span>
                            <span>${item.ZYM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">班级</span>
                            <span>${item.BM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">年级</span>
                            <span>${item.NJMC || ''}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && projectData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (projectData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.projects-section').hide();
            } else {
                $('#emptyState').hide();
                $('.projects-section').show();
            }
        }

        // 刷新数据
        function refreshData() {
            loadProjects(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
