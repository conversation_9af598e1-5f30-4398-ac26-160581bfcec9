package educationalAdministration.student.individualApplication.gradeChange.entity;

import com.urpSoft.core.entity.BaseEntity;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date 2025-06-18 14:55
 * @Description
 * @Version v1.0
 */
@Entity
@Table(name = "CJXG_XSSQB")
public class CjxgXssqb extends BaseEntity {
    /**
     *
     */
    private static final long serialVersionUID = 8085392793150268489L;

    @Id
    @Column(name="SQBH")
    private String sqbh;

    @Column(name="ZXJXJHH")
    private String zxjxjhh;

    @Column(name="KCH")
    private String kch;

    @Column(name="KXH")
    private String kxh;

    @Column(name="SQR")
    private String sqr;

    @Column(name="SQSJ")
    private String sqsj;

    @Column(name="SQYY")
    private String sqyy;

    @Column(name="SQZT")
    private String sqzt;

    @Column(name="SQLX")
    private String sqlx;

    @Column(name="XGBZ")
    private String xgbz;

    public String getSqbh() {
        return sqbh;
    }

    public void setSqbh(String sqbh) {
        this.sqbh = sqbh;
    }

    public String getZxjxjhh() {
        return zxjxjhh;
    }

    public void setZxjxjhh(String zxjxjhh) {
        this.zxjxjhh = zxjxjhh;
    }

    public String getKch() {
        return kch;
    }

    public void setKch(String kch) {
        this.kch = kch;
    }

    public String getKxh() {
        return kxh;
    }

    public void setKxh(String kxh) {
        this.kxh = kxh;
    }

    public String getSqr() {
        return sqr;
    }

    public void setSqr(String sqr) {
        this.sqr = sqr;
    }

    public String getSqsj() {
        return sqsj;
    }

    public void setSqsj(String sqsj) {
        this.sqsj = sqsj;
    }

    public String getSqyy() {
        return sqyy;
    }

    public void setSqyy(String sqyy) {
        this.sqyy = sqyy;
    }

    public String getSqzt() {
        return sqzt;
    }

    public void setSqzt(String sqzt) {
        this.sqzt = sqzt;
    }

    public String getSqlx() {
        return sqlx;
    }

    public void setSqlx(String sqlx) {
        this.sqlx = sqlx;
    }

    public String getXgbz() {
        return xgbz;
    }

    public void setXgbz(String xgbz) {
        this.xgbz = xgbz;
    }

    @Override
    public boolean equals(Object obj) {
        return EqualsBuilder.reflectionEquals(this, obj);
    }
    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }
}
