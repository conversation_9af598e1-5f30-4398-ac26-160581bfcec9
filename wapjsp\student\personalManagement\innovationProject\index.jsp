<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>创新项目申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 创新项目申请页面样式 */
        .innovation-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .project-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .type-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .type-item:last-child {
            border-bottom: none;
        }
        
        .type-item:active {
            background: var(--bg-color-active);
        }
        
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .type-icon.research {
            background: var(--success-color);
        }
        
        .type-icon.startup {
            background: var(--info-color);
        }
        
        .type-icon.competition {
            background: var(--warning-color);
        }
        
        .type-icon.patent {
            background: var(--error-color);
        }
        
        .type-icon.social {
            background: var(--primary-color);
        }
        
        .type-content {
            flex: 1;
        }
        
        .type-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .type-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-limited {
            background: var(--warning-color);
            color: white;
        }
        
        .status-closed {
            background: var(--text-disabled);
            color: white;
        }
        
        .application-notice {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            margin-right: var(--margin-xs);
            color: var(--info-color);
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .notice-list {
            margin-top: var(--margin-sm);
            padding-left: var(--padding-md);
        }
        
        .notice-item {
            margin-bottom: 4px;
        }
        
        .my-projects {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .projects-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .projects-title {
            display: flex;
            align-items: center;
        }
        
        .projects-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .projects-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .project-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .project-item:last-child {
            border-bottom: none;
        }
        
        .project-item:active {
            background: var(--bg-color-active);
        }
        
        .project-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .project-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .project-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .project-item.in-progress {
            border-left: 4px solid var(--info-color);
        }
        
        .project-item.completed {
            border-left: 4px solid var(--primary-color);
        }
        
        .project-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .project-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .project-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .status-in-progress {
            background: var(--info-color);
            color: white;
        }
        
        .status-completed {
            background: var(--primary-color);
            color: white;
        }
        
        .project-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .project-summary {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
            margin-bottom: var(--margin-md);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .project-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .innovation-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .innovation-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .form-upload:hover {
            border-color: var(--primary-color);
            background: var(--bg-tertiary);
        }
        
        .upload-icon {
            font-size: var(--font-size-h2);
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-list {
            margin-top: var(--margin-sm);
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }
        
        .file-item:last-child {
            margin-bottom: 0;
        }
        
        .file-icon {
            margin-right: var(--margin-sm);
            color: var(--primary-color);
        }
        
        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">创新项目申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="innovation-header">
            <div class="header-title">创新项目申请</div>
            <div class="header-subtitle">申请各类创新创业项目，培养创新能力</div>
        </div>

        <!-- 项目类型 -->
        <div class="project-types">
            <div class="type-item" onclick="showInnovationForm('research')">
                <div class="type-icon research">
                    <i class="ace-icon fa fa-flask"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">科研项目</div>
                    <div class="type-desc">大学生创新创业训练计划项目</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showInnovationForm('startup')">
                <div class="type-icon startup">
                    <i class="ace-icon fa fa-rocket"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">创业项目</div>
                    <div class="type-desc">创新创业实践项目申请</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showInnovationForm('competition')">
                <div class="type-icon competition">
                    <i class="ace-icon fa fa-trophy"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">竞赛项目</div>
                    <div class="type-desc">学科竞赛和创新大赛项目</div>
                </div>
                <div class="type-status status-limited">有限制</div>
            </div>

            <div class="type-item" onclick="showInnovationForm('patent')">
                <div class="type-icon patent">
                    <i class="ace-icon fa fa-lightbulb-o"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">专利申请</div>
                    <div class="type-desc">发明创造专利申请支持</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showInnovationForm('social')">
                <div class="type-icon social">
                    <i class="ace-icon fa fa-users"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">社会创新</div>
                    <div class="type-desc">社会公益创新项目</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>
        </div>

        <!-- 申请须知 -->
        <div class="application-notice">
            <div class="notice-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>申请须知</span>
            </div>
            <div class="notice-content">
                申请创新项目前，请仔细阅读以下要求：
                <div class="notice-list">
                    <div class="notice-item">• 项目申请需要指导教师推荐</div>
                    <div class="notice-item">• 提交完整的项目计划书和相关材料</div>
                    <div class="notice-item">• 项目周期一般为1-2年</div>
                    <div class="notice-item">• 需要定期提交进度报告</div>
                    <div class="notice-item">• 项目结题需要提交成果报告</div>
                </div>
            </div>
        </div>

        <!-- 我的项目 -->
        <div class="my-projects">
            <div class="projects-header">
                <div class="projects-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的项目</span>
                </div>
                <div class="projects-count" id="projectsCount">0</div>
            </div>

            <div id="projectsList">
                <!-- 项目列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-lightbulb-o"></i>
            <div id="emptyMessage">暂无创新项目申请</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 创新项目申请表单 -->
    <div class="innovation-form" id="innovationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeInnovationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">创新项目申请</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">项目类型</div>
                    <input type="text" class="form-input" id="projectType" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业班级</div>
                    <input type="text" class="form-input" id="majorClass" readonly>
                </div>
            </div>

            <!-- 项目信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-lightbulb-o"></i>
                    <span>项目信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">项目名称</div>
                    <input type="text" class="form-input" id="projectName" placeholder="请输入项目名称">
                </div>

                <div class="form-group">
                    <div class="form-label required">项目类别</div>
                    <select class="form-input" id="projectCategory">
                        <option value="">请选择项目类别</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label required">项目简介</div>
                    <textarea class="form-input form-textarea" id="projectSummary"
                              placeholder="请简要介绍项目的背景、目标和意义..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label required">创新点</div>
                    <textarea class="form-input form-textarea" id="innovationPoints"
                              placeholder="请详细描述项目的创新点和技术特色..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">预期成果</div>
                    <textarea class="form-input form-textarea" id="expectedResults"
                              placeholder="请描述项目的预期成果和应用前景..."></textarea>
                </div>
            </div>

            <!-- 项目计划 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-calendar"></i>
                    <span>项目计划</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">项目周期</div>
                    <div style="display: flex; gap: 8px;">
                        <input type="date" class="form-input" id="startDate" style="flex: 1;" placeholder="开始日期">
                        <input type="date" class="form-input" id="endDate" style="flex: 1;" placeholder="结束日期">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label required">实施方案</div>
                    <textarea class="form-input form-textarea" id="implementationPlan"
                              placeholder="请详细描述项目的实施方案和技术路线..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">进度安排</div>
                    <textarea class="form-input form-textarea" id="schedule"
                              placeholder="请描述项目的时间安排和里程碑..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">预算申请</div>
                    <input type="number" class="form-input" id="budget" placeholder="请输入预算金额（元）" min="0">
                </div>
            </div>

            <!-- 团队信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-users"></i>
                    <span>团队信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">指导教师</div>
                    <select class="form-input" id="supervisor">
                        <option value="">请选择指导教师</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">团队成员</div>
                    <textarea class="form-input form-textarea" id="teamMembers"
                              placeholder="请列出团队成员信息（姓名、学号、专业、分工）..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">团队优势</div>
                    <textarea class="form-input form-textarea" id="teamAdvantages"
                              placeholder="请描述团队的专业背景和技术优势..."></textarea>
                </div>
            </div>

            <!-- 申请理由 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请理由</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">申请理由</div>
                    <textarea class="form-input form-textarea" id="applicationReason"
                              placeholder="请详细说明申请该项目的理由和动机..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">相关经验</div>
                    <textarea class="form-input form-textarea" id="relatedExperience"
                              placeholder="请描述相关的学习经历、项目经验或技能基础..."></textarea>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮箱地址</div>
                    <input type="email" class="form-input" id="email" placeholder="请输入邮箱地址">
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-paperclip"></i>
                    <span>附件材料</span>
                </div>

                <div class="form-group">
                    <div class="form-label">上传附件</div>
                    <div class="form-upload" onclick="selectFiles()">
                        <div class="upload-icon">
                            <i class="ace-icon fa fa-cloud-upload"></i>
                        </div>
                        <div class="upload-text">点击上传项目计划书、相关证明等材料</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                    <div class="file-list" id="fileList">
                        <!-- 文件列表将动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeInnovationForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitInnovationProject();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let myProjects = [];
        let currentProjectType = '';
        let uploadedFiles = [];
        let studentInfo = {};
        let supervisors = [];
        let projectCategories = {};

        $(function() {
            initPage();
            loadStudentInfo();
            loadSupervisors();
            loadProjectCategories();
            loadMyProjects();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFileUpload();
        }

        // 绑定文件上传
        function bindFileUpload() {
            $('#fileInput').change(function() {
                handleFileSelect(this.files);
            });
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/personalManagement/innovationProject/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 加载指导教师
        function loadSupervisors() {
            $.ajax({
                url: "/student/personalManagement/innovationProject/getSupervisors",
                type: "post",
                dataType: "json",
                success: function(data) {
                    supervisors = data.supervisors || [];
                    renderSupervisorOptions();
                },
                error: function() {
                    console.log('加载指导教师失败');
                }
            });
        }

        // 渲染指导教师选项
        function renderSupervisorOptions() {
            const select = $('#supervisor');
            select.find('option:not(:first)').remove();

            supervisors.forEach(supervisor => {
                select.append(`<option value="${supervisor.id}">${supervisor.name} - ${supervisor.title}</option>`);
            });
        }

        // 加载项目类别
        function loadProjectCategories() {
            $.ajax({
                url: "/student/personalManagement/innovationProject/getProjectCategories",
                type: "post",
                dataType: "json",
                success: function(data) {
                    projectCategories = data.categories || {};
                },
                error: function() {
                    console.log('加载项目类别失败');
                }
            });
        }

        // 加载我的项目
        function loadMyProjects() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/innovationProject/getMyProjects",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myProjects = data.projects || [];
                    renderProjectsList();
                    updateProjectsCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载项目列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染项目列表
        function renderProjectsList() {
            const container = $('#projectsList');
            container.empty();

            if (myProjects.length === 0) {
                showEmptyState('暂无创新项目申请');
                return;
            } else {
                hideEmptyState();
            }

            myProjects.forEach(project => {
                const projectHtml = createProjectItem(project);
                container.append(projectHtml);
            });
        }

        // 创建项目项
        function createProjectItem(project) {
            const statusClass = getProjectStatusClass(project.status);
            const statusText = getProjectStatusText(project.status);

            return `
                <div class="project-item ${statusClass}" onclick="showProjectDetail('${project.id}')">
                    <div class="project-basic">
                        <div class="project-title">${project.name}</div>
                        <div class="project-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="project-details">
                        <div class="detail-item">
                            <span>项目类型:</span>
                            <span>${getProjectTypeText(project.type)}</span>
                        </div>
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(project.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>项目周期:</span>
                            <span>${formatDate(project.startDate)} - ${formatDate(project.endDate)}</span>
                        </div>
                        <div class="detail-item">
                            <span>指导教师:</span>
                            <span>${project.supervisorName}</span>
                        </div>
                    </div>
                    <div class="project-summary">${project.summary}</div>
                    <div class="project-actions">
                        ${createProjectActions(project)}
                    </div>
                </div>
            `;
        }

        // 创建项目操作按钮
        function createProjectActions(project) {
            const canEdit = project.status === 'draft';

            let actions = [];

            actions.push(`<button class="btn-mobile btn-view" onclick="showProjectDetail('${project.id}')">查看</button>`);

            if (canEdit) {
                actions.push(`<button class="btn-mobile btn-edit" onclick="editProject('${project.id}')">编辑</button>`);
                actions.push(`<button class="btn-mobile btn-delete" onclick="deleteProject('${project.id}')">删除</button>`);
            }

            return actions.join('');
        }

        // 获取项目状态样式类
        function getProjectStatusClass(status) {
            switch(status) {
                case 'pending': return 'pending';
                case 'approved': return 'approved';
                case 'rejected': return 'rejected';
                case 'in-progress': return 'in-progress';
                case 'completed': return 'completed';
                default: return 'pending';
            }
        }

        // 获取项目状态文本
        function getProjectStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'approved': return '已立项';
                case 'rejected': return '已拒绝';
                case 'in-progress': return '进行中';
                case 'completed': return '已结题';
                default: return '未知';
            }
        }

        // 获取项目类型文本
        function getProjectTypeText(type) {
            switch(type) {
                case 'research': return '科研项目';
                case 'startup': return '创业项目';
                case 'competition': return '竞赛项目';
                case 'patent': return '专利申请';
                case 'social': return '社会创新';
                default: return '创新项目';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示创新项目申请表单
        function showInnovationForm(type) {
            currentProjectType = type;

            // 设置表单标题
            $('#formTitle').text(getProjectTypeText(type) + '申请');
            $('#projectType').val(getProjectTypeText(type));

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 加载对应类型的项目类别
            loadProjectCategoriesByType(type);

            // 清空表单
            resetForm();

            // 显示表单
            $('#innovationForm').addClass('show');
        }

        // 根据类型加载项目类别
        function loadProjectCategoriesByType(type) {
            const select = $('#projectCategory');
            select.find('option:not(:first)').remove();

            const categories = projectCategories[type] || [];
            categories.forEach(category => {
                select.append(`<option value="${category.id}">${category.name}</option>`);
            });
        }

        // 重置表单
        function resetForm() {
            $('#projectName').val('');
            $('#projectCategory').val('');
            $('#projectSummary').val('');
            $('#innovationPoints').val('');
            $('#expectedResults').val('');
            $('#startDate').val('');
            $('#endDate').val('');
            $('#implementationPlan').val('');
            $('#schedule').val('');
            $('#budget').val('');
            $('#supervisor').val('');
            $('#teamMembers').val('');
            $('#teamAdvantages').val('');
            $('#applicationReason').val('');
            $('#relatedExperience').val('');
            $('#contactPhone').val('');
            $('#email').val('');
            uploadedFiles = [];
            renderFileList();
        }

        // 关闭创新项目申请表单
        function closeInnovationForm() {
            $('#innovationForm').removeClass('show');
        }

        // 编辑项目
        function editProject(projectId) {
            const project = myProjects.find(p => p.id === projectId);
            if (!project) return;

            // 设置当前项目类型
            currentProjectType = project.type;

            // 填充表单
            $('#formTitle').text('编辑' + getProjectTypeText(project.type));
            $('#projectType').val(getProjectTypeText(project.type));

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 加载项目类别并填充数据
            loadProjectCategoriesByType(project.type);
            setTimeout(() => {
                fillProjectForm(project);
            }, 100);

            // 显示表单
            $('#innovationForm').addClass('show');
        }

        // 填充项目表单
        function fillProjectForm(project) {
            $('#projectName').val(project.name);
            $('#projectCategory').val(project.categoryId);
            $('#projectSummary').val(project.summary);
            $('#innovationPoints').val(project.innovationPoints);
            $('#expectedResults').val(project.expectedResults);
            $('#startDate').val(project.startDate);
            $('#endDate').val(project.endDate);
            $('#implementationPlan').val(project.implementationPlan);
            $('#schedule').val(project.schedule);
            $('#budget').val(project.budget);
            $('#supervisor').val(project.supervisorId);
            $('#teamMembers').val(project.teamMembers);
            $('#teamAdvantages').val(project.teamAdvantages);
            $('#applicationReason').val(project.applicationReason);
            $('#relatedExperience').val(project.relatedExperience);
            $('#contactPhone').val(project.contactPhone);
            $('#email').val(project.email);
        }

        // 删除项目
        function deleteProject(projectId) {
            const project = myProjects.find(p => p.id === projectId);
            if (!project) return;

            const message = `确定要删除项目"${project.name}"吗？\n\n删除后无法恢复。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doDeleteProject(projectId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doDeleteProject(projectId);
                }
            }
        }

        // 执行删除项目
        function doDeleteProject(projectId) {
            $.ajax({
                url: "/student/personalManagement/innovationProject/deleteProject",
                type: "post",
                data: { projectId: projectId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('项目删除成功');
                        loadMyProjects();
                    } else {
                        showError(data.message || '删除失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件选择
        function handleFileSelect(files) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (validateFile(file)) {
                    uploadedFiles.push({
                        id: Date.now() + i,
                        file: file,
                        name: file.name,
                        size: file.size
                    });
                }
            }
            renderFileList();
        }

        // 验证文件
        function validateFile(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['application/pdf', 'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'image/jpeg', 'image/jpg', 'image/png'];

            if (file.size > maxSize) {
                showError('文件大小不能超过10MB');
                return false;
            }

            if (!allowedTypes.includes(file.type)) {
                showError('只支持PDF、Word文档和图片格式');
                return false;
            }

            return true;
        }

        // 渲染文件列表
        function renderFileList() {
            const container = $('#fileList');
            container.empty();

            uploadedFiles.forEach(fileItem => {
                const fileHtml = `
                    <div class="file-item">
                        <i class="file-icon ace-icon fa fa-file"></i>
                        <span class="file-name">${fileItem.name}</span>
                        <i class="file-remove ace-icon fa fa-times" onclick="removeFile('${fileItem.id}')"></i>
                    </div>
                `;
                container.append(fileHtml);
            });
        }

        // 移除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id != fileId);
            renderFileList();
        }

        // 保存草稿
        function saveDraft() {
            if (!validateForm(false)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交创新项目申请
        function submitInnovationProject() {
            if (!validateForm(true)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = false;

            const message = `确定要提交${getProjectTypeText(currentProjectType)}申请吗？\n\n提交后将进入审核流程，请确保信息准确无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitFormData(formData, '项目申请提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    submitFormData(formData, '项目申请提交成功');
                }
            }
        }

        // 收集表单数据
        function collectFormData() {
            return {
                projectType: currentProjectType,
                projectName: $('#projectName').val(),
                projectCategory: $('#projectCategory').val(),
                projectSummary: $('#projectSummary').val(),
                innovationPoints: $('#innovationPoints').val(),
                expectedResults: $('#expectedResults').val(),
                startDate: $('#startDate').val(),
                endDate: $('#endDate').val(),
                implementationPlan: $('#implementationPlan').val(),
                schedule: $('#schedule').val(),
                budget: $('#budget').val(),
                supervisor: $('#supervisor').val(),
                teamMembers: $('#teamMembers').val(),
                teamAdvantages: $('#teamAdvantages').val(),
                applicationReason: $('#applicationReason').val(),
                relatedExperience: $('#relatedExperience').val(),
                contactPhone: $('#contactPhone').val(),
                email: $('#email').val(),
                attachments: uploadedFiles
            };
        }

        // 验证表单
        function validateForm(isSubmit) {
            if (!$('#projectName').val().trim()) {
                showError('请填写项目名称');
                return false;
            }

            if (!$('#projectCategory').val()) {
                showError('请选择项目类别');
                return false;
            }

            if (!$('#projectSummary').val().trim()) {
                showError('请填写项目简介');
                return false;
            }

            if (!$('#innovationPoints').val().trim()) {
                showError('请填写创新点');
                return false;
            }

            if (!$('#startDate').val()) {
                showError('请选择项目开始日期');
                return false;
            }

            if (!$('#endDate').val()) {
                showError('请选择项目结束日期');
                return false;
            }

            if (new Date($('#startDate').val()) >= new Date($('#endDate').val())) {
                showError('结束日期必须晚于开始日期');
                return false;
            }

            if (!$('#implementationPlan').val().trim()) {
                showError('请填写实施方案');
                return false;
            }

            if (!$('#supervisor').val()) {
                showError('请选择指导教师');
                return false;
            }

            if (!$('#applicationReason').val().trim()) {
                showError('请填写申请理由');
                return false;
            }

            if (!$('#contactPhone').val().trim()) {
                showError('请填写联系电话');
                return false;
            }

            return true;
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            $.ajax({
                url: "/student/personalManagement/innovationProject/submitProject",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        closeInnovationForm();
                        loadMyProjects();
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示项目详情
        function showProjectDetail(projectId) {
            const project = myProjects.find(p => p.id === projectId);
            if (!project) return;

            let message = `创新项目详情\n\n`;
            message += `项目名称：${project.name}\n`;
            message += `项目类型：${getProjectTypeText(project.type)}\n`;
            message += `申请时间：${formatDate(project.createTime)}\n`;
            message += `项目周期：${formatDate(project.startDate)} - ${formatDate(project.endDate)}\n`;
            message += `指导教师：${project.supervisorName}\n`;
            message += `当前状态：${getProjectStatusText(project.status)}\n`;
            message += `预算申请：${project.budget ? project.budget + '元' : '无'}\n\n`;
            message += `项目简介：\n${project.summary}\n\n`;
            message += `创新点：\n${project.innovationPoints}\n\n`;
            message += `申请理由：\n${project.applicationReason}\n`;

            if (project.reviewComment) {
                message += `\n审核意见：${project.reviewComment}\n`;
            }

            if (project.reviewTime) {
                message += `审核时间：${formatDate(project.reviewTime)}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 更新项目数量
        function updateProjectsCount() {
            $('#projectsCount').text(myProjects.length);
        }

        // 刷新数据
        function refreshData() {
            loadMyProjects();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.my-projects').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.my-projects').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.my-projects').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#innovationForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#innovationForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeInnovationForm();
            }
        });

        $('#innovationForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
