<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>高级移动端功能</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 高级移动端功能页面样式 */
        .advanced-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .advanced-features {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .features-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .features-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .feature-list {
            display: grid;
            gap: var(--spacing-md);
        }
        
        .feature-item {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .feature-item:active {
            background: var(--bg-color-active);
            transform: scale(0.98);
        }
        
        .feature-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .feature-info {
            display: flex;
            align-items: center;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: 18px;
        }
        
        .feature-content {
            flex: 1;
        }
        
        .feature-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .feature-description {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .feature-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-beta {
            background: var(--warning-color);
            color: white;
        }
        
        .status-experimental {
            background: var(--info-color);
            color: white;
        }
        
        .status-coming-soon {
            background: var(--text-disabled);
            color: white;
        }
        
        .pwa-features {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .pwa-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .pwa-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .pwa-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .pwa-card {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .pwa-card:active {
            background: var(--bg-color-active);
        }
        
        .pwa-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: var(--success-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--margin-sm);
            font-size: 24px;
        }
        
        .pwa-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .pwa-description {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .ai-features {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .ai-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .ai-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .ai-list {
            display: grid;
            gap: var(--spacing-sm);
        }
        
        .ai-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            background: var(--bg-tertiary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .ai-item:active {
            background: var(--bg-color-active);
        }
        
        .ai-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: var(--error-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-sm);
            font-size: 14px;
        }
        
        .ai-content {
            flex: 1;
        }
        
        .ai-name {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .ai-description {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .integration-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .integration-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .integration-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .integration-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .integration-btn {
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-primary);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-primary);
            text-decoration: none;
        }
        
        .integration-btn:active {
            background: var(--bg-color-active);
        }
        
        .integration-btn i {
            display: block;
            font-size: 24px;
            margin-bottom: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .integration-btn span {
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        @media (max-width: 480px) {
            .pwa-grid {
                grid-template-columns: 1fr;
            }
            
            .integration-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">高级移动端功能</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面头部 -->
        <div class="advanced-header">
            <div class="header-title">高级移动端功能</div>
            <div class="header-subtitle">探索前沿的移动端技术特性</div>
        </div>
        
        <!-- 高级功能 -->
        <div class="advanced-features">
            <div class="features-title">
                <i class="ace-icon fa fa-rocket"></i>
                <span>高级功能</span>
            </div>
            <div class="feature-list">
                <div class="feature-item" onclick="viewFeature('webrtc')">
                    <div class="feature-header">
                        <div class="feature-info">
                            <div class="feature-icon">
                                <i class="ace-icon fa fa-video-camera"></i>
                            </div>
                            <div class="feature-content">
                                <div class="feature-name">WebRTC视频通话</div>
                                <div class="feature-description">支持实时音视频通话和屏幕共享</div>
                            </div>
                        </div>
                        <div class="feature-status status-beta">Beta</div>
                    </div>
                </div>
                
                <div class="feature-item" onclick="viewFeature('ar')">
                    <div class="feature-header">
                        <div class="feature-info">
                            <div class="feature-icon">
                                <i class="ace-icon fa fa-cube"></i>
                            </div>
                            <div class="feature-content">
                                <div class="feature-name">AR增强现实</div>
                                <div class="feature-description">增强现实技术在教育场景中的应用</div>
                            </div>
                        </div>
                        <div class="feature-status status-experimental">实验性</div>
                    </div>
                </div>
                
                <div class="feature-item" onclick="viewFeature('biometric')">
                    <div class="feature-header">
                        <div class="feature-info">
                            <div class="feature-icon">
                                <i class="ace-icon fa fa-fingerprint"></i>
                            </div>
                            <div class="feature-content">
                                <div class="feature-name">生物识别认证</div>
                                <div class="feature-description">指纹、面部识别等生物识别登录</div>
                            </div>
                        </div>
                        <div class="feature-status status-coming-soon">即将推出</div>
                    </div>
                </div>
                
                <div class="feature-item" onclick="viewFeature('blockchain')">
                    <div class="feature-header">
                        <div class="feature-info">
                            <div class="feature-icon">
                                <i class="ace-icon fa fa-chain"></i>
                            </div>
                            <div class="feature-content">
                                <div class="feature-name">区块链学历认证</div>
                                <div class="feature-description">基于区块链的学历证书验证</div>
                            </div>
                        </div>
                        <div class="feature-status status-experimental">实验性</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- PWA功能 -->
        <div class="pwa-features">
            <div class="pwa-title">
                <i class="ace-icon fa fa-mobile"></i>
                <span>PWA功能</span>
            </div>
            <div class="pwa-grid">
                <div class="pwa-card" onclick="installPWA()">
                    <div class="pwa-icon">
                        <i class="ace-icon fa fa-download"></i>
                    </div>
                    <div class="pwa-name">安装应用</div>
                    <div class="pwa-description">添加到主屏幕</div>
                </div>
                
                <div class="pwa-card" onclick="enableOffline()">
                    <div class="pwa-icon">
                        <i class="ace-icon fa fa-cloud-download"></i>
                    </div>
                    <div class="pwa-name">离线模式</div>
                    <div class="pwa-description">离线访问功能</div>
                </div>
                
                <div class="pwa-card" onclick="enableNotifications()">
                    <div class="pwa-icon">
                        <i class="ace-icon fa fa-bell"></i>
                    </div>
                    <div class="pwa-name">推送通知</div>
                    <div class="pwa-description">消息推送服务</div>
                </div>
                
                <div class="pwa-card" onclick="syncData()">
                    <div class="pwa-icon">
                        <i class="ace-icon fa fa-refresh"></i>
                    </div>
                    <div class="pwa-name">后台同步</div>
                    <div class="pwa-description">数据自动同步</div>
                </div>
            </div>
        </div>
        
        <!-- AI功能 -->
        <div class="ai-features">
            <div class="ai-title">
                <i class="ace-icon fa fa-brain"></i>
                <span>AI智能功能</span>
            </div>
            <div class="ai-list">
                <div class="ai-item" onclick="viewAI('chatbot')">
                    <div class="ai-icon">
                        <i class="ace-icon fa fa-comments"></i>
                    </div>
                    <div class="ai-content">
                        <div class="ai-name">智能客服</div>
                        <div class="ai-description">AI驱动的智能问答系统</div>
                    </div>
                </div>
                
                <div class="ai-item" onclick="viewAI('recommendation')">
                    <div class="ai-icon">
                        <i class="ace-icon fa fa-lightbulb-o"></i>
                    </div>
                    <div class="ai-content">
                        <div class="ai-name">智能推荐</div>
                        <div class="ai-description">个性化课程和资源推荐</div>
                    </div>
                </div>
                
                <div class="ai-item" onclick="viewAI('analysis')">
                    <div class="ai-icon">
                        <i class="ace-icon fa fa-line-chart"></i>
                    </div>
                    <div class="ai-content">
                        <div class="ai-name">学习分析</div>
                        <div class="ai-description">AI学习行为分析和预测</div>
                    </div>
                </div>
                
                <div class="ai-item" onclick="viewAI('ocr')">
                    <div class="ai-icon">
                        <i class="ace-icon fa fa-eye"></i>
                    </div>
                    <div class="ai-content">
                        <div class="ai-name">文字识别</div>
                        <div class="ai-description">OCR文档识别和处理</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统集成 -->
        <div class="integration-section">
            <div class="integration-title">
                <i class="ace-icon fa fa-plug"></i>
                <span>系统集成</span>
            </div>
            <div class="integration-grid">
                <a href="/student/mobileFeatures" class="integration-btn">
                    <i class="ace-icon fa fa-mobile"></i>
                    <span>移动端功能</span>
                </a>
                <a href="/student/userExperience" class="integration-btn">
                    <i class="ace-icon fa fa-star"></i>
                    <span>用户体验</span>
                </a>
                <a href="/student/systemOverview" class="integration-btn">
                    <i class="ace-icon fa fa-dashboard"></i>
                    <span>系统概览</span>
                </a>
                <a href="/student/personalManagement" class="integration-btn">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人管理</span>
                </a>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            checkPWASupport();
        }

        // 检查PWA支持
        function checkPWASupport() {
            if ('serviceWorker' in navigator) {
                console.log('PWA supported');
            }
        }

        // 查看功能详情
        function viewFeature(featureType) {
            // 实现功能详情查看逻辑
            console.log('View feature:', featureType);
        }

        // 查看AI功能
        function viewAI(aiType) {
            // 实现AI功能查看逻辑
            console.log('View AI:', aiType);
        }

        // 安装PWA
        function installPWA() {
            // 实现PWA安装逻辑
            console.log('Install PWA');
        }

        // 启用离线模式
        function enableOffline() {
            // 实现离线模式启用逻辑
            console.log('Enable offline mode');
        }

        // 启用通知
        function enableNotifications() {
            // 实现通知启用逻辑
            if ("Notification" in window) {
                Notification.requestPermission();
            }
        }

        // 同步数据
        function syncData() {
            // 实现数据同步逻辑
            console.log('Sync data');
        }

        // 刷新数据
        function refreshData() {
            showLoading(true);
            
            setTimeout(function() {
                showLoading(false);
                showSuccess('高级功能数据已更新');
            }, 1000);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            // 这里可以添加成功提示的实现
            console.log('Success: ' + message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            // 移动端页面高度调整逻辑
        }
    </script>
</body>
</html>
