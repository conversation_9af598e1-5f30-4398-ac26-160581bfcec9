<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>活动报名</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 活动报名页面样式 */
        .activity-header {
            background: linear-gradient(135deg, var(--success-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .activity-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .activity-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .activity-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tabs-header {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
        }
        
        .tab-item {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            margin-right: var(--margin-xs);
        }
        
        .tab-item:last-child {
            margin-right: 0;
        }
        
        .tab-item:hover {
            background: var(--success-light);
            color: var(--success-dark);
            border-color: var(--success-color);
        }
        
        .tab-item.active {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }
        
        .tab-content {
            padding: var(--padding-md);
        }
        
        .activity-section {
            display: none;
        }
        
        .activity-section.active {
            display: block;
        }
        
        .search-container {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .search-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--success-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .form-label {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            font-size: var(--font-size-small);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--success-color);
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
        
        .btn-search {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            margin-top: var(--margin-sm);
        }
        
        .btn-search:hover {
            background: var(--success-dark);
        }
        
        .activities-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .activity-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border: 1px solid var(--border-primary);
        }
        
        .activity-item:last-child {
            margin-bottom: 0;
        }
        
        .activity-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .activity-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .activity-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .activity-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .detail-item i {
            color: var(--success-color);
            width: 12px;
        }
        
        .activity-time {
            background: var(--info-light);
            border-radius: 4px;
            padding: var(--padding-xs);
            font-size: var(--font-size-mini);
            color: var(--info-dark);
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .activity-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .action-btn.view {
            color: var(--info-color);
            border-color: var(--info-color);
        }
        
        .action-btn.view:hover {
            background: var(--info-light);
        }
        
        .action-btn.apply {
            color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .action-btn.apply:hover {
            background: var(--success-light);
        }
        
        .action-btn.edit {
            color: var(--warning-color);
            border-color: var(--warning-color);
        }
        
        .action-btn.edit:hover {
            background: var(--warning-light);
        }
        
        .action-btn.delete {
            color: var(--error-color);
            border-color: var(--error-color);
        }
        
        .action-btn.delete:hover {
            background: var(--error-light);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--success-light);
            border-color: var(--success-color);
            color: var(--success-dark);
        }
        
        .btn-page.active {
            background: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .activity-header-info {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
            
            .activity-details {
                grid-template-columns: 1fr;
            }
            
            .activity-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">活动报名</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 活动报名头部 -->
        <div class="activity-header">
            <div class="activity-title">劳动教育活动</div>
            <div class="activity-desc">参与劳动教育活动，提升实践能力</div>
        </div>
        
        <!-- 活动标签页 -->
        <div class="activity-tabs">
            <div class="tabs-header">
                <div class="tab-item active" onclick="switchTab(0);">我的活动</div>
                <div class="tab-item" onclick="switchTab(1);">可报名活动</div>
            </div>
            
            <div class="tab-content">
                <!-- 我的活动 -->
                <div class="activity-section active" id="myActivities">
                    <div class="search-container">
                        <div class="search-title">
                            <i class="ace-icon fa fa-search"></i>
                            搜索条件
                        </div>
                        <form class="search-form" id="mineForm">
                            <div class="form-group">
                                <label class="form-label">学年学期</label>
                                <select class="form-input" name="zxjxjhh">
                                    <cache:query var="view" region="jh_zxjxjhb_view" orderby="zxjxjhh desc" />
                                    <option value="">全部</option>
                                    <c:forEach items="${view}" var="view">
                                        <option value="${view.zxjxjhh}" <c:if test="${view.zxjxjhh==zxjxjhh}">selected</c:if>>${view.zxjxjhm}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">项目名称</label>
                                <input type="text" class="form-input" name="xmmc" placeholder="请输入项目名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动名称</label>
                                <input type="text" class="form-input" name="hdmc" placeholder="请输入活动名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">是否专题活动</label>
                                <select class="form-input" name="sfzthd">
                                    <option value="">全部</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                            <button type="button" class="btn-search" onclick="searchMyActivities();">
                                <i class="ace-icon fa fa-search"></i>
                                查询
                            </button>
                        </form>
                    </div>
                    
                    <div class="activities-list" id="myActivitiesList">
                        <!-- 动态加载我的活动列表 -->
                    </div>
                </div>
                
                <!-- 可报名活动 -->
                <div class="activity-section" id="availableActivities">
                    <div class="search-container">
                        <div class="search-title">
                            <i class="ace-icon fa fa-search"></i>
                            搜索条件
                        </div>
                        <form class="search-form" id="applyForm">
                            <div class="form-group">
                                <label class="form-label">学年学期</label>
                                <select class="form-input" name="zxjxjhh">
                                    <cache:query var="view" region="jh_zxjxjhb_view" orderby="zxjxjhh desc" />
                                    <option value="">全部</option>
                                    <c:forEach items="${view}" var="view">
                                        <option value="${view.zxjxjhh}" <c:if test="${view.zxjxjhh==zxjxjhh}">selected</c:if>>${view.zxjxjhm}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动来源</label>
                                <select class="form-input" name="hdlydm">
                                    <cache:query var="hdlyb" fields="hdlydm,hdlymc" region="ldjy_code_hdlyb" orderby="hdlydm asc" />
                                    <option value="">全部</option>
                                    <c:forEach items="${hdlyb}" var="hdlyb">
                                        <option value="${hdlyb.hdlydm}">${hdlyb.hdlymc}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动级别</label>
                                <select class="form-input" name="jbdm">
                                    <cache:query var="hdjbb" fields="jbdm,jbmc" region="ldjy_code_hdjbb" orderby="jbdm asc" />
                                    <option value="">全部</option>
                                    <c:forEach items="${hdjbb}" var="hdjbb">
                                        <option value="${hdjbb.jbdm}">${hdjbb.jbmc}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动类型</label>
                                <select class="form-input" name="hdlxdm">
                                    <cache:query var="hdlxb" fields="hdlxdm,hdlxmc" region="ldjy_code_hdlxb" orderby="hdlxdm asc" />
                                    <option value="">全部</option>
                                    <c:forEach items="${hdlxb}" var="hdlxb">
                                        <option value="${hdlxb.hdlxdm}">${hdlxb.hdlxmc}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">项目名称</label>
                                <input type="text" class="form-input" name="xmmc" placeholder="请输入项目名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动名称</label>
                                <input type="text" class="form-input" name="hdmc" placeholder="请输入活动名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动负责人</label>
                                <input type="text" class="form-input" name="jsm" placeholder="请输入负责人姓名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">是否专题活动</label>
                                <select class="form-input" name="sfzthd">
                                    <option value="">全部</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                            <button type="button" class="btn-search" onclick="searchAvailableActivities();">
                                <i class="ace-icon fa fa-search"></i>
                                查询
                            </button>
                        </form>
                    </div>
                    
                    <div class="activities-list" id="availableActivitiesList">
                        <!-- 动态加载可报名活动列表 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 0;
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;

        $(function() {
            initPage();
            loadMyActivities();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换标签页
        function switchTab(index) {
            currentTab = index;

            // 更新标签状态
            $('.tab-item').removeClass('active');
            $('.tab-item').eq(index).addClass('active');

            // 更新内容显示
            $('.activity-section').removeClass('active');
            $('.activity-section').eq(index).addClass('active');

            // 根据标签页加载对应数据
            if (index === 0) {
                loadMyActivities();
            } else {
                loadAvailableActivities();
            }
        }

        // 加载我的活动
        function loadMyActivities(page = 1) {
            currentPage = page;
            showLoading(true);

            const formData = $('#mineForm').serialize();

            $.ajax({
                url: "/student/laborEducation/eventRegistration/searchMineApplys",
                type: "post",
                data: formData + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    if (data && data.records) {
                        renderMyActivities(data.records);
                        renderPagination(data.pageContext);
                    } else {
                        showEmptyState('myActivitiesList', '暂无申请记录');
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染我的活动
        function renderMyActivities(activities) {
            const container = $('#myActivitiesList');

            if (!activities || activities.length === 0) {
                showEmptyState('myActivitiesList', '暂无申请记录');
                return;
            }

            let activitiesHtml = '';

            activities.forEach((activity, index) => {
                const statusInfo = getStatusInfo(activity.LQZT);
                const canDelete = activity.LQZT === "0";
                const canEdit = activity.CNT === "1" && activity.LQZT === "1" && (!activity.ZHKPZ || activity.ZHKPZ === "");

                activitiesHtml += `
                    <div class="activity-item">
                        <div class="activity-header-info">
                            <div class="activity-name">${activity.HDMC || '未设置'}</div>
                            <div class="activity-status ${statusInfo.class}">${statusInfo.text}</div>
                        </div>

                        <div class="activity-details">
                            <div class="detail-item">
                                <i class="ace-icon fa fa-tag"></i>
                                <span>${activity.XMMC || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-graduation-cap"></i>
                                <span>${getCreditsText(activity)}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-building"></i>
                                <span>${activity.ZBDW || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-star"></i>
                                <span>${activity.JBMC || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-list"></i>
                                <span>${activity.HDLXMC || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-calendar"></i>
                                <span>${activity.SQRQ || '未设置'}</span>
                            </div>
                        </div>

                        <div class="activity-time">
                            活动时间：${activity.HDKSSJ || '未设置'} ~ ${activity.HDJSSJ || '未设置'}
                        </div>

                        <div class="activity-actions">
                            ${canDelete ? `
                                <button class="action-btn delete" onclick="deleteApplication('${activity.SQID}');">
                                    <i class="ace-icon fa fa-trash"></i>
                                    删除
                                </button>
                            ` : ''}
                            ${canEdit ? `
                                <button class="action-btn edit" onclick="editActivity('${activity.SQID}');">
                                    <i class="ace-icon fa fa-edit"></i>
                                    维护记录
                                </button>
                            ` : `
                                <button class="action-btn view" onclick="viewActivity('apply', '${activity.SQID}');">
                                    <i class="ace-icon fa fa-eye"></i>
                                    查看
                                </button>
                            `}
                            <button class="action-btn view" onclick="viewActivity('xm', '${activity.XMID}');">
                                <i class="ace-icon fa fa-file-text"></i>
                                项目信息
                            </button>
                            <button class="action-btn view" onclick="viewActivity('hd', '${activity.HDID}');">
                                <i class="ace-icon fa fa-info-circle"></i>
                                活动信息
                            </button>
                        </div>
                    </div>
                `;
            });

            container.html(activitiesHtml);
        }

        // 加载可报名活动
        function loadAvailableActivities(page = 1) {
            currentPage = page;
            showLoading(true);

            const formData = $('#applyForm').serialize();

            $.ajax({
                url: "/student/laborEducation/eventRegistration/searchAllApply",
                type: "post",
                data: formData + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    if (data && data.records) {
                        renderAvailableActivities(data.records);
                        renderPagination(data.pageContext);
                    } else {
                        showEmptyState('availableActivitiesList', '暂无可报名活动');
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染可报名活动
        function renderAvailableActivities(activities) {
            const container = $('#availableActivitiesList');

            if (!activities || activities.length === 0) {
                showEmptyState('availableActivitiesList', '暂无可报名活动');
                return;
            }

            let activitiesHtml = '';

            activities.forEach((activity, index) => {
                const canApply = activity.YXSQ === "1";

                activitiesHtml += `
                    <div class="activity-item">
                        <div class="activity-header-info">
                            <div class="activity-name">${activity.HDMC || '未设置'}</div>
                            ${canApply ? '<div class="activity-status status-pending">可报名</div>' : ''}
                        </div>

                        <div class="activity-details">
                            <div class="detail-item">
                                <i class="ace-icon fa fa-tag"></i>
                                <span>${activity.XMMC || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-graduation-cap"></i>
                                <span>${getCreditsText(activity)}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-building"></i>
                                <span>${activity.ZBDW || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-star"></i>
                                <span>${activity.JBMC || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-list"></i>
                                <span>${activity.HDLXMC || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-users"></i>
                                <span>招募${activity.ZMRS || '0'}人</span>
                            </div>
                        </div>

                        <div class="activity-time">
                            报名时间：${activity.BMKSSJ || '未设置'} ~ ${activity.BMJSSJ || '未设置'}
                        </div>

                        <div class="activity-time">
                            活动时间：${activity.HDKSSJ || '未设置'} ~ ${activity.HDJSSJ || '未设置'}
                        </div>

                        <div class="activity-actions">
                            ${canApply ? `
                                <button class="action-btn apply" onclick="applyActivity('${activity.HDID}');">
                                    <i class="ace-icon fa fa-plus"></i>
                                    报名
                                </button>
                            ` : ''}
                            <button class="action-btn view" onclick="viewActivity('xm', '${activity.XMID}');">
                                <i class="ace-icon fa fa-file-text"></i>
                                项目信息
                            </button>
                            <button class="action-btn view" onclick="viewActivity('hd', '${activity.HDID}');">
                                <i class="ace-icon fa fa-info-circle"></i>
                                活动信息
                            </button>
                            ${activity.KPFSDM !== "01" ? `
                                <button class="action-btn view" onclick="viewActivity('xf', '${activity.XMID}');">
                                    <i class="ace-icon fa fa-eye"></i>
                                    学分标准
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            container.html(activitiesHtml);
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case "0":
                    return { text: "待录取", class: "status-pending" };
                case "1":
                    return { text: "已录取", class: "status-approved" };
                case "2":
                    return { text: "落选", class: "status-rejected" };
                default:
                    return { text: "未知", class: "status-pending" };
            }
        }

        // 获取学分文本
        function getCreditsText(activity) {
            if (activity.KPFSDM === "01") {
                return (activity.XF || '0') + '/' + (activity.XS || '0');
            } else {
                return '查看学分标准';
            }
        }

        // 显示空状态
        function showEmptyState(containerId, message) {
            const container = $('#' + containerId);
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-calendar-times-o"></i>
                    <div class="empty-state-text">${message}</div>
                    <div class="empty-state-desc">请调整搜索条件后重试</div>
                </div>
            `);
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            if (!pageContext || pageContext.totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            const prevPage = currentTab === 0 ? `loadMyActivities(${currentPage - 1})` : `loadAvailableActivities(${currentPage - 1})`;
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="${prevPage};">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                const firstPage = currentTab === 0 ? `loadMyActivities(1)` : `loadAvailableActivities(1)`;
                paginationHtml += `<button class="btn-page" onclick="${firstPage};">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                const pageFunc = currentTab === 0 ? `loadMyActivities(${i})` : `loadAvailableActivities(${i})`;
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="${pageFunc};">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                const lastPage = currentTab === 0 ? `loadMyActivities(${totalPages})` : `loadAvailableActivities(${totalPages})`;
                paginationHtml += `<button class="btn-page" onclick="${lastPage};">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            const nextPage = currentTab === 0 ? `loadMyActivities(${currentPage + 1})` : `loadAvailableActivities(${currentPage + 1})`;
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="${nextPage};">下一页</button>`;

            container.html(paginationHtml);
            $('#paginationContainer').show();
        }

        // 搜索我的活动
        function searchMyActivities() {
            loadMyActivities(1);
        }

        // 搜索可报名活动
        function searchAvailableActivities() {
            loadAvailableActivities(1);
        }

        // 申请活动
        function applyActivity(hdid) {
            if (confirm('是否确认申报当前活动？')) {
                showLoading(true);

                $.ajax({
                    url: "/student/laborEducation/eventRegistration/apply",
                    type: "post",
                    data: {
                        hdid: hdid,
                        tokenValue: $('#tokenValue').val()
                    },
                    dataType: "json",
                    success: function(data) {
                        $('#tokenValue').val(data.data.token);

                        if (data.status === 200) {
                            if (data.data.result.indexOf("/") !== -1) {
                                window.location.href = data.data.result;
                            } else if (data.data.result === "ok") {
                                showSuccess("申请成功！");
                                loadAvailableActivities(currentPage);
                                loadMyActivities(1);
                            } else {
                                showError(data.data.result);
                            }
                        } else {
                            showError(data.msg);
                        }
                    },
                    error: function(xhr) {
                        showError("申请失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 删除申请
        function deleteApplication(sqid) {
            if (confirm('是否确认删除当前申请？')) {
                showLoading(true);

                $.ajax({
                    url: "/student/laborEducation/eventRegistration/doDel",
                    type: "post",
                    data: {
                        sqid: sqid,
                        tokenValue: $('#tokenValue').val()
                    },
                    dataType: "json",
                    success: function(data) {
                        $('#tokenValue').val(data.data.token);

                        if (data.status === 200) {
                            if (data.data.result.indexOf("/") !== -1) {
                                window.location.href = data.data.result;
                            } else if (data.data.result === "ok") {
                                showSuccess("删除成功！");
                                loadMyActivities(currentPage);
                            } else {
                                showError(data.data.result);
                            }
                        } else {
                            showError(data.msg);
                        }
                    },
                    error: function(xhr) {
                        showError("删除失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 编辑活动记录
        function editActivity(sqid) {
            const url = `/student/laborEducation/eventRegistration/detail?sqid=${sqid}`;

            if (parent && parent.addTab) {
                parent.addTab('维护活动记录', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看活动信息
        function viewActivity(type, id) {
            const url = `/student/laborEducation/eventRegistration/view?type=${type}&id=${id}`;

            if (parent && parent.addTab) {
                parent.addTab('查看信息', url);
            } else {
                window.location.href = url;
            }
        }

        // 刷新数据
        function refreshData() {
            if (currentTab === 0) {
                loadMyActivities(currentPage);
            } else {
                loadAvailableActivities(currentPage);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
