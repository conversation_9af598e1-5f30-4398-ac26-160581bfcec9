<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>添加申请缓考</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 添加缓考申请页面样式 */
        .add-header {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .add-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .add-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .form-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-section {
            margin-bottom: var(--margin-lg);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            color: var(--success-color);
        }
        
        .course-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .info-row {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: var(--margin-sm);
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .info-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .required {
            color: var(--error-color);
        }
        
        .form-textarea {
            width: 100%;
            min-height: 120px;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            resize: vertical;
            box-sizing: border-box;
            font-family: inherit;
        }
        
        .form-textarea:focus {
            outline: none;
            border-color: var(--success-color);
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
        
        .file-upload-container {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            background: var(--bg-tertiary);
            transition: all var(--transition-base);
            cursor: pointer;
        }
        
        .file-upload-container:hover {
            border-color: var(--success-color);
            background: var(--success-light);
        }
        
        .file-upload-container.dragover {
            border-color: var(--success-color);
            background: var(--success-light);
        }
        
        .file-upload-icon {
            font-size: 48px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .file-upload-text {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .file-upload-desc {
            font-size: var(--font-size-small);
            color: var(--text-disabled);
        }
        
        .file-input {
            display: none;
        }
        
        .file-selected {
            background: var(--success-light);
            border: 1px solid var(--success-color);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-top: var(--margin-sm);
            display: none;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .file-icon {
            color: var(--success-color);
            font-size: var(--font-size-large);
        }
        
        .file-details {
            flex: 1;
        }
        
        .file-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .file-size {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
            padding: 4px;
        }
        
        .notice-container {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-top: var(--margin-md);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--warning-dark);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .notice-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .notice-item {
            font-size: var(--font-size-small);
            color: var(--warning-dark);
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .notice-item:last-child {
            margin-bottom: 0;
        }
        
        .notice-number {
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .form-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            flex: 1;
        }
        
        .btn-submit:hover {
            background: var(--success-dark);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            flex: 1;
        }
        
        .btn-cancel:hover {
            background: var(--text-secondary);
        }
        
        .error-message {
            color: var(--error-color);
            font-size: var(--font-size-small);
            margin-top: 4px;
            display: none;
        }
        
        @media (max-width: 480px) {
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <form name="addInfo" id="addInfo" enctype="multipart/form-data" method="post">
        <input type="hidden" name="id.zxjxjhh" value="${delayedExam[0]}">
        <input type="hidden" name="id.kch" value="${delayedExam[2]}">
        <input type="hidden" name="id.kxh" value="${delayedExam[4]}">
        <input type="hidden" name="id.xh" value="${delayedExam[5]}">
        <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
        
        <div class="page-mobile">
            <!-- 导航栏 -->
            <nav class="navbar-mobile">
                <div class="navbar-back" onclick="goBack();">
                    <i class="ace-icon fa fa-arrow-left"></i>
                </div>
                <div class="navbar-title">添加申请缓考</div>
                <div class="navbar-action">
                    <!-- 空白占位 -->
                </div>
            </nav>
            
            <!-- 添加申请头部 -->
            <div class="add-header">
                <div class="add-title">申请缓考</div>
                <div class="add-desc">填写缓考申请信息</div>
            </div>
            
            <!-- 表单容器 -->
            <div class="form-container">
                <!-- 课程信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="ace-icon fa fa-book"></i>
                        课程信息
                    </div>
                    
                    <div class="course-info">
                        <div class="info-row">
                            <div class="info-label">学年学期</div>
                            <div class="info-value">${delayedExam[1]}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">课程号</div>
                            <div class="info-value">${delayedExam[2]}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">课程名</div>
                            <div class="info-value">${delayedExam[3]}</div>
                        </div>
                        
                        <div class="info-row">
                            <div class="info-label">课序号</div>
                            <div class="info-value">${delayedExam[4]}</div>
                        </div>
                    </div>
                </div>
                
                <!-- 申请原因 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="ace-icon fa fa-edit"></i>
                        申请信息
                    </div>
                    
                    <div class="form-group">
                        <div class="form-label">
                            <span class="required">*</span>
                            <span>申请原因</span>
                        </div>
                        
                        <textarea class="form-textarea" name="sqyy" id="sqyy" placeholder="请详细说明申请缓考的原因"></textarea>
                        <div class="error-message" id="sqyyError">申请原因不能为空！</div>
                    </div>
                </div>
                
                <!-- 文件上传 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="ace-icon fa fa-upload"></i>
                        附件上传
                    </div>
                    
                    <div class="form-group">
                        <div class="form-label">上传申请附件</div>
                        
                        <div class="file-upload-container" onclick="$('#fileInput').click();">
                            <i class="ace-icon fa fa-cloud-upload file-upload-icon"></i>
                            <div class="file-upload-text">点击选择文件</div>
                            <div class="file-upload-desc">支持 doc、pdf、png、jpg、docx、rar、zip 格式</div>
                        </div>
                        
                        <input type="file" id="fileInput" name="fjnr" class="file-input" accept=".doc,.pdf,.png,.jpg,.docx,.rar,.zip">
                        
                        <div class="file-selected" id="fileSelected">
                            <div class="file-info">
                                <i class="ace-icon fa fa-file file-icon"></i>
                                <div class="file-details">
                                    <div class="file-name" id="fileName"></div>
                                    <div class="file-size" id="fileSize"></div>
                                </div>
                                <i class="ace-icon fa fa-times file-remove" onclick="removeFile();"></i>
                            </div>
                        </div>
                        
                        <div class="error-message" id="fileError">文件类型不符合要求！</div>
                    </div>
                    
                    <!-- 申报注意事项 -->
                    <div class="notice-container">
                        <div class="notice-title">
                            <i class="ace-icon fa fa-exclamation-triangle"></i>
                            申报注意事项
                        </div>
                        
                        <ul class="notice-list">
                            <li class="notice-item">
                                <span class="notice-number">1.</span>
                                <span>多文件申报时，请务必将多个文件打成一个压缩包(如：*.zip或*.rar)，再进行申请操作。</span>
                            </li>
                            <li class="notice-item">
                                <span class="notice-number">2.</span>
                                <span>单文件申报时，上传类型：*.doc、*.pdf、*.png、*.jpg、*.docx、*.rar、*.zip</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 表单操作 -->
            <div class="form-actions">
                <button type="button" class="btn-cancel" onclick="goBack();">
                    <i class="ace-icon fa fa-times"></i>
                    取消
                </button>
                <button type="button" class="btn-submit" onclick="submitForm();">
                    <i class="ace-icon fa fa-check"></i>
                    提交申请
                </button>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>提交中...</span>
            </div>
        </div>
    </form>

    <script>
        $(function() {
            initPage();
            initFileUpload();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 初始化文件上传
        function initFileUpload() {
            const fileInput = $('#fileInput');
            const fileUploadContainer = $('.file-upload-container');

            // 文件选择事件
            fileInput.on('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    if (validateFile(file)) {
                        showSelectedFile(file);
                        hideError('fileError');
                    } else {
                        removeFile();
                        showError('fileError', '文件类型不符合要求！');
                    }
                }
            });

            // 拖拽上传
            fileUploadContainer.on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            fileUploadContainer.on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            fileUploadContainer.on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (validateFile(file)) {
                        fileInput[0].files = files;
                        showSelectedFile(file);
                        hideError('fileError');
                    } else {
                        showError('fileError', '文件类型不符合要求！');
                    }
                }
            });
        }

        // 验证文件类型
        function validateFile(file) {
            const allowedTypes = ['jpg', 'png', 'doc', 'pdf', 'docx', 'rar', 'zip'];
            const fileName = file.name.toLowerCase();
            const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1);

            return allowedTypes.includes(fileExt);
        }

        // 显示选中的文件
        function showSelectedFile(file) {
            $('#fileName').text(file.name);
            $('#fileSize').text(formatFileSize(file.size));
            $('#fileSelected').show();
            $('.file-upload-container').hide();
        }

        // 移除文件
        function removeFile() {
            $('#fileInput').val('');
            $('#fileSelected').hide();
            $('.file-upload-container').show();
            hideError('fileError');
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 表单验证
        function validateForm() {
            let isValid = true;

            // 验证申请原因
            const sqyy = $('#sqyy').val().trim();
            if (!sqyy) {
                showError('sqyyError', '申请原因不能为空！');
                isValid = false;
            } else if (sqyy.length > 250) {
                showError('sqyyError', '申请原因长度超过最大值！');
                isValid = false;
            } else {
                hideError('sqyyError');
            }

            return isValid;
        }

        // 提交表单
        function submitForm() {
            if (!validateForm()) {
                return;
            }

            if (confirm('确定要提交申请吗？')) {
                showLoading(true);

                const formData = new FormData($('#addInfo')[0]);

                $.ajax({
                    url: "/student/exam/ApplicationDelayedExam/save?tokenValue=" + $("#tokenValue").val(),
                    type: "post",
                    data: formData,
                    dataType: "json",
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $("#tokenValue").val(response.token);

                        if (response.result.indexOf("/") !== -1) {
                            window.location.href = response.result;
                        } else {
                            if (response.result === "ok") {
                                showSuccess("保存成功！");
                                setTimeout(() => {
                                    goBack();
                                }, 1500);
                            } else if (response.result === "no") {
                                showError('', "保存失败！");
                            } else if (response.result === "nostatus") {
                                showError('', "申请已审批，不能保存");
                            }
                        }
                    },
                    error: function(xhr) {
                        showError('', "错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 返回上一页
        function goBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                history.back();
            }
        }

        // 显示错误信息
        function showError(elementId, message) {
            if (elementId) {
                $('#' + elementId).text(message).show();
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 隐藏错误信息
        function hideError(elementId) {
            $('#' + elementId).hide();
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
