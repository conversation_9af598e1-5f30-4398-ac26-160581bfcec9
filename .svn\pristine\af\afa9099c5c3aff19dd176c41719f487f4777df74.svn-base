<%@ page language="java" contentType="text/html; charset=UTF-8"
		 pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page
		import="java.util.*,educationalAdministration.student.scoreSearch.entity.*" %>


<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>


<head>
	<title>方案成绩</title>
	<link rel="stylesheet" href="/assets/css/jquery-ui.custom.min.css"/>
	<link rel="stylesheet" href="/assets/css/fullcalendar.css"/>
	<script src="/assets/js/date-time/moment.min.js"></script>
	<script src="/assets/js/fullcalendar.min.js"></script>
	<script src="/assets/js/date-time/moment.min.js"></script>
	<script src="/assets/js/fullcalendar.min.js"></script>
	<script src="/assets/js/bootbox.min.js"></script>
	<style type="text/css">
		.scrollspy-example {
			height: calc(100vh - 170px);
			overflow: auto;
			position: relative;
		}

		.green_background {
			background: #d4f0c6 !important;
		}

		.red_background {
			background: #f0cdc7 !important;
		}
	</style>
	<script type="text/javascript">

		var index;
		$(function () {
			index = layer.load(0, {
				shade: [0.2, '#000'] //0.1透明度的白色背景
			});
			var url = "/student/integratedQuery/scoreQuery/${url_check_code}/schemeScores/callback";
			$.get(url, function (data) {

				if (data.result && data.result == "error") {
					var message = "<div class='alert alert-danger'>非法请求！！！</div>";
					$("#showMessage").html(message);
					$("#showMessage").show();
					layer.close(index);
				} else {
					var lnList = data.lnList;
					if (lnList.length == 0) {
						var message = "<div class='alert alert-block alert-success'><button type='button' class='close' data-dismiss='alert'><i class='ace-icon fa fa-times'></i>	</button>没有信息</div>";
						$("#showMessage").html(message);
						$("#showMessage").show();
						layer.close(index);
					} else {
						$("#showMessage").show();
						useScoreExtension(lnList);
					}
				}
			});
		});
		function useScoreExtension(lnList) {
			$.ajax({
				url: "/student/integratedQuery/scoreQuery/coursePropertyScores/useScoreExtension",
				type: "post",
				data: "",
				dataType: "json",
				beforeSend: function () {
					index = layer.load(0, {
						shade: [0.2, '#000']
						//0.1透明度的白色背景
					});
				},
				success: function (d) {
					$("#param").val(d["param"]);
					$("#schoolName").val(d["schoolName"]);
					fillScoreTable(lnList);
				},
				error: function (xhr) {
					layer.close(index);
					urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status
							+ "]:操作失败！");
				},
				complete: function () {
					layer.close(index);
				}
			});
		}
		function fillScoreTable(lnList) {
			if (lnList != null && lnList.length > 0) {
				var htmlStrings = "";
				var htmlNavbar = " ";
				var param = $("#param").val();
				var schoolName = $("#schoolName").val();
				var showScoreDetail = $("#showScoreDetail").val();
				for (var i = 0; i < lnList.length; i++) {
					var active = "";
					if (i == 0)
						active = "active";
					htmlNavbar += "<li class='" + active + "'><a href='#id_" + (i + 1) + "'>" + lnList[i].cjlx + "</a></li>";

					var htmlString = "<div id='id_" + (i + 1) + "' class=''>";
					htmlString += "<h4 class='header smaller lighter grey'><i class='glyphicon glyphicon-signal'></i>   " + lnList[i].cjlx;
					if("${schoolId}"!="100006"){
						htmlString += "  <span class='label label-yellow' style='border-radius: 10px;'><font style='color:black;'>已修" + lnList[i].zms + "门，" + (lnList[i].yxxf ? ((parseFloat(lnList[i].yxxf * 10000).toFixed(0)) / 10000) : 0) + "学分</font></span>";
					}
					htmlString += "  <span class='label label-success' style='border-radius: 10px;'><font style='color:black;'>通过" + lnList[i].tgms + "门，获得" + (lnList[i].zxf ? ((parseFloat(lnList[i].zxf * 10000).toFixed(0)) / 10000) : 0) + "学分</font></span>";
					htmlString += "   <span class='label' style='border-radius: 10px;background-color: pink !important;'><font style='color:black;'>不通过" + (lnList[i].zms - lnList[i].tgms) + "门</font></span>";
					if("${schoolId}"=="100006"){
						htmlString += " <font style='color:red;font-size: 13px;'>（不考虑课程替代，按实际修读学分计算）</font>";
					}
					htmlString += "</h4>"
							+ "<div class='row'>"
							+ "<div class='col-sm-12'>"
							+ "<table class='table table-striped table-bordered table-hover' style='margin-bottom: 0px;'>"
							+ "<thead>"
							+ "<tr>"
							+ "<th>序号</th>"
							+ "<th>课程号</th>"
							+ "<th>课程名</th>"
							+ "<th>课程属性</th>"
							+ "<th>学分</th>"
							+ "<th>成绩</th>";
					if (schoolName == "100027") {
						htmlString += "<th>选课课组名</th>";
					}
					htmlString += "<th>未通过原因</th>"
							+ "<th>课序号</th>"
							+ "<th>英文课程名</th>"
							+ "</tr>"
							+ "</thead>"
							+ "<tbody>";

					var htmlString1 = "";
					var htmlString2 = "";
					if (lnList[i].cjList != null) {
						for (var j = 0; j < lnList[i].cjList.length; j++) {
							htmlString1 = "<tr>"
									+ "<td>" + (j + 1) + "</td>"
									+ "<td>" + lnList[i].cjList[j].id.courseNumber + "</td>"
									+ "<td>" + lnList[i].cjList[j].courseName + "</td>"
									+ "<td>" + lnList[i].cjList[j].courseAttributeName + "</td>"
									+ "<td>" + lnList[i].cjList[j].credit + "</td>";
							if (lnList[i].cjList[j].courseScore == "-999.999" || lnList[i].cjList[j].cj == -999.999) {
								htmlString1 += "<td>未评估</td>";
							}else{
								if (schoolName == "100010") {
									if (showScoreDetail != "0") {
										if (lnList[i].cjList[j].id.courseNumber == "58000001" || lnList[i].cjList[j].id.courseNumber == "58000002" || lnList[i].cjList[j].id.courseNumber == "58000003" || lnList[i].cjList[j].id.courseNumber == "58000004" || lnList[i].cjList[j].id.courseNumber == "58000005") {
											htmlString1 += "<td " + (lnList[i].cjList[j].courseScore < 60 ? "class='red_background'" : "class='green_background'") + "><a style='cursor: pointer;text-decoration: underline;'  " + (lnList[i].cjList[j].courseScore < 60 ? "class='red'" : "class='green'") + " title='" + (param == "1" ? "查看分项成绩" : "查看明细成绩") + "' onclick='lookSubitemScore(\"" + lnList[i].cjList[j].id.executiveEducationPlanNumber + "\",\"" + lnList[i].cjList[j].id.courseNumber + "\",\"" + lnList[i].cjList[j].id.coureSequenceNumber + "\",\"" + lnList[i].cjList[j].id.startTime + "\",\"" + lnList[i].cjList[j].courseAttributeCode + "\")'>" + (lnList[i].cjList[j].wclyscj!=null&&lnList[i].cjList[j].wclyscj!=""?lnList[i].cjList[j].courseScore:"") + "" + "</a></td>";
										} else {
											htmlString1 += "<td " + (lnList[i].cjList[j].courseScore < 60 ? "class='red_background'" : "class='green_background'") + "><a style='cursor: pointer;text-decoration: underline;'  " + (lnList[i].cjList[j].courseScore < 60 ? "class='red'" : "class='green'") + " title='" + (param == "1" ? "查看分项成绩" : "查看明细成绩") + "' onclick='lookSubitemScore(\"" + lnList[i].cjList[j].id.executiveEducationPlanNumber + "\",\"" + lnList[i].cjList[j].id.courseNumber + "\",\"" + lnList[i].cjList[j].id.coureSequenceNumber + "\",\"" + lnList[i].cjList[j].id.startTime + "\",\"" + lnList[i].cjList[j].courseAttributeCode + "\")'>" + lnList[i].cjList[j].gradeName + "" + "</a></td>";
										}
									} else {
										if (lnList[i].cjList[j].id.courseNumber == "58000001" || lnList[i].cjList[j].id.courseNumber == "58000002" || lnList[i].cjList[j].id.courseNumber == "58000003" || lnList[i].cjList[j].id.courseNumber == "58000004" || lnList[i].cjList[j].id.courseNumber == "58000005") {
											htmlString1 += "<td " + (lnList[i].cjList[j].courseScore < 60 ? "class='red_background'" : "class='green_background'") + "><span  " + (lnList[i].cjList[j].courseScore < 60 ? "class='red'" : "class='green'") + ">" + (lnList[i].cjList[j].wclyscj!=null&&lnList[i].cjList[j].wclyscj!=""?lnList[i].cjList[j].courseScore:"") + "" + "</span></td>";
										} else {
											htmlString1 += "<td " + (lnList[i].cjList[j].courseScore < 60 ? "class='red_background'" : "class='green_background'") + "><span " + (lnList[i].cjList[j].courseScore < 60 ? "class='red'" : "class='green'") + ">" + lnList[i].cjList[j].gradeName + "" + "</span></td>";
										}
									}
								} else {
									if (showScoreDetail != "0") {
										htmlString1 += "<td " + (lnList[i].cjList[j].courseScore < 60 ? "class='red_background'" : "class='green_background'") + "><a style='cursor: pointer;text-decoration: underline;' " + (lnList[i].cjList[j].courseScore < 60 ? "class='red'" : "class='green'") + " title='" + (param == "1" ? "查看分项成绩" : "查看明细成绩") + "' onclick='lookSubitemScore(\"" + lnList[i].cjList[j].id.executiveEducationPlanNumber + "\",\"" + lnList[i].cjList[j].id.courseNumber + "\",\"" + lnList[i].cjList[j].id.coureSequenceNumber + "\",\"" + lnList[i].cjList[j].id.startTime + "\",\"" + lnList[i].cjList[j].courseAttributeCode + "\")'>";
										if(lnList[i].cjList[j].scoreEntryModeCode == "002"){
										    htmlString1 += lnList[i].cjList[j].gradeName;
										}else{
										    htmlString1 += lnList[i].cjList[j].wclyscj!=null&&lnList[i].cjList[j].wclyscj!=""?lnList[i].cjList[j].courseScore:"";
										}
										htmlString1 += "</a></td>";
									} else {
										htmlString1 += "<td " + (lnList[i].cjList[j].courseScore < 60 ? "class='red_background'" : "class='green_background'") + "><span " + (lnList[i].cjList[j].courseScore < 60 ? "class='red'" : "class='green'") + ">";
										if(lnList[i].cjList[j].scoreEntryModeCode == "002"){
										    htmlString1 += lnList[i].cjList[j].gradeName;
										}else{
										    htmlString1 += lnList[i].cjList[j].wclyscj!=null&&lnList[i].cjList[j].wclyscj!=""?lnList[i].cjList[j].courseScore:"";
										}
										htmlString1 += "</span></td>";
									}
								}
							}		
							
							if (schoolName == "100027") {
								htmlString1 += "<td>" + lnList[i].cjList[j].xkkzm + "</td>";
							}
							
							htmlString1 += "<td>" + lnList[i].cjList[j].notByReasonName + ""
									+ "</td>"
									+ "<td >" + (lnList[i].cjList[j].id.coureSequenceNumber == null ? "" : (lnList[i].cjList[j].id.coureSequenceNumber == 'NONE' ? "" : lnList[i].cjList[j].id.coureSequenceNumber)) + ""
									+ "</td>"
									+ "<td >" + lnList[i].cjList[j].englishCourseName + ""
									+ "</td>"
									+ "</tr>";
							htmlString2 += htmlString1;
						}
					}
					htmlString += htmlString2;
					var htmlString3 = "</tbody>"
							+ "</table>"
//							+" 最低修读学分：&nbsp;"+lnList[i].yqxf+"&nbsp;已修读课程总学分：&nbsp;&nbsp;"+lnList[i].yxxf+""
//							+"	 &nbsp;&nbsp;已修读课程门数:&nbsp;&nbsp;"+lnList[i].zms+"&nbsp;&nbsp;通过课程门数:&nbsp;&nbsp;"+lnList[i].tgms+""

								//	+"<div class='clearfix' style='border: 1px solid #E2E3E7;'>"
								/* 	+"<div class='grid4 center'><span class='bigger-175 blue '>"
								 +lnList[i].yqxf+"</span><br/>最低修读学分</div>" */
								//	+"<div class='grid4 center' ><span class='bigger-175 blue'>"
								//	+lnList[i].yxxf+"</span><br/>已修读课程总学分</div>"
								//	+"<div class='grid4 center' ><span class='bigger-175 blue'>"
								//	+lnList[i].zms+"</span><br/>已修读课程门数</div>"
								//	+"<div class='grid4 center' ><span class='bigger-175 blue'>"
								//	+lnList[i].tgms+"</span><br/>通过课程门数</div>"
//							+"<div class='grid4 center'><div class='center easy-pie-chart percentage' data-percent='"+parseInt(b) +"' data-color='#9585BF'>"
//							+"<span class='percent'>"+b+"</span>%</div>"
//							+"<div class='space-2'></div>学分完成率</div>"
								//	+"</div>"
							+ "</div>"
							+ "</div>"
							+ "</div>"
//							+"<div class='widget-box transparent' style='border: 1px solid #E2E3E7;'>"
//							+"<div class='widget-header widget-header-small'>";
//							+"<h4 class='widget-title blue smaller'>"+lnList[i].cjlx+"完成情况"
//						    +"</h4></div>"
//						     +"</div>"
					/* +"<div  style='border: 1px solid #E2E3E7;'>"
					 +"<h5 class='widget-title smaller'>"+lnList[i].cjlx+"完成情况"
					 +"</div>"
					 +"<div class='row'>"
					 +"<div class='col-sm-12'>" */
//						    +"学期总学分：&nbsp;"+lnList[i].zxf+"&nbsp;学期总学时：&nbsp;&nbsp;"+lnList[i].zxs+"</br>"
//						    +"通过门数：&nbsp;"+lnList[i].tgms+"&nbsp;未通过门数：&nbsp;&nbsp;"+(lnList[i].zms - lnList[i].tgms)+""		

					/*    +"<div class='clearfix'  style='border: 1px solid #E2E3E7;'>"
					 +"<div class='grid4 center'><span class='bigger-175 blue '>"
					 +lnList[i].zxf+"</span><br/>已获得学分</div>"
					 +"<div class='grid4 center' ><span class='bigger-175 blue'>"
					 +lnList[i].zxs+"</span><br/>学期总学时</div>"
					 +"<div class='grid4 center' ><span class='bigger-175 blue'>"
					 +lnList[i].tgms+"</span><br/>通过门数</div>"
					 +"<div class='grid4 center' ><span class='bigger-175 blue'>"
					 +(lnList[i].zms - lnList[i].tgms)+"</span><br/><a href='../unpassedScores/index'>未通过门数</a></div>"
					 +"</div>"
					 +"</div>"
					 +"</div>"
					 +"</div>";*/
					htmlString += htmlString3;
					htmlStrings += htmlString;
					htmlStrings = htmlStrings.replace(new RegExp(/(null)/g), '');
				}
				$("#timeline").html(htmlStrings);
				$("#navbar_ul").html(htmlNavbar);
				$('#div-scroll').scrollspy({target: '.navbar-example'});
				layer.close(index);
			}
		}
		function lookSubitemScore(zxjxjhh, kch, kxh, kssj, kcsxdm) {
			var param = $("#param").val();
			$.ajax({
				url: "/student/integratedQuery/scoreQuery/subitemScore/look",
				type: "post",
				data: "zxjxjhh=" + zxjxjhh + "&kch=" + kch + "&kxh=" + kxh
				+ "&kssj=" + kssj + "&param=" + param,
				dataType: "json",
				beforeSend: function () {
					index = layer.load(0, {
						shade: [0.2, '#000']
						//0.1透明度的白色背景
					});
				},
				success: function (d) {
					if (d["scoreDetailList"].length > 0) {
						if (param == "1") {
							window.location.href = "/student/integratedQuery/scoreQuery/subitemScore/fxcjIndex/"
									+ zxjxjhh
									+ "/"
									+ kch
									+ "/"
									+ kxh
									+ "/"
									+ kssj + "/" + kcsxdm;
						} else {
							window.location.href = "/student/integratedQuery/scoreQuery/subitemScore/mxcjIndex/"
									+ zxjxjhh
									+ "/"
									+ kch
									+ "/"
									+ kxh
									+ "/"
									+ kssj;

						}
					} else {
						if (param == "1") {
							urp.alert("当前课程暂无分项成绩！");
						} else {
							urp.alert("当前课程暂无明细成绩！");
						}
					}
				},
				error: function (xhr) {
					layer.close(index);
					urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status
							+ "]:操作失败！");
				},
				complete: function () {
					layer.close(index);
				}
			});
		}
	</script>
</head>

<body>
<input type="hidden" id="param" name="param">
<input type="hidden" id="schoolName" name="schoolName">
<input type="hidden" id="showScoreDetail" name="showScoreDetail" value="${showScoreDetail }">

<div class="row">
	<div class="col-sm-12 self-margin">
		<div class="navbar-example" id="showMessage">
			<div id="navbarExample" class="navbar navbar-static" style="background: #ffffff">
				<div class="navbar-inner">
					<div class="container" style="width: auto;margin-left: -11px;">
						<ul class="nav nav-tabs" id="navbar_ul">

						</ul>
					</div>
				</div>
			</div>
		</div>
		<div id="div-scroll" data-offset="50" class="scrollspy-example" style="margin-top: -9px;">
			<div class="row" style="margin-left: 0px;margin-right: 0px;">
				<div class="col-xs-12">
					<div id="timeline-1">
						<div class="row">
							<div class="col-xs-12" id="timeline">

							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</body>
</html>
