<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>准考证打印</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 准考证打印页面样式 */
        .ticket-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .exam-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .exam-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .exam-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .exam-item:last-child {
            border-bottom: none;
        }
        
        .exam-item:active {
            background: var(--bg-color-active);
        }
        
        .exam-item.printable {
            border-left: 4px solid var(--success-color);
        }
        
        .exam-item.not-ready {
            border-left: 4px solid var(--warning-color);
            opacity: 0.7;
        }
        
        .exam-item.expired {
            border-left: 4px solid var(--error-color);
            opacity: 0.5;
        }
        
        .exam-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .exam-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-printable {
            background: var(--success-color);
            color: white;
        }
        
        .status-not-ready {
            background: var(--warning-color);
            color: white;
        }
        
        .status-expired {
            background: var(--error-color);
            color: white;
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-print {
            background: var(--success-color);
            color: white;
        }
        
        .btn-preview {
            background: var(--info-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .ticket-preview {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .ticket-preview.show {
            transform: translateX(0);
        }
        
        .preview-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .preview-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .preview-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .preview-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-download {
            background: var(--warning-color);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
        }
        
        .btn-print-ticket {
            background: var(--success-color);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
        }
        
        .ticket-content {
            padding: var(--padding-md);
            background: white;
            margin: var(--margin-md);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .admission-ticket {
            color: black;
            font-family: 'SimSun', serif;
            line-height: 1.6;
        }
        
        .ticket-header-content {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
        }
        
        .ticket-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .ticket-subtitle {
            font-size: 18px;
            color: #333;
        }
        
        .ticket-body {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .student-info {
            flex: 1;
        }
        
        .photo-area {
            width: 120px;
            height: 160px;
            border: 2px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;
            font-size: 12px;
            color: #666;
            text-align: center;
            flex-shrink: 0;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .info-table td {
            padding: 8px 12px;
            border: 1px solid #000;
            font-size: 14px;
        }
        
        .info-table .label {
            background: #f5f5f5;
            font-weight: bold;
            width: 100px;
        }
        
        .exam-info-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 5px 0;
            border-bottom: 1px solid #000;
        }
        
        .exam-schedule {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .schedule-item {
            border: 1px solid #000;
            padding: 10px;
        }
        
        .schedule-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .schedule-value {
            font-size: 14px;
        }
        
        .notice-section {
            background: #f9f9f9;
            border: 1px solid #000;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .notice-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #d32f2f;
        }
        
        .notice-list {
            font-size: 12px;
            line-height: 1.8;
        }
        
        .notice-list li {
            margin-bottom: 5px;
        }
        
        .ticket-footer {
            text-align: right;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #000;
            padding-top: 15px;
        }
        
        .tips-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .tips-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .tips-title i {
            margin-right: var(--margin-xs);
            color: var(--warning-color);
        }
        
        .tip-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .tip-item:last-child {
            margin-bottom: 0;
        }
        
        @media print {
            .page-mobile > *:not(.ticket-preview) {
                display: none !important;
            }
            
            .ticket-preview {
                position: static;
                transform: none;
                background: white;
            }
            
            .preview-header {
                display: none;
            }
            
            .ticket-content {
                margin: 0;
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">准考证打印</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="ticket-header">
            <div class="header-title">准考证打印</div>
            <div class="header-subtitle">查看和打印考试准考证</div>
        </div>

        <!-- 考试列表 -->
        <div class="exam-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-id-card"></i>
                    <span>可打印准考证</span>
                </div>
                <div class="exam-count" id="examCount">0</div>
            </div>

            <div id="examList">
                <!-- 考试列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-id-card-o"></i>
            <div id="emptyMessage">暂无可打印的准考证</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>

        <!-- 温馨提示 -->
        <div class="tips-section">
            <div class="tips-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>重要提示</span>
            </div>
            <div class="tip-item">
                准考证是参加考试的重要凭证，请务必在考试前打印并妥善保管。
            </div>
            <div class="tip-item">
                打印时请使用A4纸张，确保信息清晰完整，不得涂改。
            </div>
            <div class="tip-item">
                考试当天请携带准考证和有效身份证件参加考试。
            </div>
            <div class="tip-item">
                如准考证信息有误，请及时联系考试管理部门核实修正。
            </div>
        </div>
    </div>

    <!-- 准考证预览 -->
    <div class="ticket-preview" id="ticketPreview">
        <div class="preview-header">
            <div class="preview-back" onclick="closeTicketPreview();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="preview-title">准考证预览</div>
            <div class="preview-actions">
                <button class="btn-download" onclick="downloadTicket();">
                    <i class="ace-icon fa fa-download"></i> 下载
                </button>
                <button class="btn-print-ticket" onclick="printTicket();">
                    <i class="ace-icon fa fa-print"></i> 打印
                </button>
            </div>
        </div>

        <div class="ticket-content" id="ticketContent">
            <!-- 准考证内容将动态填充 -->
        </div>
    </div>

    <script>
        // 全局变量
        let examList = [];
        let currentTicket = null;

        $(function() {
            initPage();
            loadExamList();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载考试列表
        function loadExamList() {
            showLoading(true);

            $.ajax({
                url: "/student/examinationManagement/admissionTicket/getExamList",
                type: "post",
                dataType: "json",
                success: function(data) {
                    examList = data.exams || [];
                    renderExamList();
                    updateExamCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载考试列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染考试列表
        function renderExamList() {
            const container = $('#examList');
            container.empty();

            if (examList.length === 0) {
                showEmptyState('暂无可打印的准考证');
                return;
            } else {
                hideEmptyState();
            }

            examList.forEach(exam => {
                const examHtml = createExamItem(exam);
                container.append(examHtml);
            });
        }

        // 创建考试项
        function createExamItem(exam) {
            const statusInfo = getExamStatusInfo(exam);
            const isPrintable = statusInfo.status === 'printable';

            let actionButtons = '';
            if (isPrintable) {
                actionButtons = `
                    <button class="btn-mobile btn-preview flex-1" onclick="previewTicket('${exam.id}')">预览</button>
                    <button class="btn-mobile btn-print flex-1" onclick="printDirectly('${exam.id}')">打印</button>
                `;
            } else {
                actionButtons = `
                    <button class="btn-mobile btn-disabled flex-1">${statusInfo.buttonText}</button>
                `;
            }

            return `
                <div class="exam-item ${statusInfo.class}">
                    <div class="exam-basic">
                        <div class="exam-name">${exam.name}</div>
                        <div class="exam-status ${statusInfo.statusClass}">${statusInfo.text}</div>
                    </div>
                    <div class="exam-details">
                        <div class="detail-item">
                            <span>考试时间:</span>
                            <span>${formatDateTime(exam.examDate)}</span>
                        </div>
                        <div class="detail-item">
                            <span>考试地点:</span>
                            <span>${exam.location || '待定'}</span>
                        </div>
                        <div class="detail-item">
                            <span>座位号:</span>
                            <span>${exam.seatNumber || '待分配'}</span>
                        </div>
                        <div class="detail-item">
                            <span>准考证号:</span>
                            <span>${exam.ticketNumber || '待生成'}</span>
                        </div>
                    </div>
                    <div class="exam-actions">
                        ${actionButtons}
                    </div>
                </div>
            `;
        }

        // 获取考试状态信息
        function getExamStatusInfo(exam) {
            const now = new Date();
            const printStartDate = new Date(exam.printStartDate);
            const printEndDate = new Date(exam.printEndDate);
            const examDate = new Date(exam.examDate);

            if (now < printStartDate) {
                return {
                    status: 'not-ready',
                    class: 'not-ready',
                    statusClass: 'status-not-ready',
                    text: '未开放',
                    buttonText: '未开放打印'
                };
            } else if (now > examDate) {
                return {
                    status: 'expired',
                    class: 'expired',
                    statusClass: 'status-expired',
                    text: '已过期',
                    buttonText: '考试已结束'
                };
            } else if (now >= printStartDate && now <= printEndDate) {
                return {
                    status: 'printable',
                    class: 'printable',
                    statusClass: 'status-printable',
                    text: '可打印',
                    buttonText: '可打印'
                };
            } else {
                return {
                    status: 'expired',
                    class: 'expired',
                    statusClass: 'status-expired',
                    text: '已截止',
                    buttonText: '打印已截止'
                };
            }
        }

        // 格式化日期时间
        function formatDateTime(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString().slice(0, 5);
        }

        // 预览准考证
        function previewTicket(examId) {
            const exam = examList.find(e => e.id === examId);
            if (!exam) return;

            currentTicket = exam;
            generateTicketContent(exam);
            $('#ticketPreview').addClass('show');
        }

        // 生成准考证内容
        function generateTicketContent(exam) {
            const ticketHtml = `
                <div class="admission-ticket">
                    <div class="ticket-header-content">
                        <div class="ticket-title">${exam.organizerName || '考试机构'}</div>
                        <div class="ticket-subtitle">${exam.name} 准考证</div>
                    </div>

                    <div class="ticket-body">
                        <div class="student-info">
                            <table class="info-table">
                                <tr>
                                    <td class="label">姓名</td>
                                    <td>${exam.studentName || ''}</td>
                                </tr>
                                <tr>
                                    <td class="label">性别</td>
                                    <td>${exam.gender || ''}</td>
                                </tr>
                                <tr>
                                    <td class="label">身份证号</td>
                                    <td>${exam.idNumber || ''}</td>
                                </tr>
                                <tr>
                                    <td class="label">准考证号</td>
                                    <td style="font-weight: bold; color: #d32f2f;">${exam.ticketNumber || ''}</td>
                                </tr>
                                <tr>
                                    <td class="label">报考级别</td>
                                    <td>${exam.level || ''}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="photo-area">
                            ${exam.photoUrl ? `<img src="${exam.photoUrl}" style="width: 100%; height: 100%; object-fit: cover;">` : '照片'}
                        </div>
                    </div>

                    <div class="exam-info-section">
                        <div class="section-title">考试安排</div>
                        <div class="exam-schedule">
                            <div class="schedule-item">
                                <div class="schedule-label">考试时间</div>
                                <div class="schedule-value">${formatDateTime(exam.examDate)}</div>
                            </div>
                            <div class="schedule-item">
                                <div class="schedule-label">考试地点</div>
                                <div class="schedule-value">${exam.location || ''}</div>
                            </div>
                            <div class="schedule-item">
                                <div class="schedule-label">考场号</div>
                                <div class="schedule-value">${exam.roomNumber || ''}</div>
                            </div>
                            <div class="schedule-item">
                                <div class="schedule-label">座位号</div>
                                <div class="schedule-value">${exam.seatNumber || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="notice-section">
                        <div class="notice-title">考试须知</div>
                        <ol class="notice-list">
                            <li>考生须在考试开始前30分钟到达考场，凭准考证和有效身份证件入场。</li>
                            <li>考试开始15分钟后，迟到考生不得入场；考试结束前30分钟内不得离场。</li>
                            <li>考生只准携带必要的考试用品，严禁携带手机等通讯设备进入考场。</li>
                            <li>考生应诚信考试，严禁作弊，违者将按相关规定严肃处理。</li>
                            <li>考试期间如有疑问，可举手向监考老师询问。</li>
                            <li>考试结束后，考生应立即停止答题，按监考老师要求有序离场。</li>
                        </ol>
                    </div>

                    <div class="ticket-footer">
                        <div>打印时间：${new Date().toLocaleString()}</div>
                        <div style="margin-top: 5px;">此准考证仅供本次考试使用，请妥善保管</div>
                    </div>
                </div>
            `;

            $('#ticketContent').html(ticketHtml);
        }

        // 关闭准考证预览
        function closeTicketPreview() {
            $('#ticketPreview').removeClass('show');
        }

        // 直接打印
        function printDirectly(examId) {
            const exam = examList.find(e => e.id === examId);
            if (!exam) return;

            currentTicket = exam;
            generateTicketContent(exam);

            // 短暂延迟后打印，确保内容已生成
            setTimeout(() => {
                printTicket();
            }, 100);
        }

        // 打印准考证
        function printTicket() {
            if (!currentTicket) return;

            // 记录打印日志
            recordPrintLog(currentTicket.id);

            // 执行打印
            window.print();
        }

        // 下载准考证
        function downloadTicket() {
            if (!currentTicket) return;

            $.ajax({
                url: "/student/examinationManagement/admissionTicket/downloadTicket",
                type: "post",
                data: { examId: currentTicket.id },
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data) {
                    // 创建下载链接
                    const blob = new Blob([data], { type: 'application/pdf' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `准考证_${currentTicket.name}_${currentTicket.studentName || 'ticket'}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showSuccess('准考证下载成功');
                },
                error: function() {
                    showError('下载失败，请重试');
                }
            });
        }

        // 记录打印日志
        function recordPrintLog(examId) {
            $.ajax({
                url: "/student/examinationManagement/admissionTicket/recordPrintLog",
                type: "post",
                data: {
                    examId: examId,
                    printTime: new Date().toISOString()
                },
                success: function() {
                    console.log('打印日志记录成功');
                },
                error: function() {
                    console.log('打印日志记录失败');
                }
            });
        }

        // 更新考试数量
        function updateExamCount() {
            $('#examCount').text(examList.length);
        }

        // 刷新数据
        function refreshData() {
            loadExamList();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.exam-list').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.exam-list').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.exam-list').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭预览
        let startX = 0;

        $('#ticketPreview').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#ticketPreview').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeTicketPreview();
            }
        });

        $('#ticketPreview').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
