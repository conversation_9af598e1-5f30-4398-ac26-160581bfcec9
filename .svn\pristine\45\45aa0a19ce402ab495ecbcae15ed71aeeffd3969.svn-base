<%--
  Created by IntelliJ IDEA.
  User: gdx
  Date: 2018/5/25
  Time: 16:03
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<html>
<head>
    <title>审批详情</title>
    <style type="text/css">
        .widget-header {
            background: linear-gradient(135deg, #0878f1d9, #ce1dff33);
            color: white;
            border-top-right-radius: 12px;
            border-top-left-radius: 12px;
        }

        .widget-box, .widget-body, .collapsed > .widget-header {
            border-radius: 12px;
        }

        .widget-toolbar i, .phone-message-value {
            color: white;
        }

        .itemdiv.dialogdiv .label.label-yellow {
            white-space: normal;
            height: auto;
            width: 4.4em;
            padding: 0.1em 0.2em;
            line-height: normal;
        }

        .itemdiv.dialogdiv .name a {
            color: black;
            text-decoration: none;
        }

        input[type=checkbox].ace.ace-switch.ace-switch-5 + .lbl::before {
            content: "是\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0否";
        }
    </style>
</head>
<body>
<h5 class="phone-header smaller lighter grey">
    <i class="ace-icon fa fa-chevron-left bigger-130 phone-header-left" onclick="turnBack();"></i>
    <span class="phone-header-center">查看</span>
</h5>

<div class="row">
    <div class="col-xs-12 self-margin">
        <div class="row" style="margin-left: 0px;margin-right: 0px;">
            <div>${infoHtml}</div>
            <c:if test="${fn:length(sysSqfjbs) > 0}">
                <div class="col-xs-12">
                    <div class="widget-box">
                        <div class="widget-header widget-header-flat">
                            <h4 class="widget-title lighter"><i class="glyphicon glyphicon-list"></i> 申请附件</h4>

                            <div class="widget-toolbar"><a data-action="collapse" href="#">
                                <i class="ace-icon fa fa-chevron-up"></i></a>
                            </div>
                        </div>
                        <div class="widget-body">
                            <div class="widget-main" style="padding: 0px;">
                                <div class="dialogs ace-scroll scroll-active">
                                    <c:forEach items="${sysSqfjbs}" var="sysSqfjb">
                                        <div class="col-xs-12 phone-row-header">已上传附件</div>
                                        <div class="phone-profile-info-row">
                                            <div class="col-xs-3 phone-row-title">附件信息</div>
                                            <div class="col-xs-9 phone-row-value">
                                                    ${sysSqfjb.fjmc}
                                                <a style='cursor: pointer;' class='blue' title='下载查看已上传文件'
                                                   onclick="downFjb('${sysSqfjb.fjid}');return false;">
                                                    <i class='ace-icon fa fa-cloud-download bigger-130'></i>
                                                </a>
                                                <c:if test="${fn:endsWith(sysSqfjb.fjmc,'.pdf') || fn:endsWith(sysSqfjb.fjmc,'.PDF') || fn:endsWith(sysSqfjb.fjmc,'.png') || fn:endsWith(sysSqfjb.fjmc,'.PNG') || fn:endsWith(sysSqfjb.fjmc,'.jpg') || fn:endsWith(sysSqfjb.fjmc,'.JPG')}">
                                                    <a style='cursor: pointer;' class='blue' title='查看已上传文件'
                                                       onclick="getApplyImageUrl('${sysSqfjb.fjid}','${sysSqfjb.fjmc}','other','${sysSqfjb.fileurl}');return false;">
                                                        <i class='ace-icon fa fa-eye bigger-130'></i>
                                                    </a>
                                                </c:if>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
            <c:if test="${not empty eaApplys.rollbackMode }">
                <div class="col-xs-12">
                    <div class="widget-box">
                        <div class="widget-header widget-header-flat">
                            <h4 class="widget-title lighter"><i class="glyphicon glyphicon-list"></i> 申请附件</h4>

                            <div class="widget-toolbar"><a data-action="collapse" href="#">
                                <i class="ace-icon fa fa-chevron-up"></i></a>
                            </div>
                        </div>
                        <div class="widget-body">
                            <div class="widget-main" style="padding: 0px;">
                                <div class="dialogs ace-scroll scroll-active">
                                    <div class="col-xs-12 phone-row-header">撤销信息</div>

                                    <div class="phone-profile-info-row">
                                        <div class="col-xs-3 phone-row-title">撤销人</div>
                                        <div class="col-xs-9 phone-row-value">${eaApplys.rollbackUserName}</div>
                                    </div>
                                    <div class="phone-profile-info-row">
                                        <div class="col-xs-3 phone-row-title">撤销原因</div>
                                        <div class="col-xs-9 phone-row-value">${eaApplys.rollbackDtStr}</div>
                                    </div>
                                    <div class="phone-profile-info-row">
                                        <div class="col-xs-3 phone-row-title">撤销人</div>
                                        <div class="col-xs-9 phone-row-value">${eaApplys.rollbackReason}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>

            <c:if test="${fn:length(eaResults) > 0}">
                <div class="col-xs-12">
                    <div class="widget-box">
                        <div class="widget-header widget-header-flat">
                            <h4 class="widget-title lighter">
                                <i class="ace-icon fa fa-info-circle"></i> 审批过程
                            </h4>

                            <div class="widget-toolbar">
                                <a data-action="collapse" href="#">
                                    <i class="ace-icon fa fa-chevron-up"></i></a>
                                </a>
                            </div>
                        </div>
                        <div class="widget-body">
                            <div class="widget-main no-padding">
                                <div class="dialogs ace-scroll scroll-active">
                                    <div class="scroll-content">
                                        <div class="itemdiv dialogdiv">
                                            <div class="user">
                                                <i class="phone-timeline-indicator ace-icon fa fa-star btn btn-warning no-hover green"></i>
                                            </div>
                                            <div class="body">
                                                <div class="">
                                                    <i class="ace-icon fa fa-clock-o bigger-110"></i> ${eaApplys.commitDt}
                                                </div>
                                                <div class="name">
                                                        ${eaApplys.urpUser.username}&nbsp; &nbsp; 发起申请
                                                </div>
                                            </div>
                                        </div>
                                        <c:forEach items="${eaResults}" var="eaResult">
                                            <div class="itemdiv dialogdiv">

                                                <div class="user">
                                                    <i class="phone-timeline-indicator ace-icon fa fa-leaf btn btn-primary no-hover green"></i>
                                                    <span class="label label-yellow label-sm">${eaResult[1]}</span>
                                                </div>
                                                <div class="body" style="min-height: 2.5em;">
                                                    <c:if test="${!empty eaResult[6]}">
                                                        <div>
                                                            <i class="ace-icon fa fa-clock-o bigger-110"></i>
                                                            <span>${eaResult[6]}</span>
                                                        </div>
                                                    </c:if>
                                                    <div class="name">
                                                        <a href="#">
                                                                ${eaResult[5]}&nbsp;&nbsp;
                                                            <c:if test="${eal_code == eaResult[0] }">
                                                                <font color="red">停滞时间：${times }</font>
                                                            </c:if>
                                                            <c:if test="${eal_code != eaResult[0] }">
                                                                <c:if test="${'0'== eaResult[2]}">待审批</c:if>
                                                                <c:if test="${'1'== eaResult[2]}"><span
                                                                        class="red bolder">拒绝</span></c:if>
                                                                <c:if test="${'2'== eaResult[2]}">跳过</c:if>
                                                                <c:if test="${'3'== eaResult[2]}"><span
                                                                        class="green bolder">批准</span></c:if>
                                                                <c:if test="${!empty eaResult[3]}">(${eaResult[3]})</c:if>
                                                            </c:if>
                                                        </a>
                                                    </div>
                                                </div>

                                            </div>
                                        </c:forEach>
                                    </div>
                                </div>
                            </div>
                            <!-- /.widget-main -->
                        </div>
                        <!-- /.widget-body -->
                    </div>
                    <!-- /.widget-box -->
                </div>
            </c:if>
            <div>
                <div class="col-xs-12">
                    <button class="btn btn-block btn-round" onclick="turnBack();return false;">
                        <i class="ace-icon fa fa-reply"></i> 返回
                    </button>
                </div>
            </div>
        </div>
        <%--<c:if test="${againApply == 'showAdd' && (eaApplys.applyStatus=='-1' || eaApplys.eaRslt == '0') && eaApplys.applyType != '10006' && eaApplys.applyType != '10008'}">
            <button class="btn btn-xs btn-info pull-center btn-round"
                    type="button"
                    onclick="editInfo('${eaApplys.applyId}','againApply');">
                再次提交
            </button>
        </c:if>--%>
    </div>
</div>
<script type="text/javascript">
    function turnBack() {
        self.location = document.referrer;
    }

    function doDownload(id) {
        location.href = "/student/application/index/dodownload/" + id;
    }

    function downLwFjs(filekey, filename) {
        $("#sbs_down_iframe_div").remove()
        var url = "/admin/common/file/get/down?fileid=" + filekey + "&filename=" + encodeURIComponent(encodeURIComponent(filename));
        $('<div style="display:none;" id="sbs_down_iframe_div"><iframe src="' + url + '" frameborder="0" scrolling="no" ></iframe></div>').appendTo('body');

    }

    
    function downFile(filekey, filename) {
        $("#sbs_down_iframe_div").remove();
        var url = "/admin/common/file/get/down?fileid=" + filekey + "&filename=" + encodeURIComponent(encodeURIComponent(filename));
        $('<div style="display:none;" id="sbs_down_iframe_div"><iframe src="' + url + '" frameborder="0" scrolling="no" ></iframe></div>').appendTo('body');
    }

    var base64 = new Base64();

    function getApplyImageUrl(id, fjmc, fjlx, fileurl) {
        if (fjlx == "other") {
            fjlx = fjmc.substring(fjmc.indexOf(".") + 1, fjmc.length);
        }
        var mod = urp.addModelHt("calc(100% - 33px)", "show_img_model");
        var wHeight = $(window).height();
         var url = "/admin/common/file/get/show?fileid=" + fileurl + "&filename=" + base64.encode(fjmc)
        var srcdiv = "<div><img width='100%' height='100%' src='" + url + "'/><div>";
        var rotate_btn="";
        if (fjlx == "pdf" || fjlx == "PDF") {
            srcdiv = "<embed width='100%' height='100%' src='" + url + "'></embed>";
        }else{
        	rotate_btn="<i style='margin-left: 12px;' title='逆时针旋转' class='ace-icon fa fa-history bigger-120' onclick='rotate()'></i>";
        }
        $("#show_img_model .modal-content").html("<div class='modal-header'><button type='button' class='close' data-dismiss='modal' id='closereadonlydismiss' onclick='closeCopyMod();'>&times;</button><h4 class='blue bigger'>${typename}【" + fjmc + "】的附件 " + rotate_btn + "</h4></div>");
        $("#show_img_model .modal-content").append("<div class='modal-body' style='overflow: auto;' id='tsxxmodalbody'	>" + srcdiv + " <div>");
        $("#show_img_model .modal-content").append("</div></div>");
        $("#show_img_model .modal-content").append("<div class='modal-footer'></div>");

        $("#show_img_model").modal({backdrop: 'static', keyboard: false});
        $("#show_img_model").css("z-index", "1992");
        $(".modal-backdrop").css("z-index", "1991");
    }
    
 	var current = 0;
    function rotate() {
		current = (current-90)%360;
		$("#tsxxmodalbody img").css("transform","rotate("+current+"deg)");
    } 
 
    function closeCopyMod() {
        $(".modal-backdrop").css("z-index", "");
    }  
</script>
</body>
</html>
