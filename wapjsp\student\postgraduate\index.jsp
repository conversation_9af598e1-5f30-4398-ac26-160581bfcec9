<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>考研学习</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 考研学习页面样式 */
        .postgraduate-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .postgraduate-icon {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            opacity: 0.9;
        }
        
        .postgraduate-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .postgraduate-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .study-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .actions-title i {
            color: var(--primary-color);
        }
        
        .btn-study {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 16px 24px;
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            transition: all var(--transition-base);
            width: 100%;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .btn-study:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
        }
        
        .btn-study:active {
            transform: translateY(0);
        }
        
        .btn-study i {
            font-size: 20px;
        }
        
        .study-features {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .features-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .features-title i {
            color: var(--success-color);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .feature-item {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
            border: 2px solid transparent;
            transition: all var(--transition-base);
        }
        
        .feature-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--primary-light);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin: 0 auto var(--margin-sm);
        }
        
        .feature-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .feature-desc {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .study-tips {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            font-size: var(--font-size-small);
            color: var(--info-dark);
            line-height: 1.6;
        }
        
        .tips-title {
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tips-title i {
            color: var(--info-color);
        }
        
        .tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .tips-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .tips-list li::before {
            content: '•';
            color: var(--info-color);
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .study-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--warning-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        @media (max-width: 480px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">考研学习</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 考研学习头部 -->
        <div class="postgraduate-header">
            <div class="postgraduate-icon">
                <i class="ace-icon fa fa-graduation-cap"></i>
            </div>
            <div class="postgraduate-title">考研学习平台</div>
            <div class="postgraduate-desc">专业的研究生入学考试学习资源</div>
        </div>
        
        <!-- 学习入口 -->
        <div class="study-actions">
            <div class="actions-title">
                <i class="ace-icon fa fa-external-link"></i>
                学习平台入口
            </div>
            <button class="btn-study" onclick="jumpToThirdParty();">
                <i class="ace-icon fa fa-link"></i>
                <span>进入考研学习平台</span>
            </button>
        </div>
        
        <!-- 学习统计 -->
        <div class="study-stats">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                学习统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="studyDays">0</div>
                    <div class="stat-label">学习天数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="studyHours">0</div>
                    <div class="stat-label">学习时长</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedCourses">0</div>
                    <div class="stat-label">完成课程</div>
                </div>
            </div>
        </div>
        
        <!-- 学习特色 -->
        <div class="study-features">
            <div class="features-title">
                <i class="ace-icon fa fa-star"></i>
                平台特色
            </div>
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-book"></i>
                    </div>
                    <div class="feature-title">专业课程</div>
                    <div class="feature-desc">涵盖各专业考研课程</div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-video-camera"></i>
                    </div>
                    <div class="feature-title">视频教学</div>
                    <div class="feature-desc">高清视频在线学习</div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-edit"></i>
                    </div>
                    <div class="feature-title">在线练习</div>
                    <div class="feature-desc">海量题库随时练习</div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="ace-icon fa fa-users"></i>
                    </div>
                    <div class="feature-title">名师指导</div>
                    <div class="feature-desc">专业教师在线答疑</div>
                </div>
            </div>
        </div>
        
        <!-- 学习提示 -->
        <div class="study-tips">
            <div class="tips-title">
                <i class="ace-icon fa fa-lightbulb-o"></i>
                学习提示
            </div>
            <ul class="tips-list">
                <li>建议每天保持2-3小时的学习时间</li>
                <li>制定合理的学习计划并严格执行</li>
                <li>及时复习巩固已学知识点</li>
                <li>多做练习题提高应试能力</li>
                <li>遇到问题及时向老师求助</li>
            </ul>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
            loadStudyStats();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 跳转到第三方学习平台
        function jumpToThirdParty() {
            showLoading(true);
            
            // 记录跳转日志
            $.ajax({
                url: "/student/postgraduate/logAccess",
                type: "post",
                dataType: "json",
                success: function(data) {
                    // 跳转到第三方平台
                    window.location.href = "/all/postgraduate/postgraduateExamination/study/jumpToThirdParty";
                },
                error: function() {
                    // 即使记录失败也允许跳转
                    window.location.href = "/all/postgraduate/postgraduateExamination/study/jumpToThirdParty";
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 加载学习统计
        function loadStudyStats() {
            $.ajax({
                url: "/student/postgraduate/getStudyStats",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        updateStats(data.data);
                    } else {
                        // 设置默认统计
                        updateStats({
                            studyDays: 0,
                            studyHours: 0,
                            completedCourses: 0
                        });
                    }
                },
                error: function() {
                    console.log('获取学习统计失败');
                    // 设置默认统计
                    updateStats({
                        studyDays: 0,
                        studyHours: 0,
                        completedCourses: 0
                    });
                }
            });
        }

        // 更新统计信息
        function updateStats(stats) {
            $('#studyDays').text(stats.studyDays || 0);
            $('#studyHours').text(stats.studyHours || 0);
            $('#completedCourses').text(stats.completedCourses || 0);
        }

        // 刷新数据
        function refreshData() {
            loadStudyStats();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
