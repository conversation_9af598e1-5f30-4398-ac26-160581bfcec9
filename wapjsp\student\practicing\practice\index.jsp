<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>可实习科目</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 可实习科目页面样式 */
        .practice-header {
            background: linear-gradient(135deg, var(--success-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .practice-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .practice-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .semester-info {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            text-align: center;
        }
        
        .semester-text {
            font-size: var(--font-size-base);
            color: var(--info-dark);
            font-weight: 500;
        }
        
        .search-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--success-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--success-color);
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
        
        .btn-search {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
            margin-top: var(--margin-sm);
        }
        
        .btn-search:hover {
            background: var(--success-dark);
        }
        
        .subjects-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-header i {
            color: var(--success-color);
        }
        
        .subjects-list {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .subject-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .subject-item:last-child {
            border-bottom: none;
        }
        
        .subject-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .subject-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--success-light);
            color: var(--success-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .subject-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .subject-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .subject-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 8px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .detail-item i {
            color: var(--success-color);
            width: 12px;
        }
        
        .subject-content {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .subject-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .action-btn.apply {
            color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .action-btn.apply:hover {
            background: var(--success-light);
        }
        
        .action-btn.view {
            color: var(--info-color);
            border-color: var(--info-color);
        }
        
        .action-btn.view:hover {
            background: var(--info-light);
        }
        
        .action-btn.delete {
            color: var(--error-color);
            border-color: var(--error-color);
        }
        
        .action-btn.delete:hover {
            background: var(--error-light);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-badge.pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-badge.approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-badge.rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--success-light);
            border-color: var(--success-color);
            color: var(--success-dark);
        }
        
        .btn-page.active {
            background: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .subject-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .subject-details {
                grid-template-columns: 1fr;
            }
            
            .subject-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">可实习科目</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 可实习科目头部 -->
        <div class="practice-header">
            <div class="practice-title">可实习科目</div>
            <div class="practice-desc">查看和申请实习科目</div>
        </div>
        
        <!-- 学期信息 -->
        <div class="semester-info">
            <div class="semester-text">
                <cache:get var="zxjxjhh" region="jh_zxjxjhb_view" key="${xnxq}" keyName="zxjxjhh" targetprop="zxjxjhm" out="true"/>
            </div>
        </div>
        
        <!-- 搜索容器 -->
        <div class="search-container">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                搜索科目
            </div>
            
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label class="form-label">实习科目</label>
                    <input type="text" class="form-input" id="kcm" name="kcm" placeholder="请输入实习科目名称">
                </div>
                
                <div class="form-group">
                    <label class="form-label">指导教师</label>
                    <input type="text" class="form-input" id="zdjs" name="zdjs" placeholder="请输入指导教师姓名">
                </div>
                
                <button type="button" class="btn-search" onclick="searchSubjects();">
                    <i class="ace-icon fa fa-search"></i>
                    查询
                </button>
            </form>
        </div>
        
        <!-- 科目列表 -->
        <div class="subjects-container">
            <div class="container-header">
                <i class="ace-icon fa fa-list"></i>
                科目列表
            </div>
            
            <div class="subjects-list" id="subjectsList">
                <!-- 动态加载科目列表 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let pageConditions = "";

        $(function() {
            initPage();
            loadSubjects();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载科目列表
        function loadSubjects(page = 1) {
            currentPage = page;
            showLoading(true);

            const kcm = $('#kcm').val();
            const zdjs = $('#zdjs').val();

            $.ajax({
                url: "/student/practicing/practice/search",
                type: "post",
                data: pageConditions + "&pageNum=" + page + "&pageSize=" + pageSize + "&kcm=" + kcm + "&zdjs=" + zdjs,
                dataType: "json",
                success: function(data) {
                    if (data && data.records) {
                        renderSubjects(data.records);
                        renderPagination(data.pageContext);
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染科目列表
        function renderSubjects(subjects) {
            const container = $('#subjectsList');

            if (!subjects || subjects.length === 0) {
                showEmptyState();
                return;
            }

            let subjectsHtml = '';

            subjects.forEach((subject, index) => {
                const serialNumber = (currentPage - 1) * pageSize + 1 + index;
                const hasApplied = subject.XH && subject.XH !== "";
                const statusInfo = getStatusInfo(subject.SFQR);

                subjectsHtml += `
                    <div class="subject-item">
                        <div class="subject-header">
                            <div class="subject-number">${serialNumber}</div>
                            <div class="subject-info">
                                <div class="subject-name">${subject.KCM || '未设置'}</div>
                                ${hasApplied ? `<div class="status-badge ${statusInfo.class}">${statusInfo.text}</div>` : ''}
                            </div>
                        </div>

                        <div class="subject-details">
                            <div class="detail-item">
                                <i class="ace-icon fa fa-user"></i>
                                <span>${subject.ZDJS || '未分配'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-building"></i>
                                <span>${subject.QYZDJS || '未分配'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-map-marker"></i>
                                <span>${subject.JDMC || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-users"></i>
                                <span>${subject.RNRS || '0'}人</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-graduation-cap"></i>
                                <span>${subject.MXZY || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-calendar"></i>
                                <span>${getTimeRange(subject.KSSJ, subject.JSSJ)}</span>
                            </div>
                        </div>

                        ${subject.SXNR ? `
                            <div class="subject-content">
                                <strong>实习内容：</strong>${subject.SXNR}
                            </div>
                        ` : ''}

                        <div class="subject-actions">
                            ${createActionButtons(subject, hasApplied)}
                            <button class="action-btn view" onclick="viewTaskBook('${subject.XNXQ}', '${subject.KCH}', '${subject.KXH}', '${subject.MBID}');">
                                <i class="ace-icon fa fa-book"></i>
                                任务书
                            </button>
                            <button class="action-btn view" onclick="viewOutline('${subject.XNXQ}', '${subject.KCH}', '${subject.KXH}');">
                                <i class="ace-icon fa fa-file-text"></i>
                                大纲
                            </button>
                        </div>
                    </div>
                `;
            });

            container.html(subjectsHtml);
        }

        // 创建操作按钮
        function createActionButtons(subject, hasApplied) {
            if (!hasApplied) {
                return `
                    <button class="action-btn apply" onclick="applySubject('${subject.XNXQ}', '${subject.KCH}', '${subject.KXH}', '${subject.RNRS}');">
                        <i class="ace-icon fa fa-plus"></i>
                        申请
                    </button>
                `;
            } else {
                if (!subject.SFQR || subject.SFQR === "") {
                    return `
                        <button class="action-btn delete" onclick="removeApplication('${subject.XNXQ}', '${subject.KCH}', '${subject.KXH}', '${subject.SFQR}');">
                            <i class="ace-icon fa fa-trash"></i>
                            删除
                        </button>
                    `;
                } else {
                    return `
                        <button class="action-btn view" onclick="viewApplication('${subject.XNXQ}', '${subject.KCH}', '${subject.KXH}', '${subject.XH}');">
                            <i class="ace-icon fa fa-eye"></i>
                            查看
                        </button>
                    `;
                }
            }
        }

        // 获取状态信息
        function getStatusInfo(status) {
            if (!status || status === "") {
                return { text: "待审批", class: "pending" };
            } else if (status === "1") {
                return { text: "通过", class: "approved" };
            } else if (status === "2") {
                return { text: "未通过", class: "rejected" };
            } else {
                return { text: "待审批", class: "pending" };
            }
        }

        // 获取时间范围
        function getTimeRange(startTime, endTime) {
            if (!startTime && !endTime) {
                return '未设置';
            } else if (startTime && !endTime) {
                return startTime;
            } else if (startTime && endTime) {
                return startTime + '至' + endTime;
            } else {
                return endTime;
            }
        }

        // 显示空状态
        function showEmptyState() {
            const container = $('#subjectsList');
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-book"></i>
                    <div class="empty-state-text">暂无可实习科目</div>
                    <div class="empty-state-desc">请调整搜索条件后重试</div>
                </div>
            `);
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            if (!pageContext || pageContext.totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="loadSubjects(${currentPage - 1});">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<button class="btn-page" onclick="loadSubjects(1);">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="loadSubjects(${i});">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                paginationHtml += `<button class="btn-page" onclick="loadSubjects(${totalPages});">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="loadSubjects(${currentPage + 1});">下一页</button>`;

            container.html(paginationHtml);
            $('#paginationContainer').show();
        }

        // 搜索科目
        function searchSubjects() {
            loadSubjects(1);
        }

        // 申请科目
        function applySubject(xnxq, kch, kxh, rnrs) {
            showLoading(true);

            // 先检查容纳人数
            $.ajax({
                url: "/student/practicing/practice/getSxXkbCount",
                type: "get",
                data: {
                    xnxq: xnxq,
                    kch: kch,
                    kxh: kxh
                },
                dataType: "json",
                success: function(data) {
                    if (parseInt(data.result) < parseInt(rnrs)) {
                        // 可以申请
                        submitApplication(xnxq, kch, kxh);
                    } else {
                        showError("当前科目已达容纳上限不能申请！");
                        showLoading(false);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    showLoading(false);
                }
            });
        }

        // 提交申请
        function submitApplication(xnxq, kch, kxh) {
            $.ajax({
                url: "/student/practicing/practice/saveAdd",
                type: "post",
                data: {
                    tokenValue: $('#tokenValue').val(),
                    xnxq: xnxq,
                    kch: kch,
                    kxh: kxh
                },
                dataType: "json",
                success: function(data) {
                    $('#tokenValue').val(data.token);

                    if (data.result === "ok") {
                        showSuccess("申请成功！");
                        loadSubjects(currentPage);
                    } else {
                        showError(data.result);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 删除申请
        function removeApplication(xnxq, kch, kxh, sfqr) {
            if (sfqr === "1" || sfqr === "2") {
                showError("已审批，不能删除！");
                return;
            }

            if (confirm('是否删除当前申请？')) {
                showLoading(true);

                $.ajax({
                    url: "/student/practicing/practice/removeInfo",
                    type: "post",
                    data: {
                        tokenValue: $('#tokenValue').val(),
                        xnxq: xnxq,
                        kch: kch,
                        kxh: kxh
                    },
                    dataType: "json",
                    success: function(data) {
                        $('#tokenValue').val(data.token);

                        if (data.result === "ok") {
                            showSuccess("删除成功！");
                            loadSubjects(currentPage);
                        } else {
                            showError(data.result);
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 查看申请
        function viewApplication(xnxq, kch, kxh, xh) {
            const url = `/student/practicing/practice/showApply?xnxq=${xnxq}&kch=${kch}&kxh=${kxh}&xh=${xh}`;

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看任务书
        function viewTaskBook(xnxq, kch, kxh, mbid) {
            showLoading(true);

            $.ajax({
                url: "/student/practicing/practice/showTaskBook",
                type: "post",
                data: {
                    xnxq: xnxq,
                    kch: kch,
                    kxh: kxh,
                    mbid: mbid
                },
                dataType: "json",
                success: function(data) {
                    if (data.result === "ok") {
                        showTaskBookModal(data.list);
                    } else if (data.result === "no") {
                        showError("指导教师尚未维护任务书或任务书未启用");
                    } else {
                        showError(data.result);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 查看实习大纲
        function viewOutline(xnxq, kch, kxh) {
            const url = `/student/practicing/practice/showOutline?xnxq=${xnxq}&kch=${kch}&kxh=${kxh}`;

            if (parent && parent.addTab) {
                parent.addTab('实习大纲', url);
            } else {
                window.location.href = url;
            }
        }

        // 显示任务书模态框
        function showTaskBookModal(taskList) {
            if (!taskList || taskList.length === 0) {
                showError("暂无任务书内容");
                return;
            }

            let modalHtml = `
                <div class="modal-overlay" onclick="closeTaskBookModal();">
                    <div class="modal-content" onclick="event.stopPropagation();">
                        <div class="modal-header">
                            <h3>实习任务书</h3>
                            <button class="modal-close" onclick="closeTaskBookModal();">×</button>
                        </div>
                        <div class="modal-body">
            `;

            taskList.forEach(function(task) {
                modalHtml += `
                    <div class="task-item">
                        <div class="task-title">${task[2] || ''}</div>
                        <div class="task-content">${task[1] || ''}</div>
                    </div>
                `;
            });

            modalHtml += `
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
        }

        // 关闭任务书模态框
        function closeTaskBookModal() {
            $('.modal-overlay').remove();
        }

        // 刷新数据
        function refreshData() {
            loadSubjects(currentPage);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>

    <style>
        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            max-width: 90%;
            max-height: 80%;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: var(--padding-md);
            max-height: 60vh;
            overflow-y: auto;
        }

        .task-item {
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }

        .task-content {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.6;
            white-space: pre-wrap;
        }
    </style>
</body>
</html>
