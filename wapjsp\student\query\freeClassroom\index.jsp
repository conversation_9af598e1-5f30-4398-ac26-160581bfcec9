<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>空闲教室查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 空闲教室查询页面样式 */
        .free-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .search-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .search-row {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .search-item {
            flex: 1;
        }
        
        .search-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .search-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .time-selector {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: var(--spacing-xs);
            margin-top: var(--margin-sm);
        }
        
        .time-slot {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: 8px 4px;
            text-align: center;
            font-size: var(--font-size-mini);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .time-slot:hover {
            background: var(--primary-light);
        }
        
        .time-slot.selected {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-search {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-reset {
            background: var(--text-disabled);
            color: white;
        }
        
        .result-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .result-summary.show {
            display: block;
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }
        
        .stat-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .classroom-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .classroom-list.show {
            display: block;
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .sort-selector {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .classroom-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .classroom-item:last-child {
            border-bottom: none;
        }
        
        .classroom-item:active {
            background: var(--bg-color-active);
        }
        
        .classroom-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .classroom-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .classroom-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .type-normal {
            background: var(--success-light);
            color: var(--success-color);
        }
        
        .type-multimedia {
            background: var(--info-light);
            color: var(--info-color);
        }
        
        .type-lab {
            background: var(--warning-light);
            color: var(--warning-color);
        }
        
        .type-computer {
            background: var(--error-light);
            color: var(--error-color);
        }
        
        .type-language {
            background: var(--primary-light);
            color: var(--primary-color);
        }
        
        .classroom-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .free-periods {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
        }
        
        .periods-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .periods-list {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .period-tag {
            background: var(--success-color);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .classroom-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-reserve {
            background: var(--success-color);
            color: white;
        }
        
        .filter-bar {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .filter-bar.show {
            display: block;
        }
        
        .filter-tabs {
            display: flex;
            gap: var(--spacing-xs);
            overflow-x: auto;
        }
        
        .filter-tab {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 20px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            white-space: nowrap;
            transition: all var(--transition-base);
        }
        
        .filter-tab:hover {
            background: var(--primary-light);
        }
        
        .filter-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .classroom-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .classroom-detail-modal.show {
            display: flex;
        }
        
        .classroom-detail-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        
        .classroom-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .classroom-detail-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .classroom-detail-close {
            color: var(--text-secondary);
            cursor: pointer;
            font-size: var(--font-size-h4);
        }
        
        .classroom-detail-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">空闲教室查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="free-header">
            <div class="header-title">空闲教室查询</div>
            <div class="header-subtitle">查找指定时间段的空闲教室</div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                <span>查询条件</span>
            </div>

            <div class="search-form">
                <div class="search-row">
                    <div class="search-item">
                        <div class="search-label">查询日期</div>
                        <input type="date" class="search-input" id="queryDate">
                    </div>
                    <div class="search-item">
                        <div class="search-label">教学楼</div>
                        <select class="search-input" id="building">
                            <option value="">不限教学楼</option>
                        </select>
                    </div>
                </div>

                <div class="search-row">
                    <div class="search-item">
                        <div class="search-label">教室类型</div>
                        <select class="search-input" id="classroomType">
                            <option value="">不限类型</option>
                            <option value="normal">普通教室</option>
                            <option value="multimedia">多媒体教室</option>
                            <option value="lab">实验室</option>
                            <option value="computer">机房</option>
                            <option value="language">语音室</option>
                        </select>
                    </div>
                    <div class="search-item">
                        <div class="search-label">最小容量</div>
                        <input type="number" class="search-input" id="minCapacity" placeholder="不限" min="1">
                    </div>
                </div>

                <div class="search-item">
                    <div class="search-label">时间段选择</div>
                    <div class="time-selector" id="timeSelector">
                        <div class="time-slot" data-period="1">第1-2节<br>08:00-09:40</div>
                        <div class="time-slot" data-period="2">第3-4节<br>10:00-11:40</div>
                        <div class="time-slot" data-period="3">第5-6节<br>14:00-15:40</div>
                        <div class="time-slot" data-period="4">第7-8节<br>16:00-17:40</div>
                        <div class="time-slot" data-period="5">第9-10节<br>19:00-20:40</div>
                    </div>
                </div>

                <div class="search-actions">
                    <button class="btn-mobile btn-search flex-1" onclick="searchFreeClassrooms();">
                        <i class="ace-icon fa fa-search"></i>
                        <span>查询</span>
                    </button>
                    <button class="btn-mobile btn-reset flex-1" onclick="resetSearch();">
                        <i class="ace-icon fa fa-refresh"></i>
                        <span>重置</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 查询结果统计 -->
        <div class="result-summary" id="resultSummary">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                <span>查询结果</span>
            </div>

            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalCount">0</div>
                    <div class="stat-label">空闲教室</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="multimediaCount">0</div>
                    <div class="stat-label">多媒体教室</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="largeCount">0</div>
                    <div class="stat-label">大型教室</div>
                </div>
            </div>
        </div>

        <!-- 筛选栏 -->
        <div class="filter-bar" id="filterBar">
            <div class="filter-tabs">
                <div class="filter-tab active" data-filter="all">全部</div>
                <div class="filter-tab" data-filter="normal">普通教室</div>
                <div class="filter-tab" data-filter="multimedia">多媒体</div>
                <div class="filter-tab" data-filter="lab">实验室</div>
                <div class="filter-tab" data-filter="computer">机房</div>
                <div class="filter-tab" data-filter="language">语音室</div>
                <div class="filter-tab" data-filter="large">大型(>100人)</div>
            </div>
        </div>

        <!-- 教室列表 -->
        <div class="classroom-list" id="classroomList">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span>空闲教室列表</span>
                </div>
                <div class="sort-selector" onclick="toggleSort();">
                    <i class="ace-icon fa fa-sort"></i>
                    <span id="sortText">按容量排序</span>
                </div>
            </div>

            <div id="classroomItems">
                <!-- 教室列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-search"></i>
            <div id="emptyMessage">请选择查询条件查找空闲教室</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>查询中...</span>
        </div>
    </div>

    <!-- 教室详情模态框 -->
    <div class="classroom-detail-modal" id="classroomDetailModal">
        <div class="classroom-detail-content">
            <div class="classroom-detail-header">
                <div class="classroom-detail-title" id="classroomDetailTitle">教室详情</div>
                <div class="classroom-detail-close" onclick="closeClassroomDetail();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="classroom-detail-body" id="classroomDetailBody">
                <!-- 教室详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let freeClassrooms = [];
        let filteredClassrooms = [];
        let selectedPeriods = [];
        let buildings = [];
        let currentFilter = 'all';
        let currentSort = 'capacity';

        $(function() {
            initPage();
            loadBuildings();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            setDefaultDate();
        }

        // 设置默认日期为今天
        function setDefaultDate() {
            const today = new Date();
            const dateStr = today.toISOString().split('T')[0];
            $('#queryDate').val(dateStr);
        }

        // 绑定事件
        function bindEvents() {
            // 时间段选择
            $('.time-slot').click(function() {
                const period = $(this).data('period');
                $(this).toggleClass('selected');

                if ($(this).hasClass('selected')) {
                    if (!selectedPeriods.includes(period)) {
                        selectedPeriods.push(period);
                    }
                } else {
                    selectedPeriods = selectedPeriods.filter(p => p !== period);
                }
            });

            // 筛选标签
            $('.filter-tab').click(function() {
                $('.filter-tab').removeClass('active');
                $(this).addClass('active');
                currentFilter = $(this).data('filter');
                applyFilter();
            });
        }

        // 加载教学楼列表
        function loadBuildings() {
            $.ajax({
                url: "/student/query/freeClassroom/getBuildings",
                type: "post",
                dataType: "json",
                success: function(data) {
                    buildings = data.buildings || [];
                    renderBuildingOptions();
                },
                error: function() {
                    console.log('加载教学楼列表失败');
                }
            });
        }

        // 渲染教学楼选项
        function renderBuildingOptions() {
            const select = $('#building');
            select.find('option:not(:first)').remove();

            buildings.forEach(building => {
                select.append(`<option value="${building.id}">${building.name}</option>`);
            });
        }

        // 搜索空闲教室
        function searchFreeClassrooms() {
            const queryDate = $('#queryDate').val();
            const building = $('#building').val();
            const classroomType = $('#classroomType').val();
            const minCapacity = $('#minCapacity').val();

            if (!queryDate) {
                showError('请选择查询日期');
                return;
            }

            if (selectedPeriods.length === 0) {
                showError('请选择至少一个时间段');
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/student/query/freeClassroom/searchFreeClassrooms",
                type: "post",
                data: {
                    queryDate: queryDate,
                    building: building,
                    classroomType: classroomType,
                    minCapacity: minCapacity,
                    periods: selectedPeriods.join(',')
                },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        freeClassrooms = data.classrooms || [];
                        filteredClassrooms = [...freeClassrooms];
                        updateResultSummary();
                        renderClassroomList();
                        showResults();
                    } else {
                        showError(data.message || '查询失败');
                        hideResults();
                    }
                    showLoading(false);
                },
                error: function() {
                    showError('查询失败，请重试');
                    showLoading(false);
                }
            });
        }

        // 更新结果统计
        function updateResultSummary() {
            const totalCount = freeClassrooms.length;
            const multimediaCount = freeClassrooms.filter(c => c.type === 'multimedia').length;
            const largeCount = freeClassrooms.filter(c => c.capacity > 100).length;

            $('#totalCount').text(totalCount);
            $('#multimediaCount').text(multimediaCount);
            $('#largeCount').text(largeCount);
        }

        // 渲染教室列表
        function renderClassroomList() {
            const container = $('#classroomItems');
            container.empty();

            if (filteredClassrooms.length === 0) {
                container.html(`
                    <div class="empty-state" style="padding: 40px; text-align: center;">
                        <i class="ace-icon fa fa-home" style="font-size: 48px; color: var(--text-disabled); margin-bottom: 16px;"></i>
                        <div style="color: var(--text-secondary);">未找到符合条件的空闲教室</div>
                    </div>
                `);
                return;
            }

            filteredClassrooms.forEach(classroom => {
                const classroomHtml = createClassroomItem(classroom);
                container.append(classroomHtml);
            });
        }

        // 创建教室项
        function createClassroomItem(classroom) {
            const typeClass = getTypeClass(classroom.type);
            const typeText = getClassroomTypeText(classroom.type);

            return `
                <div class="classroom-item" onclick="showClassroomDetail('${classroom.id}')">
                    <div class="classroom-basic">
                        <div class="classroom-name">${classroom.name}</div>
                        <div class="classroom-type ${typeClass}">${typeText}</div>
                    </div>
                    <div class="classroom-details">
                        <div class="detail-item">
                            <span>教学楼:</span>
                            <span>${classroom.buildingName}</span>
                        </div>
                        <div class="detail-item">
                            <span>楼层:</span>
                            <span>${classroom.floor}楼</span>
                        </div>
                        <div class="detail-item">
                            <span>容量:</span>
                            <span>${classroom.capacity}人</span>
                        </div>
                        <div class="detail-item">
                            <span>设备:</span>
                            <span>${classroom.equipment || '基础设备'}</span>
                        </div>
                    </div>
                    <div class="free-periods">
                        <div class="periods-title">空闲时段:</div>
                        <div class="periods-list">
                            ${createPeriodTags(classroom.freePeriods)}
                        </div>
                    </div>
                    <div class="classroom-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showClassroomDetail('${classroom.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        <button class="btn-mobile btn-reserve" onclick="event.stopPropagation(); reserveClassroom('${classroom.id}');">
                            <i class="ace-icon fa fa-calendar-plus-o"></i>
                            <span>预约</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 创建时段标签
        function createPeriodTags(periods) {
            const periodNames = {
                1: '1-2节',
                2: '3-4节',
                3: '5-6节',
                4: '7-8节',
                5: '9-10节'
            };

            return periods.map(period =>
                `<span class="period-tag">${periodNames[period] || period}</span>`
            ).join('');
        }

        // 获取类型样式类
        function getTypeClass(type) {
            return `type-${type}`;
        }

        // 获取教室类型文本
        function getClassroomTypeText(type) {
            switch(type) {
                case 'normal': return '普通教室';
                case 'multimedia': return '多媒体';
                case 'lab': return '实验室';
                case 'computer': return '机房';
                case 'language': return '语音室';
                default: return '普通教室';
            }
        }

        // 应用筛选
        function applyFilter() {
            if (currentFilter === 'all') {
                filteredClassrooms = [...freeClassrooms];
            } else if (currentFilter === 'large') {
                filteredClassrooms = freeClassrooms.filter(c => c.capacity > 100);
            } else {
                filteredClassrooms = freeClassrooms.filter(c => c.type === currentFilter);
            }

            applySorting();
            renderClassroomList();
        }

        // 切换排序
        function toggleSort() {
            if (currentSort === 'capacity') {
                currentSort = 'name';
                $('#sortText').text('按名称排序');
            } else {
                currentSort = 'capacity';
                $('#sortText').text('按容量排序');
            }

            applySorting();
            renderClassroomList();
        }

        // 应用排序
        function applySorting() {
            if (currentSort === 'capacity') {
                filteredClassrooms.sort((a, b) => b.capacity - a.capacity);
            } else {
                filteredClassrooms.sort((a, b) => a.name.localeCompare(b.name));
            }
        }

        // 显示教室详情
        function showClassroomDetail(classroomId) {
            const classroom = freeClassrooms.find(c => c.id === classroomId);
            if (!classroom) return;

            let detailHtml = `
                <div class="detail-item">
                    <span class="detail-label">教室名称:</span>
                    <span class="detail-value">${classroom.name}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">教学楼:</span>
                    <span class="detail-value">${classroom.buildingName}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">楼层:</span>
                    <span class="detail-value">${classroom.floor}楼</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">教室类型:</span>
                    <span class="detail-value">${getClassroomTypeText(classroom.type)}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">容量:</span>
                    <span class="detail-value">${classroom.capacity}人</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">设备配置:</span>
                    <span class="detail-value">${classroom.equipment || '基础设备'}</span>
                </div>
            `;

            if (classroom.description) {
                detailHtml += `
                    <div class="detail-item">
                        <span class="detail-label">教室描述:</span>
                        <span class="detail-value">${classroom.description}</span>
                    </div>
                `;
            }

            if (classroom.note) {
                detailHtml += `
                    <div class="detail-item">
                        <span class="detail-label">使用说明:</span>
                        <span class="detail-value">${classroom.note}</span>
                    </div>
                `;
            }

            $('#classroomDetailTitle').text(`${classroom.name} - 详细信息`);
            $('#classroomDetailBody').html(detailHtml);
            $('#classroomDetailModal').addClass('show');
        }

        // 关闭教室详情
        function closeClassroomDetail() {
            $('#classroomDetailModal').removeClass('show');
        }

        // 预约教室
        function reserveClassroom(classroomId) {
            const classroom = freeClassrooms.find(c => c.id === classroomId);
            if (!classroom) return;

            const message = `确定要预约教室"${classroom.name}"吗？\n\n预约功能需要登录并填写预约信息。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        // 这里可以跳转到预约页面或显示预约表单
                        showError('预约功能正在开发中，敬请期待');
                    }
                });
            } else {
                if (confirm(message)) {
                    showError('预约功能正在开发中，敬请期待');
                }
            }
        }

        // 重置搜索
        function resetSearch() {
            $('#queryDate').val('');
            $('#building').val('');
            $('#classroomType').val('');
            $('#minCapacity').val('');

            selectedPeriods = [];
            $('.time-slot').removeClass('selected');

            currentFilter = 'all';
            $('.filter-tab').removeClass('active');
            $('.filter-tab[data-filter="all"]').addClass('active');

            freeClassrooms = [];
            filteredClassrooms = [];

            setDefaultDate();
            hideResults();
            showEmptyState('请选择查询条件查找空闲教室');
        }

        // 显示结果
        function showResults() {
            $('#resultSummary').addClass('show');
            $('#filterBar').addClass('show');
            $('#classroomList').addClass('show');
            hideEmptyState();
        }

        // 隐藏结果
        function hideResults() {
            $('#resultSummary').removeClass('show');
            $('#filterBar').removeClass('show');
            $('#classroomList').removeClass('show');
        }

        // 刷新数据
        function refreshData() {
            if (selectedPeriods.length > 0 && $('#queryDate').val()) {
                searchFreeClassrooms();
            }
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            hideResults();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#classroomDetailModal').click(function(e) {
            if (e.target === this) {
                closeClassroomDetail();
            }
        });

        // 初始显示空状态
        showEmptyState('请选择查询条件查找空闲教室');
    </script>
</body>
</html>
