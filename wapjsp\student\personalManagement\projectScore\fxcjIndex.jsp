<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>分项成绩列表</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 分项成绩页面样式 */
        .detail-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .detail-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .detail-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .project-info {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .project-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .project-title i {
            color: var(--info-color);
        }
        
        .project-name {
            font-size: var(--font-size-base);
            color: var(--primary-color);
            font-weight: 500;
            line-height: 1.4;
        }
        
        .scores-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .scores-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .scores-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .scores-title i {
            color: var(--success-color);
        }
        
        .score-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .score-item:last-child {
            border-bottom: none;
        }
        
        .score-header-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
        }
        
        .score-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .score-content {
            flex: 1;
        }
        
        .score-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .score-value {
            background: var(--success-color);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 600;
            min-width: 60px;
            text-align: center;
        }
        
        .score-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .coefficient-badge {
            background: var(--info-light);
            color: var(--info-dark);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .summary-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .summary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .summary-title i {
            color: var(--warning-color);
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .stat-item {
            text-align: center;
            padding: var(--padding-md);
            background: var(--bg-secondary);
            border-radius: 8px;
        }
        
        .stat-value {
            font-size: var(--font-size-h2);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        @media (max-width: 480px) {
            .score-details {
                grid-template-columns: 1fr;
            }
            
            .summary-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">分项成绩列表</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 分项成绩头部 -->
        <div class="detail-header">
            <div class="detail-title">分项成绩列表</div>
            <div class="detail-desc">查看详细分项成绩信息</div>
        </div>
        
        <!-- 项目信息 -->
        <div class="project-info">
            <div class="project-title">
                <i class="ace-icon fa fa-file-text"></i>
                项目题目
            </div>
            <div class="project-name">${tmxxb.tmmc_xs}</div>
        </div>
        
        <!-- 统计信息 -->
        <div class="summary-section">
            <div class="summary-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                成绩统计
            </div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-value" id="totalItems">${fn:length(scoresList)}</div>
                    <div class="stat-label">分项数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalCoefficient">100</div>
                    <div class="stat-label">总系数(%)</div>
                </div>
            </div>
        </div>
        
        <!-- 分项成绩列表 -->
        <c:choose>
            <c:when test="${not empty scoresList && fn:length(scoresList) > 0}">
                <div class="scores-section">
                    <div class="scores-header">
                        <div class="scores-title">
                            <i class="ace-icon fa fa-list"></i>
                            分项成绩详情
                        </div>
                    </div>
                    
                    <div id="scoresList">
                        <c:forEach items="${scoresList}" var="s" varStatus="i">
                            <div class="score-item">
                                <div class="score-header-info">
                                    <div style="display: flex; align-items: center;">
                                        <div class="score-index">${i.index + 1}</div>
                                        <div class="score-content">
                                            <div class="score-name">${s[0]}</div>
                                        </div>
                                    </div>
                                    <div class="score-value">${s[2]}</div>
                                </div>
                                
                                <div class="score-details">
                                    <div class="detail-item">
                                        <span class="detail-label">分项成绩</span>
                                        <span style="color: var(--success-color); font-weight: 600;">${s[2]}</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">所占系数</span>
                                        <span class="coefficient-badge">${s[1]}%</span>
                                    </div>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </div>
            </c:when>
            <c:otherwise>
                <div class="empty-state">
                    <i class="ace-icon fa fa-file-text-o"></i>
                    <div>暂无分项成绩数据</div>
                </div>
            </c:otherwise>
        </c:choose>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 返回
        function goBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                history.back();
            }
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
