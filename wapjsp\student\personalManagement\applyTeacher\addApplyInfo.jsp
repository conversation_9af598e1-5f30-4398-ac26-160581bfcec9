<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>
        <c:if test="${empty sqb}">新增申请信息</c:if>
        <c:if test="${not empty sqb}">修改申请信息</c:if>
    </title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 申请导师新增/编辑页面样式 */
        .form-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .form-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .form-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .form-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            color: var(--primary-color);
        }
        
        .required {
            color: var(--error-color);
            margin-right: 4px;
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .teachers-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .teachers-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .teachers-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .teachers-title i {
            color: var(--success-color);
        }
        
        .teacher-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .teacher-item:last-child {
            border-bottom: none;
        }
        
        .teacher-item.selected {
            background: var(--primary-light);
            border-color: var(--primary-color);
        }
        
        .teacher-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .teacher-radio {
            margin-right: var(--margin-sm);
            margin-top: 2px;
        }
        
        .teacher-info {
            flex: 1;
        }
        
        .teacher-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .teacher-code {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .teacher-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .teacher-detail {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .teacher-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .btn-view-intro {
            background: var(--info-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            cursor: pointer;
        }
        
        .form-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-save-draft {
            flex: 1;
            background: var(--warning-color);
            color: white;
        }
        
        .btn-submit {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel {
            flex: 1;
            background: var(--text-disabled);
            color: white;
        }
        
        .empty-teachers {
            padding: var(--padding-lg);
            text-align: center;
            color: var(--text-secondary);
        }
        
        .empty-teachers i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        /* 导师简介弹窗样式 */
        .intro-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .intro-content {
            background: var(--bg-primary);
            border-radius: 8px;
            max-width: 90%;
            max-height: 80%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .intro-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .intro-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .intro-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
        }
        
        .intro-body {
            padding: var(--padding-md);
            overflow-y: auto;
            flex: 1;
        }
        
        @media (max-width: 480px) {
            .teacher-details {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" name="tokenValue" id="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">
                <c:if test="${empty sqb}">新增申请</c:if>
                <c:if test="${not empty sqb}">修改申请</c:if>
            </div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 表单头部 -->
        <div class="form-header">
            <div class="form-title">
                <c:if test="${empty sqb}">新增申请信息</c:if>
                <c:if test="${not empty sqb}">修改申请信息</c:if>
            </div>
            <div class="form-subtitle">${sy[0][1]}</div>
        </div>
        
        <form name="addTeacherInfoForm" id="addTeacherInfoForm" method="post">
            <input type="hidden" name="sqbh" value="${sqb.sqbh}"/>
            
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    基本信息
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <span class="required">*</span>导师类型
                    </label>
                    <select id="add_dslx" name="dslx" class="form-select" onchange="queryTeachers(this.value);">
                        <option value="">--请选择--</option>
                        <c:forEach var="dslx" items="${dslxs}">
                            <option value="${dslx[0]}" <c:if test="${sqb.dslxdm == dslx[0]}"> selected </c:if>>${dslx[1]}</option>
                        </c:forEach>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">备注</label>
                    <textarea name="bz" class="form-textarea" placeholder="请输入备注信息">${sqb.bz}</textarea>
                </div>
            </div>
            
            <!-- 导师选择 -->
            <div class="teachers-section">
                <div class="teachers-header">
                    <div class="teachers-title">
                        <i class="ace-icon fa fa-users"></i>
                        <span class="required">*</span>选择导师
                    </div>
                </div>
                
                <div id="teachersList">
                    <div class="empty-teachers">
                        <i class="ace-icon fa fa-user-plus"></i>
                        <div>请先选择导师类型</div>
                    </div>
                </div>
            </div>
            
            <!-- 底部操作按钮 -->
            <div style="height: 80px;"></div> <!-- 为固定底部按钮预留空间 -->
        </form>
        
        <!-- 固定底部操作按钮 -->
        <div class="form-actions">
            <button type="button" class="btn-mobile btn-save-draft" onclick="saveApplication('0');">
                <i class="ace-icon fa fa-save"></i>
                <span>暂存</span>
            </button>
            <button type="button" class="btn-mobile btn-submit" onclick="saveApplication('1');">
                <i class="ace-icon fa fa-check"></i>
                <span>提交</span>
            </button>
            <button type="button" class="btn-mobile btn-cancel" onclick="goBack();">
                <i class="ace-icon fa fa-times"></i>
                <span>取消</span>
            </button>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>
    
    <!-- 导师简介弹窗 -->
    <div class="intro-modal" id="introModal">
        <div class="intro-content">
            <div class="intro-header">
                <div class="intro-title">导师简介</div>
                <button class="intro-close" onclick="closeIntroModal();">×</button>
            </div>
            <div class="intro-body" id="introBody">
                <!-- 动态加载导师简介内容 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let teachersData = [];
        let selectedTeacher = null;

        $(function() {
            initPage();

            // 如果是编辑模式，加载导师数据
            const dslx = $("#add_dslx").val();
            if (dslx) {
                queryTeachers(dslx);

                // 延迟选中已选择的导师
                setTimeout(function() {
                    const selectedJsh = '${sqb.jsh}';
                    if (selectedJsh) {
                        $("input[name='jsh'][value='" + selectedJsh + "']").prop('checked', true);
                        updateSelectedTeacher(selectedJsh);
                    }
                }, 500);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 查询导师列表
        function queryTeachers(dslx) {
            $("#teachersList").html('<div class="empty-teachers"><i class="ace-icon fa fa-spinner fa-spin"></i><div>加载中...</div></div>');

            if (dslx === "") {
                $("#teachersList").html('<div class="empty-teachers"><i class="ace-icon fa fa-user-plus"></i><div>请先选择导师类型</div></div>');
                return;
            }

            $.ajax({
                url: "/students/personalManagement/applyTeacher/queryTeachers",
                type: "post",
                data: "dslx=" + dslx,
                dataType: "json",
                success: function(data) {
                    if (data && data.length > 0) {
                        teachersData = data;
                        renderTeachersList();
                    } else {
                        $("#teachersList").html('<div class="empty-teachers"><i class="ace-icon fa fa-user-times"></i><div>未查询到导师！</div></div>');
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取导师数据失败！");
                    $("#teachersList").html('<div class="empty-teachers"><i class="ace-icon fa fa-exclamation-triangle"></i><div>加载失败，请重试</div></div>');
                }
            });
        }

        // 渲染导师列表
        function renderTeachersList() {
            const container = $('#teachersList');
            container.empty();

            teachersData.forEach(function(teacher, index) {
                const teacherHtml = createTeacherItem(teacher, index);
                container.append(teacherHtml);
            });

            // 绑定单选框事件
            $("input[name='jsh']").change(function() {
                updateSelectedTeacher($(this).val());
            });
        }

        // 创建导师项目HTML
        function createTeacherItem(teacher, index) {
            const jsh = teacher[0] || '';
            const jsm = teacher[1] || '';
            const xb = teacher[2] || '';
            const jg = teacher[3] || '';
            const zc = teacher[4] || '';
            const zdrl = teacher[5] || '';
            const sqzxss = teacher[6] || '';
            const ytgxss = teacher[7] || '';

            return `
                <div class="teacher-item" data-jsh="${jsh}">
                    <div class="teacher-header">
                        <input type="radio" name="jsh" value="${jsh}" class="teacher-radio">
                        <div class="teacher-info">
                            <div class="teacher-name">${jsm}</div>
                            <div class="teacher-code">教师号：${jsh}</div>
                        </div>
                    </div>

                    <div class="teacher-details">
                        <div class="teacher-detail">
                            <span class="detail-label">性别</span>
                            <span>${xb}</span>
                        </div>
                        <div class="teacher-detail">
                            <span class="detail-label">职称</span>
                            <span>${zc}</span>
                        </div>
                        <div class="teacher-detail">
                            <span class="detail-label">所在机构</span>
                            <span>${jg}</span>
                        </div>
                        <div class="teacher-detail">
                            <span class="detail-label">指导容量</span>
                            <span>${zdrl}</span>
                        </div>
                        <div class="teacher-detail">
                            <span class="detail-label">申请中学生数</span>
                            <span>${sqzxss}</span>
                        </div>
                        <div class="teacher-detail">
                            <span class="detail-label">已通过学生数</span>
                            <span>${ytgxss}</span>
                        </div>
                    </div>

                    <div class="teacher-actions">
                        <button type="button" class="btn-view-intro" onclick="viewTeacherIntro('${jsh}');">
                            <i class="ace-icon fa fa-eye"></i>
                            查看简介
                        </button>
                    </div>
                </div>
            `;
        }

        // 更新选中的导师
        function updateSelectedTeacher(jsh) {
            // 移除所有选中状态
            $('.teacher-item').removeClass('selected');

            // 添加选中状态
            $(`.teacher-item[data-jsh="${jsh}"]`).addClass('selected');

            selectedTeacher = jsh;
        }

        // 查看导师简介
        function viewTeacherIntro(jsh) {
            showLoading(true);

            $.ajax({
                url: "/students/personalManagement/applyTeacher/viewTeacherJJ",
                type: "post",
                data: "jsh=" + jsh + "&dslxdm=" + $("#add_dslx").val(),
                success: function(data) {
                    if (data.jj) {
                        $("#introBody").html(data.jj);
                        $("#introModal").css('display', 'flex');
                    } else {
                        showError("该导师未维护简介！");
                    }
                },
                error: function() {
                    showError("获取导师简介失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 关闭导师简介弹窗
        function closeIntroModal() {
            $("#introModal").hide();
        }

        // 保存申请
        function saveApplication(applyStatus) {
            // 验证导师类型
            const dslx = $("#add_dslx").val();
            if (!dslx) {
                showError("导师类型不能为空！");
                $("#add_dslx").focus();
                return;
            }

            // 验证导师选择
            const selectedJsh = $("input[name='jsh']:checked").val();
            if (!selectedJsh) {
                showError("请选择导师！");
                return;
            }

            showLoading(true);

            $.ajax({
                url: "/students/personalManagement/applyTeacher/saveUpdate",
                type: "post",
                data: $("#addTeacherInfoForm").serialize() +
                      "&apply_status=" + applyStatus +
                      "&tokenValue=" + $("#tokenValue").val(),
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data.result.indexOf("/") != -1) {
                        showError("页面已过期，请刷新页面！");
                    } else {
                        $("#tokenValue").val(data.token);
                        if (data.result === "ok") {
                            const message = applyStatus === "0" ? "暂存申请信息成功！" : "提交申请信息成功！";
                            showSuccess(message, function() {
                                goBack();
                            });
                        } else {
                            showError(data.result);
                        }
                    }
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 返回上一页
        function goBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                history.back();
            }
        }

        // 刷新数据
        function refreshData() {
            const dslx = $("#add_dslx").val();
            if (dslx) {
                queryTeachers(dslx);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const actionsHeight = $('.form-actions').outerHeight();
            const containerHeight = windowHeight - navbarHeight - actionsHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击弹窗背景关闭弹窗
        $(document).on('click', '.intro-modal', function(e) {
            if (e.target === this) {
                closeIntroModal();
            }
        });
    </script>
</body>
</html>
