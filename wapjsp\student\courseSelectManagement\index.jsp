<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>选课管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 选课管理页面样式 */
        .course-select-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .course-select-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            text-align: center;
            font-size: var(--font-size-h4);
            font-weight: 500;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            padding: var(--padding-md);
        }
        
        .function-card {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
        }
        
        .function-card:hover {
            background: var(--bg-secondary);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .function-card:active {
            transform: translateY(0);
        }
        
        .function-icon {
            font-size: 32px;
            margin-bottom: var(--margin-sm);
            color: var(--primary-color);
        }
        
        .function-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .function-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .status-banner {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            text-align: center;
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .status-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-actions-title i {
            color: var(--primary-color);
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }
        
        .btn-action {
            flex: 1;
            min-width: 120px;
            padding: var(--padding-sm);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-primary-action {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-secondary-action {
            background: var(--secondary-color);
            color: white;
        }
        
        .btn-info-action {
            background: var(--info-color);
            color: white;
        }
        
        .btn-warning-action {
            background: var(--warning-color);
            color: white;
        }
        
        .notice-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .notice-title i {
            color: var(--warning-color);
        }
        
        .notice-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--warning-color);
        }
        
        .notice-item:last-child {
            margin-bottom: 0;
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .notice-time {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        .stats-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--success-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        @media (max-width: 480px) {
            .function-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn-action {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">选课管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 选课状态横幅 -->
        <div class="status-banner">
            <div class="status-title">当前选课阶段</div>
            <div class="status-desc" id="currentPhase">正在获取选课状态...</div>
        </div>
        
        <!-- 选课统计 -->
        <div class="stats-section">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                选课统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="selectedCourses">0</div>
                    <div class="stat-label">已选课程</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalCredits">0</div>
                    <div class="stat-label">总学分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="conflictCourses">0</div>
                    <div class="stat-label">冲突课程</div>
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="quick-actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                快速操作
            </div>
            <div class="action-buttons">
                <button class="btn-action btn-primary-action" onclick="goToSelectCourse();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>选课</span>
                </button>
                <button class="btn-action btn-secondary-action" onclick="goToDropCourse();">
                    <i class="ace-icon fa fa-minus"></i>
                    <span>退课</span>
                </button>
                <button class="btn-action btn-info-action" onclick="goToViewSchedule();">
                    <i class="ace-icon fa fa-calendar"></i>
                    <span>课表</span>
                </button>
                <button class="btn-action btn-warning-action" onclick="goToViewResults();">
                    <i class="ace-icon fa fa-list"></i>
                    <span>选课结果</span>
                </button>
            </div>
        </div>
        
        <!-- 功能模块 -->
        <div class="course-select-container">
            <div class="course-select-header">选课功能</div>
            <div class="function-grid">
                <div class="function-card" onclick="goToFunction('selectKc');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-plus-circle"></i>
                    </div>
                    <div class="function-title">课程选择</div>
                    <div class="function-desc">选择本学期开设的课程</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('currentCourseListInfo');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-list-alt"></i>
                    </div>
                    <div class="function-title">补修选课</div>
                    <div class="function-desc">选择需要补修的课程</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('bxkc');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-repeat"></i>
                    </div>
                    <div class="function-title">重修选课</div>
                    <div class="function-desc">选择需要重修的课程</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('courseSelectPriority');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-sort-numeric-asc"></i>
                    </div>
                    <div class="function-title">选课优先级</div>
                    <div class="function-desc">设置课程选择优先级</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('waitfor');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-clock-o"></i>
                    </div>
                    <div class="function-title">候补选课</div>
                    <div class="function-desc">加入课程候补队列</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('draw');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-random"></i>
                    </div>
                    <div class="function-title">抽签选课</div>
                    <div class="function-desc">参与课程抽签选择</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('teachingBooks');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-book"></i>
                    </div>
                    <div class="function-title">教材管理</div>
                    <div class="function-desc">查看和订购教材</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('selectCourseNoticeDetail');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-bell"></i>
                    </div>
                    <div class="function-title">选课通知</div>
                    <div class="function-desc">查看选课相关通知</div>
                </div>
            </div>
        </div>
        
        <!-- 选课通知 -->
        <div class="notice-section" id="noticeSection" style="display: none;">
            <div class="notice-title">
                <i class="ace-icon fa fa-bullhorn"></i>
                选课通知
            </div>
            <div id="noticeList">
                <!-- 动态加载通知内容 -->
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-graduation-cap"></i>
            <div>暂无选课信息</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let courseSelectData = {};
        let currentSemester = '';
        let selectPhase = '';

        $(function() {
            initPage();
            loadCourseSelectInfo();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载选课信息
        function loadCourseSelectInfo() {
            showLoading(true);

            // 获取选课状态和统计信息
            $.ajax({
                url: "/student/courseSelectManagement/getSelectInfo",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        updateSelectStatus(data.data);
                        updateStatistics(data.data);
                        loadNotices();
                    } else {
                        showError(data.message || '获取选课信息失败');
                    }
                },
                error: function() {
                    showError('网络请求失败');
                    // 设置默认状态
                    updateSelectStatus({
                        phase: '选课未开始',
                        description: '请关注选课通知'
                    });
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新选课状态
        function updateSelectStatus(data) {
            if (data.phase) {
                selectPhase = data.phase;
                $('#currentPhase').text(data.phase + (data.description ? ' - ' + data.description : ''));
            } else {
                $('#currentPhase').text('选课系统正常运行');
            }
        }

        // 更新统计信息
        function updateStatistics(data) {
            $('#selectedCourses').text(data.selectedCourses || 0);
            $('#totalCredits').text(data.totalCredits || 0);
            $('#conflictCourses').text(data.conflictCourses || 0);
        }

        // 加载选课通知
        function loadNotices() {
            $.ajax({
                url: "/student/courseSelectManagement/getNotices",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success && data.data && data.data.length > 0) {
                        renderNotices(data.data);
                        $('#noticeSection').show();
                    }
                },
                error: function() {
                    console.log('获取通知失败');
                }
            });
        }

        // 渲染通知列表
        function renderNotices(notices) {
            const container = $('#noticeList');
            container.empty();

            notices.forEach(function(notice) {
                const noticeHtml = `
                    <div class="notice-item" onclick="viewNoticeDetail('${notice.id}');">
                        <div class="notice-content">${notice.title}</div>
                        <div class="notice-time">${notice.publishTime}</div>
                    </div>
                `;
                container.append(noticeHtml);
            });
        }

        // 查看通知详情
        function viewNoticeDetail(noticeId) {
            if (parent && parent.addTab) {
                parent.addTab('选课通知详情', '/student/courseSelectManagement/selectCourseNoticeDetail?id=' + noticeId);
            } else {
                window.location.href = '/student/courseSelectManagement/selectCourseNoticeDetail?id=' + noticeId;
            }
        }

        // 跳转到功能页面
        function goToFunction(functionName) {
            let url = '';
            let title = '';

            switch(functionName) {
                case 'selectKc':
                    url = '/student/courseSelectManagement/selectKc';
                    title = '课程选择';
                    break;
                case 'currentCourseListInfo':
                    url = '/student/courseSelectManagement/currentCourseListInfo';
                    title = '补修选课';
                    break;
                case 'bxkc':
                    url = '/student/courseSelectManagement/bxkc';
                    title = '重修选课';
                    break;
                case 'courseSelectPriority':
                    url = '/student/courseSelectManagement/courseSelectPriority';
                    title = '选课优先级';
                    break;
                case 'waitfor':
                    url = '/student/courseSelectManagement/waitfor';
                    title = '候补选课';
                    break;
                case 'draw':
                    url = '/student/courseSelectManagement/draw';
                    title = '抽签选课';
                    break;
                case 'teachingBooks':
                    url = '/student/courseSelectManagement/teachingBooks/index';
                    title = '教材管理';
                    break;
                case 'selectCourseNoticeDetail':
                    url = '/student/courseSelectManagement/selectCourseNoticeDetail';
                    title = '选课通知';
                    break;
                default:
                    showError('功能暂未开放');
                    return;
            }

            if (parent && parent.addTab) {
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 快速操作函数
        function goToSelectCourse() {
            goToFunction('selectKc');
        }

        function goToDropCourse() {
            goToFunction('deleteKcList');
        }

        function goToViewSchedule() {
            if (parent && parent.addTab) {
                parent.addTab('课程表', '/student/weekLySchedule/index');
            } else {
                window.location.href = '/student/weekLySchedule/index';
            }
        }

        function goToViewResults() {
            if (parent && parent.addTab) {
                parent.addTab('选课结果', '/student/courseTableOfThisSemester/courseSelectResult');
            } else {
                window.location.href = '/student/courseTableOfThisSemester/courseSelectResult';
            }
        }

        // 刷新数据
        function refreshData() {
            loadCourseSelectInfo();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
