<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>课程志愿</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 课程志愿页面样式 */
        .voluntary-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .voluntary-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .status-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .status-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-md);
        }
        
        .status-icon.open {
            background: var(--success-color);
        }
        
        .status-icon.closed {
            background: var(--error-color);
        }
        
        .status-content {
            flex: 1;
        }
        
        .status-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .status-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .voluntary-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-add {
            background: var(--success-color);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            border: none;
            cursor: pointer;
        }
        
        .voluntary-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .voluntary-item:last-child {
            border-bottom: none;
        }
        
        .voluntary-item.priority-1 {
            border-left: 4px solid var(--error-color);
        }
        
        .voluntary-item.priority-2 {
            border-left: 4px solid var(--warning-color);
        }
        
        .voluntary-item.priority-3 {
            border-left: 4px solid var(--success-color);
        }
        
        .voluntary-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .voluntary-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .voluntary-priority {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .priority-1 {
            background: var(--error-color);
            color: white;
        }
        
        .priority-2 {
            background: var(--warning-color);
            color: white;
        }
        
        .priority-3 {
            background: var(--success-color);
            color: white;
        }
        
        .voluntary-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .voluntary-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .voluntary-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
        }
        
        .btn-edit {
            background: var(--info-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-up {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-down {
            background: var(--text-disabled);
            color: white;
        }
        
        .voluntary-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .voluntary-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .course-search {
            position: relative;
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-top: none;
            border-radius: 0 0 6px 6px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 10;
            display: none;
        }
        
        .search-result-item {
            padding: var(--padding-sm);
            cursor: pointer;
            border-bottom: 1px solid var(--divider-color);
            transition: background-color var(--transition-base);
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .search-result-item:hover {
            background: var(--bg-color-active);
        }
        
        .result-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .result-info {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">课程志愿</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="voluntary-header">
            <div class="header-title">课程志愿</div>
            <div class="header-subtitle">填报课程选择志愿</div>
        </div>

        <!-- 志愿状态 -->
        <div class="voluntary-status">
            <div class="status-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>志愿填报状态</span>
            </div>

            <div class="status-info" id="statusInfo">
                <!-- 状态信息将动态填充 -->
            </div>
        </div>

        <!-- 志愿列表 -->
        <div class="voluntary-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span>我的志愿</span>
                </div>
                <div class="list-actions">
                    <button class="btn-add" onclick="showVoluntaryForm();" id="addBtn">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>添加</span>
                    </button>
                </div>
            </div>

            <div id="voluntaryItems">
                <!-- 志愿列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-list"></i>
            <div id="emptyMessage">暂无志愿记录</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 志愿表单 -->
    <div class="voluntary-form" id="voluntaryForm">
        <div class="form-header">
            <div class="form-back" onclick="closeVoluntaryForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">添加志愿</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-search"></i>
                    <span>选择课程</span>
                </div>

                <div class="form-group">
                    <div class="form-label">课程搜索</div>
                    <div class="course-search">
                        <input type="text" class="form-input" id="courseSearch" placeholder="输入课程名称或代码搜索">
                        <div class="search-results" id="searchResults">
                            <!-- 搜索结果将动态填充 -->
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">选中课程</div>
                    <input type="text" class="form-input" id="selectedCourse" placeholder="请先搜索并选择课程" readonly>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-cog"></i>
                    <span>志愿设置</span>
                </div>

                <div class="form-group">
                    <div class="form-label">志愿优先级</div>
                    <select class="form-input" id="priority">
                        <option value="">请选择优先级</option>
                        <option value="1">第一志愿</option>
                        <option value="2">第二志愿</option>
                        <option value="3">第三志愿</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">志愿原因</div>
                    <textarea class="form-input" id="reason" placeholder="请说明选择该课程的原因" style="min-height: 80px; resize: vertical;"></textarea>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeVoluntaryForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitVoluntary();">保存</button>
        </div>
    </div>

    <script>
        // 全局变量
        let voluntaryList = [];
        let availableCourses = [];
        let voluntaryStatus = {};
        let currentVoluntary = null;
        let selectedCourseData = null;

        $(function() {
            initPage();
            loadVoluntaryStatus();
            loadVoluntaryList();
            loadAvailableCourses();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 课程搜索
            $('#courseSearch').on('input', function() {
                const keyword = $(this).val().trim();
                if (keyword.length >= 2) {
                    searchCourses(keyword);
                } else {
                    hideSearchResults();
                }
            });

            // 点击其他地方隐藏搜索结果
            $(document).click(function(e) {
                if (!$(e.target).closest('.course-search').length) {
                    hideSearchResults();
                }
            });
        }

        // 加载志愿状态
        function loadVoluntaryStatus() {
            $.ajax({
                url: "/student/courseVoluntary/getVoluntaryStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    voluntaryStatus = data.status || {};
                    renderVoluntaryStatus();
                },
                error: function() {
                    console.log('加载志愿状态失败');
                }
            });
        }

        // 渲染志愿状态
        function renderVoluntaryStatus() {
            const isOpen = voluntaryStatus.isOpen || false;
            const iconClass = isOpen ? 'open' : 'closed';
            const statusText = isOpen ? '志愿填报开放中' : '志愿填报已关闭';
            const metaText = isOpen ?
                `截止时间：${formatDate(voluntaryStatus.deadline)}` :
                '当前不在志愿填报时间内';

            const statusHtml = `
                <div class="status-icon ${iconClass}">
                    <i class="ace-icon fa ${isOpen ? 'fa-check' : 'fa-times'}"></i>
                </div>
                <div class="status-content">
                    <div class="status-text">${statusText}</div>
                    <div class="status-meta">${metaText}</div>
                </div>
            `;

            $('#statusInfo').html(statusHtml);

            // 控制添加按钮状态
            if (isOpen) {
                $('#addBtn').show();
            } else {
                $('#addBtn').hide();
            }
        }

        // 加载志愿列表
        function loadVoluntaryList() {
            showLoading(true);

            $.ajax({
                url: "/student/courseVoluntary/getVoluntaryList",
                type: "post",
                dataType: "json",
                success: function(data) {
                    voluntaryList = data.voluntaries || [];
                    renderVoluntaryList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载志愿列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染志愿列表
        function renderVoluntaryList() {
            const container = $('#voluntaryItems');
            container.empty();

            if (voluntaryList.length === 0) {
                showEmptyState('暂无志愿记录，请添加志愿');
                return;
            } else {
                hideEmptyState();
            }

            // 按优先级排序
            voluntaryList.sort((a, b) => a.priority - b.priority);

            voluntaryList.forEach((voluntary, index) => {
                const voluntaryHtml = createVoluntaryItem(voluntary, index);
                container.append(voluntaryHtml);
            });
        }

        // 创建志愿项
        function createVoluntaryItem(voluntary, index) {
            const priorityText = getPriorityText(voluntary.priority);
            const priorityClass = `priority-${voluntary.priority}`;
            const canMoveUp = index > 0;
            const canMoveDown = index < voluntaryList.length - 1;
            const isOpen = voluntaryStatus.isOpen || false;

            return `
                <div class="voluntary-item ${priorityClass}">
                    <div class="voluntary-basic">
                        <div class="voluntary-course">${voluntary.courseName}</div>
                        <div class="voluntary-priority ${priorityClass}">${priorityText}</div>
                    </div>
                    <div class="voluntary-details">
                        <div class="voluntary-detail-item">
                            <span>课程代码:</span>
                            <span>${voluntary.courseCode}</span>
                        </div>
                        <div class="voluntary-detail-item">
                            <span>学分:</span>
                            <span>${voluntary.credits}</span>
                        </div>
                        <div class="voluntary-detail-item">
                            <span>教师:</span>
                            <span>${voluntary.teacher}</span>
                        </div>
                        <div class="voluntary-detail-item">
                            <span>填报时间:</span>
                            <span>${formatDate(voluntary.createTime)}</span>
                        </div>
                    </div>
                    ${isOpen ? `
                        <div class="voluntary-actions">
                            ${canMoveUp ? `
                                <button class="btn-mobile btn-up" onclick="moveVoluntary('${voluntary.id}', 'up');">
                                    <i class="ace-icon fa fa-arrow-up"></i>
                                </button>
                            ` : ''}
                            ${canMoveDown ? `
                                <button class="btn-mobile btn-down" onclick="moveVoluntary('${voluntary.id}', 'down');">
                                    <i class="ace-icon fa fa-arrow-down"></i>
                                </button>
                            ` : ''}
                            <button class="btn-mobile btn-edit" onclick="editVoluntary('${voluntary.id}');">
                                <i class="ace-icon fa fa-edit"></i>
                            </button>
                            <button class="btn-mobile btn-delete" onclick="deleteVoluntary('${voluntary.id}');">
                                <i class="ace-icon fa fa-trash"></i>
                            </button>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            switch(priority) {
                case 1: return '第一志愿';
                case 2: return '第二志愿';
                case 3: return '第三志愿';
                default: return '未知';
            }
        }

        // 加载可选课程
        function loadAvailableCourses() {
            $.ajax({
                url: "/student/courseVoluntary/getAvailableCourses",
                type: "post",
                dataType: "json",
                success: function(data) {
                    availableCourses = data.courses || [];
                },
                error: function() {
                    console.log('加载可选课程失败');
                }
            });
        }

        // 搜索课程
        function searchCourses(keyword) {
            const results = availableCourses.filter(course =>
                course.name.toLowerCase().includes(keyword.toLowerCase()) ||
                course.code.toLowerCase().includes(keyword.toLowerCase())
            ).slice(0, 10);

            renderSearchResults(results);
        }

        // 渲染搜索结果
        function renderSearchResults(results) {
            const container = $('#searchResults');
            container.empty();

            if (results.length === 0) {
                container.html('<div class="search-result-item">未找到相关课程</div>');
            } else {
                results.forEach(course => {
                    const resultHtml = `
                        <div class="search-result-item" onclick="selectCourse('${course.id}')">
                            <div class="result-name">${course.name}</div>
                            <div class="result-info">${course.code} | ${course.credits}学分 | ${course.teacher}</div>
                        </div>
                    `;
                    container.append(resultHtml);
                });
            }

            container.show();
        }

        // 选择课程
        function selectCourse(courseId) {
            const course = availableCourses.find(c => c.id === courseId);
            if (!course) return;

            selectedCourseData = course;
            $('#selectedCourse').val(`${course.name} (${course.code})`);
            $('#courseSearch').val(course.name);
            hideSearchResults();
        }

        // 隐藏搜索结果
        function hideSearchResults() {
            $('#searchResults').hide();
        }

        // 显示志愿表单
        function showVoluntaryForm() {
            if (!voluntaryStatus.isOpen) {
                showError('当前不在志愿填报时间内');
                return;
            }

            currentVoluntary = null;
            clearForm();
            $('#formTitle').text('添加志愿');
            $('#voluntaryForm').addClass('show');
        }

        // 编辑志愿
        function editVoluntary(voluntaryId) {
            const voluntary = voluntaryList.find(v => v.id === voluntaryId);
            if (!voluntary) return;

            currentVoluntary = voluntary;
            fillForm(voluntary);
            $('#formTitle').text('编辑志愿');
            $('#voluntaryForm').addClass('show');
        }

        // 填充表单
        function fillForm(voluntary) {
            selectedCourseData = {
                id: voluntary.courseId,
                name: voluntary.courseName,
                code: voluntary.courseCode,
                credits: voluntary.credits,
                teacher: voluntary.teacher
            };

            $('#courseSearch').val(voluntary.courseName);
            $('#selectedCourse').val(`${voluntary.courseName} (${voluntary.courseCode})`);
            $('#priority').val(voluntary.priority);
            $('#reason').val(voluntary.reason || '');
        }

        // 清空表单
        function clearForm() {
            selectedCourseData = null;
            $('#courseSearch').val('');
            $('#selectedCourse').val('');
            $('#priority').val('');
            $('#reason').val('');
            hideSearchResults();
        }

        // 关闭志愿表单
        function closeVoluntaryForm() {
            $('#voluntaryForm').removeClass('show');
            currentVoluntary = null;
        }

        // 提交志愿
        function submitVoluntary() {
            const formData = {
                courseId: selectedCourseData ? selectedCourseData.id : '',
                priority: $('#priority').val(),
                reason: $('#reason').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            if (currentVoluntary) {
                formData.id = currentVoluntary.id;
            }

            const action = currentVoluntary ? '修改' : '添加';
            const message = `确定要${action}该志愿吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitVoluntary(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitVoluntary(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!selectedCourseData) {
                showError('请选择课程');
                return false;
            }

            if (!formData.priority) {
                showError('请选择志愿优先级');
                return false;
            }

            // 检查优先级是否已存在
            const existingVoluntary = voluntaryList.find(v =>
                v.priority == formData.priority &&
                (!currentVoluntary || v.id !== currentVoluntary.id)
            );

            if (existingVoluntary) {
                showError(`${getPriorityText(formData.priority)}已存在，请选择其他优先级`);
                return false;
            }

            if (!formData.reason) {
                showError('请填写志愿原因');
                return false;
            }

            return true;
        }

        // 执行提交志愿
        function doSubmitVoluntary(formData) {
            const url = currentVoluntary ?
                "/student/courseVoluntary/updateVoluntary" :
                "/student/courseVoluntary/addVoluntary";

            $.ajax({
                url: url,
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        const action = currentVoluntary ? '修改' : '添加';
                        showSuccess(`${action}志愿成功`);
                        closeVoluntaryForm();
                        loadVoluntaryList(); // 重新加载列表
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 删除志愿
        function deleteVoluntary(voluntaryId) {
            const voluntary = voluntaryList.find(v => v.id === voluntaryId);
            if (!voluntary) return;

            const message = `确定要删除"${voluntary.courseName}"志愿吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doDeleteVoluntary(voluntaryId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doDeleteVoluntary(voluntaryId);
                }
            }
        }

        // 执行删除志愿
        function doDeleteVoluntary(voluntaryId) {
            $.ajax({
                url: "/student/courseVoluntary/deleteVoluntary",
                type: "post",
                data: { id: voluntaryId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('删除志愿成功');
                        loadVoluntaryList(); // 重新加载列表
                    } else {
                        showError(data.message || '删除失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 移动志愿
        function moveVoluntary(voluntaryId, direction) {
            $.ajax({
                url: "/student/courseVoluntary/moveVoluntary",
                type: "post",
                data: {
                    id: voluntaryId,
                    direction: direction
                },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        loadVoluntaryList(); // 重新加载列表
                    } else {
                        showError(data.message || '移动失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadVoluntaryStatus();
            loadVoluntaryList();
            loadAvailableCourses();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
