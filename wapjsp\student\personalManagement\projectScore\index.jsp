<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>毕设成绩查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 项目成绩页面样式 */
        .score-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .score-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .score-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--primary-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .scores-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .scores-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .scores-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .scores-title i {
            color: var(--success-color);
        }
        
        .score-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .score-item:last-child {
            border-bottom: none;
        }
        
        .score-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .score-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .score-content {
            flex: 1;
        }
        
        .score-title-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .score-teacher {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .score-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .total-score {
            background: var(--success-color);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .total-score:hover {
            background: var(--success-dark);
            transform: scale(1.05);
        }
        
        .total-score i {
            font-size: var(--font-size-small);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .search-actions {
                flex-direction: column;
            }
            
            .score-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">毕设成绩查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 成绩查询头部 -->
        <div class="score-header">
            <div class="score-title">毕设成绩查询</div>
            <div class="score-desc">查看毕业设计成绩信息</div>
        </div>
        
        <!-- 查询条件 -->
        <div class="search-section">
            <div class="search-title">
                <i class="ace-icon fa fa-search"></i>
                查询条件
            </div>
            
            <form id="bscjform" name="bscjform" class="search-form">
                <div class="form-group">
                    <label class="form-label">题目名称</label>
                    <input type="text" name="tmmc" class="form-input" placeholder="请输入题目名称">
                </div>
            </form>
            
            <div class="search-actions">
                <button class="btn-mobile btn-search" onclick="searchScores();">
                    <i class="ace-icon fa fa-search"></i>
                    <span>查询</span>
                </button>
            </div>
        </div>
        
        <!-- 成绩列表 -->
        <div class="scores-section">
            <div class="scores-header">
                <div class="scores-title">
                    <i class="ace-icon fa fa-list"></i>
                    毕业设计题目申请信息列表
                </div>
            </div>
            
            <div id="scoresList">
                <!-- 动态加载成绩列表 -->
            </div>
            
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreScores();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>加载更多</span>
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无成绩数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let scoreData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let searchParams = '';

        $(function() {
            initPage();
            loadScores(1, true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索成绩
        function searchScores() {
            loadScores(1, true);
        }

        // 加载更多成绩
        function loadMoreScores() {
            if (hasMore) {
                loadScores(currentPage + 1, false);
            }
        }

        // 加载成绩数据
        function loadScores(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
                searchParams = $('#bscjform').serialize();
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/projectScore/search",
                type: "post",
                data: searchParams + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.page;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            scoreData = data.records;
                        } else {
                            scoreData = scoreData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = scoreData.length < totalCount;

                        renderScoresList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            scoreData = [];
                            renderScoresList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染成绩列表
        function renderScoresList(reset = false) {
            const container = $('#scoresList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : scoreData.length - pageSize;
            const endIndex = scoreData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (scoreData[i]) {
                    const itemHtml = createScoreItem(scoreData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建成绩项目HTML
        function createScoreItem(item, index) {
            return `
                <div class="score-item">
                    <div class="score-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="score-index">${index + 1}</div>
                            <div class="score-content">
                                <div class="score-title-text">${item.TMMC || ''}</div>
                                <div class="score-teacher">指导教师：${item.JSM || ''}</div>
                            </div>
                        </div>
                        <div>
                            <div class="total-score" onclick="viewDetailScore('${item.ZXJXJHH}', '${item.TMBH}');" title="点击查看分项成绩">
                                <span>${item.ZCJ || ''}</span>
                                <i class="ace-icon fa fa-eye"></i>
                            </div>
                        </div>
                    </div>

                    <div class="score-details">
                        <div class="detail-item">
                            <span class="detail-label">题目编号</span>
                            <span>${item.TMBH || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">题目来源</span>
                            <span>${item.TMLYSM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">题目类型</span>
                            <span>${item.KTLBSM || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">总成绩</span>
                            <span style="color: var(--success-color); font-weight: 600;">${item.ZCJ || ''}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 查看分项成绩
        function viewDetailScore(zxjxjhh, tmbh) {
            if (parent && parent.addTab) {
                parent.addTab('分项成绩', '/student/personalManagement/projectScore/fxcjIndex?zxjxjhh=' + zxjxjhh + '&tmbh=' + tmbh);
            } else {
                location.href = "/student/personalManagement/projectScore/fxcjIndex?zxjxjhh=" + zxjxjhh + "&tmbh=" + tmbh;
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && scoreData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (scoreData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.scores-section').hide();
            } else {
                $('#emptyState').hide();
                $('.scores-section').show();
            }
        }

        // 刷新数据
        function refreshData() {
            loadScores(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
