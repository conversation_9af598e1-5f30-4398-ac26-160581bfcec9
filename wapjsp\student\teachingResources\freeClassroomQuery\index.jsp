<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>空闲教室查询</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 空闲教室查询页面样式 */
        .classroom-header {
            background: linear-gradient(135deg, var(--warning-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .classroom-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .classroom-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .campus-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            background: var(--bg-tertiary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .selector-header:hover {
            border-color: var(--warning-color);
            background: var(--warning-light);
        }
        
        .selector-title {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .selector-title i {
            color: var(--warning-color);
        }
        
        .current-campus {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .current-badge {
            background: var(--success-color);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            margin-left: 4px;
        }
        
        .selector-arrow {
            color: var(--text-secondary);
            transition: transform var(--transition-base);
        }
        
        .selector-arrow.expanded {
            transform: rotate(180deg);
        }
        
        .campus-options {
            margin-top: var(--margin-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            background: var(--bg-primary);
            overflow: hidden;
            display: none;
        }
        
        .campus-option {
            padding: var(--padding-sm) var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .campus-option:last-child {
            border-bottom: none;
        }
        
        .campus-option:hover {
            background: var(--bg-tertiary);
        }
        
        .campus-option.active {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .campus-option i {
            color: var(--success-color);
        }
        
        .campus-option.active i {
            color: var(--warning-color);
        }
        
        .buildings-grid {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .grid-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .grid-title i {
            color: var(--warning-color);
        }
        
        .buildings-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
        }
        
        .building-card {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid var(--border-primary);
        }
        
        .building-card:hover {
            background: var(--warning-light);
            border-color: var(--warning-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }
        
        .building-card:active {
            transform: translateY(0);
        }
        
        .building-icon {
            font-size: 32px;
            color: var(--warning-color);
            margin-bottom: var(--margin-sm);
        }
        
        .building-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            line-height: 1.4;
        }
        
        .all-buildings-card {
            background: var(--warning-color);
            color: white;
            border-color: var(--warning-color);
        }
        
        .all-buildings-card:hover {
            background: var(--warning-dark);
            border-color: var(--warning-dark);
        }
        
        .all-buildings-card .building-icon {
            color: white;
        }
        
        .all-buildings-card .building-name {
            color: white;
        }
        
        .notice-card {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
            font-size: var(--font-size-small);
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .notice-card i {
            color: var(--info-color);
            margin-top: 2px;
        }
        
        .notice-content {
            flex: 1;
            line-height: 1.4;
        }
        
        .notice-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        @media (max-width: 480px) {
            .buildings-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="xqh" value="${xqh}">
    <input type="hidden" id="xqList" value="${xqList}">
    <input type="hidden" id="buildings" value="${tbuildings}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">空闲教室查询</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 空闲教室查询头部 -->
        <div class="classroom-header">
            <div class="classroom-title">空闲教室查询</div>
            <div class="classroom-desc">查询各校区教学楼空闲教室</div>
        </div>
        
        <!-- 查询范围提示 -->
        <c:if test="${info == true}">
            <div class="notice-card">
                <i class="ace-icon fa fa-info-circle"></i>
                <div class="notice-content">
                    <div class="notice-title">查询范围说明</div>
                    <div>仅用于查询东区主楼、一教、二教、三教和西区新教、旧教、农学楼的本科生空闲教室。</div>
                </div>
            </div>
        </c:if>
        
        <!-- 校区选择器 -->
        <div class="campus-selector">
            <div class="selector-header" onclick="toggleCampusOptions();">
                <div class="selector-title">
                    <i class="ace-icon fa fa-map-marker"></i>
                    <span id="currentCampusName">请选择校区</span>
                    <span class="current-badge" id="currentBadge" style="display: none;">当前校区</span>
                </div>
                <i class="ace-icon fa fa-chevron-down selector-arrow" id="selectorArrow"></i>
            </div>
            
            <div class="campus-options" id="campusOptions">
                <!-- 动态加载校区选项 -->
            </div>
        </div>
        
        <!-- 教学楼网格 -->
        <div class="buildings-grid">
            <div class="grid-title">
                <i class="ace-icon fa fa-building"></i>
                选择教学楼
            </div>
            
            <div class="buildings-container" id="buildingsContainer">
                <!-- 动态加载教学楼 -->
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentCampus = '';
        let campusData = [];
        let buildingsData = {};
        let isOptionsExpanded = false;

        $(function() {
            initPage();
            loadData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载数据
        function loadData() {
            const xqh = $('#xqh').val();
            const xqList = $('#xqList').val();
            const buildings = $('#buildings').val();
            
            try {
                campusData = JSON.parse(xqList);
                buildingsData = JSON.parse(buildings)[0];
                
                currentCampus = xqh;
                renderCampusOptions();
                renderBuildings();
            } catch (e) {
                showError('数据加载失败');
            }
        }

        // 渲染校区选项
        function renderCampusOptions() {
            const container = $('#campusOptions');
            const currentName = $('#currentCampusName');
            const currentBadge = $('#currentBadge');
            
            let optionsHtml = '';
            let selectedCampusName = '请选择校区';
            
            campusData.forEach(campus => {
                const isActive = campus.campusNumber === currentCampus;
                const badgeHtml = isActive ? '<span class="current-badge">当前校区</span>' : '';
                
                if (isActive) {
                    selectedCampusName = campus.campusName;
                    currentBadge.show();
                }
                
                optionsHtml += `
                    <div class="campus-option ${isActive ? 'active' : ''}" onclick="selectCampus('${campus.campusNumber}', '${campus.campusName}');">
                        <i class="ace-icon fa fa-check-circle"></i>
                        <span>${campus.campusName}${badgeHtml}</span>
                    </div>
                `;
            });
            
            container.html(optionsHtml);
            currentName.text(selectedCampusName);
        }

        // 切换校区选项显示
        function toggleCampusOptions() {
            const options = $('#campusOptions');
            const arrow = $('#selectorArrow');
            
            if (isOptionsExpanded) {
                options.slideUp(200);
                arrow.removeClass('expanded');
            } else {
                options.slideDown(200);
                arrow.addClass('expanded');
            }
            
            isOptionsExpanded = !isOptionsExpanded;
        }

        // 选择校区
        function selectCampus(campusNumber, campusName) {
            currentCampus = campusNumber;
            
            // 更新显示
            $('#currentCampusName').text(campusName);
            
            // 更新选项状态
            $('.campus-option').removeClass('active');
            $(`.campus-option[onclick*="${campusNumber}"]`).addClass('active');
            
            // 隐藏选项
            toggleCampusOptions();
            
            // 重新渲染教学楼
            renderBuildings();
        }

        // 渲染教学楼
        function renderBuildings() {
            const container = $('#buildingsContainer');
            
            if (!currentCampus || !buildingsData[currentCampus]) {
                container.html(`
                    <div class="empty-state">
                        <i class="ace-icon fa fa-building-o"></i>
                        <div class="empty-state-text">暂无教学楼信息</div>
                        <div class="empty-state-desc">请选择其他校区</div>
                    </div>
                `);
                return;
            }
            
            const buildings = buildingsData[currentCampus];
            let buildingsHtml = '';
            
            // 添加全部教学楼选项
            const campusName = campusData.find(c => c.campusNumber === currentCampus)?.campusName || '';
            buildingsHtml += `
                <div class="building-card all-buildings-card" onclick="queryAllBuildings('${currentCampus}', '${campusName}');">
                    <div class="building-icon">
                        <i class="ace-icon fa fa-th-large"></i>
                    </div>
                    <div class="building-name">全部教学楼</div>
                </div>
            `;
            
            // 添加具体教学楼
            buildings.forEach(building => {
                buildingsHtml += `
                    <div class="building-card" onclick="queryBuilding('${building.id.campusNumber}', '${building.id.teachingBuildingNumber}', '${building.teachingBuildingName}');">
                        <div class="building-icon">
                            <i class="ace-icon fa fa-building"></i>
                        </div>
                        <div class="building-name">${building.teachingBuildingName}</div>
                    </div>
                `;
            });
            
            container.html(buildingsHtml);
        }

        // 查询全部教学楼
        function queryAllBuildings(campusNumber, campusName) {
            const position = campusNumber + '_n';
            goToQuery(position, campusName);
        }

        // 查询具体教学楼
        function queryBuilding(campusNumber, buildingNumber, buildingName) {
            const position = campusNumber + '_' + buildingNumber;
            const campusName = campusData.find(c => c.campusNumber === campusNumber)?.campusName || '';
            goToQuery(position, campusName);
        }

        // 跳转到查询页面
        function goToQuery(position, campusName) {
            showLoading(true);
            
            // 创建表单提交
            const form = $('<form>', {
                action: '/student/teachingResources/freeClassroom/today',
                method: 'post'
            });
            
            form.append($('<input>', {
                type: 'hidden',
                name: 'position',
                value: position
            }));
            
            form.append($('<input>', {
                type: 'hidden',
                name: 'xqm',
                value: campusName
            }));
            
            $('body').append(form);
            form.submit();
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
