<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实验安排</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实验安排页面样式 */
        .experiment-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }

        .summary-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-md);
            text-align: center;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }

        .stat-item {
            padding: var(--padding-sm);
        }

        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }

        .experiment-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }

        .experiment-upcoming {
            border-left-color: var(--warning-color);
        }

        .experiment-completed {
            border-left-color: var(--success-color);
            opacity: 0.8;
        }

        .experiment-overdue {
            border-left-color: var(--error-color);
        }

        .experiment-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }

        .experiment-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }

        .experiment-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }

        .status-pending {
            background: var(--info-color);
            color: white;
        }

        .status-upcoming {
            background: var(--warning-color);
            color: white;
        }

        .status-completed {
            background: var(--success-color);
            color: white;
        }

        .status-overdue {
            background: var(--error-color);
            color: white;
        }

        .experiment-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }

        .experiment-detail-item {
            display: flex;
            justify-content: space-between;
        }

        .experiment-time {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
        }

        .time-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .time-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }

        .experiment-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--margin-sm);
        }

        .experiment-location {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }

        .experiment-location i {
            margin-right: 4px;
        }

        .btn-experiment {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .btn-detail {
            background: var(--primary-color);
            color: white;
        }

        .btn-report {
            background: var(--success-color);
            color: white;
        }

        .btn-checkin {
            background: var(--warning-color);
            color: white;
        }

        .btn-experiment:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }

        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }

        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }

        .countdown-timer {
            position: absolute;
            top: var(--padding-sm);
            right: var(--padding-sm);
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            color: var(--warning-color);
            font-weight: 500;
        }

        .experiment-requirements {
            background: rgba(24, 144, 255, 0.1);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--primary-color);
        }

        .requirements-title {
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }

        .requirements-list {
            font-size: var(--font-size-mini);
            line-height: var(--line-height-base);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实验安排</div>
            <div class="navbar-action" onclick="refreshExperiments();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 实验统计 -->
        <div class="experiment-summary">
            <div class="summary-title">实验统计</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalExperiments">0</div>
                    <div class="stat-label">总实验</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="completedExperiments">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="upcomingExperiments">0</div>
                    <div class="stat-label">待进行</div>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterExperiments('all')">全部</button>
                <button class="filter-chip" onclick="filterExperiments('upcoming')">即将开始</button>
                <button class="filter-chip" onclick="filterExperiments('pending')">待完成</button>
                <button class="filter-chip" onclick="filterExperiments('completed')">已完成</button>
            </div>
        </div>

        <!-- 实验列表 -->
        <div class="container-mobile">
            <div id="experimentList">
                <!-- 实验项将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-flask"></i>
                <div>暂无实验安排</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allExperiments = [];
        let filteredExperiments = [];
        let currentFilter = 'all';

        $(function() {
            initPage();
            loadExperiments();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载实验安排
        function loadExperiments() {
            showLoading(true);

            $.ajax({
                url: "/student/experiment/schedule/getExperiments",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allExperiments = data.experiments || [];
                    updateStatistics(data.statistics);
                    applyFilter();
                    showLoading(false);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                    showLoading(false);
                }
            });
        }

        // 渲染实验列表
        function renderExperiments() {
            const container = $('#experimentList');
            container.empty();

            if (filteredExperiments.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            filteredExperiments.forEach(function(experiment, index) {
                const experimentHtml = createExperimentItem(experiment, index);
                container.append(experimentHtml);
            });
        }

        // 创建实验项HTML
        function createExperimentItem(experiment, index) {
            const status = getExperimentStatus(experiment);
            const statusClass = getStatusClass(status);
            const itemClass = getItemClass(status);

            let actionButtons = '';
            if (status === 'upcoming') {
                actionButtons = `
                    <button class="btn-experiment btn-detail" onclick="showExperimentDetail('${experiment.id}')">查看详情</button>
                    <button class="btn-experiment btn-checkin" onclick="checkIn('${experiment.id}')">签到</button>
                `;
            } else if (status === 'pending') {
                actionButtons = `
                    <button class="btn-experiment btn-detail" onclick="showExperimentDetail('${experiment.id}')">查看详情</button>
                    <button class="btn-experiment btn-report" onclick="submitReport('${experiment.id}')">提交报告</button>
                `;
            } else if (status === 'completed') {
                actionButtons = `
                    <button class="btn-experiment btn-detail" onclick="showExperimentDetail('${experiment.id}')">查看详情</button>
                `;
            } else {
                actionButtons = `
                    <button class="btn-experiment btn-detail" onclick="showExperimentDetail('${experiment.id}')">查看详情</button>
                `;
            }

            let requirementsHtml = '';
            if (experiment.requirements) {
                requirementsHtml = `
                    <div class="experiment-requirements">
                        <div class="requirements-title">实验要求</div>
                        <div class="requirements-list">${experiment.requirements}</div>
                    </div>
                `;
            }

            return `
                <div class="experiment-item ${itemClass}" style="position: relative;">
                    ${status === 'upcoming' ? `<div class="countdown-timer" id="countdown-${experiment.id}"></div>` : ''}
                    <div class="experiment-header">
                        <div class="experiment-name">${experiment.name}</div>
                        <div class="experiment-status ${statusClass}">${getStatusText(status)}</div>
                    </div>
                    <div class="experiment-time">
                        <div class="time-title">实验时间</div>
                        <div class="time-value">${experiment.startTime} - ${experiment.endTime}</div>
                    </div>
                    <div class="experiment-details">
                        <div class="experiment-detail-item">
                            <span>课程名称:</span>
                            <span>${experiment.courseName}</span>
                        </div>
                        <div class="experiment-detail-item">
                            <span>指导教师:</span>
                            <span>${experiment.teacher}</span>
                        </div>
                        <div class="experiment-detail-item">
                            <span>实验类型:</span>
                            <span>${experiment.type}</span>
                        </div>
                        <div class="experiment-detail-item">
                            <span>学时:</span>
                            <span>${experiment.hours}学时</span>
                        </div>
                    </div>
                    ${requirementsHtml}
                    <div class="experiment-actions">
                        <div class="experiment-location">
                            <i class="ace-icon fa fa-map-marker"></i>
                            <span>${experiment.location}</span>
                        </div>
                        <div>
                            ${actionButtons}
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取实验状态
        function getExperimentStatus(experiment) {
            const now = new Date();
            const startTime = new Date(experiment.startTime);
            const endTime = new Date(experiment.endTime);

            if (experiment.isCompleted) {
                return 'completed';
            } else if (now < startTime) {
                return 'upcoming';
            } else if (now >= startTime && now <= endTime) {
                return 'pending';
            } else {
                return 'overdue';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'upcoming': return 'status-upcoming';
                case 'pending': return 'status-pending';
                case 'completed': return 'status-completed';
                case 'overdue': return 'status-overdue';
                default: return 'status-pending';
            }
        }

        // 获取项目样式类
        function getItemClass(status) {
            switch(status) {
                case 'upcoming': return 'experiment-upcoming';
                case 'completed': return 'experiment-completed';
                case 'overdue': return 'experiment-overdue';
                default: return '';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'upcoming': return '即将开始';
                case 'pending': return '进行中';
                case 'completed': return '已完成';
                case 'overdue': return '已过期';
                default: return '未知';
            }
        }

        // 显示实验详情
        function showExperimentDetail(experimentId) {
            const experiment = allExperiments.find(e => e.id === experimentId);
            if (!experiment) return;

            let message = `实验名称：${experiment.name}\n`;
            message += `课程名称：${experiment.courseName}\n`;
            message += `指导教师：${experiment.teacher}\n`;
            message += `实验时间：${experiment.startTime} - ${experiment.endTime}\n`;
            message += `实验地点：${experiment.location}\n`;
            message += `实验类型：${experiment.type}\n`;
            message += `学时：${experiment.hours}学时\n`;

            if (experiment.requirements) {
                message += `\n实验要求：\n${experiment.requirements}`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 签到
        function checkIn(experimentId) {
            const experiment = allExperiments.find(e => e.id === experimentId);
            if (!experiment) return;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要为《${experiment.name}》签到吗？`, function(confirmed) {
                    if (confirmed) {
                        doCheckIn(experimentId);
                    }
                });
            } else {
                if (confirm(`确定要为《${experiment.name}》签到吗？`)) {
                    doCheckIn(experimentId);
                }
            }
        }

        // 执行签到
        function doCheckIn(experimentId) {
            $.ajax({
                url: "/student/experiment/schedule/checkIn",
                type: "post",
                data: { experimentId: experimentId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('签到成功');
                        loadExperiments(); // 重新加载数据
                    } else {
                        showError(data.message || '签到失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 提交报告
        function submitReport(experimentId) {
            const experiment = allExperiments.find(e => e.id === experimentId);
            if (!experiment) return;

            // 跳转到实验报告页面
            if (parent && parent.addTab) {
                parent.addTab('实验报告', `/student/experiment/report/index?experimentId=${experimentId}`);
            } else {
                window.location.href = `/student/experiment/report/index?experimentId=${experimentId}`;
            }
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'upcoming':
                    filteredExperiments = allExperiments.filter(exp => getExperimentStatus(exp) === 'upcoming');
                    break;
                case 'pending':
                    filteredExperiments = allExperiments.filter(exp => getExperimentStatus(exp) === 'pending');
                    break;
                case 'completed':
                    filteredExperiments = allExperiments.filter(exp => getExperimentStatus(exp) === 'completed');
                    break;
                default:
                    filteredExperiments = allExperiments;
            }

            renderExperiments();
        }

        // 筛选实验
        function filterExperiments(filter) {
            currentFilter = filter;

            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');

            applyFilter();
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;

            $('#totalExperiments').text(statistics.totalExperiments || 0);
            $('#completedExperiments').text(statistics.completedExperiments || 0);
            $('#upcomingExperiments').text(statistics.upcomingExperiments || 0);
        }

        // 刷新实验安排
        function refreshExperiments() {
            loadExperiments();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#experimentList, .experiment-summary, .filter-section').hide();
            } else {
                $('#loadingState').hide();
                $('#experimentList, .experiment-summary, .filter-section').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>