<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学分检查</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学分检查页面样式 */
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-row {
            display: flex;
            align-items: center;
            margin-bottom: var(--margin-md);
        }
        
        .filter-row:last-child {
            margin-bottom: 0;
        }
        
        .filter-label {
            flex: 0 0 80px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .filter-control {
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .filter-control select {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .btn-search {
            flex: 0 0 60px;
            min-height: 40px;
            padding: 8px 12px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
        }
        
        .credit-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .credit-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .credit-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .credit-semester {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .credit-checkbox {
            position: absolute;
            top: var(--padding-md);
            right: var(--padding-md);
        }
        
        .credit-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
        }
        
        .credit-summary {
            background: var(--bg-tertiary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .summary-row:last-child {
            margin-bottom: 0;
            font-weight: 500;
            color: var(--primary-color);
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .summary-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .action-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
            z-index: 100;
        }
        
        .btn-confirm {
            background: var(--success-color);
            color: white;
        }
        
        .btn-print {
            background: var(--info-color);
            color: white;
        }
        
        .status-confirmed {
            background: var(--success-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .status-unconfirmed {
            background: var(--warning-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .warning-notice {
            background: var(--info-color);
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .page-content {
            padding-bottom: 80px; /* 为底部按钮留出空间 */
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: var(--padding-xxl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学分检查</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <div class="page-content">
            <!-- 警告提示 -->
            <c:if test="${sfsnj == 1}">
                <div class="warning-notice">
                    <i class="ace-icon fa fa-info-circle"></i>
                    本页面仅作为计算预收学分学费结果，未考虑学分替代等情况！
                </div>
            </c:if>
            
            <!-- 筛选条件 -->
            <div class="filter-section">
                <form name="queryInfo" method="post">
                    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
                    
                    <div class="filter-row">
                        <div class="filter-label">学年学期</div>
                        <div class="filter-control">
                            <select name="zxjxjhh" id="semesterSelect">
                                <option value="">全部</option>
                                <cache:query var="view" region="jh_zxjxjhb_view" fields="zxjxjhh,zxjxjhm" orderby="zxjxjhh desc"/>
                                <c:forEach items="${view}" var="view">
                                    <option value="${view.zxjxjhh}">${view.zxjxjhm}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    
                    <div class="filter-row">
                        <div class="filter-label">确认状态</div>
                        <div class="filter-control">
                            <select name="qrzt" id="statusSelect">
                                <option value="">全部</option>
                                <option value="1">确认</option>
                                <option value="0" selected>未确认</option>
                            </select>
                        </div>
                        <button type="button" class="btn-search" onclick="searchData();">
                            <i class="ace-icon fa fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- 学分列表 -->
            <div id="creditList">
                <!-- 动态加载内容 -->
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-file-text-o"></i>
                <div>当前查询学年暂无学分明细记录</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
        
        <!-- 底部操作按钮 -->
        <div class="action-buttons" id="actionButtons" style="display: none;">
            <button class="btn-mobile btn-confirm flex-1" onclick="confirmSelected();">
                <i class="ace-icon fa fa-check"></i>
                <span>确认</span>
            </button>
            <button class="btn-mobile btn-print flex-1" onclick="printData();">
                <i class="ace-icon fa fa-print"></i>
                <span>打印</span>
            </button>
            <c:if test="${xxbm == '100027'}">
                <button class="btn-mobile btn-secondary flex-1" onclick="searchYjXf();">
                    <i class="ace-icon fa fa-search"></i>
                    <span>预交学费</span>
                </button>
            </c:if>
        </div>
    </div>

    <script>
        // 全局变量
        let creditData = [];
        let selectedItems = new Set();
        let currentPage = 1;
        let hasMore = true;

        $(function() {
            initPage();
            loadData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载数据
        function loadData(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
                selectedItems.clear();
            }

            showLoading(true);

            const params = $(document.queryInfo).serialize();

            $.ajax({
                url: "/student/courseSelect/creditCheck/queryAll",
                type: "post",
                data: params + "&pageNum=" + page + "&pageSize=20",
                dataType: "json",
                success: function(data) {
                    if (data.records && data.records.length > 0) {
                        if (reset) {
                            creditData = data.records;
                        } else {
                            creditData = creditData.concat(data.records);
                        }

                        hasMore = creditData.length < data.pageContext.totalCount;
                        renderCreditList();
                        showActionButtons(true);
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            creditData = [];
                            renderCreditList();
                        }
                        showActionButtons(false);
                        showEmptyState(true);
                    }
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染学分列表
        function renderCreditList() {
            const container = $('#creditList');
            container.empty();

            let currentSemester = '';
            let semesterTotal = 0;
            let semesterFeeTotal = 0;
            let semesterItems = [];

            creditData.forEach(function(item, index) {
                // 检查是否需要显示学期汇总
                if (currentSemester && currentSemester !== item.ZXJXJHM) {
                    appendSemesterSummary(container, currentSemester, semesterTotal, semesterFeeTotal);
                    semesterTotal = 0;
                    semesterFeeTotal = 0;
                }

                currentSemester = item.ZXJXJHM;
                semesterTotal += parseFloat(item.SJXF || 0);
                semesterFeeTotal += parseFloat(item.XFJE || 0);

                const itemHtml = createCreditItem(item, index);
                container.append(itemHtml);
            });

            // 添加最后一个学期的汇总
            if (currentSemester && creditData.length > 0) {
                appendSemesterSummary(container, currentSemester, semesterTotal, semesterFeeTotal);
            }

            // 绑定事件
            bindItemEvents();
        }

        // 创建学分项目HTML
        function createCreditItem(item, index) {
            const isConfirmed = item.QRZT === '1';
            const itemKey = `${item.ZXJXJHH},${item.KCH},${item.KXH},${item.XH},${item.TJLX}`;

            return `
                <div class="credit-item" data-key="${itemKey}">
                    ${!isConfirmed ? `
                        <div class="credit-checkbox">
                            <input type="checkbox" class="item-checkbox" value="${itemKey}"
                                   ${selectedItems.has(itemKey) ? 'checked' : ''}>
                        </div>
                    ` : ''}

                    <div class="credit-header">
                        <div class="credit-title">${item.KCM || '-'}</div>
                        <div class="credit-semester">${item.ZXJXJHM || '-'}</div>
                    </div>

                    <div class="credit-details">
                        <div class="detail-item">
                            <span class="detail-label">课程号</span>
                            <span class="detail-value">${item.KCH || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">课序号</span>
                            <span class="detail-value">${item.KXH || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">修读方式</span>
                            <span class="detail-value">${item.XDFSMC || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">开课院系</span>
                            <span class="detail-value">${item.XSM || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">课程学分</span>
                            <span class="detail-value">${item.XF || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">计费学分</span>
                            <span class="detail-value">${item.SJXF || '-'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">学分学费</span>
                            <span class="detail-value">¥${item.XFJE || '0.00'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">状态</span>
                            <span class="detail-value">
                                <span class="${isConfirmed ? 'status-confirmed' : 'status-unconfirmed'}">
                                    ${isConfirmed ? '已确认' : '未确认'}
                                </span>
                            </span>
                        </div>
                    </div>

                    ${item.TKQJ ? `
                        <div class="credit-summary">
                            <div class="summary-row">
                                <span class="summary-label">退课区间</span>
                                <span class="summary-value">${item.TKQJ}</span>
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 添加学期汇总
        function appendSemesterSummary(container, semester, totalCredits, totalFee) {
            const summaryHtml = `
                <div class="credit-item">
                    <div class="credit-summary">
                        <div class="summary-row">
                            <span class="summary-label">${semester} 学期汇总</span>
                            <span class="summary-value"></span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">总学分</span>
                            <span class="summary-value">${totalCredits.toFixed(1)}</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">总费用</span>
                            <span class="summary-value">¥${totalFee.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            `;
            container.append(summaryHtml);
        }

        // 绑定项目事件
        function bindItemEvents() {
            $('.item-checkbox').off('change').on('change', function() {
                const key = $(this).val();
                if ($(this).is(':checked')) {
                    selectedItems.add(key);
                } else {
                    selectedItems.delete(key);
                }
            });
        }

        // 搜索数据
        function searchData() {
            loadData(1, true);
        }

        // 刷新数据
        function refreshData() {
            loadData(1, true);
        }

        // 确认选中项目
        function confirmSelected() {
            if (selectedItems.size === 0) {
                showError("请选择要确认的数据！");
                return;
            }

            const ids = Array.from(selectedItems).join(';');
            const tokenValue = $("#tokenValue").val();

            showLoading(true);

            $.ajax({
                url: "/student/courseSelect/creditCheck/doapply",
                type: "post",
                data: "ids=" + ids + "&tokenValue=" + tokenValue,
                dataType: "json",
                success: function(data) {
                    if (data.result === "ok") {
                        showSuccess("确认完成！");
                        $("#tokenValue").val(data.token);
                        selectedItems.clear();
                        loadData(1, true);
                    } else {
                        showError(data.result || "确认失败");
                    }
                },
                error: function() {
                    showError("操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 打印数据
        function printData() {
            if (creditData.length === 0) {
                showError("暂无数据可打印");
                return;
            }

            // 创建打印内容
            let printContent = `
                <table border="1" style="border-collapse: collapse; width: 100%; font-size: 12px;">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>学年学期</th>
                            <th>统计类型</th>
                            <th>课程号</th>
                            <th>课程名称</th>
                            <th>课序号</th>
                            <th>修读方式</th>
                            <th>开课院系</th>
                            <th>课程学分</th>
                            <th>计费学分</th>
                            <th>学分学费</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            creditData.forEach(function(item, index) {
                printContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.ZXJXJHM || '-'}</td>
                        <td>${item.TJLBMC || '-'}</td>
                        <td>${item.KCH || '-'}</td>
                        <td>${item.KCM || '-'}</td>
                        <td>${item.KXH || '-'}</td>
                        <td>${item.XDFSMC || '-'}</td>
                        <td>${item.XSM || '-'}</td>
                        <td>${item.XF || '-'}</td>
                        <td>${item.SJXF || '-'}</td>
                        <td>${item.XFJE || '-'}</td>
                    </tr>
                `;
            });

            printContent += `
                    </tbody>
                </table>
            `;

            // 打开新窗口进行打印
            const printWindow = window.open('', '', 'width=800,height=600');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>学分检查明细</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            table { border-collapse: collapse; width: 100%; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                            th { background-color: #f2f2f2; }
                        </style>
                    </head>
                    <body>
                        <h2>学分检查明细</h2>
                        ${printContent}
                        <script>
                            window.onload = function() {
                                window.print();
                                window.close();
                            }
                        </script>
                    </body>
                </html>
            `);
            printWindow.document.close();
        }

        // 毕业生预交学费查询
        function searchYjXf() {
            if (parent && parent.addTab) {
                parent.addTab('预交学费查询', '/student/courseSelect/creditCheck/yjxf');
            } else {
                window.location.href = '/student/courseSelect/creditCheck/yjxf';
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('#creditList').hide();
            } else {
                $('#emptyState').hide();
                $('#creditList').show();
            }
        }

        // 显示操作按钮
        function showActionButtons(show) {
            if (show) {
                $('#actionButtons').show();
            } else {
                $('#actionButtons').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 无限滚动加载
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    currentPage++;
                    loadData(currentPage, false);
                }
            }
        });
    </script>
</body>
</html>
