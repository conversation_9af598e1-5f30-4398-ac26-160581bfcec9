<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>助学金管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 助学金管理页面样式 */
        .financial-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .summary-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-btn {
            padding: var(--padding-md);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid var(--border-primary);
            background: var(--bg-primary);
        }
        
        .action-btn:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .action-icon {
            font-size: var(--font-size-h3);
            margin-bottom: var(--margin-sm);
        }
        
        .action-icon.apply {
            color: var(--success-color);
        }
        
        .action-icon.status {
            color: var(--info-color);
        }
        
        .action-icon.payment {
            color: var(--warning-color);
        }
        
        .action-icon.history {
            color: var(--primary-color);
        }
        
        .action-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .financial-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .financial-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .financial-approved {
            border-left-color: var(--success-color);
        }
        
        .financial-pending {
            border-left-color: var(--warning-color);
        }
        
        .financial-rejected {
            border-left-color: var(--error-color);
        }
        
        .financial-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .financial-type {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .financial-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .status-paid {
            background: var(--info-color);
            color: white;
        }
        
        .financial-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .financial-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .financial-amount {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            text-align: center;
        }
        
        .amount-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
        }
        
        .amount-value {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--success-color);
        }
        
        .payment-info {
            background: rgba(24, 144, 255, 0.1);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--primary-color);
        }
        
        .payment-title {
            font-weight: 500;
            margin-bottom: var(--margin-xs);
        }
        
        .payment-details {
            line-height: var(--line-height-base);
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .application-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .form-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-lg);
            text-align: center;
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            display: block;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-control {
            width: 100%;
            min-height: 44px;
            padding: 12px 16px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color var(--transition-base);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-help {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
            margin-top: var(--margin-xs);
            line-height: var(--line-height-base);
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-lg);
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-cancel {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-primary);
        }
        
        .notice-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--info-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            margin-right: var(--margin-xs);
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .notice-list {
            margin: 0;
            padding-left: var(--padding-md);
        }
        
        .notice-list li {
            margin-bottom: var(--margin-xs);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">助学金管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 助学金统计 -->
        <div class="financial-summary">
            <div class="summary-title">助学金统计</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalApplications">0</div>
                    <div class="stat-label">申请次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalAmount">¥0</div>
                    <div class="stat-label">累计金额</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="currentStatus">-</div>
                    <div class="stat-label">当前状态</div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-title">快捷操作</div>
            <div class="action-buttons">
                <div class="action-btn" onclick="showApplicationForm();">
                    <div class="action-icon apply">
                        <i class="ace-icon fa fa-plus-circle"></i>
                    </div>
                    <div class="action-text">申请助学金</div>
                </div>
                <div class="action-btn" onclick="checkApplicationStatus();">
                    <div class="action-icon status">
                        <i class="ace-icon fa fa-search"></i>
                    </div>
                    <div class="action-text">查询状态</div>
                </div>
                <div class="action-btn" onclick="viewPaymentInfo();">
                    <div class="action-icon payment">
                        <i class="ace-icon fa fa-credit-card"></i>
                    </div>
                    <div class="action-text">发放信息</div>
                </div>
                <div class="action-btn" onclick="showHistory();">
                    <div class="action-icon history">
                        <i class="ace-icon fa fa-history"></i>
                    </div>
                    <div class="action-text">历史记录</div>
                </div>
            </div>
        </div>

        <!-- 申请须知 -->
        <div class="notice-section">
            <div class="notice-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>申请须知</span>
            </div>
            <div class="notice-content">
                <ul class="notice-list">
                    <li>助学金主要面向家庭经济困难学生</li>
                    <li>需要提供家庭经济困难证明材料</li>
                    <li>每学年可申请一次，按月发放</li>
                    <li>申请材料需真实有效，如有虚假将取消资格</li>
                    <li>获得助学金的学生需参加相应的公益活动</li>
                </ul>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterApplications('all')">全部</button>
                <button class="filter-chip" onclick="filterApplications('pending')">审核中</button>
                <button class="filter-chip" onclick="filterApplications('approved')">已通过</button>
                <button class="filter-chip" onclick="filterApplications('rejected')">已拒绝</button>
                <button class="filter-chip" onclick="filterApplications('paid')">已发放</button>
            </div>
        </div>

        <!-- 申请表单 -->
        <div class="application-form" id="applicationForm">
            <div class="form-title">助学金申请</div>

            <div class="form-group">
                <label class="form-label">学生姓名</label>
                <input type="text" class="form-control" id="studentName" readonly>
            </div>

            <div class="form-group">
                <label class="form-label">学号</label>
                <input type="text" class="form-control" id="studentId" readonly>
            </div>

            <div class="form-group">
                <label class="form-label">专业班级</label>
                <input type="text" class="form-control" id="majorClass" readonly>
            </div>

            <div class="form-group">
                <label class="form-label required">联系电话</label>
                <input type="tel" class="form-control" id="contactPhone" placeholder="请输入联系电话">
            </div>

            <div class="form-group">
                <label class="form-label required">家庭年收入</label>
                <select class="form-control" id="familyIncome">
                    <option value="">请选择</option>
                    <option value="below_10000">1万元以下</option>
                    <option value="10000_30000">1-3万元</option>
                    <option value="30000_50000">3-5万元</option>
                    <option value="50000_100000">5-10万元</option>
                    <option value="above_100000">10万元以上</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label required">家庭人口数</label>
                <input type="number" class="form-control" id="familySize" placeholder="请输入家庭人口数" min="1">
            </div>

            <div class="form-group">
                <label class="form-label required">申请理由</label>
                <textarea class="form-control form-textarea" id="applicationReason"
                          placeholder="请详细说明家庭经济困难情况和申请助学金的理由..."></textarea>
                <div class="form-help">请如实填写家庭经济状况，包括收入来源、支出情况、特殊困难等</div>
            </div>

            <div class="form-group">
                <label class="form-label required">家庭成员情况</label>
                <textarea class="form-control form-textarea" id="familyMembers"
                          placeholder="请详细说明家庭成员构成、工作情况、健康状况等..."></textarea>
                <div class="form-help">包括父母工作情况、兄弟姐妹就学情况、家庭成员健康状况等</div>
            </div>

            <div class="form-group">
                <label class="form-label">申请金额</label>
                <select class="form-control" id="requestAmount">
                    <option value="">请选择</option>
                    <option value="1000">1000元/月</option>
                    <option value="1500">1500元/月</option>
                    <option value="2000">2000元/月</option>
                    <option value="2500">2500元/月</option>
                    <option value="3000">3000元/月</option>
                </select>
                <div class="form-help">根据家庭经济困难程度选择合适的申请金额</div>
            </div>

            <div class="form-actions">
                <button class="btn-mobile btn-cancel flex-1" onclick="hideApplicationForm();">取消</button>
                <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
            </div>
        </div>

        <!-- 助学金记录列表 -->
        <div class="container-mobile">
            <div id="financialList">
                <!-- 助学金记录将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-money"></i>
                <div>暂无助学金记录</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allApplications = [];
        let filteredApplications = [];
        let currentFilter = 'all';
        let studentInfo = {};

        $(function() {
            initPage();
            loadStudentInfo();
            loadApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/scholarship/financial/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                    populateStudentInfo();
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 填充学生信息
        function populateStudentInfo() {
            $('#studentName').val(studentInfo.name || '');
            $('#studentId').val(studentInfo.id || '');
            $('#majorClass').val(studentInfo.majorClass || '');
            $('#contactPhone').val(studentInfo.phone || '');
        }

        // 加载助学金申请记录
        function loadApplications() {
            showLoading(true);

            $.ajax({
                url: "/student/scholarship/financial/getApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allApplications = data.applications || [];
                    updateStatistics(data.statistics);
                    applyFilter();
                    showLoading(false);
                },
                error: function() {
                    showError('加载助学金记录失败');
                    showLoading(false);
                }
            });
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;

            $('#totalApplications').text(statistics.totalApplications || 0);
            $('#totalAmount').text('¥' + (statistics.totalAmount || 0));
            $('#currentStatus').text(statistics.currentStatus || '-');
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'pending':
                    filteredApplications = allApplications.filter(app => app.status === 'pending');
                    break;
                case 'approved':
                    filteredApplications = allApplications.filter(app => app.status === 'approved');
                    break;
                case 'rejected':
                    filteredApplications = allApplications.filter(app => app.status === 'rejected');
                    break;
                case 'paid':
                    filteredApplications = allApplications.filter(app => app.status === 'paid');
                    break;
                default:
                    filteredApplications = allApplications;
            }

            renderApplications();
        }

        // 筛选申请记录
        function filterApplications(filter) {
            currentFilter = filter;

            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');

            applyFilter();
        }

        // 渲染申请记录
        function renderApplications() {
            const container = $('#financialList');
            container.empty();

            if (filteredApplications.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            filteredApplications.forEach(application => {
                const applicationHtml = createApplicationItem(application);
                container.append(applicationHtml);
            });
        }

        // 创建申请记录项
        function createApplicationItem(application) {
            const statusClass = getStatusClass(application.status);
            const statusBadgeClass = getStatusBadgeClass(application.status);
            const statusText = getStatusText(application.status);

            let paymentHtml = '';
            if (application.status === 'approved' || application.status === 'paid') {
                paymentHtml = `
                    <div class="payment-info">
                        <div class="payment-title">发放信息</div>
                        <div class="payment-details">
                            发放金额：¥${application.amount}/月<br>
                            发放期限：${application.paymentPeriod}<br>
                            ${application.bankInfo ? `银行卡号：${application.bankInfo}` : ''}
                        </div>
                    </div>
                `;
            }

            return `
                <div class="financial-item ${statusClass}" onclick="showApplicationDetail('${application.id}')">
                    <div class="financial-header">
                        <div class="financial-type">助学金申请 - ${application.academicYear}</div>
                        <div class="financial-status ${statusBadgeClass}">${statusText}</div>
                    </div>
                    <div class="financial-details">
                        <div class="financial-detail-item">
                            <span>申请时间:</span>
                            <span>${application.applicationDate}</span>
                        </div>
                        <div class="financial-detail-item">
                            <span>申请金额:</span>
                            <span>¥${application.requestAmount}/月</span>
                        </div>
                        <div class="financial-detail-item">
                            <span>家庭年收入:</span>
                            <span>${getFamilyIncomeText(application.familyIncome)}</span>
                        </div>
                        <div class="financial-detail-item">
                            <span>审核时间:</span>
                            <span>${application.reviewDate || '待审核'}</span>
                        </div>
                    </div>
                    ${application.status === 'approved' || application.status === 'paid' ? `
                        <div class="financial-amount">
                            <div class="amount-label">批准金额</div>
                            <div class="amount-value">¥${application.approvedAmount}/月</div>
                        </div>
                    ` : ''}
                    ${paymentHtml}
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'approved': return 'financial-approved';
                case 'pending': return 'financial-pending';
                case 'rejected': return 'financial-rejected';
                case 'paid': return 'financial-approved';
                default: return '';
            }
        }

        // 获取状态徽章样式类
        function getStatusBadgeClass(status) {
            switch(status) {
                case 'approved': return 'status-approved';
                case 'pending': return 'status-pending';
                case 'rejected': return 'status-rejected';
                case 'paid': return 'status-paid';
                default: return 'status-pending';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'approved': return '已通过';
                case 'pending': return '审核中';
                case 'rejected': return '已拒绝';
                case 'paid': return '已发放';
                default: return '未知';
            }
        }

        // 获取家庭收入文本
        function getFamilyIncomeText(income) {
            switch(income) {
                case 'below_10000': return '1万元以下';
                case '10000_30000': return '1-3万元';
                case '30000_50000': return '3-5万元';
                case '50000_100000': return '5-10万元';
                case 'above_100000': return '10万元以上';
                default: return '未填写';
            }
        }

        // 显示申请表单
        function showApplicationForm() {
            // 检查是否已有进行中的申请
            const pendingApplication = allApplications.find(app => app.status === 'pending');
            if (pendingApplication) {
                showError('您已有申请正在审核中，请等待审核结果');
                return;
            }

            // 重置表单
            resetForm();

            // 显示表单
            $('#applicationForm').show();

            // 滚动到表单
            $('html, body').animate({
                scrollTop: $('#applicationForm').offset().top - 100
            }, 300);
        }

        // 隐藏申请表单
        function hideApplicationForm() {
            $('#applicationForm').hide();
        }

        // 重置表单
        function resetForm() {
            $('#contactPhone').val(studentInfo.phone || '');
            $('#familyIncome').val('');
            $('#familySize').val('');
            $('#applicationReason').val('');
            $('#familyMembers').val('');
            $('#requestAmount').val('');
        }

        // 获取表单数据
        function getFormData() {
            return {
                studentName: $('#studentName').val(),
                studentId: $('#studentId').val(),
                majorClass: $('#majorClass').val(),
                contactPhone: $('#contactPhone').val(),
                familyIncome: $('#familyIncome').val(),
                familySize: $('#familySize').val(),
                applicationReason: $('#applicationReason').val(),
                familyMembers: $('#familyMembers').val(),
                requestAmount: $('#requestAmount').val()
            };
        }

        // 验证表单
        function validateForm() {
            const errors = [];
            const formData = getFormData();

            if (!formData.contactPhone.trim()) {
                errors.push('请填写联系电话');
            }

            if (!formData.familyIncome) {
                errors.push('请选择家庭年收入');
            }

            if (!formData.familySize || formData.familySize < 1) {
                errors.push('请填写正确的家庭人口数');
            }

            if (!formData.applicationReason.trim()) {
                errors.push('请填写申请理由');
            }

            if (!formData.familyMembers.trim()) {
                errors.push('请填写家庭成员情况');
            }

            if (!formData.requestAmount) {
                errors.push('请选择申请金额');
            }

            if (errors.length > 0) {
                showError(errors.join('\n'));
                return false;
            }

            return true;
        }

        // 提交申请
        function submitApplication() {
            if (!validateForm()) return;

            const formData = getFormData();

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要提交助学金申请吗？提交后将无法修改。', function(confirmed) {
                    if (confirmed) {
                        doSubmitApplication(formData);
                    }
                });
            } else {
                if (confirm('确定要提交助学金申请吗？提交后将无法修改。')) {
                    doSubmitApplication(formData);
                }
            }
        }

        // 执行提交申请
        function doSubmitApplication(formData) {
            $.ajax({
                url: "/student/scholarship/financial/submitApplication",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('申请提交成功');
                        hideApplicationForm();
                        loadApplications();
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示申请详情
        function showApplicationDetail(applicationId) {
            const application = allApplications.find(app => app.id === applicationId);
            if (!application) return;

            let message = `助学金申请详情\n\n`;
            message += `申请学年：${application.academicYear}\n`;
            message += `申请时间：${application.applicationDate}\n`;
            message += `申请金额：¥${application.requestAmount}/月\n`;
            message += `家庭年收入：${getFamilyIncomeText(application.familyIncome)}\n`;
            message += `家庭人口：${application.familySize}人\n`;
            message += `申请状态：${getStatusText(application.status)}\n`;

            if (application.reviewDate) {
                message += `审核时间：${application.reviewDate}\n`;
            }

            if (application.approvedAmount) {
                message += `批准金额：¥${application.approvedAmount}/月\n`;
            }

            if (application.reviewComment) {
                message += `审核意见：${application.reviewComment}\n`;
            }

            if (application.paymentPeriod) {
                message += `发放期限：${application.paymentPeriod}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 查询申请状态
        function checkApplicationStatus() {
            const pendingApplication = allApplications.find(app => app.status === 'pending');

            if (!pendingApplication) {
                showError('您当前没有正在审核的申请');
                return;
            }

            showApplicationDetail(pendingApplication.id);
        }

        // 查看发放信息
        function viewPaymentInfo() {
            const paidApplications = allApplications.filter(app => app.status === 'paid' || app.status === 'approved');

            if (paidApplications.length === 0) {
                showError('您当前没有已通过的助学金申请');
                return;
            }

            let message = '助学金发放信息\n\n';
            paidApplications.forEach((app, index) => {
                message += `${index + 1}. ${app.academicYear}\n`;
                message += `   金额：¥${app.approvedAmount}/月\n`;
                message += `   期限：${app.paymentPeriod}\n`;
                if (app.bankInfo) {
                    message += `   银行卡：${app.bankInfo}\n`;
                }
                message += '\n';
            });

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示历史记录
        function showHistory() {
            $('html, body').animate({
                scrollTop: $('#financialList').offset().top - 100
            }, 300);
        }

        // 刷新数据
        function refreshData() {
            loadApplications();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#financialList').hide();
            } else {
                $('#loadingState').hide();
                $('#financialList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
