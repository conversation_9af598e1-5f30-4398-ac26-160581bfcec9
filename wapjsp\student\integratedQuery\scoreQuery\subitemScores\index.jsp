<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>分项成绩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 分项成绩页面样式 */
        .score-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-row {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-md);
        }
        
        .filter-row:last-child {
            margin-bottom: 0;
        }
        
        .filter-item {
            flex: 1;
        }
        
        .filter-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .filter-select {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .course-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .course-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .course-item.excellent {
            border-left-color: var(--success-color);
        }
        
        .course-item.good {
            border-left-color: var(--info-color);
        }
        
        .course-item.pass {
            border-left-color: var(--warning-color);
        }
        
        .course-item.fail {
            border-left-color: var(--error-color);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .course-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .course-total {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            font-weight: 600;
            color: white;
            text-align: center;
            min-width: 60px;
        }
        
        .total-excellent {
            background: var(--success-color);
        }
        
        .total-good {
            background: var(--info-color);
        }
        
        .total-pass {
            background: var(--warning-color);
        }
        
        .total-fail {
            background: var(--error-color);
        }
        
        .course-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .score-breakdown {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
        }
        
        .breakdown-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .breakdown-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .score-items {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .score-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 8px;
            background: var(--bg-primary);
            border-radius: 4px;
            font-size: var(--font-size-small);
        }
        
        .item-name {
            color: var(--text-secondary);
            flex: 1;
        }
        
        .item-weight {
            color: var(--text-disabled);
            font-size: var(--font-size-mini);
            margin: 0 var(--margin-xs);
        }
        
        .item-score {
            font-weight: 500;
            color: var(--text-primary);
            min-width: 40px;
            text-align: right;
        }
        
        .item-score.excellent {
            color: var(--success-color);
        }
        
        .item-score.good {
            color: var(--info-color);
        }
        
        .item-score.pass {
            color: var(--warning-color);
        }
        
        .item-score.fail {
            color: var(--error-color);
        }
        
        .statistics-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .stat-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .modal-close {
            font-size: var(--font-size-h4);
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .modal-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--margin-sm);
        }
        
        .detail-table th,
        .detail-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-small);
        }
        
        .detail-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .detail-table td {
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">分项成绩</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="score-header">
            <div class="header-title">课程分项成绩</div>
            <div class="header-subtitle">查看课程各项成绩构成详情</div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">学年学期</div>
                    <select class="filter-select" id="termSelect">
                        <option value="">全部学期</option>
                    </select>
                </div>
                <div class="filter-item">
                    <div class="filter-label">课程类型</div>
                    <select class="filter-select" id="courseTypeSelect">
                        <option value="">全部类型</option>
                        <option value="required">必修课</option>
                        <option value="elective">选修课</option>
                        <option value="practical">实践课</option>
                    </select>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-item">
                    <div class="filter-label">成绩等级</div>
                    <select class="filter-select" id="gradeSelect">
                        <option value="">全部等级</option>
                        <option value="excellent">优秀(90-100)</option>
                        <option value="good">良好(80-89)</option>
                        <option value="pass">及格(60-79)</option>
                        <option value="fail">不及格(0-59)</option>
                    </select>
                </div>
                <div class="filter-item">
                    <div class="filter-label">搜索课程</div>
                    <input type="text" class="filter-select" id="searchInput" placeholder="输入课程名称..." onkeyup="searchCourses()">
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="statistics-section">
            <div class="stats-title">成绩统计</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalCourses">0</div>
                    <div class="stat-label">总课程数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgScore">0</div>
                    <div class="stat-label">平均分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passRate">0%</div>
                    <div class="stat-label">及格率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="excellentRate">0%</div>
                    <div class="stat-label">优秀率</div>
                </div>
            </div>
        </div>

        <!-- 课程列表 -->
        <div class="container-mobile">
            <div id="courseList">
                <!-- 课程列表将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-bar-chart"></i>
                <div id="emptyMessage">暂无分项成绩数据</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="detail-modal" id="detailModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">成绩详情</div>
                <div class="modal-close" onclick="closeDetailModal();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allCourses = [];
        let filteredCourses = [];
        let availableTerms = [];

        $(function() {
            initPage();
            loadAvailableTerms();
            loadCourses();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFilterEvents();
        }

        // 绑定筛选事件
        function bindFilterEvents() {
            $('#termSelect, #courseTypeSelect, #gradeSelect').change(function() {
                applyFilters();
            });
        }

        // 加载可用学期
        function loadAvailableTerms() {
            $.ajax({
                url: "/student/integratedQuery/scoreQuery/subitemScores/getAvailableTerms",
                type: "post",
                dataType: "json",
                success: function(data) {
                    availableTerms = data.terms || [];
                    renderTermOptions();
                },
                error: function() {
                    console.log('加载学期列表失败');
                }
            });
        }

        // 渲染学期选项
        function renderTermOptions() {
            const select = $('#termSelect');
            select.find('option:not(:first)').remove();

            availableTerms.forEach(term => {
                select.append(`<option value="${term.id}">${term.name}</option>`);
            });
        }

        // 加载课程数据
        function loadCourses() {
            showLoading(true);

            $.ajax({
                url: "/student/integratedQuery/scoreQuery/subitemScores/getCourses",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allCourses = data.courses || [];
                    applyFilters();
                    updateStatistics();
                    showLoading(false);
                },
                error: function() {
                    showError('加载课程数据失败');
                    showLoading(false);
                }
            });
        }

        // 应用筛选条件
        function applyFilters() {
            const filters = {
                term: $('#termSelect').val(),
                courseType: $('#courseTypeSelect').val(),
                grade: $('#gradeSelect').val(),
                search: $('#searchInput').val().toLowerCase()
            };

            filteredCourses = allCourses.filter(course => {
                if (filters.term && course.termId !== filters.term) return false;
                if (filters.courseType && course.type !== filters.courseType) return false;
                if (filters.grade && getGradeLevel(course.totalScore) !== filters.grade) return false;
                if (filters.search && !course.name.toLowerCase().includes(filters.search)) return false;
                return true;
            });

            renderCourseList();
            updateStatistics();
        }

        // 搜索课程
        function searchCourses() {
            applyFilters();
        }

        // 渲染课程列表
        function renderCourseList() {
            const container = $('#courseList');
            container.empty();

            if (filteredCourses.length === 0) {
                showEmptyState('暂无符合条件的课程');
                return;
            } else {
                hideEmptyState();
            }

            filteredCourses.forEach(course => {
                const courseHtml = createCourseItem(course);
                container.append(courseHtml);
            });
        }

        // 创建课程项
        function createCourseItem(course) {
            const gradeLevel = getGradeLevel(course.totalScore);
            const totalClass = getTotalClass(course.totalScore);

            return `
                <div class="course-item ${gradeLevel}" onclick="showCourseDetail('${course.id}')">
                    <div class="course-header">
                        <div class="course-name">${course.name}</div>
                        <div class="course-total ${totalClass}">${course.totalScore}</div>
                    </div>
                    <div class="course-info">
                        <div class="info-item">
                            <span>课程代码:</span>
                            <span>${course.code}</span>
                        </div>
                        <div class="info-item">
                            <span>学分:</span>
                            <span>${course.credit}</span>
                        </div>
                        <div class="info-item">
                            <span>授课教师:</span>
                            <span>${course.teacher}</span>
                        </div>
                        <div class="info-item">
                            <span>学期:</span>
                            <span>${course.termName}</span>
                        </div>
                    </div>
                    <div class="score-breakdown">
                        <div class="breakdown-title">
                            <i class="ace-icon fa fa-pie-chart"></i>
                            <span>成绩构成</span>
                        </div>
                        <div class="score-items">
                            ${createScoreItems(course.scoreItems)}
                        </div>
                    </div>
                </div>
            `;
        }

        // 创建成绩项目
        function createScoreItems(items) {
            if (!items || items.length === 0) {
                return '<div class="score-item"><span class="item-name">暂无分项数据</span><span class="item-score">-</span></div>';
            }

            return items.map(item => {
                const scoreClass = getScoreClass(item.score);
                return `
                    <div class="score-item">
                        <span class="item-name">${item.name}</span>
                        <span class="item-weight">${item.weight}%</span>
                        <span class="item-score ${scoreClass}">${item.score}</span>
                    </div>
                `;
            }).join('');
        }

        // 获取等级
        function getGradeLevel(score) {
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 60) return 'pass';
            return 'fail';
        }

        // 获取总分样式类
        function getTotalClass(score) {
            if (score >= 90) return 'total-excellent';
            if (score >= 80) return 'total-good';
            if (score >= 60) return 'total-pass';
            return 'total-fail';
        }

        // 获取分数样式类
        function getScoreClass(score) {
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 60) return 'pass';
            return 'fail';
        }

        // 显示课程详情
        function showCourseDetail(courseId) {
            const course = allCourses.find(c => c.id === courseId);
            if (!course) return;

            $('#modalTitle').text(course.name + ' - 详细成绩');

            let modalContent = `
                <div class="course-basic-info">
                    <h4>基本信息</h4>
                    <table class="detail-table">
                        <tr><td>课程代码</td><td>${course.code}</td></tr>
                        <tr><td>课程名称</td><td>${course.name}</td></tr>
                        <tr><td>学分</td><td>${course.credit}</td></tr>
                        <tr><td>授课教师</td><td>${course.teacher}</td></tr>
                        <tr><td>学期</td><td>${course.termName}</td></tr>
                        <tr><td>总成绩</td><td>${course.totalScore}</td></tr>
                    </table>
                </div>
            `;

            if (course.scoreItems && course.scoreItems.length > 0) {
                modalContent += `
                    <div class="score-detail-info">
                        <h4>成绩构成</h4>
                        <table class="detail-table">
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>权重</th>
                                    <th>得分</th>
                                    <th>等级</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                course.scoreItems.forEach(item => {
                    const level = getGradeLevelText(item.score);
                    modalContent += `
                        <tr>
                            <td>${item.name}</td>
                            <td>${item.weight}%</td>
                            <td>${item.score}</td>
                            <td>${level}</td>
                        </tr>
                    `;
                });

                modalContent += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            if (course.remarks) {
                modalContent += `
                    <div class="remarks-info">
                        <h4>备注说明</h4>
                        <p>${course.remarks}</p>
                    </div>
                `;
            }

            $('#modalBody').html(modalContent);
            $('#detailModal').show();
        }

        // 获取等级文本
        function getGradeLevelText(score) {
            if (score >= 90) return '优秀';
            if (score >= 80) return '良好';
            if (score >= 60) return '及格';
            return '不及格';
        }

        // 关闭详情模态框
        function closeDetailModal() {
            $('#detailModal').hide();
        }

        // 更新统计信息
        function updateStatistics() {
            const courses = filteredCourses;
            const totalCourses = courses.length;

            if (totalCourses === 0) {
                $('#totalCourses').text(0);
                $('#avgScore').text(0);
                $('#passRate').text('0%');
                $('#excellentRate').text('0%');
                return;
            }

            const totalScore = courses.reduce((sum, course) => sum + parseFloat(course.totalScore), 0);
            const avgScore = (totalScore / totalCourses).toFixed(1);

            const passCount = courses.filter(course => course.totalScore >= 60).length;
            const excellentCount = courses.filter(course => course.totalScore >= 90).length;

            const passRate = ((passCount / totalCourses) * 100).toFixed(1);
            const excellentRate = ((excellentCount / totalCourses) * 100).toFixed(1);

            $('#totalCourses').text(totalCourses);
            $('#avgScore').text(avgScore);
            $('#passRate').text(passRate + '%');
            $('#excellentRate').text(excellentRate + '%');
        }

        // 刷新数据
        function refreshData() {
            loadAvailableTerms();
            loadCourses();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#courseList').hide();
            } else {
                $('#loadingState').hide();
                $('#courseList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#detailModal').click(function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
    </script>
</body>
</html>
