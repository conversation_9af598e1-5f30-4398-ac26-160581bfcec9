<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>本学期课表</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 课程表专用样式 */
        .timetable-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .view-mode-selector {
            display: flex;
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin: var(--margin-md);
            padding: 4px;
        }
        
        .view-mode-btn {
            flex: 1;
            padding: 8px 16px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            border-radius: 4px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .view-mode-btn.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
        }
        
        /* 表格视图 */
        .timetable {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--font-size-small);
        }
        
        .timetable th,
        .timetable td {
            border: 1px solid var(--divider-color);
            padding: 4px;
            text-align: center;
            vertical-align: top;
            height: 60px;
        }
        
        .timetable th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
            height: 40px;
        }
        
        .time-slot {
            background: var(--bg-tertiary);
            font-weight: 500;
            width: 60px;
            font-size: var(--font-size-mini);
        }
        
        .course-item {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-radius: 4px;
            padding: 4px;
            margin: 1px;
            font-size: 10px;
            line-height: 1.2;
            cursor: pointer;
            transition: all var(--transition-base);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .course-item:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
        
        /* 列表视图 */
        .day-view {
            display: none;
        }
        
        .day-view.active {
            display: block;
        }
        
        .day-selector {
            display: flex;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--divider-color);
            overflow-x: auto;
        }
        
        .day-btn {
            flex: 0 0 auto;
            padding: 12px 16px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-size: var(--font-size-small);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-base);
            white-space: nowrap;
        }
        
        .day-btn.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .day-course-list {
            padding: var(--padding-md);
        }
        
        .day-course-item {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .day-course-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .course-time {
            font-size: var(--font-size-small);
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .course-name {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .course-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
        }
        
        .course-location {
            display: flex;
            align-items: center;
        }
        
        .course-location i {
            margin-right: 4px;
        }
        
        /* 课程详情模态框 */
        .course-detail-modal .modal-body {
            padding: var(--padding-lg);
        }
        
        .detail-item {
            background: var(--bg-secondary);
            border-radius: 6px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: 4px;
        }
        
        .detail-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        /* 今日课程提醒 */
        .today-courses {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .today-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .today-title i {
            margin-right: var(--margin-xs);
        }
        
        .today-course-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-xs);
            font-size: var(--font-size-small);
        }
        
        .today-course-item:last-child {
            margin-bottom: 0;
        }
        
        /* 响应式优化 */
        @media (max-width: 480px) {
            .timetable th,
            .timetable td {
                height: 50px;
                font-size: 10px;
            }
            
            .course-item {
                font-size: 9px;
                padding: 2px;
            }
            
            .time-slot {
                width: 50px;
                font-size: 9px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">本学期课表</div>
            <div class="navbar-action" onclick="refreshTimetable();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 今日课程提醒 -->
        <div class="today-courses" id="todayCourses" style="display: none;">
            <div class="today-title">
                <i class="ace-icon fa fa-calendar"></i>
                <span>今日课程</span>
            </div>
            <div id="todayCoursesContent">
                <!-- 今日课程内容 -->
            </div>
        </div>
        
        <!-- 视图模式选择器 -->
        <div class="view-mode-selector">
            <button class="view-mode-btn active" onclick="switchView('table')">表格视图</button>
            <button class="view-mode-btn" onclick="switchView('list')">列表视图</button>
        </div>
        
        <!-- 表格视图 -->
        <div class="timetable-container" id="tableView">
            <table class="timetable" id="mycoursetable">
                <thead>
                    <tr>
                        <th class="time-slot">时间</th>
                        <th>周一</th>
                        <th>周二</th>
                        <th>周三</th>
                        <th>周四</th>
                        <th>周五</th>
                        <th>周六</th>
                        <th>周日</th>
                    </tr>
                </thead>
                <tbody id="timetableBody">
                    <!-- 课程表内容将通过JavaScript填充 -->
                </tbody>
            </table>
        </div>
        
        <!-- 列表视图 -->
        <div class="day-view" id="listView">
            <!-- 日期选择器 -->
            <div class="day-selector" id="daySelector">
                <button class="day-btn active" data-day="1">周一</button>
                <button class="day-btn" data-day="2">周二</button>
                <button class="day-btn" data-day="3">周三</button>
                <button class="day-btn" data-day="4">周四</button>
                <button class="day-btn" data-day="5">周五</button>
                <button class="day-btn" data-day="6">周六</button>
                <button class="day-btn" data-day="7">周日</button>
            </div>
            
            <!-- 每日课程列表 -->
            <div class="day-course-list" id="dayCourseList">
                <!-- 课程列表内容 -->
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 课程详情模态框 -->
    <div class="modal fade course-detail-modal" id="courseDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                    <h4 class="modal-title" id="courseDetailTitle">课程详情</h4>
                </div>
                <div class="modal-body" id="courseDetailBody">
                    <!-- 课程详情内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let courseData = [];
        let currentView = 'table';
        let currentDay = 1;
        let timeSlots = [
            '第1-2节\n08:00-09:40',
            '第3-4节\n10:00-11:40', 
            '第5-6节\n14:00-15:40',
            '第7-8节\n16:00-17:40',
            '第9-10节\n19:00-20:40'
        ];

        $(function() {
            initPage();
            loadTimetable();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            // 日期选择器事件
            $('.day-btn').click(function() {
                $('.day-btn').removeClass('active');
                $(this).addClass('active');
                currentDay = parseInt($(this).data('day'));
                renderDayView();
            });
        }

        // 加载课程表数据
        function loadTimetable() {
            showLoading(true);
            
            const url = "/student/courseSelect/thisSemesterCurriculum/ajaxStudentSchedule/callback";
            
            $.get(url, function(courseInfo) {
                try {
                    const infoArr = JSON.parse(JSON.stringify(courseInfo));
                    courseData = infoArr["xkxx"] || [];
                    
                    // 渲染课程表
                    renderTableView();
                    renderDayView();
                    renderTodayCourses();
                    
                    showLoading(false);
                } catch (error) {
                    console.error('解析课程数据失败:', error);
                    showError('加载课程表失败');
                    showLoading(false);
                }
            }).fail(function() {
                showError('网络错误，请重试');
                showLoading(false);
            });
        }

        // 渲染表格视图
        function renderTableView() {
            const tbody = $('#timetableBody');
            tbody.empty();
            
            // 创建时间段行
            timeSlots.forEach((timeSlot, slotIndex) => {
                const row = $('<tr></tr>');
                
                // 时间列
                const timeCell = $(`<td class="time-slot">${timeSlot}</td>`);
                row.append(timeCell);
                
                // 星期列
                for (let day = 1; day <= 7; day++) {
                    const dayCell = $('<td></td>');
                    const coursesInSlot = getCoursesByDayAndSlot(day, slotIndex + 1);
                    
                    coursesInSlot.forEach(course => {
                        const courseElement = createCourseElement(course);
                        dayCell.append(courseElement);
                    });
                    
                    row.append(dayCell);
                }
                
                tbody.append(row);
            });
        }

        // 渲染列表视图
        function renderDayView() {
            const container = $('#dayCourseList');
            container.empty();
            
            const coursesInDay = getCoursesByDay(currentDay);
            
            if (coursesInDay.length === 0) {
                container.html(`
                    <div class="empty-state">
                        <i class="ace-icon fa fa-calendar-o"></i>
                        <div>今天没有课程安排</div>
                    </div>
                `);
                return;
            }
            
            coursesInDay.forEach(course => {
                const courseHtml = createDayCourseItem(course);
                container.append(courseHtml);
            });
        }

        // 渲染今日课程
        function renderTodayCourses() {
            const today = new Date().getDay() || 7; // 周日为7
            const todayCourses = getCoursesByDay(today);
            
            if (todayCourses.length > 0) {
                const container = $('#todayCoursesContent');
                container.empty();
                
                todayCourses.forEach(course => {
                    const timeInfo = getTimeSlotInfo(course.jc);
                    const courseHtml = `
                        <div class="today-course-item">
                            <strong>${course.kcmc}</strong> - ${timeInfo} @ ${course.jsmc}
                        </div>
                    `;
                    container.append(courseHtml);
                });
                
                $('#todayCourses').show();
            }
        }

        // 获取指定日期和时间段的课程
        function getCoursesByDayAndSlot(day, slot) {
            return courseData.filter(course => {
                return course.xqj == day && isInTimeSlot(course.jc, slot);
            });
        }

        // 获取指定日期的所有课程
        function getCoursesByDay(day) {
            return courseData.filter(course => course.xqj == day)
                           .sort((a, b) => a.jc - b.jc);
        }

        // 判断节次是否在时间段内
        function isInTimeSlot(jc, slot) {
            const slotRanges = [
                [1, 2], [3, 4], [5, 6], [7, 8], [9, 10]
            ];
            const range = slotRanges[slot - 1];
            return jc >= range[0] && jc <= range[1];
        }

        // 创建课程元素
        function createCourseElement(course) {
            const element = $(`
                <div class="course-item" onclick="showCourseDetail('${course.jxb_id}')">
                    ${course.kcmc}
                </div>
            `);
            return element;
        }

        // 创建日视图课程项
        function createDayCourseItem(course) {
            const timeInfo = getTimeSlotInfo(course.jc);
            
            return `
                <div class="day-course-item" onclick="showCourseDetail('${course.jxb_id}')">
                    <div class="course-time">${timeInfo}</div>
                    <div class="course-name">${course.kcmc}</div>
                    <div class="course-info">
                        <div class="course-location">
                            <i class="ace-icon fa fa-map-marker"></i>
                            <span>${course.jsmc}</span>
                        </div>
                        <div class="course-teacher">${course.xm}</div>
                    </div>
                </div>
            `;
        }

        // 获取时间段信息
        function getTimeSlotInfo(jc) {
            const timeMap = {
                1: '08:00-08:45', 2: '08:55-09:40',
                3: '10:00-10:45', 4: '10:55-11:40',
                5: '14:00-14:45', 6: '14:55-15:40',
                7: '16:00-16:45', 8: '16:55-17:40',
                9: '19:00-19:45', 10: '19:55-20:40'
            };
            return `第${jc}节 ${timeMap[jc] || ''}`;
        }

        // 显示课程详情
        function showCourseDetail(courseId) {
            const course = courseData.find(c => c.jxb_id === courseId);
            if (!course) return;
            
            const detailHtml = `
                <div class="detail-item">
                    <div class="detail-label">课程名称</div>
                    <div class="detail-value">${course.kcmc}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">授课教师</div>
                    <div class="detail-value">${course.xm}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">上课地点</div>
                    <div class="detail-value">${course.jsmc}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">上课时间</div>
                    <div class="detail-value">周${course.xqj} ${getTimeSlotInfo(course.jc)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">课程号</div>
                    <div class="detail-value">${course.kch}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">学分</div>
                    <div class="detail-value">${course.xf}</div>
                </div>
            `;
            
            $('#courseDetailBody').html(detailHtml);
            $('#courseDetailModal').modal('show');
        }

        // 切换视图模式
        function switchView(viewType) {
            currentView = viewType;
            
            // 更新按钮状态
            $('.view-mode-btn').removeClass('active');
            $(event.target).addClass('active');
            
            // 切换视图
            if (viewType === 'table') {
                $('#tableView').show();
                $('#listView').removeClass('active');
            } else {
                $('#tableView').hide();
                $('#listView').addClass('active');
                renderDayView();
            }
        }

        // 刷新课程表
        function refreshTimetable() {
            loadTimetable();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 兼容原有函数
        function searchSemester() {
            loadTimetable();
        }

        function queryXxapinfo() {
            // 兼容原有功能
        }

        function queryExperiment() {
            // 兼容原有功能
        }
    </script>
</body>
</html>
