package educationalAdministration.student.personalApplication.curriculumReplacement.service.impl;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.hibernate.Session;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.urpSoft.core.cache.redis.RedisUtil;
import com.urpSoft.core.service.BaseService;
import com.urpSoft.core.util.AuthUtil;

import educationalAdministration.dictionary.entity.CodeKcb;
import educationalAdministration.dictionary.entity.EaApplys;
import educationalAdministration.interfaces.wechat.utils.Utils;
import educationalAdministration.student.common.service.CommonService;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.individualApplication.applyCommon.service.ApplyCommonService;
import educationalAdministration.student.personalApplication.curriculumReplacement.dao.CurriculumReplacementDao;
import educationalAdministration.student.personalApplication.curriculumReplacement.entity.CjKctdSqkcb;
import educationalAdministration.student.personalApplication.curriculumReplacement.service.CurriculumReplacementService;
import educationalAdministration.student.personalManagement.entity.JhFajhkcbView;
import educationalAdministration.student.personalManagement.entity.JhFakzsView;
import educationalAdministration.student.personalManagement.entity.PxCsb;
import educationalAdministration.student.personalManagement.entity.XsXjbView;

@Service("curriculumReplacementService")
public class CurriculumReplacementServiceImpl extends BaseService implements CurriculumReplacementService {

	@Resource
	private CurriculumReplacementDao curriculumReplacementDao;

	@PersistenceContext
	private EntityManager em;

	@Resource
	private CommonService commonService;

	@Resource
	private BaseService baseService;

	@Resource
	private ApplyCommonService applyCommonService;

	@Override
	public String querySqbh() {
		return curriculumReplacementDao.querySqbh();
	}
	@Override
	public void doDeleteAllCurriculum(String sqbh) {
		String sql = "delete from cj_kctd_sqkcb where sqbh = '"+sqbh+"'";
		executeUpdateSql(sql);
	}
	@Override
	public void doDeleteSpjlb(String sqbh) {
		String sql = "delete from cj_kctd_spjlb where sqbh = '"+sqbh+"'";
		executeUpdateSql(sql);
	}
	@Override
	public List<Object[]> queryTdkchList(String sqbh) {
		String sql = "select tdkch,pn.kcm(tdkch) tdkcm,pn.kc_xf(tdkch) xf from (select distinct tdkch from cj_kctd_sqkcb where sqbh='"+sqbh+"')";
		List<Object[]> list = curriculumReplacementDao.findEntitiesBySQL(sql);
		return list;
	}
	@Override
	public List<Object[]> queryKchList(String sqbh) {
		String sql = "select kch,pn.kcm(kch) kcm,pn.kc_xf(kch) xf from (select distinct kch from cj_kctd_sqkcb where sqbh='"+sqbh+"')";
		List<Object[]> list = curriculumReplacementDao.findEntitiesBySQL(sql);
		return list;
	}
	@Override
	public long queryCount(String xh,String kchs) {
		String sql = "select count(1) from cj_kctd_sqkcb where sqbh in(select sqbh from cj_kctd_sqb where sqzt>'-1' and xh='"+xh+"') and (kch in('"+kchs+"') or tdkch in('"+kchs+"'))";
		return curriculumReplacementDao.getCountByNativeSql(sql);
	}

	@Override
	public List<Object[]> queryKctdSpjlb(String sqbh) {
		String sql = "SELECT spxh,decode(xsh,'0','教务处',(pn.xsm(xsh))) xsm,spr,spztdm,spyj,sprq FROM cj_kctd_spjlb where sqbh='"+sqbh+"' order by spxh asc,xsh asc";
		return curriculumReplacementDao.findEntitiesBySQL(sql);
	}

	@Override
	public String queryParamValueById() {
		String value = "";
		if (RedisUtil.exists("jxglzgdwmc")) {
			value = RedisUtil.get("jxglzgdwmc");
		} else {
			value = commonService.findParamValue("教学管理主管单位名称");
			if (StringUtils.isBlank(value)) {
				value = "教务处";
			}
			RedisUtil.set("jxglzgdwmc", value);
		}
		return value;
	}

	@Override
	public Object[] queryCjKctdSqb(String sqbh) {
		String sql = "select jwckssp,jwcksspr,jwcksspip,jwcksspsj,jwcksspyj from cj_kctd_sqb where sqbh='"+sqbh+"'";
		return curriculumReplacementDao.findEntityBySQL(sql);
	}

	/**执行sql语句
	 * @param sql
	 * @return
	 */
	private int executeUpdateSql(String sql){
		Query query = em.createNativeQuery(sql);
		int i = query.executeUpdate();
		return i;
	}
	@Override
	public List<Object[]> queryAllPyfa(String studentId) {
		return curriculumReplacementDao.queryAllPyfa(studentId);
	}
	@Override
	public List<JhFakzsView> findJhFakzsList(String fajhh,String fkzh) {
		return curriculumReplacementDao.findJhFakzsList(fajhh,fkzh);
	}
	@Override
	public List<JhFajhkcbView> findJhFajhkcList(String fajhh,String fakzh) {
		return curriculumReplacementDao.findJhFajhkcList(fajhh,fakzh);
	}
	public List<Object[]> queryRootNode(String fajhh) {
		String sql = "select kzh as id,'<i class=\"ace-icon fa fa-kz blue\" style=\"font-size: 17px;\"></i>'||kzm as kzm,'kzh' as type,'folder' as isParent,kzm as title from JH_FAKZS_VIEW where fajhh='"+fajhh+"' and fkzh='-1' order by kzh";
		List<Object[]> list = curriculumReplacementDao.findEntitiesBySQL(sql);
		return list;
	}
	public List<Object[]> queryKzNode(String fajhh,String fkzh) {
		String sql = "select kzh as id,'<i class=\"ace-icon fa fa-kz blue\" style=\"font-size: 17px;\"></i>'||kzm as kzm,'kzh' as type,'folder' as isParent,kzm as title from JH_FAKZS_VIEW where fajhh='"+fajhh+"' and fkzh='"+fkzh+"' order by kzh";
		List<Object[]> list = curriculumReplacementDao.findEntitiesBySQL(sql);
		return list;
	}

	@Override
	public List<Object[]> queryKcNode(String fajhh,String fakzh,String xh) {
		String sql = "select a.fajhh||'_'||a.kch as id,case when a.kccj is null then '<i class=\"ace-icon fa fa-meh-o fa-1x grey\" style=\"font-size: 17px;\"></i> &nbsp;' " +
				" when a.kccj >= 60 then '<i class=\"ace-icon fa fa-smile-o fa-1x green\" style=\"font-size: 17px;\"></i> &nbsp;'" +
				" else '<i class=\"ace-icon fa fa-frown-o fa-1x red\" style=\"font-size: 17px;\"></i> &nbsp;' end " +
				" || a.kch || ' | ' || a.kcm || '【' || a.kcsxmc || ' | ' || pkg_com.f_NNF(a.xf) || '学分' || decode(a.kccj,null,'',' | ' || decode(a.cjlrfsdm, '002', a.djm, a.kccj)) || '】' as kcm," +
				" 'kch' as type,'item' as isParent," +
				" a.kch || ' | ' || a.kcm || '【' || a.kcsxmc || ' | ' || pkg_com.f_NNF(a.xf) || '学分' || decode(a.kccj,null,'',' | ' || decode(a.cjlrfsdm, '002', a.djm, a.kccj)) || '】' as title," +
				" a.kcm as param from (" + createQueryfajhkcbSql(fajhh, fakzh, xh, null, null) + ") a";
		return curriculumReplacementDao.findEntitiesBySQL(sql);
	}

	private String createQueryfajhkcbSql(String fajhh, String fakzh, String xh, String kch, String kcm) {
		String schoolCode = commonService.queryParamValue();
		String seach="";
		if("100006".equals(schoolCode)){
			seach += " and a.fajhh = '" + fajhh + "' and a.kcsxdm = '001' ";
		}
		if(StringUtils.isNotBlank(kch)){
			seach += " and a.kch = '" + kch + "' ";
		}
		if(StringUtils.isNotBlank(fajhh)){
			seach += " and a.fajhh = '" + fajhh + "' ";
		}
		if(StringUtils.isNotBlank(fakzh)){
			seach += " and a.fakzh = '" + fakzh + "' ";
		}
		if(StringUtils.isNotBlank(kcm)){
			seach += " and a.kcm like '%" + kcm + "%' ";
		}
		PxCsb csb = commonService.queryPxCsbById("cjgl", "jxswtgkc");
		String sql = "select a.fajhh,a.fakzh,a.jhkzh,a.jhxn,a.xqdm,a.xqlxdm,a.dj,a.kcztdm,a.kch,a.xnxq,a.kcm,a.xf,a.kcsxdm,a.kcsxmc,a.kslxdm,a.kslxmc,a.xqh,a.xqm," +
				" c.cjlrfsdm,c.djm,c.kccj from jh_fajhkcb_view a," +
				" (SELECT n.kch,m.cjlrfsdm,m.djm,m.kccj FROM xs_qbcj_all m," +
				" (SELECT xh,kch,kccj,MAX (kssj) AS kssj FROM xs_qbcj_all t WHERE xh = '" + xh + "' AND t.fajhh = '" + fajhh + "' AND (xh,kch,kccj) IN " +
				" (SELECT xh, kch, MAX (kccj) kccj FROM xs_qbcj_all WHERE xh = '" + xh + "' AND fajhh = '" + fajhh + "' GROUP BY xh, kch) GROUP BY xh, kch, kccj) n " +
				" WHERE m.kch = n.kch AND m.xh = n.xh AND m.kssj = n.kssj AND m.kccj = n.kccj AND m.xh = '" + xh + "' AND m.fajhh = '" + fajhh + "') c where a.kch=c.kch(+) " + seach +
				" and a.kch not in (select distinct tdkch from cj_kctd_sqkcb where sqbh in (select sqbh from cj_kctd_sqb where sqzt>'-1' and xh='" + xh + "'))" +
				" and a.kch not in (select kch from xs_cj_all where xh='" + xh + "' and tdkch is not null ";
		if(csb!=null&&"1".equals(csb.getCsz())){
			sql += " and nvl(kccj, 0) >= 60 ";
		}		
		sql += ") and a.kch not in(select tdkch from xs_cj_all where xh='"+xh+"' and tdkch is not null) order by a.kch";
		return sql;
	}

	@Override
	public List<Object[]> queryfajhkcb(String fajhh, String fakzh, String xh, String kch, String kcm) {
		String sql = "select a.kch,a.kccj,a.kcm,a.xf,a.kcsxdm,a.kcsxmc,pkg_fajh.f_CourseGroupPath(a.fakzh,'-') kzm from (" + createQueryfajhkcbSql(fajhh, fakzh, xh, kch, kcm) + ") a ";
		return curriculumReplacementDao.findEntitiesBySQL(sql);
	}

	@Override
	public List<Object[]> queryStudentScore(String xh,String kch,String kcm) {
		String seach="";
		if(StringUtils.isNotBlank(kch)){
			seach+=" and b.kch='"+kch+"' ";
		}
		if(StringUtils.isNotBlank(kcm)){
			seach+=" and b.kcm like'%"+kcm+"%' ";
		}
		String schoolCode = commonService.queryParamValue();
		if("100006".equals(schoolCode)){
			seach+=" and a.kch not in(select kch from jh_fajhkcb where fajhh in(select fajhh from xs_pyb where xh='"+xh+"')) ";
		}
		String sql = "select a.kch,decode(a.cjlrfsdm,'002',(select t.djmc from code_djcjzhb t where t.id=a.djcj),a.kccj) kccj,b.kcm,b.xf,a.kcsxdm," + 
				" (select t.kcsxmc from code_kcsxb t where a.kcsxdm=t.kcsxdm) kcsxmc from (SELECT m.kch,m.cjlrfsdm,m.djcj,m.kccj,m.kcsxdm FROM xs_cj_all m," +
				" (SELECT xh,kch,kccj,MAX (kssj) AS kssj FROM xs_cj_all t WHERE xh = '" + xh + "' AND (xh, kch, kccj) IN (SELECT xh, kch, MAX (kccj) kccj FROM xs_cj_all WHERE xh = '" + xh + "' GROUP BY xh, kch) GROUP BY xh, kch,kccj) n " +
				" WHERE m.kch = n.kch AND m.xh = n.xh AND m.kssj = n.kssj AND m.kccj = n.kccj AND m.xh = '" + xh + "') a, code_kcb b where a.kch = b.kch and a.kccj>=60 " + seach +
				" and a.kch not in (select distinct kch from cj_kctd_sqkcb where sqbh in (select sqbh from cj_kctd_sqb where sqzt>'-1' and xh='"+xh+"') UNION " +
				" select distinct tdkch from cj_kctd_sqkcb where sqbh in (select sqbh from cj_kctd_sqb where sqzt>'-1' and xh='"+xh+"')) " +
				" and a.kch not in(select kch from xs_cj_all where xh='"+xh+"' and tdkch is not null) and a.kch not in(select tdkch from xs_cj_all where xh='"+xh+"' and tdkch is not null)";
		return curriculumReplacementDao.findEntitiesBySQL(sql);
	}

	@Override
	public List<CjKctdSqkcb> findCjKctdSqkcbList(String sqbh) {
		return curriculumReplacementDao.findCjKctdSqkcbList(sqbh);
	}

	@Override
	public Map<String,Object> saveCurriculumInfo(Map<String,Object> map,HttpServletRequest request,String state,List<JSONObject> list) {
		String xh = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
		XsXjbView xjb = curriculumReplacementDao.findById(XsXjbView.class, xh);
		request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		/*用户ip*/
		String sqip = commonService.getRemoteHost(request);
		String sqrq = commonService.queryCurrentTimeBySql();
		String czsj = commonService.queryCurrentTimeMinssBySql();
		String xnxq = commonService.queryNowXnxqOne();
		String schoolCode = commonService.queryParamValue();
		String result="ok";
		HashSet<String> hashSet = new HashSet<String>();
		List<String> kchList=new ArrayList<String>();
		List<String> tdkchList=new ArrayList<String>();

		for (int i = 0; i < list.size(); i++) {
			JSONObject form = list.get(i);

			String kchs = form.getString("kch");
			String[] kcArr = kchs.split(",");
			BigDecimal kczxf=new BigDecimal("0");
			for (int m = 0; m < kcArr.length; m++) {
				hashSet.add(kcArr[m]);
				kchList.add(kcArr[m]);
				if("100051".equals(schoolCode)){
					CodeKcb kcb = curriculumReplacementDao.findById(CodeKcb.class, kcArr[m]);
					if(kcb!=null){
						if(kcb.getXf()!=null){
							kczxf=kczxf.add(new BigDecimal(kcb.getXf()));
						}
					}

				}
			}

			BigDecimal tdkczxf=new BigDecimal("0");
			String tdkchs = form.getString("tdkch");
			String[] tdkcArr = tdkchs.split(",");
			for (int m = 0; m < tdkcArr.length; m++) {
				hashSet.add(tdkcArr[m]);
				tdkchList.add(tdkcArr[m]);
				if("100051".equals(schoolCode)){
					CodeKcb kcb = curriculumReplacementDao.findById(CodeKcb.class, tdkcArr[m]);
					if(kcb!=null){
						if(kcb.getXf()!=null){
							tdkczxf=tdkczxf.add(new BigDecimal(kcb.getXf()));
						}
					}
				}
			}

			if("100051".equals(schoolCode)){
				if((tdkczxf.subtract(kczxf)).compareTo(new BigDecimal(1))==1){//被替代课程学分和-课程学分和>1 禁止申请
					if(list.size()>1){
						result = "第"+(i+1)+"条申请中课程学分和（"+tdkczxf+"学分）-替代课程学分（"+kczxf+"学分）和大于1学分，不能申请！";
					}else{
						result = "当前申请中课程学分和（"+tdkczxf+"学分）-替代课程学分（"+kczxf+"学分）和大于1学分，不能申请！";
					}
					map.put("result",result);
					return map;
				}else if((tdkczxf.subtract(kczxf)).compareTo(new BigDecimal(0.5))==1&&(tdkczxf.subtract(kczxf)).compareTo(new BigDecimal(1))!=1){//0.5<被替代课程学分和-课程学分和<=1  课程成绩必须>=75
					String sql="select a.kch,decode(a.cjlrfsdm, '002', pkg_cj.f_GradeName(a.djcj), a.kccj) kccj, b.kcm,b.xf,b.xs from (" +
							"SELECT m.kch, m.cjlrfsdm, m.djcj, m.kccj, m.kcsxdm FROM xs_cj_all m, (SELECT xh, kch, kccj, MAX(kssj) AS kssj FROM xs_cj_all t WHERE xh = '"+xh+"'" +
							"AND (xh, kch, kccj) IN (SELECT xh, kch, MAX(kccj) kccj FROM xs_cj_all WHERE xh = '"+xh+"' GROUP BY xh, kch) GROUP BY xh, kch, kccj) n WHERE " +
							"m.kch = n.kch AND m.xh = n.xh AND m.kssj = n.kssj AND m.kccj = n.kccj AND m.xh = '"+xh+"') a, code_kcb b where a.kch = b.kch " +
							"and a.kch in ('"+StringUtils.join(kchList, "','")+"') and nvl(a.kccj,0)<75";
					List<Object[]> kclist = curriculumReplacementDao.findEntitiesBySQL(sql);
					if(kclist!=null&&kclist.size()>0){
						String msg="";
						for (Object[] obj : kclist) {
							msg+=obj[0]+"-"+obj[2]+"（"+obj[3]+"学分，"+obj[1]+"）、";
						}
						if(list.size()>1){
							result = "第"+(i+1)+"条申请中课程"+msg.substring(0, msg.length()-1)+"成绩小于75分，不能申请！";
						}else{
							result = "当前申请中课程"+msg.substring(0, msg.length()-1)+"成绩小于75分，不能申请！";
						}
						map.put("result",result);
						return map;
					}
				}
			}
		}

		if (hashSet.size() != (kchList.size()+tdkchList.size())) {
			result = "保存失败，当前申请中有重复课程！";
			map.put("result",result);
			return map;
		} 

		if(kchList!=null&&tdkchList!=null&&kchList.size()>0&&tdkchList.size()>0){
			boolean sendMsg=false;
			if("100007".equals(schoolCode)){
				String sql="select count(1) from MSG_PUSH_BUSINESS_MANAGEMENT where id='10016' and ywkg='1'";
				if(curriculumReplacementDao.getCountByNativeSql(sql)>0){
					sendMsg=true;
				}
			}

			PxCsb csb = CommonUtils.queryPxCsbById("kctdsp", "qygzl");
			String approvalProcess=(csb!=null&&"0".equals(csb.getCsz()))?"0":"1";

			Session session = (Session) em.getDelegate();
			SessionFactoryImplementor sf = (SessionFactoryImplementor) session.getSessionFactory();
			Connection conn = null;
			CallableStatement cs = null;
			PreparedStatement ps = null;
			try {
				conn = sf.getConnectionProvider().getConnection();
				conn.setAutoCommit(false);//把自动提交方式变为人工
				for (int i = 0; i < list.size(); i++) {
					JSONObject form = list.get(i);
					String sqbh = querySqbh();
					String tdlx=form.getString("tdlx");
					String tdyym=form.getString("tdyym");
					String ealUser=form.getString("ealUser");
					String sqyy=form.getString("sqyy");
					String kcList=form.getString("kch");
					String tdkcList=form.getString("tdkch");
					long count = queryCount(xh, kcList.replace(",", "','"));
					if(count==0){
						String sql="";
						if("100007".equals(schoolCode)){
							sql="select count(1) from code_zyfxrb where (xsh,zyh) in (select xsh,zyh from jh_fajhb where fajhh in(select fajhh from xs_pyb where xh='"+xh+"' and fajhh in(select fajhh from jh_fajhkcb where kch in('"+tdkcList.replace(",", "','")+"'))))";
							long num = curriculumReplacementDao.getCountByNativeSql(sql);
							if(num==0){
								conn.rollback();
								result = "第"+(i+1)+"行申请数据中被替代课程所属方案的专业负责人尚未维护！";
								break;
							}
						}
						if("01".equals(tdlx)||"04".equals(tdlx)){//一替一
							sql="insert into cj_kctd_sqkcb(sqbh,kch,tdkch) values ('"+sqbh+"','"+kcList+"','"+tdkcList+"')";
							ps = conn.prepareStatement(sql);
							ps.executeUpdate();
							ps.close();
						}else if("02".equals(tdlx)){//一替多
							String[] tdkchArr=tdkcList.split(",");
							for (String tdkch : tdkchArr) {
								sql="insert into cj_kctd_sqkcb(sqbh,kch,tdkch) values ('"+sqbh+"','"+kcList+"','"+tdkch+"')";
								ps = conn.prepareStatement(sql);
								ps.executeUpdate();
								ps.close();
							}
						}else{//多替一
							String[] kchArr=kcList.split(",");
							for (String kch : kchArr) {
								sql="insert into cj_kctd_sqkcb(sqbh,kch,tdkch) values ('"+sqbh+"','"+kch+"','"+tdkcList+"')";
								ps = conn.prepareStatement(sql);
								ps.executeUpdate();
								ps.close();
							}
						}
						sql = "insert into cj_kctd_sqb (sqbh, xh, tdlx, sqzt, sqrq, sqip, xnxq, tdyym, sqyy) values ('"+sqbh+"', '"+xh+"', '"+tdlx+"', '"+state+"', "+sqrq+", '"+sqip+"', '"+xnxq+"', '"+tdyym+"', '"+sqyy+"')";
						ps = conn.prepareStatement(sql);
						ps.executeUpdate();
						ps.close();

						if ("100006".equals(schoolCode)||"100053".equals(schoolCode)||"100060".equals(schoolCode)) {
							/*
							 -- 根据申请编号判断当前规则是否存在，存在则自动审批通过
							 -- 传入参数：申请单号
							 -- 传出参数：0-存在;否则传出错误信息
							 */
							sql = "{call pkg_cj.p_coursereplace(?,?)}";
							cs = conn.prepareCall(sql);
							cs.setString(1, sqbh);
							cs.registerOutParameter(2, Types.VARCHAR);
							cs.executeUpdate();
							String msg = cs.getString(2);
							cs.close();
							if("0".equals(msg)){
								sql="select sqzt from cj_kctd_sqb where sqbh='"+sqbh+"' ";
								ps = conn.prepareStatement(sql);
								ResultSet set = ps.executeQuery();
								while (set.next()) {
									state = set.getString("sqzt");
								}
								if(!"2".equals(state)){
									conn.rollback();
									result = "第"+(i+1)+"行申请数据不符合替代规则，请联系所在学院教务老师。";
									break;
								}else{
									conn.commit();
								}
							}else{
								conn.rollback();//出现异常进行回滚
								result = "第"+(i+1)+"行："+msg;
								break;
							}
						}else if ("0".equals(approvalProcess)) {
							if("0".equals(state)||"1".equals(state)){
								String msg = automaticApproval(sqbh);
								/*	if("0".equals(msg)){
										conn.commit();
									}else{
										conn.rollback();//出现异常进行回滚
										result = "第"+(i+1)+"行："+msg;
										break;
									}*/
							}
							conn.commit();
						}else{
							if("1".equals(state)){
								//添加审批表数据
								sql = "insert into ea_applys (apply_id, apply_type, user_code, commit_dt, apply_status, zxjxjhh) values ('"+sqbh+"', '10001', '"+xh+"', '"+czsj+"', 1, '"+xnxq+"')";
								ps = conn.prepareStatement(sql);
								ps.executeUpdate();

								//添加审批环节数据
								/*pkg_eap：
								PROCEDURE p_NextAprove
								 *//*
								  --根据审批流程环节配置及当前审批环节，预置后续审批环节
								  *//*
								(
								as_ApplyID  IN ea_rslt_parallel.apply_id%TYPE, --申请单号
								an_ealOrder IN ea_rslt_parallel.eal_order%TYPE, --当前审批环节序号：申请时预置第一级审批环节，传入0
								as_ealOrg   IN ea_rslt_parallel.eal_org%TYPE, --审批人所属院系
								os_eMsg     OUT VARCHAR2 --传出错误信息：0表示预置审批环节成功，O表示审批终结
								);*/

								sql = "{call pkg_eap.p_NextAprove(?,?,?,?)}";
								cs = conn.prepareCall(sql);
								cs.setString(1, sqbh);
								cs.setString(2, "0");
								cs.setString(3, xjb.getDepartment().getDepartmentCode());
								cs.registerOutParameter(4, Types.VARCHAR);
								cs.executeUpdate();
								if ("0".equals(cs.getString(4))) {
									if(StringUtils.isNotBlank(ealUser)){
										String[] arr = ealUser.split(",");
										for (String obj : arr) {
											sql = "update ea_rslt_parallel set eal_user='"+obj.split("_")[3]+"' where apply_id='" + sqbh + "' and eap_code='"+obj.split("_")[1]+"' and eal_code='"+obj.split("_")[2]+"' and eal_org='"+obj.split("_")[0]+"'";
											cs = conn.prepareCall(sql);
											cs.executeUpdate();
										}
									}
									conn.commit();
									if (sendMsg&&"100007".equals(schoolCode)) {
										sql="select eap_code,eal_code,eal_org,eal_rslt,eal_user,pn.jsm(eal_user) from ea_rslt_parallel where apply_id='"+sqbh+"' and (eap_code,eal_code) in (select eap_code,eal_code from (select a.* from ea_process_link a,ea_process b where a.eap_code=b.eap_code and b.apply_type='10001' and a.in_use=1 order by a.eal_order)where rownum=1)";
										List<Object[]> reslts = curriculumReplacementDao.findEntitiesBySQL(sql);//获取第一个审批环节数据
										if(reslts!=null&&reslts.size()>0){
											boolean flag=false;
											for (Object[] rslt : reslts) {
												if(rslt[3]!=null&&!rslt[3].toString().equals("0")){//判断第一个审批环节是否存在已审批的数据
													flag=true;
												}
											}
											if(!flag){//如果第一个审批环节还未开始审批，则向审批人推送消息
												String token = Utils.getToken();
												csb = CommonUtils.queryPxCsbById("wx", "ip");
												String url = (csb != null && StringUtils.isNotBlank(csb.getCsz()) ? csb.getCsz() : "") + "/uc/api/ucs/qywx";
												csb = CommonUtils.queryPxCsbById("wx", "js_wid");
												String js_wid = (csb != null && StringUtils.isNotBlank(csb.getCsz()) ? csb.getCsz() : "");
												for (Object[] rslt : reslts) {
													String xsh=rslt[2].toString();
													String sqsj=new SimpleDateFormat("yyyy-mm-dd").format(new SimpleDateFormat("yyyymmdd").parse(sqrq));
													if(rslt[4]!=null){//如果指定了审批人则只向指定的审批人推送消息
														String param = "access_token="+token+"&numbers[0]="+rslt[4]+"&isall=0&content[msgtype]=text&content[safe]=0&content[text][content]=尊敬的"+rslt[5]+"老师：\n  您好！"+xjb.getDepartment().getDepartmentName()+xjb.getCodeYears().getYearName()+"学生"+xjb.getXm()+"于"+sqsj+"提交的课程替代申请需要您审批，请您及时处理.谢谢！【教务处】&wid="+js_wid;
														Utils.sendMsg(url, param);
													}else{//如果未指定审批人则需要向具有第一审批环节审批权限的人推送消息
														sql="select c.jsh, c.jsm, pn.xsm(c.org_id) as js_xsm from urp_user_role a, urp_user b, code_jsb c where c.jsh = b.idnumber and a.user_id = b.id and b.enabled = '1' and c.zzztdm = '01' " +
																("00".equals(xsh)?"":" and pkg_others.f_OrgGovem(c.org_id,'"+xsh+"')='1' ")+
																" and exists(select 1 from ea_rslt_parallel m,ea_process_link n where m.eap_code=n.eap_code and m.eal_code=n.eal_code and n.eal_role=a.role_id " +
																("00".equals(xsh)?"":" and pkg_others.f_OrgGovem(c.org_id,m.eal_org)='1' ")+" and m.apply_id='"+sqbh+"' and m.eap_code='"+rslt[0]+"' and m.eal_code='"+rslt[1]+"')";
														List<Object[]> jsList = curriculumReplacementDao.findEntitiesBySQL(sql);
														for (Object[] js : jsList) {
															String param = "access_token="+token+"&numbers[0]="+js[0]+"&isall=0&content[msgtype]=text&content[safe]=0&content[text][content]=尊敬的"+js[1]+"老师：\n  您好！"+xjb.getDepartment().getDepartmentName()+xjb.getCodeYears().getYearName()+"学生"+xjb.getXm()+"于"+sqsj+"提交的课程替代申请需要您审批，请您及时处理.谢谢！【教务处】&wid="+js_wid;
															Utils.sendMsg(url, param);
														}
													}
												}
											}
										}
									}
								}else if ("O".equals(cs.getString(4))) {
									//result = "第"+(i+1)+"行申请数据审批已终结！";
									//conn.rollback();//出现异常进行回滚
									conn.commit();
									if (sendMsg&&"100007".equals(schoolCode)) {
										String token = Utils.getToken();
										csb = CommonUtils.queryPxCsbById("wx", "ip");
										String url = (csb != null && StringUtils.isNotBlank(csb.getCsz()) ? csb.getCsz() : "") + "/uc/api/ucs/qywx";
										String tdlxsm="";//01 一替一，02  一替多，03 多替一
										if("01".equals(tdlx)){
											tdlxsm="一替一";
										}else if("02".equals(tdlx)){
											tdlxsm=" 一替多";
										}else if("03".equals(tdlx)){
											tdlxsm="多替一";
										}
										csb = CommonUtils.queryPxCsbById("wx", "xs_wid");
										String xs_wid = (csb != null && StringUtils.isNotBlank(csb.getCsz()) ? csb.getCsz() : "");
										String sqsj=new SimpleDateFormat("yyyy-mm-dd").format(new SimpleDateFormat("yyyymmdd").parse(sqrq));
										String param = "access_token=" + token + "&numbers[0]=" + xh + "&isall=0&content[msgtype]=text&content[safe]=0&content[text][content]="+xjb.getXm()+"同学：\n  你好！你于" + sqsj + "提交的替代课申请【替代类型：" + tdlxsm + "；申请编号：" + sqbh + "】已审批通过，详情请登录教务系统。谢谢！【教务处】&wid=" + xs_wid;
										Utils.sendMsg(url, param);
									}
								} else {
									conn.rollback();//出现异常进行回滚
									result = "第"+(i+1)+"行："+cs.getString(4);
									break;
								}

							}else{
								conn.commit();
							}
						}
					}else{
						conn.rollback();
						result = "第"+(i+1)+"行申请数据中有些课程已申请过课程替代！";
						break;
					}
				}		

			} catch (Exception e) {
				try {
					conn.rollback();//出现异常进行回滚；
				} catch (SQLException e1) {
					e1.printStackTrace();
				}
				e.printStackTrace();
			} finally {
				try {
					conn.setAutoCommit(true);
				} catch (SQLException e) {
					e.printStackTrace();
				}
				CommonUtils.closeDB(conn, cs);

			}

		}else{
			result = "保存失败，申请数据有误！";
		}
		map.put("result",result);
		return map;

	}

	@Override
	public String automaticApproval(String sqbh) {
		Session session = (Session) em.getDelegate();
		SessionFactoryImplementor sf = (SessionFactoryImplementor) session.getSessionFactory();
		Connection conn = null;
		CallableStatement cs = null;
		String result = null;
		try {
			conn = sf.getConnectionProvider().getConnection();
			conn.setAutoCommit(false);//把自动提交方式变为人工
			/*
			 -- 根据申请编号判断当前规则是否存在，存在则自动审批通过
			 -- 传入参数：申请单号
			 -- 传出参数：0-存在;否则传出错误信息
			 */
			String sql = "{call pkg_cj.p_coursereplace(?,?)}";
			cs = conn.prepareCall(sql);
			cs.setString(1, sqbh);
			cs.registerOutParameter(2, Types.VARCHAR);
			cs.executeUpdate();
			result = cs.getString(2);
			if("0".equals(result)){
				conn.commit();
			}else{
				conn.rollback();
			}
		} catch (SQLException e) {
			try {
				conn.rollback();//出现异常进行回滚；
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		} finally {
			try {
				conn.setAutoCommit(true);
			} catch (SQLException e) {
				e.printStackTrace();
			}
			CommonUtils.closeDB(conn, cs);

		}
		return result;

	}


	@Override
	public String revoke(HttpServletRequest request,String sqbh,String cxyy) {
		String xh = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
		String ip = request.getRemoteHost();
		Session session = (Session) em.getDelegate();
		SessionFactoryImplementor sf = (SessionFactoryImplementor) session.getSessionFactory();
		Connection conn = null;
		CallableStatement cs = null;
		String result = null;
		try {
			conn = sf.getConnectionProvider().getConnection();
			conn.setAutoCommit(false);//把自动提交方式变为人工
			/*
			 -- 撤销指定课程替代申请单
			 -- 传出参数：0-成功;否则传出错误信息
			 */
			String sql = "{call pkg_cj.p_srCancel(?,?,?,?,?,?)}";
			cs = conn.prepareCall(sql);
			cs.setString(1, sqbh);
			cs.setInt(2, 0);
			cs.setString(3, xh);
			cs.setString(4, ip);
			cs.setString(5, cxyy);
			cs.registerOutParameter(6, Types.VARCHAR);
			cs.executeUpdate();
			result = cs.getString(6);
			if("0".equals(result)){
				conn.commit();
			}else{
				conn.rollback();
			}
		} catch (SQLException e) {
			try {
				conn.rollback();//出现异常进行回滚；
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		} finally {
			try {
				conn.setAutoCommit(true);
			} catch (SQLException e) {
				e.printStackTrace();
			}
			CommonUtils.closeDB(conn, cs);

		}
		return result;

	}

	@Override
	public Map<String,Object> queryApprovers(HttpServletRequest request,Object[] obj,String tdlx,String kcList,String tdkcList) {
		Map<String,Object> map = new HashMap<String,Object>();
		if(StringUtils.isNotBlank(kcList)&&StringUtils.isNotBlank(tdkcList)){
			String[] kcArr = kcList.split(",");
			String[] tdkcArr = tdkcList.split(",");
			HashSet<String> hashSet = new HashSet<String>();
			for (int i = 0; i < kcArr.length; i++) {
				hashSet.add(kcArr[i]);
			}
			for (int i = 0; i < tdkcArr.length; i++) {
				hashSet.add(tdkcArr[i]);
			}
			if (hashSet.size() != (kcArr.length+tdkcArr.length)) {
				map.put("result", "保存失败，当前申请中有重复课程！");
			}else{

				String xh = AuthUtil.getCurrentUser().getIdNumber(); // 获得用户信息
				String sqbh = querySqbh();
				String sqip = commonService.getRemoteHost(request);
				String sqrq = commonService.queryCurrentTimeBySql();
				String sql="";

				Session session = (Session) em.getDelegate();
				SessionFactoryImplementor sf = (SessionFactoryImplementor) session.getSessionFactory();
				Connection conn = null;
				CallableStatement cs = null;
				PreparedStatement ps = null;

				try {
					conn = sf.getConnectionProvider().getConnection();
					conn.setAutoCommit(false);//把自动提交方式变为人工

					long count = queryCount(xh, kcList.replace(",", "','"));
					if(count==0){
						if("01".equals(tdlx)||"04".equals(tdlx)){//一替一
							sql="insert into cj_kctd_sqkcb(sqbh,kch,tdkch) values ('"+sqbh+"','"+kcList+"','"+tdkcList+"')";
							ps = conn.prepareStatement(sql);
							ps.executeUpdate();
							ps.close();
						}else if("02".equals(tdlx)){//一替多
							String[] tdkchList=tdkcList.split(",");
							for (String tdkch : tdkchList) {
								sql="insert into cj_kctd_sqkcb(sqbh,kch,tdkch) values ('"+sqbh+"','"+kcList+"','"+tdkch+"')";
								ps = conn.prepareStatement(sql);
								ps.executeUpdate();
								ps.close();
							}
						}else{//多替一
							String[] kchList=kcList.split(",");
							for (String kch : kchList) {
								sql="insert into cj_kctd_sqkcb(sqbh,kch,tdkch) values ('"+sqbh+"','"+kch+"','"+tdkcList+"')";
								ps = conn.prepareStatement(sql);
								ps.executeUpdate();
								ps.close();
							}
						}
						sql = "insert into cj_kctd_sqb (sqbh, xh, tdlx, sqzt, sqrq, sqip) values ('"+sqbh+"', '"+xh+"', '"+tdlx+"', '1', '"+sqrq+"', '"+sqip+"')";
						ps = conn.prepareStatement(sql);
						ps.executeUpdate();
						ps.close();

						//添加审批表数据
						sql = "insert into ea_applys (apply_id, apply_type, user_code, commit_dt, apply_status, zxjxjhh) values ('"+sqbh+"', '10001', '"+xh+"', '', 1, '')";
						ps = conn.prepareStatement(sql);
						ps.executeUpdate();
						ps.close();

						//添加审批环节数据
						/*pkg_eap：
							PROCEDURE p_NextAprove
						 *//*
							  --根据审批流程环节配置及当前审批环节，预置后续审批环节
						  *//*
							(
							as_ApplyID  IN ea_rslt_parallel.apply_id%TYPE, --申请单号
							an_ealOrder IN ea_rslt_parallel.eal_order%TYPE, --当前审批环节序号：申请时预置第一级审批环节，传入0
							as_ealOrg   IN ea_rslt_parallel.eal_org%TYPE, --审批人所属院系
							os_eMsg     OUT VARCHAR2 --传出错误信息：0表示预置审批环节成功，O表示审批终结
							);*/

						sql = "{call pkg_eap.p_NextAprove(?,?,?,?)}";
						cs = conn.prepareCall(sql);
						cs.setString(1, sqbh);
						cs.setString(2, "0");
						cs.setString(3, "00");
						cs.registerOutParameter(4, Types.VARCHAR);
						cs.executeUpdate();
						if ("0".equals(cs.getString(4))) {
							List<Object[]> list = new ArrayList<Object[]>();
							sql="select eal_org as xsh,pn.xsm(eal_org) xsm from ea_rslt_parallel where apply_id='"+sqbh+"' and  eap_code='"+obj[2]+"' and eal_code='"+obj[3]+"' and nvl(eal_rslt,0)=0 order by eal_org";
							ResultSet rs = conn.prepareStatement(sql).executeQuery();
							while (rs.next()) {
								String xsh = rs.getString("xsh");
								String xsm = rs.getString("xsm");

								sql="select c.jsh, c.jsm, pn.xsm(c.org_id) as js_xsm,c.org_id from urp_user_role a, urp_user b, code_jsb c where c.jsh = b.idnumber and a.user_id = b.id and b.enabled = '1' and c.zzztdm = '01' " +
										("0".equals(obj[5].toString())?"":" and pkg_others.f_OrgGovem(c.org_id,'"+xsh+"')='1' ")+
										" and exists(select 1 from ea_rslt_parallel m,ea_process_link n where m.eap_code=n.eap_code and m.eal_code=n.eal_code and n.eal_role=a.role_id " +
										("0".equals(obj[5].toString())?"":" and pkg_others.f_OrgGovem(c.org_id,m.eal_org)='1' ")+" and apply_id='"+sqbh+"' and m.eal_order="+obj[4]+")";
								ResultSet rs2 = conn.prepareStatement(sql).executeQuery();
								List<Object[]> jsList = new ArrayList<Object[]>();
								while (rs2.next()) {
									String jsh = rs2.getString("jsh");
									String jsm = rs2.getString("jsm");
									String js_xsm = rs2.getString("js_xsm");
									String js_xsh = rs2.getString("org_id");
									jsList.add(new Object[]{jsh,jsm,js_xsm,js_xsh});
								}
								list.add(new Object[]{xsh,xsm,obj[2],obj[3],jsList});
							}
							map.put("result", "ok");
							map.put("approvers", list);
						}else if ("O".equals(cs.getString(4))) {
							map.put("result", "ok");
							map.put("approvers", null);
						} else {
							map.put("result",cs.getString(4));
						}

					}else{
						map.put("result","当前申请数据中有些课程已申请过课程替代！");
					}

					conn.rollback();//出现异常进行回滚
				} catch (Exception e) {
					try {
						map.put("result","获取审批人数据失败！");
						conn.rollback();//出现异常进行回滚；
					} catch (SQLException e1) {
						e1.printStackTrace();
					}
					e.printStackTrace();
				} finally {
					try {
						conn.setAutoCommit(true);
					} catch (SQLException e) {
						e.printStackTrace();
					}
					CommonUtils.closeDB(conn, cs, ps);
				}

			}
		}else{
			map.put("result", "保存失败，申请数据有误！");
		}

		return map;
	}

	@Override
	public void doRevoke(HttpServletRequest request,String sqbh,String cxyy) {
		String xh = AuthUtil.getCurrentUser().getIdNumber();
		String ip = commonService.getRemoteHost(request);
		String cxsj = commonService.queryCurrentTimeMinssBySql();
		PxCsb csb = CommonUtils.queryPxCsbById("kctdsp", "qygzl");
		String approvalProcess=(csb!=null&&"0".equals(csb.getCsz()))?"0":"1";
		if ("0".equals(approvalProcess)) {
			String sql = "update cj_kctd_sqb set sqzt='-2',cxfs='0',cxr='"+xh+"',cxsj='"+cxsj+"',cxip='"+ip+"',cxyy='"+cxyy+"' where sqbh = '"+sqbh+"'";
			curriculumReplacementDao.executeNativeUpdate(sql);
		}else{
			String sql = "update cj_kctd_sqb set sqzt='-3',cxfs='0',cxr='"+xh+"',cxsj='"+cxsj+"',cxip='"+ip+"',cxyy='"+cxyy+"' where sqbh = '"+sqbh+"'";
			curriculumReplacementDao.executeNativeUpdate(sql);

			EaApplys eaApply = baseService.queryEntityById(EaApplys.class, sqbh);
			eaApply.setApply_status("-1");
			eaApply.setRollbackIp(ip);
			eaApply.setRollbackMode("0");
			eaApply.setRollback_dt(cxsj);
			eaApply.setRollbackReason(cxyy);
			eaApply.setRollbackUser(xh);
			curriculumReplacementDao.update(eaApply);

			sql = "update ea_rslt_parallel set eal_rslt='2' where apply_id = '"+sqbh+"' and eal_rslt='0'";
			curriculumReplacementDao.executeNativeUpdate(sql);
		}
	}


}