# 移动端UI设计系统规范

## 📋 设计系统概述
建立统一的移动端UI设计系统，确保所有手机端页面具有一致的视觉体验和交互模式。

## 🎨 设计原则

### 1. 移动优先 (Mobile First)
- 优先考虑移动端体验
- 从小屏幕向大屏幕扩展
- 触摸友好的交互设计

### 2. 内容为王 (Content First)
- 突出核心信息
- 减少视觉干扰
- 合理的信息层级

### 3. 一致性 (Consistency)
- 统一的视觉语言
- 一致的交互模式
- 标准化的组件库

### 4. 可访问性 (Accessibility)
- 足够的颜色对比度
- 合适的触摸目标大小
- 支持屏幕阅读器

## 🎯 色彩系统

### 主色调 (Primary Colors)
```css
:root {
  /* 主品牌色 - 教育蓝 */
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;
  
  /* 辅助色 */
  --secondary-color: #722ed1;
  --secondary-light: #9254de;
  --secondary-dark: #531dab;
}
```

### 功能色彩 (Functional Colors)
```css
:root {
  /* 成功色 */
  --success-color: #52c41a;
  --success-light: #73d13d;
  --success-dark: #389e0d;
  
  /* 警告色 */
  --warning-color: #faad14;
  --warning-light: #ffc53d;
  --warning-dark: #d48806;
  
  /* 错误色 */
  --error-color: #ff4d4f;
  --error-light: #ff7875;
  --error-dark: #cf1322;
  
  /* 信息色 */
  --info-color: #1890ff;
  --info-light: #40a9ff;
  --info-dark: #096dd9;
}
```

### 中性色彩 (Neutral Colors)
```css
:root {
  /* 文字色 */
  --text-primary: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.65);
  --text-disabled: rgba(0, 0, 0, 0.25);
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  
  /* 边框色 */
  --border-primary: #d9d9d9;
  --border-secondary: #f0f0f0;
  
  /* 分割线 */
  --divider-color: #f0f0f0;
}
```

### 深色模式支持
```css
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: rgba(255, 255, 255, 0.85);
    --text-secondary: rgba(255, 255, 255, 0.65);
    --text-disabled: rgba(255, 255, 255, 0.25);
    
    --bg-primary: #141414;
    --bg-secondary: #1f1f1f;
    --bg-tertiary: #262626;
    
    --border-primary: #434343;
    --border-secondary: #303030;
    
    --divider-color: #303030;
  }
}
```

## 📝 字体系统

### 字体族 (Font Family)
```css
:root {
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                         'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 
                         'Helvetica Neue', Helvetica, Arial, sans-serif;
  --font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', 
                      Menlo, Courier, monospace;
}
```

### 字体大小 (Font Size)
```css
:root {
  /* 标题字体 */
  --font-size-h1: 24px;  /* 主标题 */
  --font-size-h2: 20px;  /* 二级标题 */
  --font-size-h3: 18px;  /* 三级标题 */
  --font-size-h4: 16px;  /* 四级标题 */
  
  /* 正文字体 */
  --font-size-large: 16px;   /* 大号正文 */
  --font-size-base: 14px;    /* 基础正文 */
  --font-size-small: 12px;   /* 小号正文 */
  --font-size-mini: 10px;    /* 辅助信息 */
}
```

### 行高 (Line Height)
```css
:root {
  --line-height-tight: 1.2;
  --line-height-base: 1.5;
  --line-height-loose: 1.8;
}
```

## 📐 间距系统

### 基础间距单位
```css
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
}
```

### 组件间距
```css
:root {
  /* 内边距 */
  --padding-xs: var(--spacing-xs);
  --padding-sm: var(--spacing-sm);
  --padding-md: var(--spacing-md);
  --padding-lg: var(--spacing-lg);
  
  /* 外边距 */
  --margin-xs: var(--spacing-xs);
  --margin-sm: var(--spacing-sm);
  --margin-md: var(--spacing-md);
  --margin-lg: var(--spacing-lg);
}
```

## 🔘 组件规范

### 1. 按钮组件 (Button)
```css
.btn-mobile {
  min-height: 44px;  /* 触摸友好的最小高度 */
  padding: 12px 24px;
  border-radius: 6px;
  font-size: var(--font-size-base);
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}
```

### 2. 卡片组件 (Card)
```css
.card-mobile {
  background: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--margin-md);
  overflow: hidden;
}

.card-header {
  padding: var(--padding-md);
  border-bottom: 1px solid var(--divider-color);
  font-weight: 500;
}

.card-body {
  padding: var(--padding-md);
}

.card-footer {
  padding: var(--padding-md);
  border-top: 1px solid var(--divider-color);
  background: var(--bg-secondary);
}
```

### 3. 列表组件 (List)
```css
.list-mobile {
  background: var(--bg-primary);
  border-radius: 8px;
  overflow: hidden;
}

.list-item {
  padding: var(--padding-md);
  border-bottom: 1px solid var(--divider-color);
  display: flex;
  align-items: center;
  min-height: 56px;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  margin-bottom: 4px;
}

.list-item-subtitle {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}
```

### 4. 表单组件 (Form)
```css
.form-mobile {
  background: var(--bg-primary);
  border-radius: 8px;
  padding: var(--padding-md);
}

.form-group {
  margin-bottom: var(--margin-md);
}

.form-label {
  display: block;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  margin-bottom: var(--margin-sm);
  font-weight: 500;
}

.form-control {
  width: 100%;
  min-height: 44px;
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  font-size: var(--font-size-base);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}
```

### 5. 导航组件 (Navigation)
```css
.navbar-mobile {
  height: 56px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--divider-color);
  display: flex;
  align-items: center;
  padding: 0 var(--padding-md);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-back {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: var(--font-size-h4);
  font-weight: 500;
  color: var(--text-primary);
}

.navbar-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
```

## 📱 响应式断点

### 断点定义
```css
:root {
  --breakpoint-xs: 0px;      /* 超小屏幕 */
  --breakpoint-sm: 576px;    /* 小屏幕 */
  --breakpoint-md: 768px;    /* 中等屏幕 */
  --breakpoint-lg: 992px;    /* 大屏幕 */
  --breakpoint-xl: 1200px;   /* 超大屏幕 */
}
```

### 媒体查询
```css
/* 手机端 */
@media (max-width: 575.98px) {
  .container-mobile {
    padding: 0 var(--padding-sm);
  }
}

/* 平板端 */
@media (min-width: 576px) and (max-width: 767.98px) {
  .container-mobile {
    padding: 0 var(--padding-md);
  }
}

/* 桌面端 */
@media (min-width: 768px) {
  .container-mobile {
    max-width: 768px;
    margin: 0 auto;
    padding: 0 var(--padding-lg);
  }
}
```

## 🎭 动画系统

### 基础动画
```css
:root {
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* 淡入淡出 */
.fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all var(--transition-base);
}

/* 滑动效果 */
.slide-enter {
  transform: translateX(100%);
}

.slide-enter-active {
  transform: translateX(0);
  transition: transform var(--transition-base);
}
```

## 🔧 工具类

### 布局工具类
```css
/* Flexbox */
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.flex-1 { flex: 1; }

/* 间距 */
.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mt-sm { margin-top: var(--margin-sm); }
.mb-md { margin-bottom: var(--margin-md); }
.p-lg { padding: var(--padding-lg); }

/* 文本 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }

/* 显示/隐藏 */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
```

## 📋 组件使用示例

### 标准页面结构
```html
<div class="page-mobile">
  <!-- 导航栏 -->
  <nav class="navbar-mobile">
    <div class="navbar-back">
      <i class="icon-back"></i>
    </div>
    <div class="navbar-title">页面标题</div>
    <div class="navbar-action">
      <i class="icon-more"></i>
    </div>
  </nav>
  
  <!-- 内容区域 -->
  <div class="container-mobile">
    <!-- 卡片列表 -->
    <div class="card-mobile">
      <div class="card-header">
        <h3>卡片标题</h3>
      </div>
      <div class="card-body">
        <div class="list-mobile">
          <div class="list-item">
            <div class="list-item-content">
              <div class="list-item-title">列表项标题</div>
              <div class="list-item-subtitle">列表项副标题</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

这个设计系统为移动端页面提供了完整的视觉和交互规范，确保用户体验的一致性和专业性。
