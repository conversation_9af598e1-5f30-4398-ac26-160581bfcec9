<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache"%>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>课程替代</title>
    <%--zTree树css--%>
    <link rel="stylesheet" href="/css/zt/zTreeStyle.css" />
    <%--树js--%>
    <script type="text/javascript" src="/js/zt/jquery.ztree.core.min.js"></script>
    <script type="text/javascript" src="/js/zTree/jquery.ztree.excheck.js"></script>
    <script type="text/javascript" src="/js/customjs/urpalert.js"></script>

    <script type="text/javascript">
        var mobile = ${mobile};
        $(function() {
            var sfxyd = '${ywhdkzb.sfxyd}';
            if(sfxyd == "1"){
                tsxx();
            }
        });

        if(mobile){
            function selectCourses(kclx) {
                $("#query_info_form").find("input[name=kclx]").val(kclx);
                var mod = addslidersModel("select_course_model", "calc(100% - 12px)");
                var $this = $("#select_course_model");
                var wHeight = $(window).height();
                var css = "";
                css +=    "<h5 class=\"phone-header smaller lighter grey\">";
                css +=    "    <i class=\"ace-icon fa fa-times bigger-130 phone-header-left\" onclick='$(\"#select_course_model\").modal(\"hide\");'></i>";
                css +=    "    <span class=\"phone-header-center\">课程替代</span>";
                css +=    "    <i class=\"glyphicon glyphicon-search bigger-130 phone-header-right\" onclick=\"javascript:searchModel('" + kclx + "');\"></i>";
                css +=    "</h5>";
                css +=    "<div class=\"col-xs-12\">";
                css +=    "    <div class=\"widget-content widget-box\" id=\"pager_scroll\" style=\"max-height:"+(wHeight - 100)+"px;overflow: auto;\">";
                css +=    "        <table class=\"table table-striped table-bordered table-hover\">";
                css +=    "            <thead>";
                css +=    "                <tr>";
                css +=    "                    <th>选择</th>";
                css +=    "                    <th>课程</th>";
                css +=    "                    <th>课程属性</th>";
                css +=    "                    <th>成绩</th>";
                if(kclx == "fa_kc"){
                    css +="                    <th>所属课组</th>";
                }
                css +=    "                </tr>";
                css +=    "            </thead>";
                css +=    "            <tbody id=\"scoreintbody\" style=\"overflow: auto;\"></tbody>";
                css +=    "        </table>";
                css +=    "    </div>";
                css +=    "</div>";
                css +=    "<div style='border: 3px solid #6fb3e0; background: #6fb3e0; position: absolute; bottom: 0; width: 100%;' class='center'>";
                css +=    "    <button class='btn btn-round btn-xs btn-purple no-border' onclick='saveModelCourse();'>";
                css +=    "        <i class='fa fa-check'></i> 确定";
                css +=    "    </button>";
                css +=    "    <button class='btn btn-round btn-xs no-border' onclick='$(\"#select_course_model\").modal(\"hide\");'>";
                css +=    "        <i class='fa fa-times'></i> 关闭";
                css +=    "    </button>";
                css +=    "</div>";

                $this.find(".modal-content").html(css);
                $this.find(".modal-dialog").css("margin", "0px");
                $this.find(".modal-content").css("top", "0px");
                $this.find(".modal-content").css("height", wHeight);
                mod.modal({
                    backdrop: 'static',
                    keyboard: false
                }).on('hide.bs.modal', function () {
                    $("body").removeClass('modal-open');
                    $("body").css('padding-right', '0px');
                    mod.remove();
                    $('.modal-backdrop').remove();
                    return false;
                });
                studentScoreList();
            }

            function searchModel(kclx){
                if(kclx == "fa_kc"){
                    $("#query_info_form").find(".fa_kc").show();
                }else{
                    $("#query_info_form").find(".fa_kc").hide();
                }
                $("#select_course_model").css("z-index", "1000");
                $("#search-modal").find(".modal-dialog").css("width","calc(100% - 32px)");
                $("#search-modal").modal("show");
            }

            function closeSearchModel(type){
                $("#select_course_model").css("z-index", "1050");
                $("#search-modal").modal("hide");
                if(type == "2"){
                    studentScoreList();
                }
            }

            function saveModelCourse(){
                var kclx = $("#query_info_form").find("input[name=kclx]").val();
                var tdlx = $("input[name='tdlx']:checked").val();
                var tdlxsm = $("input[name='tdlx']:checked").next("span").text();
                if (!tdlx) {
                    urp.alert("请选择替代类型！");
                    return;
                }
                var kchList = [];
                var kcmList = [];
                $("#scoreintbody").find("tr").find("td:eq(0) input:checked").each(function() {
                    kchList.push($(this).closest("tr").find("td input[name=kch]").val());
                    kcmList.push($(this).closest("tr").find("td input[name=kch]").val() + "-" + $(this).closest("tr").find("td input[name=kcm]").val());
                });
                if (kchList && kchList.length > 0) {
                    if(kclx == "fa_kc"){
                        if(tdlx == "02"){
                            if (kchList.length < 2) {
                                urp.alert("当前替代类型为：" + tdlxsm + "，请至少选择两门课程！");
                                return;
                            }
                        }else{
                            if (kchList.length > 1) {
                                urp.alert("当前替代类型为：" + tdlxsm + "，只能选择一门课程！");
                                return;
                            }
                        }
                    }else{
                        if(tdlx == "03"){
                            if (kchList.length < 2) {
                                urp.alert("当前替代类型为：" + tdlxsm + "，请至少选择两门课程！");
                                return;
                            }
                        }else{
                            if (kchList.length > 1) {
                                urp.alert("当前替代类型为：" + tdlxsm + "，只能选择一门课程！");
                                return;
                            }
                        }
                    }
                } else {
                    urp.alert("请选择课程！");
                    return;
                }
                if(kclx == "fa_kc"){
                    $("#tdkc_div").html("<input name='tdkch' type='hidden' value='" + kchList.join(",") + "'>" + kcmList.join("<br>") + " <i class=\"ace-icon fa fa-pencil-square-o bigger-120 purple\" onclick=\"selectCourses('fa_kc');\"></i>");
                }else{
                    $("#kc_div").html("<input name='kch' type='hidden' value='" + kchList.join(",") + "'>" + kcmList.join("<br>") + " <i class=\"ace-icon fa fa-pencil-square-o bigger-120 purple\" onclick=\"selectCourses('jg_kc');\"></i>");
                }
                $("#select_course_model").modal("hide");
                if("${assigning}"=="1"){
                    var kch = $("#kc_div").find("input[name=kch]").val();
                    var tdkch = $("#tdkc_div").find("input[name=tdkch]").val();
                    if(kch && kch.split(",").length >0 && tdkch && tdkch.split(",").length >0){
                        queryApprovers(tdlx,kch,tdkch);
                    }
                }
            }

            function saveCourse(state){
                var tdlx = $("input[name='tdlx']:checked").val();
                var tdlxsm = $("input[name='tdlx']:checked").next("span").text();
                var list = [];
                var kch = $("#kc_div").find("input[name=kch]").val();
                var tdkch = $("#tdkc_div").find("input[name=tdkch]").val();
                var tdyym = "";
                var sqyy = "";
                if(kch && kch.split(",").length >0){
                    if(tdlx == "03"){
                        if (kch.split(",").length < 2) {
                            urp.alert("当前替代类型为：" + tdlxsm + "，请至少选择两门课程！");
                            return;
                        }
                    }else{
                        if (kch.split(",").length > 1) {
                            urp.alert("当前替代类型为：" + tdlxsm + "，只能选择一门课程！");
                            return;
                        }
                    }
                }else{
                    urp.alert("请选择课程！");
                    return;
                }

                if(tdkch && tdkch.split(",").length >0){
                    if(tdlx == "02"){
                        if (tdkch.split(",").length < 2) {
                            urp.alert("当前替代类型为：" + tdlxsm + "，请至少选择两门被替代课程！");
                            return;
                        }
                    }else{
                        if (tdkch.split(",").length > 1) {
                            urp.alert("当前替代类型为：" + tdlxsm + "，只能选择一门被替代课程！");
                            return;
                        }
                    }
                }else{
                    urp.alert("请选择被替代课程！");
                    return;
                }

                if("${cjtdyy}"=="1"){
                    tdyym = $("select[name=tdyym]").val();
                    if (tdyym == "") {
                        urp.alert("请选择替代原因！");
                        return;
                    }else{
                        var sgtx=$("select[name=tdyym]").find("option:selected").attr("sgtx");
                        if(sgtx == "1"){
                            sqyy = $("textarea[name=sqyy]").val();
                            if (sqyy == "") {
                                urp.alert("申请原因不能为空！");
                                return;
                            }
                        }
                    }
                }else{
                    sqyy = $("textarea[name=sqyy]").val();
                    if (sqyy == "") {
                        urp.alert("申请原因不能为空！");
                        return;
                    }
                }
                var ealUser = [];
                if("${assigning}"=="1"){
                    var msg="";
                    $("#approval_tbody").find("tr").each(function(){
                        var xsh=$(this).attr("xsh");
                        var xsm=$(this).attr("xsm");
                        var eap_code=$(this).attr("eap_code");
                        var eal_code=$(this).attr("eal_code");
                        var jsh = $(this).find("input[type=radio][name='choose"+xsh+"']:checked").val();
                        if (!jsh) {
                            msg+=xsm+"、";
                        }else{
                            ealUser.push(xsh+"_"+eap_code+"_"+eal_code+"_"+jsh);
                        }
                    });
                    if(msg){
                        urp.alert("请指定"+msg.substring(0,msg.length-1)+"审批人！");
                        return;
                    }
                }
                list.push({tdlx: tdlx,kch: kch,tdkch: tdkch,tdyym: tdyym,sqyy: sqyy,ealUser: ealUser.join(",")});
                if (list && list.length>0) {
                    var msg="提交后只能删除此项申请，不能修改课程的替代关系。是否确认提交?";
                    if("${schoolCode}"=="100010"){
                        msg="是否确认提交申请?";
                    }
                    urp.confirm(msg, function(f) {
                        if (f) {
                            saveCurriculum(list, state);
                        }
                    });
                }
            }

            function queryApprovers(tdlx,kcList,tdkcList) {
                var index;
                $.ajax({
                    url : "/student/personalManagement/personalApplication/curriculumReplacement/queryApprovers",
                    type : "post",
                    data : {
                        tdlx:tdlx,
                        kcList:kcList,
                        tdkcList:tdkcList
                    },
                    dataType : "json",
                    beforeSend: function () {
                        index = layer.load(0, {
                            shade: [0.2, "#000"] //0.1透明度的白色背景
                        });
                    },
                    complete : function() {
                        layer.close(index);
                    },
                    success : function(data) {
                        var data = data.data;
                        var tcont = "";
                        if(data.result == "ok"){
                            $("#approvers_title").html("【" + data.eal_name + "】");
                            if (data.approvers && data.approvers.length > 0) {
                                $.each(data.approvers,function(i,v){
                                    tcont += "<tr xsh='"+v[0]+"' xsm='"+v[1]+"' eap_code='"+v[2]+"' eal_code='"+v[3]+"'>";
                                    tcont += "<td>" + (v[1] == null ? "" : v[1]) + "</td>";
                                    tcont += "<td>";
                                    $.each(v[4],function(j,vv){
                                        tcont += "<label><input name='choose"+v[0]+"' type='radio' class='ace' value='"+vv[0]+"' " + (j == 0 ? "checked" : "") + "><span class='lbl'>"+vv[1]+"&nbsp;</span></label>";
                                    });
                                    tcont += "</td>";
                                    tcont += "</tr>";
                                });
                            }
                        }else{
                            urp.alert(data.result);
                        }
                        $("#approval_tbody").html(tcont);
                    },
                    error : function(xhr) {
                        urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    }
                });
            }

        }else{
            var onclickTreeNode = null;
            $(function() {
                resize();
                searchTree();
                studentScoreList();
                $(window.parent.document).find("#sidebar-collapse").bind("click",function(){
                    setTimeout('resize()',200);
                });
            });

            //当浏览器窗口大小改变时，设置显示内容的高度
            window.onresize = function () {
                resize();
            };

            function resize() {
                var winH = $(window).height()-20;
                var divTop = $("#tree_div").offset().top;
                var divTop2 = $("#trainprogram_tree_Div").offset().top;
                $("#pager_scroll").css("height", (winH - divTop - 100) / 2);
                $("#pager_scroll2").css("height", (winH - divTop - 100) / 2);
                //	$("#treeDemo").css("min-height", winH - divTop - 5);
                $("#tree_div").css("height", winH - divTop);
                $("#trainprogram_tree_Div").css("height", winH - divTop2-5);
            }

            /*以下是构建树的相关代码*/
            /*树的相关代码*/
            var firstAsyncSuccessFlag = "";
            function searchTree() {
                zTreeObj = $.fn.zTree.init($("#treeDemo"), setting);
                var zTree = $.fn.zTree.getZTreeObj("treeDemo");
                urp.keyDo("program_key", "treeDemo", "program_number");
                firstAsyncSuccessFlag = 0;
            }

            var setting = {
                view : {
                    nameIsHTML : true,
                    selectedMulti : true,
                    showIcon : false,
                    fontCss : setHighlight
                },
                check : {
                    enable : true,
                    nocheckInherit : false,
                    chkStyle : "checkbox",
                },
                edit : {
                    enable : true,
                    showRenameBtn : false,
                    showRemoveBtn : false,
                    drag : false
                },
                async : {
                    enable : true,
                    url : "/student/personalManagement/personalApplication/curriculumReplacement/getTreeNodes",
                    autoParam : [ "id", "name=n", "level=lv", "type", "parentIds" ],
                    otherParam : {
                        "fajhh" : function() {
                            var fajhh = $("#fajhh").val();
                            return fajhh;
                        }
                    },
                    dataFilter : filter
                },
                data : {
                    key : {
                        title : "title"
                    },
                    simpleData : {
                        enable : true
                    }
                },
                callback : {
                    onAsyncError : onAsyncError,//异步加载错误回调函数
                    onAsyncSuccess : onAsyncSuccess,//异步加载成功回调函数
                    onClick : onClick,//单击节点回调函数
                }
            };

            function onAsyncError(event, treeId, treeNode, XMLHttpRequest, textStatus, errorThrown) {
            }

            // 设置高亮字体
            function setHighlight(treeId, treeNode) {
                return (!!treeNode.highlight) ? {
                    color : "#ff7637",
                    "font-weight" : "bold"
                } : {
                    color : "#333",
                    "font-weight" : "normal"
                }
            }

            /*以下为默认展开第一层节点方法*/
            function onAsyncSuccess(event, treeId, treeNode, msg) {
                var zTree = $.fn.zTree.getZTreeObj("treeDemo");
                if (firstAsyncSuccessFlag == 0) {
                    try {
                        /*调用默认展开第一个结点*/
                        var selectedNode = zTree.getSelectedNodes();
                        var nodes = zTree.getNodes();
                        zTree.expandNode(nodes[0], true);
                        firstAsyncSuccessFlag = 1;
                    } catch (err) {
                    }
                } else {
                    if (treeNode != null && treeNode.isParent) {
                        zTree.expandNode(treeNode.children[0], true, true, true);
                    }
                }
            }

            function filter(treeId, parentNode, childNodes) {
                if (!childNodes)
                    return null;
                for ( var i = 0, l = childNodes.length; i < l; i++) {
                    childNodes[i].name = childNodes[i].name.replace(/\.n/g, '.');
                    if (childNodes[i].type != "kch") {//只有课程前面才显示checkbox
                        childNodes[i].nocheck = true;
                    }
                }
                return childNodes;
            }

            /*tree1节点点击*/
            function onClick(event, treeId, treeNode) {
                var tdlx = $("input[name='tdlx']:checked").val();
                if (tdlx == undefined) {
                    urp.alert("请选择替代类型！");
                    return;
                }
                var treeObj = $.fn.zTree.getZTreeObj("treeDemo");
                onclickTreeNode = treeNode;
                if (treeNode.type != "kch") {
                    urp.alert("请选择课程节点！");
                } else {
                    if (treeNode.checked) {
                        treeObj.checkNode(treeNode, false, true);
                    } else {
                        if (tdlx == "01" || tdlx == "03" || tdlx == "04") {//一替一、多替一、主辅修学分认定
                            treeObj.checkAllNodes(false);
                        }
                        treeObj.checkNode(treeNode, true, true);
                    }
                }
            }

            /*
             *
             * 两个数组取交集
             */
            function arrayIntersection(a, b) {
                var ai = 0, bi = 0;
                var result = new Array();
                while (ai < a.length && bi < b.length) {
                    if (a[ai] < b[bi]) {
                        ai++;
                    } else if (a[ai] > b[bi]) {
                        bi++;
                    } else /* they're equal */
                    {
                        result.push(a[ai]);
                        ai++;
                        bi++;
                    }
                }
                return result;
            }

            //将选择的替代关系添加至列表
            function saveSelect() {
                var tdlx = $("input[name='tdlx']:checked").val();
                var tdlxsm = $("input[name='tdlx']:checked").next("span").text();
                if (!tdlx) {
                    urp.alert("请选择替代类型！");
                    return;
                }
                var kcList = "";//当前选中右侧替代课程号
                var kcmList = "";//当前选中右侧替代课程名
                var a = 0;
                $("#scoreintbody").find("tr").find("td:eq(0) input:checked").each(function() {
                    kcList += $(this).closest("tr").find("td input[name=kch]").val() + ",";
                    kcmList += $(this).closest("tr").find("td input[name=kcm]").val() + ",";
                    a++;
                });
                if (kcList != "") {
                    if(tdlx == "03"){
                        if (a < 2) {
                            urp.alert("当前替代类型为：" + tdlxsm + "，请至少选择两门右侧的课程！");
                            return;
                        }
                    }else{
                        if (a > 1) {
                            urp.alert("当前替代类型为：" + tdlxsm + "，只能选择一门右侧的课程！");
                            return;
                        }
                    }
                } else {
                    urp.alert("请选择右侧课程！");
                    return;
                }
                var tdkcList = "";//当前选中的左侧课程号
                var tdkcmList = "";//当前选中的左侧课程名
                var zTree = $.fn.zTree.getZTreeObj("treeDemo");
                var selectedNodes = zTree.getCheckedNodes();
                for ( var i = 0; i < selectedNodes.length; i++) {
                    var type = selectedNodes[i].type;
                    if (type == "kch") {
                        var id = selectedNodes[i].id;
                        var param = selectedNodes[i].param;
                        tdkcList += id.split("_")[1] + ",";
                        tdkcmList += param + ",";
                    } else {
                        urp.alert("当前选中的节点包含非课程节点，请全部选择课程节点！");
                        return;
                    }
                }
                if (tdkcList != "") {
                    if (tdlx == "02") {
                        if (selectedNodes.length < 2) {
                            urp.alert("当前替代类型为：" + tdlxsm + "，请至少选择两门左侧的课程！");
                            return;
                        }
                    } else {
                        if (selectedNodes.length > 1) {
                            urp.alert("当前替代类型为：" + tdlxsm + "，只能选择一门左侧的课程！");
                            return;
                        }
                    }
                } else {
                    urp.alert("请点击左侧课程节点选择课程！");
                    return;
                }
                var tcont = "";
                tcont += "<tr>";
                tcont += "<td><input type='hidden' value='"+tdlx+"'/>";
                tcont += "    <a style='cursor:pointer;' class='red' title='删除' onclick='revokeInfo(this);'><i class='ace-icon fa fa-trash-o bigger-130'></i></a>";
                tcont += "</td>";
                tcont += "<td>" + kcList.replace(/\,/g, "<br>") + "</td>";
                tcont += "<td>" + kcmList.replace(/\,/g, "<br>") + "</td>";
                tcont += "<td>" + tdkcList.replace(/\,/g, "<br>") + "</td>";
                tcont += "<td>" + tdkcmList.replace(/\,/g, "<br>") + "</td>";
                tcont += "<td></td>";
                tcont += "</tr>";
                var kchlist = "";
                var tdkchlist = "";
                $("#scoreintbody2").find("tr").each(function() {
                    kchlist += $(this).find("td:eq(1)").html();
                    tdkchlist += $(this).find("td:eq(3)").html();
                });
                var kcResult = arrayIntersection(kcList.split(","), kchlist.split("<br>"));
                var tdkcResult = arrayIntersection(tdkcList.split(","), tdkchlist.split("<br>"));
                if (kcResult == "" && tdkcResult == "") {
                    if("${assigning}"=="1"){
                        $("#addHtml").html(tcont);
                        var url = "/student/personalManagement/personalApplication/curriculumReplacement/addSqyy?tdlx="+tdlx
                            +"&tdkcList="+tdkcList.substring(0, tdkcList.length - 1)+"&kcList="+kcList.substring(0, kcList.length - 1);
                        var modal = addslidersModel("sqyy_model", "60%");
                        modal.modal({
                            remote: url
                        }).on('hide.bs.modal', function () {
                            $("#addHtml").html("");
                            modal.remove();
                        });
                    }else{
                        addSqyy(tcont);
                    }
                } else {
                    urp.alert("当前所选课程已存在替代关系，请重新选择！");
                }
            }

            function addSqyy(tcont){
                var content="";
                if("${cjtdyy}"=="1"){

                    content+= "<div class=\"profile-user-info profile-user-info-striped self\">";

                    content+= "<div class=\"profile-info-row\" id=\"tdyy_div\">";
                    content+= "<div class=\"profile-info-name\">替代原因</div>";
                    content+= "<div class=\"profile-info-value\">";
                    content+= "<select id=\"tdyym\" name=\"tdyym\" onchange=\"changeTdyy(this);\">";
                    content+= $("#tdyyb").html();
                    content+= "</select>";
                    content+= "</div>";
                    content+= "</div>";

                    content+= "<div class=\"profile-info-row\" style=\"display: none;\" id=\"sqsm_div\">";
                    content+= "<div class=\"profile-info-name\">原因说明</div>";
                    content+= "<div class=\"profile-info-value\" id='sqsm'></div>";
                    content+= "</div>";

                    content+= "<div class=\"profile-info-row\" style=\"display: none;\" id=\"sqyy_div\">";
                    content+= "<div class=\"profile-info-name\">申请原因</div>";
                    content+= "<div class=\"profile-info-value\"><textarea maxlength='100' id='sqyy' style='height: 100px;' class='autosize-transition form-control'></textarea></div>";
                    content+= "</div>";

                    content+= "</div>";
                }else{
                    content="<textarea maxlength='100' id='sqyy' style='height: 300px;' class='autosize-transition form-control'></textarea>";
                }
                layer.open({
                    type : 1,
                    id : "sqyyDemo",
                    title : "申请原因",
                    shadeClose : true,
                    skin : 'yourclass',
                    content : content,
                    btnAlign : 'c',
                    area : [ '400px' ],
                    btn : [ '确定', '关闭' ],
                    btn1 : function(index, layero) {
                        var flag=true;
                        var tdyym ="";
                        var tdyy ="";
                        var sgtx ="0";
                        var sqyy ="";
                        if("${cjtdyy}"=="1"){
                            tdyym = $("#tdyym").val();
                            tdyy=$("#tdyym").find("option:selected").text();
                            if (tdyym == "") {
                                urp.alert("请选择替代原因！");
                                flag=false;
                            }else{
                                sgtx=$("#tdyym").find("option:selected").attr("sgtx");
                                if(sgtx=="1"){
                                    sqyy = $("#sqyy").val();
                                    if (sqyy == "") {
                                        urp.alert("申请原因不能为空！");
                                        flag=false;
                                    }
                                }

                            }
                        }else{
                            sqyy = $("#sqyy").val();
                            if (sqyy == "") {
                                urp.alert("申请原因不能为空！");
                                flag=false;
                            }
                        }
                        if(flag){
                            layer.close(index);
                            $("#scoreintbody2").append(tcont);
                            var sqyysm="<input type='hidden' value='"+tdyym+"'/><input type='hidden' value='"+sqyy+"'/><input type='hidden' value=''/>";
                            if("${cjtdyy}"=="1"){
                                sqyysm+=tdyy;
                                if(sgtx=="1"){
                                    sqyysm+="："+sqyy;
                                }
                            }else{
                                sqyysm+=sqyy;
                            }
                            $("#scoreintbody2").find("tr:last").find("td:eq(5)").html(sqyysm);
                            var zTree = $.fn.zTree.getZTreeObj("treeDemo");
                            zTree.checkAllNodes(false);
                            $("#scoreintbody").find("input[type=checkbox]").prop("checked",false);
                        }
                    }
                });
            }

            //移除当前替代关系
            function revokeInfo(obj) {
                $(obj).closest("tr").remove();
            }

            //保存当前所选的替代关系
            function saveAll(state) {
                var list = [];
                $("#scoreintbody2").find("tr").each(function() {
                    var tdlx = $(this).find("td:eq(0) input:eq(0)").val();
                    var kch = $(this).find("td:eq(1)").html().replace(/\<br>/g,",");
                    var tdkch = $(this).find("td:eq(3)").html().replace(/\<br>/g, ",");
                    var tdyym = $(this).find("td:eq(5) input:eq(0)").val();
                    var sqyy = $(this).find("td:eq(5) input:eq(1)").val();
                    var ealUser = $(this).find("td:eq(5) input:eq(2)").val();
                    list.push({tdlx: tdlx,kch: kch.substring(0, kch.length - 1),tdkch: tdkch.substring(0, tdkch.length - 1),tdyym: tdyym,sqyy: sqyy,ealUser: ealUser});
                });
                if (list.length>0) {
                    var msg="提交后只能删除此项申请，不能修改课程的替代关系。是否确认提交?";
                    if("${schoolCode}"=="100010"){
                        msg="是否确认提交申请?";
                    }
                    urp.confirm(msg, function(f) {
                        if (f) {
                            saveCurriculum(list, state);
                        }
                    });

                } else {
                    urp.alert("请先建立课程的替代关系！");
                }
            }

        }

        //保存当前所选的替代关系
        function saveCurriculum(list, state) {
            $.ajax({
                url : "/student/personalManagement/personalApplication/curriculumReplacement/index/saveCurriculumInfo?tokenValue=" + $("#tokenValue").val()+"&state="+state,
                type : "post",
                contentType: "application/json;charset=utf-8",
                data: JSON.stringify(list),
                dataType : "json",
                beforeSend : function() {
                    $("#loading-btn").attr("data-loading-text", "正在提交...");
                    $("#loading-btn").button('loading');
                },
                complete : function() {
                    $("#loading-btn").removeAttr("data-loading-text");
                    $("#loading-btn").button('reset');
                },
                success : function(data) {
                    if (data.status != 200) {
                        layer.alert(data.msg, {
                            icon : 5,
                            time : 3000
                        });
                    } else {
                        if (data.data["result"].indexOf("/") != -1) {
                            window.location.href = data.data["result"];
                        } else {
                            if (data.data["result"] == "ok") {
                                urp.alert("保存成功！");
                                location.href = "/student/personalManagement/personalApplication/curriculumReplacement/index";
                            }else{
                                urp.alert(data.data["result"]);
                            }
                        }
                    }
                    $("#tokenValue").val(data.data["token"]);
                },
                error : function(data) {
                    urp.alert("保存失败，请重新添加！");
                    $(".close").click();
                }
            });
        }

        function changeTdyy(obj){
            $("#sqsm_div").hide();
            $("#sqyy_div").hide();
            var tdyym=$(obj).val();
            if(tdyym!=""){
                var sm=$(obj).find("option:selected").attr("sm");
                var sgtx=$(obj).find("option:selected").attr("sgtx");
                if(sm!=""){
                    $("#sqsm").html(sm);
                    $("#sqsm_div").show();
                }
                if(sgtx=="1"){
                    $("#sqyy_div").show();
                }
            }
            if(!mobile){
                var h1=document.getElementById("tdyy_div").offsetHeight;
                var h2=document.getElementById("sqsm_div").offsetHeight;
                var h3=document.getElementById("sqyy_div").offsetHeight;
                $('#sqyyDemo').css("height", h1 + h2 + h3 + 5 + "px");
            }
        }

        function studentScoreList() {
            var kclx = $("#query_info_form").find("input[name=kclx]").val();
            var index;
            $.ajax({
                url : "/student/personalManagement/personalApplication/curriculumReplacement/index/queryCurriculumList",
                type : "post",
                data : $(document.query_info_form).serialize(),
                dataType : "json",
                beforeSend: function () {
                    index = layer.load(0, {
                        shade: [0.2, "#000"] //0.1透明度的白色背景
                    });
                },
                complete : function() {
                    layer.close(index);
                },
                success : function(data) {
                    var tcont = "";
                    if (data["studentScoreList"].length > 0) {
                        for ( var i = 0; i < data["studentScoreList"].length; i++) {
                            var v = data["studentScoreList"][i];
                            tcont += "<tr>";
                            tcont += "<td class='center'>";//可选
                            tcont += "<label><input name='choose' type='checkbox' class='ace'><span class='lbl'></span></label>";
                            tcont += "<input name='kch' type='hidden' value='"+v[0]+"'>";
                            tcont += "<input name='kcm' type='hidden' value='"+v[2]+"'>";
                            tcont += "</td>";//可选
                            if(mobile){
                                tcont += "<td>" + (v[0] == null ? "" : v[0]) + "<br>" + (v[2] == null ? "" : v[2]) + "</td>";
                                tcont += "<td>" + (v[5] == null ? "" : v[5]) + "<br>" + (v[3] == null ? "" : (v[3]+"学分")) + "</td>";
                                tcont += "<td>" + (v[1] == null ? "" : v[1]) + "</td>";
                                if(kclx == "fa_kc"){
                                    tcont += "<td>" + (v[6] == null ? "" : v[6]) + "</td>";
                                }
                            }else{
                                tcont += "<td>" + (v[0] == null ? "" : v[0]) + "</td>";
                                tcont += "<td>" + (v[2] == null ? "" : v[2]) + "</td>";
                                tcont += "<td>" + (v[5] == null ? "" : v[5]) + "</td>";
                                tcont += "<td>" + (v[1] == null ? "" : v[1]) + "</td>";
                                tcont += "<td>" + (v[3] == null ? "" : v[3]) + "</td>";
                            }
                            tcont += "</tr>";
                        }

                    }
                    $("#scoreintbody").html(tcont);
                },
                error : function(xhr) {
                    urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                }
            });
        }

        function addslidersModel(id, width) {
            var modal = '<div id="' + id + '" class="modal right fade" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" tabindex="-1">\
	            <div class="modal-dialog">\
	                <div class="modal-content">\
	                    <div class="center">\
	                           <img src="/img/icon/pageloading.gif" style="width:28px;height:28px;">\
	                       </div>\
	                </div>\
	            </div>\
	        </div>';
            var modal = $(modal).appendTo('body');
            $(".modal-dialog").css("width", width);
            return modal;
        }

        function tsxx() {
            var mod = addslidersModel("add_model", (mobile ? "calc(100% - 30px)" : "calc(60% - 30px)"));
            var $this = $("#add_model");
            var time = '${ywhdkzb.qzydms}';
            var ydnr = $("#ydnrstr").val().replace("＆nbsp;", "&nbsp;");
            var wHeight = $(window).height();
            $this.find(".modal-content").html("<div id='tsDiv'><pre class='col-xs-12' style='height:"+(wHeight - 130)+"px'>"	+ ydnr + "</pre></div>");
            $this.find(".modal-content").append("<div style='border: 3px solid #6fb3e0; background: #6fb3e0; position: absolute; bottom: 0; width: 100%;' class='center'>\
					<button class='btn btn-round btn-xs no-border' disabled id='tsxxClose' onclick='$(\"#add_model\").modal(\"hide\");'>\
					<i class='fa fa-times'></i> 关闭（"+time+"s）\
					</button>\
					</div>");

            $this.find(".modal-content").css("top", "30px");
            $this.find(".modal-content").css("height", wHeight - 120);
            mod.modal({
                backdrop: 'static',
                keyboard: false
            }).on('hide.bs.modal', function () {
                $("body").removeClass('modal-open');
                $("body").css('padding-right', '0px');
                mod.remove();
                $('.modal-backdrop').remove();
                return false;
            });

            timer = setInterval(function () {
                //console.log($("#tsxxClose").html());
                time--;
                if (time == 0) {
                    $(mod).find("#tsxxClose").html("<i class='fa fa-times'></i> 关闭");
                    $(mod).find("#tsxxClose").prop("disabled", false);
                    clearInterval(timer);
                } else {
                    $(mod).find("#tsxxClose").html("<i class='fa fa-times'></i> 关闭（" + time + "s）");
                }
            }, 1000);
        }

        function returnIndex(){
            window.location.href = "/student/personalManagement/personalApplication/curriculumReplacement/index";
        }

    </script>

    <style type="text/css">
        .line {
            border-bottom: 0px
        }
    </style>
</head>
<body>
<input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}" />
<textarea class="form-control" id="ydnrstr" name="ydnrstr" style="display: none;">${ywhdkzb.ydnrstr}</textarea>
<div class="row">
    <div class="self-margin col-xs-12">
        <c:if test="${mobile}">
            <h5 class="phone-header smaller lighter grey">
                <i class="ace-icon fa fa-chevron-left bigger-130 phone-header-left" onclick="returnIndex();"></i>
                <span class="phone-header-center">课程替代</span>
            </h5>
            <div class="phone-profile-info-row">
                <div class="phone-profile-info-name">
                    <span style="color: red">*</span>替代类型
                </div>
                <div class="phone-profile-info-value">
                    <cache:query var="tdlxb" region="code_tdlxb" where=" using_flag=1" />
                    <c:forEach items="${tdlxb}" var="tdlxb">
                        <label>
                            <input name="tdlx" type="radio" class="ace" value="${tdlxb.tdlx}"  <c:if test="${tdlxb.tdlx=='01'}">checked="checked"</c:if>>
                            <span class="lbl"> ${tdlxb.tdlxsm}</span>
                        </label>
                    </c:forEach>
                </div>
            </div>
            <div class="phone-profile-info-row">
                <div class="phone-profile-info-name">
                    <span style="color: red">*</span>课程
                </div>
                <div class="phone-profile-info-value" id="kc_div">
                    <i class="ace-icon fa fa-plus bigger-120 green" onclick="selectCourses('jg_kc');"></i>
                </div>
            </div>
            <div class="phone-profile-info-row">
                <div class="phone-profile-info-name">
                    <span style="color: red">*</span>被替代课程
                </div>
                <div class="phone-profile-info-value" id="tdkc_div">
                    <i class="ace-icon fa fa-plus bigger-120 green" onclick="selectCourses('fa_kc');"></i>
                </div>
            </div>
            <c:if test="${cjtdyy == '1'}">
                <div class="phone-profile-info-row">
                    <div class="phone-profile-info-name">
                        <span style="color: red">*</span>替代原因
                    </div>
                    <div class="phone-profile-info-value">
                        <cache:query var="tdyyb" region="cj_kctd_tdyyb" orderby="tdyym" />
                        <select name="tdyym" style="width: 100% !important;" onchange="changeTdyy(this);">
                            <option value="">--请选择--</option>
                            <c:forEach items="${tdyyb}" var="tdyyb">
                                <option value="${tdyyb.tdyym }" sgtx="${tdyyb.sgtx }" sm="${tdyyb.sm }">${tdyyb.tdyy }</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="phone-profile-info-row" style="display: none;" id="sqsm_div">
                    <div class="phone-profile-info-name">
                        <span style="color: red">*</span>原因说明
                    </div>
                    <div class="phone-profile-info-value" id="sqsm">
                    </div>
                </div>
            </c:if>
            <div class="phone-profile-info-row" style="${cjtdyy == '1' ? 'display: none;' : ''}" id="sqyy_div">
                <div class="phone-profile-info-name">
                    <span style="color: red">*</span>申请原因
                </div>
                <div class="phone-profile-info-value">
                    <textarea rows="5" style="width: 100%;" name="sqyy"></textarea>
                </div>
            </div>
            <c:if test="${assigning == '1'}">
                <div class="col-xs-12">
                    <div class="widget-box">
                        <div class="widget-header widget-header-flat">
                            <h4 class="widget-title lighter" style="font-size: 13px; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                                <i class="glyphicon glyphicon-list"></i> 指定<font id="approvers_title"></font>环节审批人
                            </h4>
                            <div class="widget-toolbar"><a data-action="collapse" href="#">
                                <i class="ace-icon fa fa-chevron-up"></i></a>
                            </div>
                        </div>
                        <div class="widget-body">
                            <div class="widget-main" style="padding: 0px;">
                                <div class="dialogs ace-scroll scroll-active">
                                    <div class="widget-content widget-box" id="approvers_div" style="max-height: calc(100vh - 345px);overflow: auto;">
                                        <table class="table table-bordered table-hover">
                                            <thead>
                                            <tr class="center">
                                                <th>院系</th>
                                                <th>教师</th>
                                            </tr>
                                            </thead>
                                            <tbody style="overflow: auto;" id="approval_tbody"></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
            <div class="col-xs-12">
                <button title="提交后不能修改" type="submit" id="loading-btn" class="btn btn-purple btn-xs btn-block" style="border-radius: 3px;" onclick="saveCourse('${approvalProcess}');">
                    <i class="ace-icon fa fa-check bigger-120"></i> 我已完成，提交审批
                </button>
            </div>
        </c:if>
        <c:if test="${!mobile}">
            <div class="col-sm-4" style="margin-top: -10px;">
                <h4 class="header smaller lighter grey">
                    <i class="glyphicon glyphicon-list"></i> 需要被替代的课程
                </h4>
                <div class="col-sm-12 widget-body widget-box widget-color-blue2" style="height: calc(100vh - 10px);" id="tree_div">
                    <div class="profile-user-info profile-user-info-striped self">
                        <div class="profile-info-row">
                            <div class="profile-info-name">培养方案</div>
                            <div class="profile-info-value">
                                <select class="selectwidth" onchange="searchTree();" style="width: 280px !important;" id="fajhh" name="fajhh">
                                    <c:forEach items="${listAll}" var="list">
                                        <option value="${list[0]}">
                                                ${list[1]}
                                            <c:if test="${list[2] == '00002'}">(辅修)</c:if>
                                            <c:if test="${list[2] == '00001'}">(主修)</c:if>
                                        </option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="profile-info-row">
                            <div class="profile-info-name">
                                <span style="color: red">*</span>替代类型
                            </div>
                            <div class="profile-info-value">
                                <cache:query var="tdlxb" region="code_tdlxb" where=" using_flag=1" />
                                <c:forEach items="${tdlxb}" var="tdlxb">
                                    <label>
                                        <input name="tdlx" type="radio" class="ace" value="${tdlxb.tdlx}"  <c:if test="${tdlxb.tdlx=='01'}">checked="checked"</c:if>>
                                        <span class="lbl"> ${tdlxb.tdlxsm}</span>
                                    </label>
                                </c:forEach>
                            </div>
                        </div>
                    </div>
                    <div style="margin:10px 0 0 10px;padding-right: 30px;">
                        <input type="text" id="program_key" class="form-control" placeholder="搜索..." style="border-radius: 5px!important;">
                        <div style="position: relative;top:-33px;right: 10px;float: right;">
                            <div class="col-sm-8" style="padding-top: 7px;margin-right: -15px;">
                                <label id="program_number"></label>
                            </div>
                            <div class="col-sm-4">
                                <i class="clickUp mouse_change glyphicon glyphicon-chevron-up"></i>
                                <i class="clickDown mouse_change glyphicon glyphicon-chevron-down"></i>
                            </div>
                        </div>
                    </div>
                    <div style="width: 100%;position: relative;top:-33px;margin-bottom: -30px">
                        <div id='trainprogram_tree_Div' style="height: calc(100vh - 15px);overflow: auto;width: 100%;float: right;">
                            <ul id="treeDemo" class="ztree"></ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-8" style="padding-right: 0px;margin-top: -10px;">
                <h4 class="header smaller lighter grey">
                    <i class="glyphicon glyphicon-list"></i>
                    <c:if test="${schoolCode=='100010' }">已修读通过且不在方案中的课程</c:if>
                    <c:if test="${schoolCode!='100010' }">已修读通过的课程</c:if>
                    <span class="right_top_oper">
                    <button type="button" class="btn btn-xs btn-round" title="返回" onclick="returnIndex();return false;">
                        <i class="fa fa-reply bigger-120"></i> 返回
                    </button>
                 </span>
                </h4>
                <form name="query_info_form" id="query_info_form">
                    <input type="hidden" name="kclx" value="jg_kc"/>
                    <div class="profile-user-info profile-user-info-striped self">
                        <div class="profile-info-row">
                            <div class="profile-info-name">课程号</div>
                            <div class="profile-info-value">
                                <input type="text" name="kch">
                            </div>
                            <div class="profile-info-name">课程名</div>
                            <div class="profile-info-value">
                                <input type="text" name="kcm">
                            </div>
                            <div class="profile-info-value">
                                <button class="btn btn-info btn-xs btn-round" onclick="studentScoreList();return false;">
                                    <i class="ace-con fa fa-search white bigger-120"></i> 查询
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="widget-content navbar-example" id="pager_scroll" style="margin-top: 6px;overflow: auto;">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>选择</th>
                            <th>课程号</th>
                            <th>课程名</th>
                            <th>课程属性</th>
                            <th>成绩</th>
                            <th>学分</th>
                        </tr>
                        </thead>
                        <tbody id="scoreintbody" style="overflow: auto;">
                        </tbody>
                    </table>
                    <!-- 页码开始 -->
                </div>
                <h4 class="header smaller lighter grey" style="padding-top: 6px;">
                    <i class="glyphicon glyphicon-list"></i> 已建立的替代关系
                    <span class="right_top_oper" style="margin-top: 6px;">
                    <button title="确认已选择课程的替代关系" class="btn btn-info btn-xs btn-round" onclick="saveSelect();">
                        <i class="ace-icon fa fa-chain bigger-120"></i> 建立替代关系
                    </button>
                    <button title="提交后不能修改" id="loading-btn" class="btn btn-purple btn-xs btn-round" onclick="saveAll('${approvalProcess}');">
                        <i class="ace-icon fa fa-check bigger-120"></i> 我已完成，提交审批
                    </button>
            </span>
                </h4>
                <div class="widget-content navbar-example" id="pager_scroll2" style="margin-top: 0px;overflow: auto;">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                        <tr>
                            <th width="6%">选择</th>
                            <th>课程号</th>
                            <th>课程名</th>
                            <th>被替代课程号</th>
                            <th>被替代课程名</th>
                            <th>申请原因</th>
                        </tr>
                        </thead>
                        <tbody id="scoreintbody2" style="overflow: auto;"></tbody>
                    </table>
                    <!-- 页码开始 -->
                </div>
            </div>
        </c:if>
    </div>
</div>
<c:if test="${mobile}">
    <div id="search-modal" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header no-padding">
                    <div class="table-header">
                        <button type="button" class="close" onclick="closeSearchModel('1');return false;" data-dismiss="modal" aria-hidden="true">
                            <span class="white">×</span>
                        </button>
                        <i class="glyphicon glyphicon-search"></i> 查询
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row" style="margin-top: 6px;">
                        <div class="col-sm-12 self-margin">
                            <form name="query_info_form" id="query_info_form">
                                <input type="hidden" name="kclx" value="jg_kc"/>
                                <div class="phone-profile-info-row fa_kc">
                                    <div class="phone-profile-info-name">培养方案</div>
                                    <div class="phone-profile-info-value">
                                        <select name="fajhh" class="phone-control-select">
                                            <c:forEach items="${listAll}" var="list">
                                                <option value="${list[0]}">
                                                        ${list[1]}<c:if test="${list[2] == '00002'}">(辅修)</c:if><c:if test="${list[2] == '00001'}">(主修)</c:if>
                                                </option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </div>
                                <div class="phone-profile-info-row">
                                    <div class="phone-profile-info-name">课程号</div>
                                    <div class="phone-profile-info-value">
                                        <input type="text" name="kch" placeholder="" class="phone-control-input">
                                    </div>
                                </div>
                                <div class="phone-profile-info-row">
                                    <div class="phone-profile-info-name">课程名</div>
                                    <div class="phone-profile-info-value">
                                        <input type="text" name="kcm" placeholder="" class="phone-control-input">
                                    </div>
                                </div>
                            </form>
                            <div class="row">
                                <div class="col-xs-6">
                                    <button class="btn btn-xs btn-default btn-block phone-btn-round" onclick="closeSearchModel('1');return false;"><i class="ace-icon fa fa-times"></i> 关闭</button>
                                </div>
                                <div class="col-xs-6">
                                    <button class="btn btn-xs btn-info btn-block phone-btn-round" onclick="closeSearchModel('2');return false;"><i class="glyphicon glyphicon-search"></i> 查询</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</c:if>
<div style="display: none;" id="addHtml"></div>
<cache:query var="tdyyb" region="cj_kctd_tdyyb" orderby="tdyym" />
<select id="tdyyb" name="tdyyb" style="display: none;">
    <option value="">--请选择--</option>
    <c:forEach items="${tdyyb}" var="tdyyb">
        <option value="${tdyyb.tdyym }" sgtx="${tdyyb.sgtx }" sm="${tdyyb.sm }">${tdyyb.tdyy }</option>
    </c:forEach>
</select>
</body>
</html>