<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的答辩</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 我的答辩页面样式 */
        .defense-header {
            background: linear-gradient(135deg, var(--info-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .defense-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .defense-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .defense-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .defense-tab {
            flex: 1;
            min-width: 120px;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
            border: 1px solid transparent;
        }
        
        .defense-tab.active {
            background: var(--info-color);
            color: white;
            border-color: var(--info-color);
        }
        
        .defense-tab:hover:not(.active) {
            background: var(--bg-secondary);
            border-color: var(--info-color);
        }
        
        .defense-content {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .defense-panel {
            display: none;
            padding: var(--padding-md);
        }
        
        .defense-panel.active {
            display: block;
        }
        
        .thesis-info {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            border-left: 4px solid var(--info-color);
        }
        
        .thesis-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            line-height: 1.4;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .info-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .info-item {
            background: var(--bg-primary);
            border-radius: 6px;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
        }
        
        .info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            line-height: 1.4;
            word-wrap: break-word;
        }
        
        .info-value.empty {
            color: var(--text-disabled);
            font-style: italic;
        }
        
        .defense-schedule {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .schedule-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--warning-dark);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .schedule-title i {
            color: var(--warning-color);
        }
        
        .schedule-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .schedule-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .schedule-label {
            font-size: var(--font-size-small);
            color: var(--warning-dark);
            font-weight: 500;
        }
        
        .schedule-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 600;
        }
        
        .defense-group {
            background: var(--success-light);
            border: 1px solid var(--success-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .group-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--success-dark);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .group-title i {
            color: var(--success-color);
        }
        
        .group-name {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }
        
        .group-members {
            background: var(--bg-primary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .qualification-review {
            background: var(--error-light);
            border: 1px solid var(--error-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
        }
        
        .review-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--error-dark);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .review-title i {
            color: var(--error-color);
        }
        
        .review-result {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .review-time {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        @media (max-width: 480px) {
            .info-row {
                grid-template-columns: 1fr;
            }
            
            .schedule-details {
                grid-template-columns: 1fr;
            }
            
            .defense-tab {
                min-width: 100px;
                font-size: var(--font-size-mini);
                padding: 6px 8px;
            }
        }
        
        @media (max-width: 360px) {
            .defense-tabs {
                flex-direction: column;
            }
            
            .defense-tab {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">我的答辩</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 答辩头部 -->
        <div class="defense-header">
            <div class="defense-title">我的答辩</div>
            <div class="defense-desc">查看论文答辩安排和详细信息</div>
        </div>
        
        <!-- 答辩标签页 -->
        <c:if test="${not empty xsxtfabs}">
            <div class="defense-tabs">
                <c:forEach items="${xsxtfabs}" var="xsxtfa" varStatus="i">
                    <div class="defense-tab ${i.first ? 'active' : ''}" 
                         data-target="defense_${xsxtfa.tmbh}" 
                         onclick="switchDefenseTab(this, 'defense_${xsxtfa.tmbh}');">
                        ${xsxtfa.pcmc}【${xsxtfa.famc}】${xsxtfa.dbxzlbmc}
                    </div>
                </c:forEach>
            </div>
        </c:if>
        
        <!-- 答辩内容 -->
        <div class="defense-content">
            <c:forEach items="${xsxtfabs}" var="xsxtfa" varStatus="i">
                <div id="defense_${xsxtfa.tmbh}" class="defense-panel ${i.first ? 'active' : ''}">
                    <!-- 论文基本信息 -->
                    <div class="thesis-info">
                        <div class="thesis-title">${xsxtfa.tmmc}</div>
                        
                        <div class="info-grid">
                            <div class="info-row">
                                <div class="info-item">
                                    <div class="info-label">方案年级</div>
                                    <div class="info-value">${xsxtfa.nj}</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">方案院系</div>
                                    <div class="info-value">${xsxtfa.xsm}</div>
                                </div>
                            </div>
                            
                            <div class="info-row">
                                <div class="info-item">
                                    <div class="info-label">方案专业</div>
                                    <div class="info-value">${xsxtfa.zym}</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">指导教师</div>
                                    <div class="info-value">${xsxtfa.jsm}</div>
                                </div>
                            </div>
                            
                            <c:if test="${not empty xsxtfa.jsm2nd}">
                                <div class="info-item">
                                    <div class="info-label">第二指导教师</div>
                                    <div class="info-value">${xsxtfa.jsm2nd}</div>
                                </div>
                            </c:if>
                        </div>
                    </div>
                    
                    <!-- 答辩资格审查 -->
                    <c:if test="${not empty xsxtfa.dbzgscjg or not empty xsxtfa.dbzgczsj}">
                        <div class="qualification-review">
                            <div class="review-title">
                                <i class="ace-icon fa fa-check-circle"></i>
                                答辩资格审查
                            </div>
                            <div class="review-result">${xsxtfa.dbzgscjg}</div>
                            <div class="review-time">审查时间：${xsxtfa.dbzgczsj}</div>
                        </div>
                    </c:if>
                    
                    <!-- 答辩安排 -->
                    <c:if test="${not empty xsxtfa.dbsj or not empty xsxtfa.dbdd}">
                        <div class="defense-schedule">
                            <div class="schedule-title">
                                <i class="ace-icon fa fa-calendar"></i>
                                答辩安排
                            </div>
                            <div class="schedule-details">
                                <div class="schedule-item">
                                    <div class="schedule-label">答辩时间</div>
                                    <div class="schedule-value">${xsxtfa.dbsj}</div>
                                </div>
                                <div class="schedule-item">
                                    <div class="schedule-label">答辩地点</div>
                                    <div class="schedule-value">${xsxtfa.dbdd}</div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                    
                    <!-- 答辩小组 -->
                    <c:if test="${not empty xsxtfa.dbxzmc}">
                        <div class="defense-group">
                            <div class="group-title">
                                <i class="ace-icon fa fa-users"></i>
                                答辩小组
                            </div>
                            <div class="group-name">${xsxtfa.dbxzmc}</div>
                            
                            <c:if test="${schoolCode == '100015' and not empty xsxtfa.dbcy}">
                                <div class="group-members">
                                    <strong>小组成员：</strong><br>
                                    ${xsxtfa.dbcy}
                                </div>
                            </c:if>
                        </div>
                    </c:if>
                </div>
            </c:forEach>
        </div>
        
        <!-- 空状态 -->
        <c:if test="${empty xsxtfabs}">
            <div class="empty-state">
                <i class="ace-icon fa fa-microphone"></i>
                <div>暂无答辩信息</div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换答辩标签页
        function switchDefenseTab(element, targetId) {
            // 移除所有活动状态
            $('.defense-tab').removeClass('active');
            $('.defense-panel').removeClass('active');
            
            // 设置当前活动状态
            $(element).addClass('active');
            $('#' + targetId).addClass('active');
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
