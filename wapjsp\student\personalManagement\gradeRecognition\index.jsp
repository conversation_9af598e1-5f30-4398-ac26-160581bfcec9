<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>成绩认定申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 成绩认定申请页面样式 */
        .recognition-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .recognition-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .type-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .type-item:last-child {
            border-bottom: none;
        }
        
        .type-item:active {
            background: var(--bg-color-active);
        }
        
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .type-icon.transfer {
            background: var(--success-color);
        }
        
        .type-icon.exchange {
            background: var(--info-color);
        }
        
        .type-icon.competition {
            background: var(--warning-color);
        }
        
        .type-icon.certificate {
            background: var(--error-color);
        }
        
        .type-icon.practice {
            background: var(--primary-color);
        }
        
        .type-content {
            flex: 1;
        }
        
        .type-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .type-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-limited {
            background: var(--warning-color);
            color: white;
        }
        
        .my-applications {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .applications-title {
            display: flex;
            align-items: center;
        }
        
        .applications-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .applications-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .application-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .application-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .application-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .application-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .course-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
        }
        
        .course-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .course-details {
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
        }
        
        .recognition-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .recognition-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .course-mapping {
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-md);
            background: var(--bg-tertiary);
        }
        
        .mapping-row {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .mapping-row:last-child {
            margin-bottom: 0;
        }
        
        .mapping-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            min-width: 80px;
        }
        
        .mapping-arrow {
            color: var(--primary-color);
            font-size: var(--font-size-base);
        }
        
        .form-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .form-upload:hover {
            border-color: var(--primary-color);
            background: var(--bg-tertiary);
        }
        
        .upload-icon {
            font-size: var(--font-size-h2);
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-list {
            margin-top: var(--margin-sm);
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }
        
        .file-item:last-child {
            margin-bottom: 0;
        }
        
        .file-icon {
            margin-right: var(--margin-sm);
            color: var(--primary-color);
        }
        
        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">成绩认定申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="recognition-header">
            <div class="header-title">成绩认定申请</div>
            <div class="header-subtitle">申请转学分、交换学习、竞赛等成绩认定</div>
        </div>

        <!-- 认定类型 -->
        <div class="recognition-types">
            <div class="type-item" onclick="showRecognitionForm('transfer')">
                <div class="type-icon transfer">
                    <i class="ace-icon fa fa-exchange"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">转学分认定</div>
                    <div class="type-desc">其他学校修读课程学分认定</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showRecognitionForm('exchange')">
                <div class="type-icon exchange">
                    <i class="ace-icon fa fa-globe"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">交换学习认定</div>
                    <div class="type-desc">国内外交换学习成绩认定</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showRecognitionForm('competition')">
                <div class="type-icon competition">
                    <i class="ace-icon fa fa-trophy"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">竞赛成绩认定</div>
                    <div class="type-desc">学科竞赛、创新创业等成绩认定</div>
                </div>
                <div class="type-status status-limited">有限制</div>
            </div>

            <div class="type-item" onclick="showRecognitionForm('certificate')">
                <div class="type-icon certificate">
                    <i class="ace-icon fa fa-certificate"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">证书认定</div>
                    <div class="type-desc">职业资格证书、技能证书认定</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showRecognitionForm('practice')">
                <div class="type-icon practice">
                    <i class="ace-icon fa fa-briefcase"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">实践成绩认定</div>
                    <div class="type-desc">社会实践、志愿服务等认定</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>
        </div>

        <!-- 我的申请 -->
        <div class="my-applications">
            <div class="applications-header">
                <div class="applications-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的申请</span>
                </div>
                <div class="applications-count" id="applicationsCount">0</div>
            </div>

            <div id="applicationsList">
                <!-- 申请列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-o"></i>
            <div id="emptyMessage">暂无认定申请记录</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 认定申请表单 -->
    <div class="recognition-form" id="recognitionForm">
        <div class="form-header">
            <div class="form-back" onclick="closeRecognitionForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">成绩认定申请</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">认定类型</div>
                    <input type="text" class="form-input" id="recognitionType" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业班级</div>
                    <input type="text" class="form-input" id="majorClass" readonly>
                </div>
            </div>

            <!-- 原始成绩信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span>原始成绩信息</span>
                </div>

                <div class="form-group" id="sourceSchoolGroup">
                    <div class="form-label required">来源学校/机构</div>
                    <input type="text" class="form-input" id="sourceSchool" placeholder="请输入学校或机构名称">
                </div>

                <div class="form-group">
                    <div class="form-label required">课程/项目名称</div>
                    <input type="text" class="form-input" id="sourceCourse" placeholder="请输入课程或项目名称">
                </div>

                <div class="form-group">
                    <div class="form-label required">获得成绩/等级</div>
                    <input type="text" class="form-input" id="sourceGrade" placeholder="请输入成绩或等级">
                </div>

                <div class="form-group">
                    <div class="form-label required">学分/学时</div>
                    <input type="number" class="form-input" id="sourceCredit" placeholder="请输入学分或学时数" step="0.5">
                </div>

                <div class="form-group">
                    <div class="form-label required">完成时间</div>
                    <input type="date" class="form-input" id="completionDate">
                </div>
            </div>

            <!-- 目标课程信息 -->
            <div class="form-section" id="targetCourseSection">
                <div class="section-title">
                    <i class="ace-icon fa fa-bullseye"></i>
                    <span>目标课程信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">目标课程</div>
                    <select class="form-input" id="targetCourse">
                        <option value="">请选择要认定的课程</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">课程映射关系</div>
                    <div class="course-mapping" id="courseMapping">
                        <div class="mapping-row">
                            <span class="mapping-label">原课程:</span>
                            <span id="sourceCourseDisplay">-</span>
                            <span class="mapping-arrow">→</span>
                            <span id="targetCourseDisplay">-</span>
                        </div>
                        <div class="mapping-row">
                            <span class="mapping-label">学分:</span>
                            <span id="sourceCreditDisplay">-</span>
                            <span class="mapping-arrow">→</span>
                            <span id="targetCreditDisplay">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 申请说明 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请说明</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">认定理由</div>
                    <textarea class="form-input form-textarea" id="recognitionReason"
                              placeholder="请详细说明申请认定的理由和依据..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">课程内容对比</div>
                    <textarea class="form-input form-textarea" id="contentComparison"
                              placeholder="请对比说明原课程与目标课程的内容相似性..."></textarea>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮箱地址</div>
                    <input type="email" class="form-input" id="email" placeholder="请输入邮箱地址">
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-paperclip"></i>
                    <span>证明材料</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">上传附件</div>
                    <div class="form-upload" onclick="selectFiles()">
                        <div class="upload-icon">
                            <i class="ace-icon fa fa-cloud-upload"></i>
                        </div>
                        <div class="upload-text">点击上传成绩单、证书等证明材料</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                    <div class="file-list" id="fileList">
                        <!-- 文件列表将动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeRecognitionForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitRecognition();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let myApplications = [];
        let currentRecognitionType = '';
        let uploadedFiles = [];
        let studentInfo = {};
        let availableCourses = [];

        $(function() {
            initPage();
            loadStudentInfo();
            loadMyApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFileUpload();
            bindFormEvents();
        }

        // 绑定文件上传
        function bindFileUpload() {
            $('#fileInput').change(function() {
                handleFileSelect(this.files);
            });
        }

        // 绑定表单事件
        function bindFormEvents() {
            $('#sourceCourse, #sourceCredit').on('input', updateCourseMapping);
            $('#targetCourse').change(updateCourseMapping);
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/personalManagement/gradeRecognition/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 加载我的申请
        function loadMyApplications() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/gradeRecognition/getMyApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myApplications = data.applications || [];
                    renderApplicationsList();
                    updateApplicationsCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载申请列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationsList() {
            const container = $('#applicationsList');
            container.empty();

            if (myApplications.length === 0) {
                showEmptyState('暂无认定申请记录');
                return;
            } else {
                hideEmptyState();
            }

            myApplications.forEach(application => {
                const applicationHtml = createApplicationItem(application);
                container.append(applicationHtml);
            });
        }

        // 创建申请项
        function createApplicationItem(application) {
            const statusClass = getStatusClass(application.status);
            const statusText = getStatusText(application.status);

            return `
                <div class="application-item ${statusClass}" onclick="showApplicationDetail('${application.id}')">
                    <div class="application-basic">
                        <div class="application-title">${getRecognitionTypeText(application.type)}</div>
                        <div class="application-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="application-details">
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(application.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>来源机构:</span>
                            <span>${application.sourceSchool}</span>
                        </div>
                        <div class="detail-item">
                            <span>原课程:</span>
                            <span>${application.sourceCourse}</span>
                        </div>
                        <div class="detail-item">
                            <span>目标课程:</span>
                            <span>${application.targetCourse || '-'}</span>
                        </div>
                    </div>
                    <div class="course-info">
                        <div class="course-title">认定信息</div>
                        <div class="course-details">
                            <span>学分: ${application.sourceCredit} → ${application.targetCredit || '-'}</span>
                            <span>成绩: ${application.sourceGrade}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'pending';
                case 'approved': return 'approved';
                case 'rejected': return 'rejected';
                default: return 'pending';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                default: return '未知';
            }
        }

        // 获取认定类型文本
        function getRecognitionTypeText(type) {
            switch(type) {
                case 'transfer': return '转学分认定';
                case 'exchange': return '交换学习认定';
                case 'competition': return '竞赛成绩认定';
                case 'certificate': return '证书认定';
                case 'practice': return '实践成绩认定';
                default: return '成绩认定';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示认定申请表单
        function showRecognitionForm(type) {
            currentRecognitionType = type;

            // 设置表单标题
            $('#formTitle').text(getRecognitionTypeText(type));
            $('#recognitionType').val(getRecognitionTypeText(type));

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 根据类型调整表单
            adjustFormByType(type);

            // 加载可选课程
            loadAvailableCourses(type);

            // 清空表单
            resetForm();

            // 显示表单
            $('#recognitionForm').addClass('show');
        }

        // 根据类型调整表单
        function adjustFormByType(type) {
            if (type === 'competition' || type === 'certificate' || type === 'practice') {
                $('#targetCourseSection').hide();
                $('#sourceSchoolGroup .form-label').text('获得机构/组织');
            } else {
                $('#targetCourseSection').show();
                $('#sourceSchoolGroup .form-label').text('来源学校/机构');
            }
        }

        // 加载可选课程
        function loadAvailableCourses(type) {
            $.ajax({
                url: "/student/personalManagement/gradeRecognition/getAvailableCourses",
                type: "post",
                data: { recognitionType: type },
                dataType: "json",
                success: function(data) {
                    availableCourses = data.courses || [];
                    renderCourseOptions();
                },
                error: function() {
                    console.log('加载课程列表失败');
                }
            });
        }

        // 渲染课程选项
        function renderCourseOptions() {
            const select = $('#targetCourse');
            select.find('option:not(:first)').remove();

            availableCourses.forEach(course => {
                select.append(`<option value="${course.id}" data-credit="${course.credit}">${course.name} (${course.credit}学分)</option>`);
            });
        }

        // 更新课程映射
        function updateCourseMapping() {
            const sourceCourse = $('#sourceCourse').val();
            const sourceCredit = $('#sourceCredit').val();
            const targetCourseId = $('#targetCourse').val();

            $('#sourceCourseDisplay').text(sourceCourse || '-');
            $('#sourceCreditDisplay').text(sourceCredit ? sourceCredit + '学分' : '-');

            if (targetCourseId) {
                const targetCourse = availableCourses.find(c => c.id === targetCourseId);
                if (targetCourse) {
                    $('#targetCourseDisplay').text(targetCourse.name);
                    $('#targetCreditDisplay').text(targetCourse.credit + '学分');
                }
            } else {
                $('#targetCourseDisplay').text('-');
                $('#targetCreditDisplay').text('-');
            }
        }

        // 重置表单
        function resetForm() {
            $('#sourceSchool').val('');
            $('#sourceCourse').val('');
            $('#sourceGrade').val('');
            $('#sourceCredit').val('');
            $('#completionDate').val('');
            $('#targetCourse').val('');
            $('#recognitionReason').val('');
            $('#contentComparison').val('');
            $('#contactPhone').val('');
            $('#email').val('');
            uploadedFiles = [];
            renderFileList();
            updateCourseMapping();
        }

        // 关闭认定申请表单
        function closeRecognitionForm() {
            $('#recognitionForm').removeClass('show');
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件选择
        function handleFileSelect(files) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (validateFile(file)) {
                    uploadedFiles.push({
                        id: Date.now() + i,
                        file: file,
                        name: file.name,
                        size: file.size
                    });
                }
            }
            renderFileList();
        }

        // 验证文件
        function validateFile(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['application/pdf', 'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'image/jpeg', 'image/jpg', 'image/png'];

            if (file.size > maxSize) {
                showError('文件大小不能超过10MB');
                return false;
            }

            if (!allowedTypes.includes(file.type)) {
                showError('只支持PDF、Word文档和图片格式');
                return false;
            }

            return true;
        }

        // 渲染文件列表
        function renderFileList() {
            const container = $('#fileList');
            container.empty();

            uploadedFiles.forEach(fileItem => {
                const fileHtml = `
                    <div class="file-item">
                        <i class="file-icon ace-icon fa fa-file"></i>
                        <span class="file-name">${fileItem.name}</span>
                        <i class="file-remove ace-icon fa fa-times" onclick="removeFile('${fileItem.id}')"></i>
                    </div>
                `;
                container.append(fileHtml);
            });
        }

        // 移除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id != fileId);
            renderFileList();
        }

        // 保存草稿
        function saveDraft() {
            if (!validateForm(false)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交认定申请
        function submitRecognition() {
            if (!validateForm(true)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = false;

            const message = `确定要提交${getRecognitionTypeText(currentRecognitionType)}吗？\n\n提交后将进入审核流程，请确保信息准确无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitFormData(formData, '认定申请提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    submitFormData(formData, '认定申请提交成功');
                }
            }
        }

        // 收集表单数据
        function collectFormData() {
            return {
                recognitionType: currentRecognitionType,
                sourceSchool: $('#sourceSchool').val(),
                sourceCourse: $('#sourceCourse').val(),
                sourceGrade: $('#sourceGrade').val(),
                sourceCredit: $('#sourceCredit').val(),
                completionDate: $('#completionDate').val(),
                targetCourse: $('#targetCourse').val(),
                recognitionReason: $('#recognitionReason').val(),
                contentComparison: $('#contentComparison').val(),
                contactPhone: $('#contactPhone').val(),
                email: $('#email').val(),
                attachments: uploadedFiles
            };
        }

        // 验证表单
        function validateForm(isSubmit) {
            if (!$('#sourceSchool').val().trim()) {
                showError('请填写来源学校/机构');
                return false;
            }

            if (!$('#sourceCourse').val().trim()) {
                showError('请填写课程/项目名称');
                return false;
            }

            if (!$('#sourceGrade').val().trim()) {
                showError('请填写获得成绩/等级');
                return false;
            }

            if (!$('#sourceCredit').val()) {
                showError('请填写学分/学时');
                return false;
            }

            if (!$('#completionDate').val()) {
                showError('请选择完成时间');
                return false;
            }

            if (currentRecognitionType === 'transfer' || currentRecognitionType === 'exchange') {
                if (!$('#targetCourse').val()) {
                    showError('请选择目标课程');
                    return false;
                }
            }

            if (!$('#recognitionReason').val().trim()) {
                showError('请填写认定理由');
                return false;
            }

            if (!$('#contactPhone').val().trim()) {
                showError('请填写联系电话');
                return false;
            }

            if (isSubmit && uploadedFiles.length === 0) {
                showError('请上传相关证明材料');
                return false;
            }

            return true;
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            $.ajax({
                url: "/student/personalManagement/gradeRecognition/submitRecognition",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        closeRecognitionForm();
                        loadMyApplications();
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示申请详情
        function showApplicationDetail(applicationId) {
            const application = myApplications.find(app => app.id === applicationId);
            if (!application) return;

            let message = `认定申请详情\n\n`;
            message += `认定类型：${getRecognitionTypeText(application.type)}\n`;
            message += `申请时间：${formatDate(application.createTime)}\n`;
            message += `来源机构：${application.sourceSchool}\n`;
            message += `原课程：${application.sourceCourse}\n`;
            message += `原成绩：${application.sourceGrade}\n`;
            message += `原学分：${application.sourceCredit}\n`;

            if (application.targetCourse) {
                message += `目标课程：${application.targetCourse}\n`;
                message += `目标学分：${application.targetCredit}\n`;
            }

            message += `认定理由：${application.recognitionReason}\n`;
            message += `当前状态：${getStatusText(application.status)}\n`;

            if (application.reviewComment) {
                message += `审核意见：${application.reviewComment}\n`;
            }

            if (application.reviewTime) {
                message += `审核时间：${formatDate(application.reviewTime)}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 更新申请数量
        function updateApplicationsCount() {
            $('#applicationsCount').text(myApplications.length);
        }

        // 刷新数据
        function refreshData() {
            loadMyApplications();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.my-applications').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.my-applications').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.my-applications').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#recognitionForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#recognitionForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeRecognitionForm();
            }
        });

        $('#recognitionForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
