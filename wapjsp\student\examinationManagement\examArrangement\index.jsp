<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>考试安排</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 考试安排页面样式 */
        .exam-calendar {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .calendar-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: var(--padding-md);
            text-align: center;
            font-weight: 500;
        }
        
        .exam-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            position: relative;
        }
        
        .exam-urgent {
            border-left-color: var(--error-color);
        }
        
        .exam-soon {
            border-left-color: var(--warning-color);
        }
        
        .exam-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-course {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .exam-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-upcoming {
            background: var(--info-color);
            color: white;
        }
        
        .status-soon {
            background: var(--warning-color);
            color: white;
        }
        
        .status-urgent {
            background: var(--error-color);
            color: white;
        }
        
        .status-finished {
            background: var(--text-disabled);
            color: white;
        }
        
        .exam-time {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .exam-date {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 2px;
        }
        
        .exam-period {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .exam-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .exam-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
        }
        
        .btn-exam {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-ticket {
            background: var(--success-color);
            color: white;
        }
        
        .btn-location {
            background: var(--info-color);
            color: white;
        }
        
        .btn-remind {
            background: var(--warning-color);
            color: white;
        }
        
        .countdown-timer {
            position: absolute;
            top: var(--padding-sm);
            right: var(--padding-sm);
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            color: var(--error-color);
            font-weight: 500;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .exam-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .summary-item {
            padding: var(--padding-sm);
        }
        
        .summary-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .summary-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .no-exam-notice {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .no-exam-icon {
            font-size: 48px;
            color: var(--text-disabled);
            margin-bottom: var(--margin-md);
        }
        
        .no-exam-text {
            color: var(--text-secondary);
            font-size: var(--font-size-base);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">考试安排</div>
            <div class="navbar-action" onclick="refreshExams();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 考试统计 -->
        <div class="exam-summary" id="examSummary" style="display: none;">
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-number" id="totalExams">0</div>
                    <div class="summary-label">总考试</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="upcomingExams">0</div>
                    <div class="summary-label">待考试</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="finishedExams">0</div>
                    <div class="summary-label">已完成</div>
                </div>
            </div>
        </div>
        
        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterExams('all')">全部</button>
                <button class="filter-chip" onclick="filterExams('upcoming')">待考试</button>
                <button class="filter-chip" onclick="filterExams('soon')">即将开始</button>
                <button class="filter-chip" onclick="filterExams('finished')">已完成</button>
            </div>
        </div>
        
        <!-- 考试列表 -->
        <div class="container-mobile">
            <div id="examList">
                <!-- 考试项将通过JavaScript动态填充 -->
            </div>
            
            <!-- 无考试提示 -->
            <div class="no-exam-notice" id="noExamNotice" style="display: none;">
                <div class="no-exam-icon">
                    <i class="ace-icon fa fa-calendar-check-o"></i>
                </div>
                <div class="no-exam-text">暂无考试安排</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <!-- 考试详情模态框 -->
    <div class="modal fade" id="examDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                    <h4 class="modal-title" id="examDetailTitle">考试详情</h4>
                </div>
                <div class="modal-body" id="examDetailBody">
                    <!-- 考试详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-mobile btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn-mobile btn-primary" id="printTicketBtn">打印准考证</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allExams = [];
        let filteredExams = [];
        let currentFilter = 'all';
        let countdownTimers = {};

        $(function() {
            initPage();
            loadExams();
            startCountdownUpdater();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载考试安排
        function loadExams() {
            showLoading(true);
            
            $.ajax({
                url: "/student/examinationManagement/examArrangement/getExams",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allExams = data || [];
                    updateStatistics();
                    applyFilter();
                    showLoading(false);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                    showLoading(false);
                }
            });
        }

        // 渲染考试列表
        function renderExams() {
            const container = $('#examList');
            container.empty();
            
            if (filteredExams.length === 0) {
                $('#noExamNotice').show();
                return;
            } else {
                $('#noExamNotice').hide();
            }

            filteredExams.forEach(function(exam, index) {
                const examHtml = createExamItem(exam, index);
                container.append(examHtml);
            });
            
            // 启动倒计时
            startCountdowns();
        }

        // 创建考试项HTML
        function createExamItem(exam, index) {
            const examStatus = getExamStatus(exam);
            const statusClass = getStatusClass(examStatus);
            const urgencyClass = getUrgencyClass(exam);
            
            const countdownHtml = examStatus === 'upcoming' || examStatus === 'soon' ? 
                `<div class="countdown-timer" id="countdown-${exam.id}"></div>` : '';
            
            return `
                <div class="exam-item ${urgencyClass}" onclick="showExamDetail('${exam.id}')">
                    ${countdownHtml}
                    <div class="exam-header">
                        <div class="exam-course">${exam.courseName}</div>
                        <div class="exam-status ${statusClass}">${getStatusText(examStatus)}</div>
                    </div>
                    <div class="exam-time">
                        <div class="exam-date">${formatDate(exam.examDate)}</div>
                        <div class="exam-period">${exam.startTime} - ${exam.endTime}</div>
                    </div>
                    <div class="exam-details">
                        <div class="exam-detail-item">
                            <span>考试地点:</span>
                            <span>${exam.location}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>座位号:</span>
                            <span>${exam.seatNumber || '待安排'}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>考试类型:</span>
                            <span>${exam.examType}</span>
                        </div>
                        <div class="exam-detail-item">
                            <span>监考老师:</span>
                            <span>${exam.invigilator}</span>
                        </div>
                    </div>
                    <div class="exam-actions">
                        <button class="btn-exam btn-location" onclick="event.stopPropagation(); showLocation('${exam.location}');">
                            <i class="ace-icon fa fa-map-marker"></i> 位置
                        </button>
                        ${examStatus !== 'finished' ? `
                            <button class="btn-exam btn-remind" onclick="event.stopPropagation(); setReminder('${exam.id}');">
                                <i class="ace-icon fa fa-bell"></i> 提醒
                            </button>
                            <button class="btn-exam btn-ticket" onclick="event.stopPropagation(); printTicket('${exam.id}');">
                                <i class="ace-icon fa fa-print"></i> 准考证
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 获取考试状态
        function getExamStatus(exam) {
            const now = new Date();
            const examDateTime = new Date(exam.examDate + ' ' + exam.startTime);
            const timeDiff = examDateTime.getTime() - now.getTime();
            const hoursDiff = timeDiff / (1000 * 60 * 60);
            
            if (timeDiff < 0) {
                return 'finished';
            } else if (hoursDiff <= 24) {
                return 'urgent';
            } else if (hoursDiff <= 72) {
                return 'soon';
            } else {
                return 'upcoming';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'upcoming': return 'status-upcoming';
                case 'soon': return 'status-soon';
                case 'urgent': return 'status-urgent';
                case 'finished': return 'status-finished';
                default: return 'status-upcoming';
            }
        }

        // 获取紧急程度样式类
        function getUrgencyClass(exam) {
            const status = getExamStatus(exam);
            switch(status) {
                case 'urgent': return 'exam-urgent';
                case 'soon': return 'exam-soon';
                default: return '';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'upcoming': return '待考试';
                case 'soon': return '即将开始';
                case 'urgent': return '紧急';
                case 'finished': return '已完成';
                default: return '待考试';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            
            if (date.toDateString() === today.toDateString()) {
                return '今天 ' + date.toLocaleDateString();
            } else if (date.toDateString() === tomorrow.toDateString()) {
                return '明天 ' + date.toLocaleDateString();
            } else {
                return date.toLocaleDateString();
            }
        }

        // 启动倒计时
        function startCountdowns() {
            // 清除之前的倒计时
            Object.values(countdownTimers).forEach(timer => clearInterval(timer));
            countdownTimers = {};
            
            filteredExams.forEach(function(exam) {
                const status = getExamStatus(exam);
                if (status === 'upcoming' || status === 'soon' || status === 'urgent') {
                    startCountdown(exam);
                }
            });
        }

        // 启动单个倒计时
        function startCountdown(exam) {
            const countdownElement = $(`#countdown-${exam.id}`);
            if (countdownElement.length === 0) return;
            
            const timer = setInterval(function() {
                const now = new Date();
                const examDateTime = new Date(exam.examDate + ' ' + exam.startTime);
                const timeDiff = examDateTime.getTime() - now.getTime();
                
                if (timeDiff <= 0) {
                    countdownElement.text('已开始');
                    clearInterval(timer);
                    delete countdownTimers[exam.id];
                } else {
                    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                    
                    if (days > 0) {
                        countdownElement.text(`${days}天${hours}时`);
                    } else if (hours > 0) {
                        countdownElement.text(`${hours}时${minutes}分`);
                    } else {
                        countdownElement.text(`${minutes}分钟`);
                    }
                }
            }, 60000); // 每分钟更新一次
            
            countdownTimers[exam.id] = timer;
            
            // 立即执行一次
            timer();
        }

        // 启动倒计时更新器
        function startCountdownUpdater() {
            setInterval(function() {
                startCountdowns();
            }, 300000); // 每5分钟重新计算一次
        }

        // 显示考试详情
        function showExamDetail(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;
            
            const detailHtml = `
                <div class="detail-item">
                    <div class="detail-label">课程名称</div>
                    <div class="detail-value">${exam.courseName}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">考试日期</div>
                    <div class="detail-value">${exam.examDate}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">考试时间</div>
                    <div class="detail-value">${exam.startTime} - ${exam.endTime}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">考试地点</div>
                    <div class="detail-value">${exam.location}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">座位号</div>
                    <div class="detail-value">${exam.seatNumber || '待安排'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">考试类型</div>
                    <div class="detail-value">${exam.examType}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">监考老师</div>
                    <div class="detail-value">${exam.invigilator}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">考试要求</div>
                    <div class="detail-value">${exam.requirements || '无特殊要求'}</div>
                </div>
            `;
            
            $('#examDetailTitle').text(exam.courseName + ' - 考试详情');
            $('#examDetailBody').html(detailHtml);
            
            $('#printTicketBtn').off('click').on('click', function() {
                $('#examDetailModal').modal('hide');
                printTicket(exam.id);
            });
            
            $('#examDetailModal').modal('show');
        }

        // 打印准考证
        function printTicket(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('确定要打印准考证吗？', function(confirmed) {
                    if (confirmed) {
                        window.open(`/student/examinationManagement/examArrangement/printTicket?examId=${examId}`);
                    }
                });
            } else {
                if (confirm('确定要打印准考证吗？')) {
                    window.open(`/student/examinationManagement/examArrangement/printTicket?examId=${examId}`);
                }
            }
        }

        // 显示位置
        function showLocation(location) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert('考试地点：' + location);
            } else {
                alert('考试地点：' + location);
            }
        }

        // 设置提醒
        function setReminder(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm('是否设置考试提醒？', function(confirmed) {
                    if (confirmed) {
                        // 这里可以调用设置提醒的接口
                        showSuccess('提醒设置成功');
                    }
                });
            } else {
                if (confirm('是否设置考试提醒？')) {
                    showSuccess('提醒设置成功');
                }
            }
        }

        // 筛选考试
        function filterExams(filter) {
            currentFilter = filter;
            
            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');
            
            applyFilter();
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'upcoming':
                    filteredExams = allExams.filter(exam => getExamStatus(exam) === 'upcoming');
                    break;
                case 'soon':
                    filteredExams = allExams.filter(exam => {
                        const status = getExamStatus(exam);
                        return status === 'soon' || status === 'urgent';
                    });
                    break;
                case 'finished':
                    filteredExams = allExams.filter(exam => getExamStatus(exam) === 'finished');
                    break;
                default:
                    filteredExams = allExams;
            }
            
            renderExams();
        }

        // 更新统计信息
        function updateStatistics() {
            const total = allExams.length;
            const upcoming = allExams.filter(exam => getExamStatus(exam) !== 'finished').length;
            const finished = allExams.filter(exam => getExamStatus(exam) === 'finished').length;
            
            $('#totalExams').text(total);
            $('#upcomingExams').text(upcoming);
            $('#finishedExams').text(finished);
            
            if (total > 0) {
                $('#examSummary').show();
            }
        }

        // 刷新考试安排
        function refreshExams() {
            loadExams();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#examList, #examSummary, .filter-section').hide();
            } else {
                $('#loadingState').hide();
                $('#examList, .filter-section').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 页面卸载时清除倒计时
        $(window).on('beforeunload', function() {
            Object.values(countdownTimers).forEach(timer => clearInterval(timer));
        });
    </script>
</body>
</html>
