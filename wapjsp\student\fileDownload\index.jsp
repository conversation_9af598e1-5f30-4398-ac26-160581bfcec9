<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>文件下载</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 文件下载页面样式 */
        .file-header {
            background: linear-gradient(135deg, var(--info-color), var(--info-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .file-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .file-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .file-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .list-header i {
            color: var(--info-color);
        }
        
        .file-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            transition: all var(--transition-base);
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-item:hover {
            background: var(--bg-tertiary);
        }
        
        .file-header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
            margin-bottom: var(--margin-sm);
        }
        
        .file-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .file-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            word-break: break-word;
        }
        
        .file-uploader {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-index {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            flex-shrink: 0;
        }
        
        .file-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin: var(--margin-sm) 0;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .detail-label {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
        }
        
        .download-btn {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-sm) var(--padding-md);
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .download-btn:hover {
            background: var(--success-dark);
        }
        
        .download-btn:active {
            transform: translateY(1px);
        }
        
        .file-remark {
            margin-top: var(--margin-sm);
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 4px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            padding: var(--padding-md);
            text-align: center;
        }
        
        .load-more-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md) var(--padding-lg);
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .load-more-button:hover {
            background: var(--primary-dark);
        }
        
        .load-more-button:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .file-header-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .file-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">文件下载</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 文件下载头部 -->
        <div class="file-header">
            <div class="file-title">文件下载</div>
            <div class="file-desc">下载教师上传的文件</div>
        </div>
        
        <!-- 文件列表 -->
        <div class="file-list" id="fileList" style="display: none;">
            <div class="list-header">
                <i class="ace-icon fa fa-download"></i>
                下载文件列表
            </div>
            
            <div id="fileItems">
                <!-- 动态加载文件项 -->
            </div>
            
            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <button class="load-more-button" id="loadMoreButton" onclick="loadMore();">
                    加载更多
                </button>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-o"></i>
            <div class="empty-state-text">暂无文件</div>
            <div class="empty-state-desc">当前没有可下载的文件</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let isLoading = false;

        $(function() {
            initPage();
            getScfjList(1, "30_sl", true);
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 分页查询
        function getScfjList(page, pageSizeVal, conditionChanged) {
            if (pageSizeVal == undefined) {
                pageSizeVal = "30_sl";
                page = "1";
            }

            const parr = (pageSizeVal + "").split("_");
            const pageSize = parseInt(parr[0]);

            showLoading(true);

            $.ajax({
                url: "/student/fileUpLoad/index/getTeacherPageList",
                cache: false,
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(d) {
                    const data = d.data;

                    if (data && data.records && data.records.length > 0) {
                        totalCount = data.pageContext.totalCount;
                        fillTable(data.records, false, page, pageSize);
                        showEmptyState(false);
                        showFileList(true);
                    } else {
                        fillTable(null, false, page, pageSize);
                        showEmptyState(true);
                        showFileList(false);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示数据
        function fillTable(data, isScroll, page, pageSize) {
            const container = $('#fileItems');
            let content = '';

            if (data != null && data.length > 0) {
                data.forEach(function(item, index) {
                    const tableId = isScroll ? (page - 1) * pageSize + 1 + index : index + 1;
                    content += createFileItem(item, tableId);
                });
            }

            if (isScroll) {
                container.append(content);
            } else {
                container.html(content);
            }
        }

        // 创建文件项HTML
        function createFileItem(item, index) {
            const fileName = item.FJM || '未知文件';
            const uploadTime = item.CZSJ || '-';
            const uploader = item.JSM || '-';
            const remark = item.BZ || '';

            return `
                <div class="file-item">
                    <div class="file-header-row">
                        <div class="file-info">
                            <div class="file-name">${fileName}</div>
                            <div class="file-uploader">上传人：${uploader}</div>
                        </div>
                        <div class="file-index">#${index}</div>
                    </div>

                    <div class="file-details">
                        <div class="detail-item">
                            <div class="detail-label">上传时间</div>
                            <div class="detail-value">${uploadTime}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">文件大小</div>
                            <div class="detail-value">-</div>
                        </div>
                    </div>

                    ${remark ? `
                        <div class="file-remark">
                            <strong>备注：</strong>${remark}
                        </div>
                    ` : ''}

                    <div class="file-actions">
                        <button class="download-btn" onclick="doDownload('${item.SCID}');">
                            <i class="ace-icon fa fa-download"></i>
                            下载文件
                        </button>
                    </div>
                </div>
            `;
        }

        // 下载文件
        function doDownload(id) {
            if (!id) {
                showError('文件ID不能为空');
                return;
            }

            // 直接跳转到下载链接
            location.href = "/student/fileUpLoad/index/dodownload/" + id;
        }

        // 加载更多
        function loadMore() {
            currentPage++;
            getScfjList(currentPage, pageSize + "_sl", false);
        }

        // 刷新数据
        function refreshData() {
            currentPage = 1;
            getScfjList(1, "30_sl", true);
        }

        // 显示文件列表
        function showFileList(show) {
            if (show) {
                $('#fileList').show();
            } else {
                $('#fileList').hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
            } else {
                $('#emptyState').hide();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            alert(message);
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
