<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实践管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实践管理页面样式 */
        .practice-header {
            background: linear-gradient(135deg, var(--success-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .practice-icon {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            opacity: 0.9;
        }
        
        .practice-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .practice-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin: var(--margin-sm) var(--margin-md);
        }
        
        .function-card {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .function-card:hover::before {
            left: 100%;
        }
        
        .function-card:hover {
            border-color: var(--success-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .function-card:active {
            transform: translateY(-2px);
        }
        
        .function-icon {
            font-size: 40px;
            margin-bottom: var(--margin-md);
            color: var(--success-color);
            position: relative;
            z-index: 1;
        }
        
        .function-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .function-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
            position: relative;
            z-index: 1;
        }
        
        .function-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: var(--warning-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: var(--font-size-mini);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }
        
        .practice-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--info-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-actions-title i {
            color: var(--warning-color);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-item {
            text-align: center;
            padding: var(--padding-sm);
            border-radius: 6px;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .action-item:hover {
            background: var(--bg-tertiary);
            transform: translateY(-2px);
        }
        
        .action-item-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--success-light);
            color: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto var(--margin-sm);
        }
        
        .action-item-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .recent-activities {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .activities-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .activities-title i {
            color: var(--error-color);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-sm);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .activity-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .activity-item:last-child {
            margin-bottom: 0;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: var(--success-light);
            color: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-right: var(--margin-sm);
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .activity-desc {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .activity-arrow {
            color: var(--text-disabled);
            font-size: 14px;
        }
        
        @media (max-width: 480px) {
            .function-grid {
                grid-template-columns: 1fr;
            }
            
            .action-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实践管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 实践管理头部 -->
        <div class="practice-header">
            <div class="practice-icon">
                <i class="ace-icon fa fa-cogs"></i>
            </div>
            <div class="practice-title">实践管理中心</div>
            <div class="practice-desc">实习申请、独立实践、过程管理</div>
        </div>
        
        <!-- 实践统计 -->
        <div class="practice-stats">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                实践统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalPractices">0</div>
                    <div class="stat-label">总实践数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="activePractices">0</div>
                    <div class="stat-label">进行中</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedPractices">0</div>
                    <div class="stat-label">已完成</div>
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="quick-actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                快速操作
            </div>
            <div class="action-grid">
                <div class="action-item" onclick="goToFunction('practice');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-list"></i>
                    </div>
                    <div class="action-item-title">实习科目</div>
                </div>
                <div class="action-item" onclick="goToFunction('independentPractice');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-user"></i>
                    </div>
                    <div class="action-item-title">独立实践</div>
                </div>
                <div class="action-item" onclick="goToFunction('practiceProgress');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-tasks"></i>
                    </div>
                    <div class="action-item-title">进度管理</div>
                </div>
                <div class="action-item" onclick="goToFunction('practiceReport');">
                    <div class="action-item-icon">
                        <i class="ace-icon fa fa-file-text"></i>
                    </div>
                    <div class="action-item-title">实践报告</div>
                </div>
            </div>
        </div>
        
        <!-- 功能模块 -->
        <div class="function-grid">
            <div class="function-card" onclick="goToFunction('practice');">
                <div class="function-badge">
                    <i class="ace-icon fa fa-star"></i>
                </div>
                <div class="function-icon">
                    <i class="ace-icon fa fa-list"></i>
                </div>
                <div class="function-title">可实习科目列表</div>
                <div class="function-desc">查看和申请实习科目</div>
            </div>
            
            <div class="function-card" onclick="goToFunction('independentPractice');">
                <div class="function-icon">
                    <i class="ace-icon fa fa-user"></i>
                </div>
                <div class="function-title">独立实践</div>
                <div class="function-desc">独立实践项目管理</div>
            </div>
        </div>
        
        <!-- 最近活动 -->
        <div class="recent-activities" id="recentActivities" style="display: none;">
            <div class="activities-title">
                <i class="ace-icon fa fa-clock-o"></i>
                最近活动
            </div>
            <div id="activitiesList">
                <!-- 动态加载活动内容 -->
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let practiceStats = {};

        $(function() {
            initPage();
            loadPracticeStats();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载实践统计
        function loadPracticeStats() {
            showLoading(true);

            // 获取实践统计信息
            $.ajax({
                url: "/student/practicing/getStats",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        practiceStats = data.data;
                        updateStats();
                        loadRecentActivities();
                    } else {
                        // 设置默认统计
                        updateStats({
                            totalPractices: 0,
                            activePractices: 0,
                            completedPractices: 0
                        });
                    }
                },
                error: function() {
                    console.log('获取实践统计失败');
                    // 设置默认统计
                    updateStats({
                        totalPractices: 0,
                        activePractices: 0,
                        completedPractices: 0
                    });
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新统计信息
        function updateStats(stats = practiceStats) {
            $('#totalPractices').text(stats.totalPractices || 0);
            $('#activePractices').text(stats.activePractices || 0);
            $('#completedPractices').text(stats.completedPractices || 0);
        }

        // 加载最近活动
        function loadRecentActivities() {
            $.ajax({
                url: "/student/practicing/getRecentActivities",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success && data.data && data.data.length > 0) {
                        renderRecentActivities(data.data);
                        $('#recentActivities').show();
                    }
                },
                error: function() {
                    console.log('获取最近活动失败');
                }
            });
        }

        // 渲染最近活动
        function renderRecentActivities(activities) {
            const container = $('#activitiesList');
            container.empty();

            activities.forEach(function(activity) {
                const activityHtml = `
                    <div class="activity-item" onclick="handleActivity('${activity.id}', '${activity.type}');">
                        <div class="activity-icon">
                            <i class="ace-icon fa fa-${getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.title}</div>
                            <div class="activity-desc">${activity.description}</div>
                        </div>
                        <i class="activity-arrow ace-icon fa fa-chevron-right"></i>
                    </div>
                `;
                container.append(activityHtml);
            });
        }

        // 获取活动图标
        function getActivityIcon(type) {
            switch(type) {
                case 'practice': return 'list';
                case 'independent': return 'user';
                case 'progress': return 'tasks';
                case 'report': return 'file-text';
                default: return 'cogs';
            }
        }

        // 处理活动点击
        function handleActivity(activityId, activityType) {
            // 根据活动类型跳转到相应页面
            switch(activityType) {
                case 'practice':
                    goToFunction('practice');
                    break;
                case 'independent':
                    goToFunction('independentPractice');
                    break;
                case 'progress':
                    goToFunction('practiceProgress');
                    break;
                case 'report':
                    goToFunction('practiceReport');
                    break;
                default:
                    showError('未知活动类型');
            }
        }

        // 跳转到功能页面
        function goToFunction(functionName) {
            let url = '';
            let title = '';

            switch(functionName) {
                case 'practice':
                    url = '/student/practicing/practice/index';
                    title = '可实习科目列表';
                    break;
                case 'independentPractice':
                    url = '/student/practicing/independentPractice/index';
                    title = '独立实践';
                    break;
                case 'practiceProgress':
                    showError('实践进度管理功能开发中');
                    return;
                case 'practiceReport':
                    showError('实践报告功能开发中');
                    return;
                default:
                    showError('功能暂未开放');
                    return;
            }

            if (parent && parent.addTab) {
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 刷新数据
        function refreshData() {
            loadPracticeStats();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
