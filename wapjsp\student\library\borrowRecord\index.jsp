<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>借阅记录</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 借阅记录页面样式 */
        .borrow-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .summary-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .borrow-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .borrow-overdue {
            border-left-color: var(--error-color);
        }
        
        .borrow-due-soon {
            border-left-color: var(--warning-color);
        }
        
        .borrow-returned {
            border-left-color: var(--success-color);
            opacity: 0.8;
        }
        
        .borrow-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .book-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .borrow-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-borrowed {
            background: var(--info-color);
            color: white;
        }
        
        .status-overdue {
            background: var(--error-color);
            color: white;
        }
        
        .status-due-soon {
            background: var(--warning-color);
            color: white;
        }
        
        .status-returned {
            background: var(--success-color);
            color: white;
        }
        
        .status-renewed {
            background: var(--primary-color);
            color: white;
        }
        
        .borrow-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .borrow-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .due-date-warning {
            background: rgba(255, 77, 79, 0.1);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--error-color);
            display: flex;
            align-items: center;
        }
        
        .due-date-warning i {
            margin-right: var(--margin-xs);
        }
        
        .borrow-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--margin-sm);
        }
        
        .borrow-location {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }
        
        .borrow-location i {
            margin-right: 4px;
        }
        
        .btn-borrow {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-renew {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-return {
            background: var(--success-color);
            color: white;
        }
        
        .btn-detail {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .btn-borrow:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .overdue-notice {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--error-color);
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--error-color);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .notice-title i {
            margin-right: var(--margin-xs);
        }
        
        .notice-content {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .countdown-timer {
            position: absolute;
            top: var(--padding-sm);
            right: var(--padding-sm);
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            color: var(--error-color);
            font-weight: 500;
        }
        
        .fine-info {
            background: rgba(255, 193, 7, 0.1);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--warning-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .fine-amount {
            font-weight: 500;
        }
        
        .btn-pay-fine {
            background: var(--warning-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">借阅记录</div>
            <div class="navbar-action" onclick="refreshRecords();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 借阅统计 -->
        <div class="borrow-summary">
            <div class="summary-title">借阅统计</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalBorrowed">0</div>
                    <div class="stat-label">总借阅</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="currentBorrowed">0</div>
                    <div class="stat-label">在借</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="overdueBooks">0</div>
                    <div class="stat-label">逾期</div>
                </div>
            </div>
        </div>
        
        <!-- 逾期提醒 -->
        <div class="overdue-notice" id="overdueNotice" style="display: none;">
            <div class="notice-title">
                <i class="ace-icon fa fa-exclamation-triangle"></i>
                <span>逾期提醒</span>
            </div>
            <div class="notice-content" id="overdueContent">
                您有图书逾期未还，请及时归还以免产生罚金。
            </div>
        </div>
        
        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterRecords('all')">全部</button>
                <button class="filter-chip" onclick="filterRecords('borrowed')">在借</button>
                <button class="filter-chip" onclick="filterRecords('overdue')">逾期</button>
                <button class="filter-chip" onclick="filterRecords('returned')">已还</button>
            </div>
        </div>
        
        <!-- 借阅记录列表 -->
        <div class="container-mobile">
            <div id="borrowList">
                <!-- 借阅记录将通过JavaScript动态填充 -->
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-book"></i>
                <div>暂无借阅记录</div>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allRecords = [];
        let filteredRecords = [];
        let currentFilter = 'all';

        $(function() {
            initPage();
            loadBorrowRecords();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载借阅记录
        function loadBorrowRecords() {
            showLoading(true);
            
            $.ajax({
                url: "/student/library/borrowRecord/getRecords",
                type: "post",
                dataType: "json",
                success: function(data) {
                    allRecords = data.records || [];
                    updateStatistics(data.statistics);
                    updateOverdueNotice(data.overdueInfo);
                    applyFilter();
                    showLoading(false);
                },
                error: function(xhr) {
                    showError("加载失败，请重试");
                    showLoading(false);
                }
            });
        }

        // 渲染借阅记录
        function renderRecords() {
            const container = $('#borrowList');
            container.empty();
            
            if (filteredRecords.length === 0) {
                $('#emptyState').show();
                return;
            } else {
                $('#emptyState').hide();
            }

            filteredRecords.forEach(function(record, index) {
                const recordHtml = createRecordItem(record, index);
                container.append(recordHtml);
            });
        }

        // 创建借阅记录项HTML
        function createRecordItem(record, index) {
            const status = getBorrowStatus(record);
            const statusClass = getStatusClass(status);
            const itemClass = getItemClass(status);
            
            let actionButtons = '';
            if (status === 'borrowed' || status === 'due-soon') {
                actionButtons = `
                    <button class="btn-borrow btn-detail" onclick="showRecordDetail('${record.id}')">详情</button>
                    <button class="btn-borrow btn-renew" onclick="renewBook('${record.id}')">续借</button>
                `;
            } else if (status === 'overdue') {
                actionButtons = `
                    <button class="btn-borrow btn-detail" onclick="showRecordDetail('${record.id}')">详情</button>
                    <button class="btn-borrow btn-return" onclick="returnBook('${record.id}')">归还</button>
                `;
            } else {
                actionButtons = `
                    <button class="btn-borrow btn-detail" onclick="showRecordDetail('${record.id}')">详情</button>
                `;
            }
            
            let warningHtml = '';
            if (status === 'overdue') {
                const overdueDays = Math.floor((new Date() - new Date(record.dueDate)) / (1000 * 60 * 60 * 24));
                warningHtml = `
                    <div class="due-date-warning">
                        <i class="ace-icon fa fa-warning"></i>
                        <span>已逾期 ${overdueDays} 天</span>
                    </div>
                `;
            } else if (status === 'due-soon') {
                const daysLeft = Math.ceil((new Date(record.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
                warningHtml = `
                    <div class="due-date-warning" style="color: var(--warning-color); background: rgba(255, 193, 7, 0.1);">
                        <i class="ace-icon fa fa-clock-o"></i>
                        <span>还有 ${daysLeft} 天到期</span>
                    </div>
                `;
            }
            
            let fineHtml = '';
            if (record.fine && record.fine > 0) {
                fineHtml = `
                    <div class="fine-info">
                        <span>罚金: <span class="fine-amount">¥${record.fine}</span></span>
                        <button class="btn-pay-fine" onclick="payFine('${record.id}')">缴费</button>
                    </div>
                `;
            }
            
            return `
                <div class="borrow-item ${itemClass}" style="position: relative;">
                    ${status === 'due-soon' ? `<div class="countdown-timer" id="countdown-${record.id}"></div>` : ''}
                    <div class="borrow-header">
                        <div class="book-title">${record.bookTitle}</div>
                        <div class="borrow-status ${statusClass}">${getStatusText(status)}</div>
                    </div>
                    <div class="borrow-details">
                        <div class="borrow-detail-item">
                            <span>作者:</span>
                            <span>${record.author}</span>
                        </div>
                        <div class="borrow-detail-item">
                            <span>借阅日期:</span>
                            <span>${record.borrowDate}</span>
                        </div>
                        <div class="borrow-detail-item">
                            <span>应还日期:</span>
                            <span>${record.dueDate}</span>
                        </div>
                        <div class="borrow-detail-item">
                            <span>续借次数:</span>
                            <span>${record.renewCount}/3</span>
                        </div>
                    </div>
                    ${warningHtml}
                    ${fineHtml}
                    <div class="borrow-actions">
                        <div class="borrow-location">
                            <i class="ace-icon fa fa-map-marker"></i>
                            <span>${record.location}</span>
                        </div>
                        <div>
                            ${actionButtons}
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取借阅状态
        function getBorrowStatus(record) {
            if (record.returnDate) {
                return 'returned';
            }
            
            const now = new Date();
            const dueDate = new Date(record.dueDate);
            const timeDiff = dueDate.getTime() - now.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
            
            if (daysDiff < 0) {
                return 'overdue';
            } else if (daysDiff <= 3) {
                return 'due-soon';
            } else {
                return 'borrowed';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'borrowed': return 'status-borrowed';
                case 'overdue': return 'status-overdue';
                case 'due-soon': return 'status-due-soon';
                case 'returned': return 'status-returned';
                case 'renewed': return 'status-renewed';
                default: return 'status-borrowed';
            }
        }

        // 获取项目样式类
        function getItemClass(status) {
            switch(status) {
                case 'overdue': return 'borrow-overdue';
                case 'due-soon': return 'borrow-due-soon';
                case 'returned': return 'borrow-returned';
                default: return '';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'borrowed': return '在借';
                case 'overdue': return '逾期';
                case 'due-soon': return '即将到期';
                case 'returned': return '已还';
                case 'renewed': return '已续借';
                default: return '在借';
            }
        }

        // 显示记录详情
        function showRecordDetail(recordId) {
            const record = allRecords.find(r => r.id === recordId);
            if (!record) return;
            
            let message = `书名：${record.bookTitle}\n`;
            message += `作者：${record.author}\n`;
            message += `ISBN：${record.isbn}\n`;
            message += `借阅日期：${record.borrowDate}\n`;
            message += `应还日期：${record.dueDate}\n`;
            message += `续借次数：${record.renewCount}/3\n`;
            message += `借阅地点：${record.location}\n`;
            
            if (record.returnDate) {
                message += `归还日期：${record.returnDate}\n`;
            }
            
            if (record.fine && record.fine > 0) {
                message += `罚金：¥${record.fine}\n`;
            }
            
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 续借图书
        function renewBook(recordId) {
            const record = allRecords.find(r => r.id === recordId);
            if (!record) return;
            
            if (record.renewCount >= 3) {
                showError('该图书已达到最大续借次数');
                return;
            }
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要续借《${record.bookTitle}》吗？`, function(confirmed) {
                    if (confirmed) {
                        doRenewBook(recordId);
                    }
                });
            } else {
                if (confirm(`确定要续借《${record.bookTitle}》吗？`)) {
                    doRenewBook(recordId);
                }
            }
        }

        // 执行续借
        function doRenewBook(recordId) {
            $.ajax({
                url: "/student/library/borrowRecord/renew",
                type: "post",
                data: { recordId: recordId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('续借成功');
                        loadBorrowRecords(); // 重新加载数据
                    } else {
                        showError(data.message || '续借失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 归还图书
        function returnBook(recordId) {
            const record = allRecords.find(r => r.id === recordId);
            if (!record) return;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要归还《${record.bookTitle}》吗？`, function(confirmed) {
                    if (confirmed) {
                        doReturnBook(recordId);
                    }
                });
            } else {
                if (confirm(`确定要归还《${record.bookTitle}》吗？`)) {
                    doReturnBook(recordId);
                }
            }
        }

        // 执行归还
        function doReturnBook(recordId) {
            $.ajax({
                url: "/student/library/borrowRecord/return",
                type: "post",
                data: { recordId: recordId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('归还成功');
                        loadBorrowRecords(); // 重新加载数据
                    } else {
                        showError(data.message || '归还失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 缴纳罚金
        function payFine(recordId) {
            const record = allRecords.find(r => r.id === recordId);
            if (!record) return;
            
            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(`确定要缴纳罚金 ¥${record.fine} 吗？`, function(confirmed) {
                    if (confirmed) {
                        doPayFine(recordId);
                    }
                });
            } else {
                if (confirm(`确定要缴纳罚金 ¥${record.fine} 吗？`)) {
                    doPayFine(recordId);
                }
            }
        }

        // 执行缴费
        function doPayFine(recordId) {
            $.ajax({
                url: "/student/library/borrowRecord/payFine",
                type: "post",
                data: { recordId: recordId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('缴费成功');
                        loadBorrowRecords(); // 重新加载数据
                    } else {
                        showError(data.message || '缴费失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 应用筛选
        function applyFilter() {
            switch(currentFilter) {
                case 'borrowed':
                    filteredRecords = allRecords.filter(record => !record.returnDate);
                    break;
                case 'overdue':
                    filteredRecords = allRecords.filter(record => getBorrowStatus(record) === 'overdue');
                    break;
                case 'returned':
                    filteredRecords = allRecords.filter(record => record.returnDate);
                    break;
                default:
                    filteredRecords = allRecords;
            }
            
            renderRecords();
        }

        // 筛选记录
        function filterRecords(filter) {
            currentFilter = filter;
            
            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');
            
            applyFilter();
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;
            
            $('#totalBorrowed').text(statistics.totalBorrowed || 0);
            $('#currentBorrowed').text(statistics.currentBorrowed || 0);
            $('#overdueBooks').text(statistics.overdueBooks || 0);
        }

        // 更新逾期提醒
        function updateOverdueNotice(overdueInfo) {
            if (overdueInfo && overdueInfo.count > 0) {
                $('#overdueContent').text(`您有 ${overdueInfo.count} 本图书逾期未还，总罚金 ¥${overdueInfo.totalFine}，请及时归还。`);
                $('#overdueNotice').show();
            } else {
                $('#overdueNotice').hide();
            }
        }

        // 刷新借阅记录
        function refreshRecords() {
            loadBorrowRecords();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#borrowList, .borrow-summary, .filter-section').hide();
            } else {
                $('#loadingState').hide();
                $('#borrowList, .borrow-summary, .filter-section').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.container-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
