package educationalAdministration.student.individualApplication.gradeChange.service;

import com.urpSoft.core.service.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-06-18 11:05
 * @Description
 * @Version v1.0
 */
public interface GradeChangeService extends IBaseService {
    Object[] querySqById(String sqbh);

    List<Object[]> queryFjById(String sqbh);

    Object[] queryFjKz();

    List<Object[]> queryXsCj();

    List<Object[]> queryResult(String sqbh);

    String saveInfo(HttpServletRequest request, String sqbh, String xnxq, String kch, String kxh, String sqyy,
                    String fjids, MultipartFile[] sqfj, String apply_status, String spjsh);

    String revokeApply(HttpServletRequest request, String sqbh);
}
