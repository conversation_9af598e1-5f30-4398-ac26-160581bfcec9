<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>教学评估</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 教学评估页面样式 */
        .evaluation-header {
            background: linear-gradient(135deg, var(--primary-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .evaluation-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .evaluation-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .evaluation-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .evaluation-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
        }
        
        .evaluation-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .evaluation-stats {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--info-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .evaluation-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            position: relative;
        }
        
        .evaluation-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .evaluation-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 600;
        }
        
        .evaluation-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .evaluation-title-info {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 var(--margin-sm);
            line-height: 1.4;
        }
        
        .evaluation-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            padding: 4px 0;
        }
        
        .detail-label {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .detail-value {
            color: var(--text-secondary);
        }
        
        .evaluation-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
            margin-top: var(--margin-sm);
        }
        
        .btn-action {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-evaluate {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-evaluate:hover {
            background: var(--primary-dark);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-view:hover {
            background: var(--info-dark);
        }
        
        .btn-edit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-edit:hover {
            background: var(--success-dark);
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .alert-info {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            font-size: var(--font-size-small);
            color: var(--info-dark);
            line-height: 1.6;
        }
        
        .alert-title {
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert-title i {
            color: var(--info-color);
        }
        
        @media (max-width: 480px) {
            .evaluation-details {
                grid-template-columns: 1fr;
            }
            
            .evaluation-header-info {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-sm);
            }
            
            .evaluation-actions {
                flex-wrap: wrap;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .evaluation-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">教学评估</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 教学评估头部 -->
        <div class="evaluation-header">
            <div class="evaluation-title">教学评估</div>
            <div class="evaluation-desc">参与课程教学质量评估</div>
        </div>
        
        <!-- 提示信息 -->
        <c:if test="${not empty msg}">
            <div class="alert-info">
                <div class="alert-title">
                    <i class="ace-icon fa fa-hand-o-right"></i>
                    提示信息
                </div>
                <div>${msg}</div>
            </div>
        </c:if>
        
        <!-- 评估标签页 -->
        <c:if test="${empty msg}">
            <div class="evaluation-tabs">
                <div class="evaluation-tab active" data-tab="ktjs" onclick="switchTab('ktjs', this);">
                    <c:choose>
                        <c:when test="${'100006' eq schoolcode}">课堂及时评教</c:when>
                        <c:otherwise>课堂教师</c:otherwise>
                    </c:choose>
                </div>
                <div class="evaluation-tab" data-tab="kt" onclick="switchTab('kt', this);">
                    <c:choose>
                        <c:when test="${'100006' eq schoolcode}">期末评教</c:when>
                        <c:otherwise>课堂</c:otherwise>
                    </c:choose>
                </div>
                <c:if test="${'100006' ne schoolcode}">
                    <div class="evaluation-tab" data-tab="bysdc" onclick="switchTab('bysdc', this);">
                        毕业生调查
                    </div>
                </c:if>
            </div>
            
            <!-- 评估统计 -->
            <div class="evaluation-stats">
                <div class="stats-title">
                    <i class="ace-icon fa fa-bar-chart"></i>
                    评估统计
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="totalEvaluations">0</div>
                        <div class="stat-label">总评估数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="completedEvaluations">0</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="pendingEvaluations">0</div>
                        <div class="stat-label">待评估</div>
                    </div>
                </div>
            </div>
            
            <!-- 评估记录列表 -->
            <div id="evaluationList">
                <!-- 动态加载评估记录 -->
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text"></i>
            <div>暂无评估记录</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let evaluationData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let tabType = "ktjs";
        let jxpgkg = "${jxpgkg.csz}"; // 绩效评估开关

        $(function() {
            initPage();
            
            // 检查是否是期末评估期间
            let isendperiod = "${isendperiod}";
            if (isendperiod) {
                switchTab('kt', $('.evaluation-tab[data-tab="kt"]')[0]);
            } else {
                loadEvaluationList(1, true);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换标签页
        function switchTab(tab, element) {
            // 移除所有活动状态
            $('.evaluation-tab').removeClass('active');

            // 设置当前活动状态
            $(element).addClass('active');

            // 更新当前标签类型
            tabType = tab;

            // 重新加载数据
            loadEvaluationList(1, true);
        }

        // 加载评估列表
        function loadEvaluationList(page, reset) {
            if (reset) {
                currentPage = 1;
                evaluationData = [];
                hasMore = true;
            }

            if (!hasMore) return;

            showLoading(true);

            $.ajax({
                url: "/student/teachingAssessment/evaluation/queryAll",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize + "&flag=" + tabType,
                dataType: "json",
                success: function(data) {
                    if (data && data.data && data.data.records) {
                        const records = data.data.records;
                        totalCount = data.data.pageContext ? data.data.pageContext.totalCount : 0;

                        if (reset) {
                            evaluationData = records;
                        } else {
                            evaluationData = evaluationData.concat(records);
                        }

                        hasMore = evaluationData.length < totalCount;
                        currentPage = page;

                        renderEvaluationList();
                        updateStats();
                    } else {
                        if (reset) {
                            showEmptyState();
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染评估列表
        function renderEvaluationList() {
            const container = $('#evaluationList');
            container.empty();

            if (evaluationData.length === 0) {
                showEmptyState();
                return;
            }

            hideEmptyState();

            evaluationData.forEach(function(item, index) {
                const evaluationHtml = `
                    <div class="evaluation-item">
                        <div class="evaluation-header-info">
                            <div class="evaluation-index">${index + 1}</div>
                            <div class="evaluation-title-info">${item.WJMC || '未知问卷'}</div>
                            <div class="evaluation-status ${item.SFPG == '1' ? 'status-completed' : 'status-pending'}">
                                ${item.SFPG == '1' ? '已评估' : '待评估'}
                            </div>
                        </div>

                        <div class="evaluation-details">
                            <div class="detail-item">
                                <span class="detail-label">被评人</span>
                                <span class="detail-value">${getEvaluatedPerson(item)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">评估内容</span>
                                <span class="detail-value">${item.KCM || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">课程号</span>
                                <span class="detail-value">${item.KCH || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">课序号</span>
                                <span class="detail-value">${item.KXH || '-'}</span>
                            </div>
                        </div>

                        <div class="evaluation-actions">
                            ${getActionButtons(item)}
                        </div>
                    </div>
                `;
                container.append(evaluationHtml);
            });
        }

        // 获取被评估人
        function getEvaluatedPerson(item) {
            if (tabType === 'ktjs') {
                return item.JSM || '-';
            } else if (tabType === 'kt') {
                return item.LSRXM || '-';
            } else {
                return item.JSM || item.LSRXM || '-';
            }
        }

        // 获取操作按钮
        function getActionButtons(item) {
            const yxdcpg = item.YXDCPG;
            const pglxdm = item.PGLXDM;
            const jkkg = item.JKKG;
            const jkpgts = item.JKPGTS;
            const jksj = item.JKRQ;

            let buttons = '';

            if (pglxdm == "02") { // 绩效评估
                if (yxdcpg == "1") { // 允许多次评估
                    buttons += `
                        <button class="btn-action btn-evaluate" onclick="evaluation('${item.KTID}', this);">
                            <i class="ace-icon fa fa-edit"></i>
                            <span>评估</span>
                        </button>
                    `;
                    if (item.SFPG == "1") {
                        buttons += `
                            <button class="btn-action btn-view" onclick="evaluationResult('${item.KTID}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看</span>
                            </button>
                        `;
                    }
                } else { // 一次评估
                    if (item.SFPG == "1") {
                        if (jxpgkg == "1" && "${jxpjxg.csz}" == '1') {
                            buttons += `
                                <button class="btn-action btn-edit" onclick="editEvaluationResult('${item.KTID}');">
                                    <i class="ace-icon fa fa-pencil"></i>
                                    <span>修改</span>
                                </button>
                            `;
                        }
                        buttons += `
                            <button class="btn-action btn-view" onclick="evaluationResult2('${item.KTID}', '${item.PGID}');">
                                <i class="ace-icon fa fa-eye"></i>
                                <span>查看</span>
                            </button>
                        `;
                    } else {
                        buttons += `
                            <button class="btn-action btn-evaluate" onclick="evaluation3('${item.KTID}', '${jkkg}', '${jkpgts}', '${jksj}', this);">
                                <i class="ace-icon fa fa-edit"></i>
                                <span>评估</span>
                            </button>
                        `;
                    }
                }
            } else if (pglxdm == "01") { // 过程评估
                if (item.SFPG == "1") {
                    buttons += `
                        <button class="btn-action btn-view" onclick="evaluationResult('${item.KTID}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                    `;
                } else {
                    buttons += `
                        <button class="btn-action btn-evaluate" onclick="evaluation2('${item.KTID}', '${item.WJBM}', '${item.KCH}', '${item.KXH}');">
                            <i class="ace-icon fa fa-edit"></i>
                            <span>评估</span>
                        </button>
                    `;
                }
            }

            return buttons;
        }

        // 评估操作
        function evaluation(ktid, element) {
            const url = "/student/teachingEvaluation/newEvaluation/evaluation?ktid=" + ktid;
            if (parent && parent.addTab) {
                parent.addTab("教学评估", url);
            } else {
                window.location.href = url;
            }
        }

        function evaluation2(ktid, wjbm, kch, kxh) {
            const url = "/student/teachingEvaluation/newEvaluation/evaluation?ktid=" + ktid + "&wjbm=" + wjbm + "&kch=" + kch + "&kxh=" + kxh;
            if (parent && parent.addTab) {
                parent.addTab("教学评估", url);
            } else {
                window.location.href = url;
            }
        }

        function evaluation3(ktid, jkkg, jkpgts, jksj, element) {
            const url = "/student/teachingEvaluation/newEvaluation/evaluation?ktid=" + ktid + "&jkkg=" + jkkg + "&jkpgts=" + jkpgts + "&jksj=" + jksj;
            if (parent && parent.addTab) {
                parent.addTab("教学评估", url);
            } else {
                window.location.href = url;
            }
        }

        // 查看评估结果
        function evaluationResult(ktid) {
            const url = "/student/teachingEvaluation/newEvaluation/lookEvaluation?ktid=" + ktid;
            if (parent && parent.addTab) {
                parent.addTab("查看评估", url);
            } else {
                window.location.href = url;
            }
        }

        function evaluationResult2(ktid, pgid) {
            const url = "/student/teachingEvaluation/newEvaluation/lookEvaluation?ktid=" + ktid + "&pgid=" + pgid;
            if (parent && parent.addTab) {
                parent.addTab("查看评估", url);
            } else {
                window.location.href = url;
            }
        }

        // 修改评估结果
        function editEvaluationResult(ktid) {
            const url = "/student/teachingEvaluation/newEvaluation/editEvaluationResult?ktid=" + ktid;
            if (parent && parent.addTab) {
                parent.addTab("修改评估", url);
            } else {
                window.location.href = url;
            }
        }

        // 更新统计信息
        function updateStats() {
            const total = evaluationData.length;
            const completed = evaluationData.filter(item => item.SFPG == '1').length;
            const pending = total - completed;

            $('#totalEvaluations').text(total);
            $('#completedEvaluations').text(completed);
            $('#pendingEvaluations').text(pending);
        }

        // 显示空状态
        function showEmptyState() {
            $('#emptyState').show();
            $('#evaluationList').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('#evaluationList').show();
        }

        // 刷新数据
        function refreshData() {
            loadEvaluationList(1, true);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 滚动加载更多
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                if (hasMore && !$('#loadingState').is(':visible')) {
                    loadEvaluationList(currentPage + 1, false);
                }
            }
        });
    </script>
</body>
</html>
