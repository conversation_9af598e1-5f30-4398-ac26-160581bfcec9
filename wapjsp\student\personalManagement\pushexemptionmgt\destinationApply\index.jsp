<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>推免去向申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 推免管理页面样式 */
        .exemption-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .exemption-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .exemption-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-warning {
            background: var(--warning-light);
            color: var(--warning-dark);
            border-left: 4px solid var(--warning-color);
        }
        
        .notice-info {
            background: var(--info-light);
            color: var(--info-dark);
            border-left: 4px solid var(--info-color);
        }
        
        .notice-section i {
            color: var(--warning-color);
            margin-right: 8px;
        }
        
        .time-info {
            background: var(--bg-secondary);
            padding: var(--padding-sm);
            border-radius: 6px;
            margin-top: var(--margin-sm);
            font-size: var(--font-size-mini);
        }
        
        .actions-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .actions-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-apply {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .applications-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .applications-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .applications-title i {
            color: var(--primary-color);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .application-content {
            flex: 1;
        }
        
        .application-school {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .application-id {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-submitted {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-reviewing {
            background: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-revoked {
            background: var(--text-disabled);
            color: white;
        }
        
        .result-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .result-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .result-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-operation {
            flex: 1;
            min-width: 60px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-revoke {
            background: var(--error-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .notice-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .notice-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .notice-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .notice-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .notice-body {
            padding: var(--padding-md);
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .notice-footer {
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-continue {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .btn-cancel {
            flex: 1;
            background: var(--text-disabled);
            color: white;
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .application-details {
                grid-template-columns: 1fr;
            }
            
            .operation-buttons {
                flex-direction: column;
            }
            
            .notice-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">推免去向申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 推免管理头部 -->
        <div class="exemption-header">
            <div class="exemption-title">推免去向申请</div>
            <div class="exemption-desc">管理推免去向申请</div>
        </div>
        
        <!-- 状态提示 -->
        <c:choose>
            <c:when test="${tmxs == 0}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-triangle"></i>
                    您没有通过推免，不能进行申请！
                </div>
            </c:when>
            <c:when test="${tmxs > 0 && count == 0}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-triangle"></i>
                    推免去向申请无审批流程，请联系管理员！
                </div>
            </c:when>
            <c:when test="${tmxs > 0 && count != 0}">
                <!-- 状态信息 -->
                <c:if test="${flag == 'nonparametric'}">
                    <div class="notice-section notice-warning">
                        <i class="ace-icon fa fa-exclamation-triangle"></i>
                        推免去向申参数未维护，请联系管理员处理
                    </div>
                </c:if>
                <c:if test="${flag == 'notenabled'}">
                    <div class="notice-section notice-warning">
                        <i class="ace-icon fa fa-exclamation-triangle"></i>
                        推免去向申未启用，请联系管理员处理
                    </div>
                </c:if>
                <c:if test="${flag == 'nottime'}">
                    <div class="notice-section notice-warning">
                        <i class="ace-icon fa fa-exclamation-triangle"></i>
                        不在可推免去向申时间范围或推免去向申开关关闭
                    </div>
                </c:if>
                <c:if test="${flag == 'showAdd'}">
                    <div class="notice-section notice-info">
                        <i class="ace-icon fa fa-info-circle"></i>
                        申请时间范围
                        <div class="time-info">
                            ${fn:substring(kzkg.kssj, 0, 4)}-${fn:substring(kzkg.kssj, 4, 6)}-${fn:substring(kzkg.kssj, 6, 8)} ${fn:substring(kzkg.kssj, 8, 10)}:${fn:substring(kzkg.kssj, 10, 12)}:${fn:substring(kzkg.kssj, 12, 14)}
                            ~
                            ${fn:substring(kzkg.jssj, 0, 4)}-${fn:substring(kzkg.jssj, 4, 6)}-${fn:substring(kzkg.jssj, 6, 8)} ${fn:substring(kzkg.jssj, 8, 10)}:${fn:substring(kzkg.jssj, 10, 12)}:${fn:substring(kzkg.jssj, 12, 14)}
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="actions-section">
                        <div class="actions-title">
                            <i class="ace-icon fa fa-cogs"></i>
                            操作
                        </div>
                        <div class="action-buttons">
                            <button class="btn-mobile btn-apply" onclick="addApplication();">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>推免去向申请</span>
                            </button>
                        </div>
                    </div>
                </c:if>
                
                <!-- 申请列表 -->
                <div class="applications-section">
                    <div class="applications-header">
                        <div class="applications-title">
                            <i class="ace-icon fa fa-list"></i>
                            推免去向申请列表
                        </div>
                    </div>
                    
                    <div id="applicationsList">
                        <!-- 动态加载申请列表 -->
                    </div>
                    
                    <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                        <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreApplications();">
                            <i class="ace-icon fa fa-plus"></i>
                            <span>加载更多</span>
                        </button>
                    </div>
                </div>
            </c:when>
        </c:choose>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无申请数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 申请需知模态框 -->
        <div class="notice-modal" id="noticeModal">
            <div class="notice-content">
                <div class="notice-header">
                    <div class="notice-title">申请需知</div>
                    <button class="notice-close" onclick="closeNoticeModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="notice-body" id="noticeBody">
                    <!-- 动态加载申请需知内容 -->
                </div>
                <div class="notice-footer">
                    <button class="btn-mobile btn-continue" id="continueBtn" disabled>
                        <i class="ace-icon fa fa-arrow-right"></i>
                        <span>继续</span>
                        <span id="countdown"></span>
                    </button>
                    <button class="btn-mobile btn-cancel" onclick="closeNoticeModal();">
                        <i class="ace-icon fa fa-times"></i>
                        <span>关闭</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let applicationData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let countdownInterval = null;

        $(function() {
            initPage();

            // 如果可以查看申请列表，加载数据
            if ('${tmxs}' > 0 && '${count}' != 0) {
                loadApplications(1, true);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载更多申请
        function loadMoreApplications() {
            if (hasMore) {
                loadApplications(currentPage + 1, false);
            }
        }

        // 加载申请数据
        function loadApplications(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/pushexemptionmgt/destinationApply/index/getApplyList",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            applicationData = data.records;
                        } else {
                            applicationData = applicationData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = applicationData.length < totalCount;

                        renderApplicationsList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            applicationData = [];
                            renderApplicationsList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationsList(reset = false) {
            const container = $('#applicationsList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : applicationData.length - pageSize;
            const endIndex = applicationData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (applicationData[i]) {
                    const itemHtml = createApplicationItem(applicationData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建申请项目HTML
        function createApplicationItem(item, index) {
            // 获取状态信息
            const statusInfo = getStatusInfo(item.APPLY_STATUS);
            const resultInfo = getResultInfo(item.EA_RSLT);

            // 构建操作按钮
            let operationButtons = '';
            if (item.APPLY_STATUS == 1 || item.APPLY_STATUS == 2 || item.APPLY_STATUS == 3 || item.APPLY_STATUS == -1) {
                operationButtons = `
                    <button class="btn-operation btn-view" onclick="viewApplication('${item.SQBH}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `;
            }
            if (item.APPLY_STATUS == 0) {
                operationButtons = `
                    <button class="btn-operation btn-edit" onclick="editApplication('${item.SQBH}', 1);">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                    <button class="btn-operation btn-revoke" onclick="revokeApplication('${item.SQBH}');">
                        <i class="ace-icon fa fa-reply"></i>
                        <span>撤回</span>
                    </button>
                `;
            }

            return `
                <div class="application-item">
                    <div class="application-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="application-index">${index + 1}</div>
                            <div class="application-content">
                                <div class="application-school">${item.JSYX || ''}</div>
                                <div class="application-id">申请编号：${item.SQBH || ''}</div>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                        </div>
                    </div>

                    <div class="application-details">
                        <div class="detail-item">
                            <span class="detail-label">接收专业</span>
                            <span>${item.JSZY || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">接收类别</span>
                            <span>${item.JSLBMC || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">学校类别1</span>
                            <span>${item.JSYXLBMC1 || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">学校类别2</span>
                            <span>${item.JSYXLBMC2 || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">审批结果</span>
                            <span>${resultInfo.text ? `<span class="result-badge ${resultInfo.class}">${resultInfo.text}</span>` : ''}</span>
                        </div>
                        <div class="detail-item" style="grid-column: 1 / -1;">
                            <span class="detail-label">备注</span>
                            <span title="${item.BZ || ''}">${item.BZ || ''}</span>
                        </div>
                    </div>

                    <div class="operation-buttons">
                        ${operationButtons}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(applyStatus) {
            switch (applyStatus) {
                case -2: return { text: '可申请', class: 'status-draft' };
                case -1: return { text: '撤销', class: 'status-revoked' };
                case 0: return { text: '待提交', class: 'status-draft' };
                case 1: return { text: '已提交', class: 'status-submitted' };
                case 2: return { text: '审批中', class: 'status-reviewing' };
                case 3: return { text: '审批结束', class: 'status-approved' };
                default: return { text: '', class: 'status-draft' };
            }
        }

        // 获取审批结果信息
        function getResultInfo(eaRslt) {
            switch (eaRslt) {
                case "0": return { text: '拒绝', class: 'result-rejected' };
                case "1": return { text: '批准', class: 'result-approved' };
                default: return { text: '', class: '' };
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && applicationData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (applicationData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.applications-section').hide();
            } else {
                $('#emptyState').hide();
                $('.applications-section').show();
            }
        }

        // 新增申请
        function addApplication() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/pushexemptionmgt/destinationApply/index/checkApply",
                type: "post",
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data.msg) {
                        showError(data.msg);
                    } else {
                        if (data.sfxyd === "1") {
                            showNoticeModal(data, '', 0);
                        } else {
                            openEditPage('', 0);
                        }
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 显示申请需知模态框
        function showNoticeModal(data, sqbh, type) {
            $('#noticeBody').html(data.ydnr);
            $('#noticeModal').fadeIn(300);

            let countdown = data.qzydms || 0;
            const continueBtn = $('#continueBtn');
            const countdownSpan = $('#countdown');

            if (countdown > 0) {
                continueBtn.prop('disabled', true);
                countdownSpan.text(`（${countdown}s）`);

                countdownInterval = setInterval(function() {
                    countdown--;
                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        countdownSpan.text('');
                        continueBtn.prop('disabled', false);
                        continueBtn.off('click').on('click', function() {
                            closeNoticeModal();
                            setTimeout(function() {
                                openEditPage(sqbh, type);
                            }, 500);
                        });
                    } else {
                        countdownSpan.text(`（${countdown}s）`);
                    }
                }, 1000);
            } else {
                continueBtn.prop('disabled', false);
                continueBtn.off('click').on('click', function() {
                    closeNoticeModal();
                    setTimeout(function() {
                        openEditPage(sqbh, type);
                    }, 500);
                });
            }
        }

        // 关闭申请需知模态框
        function closeNoticeModal() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            $('#noticeModal').fadeOut(300);
        }

        // 打开编辑页面
        function openEditPage(sqbh, type) {
            if (parent && parent.addTab) {
                parent.addTab('推免去向申请', '/student/personalManagement/pushexemptionmgt/destinationApply/index/addApply?sqbh=' + (sqbh || '') + '&type=' + type);
            } else {
                location.href = "/student/personalManagement/pushexemptionmgt/destinationApply/index/addApply?sqbh=" + (sqbh || "") + "&type=" + type;
            }
        }

        // 编辑申请
        function editApplication(sqbh, type) {
            openEditPage(sqbh, type);
        }

        // 查看申请
        function viewApplication(sqbh) {
            if (parent && parent.addTab) {
                parent.addTab('查看申请', '/student/application/index/seeInfo?applyId=' + sqbh + '&applyType=10031');
            } else {
                window.open("/student/application/index/seeInfo?applyId=" + sqbh + "&applyType=10031");
            }
        }

        // 撤回申请
        function revokeApplication(sqbh) {
            if (confirm("确定要撤回申请？")) {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/pushexemptionmgt/destinationApply/index/revokeApply",
                    type: "post",
                    data: "sqbh=" + sqbh + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        if (response.status != 200) {
                            showError(response.msg);
                        } else {
                            const data = response.data;
                            $("#tokenValue").val(data.token);

                            if (data.result.indexOf("/") != -1) {
                                showError("页面已过期，请刷新页面！");
                            } else if (data.result === "ok") {
                                showSuccess("撤销成功！", function() {
                                    loadApplications(1, true);
                                });
                            }
                        }
                    },
                    error: function(xhr) {
                        showError("撤销失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 刷新数据
        function refreshData() {
            if ('${tmxs}' > 0 && '${count}' != 0) {
                loadApplications(1, true);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.notice-modal', function(e) {
            if (e.target === this) {
                closeNoticeModal();
            }
        });
    </script>
</body>
</html>
