<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学科竞赛申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学科竞赛申请页面样式 */
        .competition-header {
            background: linear-gradient(135deg, var(--primary-color), var(--warning-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .competition-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .competition-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .applications-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .container-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-title i {
            color: var(--primary-color);
        }
        
        .btn-add-application {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-application:hover {
            background: var(--success-dark);
        }
        
        .applications-list {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }
        
        .application-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary-light);
            color: var(--primary-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .application-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .application-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .application-event {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .application-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 8px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .detail-item i {
            color: var(--primary-color);
            width: 12px;
        }
        
        .application-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-sm);
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            justify-content: center;
        }
        
        .action-btn.view {
            color: var(--info-color);
            border-color: var(--info-color);
        }
        
        .action-btn.view:hover {
            background: var(--info-light);
        }
        
        .action-btn.edit {
            color: var(--warning-color);
            border-color: var(--warning-color);
        }
        
        .action-btn.edit:hover {
            background: var(--warning-light);
        }
        
        .action-btn.delete {
            color: var(--error-color);
            border-color: var(--error-color);
        }
        
        .action-btn.delete:hover {
            background: var(--error-light);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-dark);
        }
        
        .btn-page.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .application-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .application-details {
                grid-template-columns: 1fr;
            }
            
            .application-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学科竞赛申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 学科竞赛申请头部 -->
        <div class="competition-header">
            <div class="competition-title">学科竞赛申请</div>
            <div class="competition-desc">管理您的学科竞赛申请</div>
        </div>
        
        <!-- 申请列表 -->
        <div class="applications-container">
            <div class="container-header">
                <div class="container-title">
                    <i class="ace-icon fa fa-list"></i>
                    申请列表
                </div>
                <button class="btn-add-application" onclick="addApplication();">
                    <i class="ace-icon fa fa-plus"></i>
                    申请学科竞赛
                </button>
            </div>
            
            <div class="applications-list" id="applicationsList">
                <!-- 动态加载申请列表 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;

        $(function() {
            initPage();
            loadApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载申请列表
        function loadApplications(page = 1) {
            currentPage = page;
            showLoading(true);

            $.ajax({
                url: "/student/subjectCompetition/selectXkjsSq",
                type: "post",
                data: "pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(data) {
                    if (data && data.data && data.data.records) {
                        renderApplications(data.data.records);
                        renderPagination(data.data.pageContext);
                    } else {
                        showEmptyState('applicationsList', '暂无学科竞赛申请记录');
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplications(applications) {
            const container = $('#applicationsList');

            if (!applications || applications.length === 0) {
                showEmptyState('applicationsList', '暂无学科竞赛申请记录');
                return;
            }

            let applicationsHtml = '';

            applications.forEach((application, index) => {
                const serialNumber = (currentPage - 1) * pageSize + 1 + index;
                const statusInfo = getStatusInfo(application.ZDJSSPZT);
                const canEdit = application.TJZT === "0";

                applicationsHtml += `
                    <div class="application-item">
                        <div class="application-header">
                            <div class="application-number">${serialNumber}</div>
                            <div class="application-info">
                                <div class="application-name">${application.JSMC || '未设置'}</div>
                                <div class="application-event">${application.SSJSMC || '未设置'}</div>
                            </div>
                            <div class="application-status ${statusInfo.class}">${statusInfo.text}</div>
                        </div>

                        <div class="application-details">
                            <div class="detail-item">
                                <i class="ace-icon fa fa-trophy"></i>
                                <span>${application.HJDJM || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-star"></i>
                                <span>${application.HJJBM || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-calendar"></i>
                                <span>${application.HJSJ || '未设置'}</span>
                            </div>
                            <div class="detail-item">
                                <i class="ace-icon fa fa-comment"></i>
                                <span>${application.ZDJSSPYJ || '暂无意见'}</span>
                            </div>
                        </div>

                        <div class="application-actions">
                            <button class="action-btn view" onclick="viewApplication('${application.SQID}');">
                                <i class="ace-icon fa fa-eye"></i>
                                查看
                            </button>
                            ${canEdit ? `
                                <button class="action-btn edit" onclick="editApplication('${application.SQID}');">
                                    <i class="ace-icon fa fa-edit"></i>
                                    修改
                                </button>
                                <button class="action-btn delete" onclick="deleteApplication('${application.SQID}');">
                                    <i class="ace-icon fa fa-trash"></i>
                                    删除
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            container.html(applicationsHtml);
        }

        // 获取状态信息
        function getStatusInfo(status) {
            if (!status || status === "" || status === "01") {
                return { text: "待审批", class: "status-pending" };
            } else if (status === "02") {
                return { text: "已批准", class: "status-approved" };
            } else {
                return { text: "未批准", class: "status-rejected" };
            }
        }

        // 显示空状态
        function showEmptyState(containerId, message) {
            const container = $('#' + containerId);
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-trophy"></i>
                    <div class="empty-state-text">${message}</div>
                    <div class="empty-state-desc">点击上方按钮申请学科竞赛</div>
                </div>
            `);
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            if (!pageContext || pageContext.totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="loadApplications(${currentPage - 1});">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<button class="btn-page" onclick="loadApplications(1);">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="loadApplications(${i});">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                paginationHtml += `<button class="btn-page" onclick="loadApplications(${totalPages});">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="loadApplications(${currentPage + 1});">下一页</button>`;

            container.html(paginationHtml);
            $('#paginationContainer').show();
        }

        // 添加申请
        function addApplication() {
            const url = "/student/subjectCompetition/to/add";

            if (parent && parent.addTab) {
                parent.addTab('申请学科竞赛', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看申请
        function viewApplication(sqid) {
            const url = `/student/subjectCompetition/views?sqid=${sqid}`;

            if (parent && parent.addTab) {
                parent.addTab('查看申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 编辑申请
        function editApplication(sqid) {
            const url = `/student/subjectCompetition/editSq?sqid=${sqid}`;

            if (parent && parent.addTab) {
                parent.addTab('修改申请', url);
            } else {
                window.location.href = url;
            }
        }

        // 删除申请
        function deleteApplication(sqid) {
            if (confirm('删除后不可恢复，是否确认删除？')) {
                showLoading(true);

                $.ajax({
                    url: "/student/subjectCompetition/delXkjsSq",
                    type: "post",
                    data: {
                        id: sqid,
                        tokenValue: $('#tokenValue').val()
                    },
                    dataType: "json",
                    success: function(data) {
                        if (data.status === 200) {
                            $('#tokenValue').val(data.data.token);

                            if (data.data.result.indexOf("/logout") !== -1) {
                                window.location.href = data.data.result;
                            } else if (data.data.result === "ok") {
                                showSuccess("删除成功！");
                                loadApplications(currentPage);
                            } else {
                                showError(data.data.result);
                            }
                        } else {
                            showError(data.msg);
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:删除失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 刷新数据
        function refreshData() {
            loadApplications(currentPage);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
