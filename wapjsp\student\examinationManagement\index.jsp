<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>考试管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 考试管理页面样式 */
        .exam-management-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .exam-management-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            padding: var(--padding-md);
            text-align: center;
            font-size: var(--font-size-h4);
            font-weight: 500;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            padding: var(--padding-md);
        }
        
        .function-card {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
            position: relative;
        }
        
        .function-card:hover {
            background: var(--bg-secondary);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .function-card:active {
            transform: translateY(0);
        }
        
        .function-icon {
            font-size: 32px;
            margin-bottom: var(--margin-sm);
            color: var(--primary-color);
        }
        
        .function-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .function-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        .function-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: var(--error-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: var(--font-size-mini);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .upcoming-exams {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .upcoming-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .upcoming-title i {
            color: var(--warning-color);
        }
        
        .exam-preview {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            border-left: 4px solid var(--primary-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .exam-preview:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .exam-preview:last-child {
            margin-bottom: 0;
        }
        
        .exam-preview-title {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .exam-preview-time {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .exam-preview-location {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            margin-top: 2px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-actions-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }
        
        .btn-action {
            flex: 1;
            min-width: 120px;
            padding: var(--padding-sm);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-primary-action {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-secondary-action {
            background: var(--secondary-color);
            color: white;
        }
        
        .btn-info-action {
            background: var(--info-color);
            color: white;
        }
        
        .btn-warning-action {
            background: var(--warning-color);
            color: white;
        }
        
        .stats-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-title i {
            color: var(--info-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: var(--font-size-h3);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        @media (max-width: 480px) {
            .function-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn-action {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">考试管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 考试统计 -->
        <div class="stats-section">
            <div class="stats-title">
                <i class="ace-icon fa fa-bar-chart"></i>
                考试统计
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalExams">0</div>
                    <div class="stat-label">总考试</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="upcomingExams">0</div>
                    <div class="stat-label">即将考试</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedExams">0</div>
                    <div class="stat-label">已完成</div>
                </div>
            </div>
        </div>
        
        <!-- 即将到来的考试 -->
        <div class="upcoming-exams" id="upcomingSection" style="display: none;">
            <div class="upcoming-title">
                <i class="ace-icon fa fa-clock-o"></i>
                即将到来的考试
            </div>
            <div id="upcomingList">
                <!-- 动态加载即将到来的考试 -->
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="quick-actions-title">
                <i class="ace-icon fa fa-bolt"></i>
                快速操作
            </div>
            <div class="action-buttons">
                <button class="btn-action btn-primary-action" onclick="goToExamPlan();">
                    <i class="ace-icon fa fa-calendar"></i>
                    <span>考试安排</span>
                </button>
                <button class="btn-action btn-secondary-action" onclick="goToExamGrade();">
                    <i class="ace-icon fa fa-list-alt"></i>
                    <span>考试成绩</span>
                </button>
                <button class="btn-action btn-info-action" onclick="goToPrintCertificate();">
                    <i class="ace-icon fa fa-print"></i>
                    <span>准考证</span>
                </button>
                <button class="btn-action btn-warning-action" onclick="goToExamSignUp();">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>考试报名</span>
                </button>
            </div>
        </div>
        
        <!-- 功能模块 -->
        <div class="exam-management-container">
            <div class="exam-management-header">考试功能</div>
            <div class="function-grid">
                <div class="function-card" onclick="goToFunction('examPlan');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-calendar-check-o"></i>
                    </div>
                    <div class="function-title">考试安排</div>
                    <div class="function-desc">查看本学期考试时间安排</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('examGrade');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-line-chart"></i>
                    </div>
                    <div class="function-title">考试成绩</div>
                    <div class="function-desc">查询历次考试成绩</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('printAdmissionCertificate');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-print"></i>
                    </div>
                    <div class="function-title">打印准考证</div>
                    <div class="function-desc">下载打印考试准考证</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('examSignUp');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-edit"></i>
                    </div>
                    <div class="function-title">考试报名</div>
                    <div class="function-desc">参加各类考试报名</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('examregistration');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-file-text-o"></i>
                    </div>
                    <div class="function-title">考试报名申请</div>
                    <div class="function-desc">提交考试报名申请</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('specialReTestApply');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-repeat"></i>
                    </div>
                    <div class="function-title">特殊重考申请</div>
                    <div class="function-desc">申请特殊情况重考</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('cet');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-language"></i>
                    </div>
                    <div class="function-title">英语等级考试</div>
                    <div class="function-desc">CET考试相关功能</div>
                </div>
                
                <div class="function-card" onclick="goToFunction('othersExamPlan');">
                    <div class="function-icon">
                        <i class="ace-icon fa fa-list"></i>
                    </div>
                    <div class="function-title">其他考试安排</div>
                    <div class="function-desc">查看其他类型考试</div>
                </div>
            </div>
        </div>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-calendar-times-o"></i>
            <div>暂无考试信息</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let examStats = {};
        let upcomingExamsList = [];

        $(function() {
            initPage();
            loadExamStats();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载考试统计
        function loadExamStats() {
            showLoading(true);

            // 获取考试统计信息
            $.ajax({
                url: "/student/examinationManagement/getStats",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success) {
                        updateStats(data.data);
                        loadUpcomingExams();
                    } else {
                        // 设置默认统计
                        updateStats({
                            totalExams: 0,
                            upcomingExams: 0,
                            completedExams: 0
                        });
                    }
                },
                error: function() {
                    console.log('获取考试统计失败');
                    // 设置默认统计
                    updateStats({
                        totalExams: 0,
                        upcomingExams: 0,
                        completedExams: 0
                    });
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 更新统计信息
        function updateStats(stats) {
            $('#totalExams').text(stats.totalExams || 0);
            $('#upcomingExams').text(stats.upcomingExams || 0);
            $('#completedExams').text(stats.completedExams || 0);
        }

        // 加载即将到来的考试
        function loadUpcomingExams() {
            $.ajax({
                url: "/student/examinationManagement/getUpcomingExams",
                type: "get",
                dataType: "json",
                success: function(data) {
                    if (data && data.success && data.data && data.data.length > 0) {
                        upcomingExamsList = data.data;
                        renderUpcomingExams();
                        $('#upcomingSection').show();
                    }
                },
                error: function() {
                    console.log('获取即将到来的考试失败');
                }
            });
        }

        // 渲染即将到来的考试
        function renderUpcomingExams() {
            const container = $('#upcomingList');
            container.empty();

            upcomingExamsList.forEach(function(exam) {
                const examHtml = `
                    <div class="exam-preview" onclick="viewExamDetail('${exam.id}');">
                        <div class="exam-preview-title">${exam.courseName || exam.examName}</div>
                        <div class="exam-preview-time">
                            <i class="ace-icon fa fa-clock-o"></i>
                            ${exam.examDate} ${exam.examTime}
                        </div>
                        <div class="exam-preview-location">
                            <i class="ace-icon fa fa-map-marker"></i>
                            ${exam.location || '地点待定'}
                        </div>
                    </div>
                `;
                container.append(examHtml);
            });
        }

        // 查看考试详情
        function viewExamDetail(examId) {
            if (parent && parent.addTab) {
                parent.addTab('考试详情', '/student/examinationManagement/examDetail?id=' + examId);
            } else {
                window.location.href = '/student/examinationManagement/examDetail?id=' + examId;
            }
        }

        // 跳转到功能页面
        function goToFunction(functionName) {
            let url = '';
            let title = '';

            switch(functionName) {
                case 'examPlan':
                    url = '/student/examinationManagement/examPlan/index';
                    title = '考试安排';
                    break;
                case 'examGrade':
                    url = '/student/examinationManagement/examGrade/index';
                    title = '考试成绩';
                    break;
                case 'printAdmissionCertificate':
                    url = '/student/examinationManagement/printAdmissionCertificate/index';
                    title = '打印准考证';
                    break;
                case 'examSignUp':
                    url = '/student/examinationManagement/examSignUp/index';
                    title = '考试报名';
                    break;
                case 'examregistration':
                    url = '/student/examinationManagement/examregistration/index';
                    title = '考试报名申请';
                    break;
                case 'specialReTestApply':
                    url = '/student/examinationManagement/specialReTestApply/index';
                    title = '特殊重考申请';
                    break;
                case 'cet':
                    url = '/student/examinationManagement/cet/index';
                    title = '英语等级考试';
                    break;
                case 'othersExamPlan':
                    url = '/student/examinationManagement/othersExamPlan/index';
                    title = '其他考试安排';
                    break;
                default:
                    showError('功能暂未开放');
                    return;
            }

            if (parent && parent.addTab) {
                parent.addTab(title, url);
            } else {
                window.location.href = url;
            }
        }

        // 快速操作函数
        function goToExamPlan() {
            goToFunction('examPlan');
        }

        function goToExamGrade() {
            goToFunction('examGrade');
        }

        function goToPrintCertificate() {
            goToFunction('printAdmissionCertificate');
        }

        function goToExamSignUp() {
            goToFunction('examSignUp');
        }

        // 刷新数据
        function refreshData() {
            loadExamStats();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
