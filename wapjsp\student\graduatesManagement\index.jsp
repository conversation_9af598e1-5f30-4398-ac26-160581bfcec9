<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>毕业生管理</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 毕业生管理页面样式 */
        .graduate-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .graduate-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .status-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .status-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
        }
        
        .status-icon.qualified {
            background: var(--success-color);
        }
        
        .status-icon.pending {
            background: var(--warning-color);
        }
        
        .status-icon.unqualified {
            background: var(--error-color);
        }
        
        .status-content {
            flex: 1;
        }
        
        .status-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .status-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .graduate-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .graduate-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: var(--bg-tertiary);
        }
        
        .graduate-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .graduate-list {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            display: flex;
            align-items: center;
        }
        
        .list-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .list-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .graduate-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .graduate-item:last-child {
            border-bottom: none;
        }
        
        .graduate-item:active {
            background: var(--bg-color-active);
        }
        
        .graduate-item.completed {
            border-left: 4px solid var(--success-color);
        }
        
        .graduate-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .graduate-item.incomplete {
            border-left: 4px solid var(--error-color);
        }
        
        .graduate-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .graduate-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .graduate-status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-completed {
            background: var(--success-color);
            color: white;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-incomplete {
            background: var(--error-color);
            color: white;
        }
        
        .graduate-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .graduate-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .graduate-progress {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
        }
        
        .progress-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .progress-bar-container {
            background: var(--bg-primary);
            border-radius: 10px;
            height: 6px;
            overflow: hidden;
            margin-bottom: var(--margin-xs);
        }
        
        .progress-bar {
            height: 100%;
            border-radius: 10px;
            transition: width var(--transition-base);
        }
        
        .progress-bar.completed {
            background: var(--success-color);
        }
        
        .progress-bar.pending {
            background: var(--warning-color);
        }
        
        .progress-bar.incomplete {
            background: var(--error-color);
        }
        
        .progress-text {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
            text-align: right;
        }
        
        .graduate-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-apply {
            background: var(--success-color);
            color: white;
        }
        
        .btn-download {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .application-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .application-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            cursor: pointer;
            font-size: var(--font-size-base);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
        }
        
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">毕业生管理</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="graduate-header">
            <div class="header-title">毕业生管理</div>
            <div class="header-subtitle">毕业资格审查和相关事务管理</div>
        </div>

        <!-- 毕业状态 -->
        <div class="graduate-status">
            <div class="status-title">
                <i class="ace-icon fa fa-graduation-cap"></i>
                <span>毕业状态</span>
            </div>

            <div class="status-card" id="graduateStatusCard">
                <!-- 状态信息将动态填充 -->
            </div>
        </div>

        <!-- 毕业标签 -->
        <div class="graduate-tabs">
            <div class="graduate-tab active" data-tab="requirements" onclick="switchTab('requirements')">毕业要求</div>
            <div class="graduate-tab" data-tab="applications" onclick="switchTab('applications')">申请事务</div>
            <div class="graduate-tab" data-tab="documents" onclick="switchTab('documents')">相关文档</div>
        </div>

        <!-- 毕业列表 -->
        <div class="graduate-list">
            <div class="list-header">
                <div class="list-title">
                    <i class="ace-icon fa fa-list"></i>
                    <span id="listTitle">毕业要求</span>
                </div>
                <div class="list-count" id="itemCount">0</div>
            </div>

            <div id="graduateItems">
                <!-- 毕业列表将动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-graduation-cap"></i>
            <div id="emptyMessage">暂无相关信息</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 申请表单 -->
    <div class="application-form" id="applicationForm">
        <div class="form-header">
            <div class="form-back" onclick="closeApplicationForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">毕业申请</div>
        </div>

        <div class="form-content">
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>个人信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">专业</div>
                    <input type="text" class="form-input" id="major" disabled>
                </div>

                <div class="form-group">
                    <div class="form-label">班级</div>
                    <input type="text" class="form-input" id="className" disabled>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>申请信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">申请类型</div>
                    <select class="form-input" id="applicationType">
                        <option value="">请选择申请类型</option>
                        <option value="graduation">毕业申请</option>
                        <option value="degree">学位申请</option>
                        <option value="certificate">证书申请</option>
                        <option value="transcript">成绩单申请</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">申请理由</div>
                    <textarea class="form-input" id="reason" placeholder="请说明申请理由" style="min-height: 80px; resize: vertical;"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">联系电话</div>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮寄地址</div>
                    <textarea class="form-input" id="address" placeholder="请输入邮寄地址（选填）" style="min-height: 60px; resize: vertical;"></textarea>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeApplicationForm();">取消</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let allItems = [];
        let currentTab = 'requirements';
        let graduateStatus = {};
        let currentItem = null;

        $(function() {
            initPage();
            loadGraduateStatus();
            loadGraduateData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载毕业状态
        function loadGraduateStatus() {
            $.ajax({
                url: "/student/graduatesManagement/getGraduateStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    graduateStatus = data.status || {};
                    renderGraduateStatus();
                },
                error: function() {
                    console.log('加载毕业状态失败');
                }
            });
        }

        // 渲染毕业状态
        function renderGraduateStatus() {
            const status = graduateStatus.status || 'pending';
            const iconClass = getStatusIconClass(status);
            const statusText = getStatusText(status);
            const metaText = graduateStatus.description || '请完成所有毕业要求';

            const statusHtml = `
                <div class="status-icon ${status}">
                    <i class="ace-icon fa ${iconClass}"></i>
                </div>
                <div class="status-content">
                    <div class="status-text">${statusText}</div>
                    <div class="status-meta">${metaText}</div>
                </div>
            `;

            $('#graduateStatusCard').html(statusHtml);
        }

        // 获取状态图标类
        function getStatusIconClass(status) {
            switch(status) {
                case 'qualified': return 'fa-check';
                case 'pending': return 'fa-clock-o';
                case 'unqualified': return 'fa-times';
                default: return 'fa-question';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'qualified': return '符合毕业条件';
                case 'pending': return '审核中';
                case 'unqualified': return '不符合毕业条件';
                default: return '未知状态';
            }
        }

        // 加载毕业数据
        function loadGraduateData() {
            showLoading(true);

            $.ajax({
                url: "/student/graduatesManagement/getGraduateData",
                type: "post",
                data: { category: currentTab },
                dataType: "json",
                success: function(data) {
                    allItems = data.items || [];
                    renderGraduateList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载数据失败');
                    showLoading(false);
                }
            });
        }

        // 切换标签
        function switchTab(tab) {
            currentTab = tab;

            // 更新标签状态
            $('.graduate-tab').removeClass('active');
            $(`.graduate-tab[data-tab="${tab}"]`).addClass('active');

            // 更新列表标题
            const titles = {
                'requirements': '毕业要求',
                'applications': '申请事务',
                'documents': '相关文档'
            };
            $('#listTitle').text(titles[tab]);

            loadGraduateData();
        }

        // 渲染毕业列表
        function renderGraduateList() {
            $('#itemCount').text(allItems.length);

            const container = $('#graduateItems');
            container.empty();

            if (allItems.length === 0) {
                const messages = {
                    'requirements': '暂无毕业要求信息',
                    'applications': '暂无申请事务',
                    'documents': '暂无相关文档'
                };
                showEmptyState(messages[currentTab]);
                return;
            } else {
                hideEmptyState();
            }

            allItems.forEach(item => {
                const itemHtml = createGraduateItem(item);
                container.append(itemHtml);
            });
        }

        // 创建毕业项
        function createGraduateItem(item) {
            const status = item.status || 'pending';
            const statusClass = getItemStatusClass(status);
            const statusText = getItemStatusText(status);

            if (currentTab === 'requirements') {
                return createRequirementItem(item, status, statusClass, statusText);
            } else if (currentTab === 'applications') {
                return createApplicationItem(item, status, statusClass, statusText);
            } else {
                return createDocumentItem(item, status, statusClass, statusText);
            }
        }

        // 创建要求项
        function createRequirementItem(item, status, statusClass, statusText) {
            const progress = item.progress || 0;

            return `
                <div class="graduate-item ${status}" onclick="showItemDetail('${item.id}')">
                    <div class="graduate-basic">
                        <div class="graduate-name">${item.name}</div>
                        <div class="graduate-status-badge ${statusClass}">${statusText}</div>
                    </div>
                    <div class="graduate-details">
                        <div class="graduate-detail-item">
                            <span>要求类型:</span>
                            <span>${getRequirementTypeText(item.type)}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>最低要求:</span>
                            <span>${item.requirement}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>当前状态:</span>
                            <span>${item.current}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>截止时间:</span>
                            <span>${formatDate(item.deadline)}</span>
                        </div>
                    </div>
                    <div class="graduate-progress">
                        <div class="progress-title">完成进度</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar ${status}" style="width: ${progress}%"></div>
                        </div>
                        <div class="progress-text">${progress}%</div>
                    </div>
                    <div class="graduate-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showItemDetail('${item.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 创建申请项
        function createApplicationItem(item, status, statusClass, statusText) {
            return `
                <div class="graduate-item ${status}" onclick="showItemDetail('${item.id}')">
                    <div class="graduate-basic">
                        <div class="graduate-name">${item.name}</div>
                        <div class="graduate-status-badge ${statusClass}">${statusText}</div>
                    </div>
                    <div class="graduate-details">
                        <div class="graduate-detail-item">
                            <span>申请类型:</span>
                            <span>${getApplicationTypeText(item.type)}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(item.applyTime)}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>处理时间:</span>
                            <span>${formatDate(item.processTime)}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>办理周期:</span>
                            <span>${item.duration || '-'}</span>
                        </div>
                    </div>
                    <div class="graduate-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showItemDetail('${item.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        ${status === 'pending' || !item.isApplied ? `
                            <button class="btn-mobile btn-apply" onclick="event.stopPropagation(); showApplicationForm('${item.id}');">
                                <i class="ace-icon fa fa-plus"></i>
                                <span>申请</span>
                            </button>
                        ` : status === 'completed' ? `
                            <button class="btn-mobile btn-download" onclick="event.stopPropagation(); downloadDocument('${item.id}');">
                                <i class="ace-icon fa fa-download"></i>
                                <span>下载</span>
                            </button>
                        ` : `
                            <button class="btn-mobile btn-disabled">
                                <i class="ace-icon fa fa-check"></i>
                                <span>已处理</span>
                            </button>
                        `}
                    </div>
                </div>
            `;
        }

        // 创建文档项
        function createDocumentItem(item, status, statusClass, statusText) {
            return `
                <div class="graduate-item ${status}" onclick="showItemDetail('${item.id}')">
                    <div class="graduate-basic">
                        <div class="graduate-name">${item.name}</div>
                        <div class="graduate-status-badge ${statusClass}">${statusText}</div>
                    </div>
                    <div class="graduate-details">
                        <div class="graduate-detail-item">
                            <span>文档类型:</span>
                            <span>${getDocumentTypeText(item.type)}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>文件大小:</span>
                            <span>${item.size || '-'}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>更新时间:</span>
                            <span>${formatDate(item.updateTime)}</span>
                        </div>
                        <div class="graduate-detail-item">
                            <span>下载次数:</span>
                            <span>${item.downloadCount || 0}</span>
                        </div>
                    </div>
                    <div class="graduate-actions">
                        <button class="btn-mobile btn-view" onclick="event.stopPropagation(); showItemDetail('${item.id}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>查看</span>
                        </button>
                        <button class="btn-mobile btn-download" onclick="event.stopPropagation(); downloadDocument('${item.id}');">
                            <i class="ace-icon fa fa-download"></i>
                            <span>下载</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 获取项目状态样式类
        function getItemStatusClass(status) {
            return `status-${status}`;
        }

        // 获取项目状态文本
        function getItemStatusText(status) {
            switch(status) {
                case 'completed': return '已完成';
                case 'pending': return '进行中';
                case 'incomplete': return '未完成';
                default: return '未知';
            }
        }

        // 获取要求类型文本
        function getRequirementTypeText(type) {
            switch(type) {
                case 'credits': return '学分要求';
                case 'courses': return '课程要求';
                case 'thesis': return '论文要求';
                case 'practice': return '实践要求';
                default: return '其他要求';
            }
        }

        // 获取申请类型文本
        function getApplicationTypeText(type) {
            switch(type) {
                case 'graduation': return '毕业申请';
                case 'degree': return '学位申请';
                case 'certificate': return '证书申请';
                case 'transcript': return '成绩单申请';
                default: return '其他申请';
            }
        }

        // 获取文档类型文本
        function getDocumentTypeText(type) {
            switch(type) {
                case 'guide': return '指导文档';
                case 'form': return '申请表格';
                case 'template': return '模板文件';
                case 'notice': return '通知公告';
                default: return '其他文档';
            }
        }

        // 显示项目详情
        function showItemDetail(itemId) {
            const item = allItems.find(i => i.id === itemId);
            if (!item) return;

            let message = `详情信息\n\n`;
            message += `名称：${item.name}\n`;
            message += `状态：${getItemStatusText(item.status)}\n`;

            if (currentTab === 'requirements') {
                message += `类型：${getRequirementTypeText(item.type)}\n`;
                message += `要求：${item.requirement}\n`;
                message += `当前：${item.current}\n`;
                message += `进度：${item.progress || 0}%\n`;
                message += `截止：${formatDate(item.deadline)}\n`;
            } else if (currentTab === 'applications') {
                message += `类型：${getApplicationTypeText(item.type)}\n`;
                message += `申请时间：${formatDate(item.applyTime)}\n`;
                message += `处理时间：${formatDate(item.processTime)}\n`;
                message += `办理周期：${item.duration || '-'}\n`;
            } else {
                message += `类型：${getDocumentTypeText(item.type)}\n`;
                message += `大小：${item.size || '-'}\n`;
                message += `更新：${formatDate(item.updateTime)}\n`;
                message += `下载：${item.downloadCount || 0}次\n`;
            }

            if (item.description) {
                message += `\n说明：${item.description}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示申请表单
        function showApplicationForm(itemId) {
            const item = allItems.find(i => i.id === itemId);
            if (!item) return;

            currentItem = item;

            // 填充基本信息
            $('#studentId').val('2021001001'); // 模拟数据
            $('#studentName').val('张三'); // 模拟数据
            $('#major').val('计算机科学与技术'); // 模拟数据
            $('#className').val('计科2021-1班'); // 模拟数据

            // 清空表单
            $('#applicationType').val(item.type || '');
            $('#reason').val('');
            $('#phone').val('');
            $('#address').val('');

            $('#formTitle').text(`${item.name} - 申请`);
            $('#applicationForm').addClass('show');
        }

        // 关闭申请表单
        function closeApplicationForm() {
            $('#applicationForm').removeClass('show');
            currentItem = null;
        }

        // 提交申请
        function submitApplication() {
            if (!currentItem) return;

            const formData = {
                itemId: currentItem.id,
                applicationType: $('#applicationType').val(),
                reason: $('#reason').val().trim(),
                phone: $('#phone').val().trim(),
                address: $('#address').val().trim()
            };

            if (!validateForm(formData)) {
                return;
            }

            const message = `确定要提交"${currentItem.name}"申请吗？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSubmitApplication(formData);
                    }
                });
            } else {
                if (confirm(message)) {
                    doSubmitApplication(formData);
                }
            }
        }

        // 验证表单
        function validateForm(formData) {
            if (!formData.applicationType) {
                showError('请选择申请类型');
                return false;
            }

            if (!formData.reason) {
                showError('请填写申请理由');
                return false;
            }

            if (!formData.phone) {
                showError('请填写联系电话');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                showError('请输入正确的手机号码');
                return false;
            }

            return true;
        }

        // 执行提交申请
        function doSubmitApplication(formData) {
            $.ajax({
                url: "/student/graduatesManagement/submitApplication",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('申请提交成功');
                        closeApplicationForm();
                        loadGraduateData(); // 重新加载数据
                    } else {
                        showError(data.message || '申请提交失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 下载文档
        function downloadDocument(itemId) {
            const item = allItems.find(i => i.id === itemId);
            if (!item) return;

            // 创建下载链接
            const link = document.createElement('a');
            link.href = `/student/graduatesManagement/download?itemId=${itemId}`;
            link.download = `${item.name}.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showSuccess('文档下载成功');
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 刷新数据
        function refreshData() {
            loadGraduateStatus();
            loadGraduateData();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
