package educationalAdministration.student.individualApplication.applyCommon.controller;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.urpSoft.business.utils.UrpResult;
import com.urpSoft.core.data.orm.dao.IBaseDao;
import com.urpSoft.core.document.common.service.CommonExportService;

import educationalAdministration.dictionary.entity.EaApplyType;
import educationalAdministration.dictionary.entity.SysSqfjb;
import educationalAdministration.dictionary.entity.SysYwhdkzb;
import educationalAdministration.student.common.service.CommonService;
import educationalAdministration.student.common.utils.CommonUtils;
import educationalAdministration.student.individualApplication.applyCommon.entity.EaApplysQu;
import educationalAdministration.student.individualApplication.applyCommon.service.ApplyCommonService;
import educationalAdministration.student.individualApplication.creditCertification.entity.CodeXfrzlxb;
import educationalAdministration.student.personalManagement.entity.PxCsb;

@Controller
public class ApplyCommonController {


	/*@Resource
    private IPageService pageService;

    private CSRFToken csrfToken = CSRFToken.getInstance();

	 */
	@Resource
	private ApplyCommonService applyCommonService;

	@Resource
	private CommonService commonService;

	@Resource
	private CommonExportService commonExportService;

	@Resource
	private IBaseDao baseDao;

	/**
	 * 个人申请首页
	 *
	 * @param
	 * @return
	 * @author: gdx
	 * @date: 2018/8/15 14:14
	 */
	@RequestMapping(value = "/student/application/index", method = RequestMethod.GET)
	public String index(Model model) {
		String schoolCode = commonService.queryParamValue();
		model.addAttribute("schoolCode", schoolCode);
		return "/student/personalManagement/individualApplication/index";
	}

	@RequestMapping(value = "/student/application/index/showApplication", method = RequestMethod.POST)
	@ResponseBody
	public UrpResult showApplication(Model model) {
		Map<String, Object> map = new HashMap<String, Object>();
		List<SysYwhdkzb> list = applyCommonService.queryAllSysYwhdkzb("STUDENT");
		map.put("list", list);
		String todate = commonService.queryCurrentTimeMinssBySql();
		map.put("todate", todate);
		String sql = "select count(1) from px_csb where csdm = 'ZZY' and zcsdm = 'XSDKFF' and csz = '1'";
		long zzykgcount = applyCommonService.queryCounBySql(sql);
		sql = "select count(1) from xs_zzypcb a where a.a_opened = '1' and pclb = 'ZZY'";
		long zzycount = applyCommonService.queryCounBySql(sql);
		map.put("zzykgcount", zzykgcount);
		map.put("zzycount", zzycount);
		sql = "select count(1) from px_csb where csdm = 'DLFL' and zcsdm = 'XSDKFF' and csz = '1'";
		long dlflkgcount = applyCommonService.queryCounBySql(sql);
		sql = "select count(1) from xs_flpcb a where a.a_opened = '1'";
		long dlflcount = applyCommonService.queryCounBySql(sql);
		map.put("dlflkgcount", dlflkgcount);
		map.put("dlflcount", dlflcount);
		sql = "select count(1) from px_csb where csdm = 'ZYFFX' and zcsdm = 'XSDKFF' and csz = '1'";
		long zyffxkgcount = applyCommonService.queryCounBySql(sql);
		sql = "select count(1) from xs_zzypcb a where a.a_opened = '1' and pclb = 'ZYFFX'";
		long zyffxcount = applyCommonService.queryCounBySql(sql);
		map.put("zyffxkgcount", zyffxkgcount);
		map.put("zyffxcount", zyffxcount);
		long jxjcount = 0;
		String schoolCode = CommonUtils.queryParamValue();
		if (!("100041".equals(schoolCode) || "100053".equals(schoolCode))){
			sql = "select count(1) from xs_jxjsqpcb where xsbmkg = '1' and sysdate between to_date(bmkssj,'yyyymmddhh24miss') and to_date(bmjssj,'yyyymmddhh24miss')";
			jxjcount = commonService.queryCounBySql(sql);
		}
		map.put("jxjcount", jxjcount);
		
		sql = "SELECT nvl(sum(a.csz),0) csz FROM px_csb a where a.csdm = 'chxxm' and a.zcsdm = 'xsdkff' ";
		long xsdkffcount = applyCommonService.queryCounBySql(sql);
		map.put("xsdkffcount", xsdkffcount);


		sql = "select count(kg) from chx_xmkgkzb where kg ='开'";
		long cxcycount = commonService.queryCounBySql(sql);
		map.put("cxcycount", cxcycount);
		return UrpResult.ok(map);
	}

	@RequestMapping(value = {"/student/application/index/seeInfo","/phone/student/application/index/seeInfo"}, method = RequestMethod.GET)
	public String showInofView(HttpServletRequest request,Model model, String applyId, String applyType) {
		String flag = "";
		SysYwhdkzb ywsqkzb = applyCommonService.querySysYwhdkzbById(applyType);
		if (ywsqkzb == null) {
			flag = "nonparametric";
		} else {
			if (ywsqkzb.getQyf().equals("0")) {
				flag = "notenabled";
			} else {
				int timeCount = applyCommonService.queryTimeFrame(applyType);
				if (timeCount == 0) {
					flag = "nottime";
				} else {
					flag = "showAdd";
				}
			}
		}

		boolean mobile = false;
		if (CommonUtils.checkMobile(request)) {
			mobile = true;
		}
		model.addAttribute("mobile", mobile);

		/*
		 * 1.获取申请单详情信息
		 * 2.获取获取审批信息
		 */
		String infoHtml = "";
		EaApplyType eaApplyType = null;
		if (applyType.toUpperCase().startsWith("XFRZ")) {//学分认证相关的申请
			String sql="select * from CODE_XFRZLXB where apply_type='"+applyType+"' and rownum=1";
			CodeXfrzlxb codeXfrzlxb = baseDao.findEntityBySQL(sql, CodeXfrzlxb.class);
			if("G".equals(codeXfrzlxb.getKcrdfs())){
				eaApplyType = applyCommonService.queryEntityById(EaApplyType.class, codeXfrzlxb.getApplyType());
			}
		}else{
			eaApplyType = applyCommonService.queryEntityById(EaApplyType.class, applyType);
		}
		String type = applyType;
		if (eaApplyType!=null&&StringUtils.isNotBlank(eaApplyType.getAdTable()) && "xs_rcswsqb".equals(eaApplyType.getAdTable().toLowerCase())) {//学生日常事务表"CODE_XSRCSWB"相关的申请
			type = "xs_rcswsqb";
		}
		if (type.toUpperCase().startsWith("XFRZ")||type.toUpperCase().startsWith("KZR")) {//学分认证相关的申请
			type = "XFRZ";
		}else if (type.toUpperCase().startsWith("RKZ")) {//课组认证相关的申请
			type = "10036";
		}
		if ("CHX".equals(applyType.toUpperCase().substring(0, 3)) && !"CHXM1".equals(applyType) && !"CHXM2".equals(applyType) && !"CHXM3".equals(applyType) && !"CHXM4".equals(applyType)) {//创新学分项目认定相关的申请
			type = "10021";
		}
		switch (type) {
		case "10001":
			//课程替代
			infoHtml = applyCommonService.queryKctdInfoHtml(applyId, mobile);
			break;
		case "10002":
			//退课申请
			infoHtml = applyCommonService.queryStudentRetreatClassInfoHtml(applyId, applyType);
			break;
		case "10003":
			//补选课申请
			infoHtml = applyCommonService.queryStudentByElectionClassInfoHtml(applyId);
			break;
		case "10004":
			//免听申请
			infoHtml = applyCommonService.queryStudentListenFreeInfoHtml(applyId);
			break;
		case "10005":
			//免修申请
			infoHtml = applyCommonService.queryStudentExemptionInfoHtml(applyId);
			break;
		case "10006":
			//方案注销
			infoHtml = applyCommonService.queryFaxdsqInfoHtml(applyId, mobile);
			break;
		case "10008":
			//辅修方案注册
			infoHtml = applyCommonService.queryFaxdsqInfoHtml(applyId,mobile);
			break;
		case "10010":
			//自学重修
			infoHtml = applyCommonService.queryStudentRetreatClassInfoHtml(applyId, applyType);
			break;
		case "10011":
			//缓考申请
			infoHtml = applyCommonService.queryStudentRetreatClassInfoHtml(applyId, applyType);
			break;
		case "10014":
			//竞赛免考申请
			infoHtml = applyCommonService.queryStudentCompetitionExemInfoHtml(applyId, applyType);
			break;
		case "10015":
			//退伍复学免修申请
			infoHtml = applyCommonService.queryStudentJoinTheArmyInfoHtml(applyId, applyType);
			break;
		case "xs_rcswsqb":
			//学生日常事务表"CODE_XSRCSWB"相关的申请
			infoHtml = applyCommonService.queryBusSectionInfoHtml(applyId, applyType);
			break;
		case "XFRZ":
			///学分认证相关的申请
			flag = "";
			infoHtml = applyCommonService.queryCreditCertificationInfoHtml(model, applyId, mobile);
			break;
		case "10021":
			//创新学分项目认定相关的申请
			flag = "";
			infoHtml = applyCommonService.queryInnovationProjectInfoHtml(applyId, applyType);
			break;
		case "10022"://结业生考试
		case "10023"://结业生选课
			infoHtml = applyCommonService.queryGraduateStudentHtml(applyId);
			break;
		case "10024":
			infoHtml = applyCommonService.queryChangeStudentInfoHtml(applyId);
			break;
		case "10025":
			//学生申请成绩认定
			infoHtml = applyCommonService.achievementRecognitionHtml(applyId);
			break;
		case "10027":
			//审查降分申请
			infoHtml = applyCommonService.reviewScoreReductionHtml(applyId);
			break;
		case "10031":
			//推免去向申请
			infoHtml = applyCommonService.destinationApplyHtml(applyId);
			break;
		case "10036":
			//课组认定申请
			flag = "";
			infoHtml = applyCommonService.queryClassIdentificationInfoHtml(applyId);
			break;
		case "CHXM2":
			//项目终止申请
			flag = "";
			infoHtml = applyCommonService.queryTerminationInnovationInfoHtml(applyId);
			break;
		case "CHXM3":
			//项目延期申请
			flag = "";
			infoHtml = applyCommonService.queryExtensionInnovationInfoHtml(applyId);
			break;
			case "10043":
				//学生自主申报毕设题目
				infoHtml = applyCommonService.queryStudentsDeclareGraduationHtml(applyId,false);
				break;
		default:
			break;

		}
		EaApplysQu eaApplys = applyCommonService.queryEntityById(EaApplysQu.class, applyId);
		//List<EaResultQu> eaResults = applyCommonService.queryEaResultByApplyId(applyId, model);
		List<Object[]> eaResults = null;
		if (eaApplyType!=null&&"1".equals(eaApplyType.getParallel())) {
			eaResults = applyCommonService.queryEaRsltByApplyId("ea_rslt_parallel", applyId, eaApplys.getApplyStatus());
		} else {
			eaResults = applyCommonService.queryEaResultsByApplyId(applyId);
		}
		//List<Object[]> processLinks = applyCommonService.queryProcessLinkByApplyId(applyId);
		model.addAttribute("infoHtml", infoHtml);
		model.addAttribute("applyId", applyId);
		model.addAttribute("applyType", applyType);
		model.addAttribute("eaResults", eaResults);
		if(eaApplys!=null){
			if (StringUtils.isNotBlank(eaApplys.getRollbackMode())) {
				if ("0".equals(eaApplys.getRollbackMode())) {
					eaApplys.setRollbackUserName(applyCommonService.queryXm(eaApplys.getRollbackUser()));
				} else {
					eaApplys.setRollbackUserName(applyCommonService.queryJsm(eaApplys.getRollbackUser()));
				}
			}
		}
		model.addAttribute("eaApplys", eaApplys);
		//model.addAttribute("processLinks", processLinks);

		//获取上传附件
		List<SysSqfjb> sysSqfjbs = applyCommonService.querySysSqfjbByApplyId(applyId);
		model.addAttribute("sysSqfjbs", sysSqfjbs);

		String approvalHtml = "";
		if (eaApplyType!=null&&"1".equals(eaApplyType.getParallel()) && eaResults != null && eaResults.size() > 0) {
			int len = eaResults.size();
			for (int i = 0; i < len; i++) {
				Object[] obj = eaResults.get(i);
				String ea_rslt = obj[2] == null ? "" : obj[2].toString();
				boolean eqLast = false;//当前审批结果是否与上一条审批结果为并行审批数据
				boolean eqNext = false;//当前审批结果是否与下一条审批结果为并行审批数据
				if (i - 1 >= 0) {
					if (eaResults.get(i - 1)[0].equals(obj[0])) {
						eqLast = true;
					}
				}
				if (i + 1 < len) {
					if (eaResults.get(i + 1)[0].equals(obj[0])) {
						eqNext = true;
					}
				}

				if (!eqLast) {
					approvalHtml += "<div class=\"timeline-item clearfix\">";
					approvalHtml += "    <div class=\"timeline-info\" style=\"z-index: 1;\">";
					approvalHtml += "        <i class=\"timeline-indicator ace-icon fa fa-leaf btn btn-primary no-hover green\"></i> ";
					approvalHtml += "        <span class=\"label label-yellow label-sm\">" + obj[1] + "</span>";
					approvalHtml += "    </div>";
				}

				approvalHtml += "    <div class=\"widget-box transparent\" " + (eqNext ? "style=\"margin-bottom: 3px;\"" : "") + ">";
				approvalHtml += "        <div class=\"widget-body\">";
				approvalHtml += "            <div class=\"widget-main\" style=\"height: 43px;\">";
				if (eqLast || eqNext) {
					approvalHtml += "        " + (obj[9] == null ? "" : obj[9] + "&nbsp;&nbsp;");
				}
				approvalHtml += "            " + (obj[5] == null ? "" : obj[5] + "&nbsp;&nbsp;");

				if ("0".equals(ea_rslt)) {
					approvalHtml += "        待审批";
				} else if ("1".equals(ea_rslt)) {
					approvalHtml += "        <span class=\"red bolder\">拒绝</span>";
				} else if ("2".equals(ea_rslt)) {
					approvalHtml += "        跳过";
				} else if ("3".equals(ea_rslt)) {
					approvalHtml += "        <span class=\"green bolder\">批准</span>";
				}
				if (obj[3] != null) {
					approvalHtml += "        (" + obj[3] + ")";
				}
				if (obj[6] != null) {
					approvalHtml += "        <div class=\"pull-right\"><i class=\"ace-icon fa fa-clock-o bigger-110\"></i>" + obj[6] + "</div>";
				}
				approvalHtml += "            </div>";
				approvalHtml += "        </div>";
				approvalHtml += "    </div>";

				if (!eqNext) {
					approvalHtml += "</div>";
				}

			}
		}
		model.addAttribute("againApply", flag);
		model.addAttribute("approvalHtml", approvalHtml);
		model.addAttribute("eaApplyType", eaApplyType);
		if (mobile) {
			// 检查对应路径的 JSP 文件是否存在
			ServletContext servletContext = request.getSession().getServletContext();
			String realPath = servletContext.getRealPath("/WEB-INF/jsp/wap/student/personalManagement/individualApplication/approval.jsp");
			if (realPath != null) {
				File file = new File(realPath);
				if (file.exists()) {
					return "/wap/student/personalManagement/individualApplication/approval";
				}
			}
		}
		return "/student/personalManagement/individualApplication/approval";
	}

	/**
	 * 下载附件
	 *
	 * @param model
	 * @param response
	 * @param scid
	 */
	@RequestMapping(value = "/student/application/index/dodownload/{sqbh}")
	public void download(Model model,
			HttpServletResponse response,
			@PathVariable("sqbh") String sqbh) {
		SysSqfjb sysSqfjb = applyCommonService.querySysSqfjbBySqbh(sqbh);
		if (sysSqfjb != null) {
			commonExportService.exportCommon(sysSqfjb.getFjmc(), sysSqfjb.getFjnr(), response);
		}
	}

	/**
	 * 下载附件
	 *
	 * @param model
	 * @param response
	 * @param fjid
	 */
	@RequestMapping(value = "/student/application/index/downloadById")
	public void downloadById(Model model, HttpServletResponse response, @RequestParam("fjid") String fjid) {
		SysSqfjb sysSqfjb = applyCommonService.queryEntityById(SysSqfjb.class, fjid);
		if (sysSqfjb != null) {
			commonExportService.exportCommon(sysSqfjb.getFjmc(), sysSqfjb.getFjnr(), response);
		}
	}

	/**
	 * 校验是否需要指定审批人
	 *
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "/student/personalManagement/individualApplication/index/checkSpgzlZdspr")
	@ResponseBody
	public UrpResult checkSpgzlZdspr(Model model, String apply_type) {
		PxCsb pxCsb = CommonUtils.queryPxCsbById("spgzl", "zdspr");//审批工作流是否指定审批人（1是0否）
		String csz = pxCsb != null ? pxCsb.getCsz() : "0";
		if ("10001".equals(apply_type) || "10002".equals(apply_type) || "10003".equals(apply_type) || "10012".equals(apply_type) || "10016".equals(apply_type)
				|| "10017".equals(apply_type) || "10018".equals(apply_type) || "10028".equals(apply_type) || "10029".equals(apply_type) || "10030".equals(apply_type)
				|| "10031".equals(apply_type) || "10037".equals(apply_type) || apply_type.startsWith("YD") || apply_type.startsWith("XFRZ")
				|| apply_type.startsWith("10039") || apply_type.startsWith("10041") || apply_type.startsWith("10042") || apply_type.startsWith("10043")
				|| apply_type.startsWith("10011")) {
			EaApplyType eaApplyType = applyCommonService.queryEntityById(EaApplyType.class, apply_type);
			csz = eaApplyType.getAssigning();
		}
		return UrpResult.ok(csz);
	}

}