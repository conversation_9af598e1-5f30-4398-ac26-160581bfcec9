<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>勤工助学</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 勤工助学页面样式 */
        .work-summary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .summary-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .stat-item {
            padding: var(--padding-sm);
        }
        
        .stat-number {
            font-size: var(--font-size-h3);
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .action-btn {
            padding: var(--padding-md);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 1px solid var(--border-primary);
            background: var(--bg-primary);
        }
        
        .action-btn:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .action-icon {
            font-size: var(--font-size-h3);
            margin-bottom: var(--margin-sm);
        }
        
        .action-icon.jobs {
            color: var(--success-color);
        }
        
        .action-icon.apply {
            color: var(--primary-color);
        }
        
        .action-icon.record {
            color: var(--warning-color);
        }
        
        .action-icon.salary {
            color: var(--info-color);
        }
        
        .action-text {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .filter-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .filter-chip {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: var(--font-size-small);
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: none;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .job-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .job-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .job-urgent {
            border-left-color: var(--error-color);
        }
        
        .job-popular {
            border-left-color: var(--warning-color);
        }
        
        .job-new {
            border-left-color: var(--success-color);
        }
        
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .job-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .job-salary {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-small);
            font-weight: 500;
            background: var(--success-color);
            color: white;
        }
        
        .job-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .job-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .job-description {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-top: var(--margin-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .job-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
            margin-top: var(--margin-sm);
        }
        
        .job-tag {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
            background: var(--primary-color);
            color: white;
        }
        
        .job-tag.urgent {
            background: var(--error-color);
        }
        
        .job-tag.popular {
            background: var(--warning-color);
        }
        
        .job-tag.new {
            background: var(--success-color);
        }
        
        .application-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--info-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .application-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .application-approved {
            border-left-color: var(--success-color);
        }
        
        .application-rejected {
            border-left-color: var(--error-color);
        }
        
        .application-working {
            border-left-color: var(--warning-color);
        }
        
        .application-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-job {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--info-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .status-working {
            background: var(--warning-color);
            color: white;
        }
        
        .status-completed {
            background: var(--text-disabled);
            color: white;
        }
        
        .application-details {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .application-time {
            margin-top: var(--margin-xs);
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .work-record {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--success-color);
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .record-date {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .record-hours {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-small);
            font-weight: 500;
            background: var(--info-color);
            color: white;
        }
        
        .record-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .record-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .salary-summary {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .salary-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .salary-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            text-align: center;
        }
        
        .salary-stat {
            padding: var(--padding-md);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .salary-amount {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: var(--margin-xs);
        }
        
        .salary-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">勤工助学</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 工作统计 -->
        <div class="work-summary">
            <div class="summary-title">勤工助学统计</div>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalJobs">0</div>
                    <div class="stat-label">可申请岗位</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="myApplications">0</div>
                    <div class="stat-label">我的申请</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalEarnings">¥0</div>
                    <div class="stat-label">累计收入</div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-title">快捷操作</div>
            <div class="action-buttons">
                <div class="action-btn" onclick="showJobList();">
                    <div class="action-icon jobs">
                        <i class="ace-icon fa fa-briefcase"></i>
                    </div>
                    <div class="action-text">浏览岗位</div>
                </div>
                <div class="action-btn" onclick="showMyApplications();">
                    <div class="action-icon apply">
                        <i class="ace-icon fa fa-file-text"></i>
                    </div>
                    <div class="action-text">我的申请</div>
                </div>
                <div class="action-btn" onclick="showWorkRecord();">
                    <div class="action-icon record">
                        <i class="ace-icon fa fa-clock-o"></i>
                    </div>
                    <div class="action-text">工作记录</div>
                </div>
                <div class="action-btn" onclick="showSalarySummary();">
                    <div class="action-icon salary">
                        <i class="ace-icon fa fa-money"></i>
                    </div>
                    <div class="action-text">薪资统计</div>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-section">
            <div class="filter-chips">
                <button class="filter-chip active" onclick="filterContent('jobs')">可申请岗位</button>
                <button class="filter-chip" onclick="filterContent('applications')">我的申请</button>
                <button class="filter-chip" onclick="filterContent('records')">工作记录</button>
            </div>
        </div>

        <!-- 岗位列表 -->
        <div class="container-mobile" id="jobsContainer">
            <div id="jobList">
                <!-- 岗位列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 申请记录 -->
        <div class="container-mobile" id="applicationsContainer" style="display: none;">
            <div id="applicationList">
                <!-- 申请记录将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 工作记录 -->
        <div class="container-mobile" id="recordsContainer" style="display: none;">
            <div id="workRecordList">
                <!-- 工作记录将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 薪资统计 -->
        <div class="salary-summary" id="salarySummary" style="display: none;">
            <div class="salary-title">薪资统计</div>
            <div class="salary-stats">
                <div class="salary-stat">
                    <div class="salary-amount" id="thisMonthSalary">¥0</div>
                    <div class="salary-label">本月收入</div>
                </div>
                <div class="salary-stat">
                    <div class="salary-amount" id="totalSalary">¥0</div>
                    <div class="salary-label">累计收入</div>
                </div>
                <div class="salary-stat">
                    <div class="salary-amount" id="totalHours">0</div>
                    <div class="salary-label">累计工时</div>
                </div>
                <div class="salary-stat">
                    <div class="salary-amount" id="averageHourly">¥0</div>
                    <div class="salary-label">平均时薪</div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-briefcase"></i>
            <div id="emptyMessage">暂无数据</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let jobs = [];
        let applications = [];
        let workRecords = [];
        let currentView = 'jobs';
        let salaryData = {};

        $(function() {
            initPage();
            loadAllData();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载所有数据
        function loadAllData() {
            showLoading(true);

            Promise.all([
                loadJobs(),
                loadApplications(),
                loadWorkRecords(),
                loadSalaryData(),
                loadStatistics()
            ]).then(() => {
                showLoading(false);
                renderCurrentView();
            }).catch(() => {
                showLoading(false);
                showError('加载数据失败');
            });
        }

        // 加载岗位列表
        function loadJobs() {
            return $.ajax({
                url: "/student/scholarship/workStudy/getJobs",
                type: "post",
                dataType: "json",
                success: function(data) {
                    jobs = data || [];
                }
            });
        }

        // 加载申请记录
        function loadApplications() {
            return $.ajax({
                url: "/student/scholarship/workStudy/getApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    applications = data || [];
                }
            });
        }

        // 加载工作记录
        function loadWorkRecords() {
            return $.ajax({
                url: "/student/scholarship/workStudy/getWorkRecords",
                type: "post",
                dataType: "json",
                success: function(data) {
                    workRecords = data || [];
                }
            });
        }

        // 加载薪资数据
        function loadSalaryData() {
            return $.ajax({
                url: "/student/scholarship/workStudy/getSalaryData",
                type: "post",
                dataType: "json",
                success: function(data) {
                    salaryData = data || {};
                    updateSalaryDisplay();
                }
            });
        }

        // 加载统计信息
        function loadStatistics() {
            return $.ajax({
                url: "/student/scholarship/workStudy/getStatistics",
                type: "post",
                dataType: "json",
                success: function(data) {
                    updateStatistics(data);
                }
            });
        }

        // 更新统计信息
        function updateStatistics(statistics) {
            if (!statistics) return;

            $('#totalJobs').text(statistics.totalJobs || 0);
            $('#myApplications').text(statistics.myApplications || 0);
            $('#totalEarnings').text('¥' + (statistics.totalEarnings || 0));
        }

        // 更新薪资显示
        function updateSalaryDisplay() {
            $('#thisMonthSalary').text('¥' + (salaryData.thisMonthSalary || 0));
            $('#totalSalary').text('¥' + (salaryData.totalSalary || 0));
            $('#totalHours').text(salaryData.totalHours || 0);
            $('#averageHourly').text('¥' + (salaryData.averageHourly || 0));
        }

        // 筛选内容
        function filterContent(view) {
            currentView = view;

            // 更新筛选按钮状态
            $('.filter-chip').removeClass('active');
            $(event.target).addClass('active');

            renderCurrentView();
        }

        // 渲染当前视图
        function renderCurrentView() {
            // 隐藏所有容器
            $('#jobsContainer, #applicationsContainer, #recordsContainer, #salarySummary').hide();

            switch(currentView) {
                case 'jobs':
                    $('#jobsContainer').show();
                    renderJobList();
                    break;
                case 'applications':
                    $('#applicationsContainer').show();
                    renderApplicationList();
                    break;
                case 'records':
                    $('#recordsContainer').show();
                    renderWorkRecordList();
                    break;
            }
        }

        // 渲染岗位列表
        function renderJobList() {
            const container = $('#jobList');
            container.empty();

            if (jobs.length === 0) {
                showEmptyState('暂无可申请岗位');
                return;
            } else {
                hideEmptyState();
            }

            jobs.forEach(job => {
                const jobHtml = createJobItem(job);
                container.append(jobHtml);
            });
        }

        // 创建岗位项
        function createJobItem(job) {
            const priorityClass = getPriorityClass(job.priority);
            const tags = createJobTags(job);

            return `
                <div class="job-item ${priorityClass}" onclick="showJobDetail('${job.id}')">
                    <div class="job-header">
                        <div class="job-title">${job.title}</div>
                        <div class="job-salary">¥${job.hourlyRate}/时</div>
                    </div>
                    <div class="job-details">
                        <div class="job-detail-item">
                            <span>工作地点:</span>
                            <span>${job.location}</span>
                        </div>
                        <div class="job-detail-item">
                            <span>工作时间:</span>
                            <span>${job.workTime}</span>
                        </div>
                        <div class="job-detail-item">
                            <span>招聘人数:</span>
                            <span>${job.positions}人</span>
                        </div>
                        <div class="job-detail-item">
                            <span>已申请:</span>
                            <span>${job.applicants}人</span>
                        </div>
                    </div>
                    <div class="job-description">${job.description}</div>
                    <div class="job-tags">${tags}</div>
                </div>
            `;
        }

        // 获取优先级样式类
        function getPriorityClass(priority) {
            switch(priority) {
                case 'urgent': return 'job-urgent';
                case 'popular': return 'job-popular';
                case 'new': return 'job-new';
                default: return '';
            }
        }

        // 创建岗位标签
        function createJobTags(job) {
            let tags = '';

            if (job.priority === 'urgent') {
                tags += '<span class="job-tag urgent">急招</span>';
            }
            if (job.priority === 'popular') {
                tags += '<span class="job-tag popular">热门</span>';
            }
            if (job.priority === 'new') {
                tags += '<span class="job-tag new">新发布</span>';
            }
            if (job.isFlexible) {
                tags += '<span class="job-tag">时间灵活</span>';
            }
            if (job.isRemote) {
                tags += '<span class="job-tag">可远程</span>';
            }

            return tags;
        }

        // 显示岗位详情
        function showJobDetail(jobId) {
            const job = jobs.find(j => j.id === jobId);
            if (!job) return;

            let message = `岗位详情\n\n`;
            message += `岗位名称：${job.title}\n`;
            message += `工作地点：${job.location}\n`;
            message += `工作时间：${job.workTime}\n`;
            message += `时薪：¥${job.hourlyRate}/时\n`;
            message += `招聘人数：${job.positions}人\n`;
            message += `已申请：${job.applicants}人\n`;
            message += `工作描述：${job.description}\n`;

            if (job.requirements) {
                message += `任职要求：${job.requirements}\n`;
            }

            message += `\n是否申请该岗位？`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        applyForJob(jobId);
                    }
                });
            } else {
                if (confirm(message)) {
                    applyForJob(jobId);
                }
            }
        }

        // 申请岗位
        function applyForJob(jobId) {
            // 检查是否已申请
            const existingApplication = applications.find(app => app.jobId === jobId && app.status !== 'rejected');
            if (existingApplication) {
                showError('您已申请过该岗位');
                return;
            }

            $.ajax({
                url: "/student/scholarship/workStudy/applyJob",
                type: "post",
                data: { jobId: jobId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('申请提交成功');
                        loadApplications();
                        loadStatistics();
                    } else {
                        showError(data.message || '申请失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 渲染申请列表
        function renderApplicationList() {
            const container = $('#applicationList');
            container.empty();

            if (applications.length === 0) {
                showEmptyState('暂无申请记录');
                return;
            } else {
                hideEmptyState();
            }

            applications.forEach(application => {
                const applicationHtml = createApplicationItem(application);
                container.append(applicationHtml);
            });
        }

        // 创建申请项
        function createApplicationItem(application) {
            const statusClass = getApplicationStatusClass(application.status);
            const statusBadgeClass = getApplicationStatusBadgeClass(application.status);
            const statusText = getApplicationStatusText(application.status);

            return `
                <div class="application-item ${statusClass}" onclick="showApplicationDetail('${application.id}')">
                    <div class="application-header">
                        <div class="application-job">${application.jobTitle}</div>
                        <div class="application-status ${statusBadgeClass}">${statusText}</div>
                    </div>
                    <div class="application-details">
                        申请时间：${application.applicationDate} | 时薪：¥${application.hourlyRate}/时
                        ${application.interviewTime ? ` | 面试时间：${application.interviewTime}` : ''}
                    </div>
                    <div class="application-time">
                        最后更新：${application.updateTime}
                    </div>
                </div>
            `;
        }

        // 获取申请状态样式类
        function getApplicationStatusClass(status) {
            switch(status) {
                case 'approved': return 'application-approved';
                case 'rejected': return 'application-rejected';
                case 'working': return 'application-working';
                default: return '';
            }
        }

        // 获取申请状态徽章样式类
        function getApplicationStatusBadgeClass(status) {
            switch(status) {
                case 'pending': return 'status-pending';
                case 'approved': return 'status-approved';
                case 'rejected': return 'status-rejected';
                case 'working': return 'status-working';
                case 'completed': return 'status-completed';
                default: return 'status-pending';
            }
        }

        // 获取申请状态文本
        function getApplicationStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                case 'working': return '工作中';
                case 'completed': return '已完成';
                default: return '未知';
            }
        }

        // 显示申请详情
        function showApplicationDetail(applicationId) {
            const application = applications.find(app => app.id === applicationId);
            if (!application) return;

            let message = `申请详情\n\n`;
            message += `岗位名称：${application.jobTitle}\n`;
            message += `申请时间：${application.applicationDate}\n`;
            message += `申请状态：${getApplicationStatusText(application.status)}\n`;
            message += `时薪：¥${application.hourlyRate}/时\n`;

            if (application.interviewTime) {
                message += `面试时间：${application.interviewTime}\n`;
            }

            if (application.workStartDate) {
                message += `工作开始时间：${application.workStartDate}\n`;
            }

            if (application.reviewComment) {
                message += `审核意见：${application.reviewComment}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 渲染工作记录列表
        function renderWorkRecordList() {
            const container = $('#workRecordList');
            container.empty();

            if (workRecords.length === 0) {
                showEmptyState('暂无工作记录');
                return;
            } else {
                hideEmptyState();
            }

            workRecords.forEach(record => {
                const recordHtml = createWorkRecordItem(record);
                container.append(recordHtml);
            });
        }

        // 创建工作记录项
        function createWorkRecordItem(record) {
            return `
                <div class="work-record" onclick="showWorkRecordDetail('${record.id}')">
                    <div class="record-header">
                        <div class="record-date">${record.workDate}</div>
                        <div class="record-hours">${record.hours}小时</div>
                    </div>
                    <div class="record-details">
                        <div class="record-detail-item">
                            <span>岗位:</span>
                            <span>${record.jobTitle}</span>
                        </div>
                        <div class="record-detail-item">
                            <span>时薪:</span>
                            <span>¥${record.hourlyRate}/时</span>
                        </div>
                        <div class="record-detail-item">
                            <span>工作时间:</span>
                            <span>${record.startTime}-${record.endTime}</span>
                        </div>
                        <div class="record-detail-item">
                            <span>薪资:</span>
                            <span>¥${record.salary}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示工作记录详情
        function showWorkRecordDetail(recordId) {
            const record = workRecords.find(r => r.id === recordId);
            if (!record) return;

            let message = `工作记录详情\n\n`;
            message += `工作日期：${record.workDate}\n`;
            message += `岗位名称：${record.jobTitle}\n`;
            message += `工作时间：${record.startTime}-${record.endTime}\n`;
            message += `工作时长：${record.hours}小时\n`;
            message += `时薪：¥${record.hourlyRate}/时\n`;
            message += `薪资：¥${record.salary}\n`;

            if (record.workContent) {
                message += `工作内容：${record.workContent}\n`;
            }

            if (record.evaluation) {
                message += `工作评价：${record.evaluation}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 快捷操作函数
        function showJobList() {
            filterContent('jobs');
            $('.filter-chip').removeClass('active');
            $('.filter-chip:first').addClass('active');
        }

        function showMyApplications() {
            filterContent('applications');
            $('.filter-chip').removeClass('active');
            $('.filter-chip:nth-child(2)').addClass('active');
        }

        function showWorkRecord() {
            filterContent('records');
            $('.filter-chip').removeClass('active');
            $('.filter-chip:nth-child(3)').addClass('active');
        }

        function showSalarySummary() {
            // 隐藏所有容器
            $('#jobsContainer, #applicationsContainer, #recordsContainer').hide();
            $('#salarySummary').show();
            hideEmptyState();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 刷新数据
        function refreshData() {
            loadAllData();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
