<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>申请详情</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 申请详情页面样式 */
        .detail-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .detail-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .detail-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .info-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            color: var(--primary-color);
        }
        
        .section-content {
            padding: var(--padding-md);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .info-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .info-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            word-break: break-all;
        }
        
        .teacher-info {
            background: var(--primary-light);
            padding: var(--padding-sm);
            border-radius: 6px;
            border-left: 4px solid var(--primary-color);
        }
        
        .teacher-name {
            font-weight: 500;
            color: var(--primary-dark);
        }
        
        .teacher-code {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 2px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-submitted {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-reviewing {
            background: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-cancelled {
            background: var(--text-disabled);
            color: white;
        }
        
        .remarks-area {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-sm);
            min-height: 60px;
            font-size: var(--font-size-base);
            color: var(--text-primary);
            resize: none;
            width: 100%;
            box-sizing: border-box;
        }
        
        .approval-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--margin-sm);
        }
        
        .approval-table th,
        .approval-table td {
            padding: var(--padding-sm);
            text-align: left;
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-small);
        }
        
        .approval-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .approval-table td {
            color: var(--text-primary);
        }
        
        .approval-result {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .result-approved {
            background: var(--success-color);
            color: white;
        }
        
        .result-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .result-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .empty-approval {
            text-align: center;
            padding: var(--padding-lg);
            color: var(--text-secondary);
        }
        
        .empty-approval i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .close-button {
            position: fixed;
            bottom: var(--margin-md);
            left: var(--margin-md);
            right: var(--margin-md);
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-base);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .close-button:hover {
            background: var(--primary-dark);
        }
        
        @media (max-width: 480px) {
            .approval-table {
                font-size: var(--font-size-mini);
            }
            
            .approval-table th,
            .approval-table td {
                padding: 6px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">申请详情</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 详情头部 -->
        <div class="detail-header">
            <div class="detail-title">申请详情</div>
            <div class="detail-subtitle">${sy[0][1]}</div>
        </div>
        
        <!-- 基本信息 -->
        <div class="info-section">
            <div class="section-header">
                <div class="section-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    基本信息
                </div>
            </div>
            <div class="section-content">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">导师类型</div>
                        <div class="info-value">
                            <c:forEach var="dslx" items="${dslxs}">
                                <c:if test="${sqb.dslxdm == dslx[0]}">${dslx[1]}</c:if>
                            </c:forEach>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">导师</div>
                        <div class="info-value">
                            <div class="teacher-info">
                                <div class="teacher-name">${ds.jsm}</div>
                                <div class="teacher-code">教师号：${ds.jsh}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">备注</div>
                        <div class="info-value">
                            <textarea class="remarks-area" readonly>${sqb.bz}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 申请信息 -->
        <div class="info-section">
            <div class="section-header">
                <div class="section-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    申请信息
                </div>
            </div>
            <div class="section-content">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">申请人</div>
                        <div class="info-value">
                            <cache:get var="xjb" region="xs_xjb" key="${eaApplys.user_code}" keyName="xh" targetprop="xm" out="true"/>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">申请时间</div>
                        <div class="info-value">
                            ${fn:substring(eaApplys.commit_dt, 0, 4)}-${fn:substring(eaApplys.commit_dt, 4, 6)}-${fn:substring(eaApplys.commit_dt, 6, 8)} ${fn:substring(eaApplys.commit_dt, 9, 11)}:${fn:substring(eaApplys.commit_dt, 11, 13)}:${fn:substring(eaApplys.commit_dt, 13, 15)}
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">申请状态</div>
                        <div class="info-value">
                            <c:choose>
                                <c:when test="${eaApplys.apply_status == '-1'}">
                                    <span class="status-badge status-cancelled">撤销</span>
                                </c:when>
                                <c:when test="${eaApplys.apply_status == '0'}">
                                    <span class="status-badge status-draft">待提交</span>
                                </c:when>
                                <c:when test="${eaApplys.apply_status == '1'}">
                                    <span class="status-badge status-submitted">已提交</span>
                                </c:when>
                                <c:when test="${eaApplys.apply_status == '2'}">
                                    <span class="status-badge status-reviewing">审批中</span>
                                </c:when>
                                <c:when test="${eaApplys.apply_status == '3'}">
                                    <span class="status-badge status-approved">审批结束</span>
                                </c:when>
                                <c:otherwise>
                                    <span class="status-badge status-draft">其他</span>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 审批信息 -->
        <div class="info-section">
            <div class="section-header">
                <div class="section-title">
                    <i class="ace-icon fa fa-check-circle"></i>
                    审批信息
                </div>
            </div>
            <div class="section-content">
                <c:choose>
                    <c:when test="${not empty eaResult}">
                        <div style="overflow-x: auto;">
                            <table class="approval-table">
                                <thead>
                                    <tr>
                                        <th>审批角色</th>
                                        <th>审批人</th>
                                        <th>审批结果</th>
                                        <th>审批意见</th>
                                        <th>审批时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <c:forEach var="re" items="${eaResult}">
                                        <tr>
                                            <td>${re[0]}</td>
                                            <td>${re[1]}</td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${re[2] == '批准'}">
                                                        <span class="approval-result result-approved">${re[2]}</span>
                                                    </c:when>
                                                    <c:when test="${re[2] == '拒绝'}">
                                                        <span class="approval-result result-rejected">${re[2]}</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span class="approval-result result-pending">${re[2]}</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td>${re[3]}</td>
                                            <td>${re[4]}</td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </c:when>
                    <c:otherwise>
                        <div class="empty-approval">
                            <i class="ace-icon fa fa-clock-o"></i>
                            <div>暂无审批信息</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
        
        <!-- 底部留白，避免被固定按钮遮挡 -->
        <div style="height: 80px;"></div>
        
        <!-- 关闭按钮 -->
        <button class="close-button" onclick="goBack();">
            <i class="ace-icon fa fa-times"></i>
            <span>关闭</span>
        </button>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 返回上一页
        function goBack() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                history.back();
            }
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const buttonHeight = $('.close-button').outerHeight();
            const containerHeight = windowHeight - navbarHeight - buttonHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
