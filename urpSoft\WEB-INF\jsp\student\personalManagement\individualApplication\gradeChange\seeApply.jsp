<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://www.urpSoft.com/cache" prefix="cache"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<html>
	<head>
	    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	    <title>申请信息</title>
	</head>
	<body>
		<div class="row">
			<div class="col-sm-12 self-margin">
	            <div class="modal-header no-padding">
	                <div class="table-header">
	                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
	                        <span class="white">×</span>
	                    </button>
	                    <i class="fa fa-info-circle"></i> 详细信息
	                </div>
	            </div>
	            <div class="modal-body" style="margin-top: 0;">
	                <div class="widget-content" style="height: calc(100vh - 200px); overflow-y:auto;">
	                    <h4 class="header smaller lighter grey" style="margin-top: 0;">
							<i class="fa fa-info-circle"></i> 申请信息
						</h4>
						<div class=" profile-user-info profile-user-info-striped self">
							<div class="profile-info-row">
								<div class="profile-info-name" style="width: 150px;"> 成绩学年学期</div>
								<div class="profile-info-value">
									<cache:get var="zxjxjhm" key="${empty sqxx ? '' : sqxx[1]}" keyName="zxjxjhh" region="jh_zxjxjhb_view" targetprop="zxjxjhm" out="true"/>
								</div>
							</div>
							
							<div class="profile-info-row">
								<div class="profile-info-name"> 课程课序</div>
								<div class="profile-info-value">
									<span id="edit_kckx"> <c:if test="${not empty sqxx}">【${sqxx[3]}_${sqxx[5]}】${sqxx[4]}</c:if></span>
								</div>
							</div>
							<%--<c:forEach items="${colList }" var="sysColConfig" varStatus="i">--%>
								<%--<div class="profile-info-row">--%>
									<%--<div class="profile-info-name">${sysColConfig.colname }</div>--%>
									<%--<div class="profile-info-value">--%>
										<%--${(sysColConfig.colid == 'c1' ? sqxx[9] : (sysColConfig.colid == 'c2' ? sqxx[10] : (sysColConfig.colid == 'c3' ? sqxx[11] : --%>
													<%--(sysColConfig.colid == 'c4' ? sqxx[12] : (sysColConfig.colid == 'c5' ? sqxx[13] : '')))))}--%>
									<%--</div>--%>
								<%--</div>--%>
							<%--</c:forEach>--%>
							
							<div class="profile-info-row">
								<div class="profile-info-name"> 申请原因</div>
								<div class="profile-info-value">
									${sqxx[6]}
								</div>
							</div>
							
							<div class="profile-info-row">
								<div class="profile-info-name">附件</div>
								<div class="profile-info-value">
									<c:forEach items="${fjs}" var="fj">
										<a target="_blank" href="/student/application/gradeChange/downLoad/${fj[0]}">${fj[1]}</a><br>
									</c:forEach>
								</div>
							</div>
						</div>
						<h4 class="header smaller lighter grey">
							<i class="glyphicon glyphicon-list"></i> 申请信息
						</h4>
						<div class="profile-user-info profile-user-info-striped self">
							<div class="profile-info-row">
	                            <div class="profile-info-name">申请人</div>
	                            <div class="profile-info-value">
	                               <cache:get var="xjb" region="xs_xjb" key="${eaApplys.user_code}" keyName="xh" targetprop="xm" out="true"/>
	                            </div>
	                            <div class="profile-info-name">申请时间</div>
	                            <div class="profile-info-value">
									${fn:substring(eaApplys.commit_dt, 0, 4)}-${fn:substring(eaApplys.commit_dt, 4, 6)}-${fn:substring(eaApplys.commit_dt, 6, 8)} ${fn:substring(eaApplys.commit_dt, 8, 10)}:${fn:substring(eaApplys.commit_dt, 10, 12)}:${fn:substring(eaApplys.commit_dt, 12, 14)}
	                            </div>
	                            <div class="profile-info-name">申请状态</div>
	                            <div class="profile-info-value">
									${eaApplys.apply_status=='-1' ? '撤销' : (eaApplys.apply_status=='0' ? '待提交' : (eaApplys.apply_status=='1' ? '已提交' : ( eaApplys.apply_status=='2' ? '审批中' : ( eaApplys.apply_status=='3' ? '审批结束' : '其他'))))}
	                            </div>
	                        </div>
						</div>
						<h4 class="header smaller lighter grey">
							<i class="glyphicon glyphicon-list"></i> 审批信息
						</h4>
						<div style="overflow-x: auto;">
							<table class="table table-bordered table-hover">
								<thead>
								<tr class="center">
									<th>审批角色</th>
									<th>审批人</th>
									<th>审批结果</th>
									<th>审批意见</th>
									<th>审批时间</th>
								</tr>
								</thead>
								<tbody>
									<c:forEach var='re' items="${eaResult}">
										<tr>
											<td>${re[0]}</td>
											<td>${re[1]}</td>
											<td>${re[2]}</td>
											<td>${re[3]}</td>
											<td>${re[4]}</td>
										</tr>
									</c:forEach>
								</tbody>
							</table>
						</div>
					</div>
	            </div>
	        </div>
	    </div>
		<div class="modal-footer no-margin-top ">
	         <div class="center">
				<button title='关闭' id="butn" class="btn btn-xs btn-round" data-dismiss="modal" type="reset">
	                 <i class="ace-icon fa fa-times bigger-120"></i> 关闭
				</button>
			</div>
		</div>
</body>
</html>