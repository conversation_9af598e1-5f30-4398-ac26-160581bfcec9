package educationalAdministration.student.individualApplication.applyCommon.service;

import java.util.List;
import java.util.Map;

import org.springframework.ui.Model;

import com.urpSoft.core.service.IBaseService;

import educationalAdministration.dictionary.entity.EaProcess;
import educationalAdministration.dictionary.entity.EaProcessLink;
import educationalAdministration.dictionary.entity.SysColConfig;
import educationalAdministration.dictionary.entity.SysSqfjb;
import educationalAdministration.dictionary.entity.SysYwhdkzb;
import educationalAdministration.dictionary.entity.XsYwsqxzb;
import educationalAdministration.student.courseSelectMangement.entity.XkCxXnxqView;
import educationalAdministration.student.individualApplication.applyCommon.entity.EaResultQu;

public interface ApplyCommonService extends IBaseService {

	int queryEaProcessCount(String apply_type);

	List<SysColConfig> querySysColConfigByTabName(String tabname);

	void doSaveEaResult(String apply_type, String sqbh);

	SysYwhdkzb querySysYwhdkzbById(String id);

	int queryTimeFrame(String id);

	boolean checkXsYwsqxzbByYwid(String id);

	List<XkCxXnxqView> queryXkCxXnxqViewByXh(String xh);

	String queryKctdInfoHtml(String applyId, boolean mobile);

	String queryStudentRetreatClassInfoHtml(String applyId, String applyType);

	String queryStudentByElectionClassInfoHtml(String applyId);

	String queryStudentListenFreeInfoHtml(String applyId);

	String queryStudentExemptionInfoHtml(String applyId);

	List<EaResultQu> queryEaResultByApplyId(String applyId, Model model);

	void doDeleteAllCurriculum(String sqbh);

	List<XsYwsqxzb> queryXsYwsqxzbByYwid(String ywid, String type);

	List<SysYwhdkzb> queryAllSysYwhdkzb(String csmxqt);

	SysSqfjb querySysSqfjbBySqbh(String sqbh);


	/**
	 * 根据申请id获取用户上传的附件表列表
	 *
	 * @param applyId
	 * @author: gdx
	 * @date: 2019/8/7 8:41:18
	 */
	List<SysSqfjb> querySysSqfjbByApplyId(String applyId);

	/**
	 * 根据SQBH和applyType获取申请表的Html信息
	 *
	 * @param applyId
	 * @param applyType
	 * @return
	 * @author: gdx
	 * @date: 2020/2/18 17:29
	 */
	String queryStudentJoinTheArmyInfoHtml(String applyId, String applyType);

	String queryStudentCompetitionExemInfoHtml(String applyId, String applyType);
	
	String queryBusSectionInfoHtml(String applyId, String applyType);
	
	String queryFaxdsqInfoHtml(String applyId, boolean mobile);

	List<Object[]> queryProcessLinkByApplyId(String applyId);

	List<Object[]> queryEaResultsByApplyId(String applyId);
	
	List<Object[]> queryEaRsltParallelByApplyId(String applyId);

	String queryXm(String rollbackUser);

	String queryJsm(String rollbackUser);

	List<Object[]> queryEalByApplyType(String applyType);

	String querySqbhByApply();

	List<EaProcess> queryEaProcess(String apply_type);

	List<EaProcessLink> queryEaProcessLink(String eapCode);

	void doSaveEaResult(String apply_type, String sqbh, String jsh);

	Map<String, Object> queryApprovers(String sql, String apply_type, Object[] names, String sqbh, String zxjxjhh, String others);

	
	Map<String, Object> queryApproversByApplyId(String sql,String sqbh,String apply_type, String zxjxjhh);

	String queryInnovationProjectInfoHtml(String applyId, String applyType);
	
	String queryGraduateStudentHtml(String applyId);

	String queryChangeStudentInfoHtml(String applyId);

	String achievementRecognitionHtml(String applyId);

	String reviewScoreReductionHtml(String applyId);
	
	List<Object[]> queryEaRsltByApplyId(String tableName,String applyId,String applyStatus);

	String destinationApplyHtml(String applyId);
	
	String queryCreditCertificationInfoHtml(Model model, String applyId, boolean mobile);
	
	String queryClassIdentificationInfoHtml(String applyId);

	Map<String, Object> queryApprovers(String sql, String apply_type, Object[] names, String sqbh);


	String queryProcessApplyWhiteList(String tabName, String xh);
	
	String queryTerminationInnovationInfoHtml(String applyId);
	
	String queryExtensionInnovationInfoHtml(String applyId);

	List<XkCxXnxqView> queryXkCxXnxqViewByOne(String xh, String zxjxjhh);

	String queryStudentsDeclareGraduationHtml(String applyId, boolean b);
}