<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jstl/fmt" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>查看课程替代申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 查看课程替代申请页面样式 */
        .view-header {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .view-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .view-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .info-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: var(--margin-md);
        }
        
        .section-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-header i {
            color: var(--info-color);
        }
        
        .info-content {
            padding: var(--padding-md);
        }
        
        .info-row {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .info-row:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }
        
        .info-label {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .info-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .status-reviewing {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-withdrawn {
            background: var(--text-disabled);
            color: white;
        }
        
        .replacement-type {
            display: inline-block;
            padding: 4px 8px;
            background: var(--primary-light);
            color: var(--primary-dark);
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .course-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .course-item {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
        }
        
        .course-name {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .course-details {
            color: var(--text-secondary);
            font-size: var(--font-size-mini);
            margin-top: 4px;
        }
        
        .replaced-course {
            background: var(--error-light);
            border-color: var(--error-color);
        }
        
        .replaced-course .course-name {
            text-decoration: line-through;
            color: var(--error-dark);
        }
        
        .approval-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: var(--margin-md);
        }
        
        .approval-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .approval-role-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--info-light);
            color: var(--info-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
        }
        
        .approval-role {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .approval-content {
            padding: var(--padding-md);
        }
        
        .approval-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--margin-md);
        }
        
        .approval-field {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .approval-label {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        .approval-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        .approval-opinion {
            grid-column: 1 / -1;
        }
        
        .opinion-textarea {
            width: 100%;
            min-height: 80px;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-tertiary);
            color: var(--text-primary);
            resize: none;
            box-sizing: border-box;
        }
        
        .back-button {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .btn-back {
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md) var(--padding-lg);
            font-size: var(--font-size-base);
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-back:hover {
            background: var(--info-dark);
        }
        
        @media (max-width: 480px) {
            .approval-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">查看课程替代申请</div>
            <div class="navbar-action">
                <!-- 空白占位 -->
            </div>
        </nav>
        
        <!-- 查看申请头部 -->
        <div class="view-header">
            <div class="view-title">课程替代申请详情</div>
            <div class="view-desc">查看申请信息和审批流程</div>
        </div>
        
        <!-- 申请信息 -->
        <div class="info-section">
            <div class="section-header">
                <i class="ace-icon fa fa-file-text"></i>
                申请信息
            </div>
            
            <div class="info-content">
                <div class="info-row">
                    <div class="info-label">申请日期</div>
                    <div class="info-value">${cjKctdSqb.sqrq}</div>
                </div>
                
                <div class="info-row">
                    <div class="info-label">替代类型</div>
                    <div class="info-value">
                        <span class="replacement-type">
                            <c:if test="${cjKctdSqb.tdlx == '01'}">一替一</c:if>
                            <c:if test="${cjKctdSqb.tdlx == '02'}">一替多</c:if>
                            <c:if test="${cjKctdSqb.tdlx == '03'}">多替一</c:if>
                            <c:if test="${cjKctdSqb.tdlx == '04'}">主辅修学分认定</c:if>
                        </span>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-label">申请状态</div>
                    <div class="info-value">
                        <span class="status-badge 
                            <c:if test='${cjKctdSqb.sqzt == "-1"}'>status-rejected</c:if>
                            <c:if test='${cjKctdSqb.sqzt == "-2"}'>status-withdrawn</c:if>
                            <c:if test='${cjKctdSqb.sqzt == "0"}'>status-pending</c:if>
                            <c:if test='${cjKctdSqb.sqzt == "1"}'>status-reviewing</c:if>
                            <c:if test='${cjKctdSqb.sqzt == "2"}'>status-approved</c:if>
                        ">
                            <c:if test="${cjKctdSqb.sqzt == '-1'}">不批准</c:if>
                            <c:if test="${cjKctdSqb.sqzt == '-2'}">已撤回</c:if>
                            <c:if test="${cjKctdSqb.sqzt == '0'}">待审批</c:if>
                            <c:if test="${cjKctdSqb.sqzt == '1'}">审批中</c:if>
                            <c:if test="${cjKctdSqb.sqzt == '2'}">已批准</c:if>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 课程信息 -->
        <div class="info-section">
            <div class="section-header">
                <i class="ace-icon fa fa-book"></i>
                课程信息
            </div>
            
            <div class="info-content">
                <div class="info-row">
                    <div class="info-label">替代课程</div>
                    <div class="info-value">
                        <div class="course-list">
                            <c:forEach items="${kchList}" var="course">
                                <div class="course-item">
                                    <div class="course-name">${course[1]}</div>
                                    <div class="course-details">课程号：${course[0]} | 学分：${course[2]}</div>
                                </div>
                            </c:forEach>
                        </div>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-label">被替代课程</div>
                    <div class="info-value">
                        <div class="course-list">
                            <c:forEach items="${tdkchList}" var="course">
                                <div class="course-item replaced-course">
                                    <div class="course-name">${course[1]}</div>
                                    <div class="course-details">课程号：${course[0]} | 学分：${course[2]}</div>
                                </div>
                            </c:forEach>
                        </div>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-label">申请原因</div>
                    <div class="info-value">${cjKctdSqb.sqyy}</div>
                </div>
            </div>
        </div>
        
        <!-- 审批流程 -->
        <c:forEach items="${spjlbList}" var="approval" varStatus="status">
            <div class="approval-item">
                <div class="approval-header">
                    <div class="approval-role-icon">${status.index + 1}</div>
                    <div class="approval-role">
                        <c:if test="${approval[0] == '1'}">学生院系教学秘书</c:if>
                        <c:if test="${approval[0] == '2'}">学生院系主管领导</c:if>
                        <c:if test="${approval[0] == '3'}">开课院系教学秘书</c:if>
                        <c:if test="${approval[0] == '4'}">开课院系主管领导</c:if>
                        <c:if test="${approval[0] == '5'}">教务处</c:if>
                    </div>
                </div>
                
                <div class="approval-content">
                    <div class="approval-info">
                        <div class="approval-field">
                            <div class="approval-label">审批院系</div>
                            <div class="approval-value">${approval[1]}</div>
                        </div>
                        
                        <div class="approval-field">
                            <div class="approval-label">审批人</div>
                            <div class="approval-value">${approval[2]}</div>
                        </div>
                        
                        <div class="approval-field">
                            <div class="approval-label">审批时间</div>
                            <div class="approval-value">
                                <fmt:parseDate var="spsj" value="${approval[5]}" pattern="yyyyMMddHHssmm" />
                                <fmt:formatDate pattern="yyyy-MM-dd HH:ss:mm" value="${spsj}" />
                            </div>
                        </div>
                        
                        <div class="approval-field">
                            <div class="approval-label">审批结果</div>
                            <div class="approval-value">
                                <span class="status-badge 
                                    <c:if test='${approval[3] == "-1"}'>status-rejected</c:if>
                                    <c:if test='${approval[3] == "0"}'>status-pending</c:if>
                                    <c:if test='${approval[3] == "2"}'>status-approved</c:if>
                                ">
                                    <c:if test="${approval[3] == '-1'}">不批准</c:if>
                                    <c:if test="${approval[3] == '0'}">待审批</c:if>
                                    <c:if test="${approval[3] == '2'}">批准</c:if>
                                </span>
                            </div>
                        </div>
                        
                        <div class="approval-field approval-opinion">
                            <div class="approval-label">审批意见</div>
                            <div class="approval-value">
                                <textarea class="opinion-textarea" readonly>${approval[4]}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </c:forEach>
        
        <!-- 返回按钮 -->
        <div class="back-button">
            <button class="btn-back" onclick="returnIndex();">
                <i class="ace-icon fa fa-arrow-left"></i>
                返回列表
            </button>
        </div>
    </div>

    <script>
        $(function() {
            adjustPageHeight();
        });

        // 返回列表页
        function returnIndex() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                window.location.href = "/student/personalManagement/personalApplication/curriculumReplacement/index";
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
