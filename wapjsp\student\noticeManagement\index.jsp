<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>选课公告</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 选课公告页面样式 */
        .quick-actions {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .actions-title i {
            color: var(--primary-color);
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }
        
        .action-button {
            padding: var(--padding-sm);
            background: var(--info-color);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            min-height: 40px;
            transition: all var(--transition-base);
        }
        
        .action-button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .action-button:active {
            transform: translateY(0);
        }
        
        .notice-item {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all var(--transition-base);
            position: relative;
        }
        
        .notice-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .notice-item:active {
            transform: translateY(0);
        }
        
        .notice-indicator {
            position: absolute;
            left: var(--padding-md);
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }
        
        .notice-content {
            margin-left: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .notice-title {
            flex: 1;
            font-size: var(--font-size-base);
            color: var(--text-primary);
            line-height: 1.4;
            margin-right: var(--margin-sm);
            text-decoration: none;
        }
        
        .notice-title:hover {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .notice-date {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            white-space: nowrap;
        }
        
        .empty-notice {
            background: var(--success-color);
            color: white;
            margin: var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-xl);
            text-align: center;
            position: relative;
        }
        
        .empty-notice .close-btn {
            position: absolute;
            top: var(--padding-md);
            right: var(--padding-md);
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color var(--transition-base);
        }
        
        .empty-notice .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            opacity: 0.8;
        }
        
        .empty-message {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
        }
        
        .empty-submessage {
            font-size: var(--font-size-base);
            opacity: 0.9;
        }
        
        .header-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: var(--margin-md);
        }
        
        .header-title i {
            color: var(--warning-color);
        }
        
        @media (max-width: 480px) {
            .actions-grid {
                grid-template-columns: 1fr;
            }
            
            .notice-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }
            
            .notice-date {
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">选课公告</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 页面标题 -->
        <div class="header-section">
            <div class="header-title">
                <i class="ace-icon fa fa-bell"></i>
                选课公告
            </div>
            
            <!-- 快捷操作 -->
            <div class="actions-grid">
                <button class="action-button" onclick="goToCourseSelect();">
                    <i class="ace-icon fa fa-plus"></i>
                    <span>去选课</span>
                </button>
                <button class="action-button" onclick="goToQuitCourse();">
                    <i class="ace-icon fa fa-minus"></i>
                    <span>去退课</span>
                </button>
                <button class="action-button" onclick="goToCourseResult();">
                    <i class="ace-icon fa fa-list"></i>
                    <span>选课结果</span>
                </button>
                <button class="action-button" onclick="goToHome();">
                    <i class="ace-icon fa fa-home"></i>
                    <span>返回首页</span>
                </button>
            </div>
        </div>
        
        <!-- 公告列表 -->
        <c:if test="${list != null && fn:length(list) > 0}">
            <div id="noticeList">
                <c:forEach items="${list}" var="notice" varStatus="noticeIndex">
                    <div class="notice-item" onclick="queryNotice('${notice[0]}');">
                        <div class="notice-indicator"></div>
                        <div class="notice-content">
                            <div class="notice-title">${notice[1]}</div>
                            <div class="notice-date">${notice[2]}</div>
                        </div>
                    </div>
                </c:forEach>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <c:if test="${fn:length(list) == 0}">
            <div class="empty-notice" id="emptyNotice">
                <button type="button" class="close-btn" onclick="closeEmptyNotice();">
                    <i class="ace-icon fa fa-times"></i>
                </button>
                <div class="empty-icon">
                    <i class="ace-icon fa fa-check"></i>
                </div>
                <div class="empty-message">暂时木有内容呀~~</div>
                <div class="empty-submessage">当前没有新的选课公告</div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 查看公告详情
        function queryNotice(tzid) {
            if (parent && parent.addTab) {
                parent.addTab('公告详情', '/student/courseSelect/courseSelectNotice/selectCourseNoticeDetail?tzId=' + tzid);
            } else {
                window.location.href = '/student/courseSelect/courseSelectNotice/selectCourseNoticeDetail?tzId=' + tzid;
            }
        }

        // 去选课
        function goToCourseSelect() {
            if (parent && parent.addTab) {
                parent.addTab('选课', '/student/courseSelect/courseSelect/index');
            } else {
                window.location.href = '/student/courseSelect/courseSelect/index';
            }
        }

        // 去退课
        function goToQuitCourse() {
            if (parent && parent.addTab) {
                parent.addTab('退课', '/student/courseSelect/quitCourse/index');
            } else {
                window.location.href = '/student/courseSelect/quitCourse/index';
            }
        }

        // 查看选课结果
        function goToCourseResult() {
            if (parent && parent.addTab) {
                parent.addTab('选课结果', '/student/courseSelect/courseSelectResult/index');
            } else {
                window.location.href = '/student/courseSelect/courseSelectResult/index';
            }
        }

        // 返回首页
        function goToHome() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                window.location.href = '/';
            }
        }

        // 关闭空状态提示
        function closeEmptyNotice() {
            $('#emptyNotice').fadeOut();
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
