<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>作息时间</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 作息时间页面样式 */
        .schedule-header {
            background: linear-gradient(135deg, var(--success-color), var(--primary-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }
        
        .schedule-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .schedule-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .campus-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: var(--margin-lg);
        }
        
        .campus-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
        }
        
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .schedule-table td {
            border: 1px solid var(--divider-color);
            padding: var(--padding-sm);
            text-align: center;
            vertical-align: middle;
            font-size: var(--font-size-small);
        }
        
        .time-period {
            background: var(--error-light);
            color: var(--error-dark);
            font-weight: 500;
            width: 15%;
            writing-mode: vertical-lr;
            text-orientation: upright;
            font-size: var(--font-size-base);
        }
        
        .class-session {
            width: 35%;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .class-time {
            width: 25%;
            color: var(--text-secondary);
            background: var(--bg-tertiary);
        }
        
        .big-session {
            width: 25%;
            background: var(--primary-light);
            color: var(--primary-dark);
            font-weight: 500;
            font-size: var(--font-size-base);
        }
        
        .error-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .error-alert {
            background: var(--error-light);
            border: 1px solid var(--error-color);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .error-icon {
            color: var(--error-color);
            font-size: var(--font-size-large);
        }
        
        .error-content {
            flex: 1;
        }
        
        .error-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--error-dark);
            margin-bottom: 4px;
        }
        
        .error-message {
            font-size: var(--font-size-small);
            color: var(--error-dark);
        }
        
        .btn-close {
            background: none;
            border: none;
            color: var(--error-color);
            font-size: var(--font-size-base);
            cursor: pointer;
            padding: 4px;
        }
        
        @media (max-width: 480px) {
            .schedule-table td {
                padding: 6px 4px;
                font-size: var(--font-size-mini);
            }
            
            .time-period {
                font-size: var(--font-size-small);
            }
            
            .big-session {
                font-size: var(--font-size-small);
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">作息时间</div>
            <div class="navbar-action">
                <!-- 空白占位 -->
            </div>
        </nav>
        
        <c:if test="${show}">
            <!-- 作息时间头部 -->
            <div class="schedule-header">
                <div class="schedule-title">四川大学本科教学作息时间</div>
                <div class="schedule-desc">查看各校区教学时间安排</div>
            </div>
            
            <!-- 望江、华西校区 -->
            <div class="campus-section">
                <div class="campus-header">望江、华西校区教学时间安排表</div>
                
                <table class="schedule-table">
                    <tbody>
                        <tr>
                            <td rowspan="4" class="time-period">上午</td>
                            <td class="class-session">第01节课</td>
                            <td class="class-time">08:00-08:45</td>
                            <td rowspan="2" class="big-session">第一大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第02节课</td>
                            <td class="class-time">08:55-09:40</td>
                        </tr>
                        <tr>
                            <td class="class-session">第03节课</td>
                            <td class="class-time">10:00-10:45</td>
                            <td rowspan="2" class="big-session">第二大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第04节课</td>
                            <td class="class-time">10:55-11:40</td>
                        </tr>
                        
                        <tr>
                            <td rowspan="5" class="time-period">下午</td>
                            <td class="class-session">第05节课</td>
                            <td class="class-time">14:00-14:45</td>
                            <td rowspan="3" class="big-session">第三大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第06节课</td>
                            <td class="class-time">14:55-15:40</td>
                        </tr>
                        <tr>
                            <td class="class-session">第07节课</td>
                            <td class="class-time">15:50-16:35</td>
                        </tr>
                        <tr>
                            <td class="class-session">第08节课</td>
                            <td class="class-time">16:55-17:40</td>
                            <td rowspan="2" class="big-session">第四大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第09节课</td>
                            <td class="class-time">17:50-18:35</td>
                        </tr>
                        
                        <tr>
                            <td rowspan="3" class="time-period">晚上</td>
                            <td class="class-session">第10节课</td>
                            <td class="class-time">19:30-20:15</td>
                            <td rowspan="3" class="big-session">第五大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第11节课</td>
                            <td class="class-time">20:25-21:10</td>
                        </tr>
                        <tr>
                            <td class="class-session">第12节课</td>
                            <td class="class-time">21:20-22:05</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 江安校区 -->
            <div class="campus-section">
                <div class="campus-header">江安校区教学时间表</div>
                
                <table class="schedule-table">
                    <tbody>
                        <tr>
                            <td rowspan="4" class="time-period">上午</td>
                            <td class="class-session">第01节课</td>
                            <td class="class-time">08:15-09:00</td>
                            <td rowspan="2" class="big-session">第一大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第02节课</td>
                            <td class="class-time">09:10-09:55</td>
                        </tr>
                        <tr>
                            <td class="class-session">第03节课</td>
                            <td class="class-time">10:15-11:00</td>
                            <td rowspan="2" class="big-session">第二大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第04节课</td>
                            <td class="class-time">11:10-11:55</td>
                        </tr>
                        
                        <tr>
                            <td rowspan="5" class="time-period">下午</td>
                            <td class="class-session">第05节课</td>
                            <td class="class-time">13:50-14:35</td>
                            <td rowspan="3" class="big-session">第三大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第06节课</td>
                            <td class="class-time">14:45-15:30</td>
                        </tr>
                        <tr>
                            <td class="class-session">第07节课</td>
                            <td class="class-time">15:40-16:25</td>
                        </tr>
                        <tr>
                            <td class="class-session">第08节课</td>
                            <td class="class-time">16:45-17:30</td>
                            <td rowspan="2" class="big-session">第四大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第09节课</td>
                            <td class="class-time">17:40-18:25</td>
                        </tr>
                        
                        <tr>
                            <td rowspan="3" class="time-period">晚上</td>
                            <td class="class-session">第10节课</td>
                            <td class="class-time">19:20-20:05</td>
                            <td rowspan="3" class="big-session">第五大节</td>
                        </tr>
                        <tr>
                            <td class="class-session">第11节课</td>
                            <td class="class-time">20:15-21:00</td>
                        </tr>
                        <tr>
                            <td class="class-session">第12节课</td>
                            <td class="class-time">21:10-21:55</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </c:if>
        
        <c:if test="${!show}">
            <!-- 错误提示 -->
            <div class="error-container">
                <div class="error-alert">
                    <i class="ace-icon fa fa-exclamation-triangle error-icon"></i>
                    <div class="error-content">
                        <div class="error-title">警告!</div>
                        <div class="error-message">数据有误，请检查数据！</div>
                    </div>
                    <button class="btn-close" onclick="parent.closeFrame();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
            </div>
        </c:if>
    </div>

    <script>
        $(function() {
            adjustPageHeight();
        });

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
