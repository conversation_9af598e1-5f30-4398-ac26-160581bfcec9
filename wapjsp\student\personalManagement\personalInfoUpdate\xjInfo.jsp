<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>个人信息修改</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 个人信息修改页面样式 */
        .info-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .info-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .info-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .form-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-header i {
            color: var(--primary-color);
        }
        
        .form-content {
            padding: var(--padding-lg);
        }
        
        .form-group {
            margin-bottom: var(--margin-lg);
        }
        
        .form-label {
            display: block;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .form-label.required::after {
            content: " *";
            color: var(--error-color);
        }
        
        .form-input {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--transition-base);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-select {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
        }
        
        .form-select:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-help {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 4px;
        }
        
        .form-error {
            font-size: var(--font-size-small);
            color: var(--error-color);
            margin-top: 4px;
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-xl);
            padding-top: var(--padding-md);
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-save {
            flex: 1;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-save:hover {
            background: var(--primary-dark);
        }
        
        .btn-save:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .btn-reset {
            flex: 1;
            background: var(--text-disabled);
            color: white;
            border: none;
            border-radius: 6px;
            padding: var(--padding-md);
            font-size: var(--font-size-base);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-reset:hover {
            background: var(--text-secondary);
        }
        
        .warning-container {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
        }
        
        .warning-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-title i {
            color: var(--warning-color);
        }
        
        .readonly-info {
            background: var(--info-light);
            border: 1px solid var(--info-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--info-dark);
        }
        
        .readonly-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .readonly-title i {
            color: var(--info-color);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .info-item-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .info-item-value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        @media (max-width: 480px) {
            .form-actions {
                flex-direction: column;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">个人信息修改</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 个人信息头部 -->
        <div class="info-header">
            <div class="info-title">个人信息修改</div>
            <div class="info-desc">修改个人联系信息</div>
        </div>
        
        <c:if test="${onOff != '1'}">
            <!-- 评估未完成提示 -->
            <div class="warning-container">
                <div class="warning-title">
                    <i class="ace-icon fa fa-exclamation-triangle"></i>
                    提示
                </div>
                没有完成评估，不能查看个人信息！
            </div>
        </c:if>
        
        <c:if test="${onOff == '1'}">
            <!-- 只读信息展示 -->
            <div class="readonly-info">
                <div class="readonly-title">
                    <i class="ace-icon fa fa-info-circle"></i>
                    基本信息（只读）
                </div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-item-label">学号</div>
                        <div class="info-item-value">${xsGrxxb.xh}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-item-label">姓名</div>
                        <div class="info-item-value">${xsGrxxb.xm}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-item-label">性别</div>
                        <div class="info-item-value">${xsGrxxb.xbm}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-item-label">学院</div>
                        <div class="info-item-value">${xsGrxxb.xsm}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-item-label">专业</div>
                        <div class="info-item-value">${xsGrxxb.zym}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-item-label">班级</div>
                        <div class="info-item-value">${xsGrxxb.bjm}</div>
                    </div>
                </div>
            </div>
            
            <!-- 可修改信息表单 -->
            <div class="form-container">
                <div class="form-header">
                    <i class="ace-icon fa fa-edit"></i>
                    可修改信息
                </div>
                
                <div class="form-content">
                    <form:form name="frm" modelAttribute="xsGrxxb" action="#" method="POST" id="frm1">
                        <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
                        
                        <div class="form-group">
                            <label class="form-label required" for="brlxdh">手机号码</label>
                            <form:input path="brlxdh" id="brlxdh" class="form-input" placeholder="请输入手机号码" maxlength="11"/>
                            <div class="form-help">请输入11位手机号码</div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="dh">宿舍电话</label>
                            <form:input path="dh" id="dh" class="form-input" placeholder="请输入宿舍电话" maxlength="20"/>
                            <div class="form-help">格式：区号-电话号码</div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label required" for="email">邮箱地址</label>
                            <form:input path="email" id="email" class="form-input" placeholder="请输入邮箱地址" maxlength="200"/>
                            <div class="form-help">请输入有效的邮箱地址</div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="txdz">通讯地址</label>
                            <form:input path="txdz" id="txdz" class="form-input" placeholder="请输入通讯地址" maxlength="200"/>
                            <div class="form-help">详细的通讯地址</div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="grzy">个人主页</label>
                            <form:input path="grzy" id="grzy" class="form-input" placeholder="请输入个人主页URL" maxlength="200"/>
                            <div class="form-help">个人网站或博客地址</div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="grjj">个人简介</label>
                            <form:textarea path="grjj" id="grjj" class="form-input form-textarea" placeholder="请输入个人简介" maxlength="200"/>
                            <div class="form-help">简单介绍自己，最多200字</div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn-save" id="loading-btn" onclick="doSave();">
                                <i class="ace-icon fa fa-save"></i>
                                保存
                            </button>
                            <button type="button" class="btn-reset" onclick="resettj();">
                                <i class="ace-icon fa fa-undo"></i>
                                重置
                            </button>
                        </div>
                    </form:form>
                </div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 字符串长度计算（中文算2个字符）
        function strlen(str) {
            var len = 0;
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) > 255) {
                    len += 2;
                } else {
                    len++;
                }
            }
            return len;
        }

        // 手机号码验证
        function validatelxdh() {
            var brlxdh = document.getElementById("brlxdh").value;
            var varlen = strlen(brlxdh);
            var isMob = /^1[3456789]\d{9}$/;

            if (varlen == 0 && "${schoolCode}" != "100052" && "${schoolCode}" != "100008") {
                showError("请输入你的手机号码！");
                document.getElementById("brlxdh").focus();
                return false;
            }

            if (varlen != 0) {
                if (!isMob.test(brlxdh)) {
                    showError("手机号码格式不正确！");
                    document.getElementById("brlxdh").focus();
                    return false;
                }
                if (varlen > 11) {
                    showError("手机号码的长度必须为11位！");
                    document.getElementById("brlxdh").focus();
                    return false;
                }
            }
            return true;
        }

        // 电话号码验证
        function validatedh() {
            var dh = document.getElementById("dh").value;
            var varlen = strlen(dh);
            var isMob = /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/;

            if (varlen == 0 && "${schoolCode}" != "100052" && "${schoolCode}" != "100008" && "${schoolCode}" != "100006") {
                showError("请输入你的宿舍电话号码！");
                document.getElementById("dh").focus();
                return false;
            }

            if (varlen != 0 && !isMob.test(dh)) {
                showError("宿舍电话号码格式不正确！");
                document.getElementById("dh").focus();
                return false;
            }
            return true;
        }

        // 邮箱验证
        function validateemail() {
            var email = document.getElementById("email").value;
            var varlen = strlen(email);
            var regEmail = /^([a-z0-9_\.-]+)@([\da-z\.-]+)\.(.[a-z\.]{2,6})$/;

            if (varlen == 0 && "${schoolCode}" != "100052" && "${schoolCode}" != "100008") {
                showError("邮箱地址不允许为空请输入你的地址！");
                document.getElementById("email").focus();
                return false;
            }

            if (varlen != 0) {
                if (!regEmail.test(email)) {
                    showError("Email格式不正确！");
                    document.getElementById("email").focus();
                    return false;
                }
                if (varlen > 200) {
                    showError("你的邮箱长度必须小于200位！");
                    document.getElementById("email").focus();
                    return false;
                }
            }
            return true;
        }

        // 个人主页验证
        function validategrzy() {
            var grzy = document.getElementById("grzy").value;
            var varlen = strlen(grzy);

            if (varlen > 200) {
                showError("你个人主页的内容长度不允许大于200位！");
                return false;
            }
            return true;
        }

        // 个人简介验证
        function validategrjj() {
            var grjj = document.getElementById("grjj").value;
            var varlen = strlen(grjj);

            if (varlen > 200) {
                showError("你个人简介内容长度不允许大于200位！");
                document.getElementById("grjj").focus();
                return false;
            }
            return true;
        }

        // 保存信息
        function doSave() {
            if (validatelxdh() && validatedh() && validateemail() && validategrzy() && validategrjj()) {
                var frm = document.getElementById("frm1");
                var pageConditions = $(frm).serialize();

                showLoading(true);
                $("#loading-btn").prop('disabled', true);

                $.ajax({
                    url: "/student/rollManagement/personalInfoUpdate/update",
                    cache: false,
                    type: "post",
                    data: pageConditions,
                    success: function(d) {
                        if (d["result"].indexOf("/logout") != -1) {
                            window.location.href = d["result"];
                        } else {
                            if (d["result"] == "success") {
                                showSuccess("保存成功！", function() {
                                    if (parent && parent.closeFrame) {
                                        parent.closeFrame();
                                    } else {
                                        location.href = "/student/rollManagement/personalInfoUpdate/index";
                                    }
                                });
                            } else {
                                showError("保存失败！");
                            }
                        }
                        $("#tokenValue").val(d["token"]);
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                        $("#loading-btn").prop('disabled', false);
                    }
                });
            }
            return false;
        }

        // 重置表单
        function resettj() {
            if (confirm("确定要重置所有修改的信息吗？")) {
                document.getElementById("dh").value = "";
                document.getElementById("txdz").value = "";
                document.getElementById("email").value = "";
                document.getElementById("grjj").value = "";
                document.getElementById("grzy").value = "";
                document.getElementById("brlxdh").value = "";
            }
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            alert(message);
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            alert(message);
            if (callback) {
                callback();
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
