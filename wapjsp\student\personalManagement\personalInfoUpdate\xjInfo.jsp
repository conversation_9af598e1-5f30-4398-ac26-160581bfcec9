<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isELIgnored="false" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<%@page import="educationalAdministration.student.common.utils.CommonUtils" %>
<%@page import="educationalAdministration.student.personalManagement.entity.PxCsb" %>
<%@ page import="org.apache.commons.lang.StringUtils" %>

<% session.setAttribute("emptyString", "<span style='color:#DE4C5B;font-style: italic;'>Empty</span>"); %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>个人信息修改</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 个人信息修改页面样式 - 移动端优化 */
        .info-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .info-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }

        .info-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }

        /* 表单区域样式 */
        .form-section-mobile {
            margin-bottom: var(--margin-lg);
        }

        .section-title-mobile {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 2px solid var(--primary-light);
        }

        .section-title-mobile i {
            font-size: 18px;
        }

        .form-group-mobile {
            margin-bottom: var(--margin-lg);
        }

        .form-label-mobile {
            display: block;
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }

        .form-label-mobile.required::after {
            content: " *";
            color: var(--error-color);
            font-weight: bold;
        }

        .form-input-mobile {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--transition-base);
            box-sizing: border-box;
        }

        .form-input-mobile:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-input-mobile:disabled {
            background: var(--bg-tertiary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }

        .form-textarea-mobile {
            min-height: 80px;
            resize: vertical;
            font-family: inherit;
        }

        .form-select-mobile {
            width: 100%;
            padding: var(--padding-md);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            box-sizing: border-box;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }

        .form-select-mobile:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-help-mobile {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: 4px;
            line-height: 1.4;
        }

        .form-error-mobile {
            font-size: var(--font-size-small);
            color: var(--error-color);
            margin-top: 4px;
            line-height: 1.4;
        }

        .form-actions-mobile {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-xl);
            padding-top: var(--padding-lg);
            border-top: 1px solid var(--divider-color);
        }

        .info-grid-mobile {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
        }

        .info-item-mobile {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm) 0;
            border-bottom: 1px solid var(--divider-light);
        }

        .info-item-mobile:last-child {
            border-bottom: none;
        }

        .info-item-mobile .label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            flex: 0 0 80px;
        }

        .info-item-mobile .value {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            flex: 1;
            text-align: right;
            word-break: break-all;
        }

        /* 警告和提示样式 */
        .warning-container {
            background: var(--warning-light);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--padding-md);
            margin: var(--margin-sm) var(--margin-md);
            color: var(--warning-dark);
        }

        .warning-title {
            font-weight: 600;
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .warning-title i {
            color: var(--warning-color);
        }

        /* 加载状态 */
        .loading-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            padding: var(--padding-lg);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            display: none;
            text-align: center;
        }

        .loading-container i {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: var(--margin-sm);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .form-actions-mobile {
                flex-direction: column;
            }

            .info-grid-mobile {
                grid-template-columns: 1fr;
            }

            .info-item-mobile {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .info-item-mobile .label {
                flex: none;
            }

            .info-item-mobile .value {
                text-align: left;
            }
        }

        /* 禁用状态样式 */
        .form-disabled {
            opacity: 0.6;
            pointer-events: none;
        }

        /* 身份证验证错误提示 */
        .id-card-error {
            color: var(--error-color);
            font-size: var(--font-size-small);
            margin-top: 4px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">个人信息修改</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 个人信息头部 -->
        <div class="info-header">
            <div class="info-title">个人信息修改</div>
            <div class="info-desc">修改个人联系信息</div>
        </div>
        
        <c:if test="${onOff != '1'}">
            <!-- 评估未完成提示 -->
            <div class="warning-container">
                <div class="warning-title">
                    <i class="ace-icon fa fa-exclamation-triangle"></i>
                    提示
                </div>
                没有完成评估，不能查看个人信息！
            </div>
        </c:if>
        
        <c:if test="${onOff == '1'}">
            <!-- 学生基本信息（只读） -->
            <div class="card-mobile">
                <div class="card-header">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息（只读）</span>
                </div>
                <div class="card-body">
                    <div class="info-grid-mobile">
                        <div class="info-item-mobile">
                            <span class="label">学号：</span>
                            <span class="value">${studentRoll.StudentNo}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">姓名：</span>
                            <span class="value">${studentRoll.Name}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">性别：</span>
                            <span class="value">${studentRoll.SexName}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">学院：</span>
                            <span class="value">${studentRoll.CollegeName}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">专业：</span>
                            <span class="value">${studentRoll.SubjectName}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">班级：</span>
                            <span class="value">${studentRoll.ClassName}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">年级：</span>
                            <span class="value">${studentRoll.GradeName}</span>
                        </div>
                        <div class="info-item-mobile">
                            <span class="label">学制：</span>
                            <span class="value">${studentRoll.EducationLength}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 个人信息修改表单 -->
            <div class="card-mobile">
                <div class="card-header">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>个人信息修改</span>
                </div>
                <div class="card-body">
                    <form name="frm" action="#" method="POST" id="frm1">
                        <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>

                        <!-- 个人联系信息 -->
                        <div class="form-section-mobile">
                            <div class="section-title-mobile">
                                <i class="ace-icon fa fa-phone"></i>
                                <span>联系信息</span>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile required">手机号码</label>
                                <input type="text" id="brlxdh" name="brlxdh" class="form-input-mobile"
                                       value="${xsGrxxb.brlxdh}" placeholder="请输入手机号码" maxlength="11"/>
                                <div class="form-help-mobile">请输入11位手机号码</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">宿舍电话</label>
                                <input type="text" id="dh" name="dh" class="form-input-mobile"
                                       value="${xsGrxxb.dh}" placeholder="请输入宿舍电话" maxlength="20"/>
                                <div class="form-help-mobile">格式：区号-电话号码</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile required">邮箱地址</label>
                                <input type="email" id="email" name="email" class="form-input-mobile"
                                       value="${xsGrxxb.email}" placeholder="请输入邮箱地址" maxlength="200"/>
                                <div class="form-help-mobile">请输入有效的邮箱地址</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">通讯地址</label>
                                <input type="text" id="txdz" name="txdz" class="form-input-mobile"
                                       value="${xsGrxxb.txdz}" placeholder="请输入通讯地址" maxlength="200"/>
                                <div class="form-help-mobile">详细的通讯地址</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">个人主页</label>
                                <input type="url" id="grzy" name="grzy" class="form-input-mobile"
                                       value="${xsGrxxb.grzy}" placeholder="请输入个人主页URL" maxlength="200"/>
                                <div class="form-help-mobile">个人网站或博客地址</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">个人简介</label>
                                <textarea id="grjj" name="grjj" class="form-input-mobile form-textarea-mobile"
                                          placeholder="请输入个人简介" maxlength="200">${xsGrxxb.grjj}</textarea>
                                <div class="form-help-mobile">简单介绍自己，最多200字</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">QQ号</label>
                                <input type="text" id="qqh" name="qqh" class="form-input-mobile"
                                       value="${xsGrxxb.qqh}" placeholder="请输入QQ号" maxlength="20"/>
                                <div class="form-help-mobile">QQ号码</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">身高(cm)</label>
                                <input type="number" id="grsg" name="grsg" class="form-input-mobile"
                                       value="${xsGrxxb.grsg}" placeholder="请输入身高" min="100" max="250"/>
                                <div class="form-help-mobile">身高，单位：厘米</div>
                            </div>

                            <div class="form-group-mobile">
                                <label class="form-label-mobile">体重(kg)</label>
                                <input type="number" id="grtz" name="grtz" class="form-input-mobile"
                                       value="${xsGrxxb.grtz}" placeholder="请输入体重" min="30" max="200"/>
                                <div class="form-help-mobile">体重，单位：公斤</div>
                            </div>
                        </div>

                        <!-- 学籍信息修改 -->
                        <c:if test="${fn:length(codeXjWhxgxxList) > 0}">
                            <div class="form-section-mobile">
                                <div class="section-title-mobile">
                                    <i class="ace-icon fa fa-graduation-cap"></i>
                                    <span>学籍信息修改</span>
                                </div>

                                <c:forEach items="${codeXjWhxgxxList}" var="codeXjWhxgxx" varStatus="status">
                                    <!-- 院系选择 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'XSH'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">院系</label>
                                            <select id="select_xsh" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile" onchange="toSelectSubject();">
                                                <option value="">请选择院系</option>
                                                <c:forEach items="${xsbs}" var="xsb">
                                                    <option value="${xsb.xsh}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == xsb.xsh}">selected</c:if>>
                                                        ${xsb.xsm}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_xsh" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 专业选择 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'ZYH'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">专业</label>
                                            <select id="select_zyh" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile" onchange="toSelectDirection();">
                                                <option value="">请选择专业</option>
                                                <c:forEach items="${zybs}" var="zyb">
                                                    <option value="${zyb.zyh}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == zyb.zyh}">selected</c:if>>
                                                        ${zyb.zym}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_zyh" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 专业方向选择 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'ZYFXH'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">专业方向</label>
                                            <select id="select_zyfxh" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile" onchange="toSelectProfessionalDirection();">
                                                <option value="">请选择专业方向</option>
                                                <c:forEach items="${zyfxbs}" var="zyfxb">
                                                    <option value="${zyfxb.id.zyfxh}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == zyfxb.id.zyfxh}">selected</c:if>>
                                                        ${zyfxb.zyfxm}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_zyfxh" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 班级选择 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'BJH'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">班级</label>
                                            <select id="select_bjh" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile">
                                                <option value="">请选择班级</option>
                                                <c:forEach items="${bjbs}" var="bjb">
                                                    <option value="${bjb.classCode}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == bjb.classCode}">selected</c:if>>
                                                        ${bjb.className}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_bjh" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 校区选择 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'XQH'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">校区</label>
                                            <select id="select_xqh" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile">
                                                <option value="">请选择校区</option>
                                                <c:forEach items="${xaqbs}" var="xaqb">
                                                    <option value="${xaqb.xqh}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == xaqb.xqh}">selected</c:if>>
                                                        ${xaqb.xqm}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_xqh" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 辅修专业 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'FXZYH'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">辅修专业</label>
                                            <select id="select_fxzyh" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile">
                                                <option value="">请选择辅修专业</option>
                                                <cache:query var="Zybs" region="code_zyb"/>
                                                <c:forEach items="${Zybs}" var="zyb">
                                                    <option value="${zyb.zyh}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == zyb.zyh}">selected</c:if>>
                                                        ${zyb.zym}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_fxzyh" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 第二学位专业 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'DEXWZYH'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">第二学位专业</label>
                                            <select id="select_dexwzyh" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile">
                                                <option value="">请选择第二学位专业</option>
                                                <cache:query var="Zybs" region="code_zyb"/>
                                                <c:forEach items="${Zybs}" var="zyb">
                                                    <option value="${zyb.zyh}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == zyb.zyh}">selected</c:if>>
                                                        ${zyb.zym}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_dexwzyh" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 学生类别 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'XSLBDM'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">学生类别</label>
                                            <select id="select_xslbdm" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile">
                                                <option value="">请选择学生类别</option>
                                                <cache:query var="xslbs" region="code_xslb"/>
                                                <c:forEach items="${xslbs}" var="xslb">
                                                    <option value="${xslb.xslbdm}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == xslb.xslbdm}">selected</c:if>>
                                                        ${xslb.xslbsm}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_xslbdm" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 学籍状态 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'XJZTDM'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">学籍状态</label>
                                            <select id="select_xjztdm" name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                    class="form-select-mobile">
                                                <option value="">请选择学籍状态</option>
                                                <cache:query var="xjztbs" region="code_xjztb"/>
                                                <c:forEach items="${xjztbs}" var="xjztb">
                                                    <option value="${xjztb.xjztdm}"
                                                            <c:if test="${codeXjWhxgxx.fldValue == xjztb.xjztdm}">selected</c:if>>
                                                        ${xjztb.xjztsm}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="hidden_xjztdm" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>

                                    <!-- 入学日期 -->
                                    <c:if test="${codeXjWhxgxx.id.fldId == 'RXRQ'}">
                                        <div class="form-group-mobile">
                                            <label class="form-label-mobile">入学日期</label>
                                            <input type="date" id="input_calendar_rxrq"
                                                   name="${fn:toLowerCase(codeXjWhxgxx.id.fldId)}"
                                                   class="form-input-mobile" value="${codeXjWhxgxx.fldValue}"/>
                                            <input type="hidden" id="hidden_rxrq" value="${codeXjWhxgxx.fldValue}"/>
                                        </div>
                                    </c:if>
                                </c:forEach>
                            </div>
                        </c:if>

                        <!-- 表单操作按钮 -->
                        <div class="form-actions-mobile">
                            <button type="button" class="btn-mobile btn-primary flex-1" id="loading-btn" onclick="doSave();">
                                <i class="ace-icon fa fa-save"></i>
                                <span>保存修改</span>
                            </button>
                            <button type="button" class="btn-mobile btn-secondary flex-1" onclick="resettj();">
                                <i class="ace-icon fa fa-undo"></i>
                                <span>重置</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </c:if>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        var disabled = true;

        $(function() {
            initPage();
            initFormValidation();
            checkEditPermission();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();

            // 设置表单禁用状态
            if ("${schoolCode}" == "100038") {
                disabled = false;
            } else {
                if ("${xsXjbTemp.xxzqf}" == "1" || "${xsXjbTemp.xxzqf}" == "0") {
                    disabled = false;
                } else {
                    disabled = true;
                    $('.form-input-mobile, .form-select-mobile, .form-textarea-mobile').prop('disabled', true);
                    $('#frm1').addClass('form-disabled');
                }
            }
        }

        // 初始化表单验证
        function initFormValidation() {
            // 实时验证手机号
            $('#brlxdh').on('blur', function() {
                validatelxdh();
            });

            // 实时验证电话
            $('#dh').on('blur', function() {
                validatedh();
            });

            // 实时验证邮箱
            $('#email').on('blur', function() {
                validateemail();
            });

            // 实时验证个人主页
            $('#grzy').on('blur', function() {
                validategrzy();
            });

            // 实时验证个人简介
            $('#grjj').on('blur', function() {
                validategrjj();
            });
        }

        // 检查编辑权限
        function checkEditPermission() {
            if (disabled) {
                showToast('当前不允许修改个人信息');
            }
        }

        // 字符串长度计算（中文算2个字符）
        function strlen(str) {
            var len = 0;
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) > 255) {
                    len += 2;
                } else {
                    len++;
                }
            }
            return len;
        }

        // 手机号码验证
        function validatelxdh() {
            var brlxdh = document.getElementById("brlxdh").value;
            var varlen = strlen(brlxdh);
            var isMob = /^1[3456789]\d{9}$/;

            if (varlen == 0 && "${schoolCode}" != "100052" && "${schoolCode}" != "100008") {
                showError("请输入你的手机号码！");
                document.getElementById("brlxdh").focus();
                return false;
            }

            if (varlen != 0) {
                if (!isMob.test(brlxdh)) {
                    showError("手机号码格式不正确！");
                    document.getElementById("brlxdh").focus();
                    return false;
                }
                if (varlen > 11) {
                    showError("手机号码的长度必须为11位！");
                    document.getElementById("brlxdh").focus();
                    return false;
                }
            }
            return true;
        }

        // 电话号码验证
        function validatedh() {
            var dh = document.getElementById("dh").value;
            var varlen = strlen(dh);
            var isMob = /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/;

            if (varlen == 0 && "${schoolCode}" != "100052" && "${schoolCode}" != "100008" && "${schoolCode}" != "100006") {
                showError("请输入你的宿舍电话号码！");
                document.getElementById("dh").focus();
                return false;
            }

            if (varlen != 0 && !isMob.test(dh)) {
                showError("宿舍电话号码格式不正确！");
                document.getElementById("dh").focus();
                return false;
            }
            return true;
        }

        // 邮箱验证
        function validateemail() {
            var email = document.getElementById("email").value;
            var varlen = strlen(email);
            var regEmail = /^([a-z0-9_\.-]+)@([\da-z\.-]+)\.(.[a-z\.]{2,6})$/;

            if (varlen == 0 && "${schoolCode}" != "100052" && "${schoolCode}" != "100008") {
                showError("邮箱地址不允许为空请输入你的地址！");
                document.getElementById("email").focus();
                return false;
            }

            if (varlen != 0) {
                if (!regEmail.test(email)) {
                    showError("Email格式不正确！");
                    document.getElementById("email").focus();
                    return false;
                }
                if (varlen > 200) {
                    showError("你的邮箱长度必须小于200位！");
                    document.getElementById("email").focus();
                    return false;
                }
            }
            return true;
        }

        // 个人主页验证
        function validategrzy() {
            var grzy = document.getElementById("grzy").value;
            var varlen = strlen(grzy);

            if (varlen > 200) {
                showError("你个人主页的内容长度不允许大于200位！");
                return false;
            }
            return true;
        }

        // 个人简介验证
        function validategrjj() {
            var grjj = document.getElementById("grjj").value;
            var varlen = strlen(grjj);

            if (varlen > 200) {
                showError("你个人简介内容长度不允许大于200位！");
                document.getElementById("grjj").focus();
                return false;
            }
            return true;
        }

        // 根据院系号查询专业
        function toSelectSubject() {
            var xsh = document.getElementById("select_xsh").value;
            if (!xsh) return;

            showLoading(true);

            $.ajax({
                url: "/student/rollManagement/personalInfoUpdate/searchSubject",
                cache: false,
                type: "post",
                data: "xsh=" + xsh,
                dataType: "json",
                success: function(data) {
                    subjectOption(data);
                    // 清空专业方向和班级
                    $("#select_zyfxh").html('<option value="">请选择专业方向</option>');
                    $("#select_bjh").html('<option value="">请选择班级</option>');
                },
                error: function() {
                    showToast('获取专业信息失败');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 根据专业号查询专业方向和班级
        function toSelectDirection() {
            var subject = document.getElementById("select_zyh").value;
            var xsh = document.getElementById("select_xsh").value;
            if (!subject || !xsh) return;

            showLoading(true);

            $.ajax({
                url: "/student/rollManagement/personalInfoUpdate/toSelectDirection",
                cache: false,
                type: "post",
                data: "subject=" + subject + "&xsh=" + xsh,
                dataType: "json",
                success: function(data) {
                    directionOption(data["directionList"]);
                    classOption(data["bjList"]);
                },
                error: function() {
                    showToast('获取专业方向信息失败');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 根据专业方向获取班级
        function toSelectProfessionalDirection() {
            var xsh = document.getElementById("select_xsh").value;
            var subject = document.getElementById("select_zyh").value;
            var zyfxh = document.getElementById("select_zyfxh").value;
            if (!xsh || !subject || !zyfxh) return;

            showLoading(true);

            $.ajax({
                url: "/student/rollManagement/personalInfoUpdate/toSelectProfessionalDirection",
                cache: false,
                type: "post",
                data: "subject=" + subject + "&xsh=" + xsh + "&zyfxh=" + zyfxh,
                dataType: "json",
                success: function(data) {
                    classOption(data["bjList"]);
                },
                error: function() {
                    showToast('获取班级信息失败');
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 生成专业选项
        function subjectOption(data) {
            var sOption = "<option value=''>请选择专业</option>";
            $.each(data, function(i, v) {
                sOption += "<option value='" + v.subjectCode + "'>" + v.subjectName + "</option>";
            });
            $("#select_zyh").html(sOption);
        }

        // 生成专业方向选项
        function directionOption(data) {
            var dOption = "<option value=''>请选择专业方向</option>";
            $.each(data, function(i, v) {
                dOption += "<option value='" + v.id.zyfxh + "'>" + v.zyfxm + "</option>";
            });
            $("#select_zyfxh").html(dOption);
        }

        // 生成班级选项
        function classOption(data) {
            var cOption = "<option value=''>请选择班级</option>";
            $.each(data, function(i, v) {
                cOption += "<option value='" + v.classCode + "'>" + v.className + "</option>";
            });
            $("#select_bjh").html(cOption);
        }

        // 保存信息
        function doSave() {
            if (disabled) {
                showToast('当前不允许修改个人信息');
                return false;
            }

            if (validatelxdh() && validatedh() && validateemail() && validategrzy() && validategrjj()) {
                var frm = document.getElementById("frm1");
                var pageConditions = $(frm).serialize();

                showLoading(true);
                $("#loading-btn").prop('disabled', true);

                $.ajax({
                    url: "/student/rollManagement/personalInfoUpdate/update",
                    cache: false,
                    type: "post",
                    data: pageConditions,
                    success: function(d) {
                        if (d["result"].indexOf("/logout") != -1) {
                            window.location.href = d["result"];
                        } else {
                            if (d["result"] == "success") {
                                showSuccess("保存成功！", function() {
                                    if (parent && parent.closeFrame) {
                                        parent.closeFrame();
                                    } else {
                                        location.href = "/student/rollManagement/personalInfoUpdate/index";
                                    }
                                });
                            } else {
                                showError("保存失败！");
                            }
                        }
                        $("#tokenValue").val(d["token"]);
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                        $("#loading-btn").prop('disabled', false);
                    }
                });
            }
            return false;
        }

        // 重置表单
        function resettj() {
            if (disabled) {
                showToast('当前不允许修改个人信息');
                return;
            }

            if (confirm("确定要重置所有修改的信息吗？")) {
                // 重置个人信息
                document.getElementById("dh").value = "";
                document.getElementById("txdz").value = "";
                document.getElementById("email").value = "";
                document.getElementById("grjj").value = "";
                document.getElementById("grzy").value = "";
                document.getElementById("brlxdh").value = "";
                document.getElementById("qqh").value = "";
                document.getElementById("grsg").value = "";
                document.getElementById("grtz").value = "";

                // 重置学籍信息选择框
                $('select[id^="select_"]').each(function() {
                    $(this).val('');
                });

                // 重置日期输入框
                $('input[type="date"]').each(function() {
                    $(this).val('');
                });
            }
        }

        // 刷新数据
        function refreshData() {
            window.location.reload();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                if (typeof urp !== 'undefined' && urp.showLoading) {
                    urp.showLoading();
                } else {
                    $('#loadingState').show();
                }
            } else {
                if (typeof urp !== 'undefined' && urp.hideLoading) {
                    urp.hideLoading();
                } else {
                    $('#loadingState').hide();
                }
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) {
                    callback();
                }
            }
        }

        // 显示提示信息
        function showToast(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 身份证验证函数（与PC端一致）
        function validateIdCard(value) {
            var city = {11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江 ",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北 ",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏 ",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外 "};
            var tip = "";
            var pass = true;

            if(!value || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(value)){
                tip = "身份证号格式错误！";
                return tip;
            } else if(!city[value.substr(0,2)]){
                tip = "地址编码错误！";
                return tip;
            } else {
                //18位身份证需要验证最后一位校验位
                if(value.length == 18){
                    value = value.split('');
                    //∑(ai×Wi)(mod 11)
                    //加权因子
                    var factor = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ];
                    //校验位
                    var parity = [ 1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2 ];
                    var sum = 0;
                    var ai = 0;
                    var wi = 0;
                    for (var i = 0; i < 17; i++) {
                        ai = value[i];
                        wi = factor[i];
                        sum += ai * wi;
                    }
                    var last = parity[sum % 11];
                    if(parity[sum % 11] != value[17]){
                        tip = "校验位错误！";
                        return tip;
                    }
                }
            }
            return "";
        }
    </script>
</body>
</html>
