<?xml version="1.0" encoding="UTF-8"?>
<!-- 建议的pom.xml配置 - 仅包含properties部分的修改建议 -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>

    <name>urpSoft</name>
    <groupId>urpSoft</groupId>
    <artifactId>urpSoft</artifactId>
    <version>1.0-SNAPSHOT</version>

    <!-- 建议的版本配置 -->
    <properties>
        <!-- 核心框架版本 - 与实际lib目录保持一致 -->
        <spring.version>3.2.12.RELEASE</spring.version>
        <hibernate-core.version>4.1.7.Final</hibernate-core.version>
        
        <!-- Web相关 -->
        <javaee-api.version>5.0-1</javaee-api.version>
        <javax.servlet-api.version>3.0.1</javax.servlet-api.version>
        <jstl.version>1.2</jstl.version>
        <jsp-api.version>2.1</jsp-api.version>
        
        <!-- Hibernate相关 -->
        <hibernate-commons-annotations.version>4.0.1.Final</hibernate-commons-annotations.version>
        <hibernate-jpa-2.0-api.version>1.0.1.Final</hibernate-jpa-2.0-api.version>
        <hibernate-validator.version>4.3.0.Final</hibernate-validator.version>
        <hibernate-cglib-repack.version>2.1_3</hibernate-cglib-repack.version>
        
        <!-- AOP相关 -->
        <aspectj.version>1.7.0</aspectj.version>
        <cglib.version>2.2.2</cglib.version>
        <asm.version>3.3.1</asm.version>
        
        <!-- JSON处理 - 统一使用Jackson 2.5.4 -->
        <jackson.version>2.5.4</jackson.version>
        <jackson1.version>1.9.9</jackson1.version> <!-- 保留用于向后兼容 -->
        <gson.version>2.8.6</gson.version>
        <fastjson.version>1.2.83</fastjson.version>
        
        <!-- XML处理 -->
        <xstream.version>1.4.3</xstream.version>
        
        <!-- 缓存相关 -->
        <ehcache-core.version>2.6.0</ehcache-core.version>
        <xmemcached.version>1.3.8</xmemcached.version>
        <redis.version>2.9.0</redis.version>
        <jedis.version>2.9.0</jedis.version> <!-- 补充缺失的版本定义 -->
        
        <!-- 日志相关 -->
        <log4j.version>1.2.17</log4j.version>
        <slf4j.version>1.7.1</slf4j.version>
        
        <!-- 数据库连接池 -->
        <commons-dbcp.version>1.4</commons-dbcp.version>
        <commons-pool.version>1.6</commons-pool.version>
        <commons-pool2.version>2.4.2</commons-pool2.version>
        <druid.version>1.1.24</druid.version>
        
        <!-- Apache Commons - 统一使用较新版本 -->
        <commons-fileupload.version>1.3.3</commons-fileupload.version>
        <commons-io.version>2.4</commons-io.version>
        <commons-collections.version>3.2.1</commons-collections.version>
        <commons-codec.version>1.9</commons-codec.version>
        <commons-lang.version>2.5</commons-lang.version>
        <commons-lang3.version>3.3.2</commons-lang3.version>
        
        <!-- Office文档处理 - 统一使用POI 3.13 -->
        <poi.version>3.13</poi.version>
        
        <!-- PDF处理 -->
        <itextpdf.version>5.3.2</itextpdf.version>
        <itextasian.version>1.0</itextasian.version>
        <pdfbox.version>2.0.12</pdfbox.version>
        
        <!-- HTTP客户端 - 统一版本 -->
        <httpclient.version>4.5.6</httpclient.version>
        <httpcore.version>4.4.10</httpcore.version>
        <okhttp.version>2.5.0</okhttp.version>
        
        <!-- 邮件相关 -->
        <javaMail.version>1.4.5</javaMail.version>
        
        <!-- 对象映射 -->
        <dozer.version>5.3.2</dozer.version>
        
        <!-- 消息队列 -->
        <rabbitmq.version>1.3.5</rabbitmq.version>
        <spring-amqp.version>1.3.3</spring-amqp.version>
        
        <!-- 安全相关 -->
        <spring-security.version>3.1.3.RELEASE</spring-security.version>
        <cas-client.version>3.2.1</cas-client.version>
        <lucy-xss.version>1.6.3</lucy-xss.version>
        
        <!-- 测试相关 -->
        <mockito.version>1.9.0</mockito.version>
        <junit.version>4.10</junit.version>
        <hamcrest.version>1.3</hamcrest.version>
        
        <!-- 数据库驱动 -->
        <ojdbc.version>14</ojdbc.version>
        
        <!-- 监控相关 -->
        <javamelody.version>1.41.0</javamelody.version>
        <jrobin.version>1.5.9</jrobin.version>
        
        <!-- 验证码 -->
        <kaptcha.version>2.3.2</kaptcha.version>
        
        <!-- 页面装饰 -->
        <sitemesh.version>2.4.2</sitemesh.version>
        
        <!-- 资源优化 -->
        <wro4j.version>1.6.3</wro4j.version>
        
        <!-- 其他工具 -->
        <hutool.version>4.6.8</hutool.version>
        <liquibase.version>3.5.3</liquibase.version>
    </properties>

    <!-- 注意：这只是properties部分的建议配置 -->
    <!-- 实际使用时需要将这些版本应用到对应的dependency中 -->
    <!-- 并且需要清理lib目录中的重复jar包 -->

</project>
