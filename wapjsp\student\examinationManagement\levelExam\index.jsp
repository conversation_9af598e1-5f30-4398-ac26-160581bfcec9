<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>等级考试</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 等级考试页面样式 */
        .level-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .exam-tabs {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 4px;
        }
        
        .exam-tab {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-base);
            color: var(--text-secondary);
            background: transparent;
        }
        
        .exam-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .stats-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .stat-card {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-h4);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .exam-item {
            background: var(--bg-primary);
            border-radius: 8px;
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .exam-item:active {
            transform: scale(0.98);
            background: var(--bg-color-active);
        }
        
        .exam-item.cet4 {
            border-left-color: var(--success-color);
        }
        
        .exam-item.cet6 {
            border-left-color: var(--info-color);
        }
        
        .exam-item.tem4 {
            border-left-color: var(--warning-color);
        }
        
        .exam-item.tem8 {
            border-left-color: var(--error-color);
        }
        
        .exam-item.computer {
            border-left-color: var(--primary-color);
        }
        
        .exam-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .exam-name {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
            line-height: var(--line-height-base);
        }
        
        .exam-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-small);
            font-weight: 500;
            color: white;
        }
        
        .level-cet4 {
            background: var(--success-color);
        }
        
        .level-cet6 {
            background: var(--info-color);
        }
        
        .level-tem4 {
            background: var(--warning-color);
        }
        
        .level-tem8 {
            background: var(--error-color);
        }
        
        .level-computer {
            background: var(--primary-color);
        }
        
        .exam-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-md);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .exam-progress {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
        }
        
        .progress-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            margin-bottom: var(--margin-sm);
        }
        
        .progress-step {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--bg-primary);
            color: var(--text-disabled);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 500;
            position: relative;
            z-index: 2;
        }
        
        .progress-step.completed {
            background: var(--success-color);
            color: white;
        }
        
        .progress-step.active {
            background: var(--primary-color);
            color: white;
        }
        
        .progress-line {
            position: absolute;
            top: 50%;
            left: 12px;
            right: 12px;
            height: 2px;
            background: var(--bg-primary);
            z-index: 1;
        }
        
        .progress-labels {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .exam-scores {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-md);
        }
        
        .scores-title {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-sm);
        }
        
        .score-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .score-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 8px;
            background: var(--bg-primary);
            border-radius: 4px;
            font-size: var(--font-size-small);
        }
        
        .score-date {
            color: var(--text-secondary);
        }
        
        .score-value {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .score-value.pass {
            color: var(--success-color);
        }
        
        .score-value.fail {
            color: var(--error-color);
        }
        
        .exam-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn-register {
            background: var(--success-color);
            color: white;
        }
        
        .btn-query {
            background: var(--info-color);
            color: white;
        }
        
        .btn-certificate {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--padding-md);
        }
        
        .modal-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--margin-md);
            padding-bottom: var(--padding-sm);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .modal-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .modal-close {
            font-size: var(--font-size-h4);
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .modal-body {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--margin-sm);
        }
        
        .detail-table th,
        .detail-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid var(--divider-color);
            font-size: var(--font-size-small);
        }
        
        .detail-table th {
            background: var(--bg-tertiary);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .detail-table td {
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">等级考试</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="level-header">
            <div class="header-title">等级考试管理</div>
            <div class="header-subtitle">英语、计算机等级考试报名与查询</div>
        </div>

        <!-- 考试分类标签 -->
        <div class="exam-tabs">
            <div class="exam-tab active" onclick="switchExamType('english')">英语等级</div>
            <div class="exam-tab" onclick="switchExamType('computer')">计算机等级</div>
            <div class="exam-tab" onclick="switchExamType('other')">其他考试</div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
            <div class="stats-title">考试统计</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalExams">0</div>
                    <div class="stat-label">参加考试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedExams">0</div>
                    <div class="stat-label">通过考试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="highestScore">0</div>
                    <div class="stat-label">最高分数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="certificates">0</div>
                    <div class="stat-label">获得证书</div>
                </div>
            </div>
        </div>

        <!-- 考试列表 -->
        <div class="container-mobile">
            <div id="examList">
                <!-- 考试列表将通过JavaScript动态填充 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="ace-icon fa fa-certificate"></i>
                <div id="emptyMessage">暂无等级考试记录</div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-container" id="loadingState" style="display: none;">
                <i class="ace-icon fa fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="detail-modal" id="detailModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">考试详情</div>
                <div class="modal-close" onclick="closeDetailModal();">
                    <i class="ace-icon fa fa-times"></i>
                </div>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详情内容将动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allExams = [];
        let currentExamType = 'english';
        let examStats = {};

        $(function() {
            initPage();
            loadExamStats();
            loadExamsByType('english');
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 切换考试类型
        function switchExamType(type) {
            currentExamType = type;

            // 更新标签状态
            $('.exam-tab').removeClass('active');
            $(event.target).addClass('active');

            // 加载对应类型的考试
            loadExamsByType(type);
        }

        // 加载考试统计
        function loadExamStats() {
            $.ajax({
                url: "/student/examinationManagement/levelExam/getExamStats",
                type: "post",
                dataType: "json",
                success: function(data) {
                    examStats = data || {};
                    updateExamStats();
                },
                error: function() {
                    console.log('加载考试统计失败');
                }
            });
        }

        // 更新考试统计
        function updateExamStats() {
            $('#totalExams').text(examStats.totalExams || 0);
            $('#passedExams').text(examStats.passedExams || 0);
            $('#highestScore').text(examStats.highestScore || 0);
            $('#certificates').text(examStats.certificates || 0);
        }

        // 根据类型加载考试
        function loadExamsByType(type) {
            showLoading(true);

            $.ajax({
                url: "/student/examinationManagement/levelExam/getExamsByType",
                type: "post",
                data: { examType: type },
                dataType: "json",
                success: function(data) {
                    allExams = data.exams || [];
                    renderExamList();
                    showLoading(false);
                },
                error: function() {
                    showError('加载考试列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染考试列表
        function renderExamList() {
            const container = $('#examList');
            container.empty();

            if (allExams.length === 0) {
                showEmptyState('该类型暂无考试记录');
                return;
            } else {
                hideEmptyState();
            }

            allExams.forEach(exam => {
                const examHtml = createExamItem(exam);
                container.append(examHtml);
            });
        }

        // 创建考试项
        function createExamItem(exam) {
            const examClass = getExamClass(exam.examCode);
            const levelClass = getLevelClass(exam.examCode);

            return `
                <div class="exam-item ${examClass}" onclick="showExamDetail('${exam.id}')">
                    <div class="exam-header">
                        <div class="exam-name">${exam.name}</div>
                        <div class="exam-level ${levelClass}">${exam.level}</div>
                    </div>
                    <div class="exam-info">
                        <div class="info-item">
                            <span>考试时间:</span>
                            <span>${formatDate(exam.examDate)}</span>
                        </div>
                        <div class="info-item">
                            <span>报名状态:</span>
                            <span>${getRegistrationStatus(exam.registrationStatus)}</span>
                        </div>
                        <div class="info-item">
                            <span>准考证号:</span>
                            <span>${exam.ticketNumber || '未分配'}</span>
                        </div>
                        <div class="info-item">
                            <span>考试地点:</span>
                            <span>${exam.location || '待定'}</span>
                        </div>
                    </div>

                    ${createProgressSection(exam)}
                    ${createScoresSection(exam)}

                    <div class="exam-actions">
                        ${createActionButtons(exam)}
                    </div>
                </div>
            `;
        }

        // 创建进度部分
        function createProgressSection(exam) {
            const progress = exam.progress || {};
            const currentStep = progress.currentStep || 1;

            return `
                <div class="exam-progress">
                    <div class="progress-title">考试进度</div>
                    <div class="progress-steps">
                        <div class="progress-line"></div>
                        <div class="progress-step ${currentStep >= 1 ? 'completed' : ''}">${currentStep === 1 ? '●' : '1'}</div>
                        <div class="progress-step ${currentStep >= 2 ? (currentStep === 2 ? 'active' : 'completed') : ''}">${currentStep === 2 ? '●' : '2'}</div>
                        <div class="progress-step ${currentStep >= 3 ? (currentStep === 3 ? 'active' : 'completed') : ''}">${currentStep === 3 ? '●' : '3'}</div>
                        <div class="progress-step ${currentStep >= 4 ? (currentStep === 4 ? 'active' : 'completed') : ''}">${currentStep === 4 ? '●' : '4'}</div>
                    </div>
                    <div class="progress-labels">
                        <span>报名</span>
                        <span>缴费</span>
                        <span>考试</span>
                        <span>查分</span>
                    </div>
                </div>
            `;
        }

        // 创建成绩部分
        function createScoresSection(exam) {
            const scores = exam.scores || [];

            if (scores.length === 0) {
                return `
                    <div class="exam-scores">
                        <div class="scores-title">历史成绩</div>
                        <div class="score-item">
                            <span class="score-date">暂无成绩记录</span>
                            <span class="score-value">-</span>
                        </div>
                    </div>
                `;
            }

            const scoreItems = scores.map(score => {
                const scoreClass = score.score >= (score.passLine || 425) ? 'pass' : 'fail';
                return `
                    <div class="score-item">
                        <span class="score-date">${formatDate(score.examDate)}</span>
                        <span class="score-value ${scoreClass}">${score.score}分</span>
                    </div>
                `;
            }).join('');

            return `
                <div class="exam-scores">
                    <div class="scores-title">历史成绩</div>
                    <div class="score-list">
                        ${scoreItems}
                    </div>
                </div>
            `;
        }

        // 创建操作按钮
        function createActionButtons(exam) {
            const status = exam.registrationStatus;
            const hasScores = exam.scores && exam.scores.length > 0;
            const canGetCertificate = hasScores && exam.scores.some(s => s.score >= (s.passLine || 425));

            let buttons = [];

            if (status === 'not_registered') {
                buttons.push('<button class="btn-mobile btn-register flex-1" onclick="registerExam(\'' + exam.id + '\')">报名</button>');
            } else if (status === 'registered') {
                buttons.push('<button class="btn-mobile btn-query flex-1" onclick="queryExamInfo(\'' + exam.id + '\')">查询</button>');
            } else if (status === 'completed') {
                buttons.push('<button class="btn-mobile btn-query flex-1" onclick="queryScores(\'' + exam.id + '\')">查分</button>');
                if (canGetCertificate) {
                    buttons.push('<button class="btn-mobile btn-certificate flex-1" onclick="getCertificate(\'' + exam.id + '\')">证书</button>');
                }
            } else {
                buttons.push('<button class="btn-mobile btn-disabled flex-1">暂不可用</button>');
            }

            return buttons.join('');
        }

        // 获取考试样式类
        function getExamClass(examCode) {
            if (examCode.includes('CET4')) return 'cet4';
            if (examCode.includes('CET6')) return 'cet6';
            if (examCode.includes('TEM4')) return 'tem4';
            if (examCode.includes('TEM8')) return 'tem8';
            if (examCode.includes('NCRE')) return 'computer';
            return '';
        }

        // 获取等级样式类
        function getLevelClass(examCode) {
            if (examCode.includes('CET4')) return 'level-cet4';
            if (examCode.includes('CET6')) return 'level-cet6';
            if (examCode.includes('TEM4')) return 'level-tem4';
            if (examCode.includes('TEM8')) return 'level-tem8';
            if (examCode.includes('NCRE')) return 'level-computer';
            return 'level-computer';
        }

        // 获取报名状态文本
        function getRegistrationStatus(status) {
            switch(status) {
                case 'not_registered': return '未报名';
                case 'registered': return '已报名';
                case 'paid': return '已缴费';
                case 'completed': return '已完成';
                case 'cancelled': return '已取消';
                default: return '未知';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 报名考试
        function registerExam(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            const message = `确定要报名"${exam.name}"吗？\n\n报名费：¥${exam.fee || 0}\n考试时间：${formatDate(exam.examDate)}`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doRegisterExam(examId);
                    }
                });
            } else {
                if (confirm(message)) {
                    doRegisterExam(examId);
                }
            }
        }

        // 执行报名
        function doRegisterExam(examId) {
            $.ajax({
                url: "/student/examinationManagement/levelExam/registerExam",
                type: "post",
                data: { examId: examId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess('报名成功！请按时缴费并参加考试。');
                        loadExamStats();
                        loadExamsByType(currentExamType);
                    } else {
                        showError(data.message || '报名失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 查询考试信息
        function queryExamInfo(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            let message = `考试信息查询\n\n`;
            message += `考试名称：${exam.name}\n`;
            message += `考试时间：${formatDate(exam.examDate)}\n`;
            message += `报名状态：${getRegistrationStatus(exam.registrationStatus)}\n`;
            message += `准考证号：${exam.ticketNumber || '未分配'}\n`;
            message += `考试地点：${exam.location || '待定'}\n`;

            if (exam.seatNumber) {
                message += `座位号：${exam.seatNumber}\n`;
            }

            if (exam.paymentStatus) {
                message += `缴费状态：${exam.paymentStatus === 'paid' ? '已缴费' : '未缴费'}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 查询成绩
        function queryScores(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            $.ajax({
                url: "/student/examinationManagement/levelExam/queryScores",
                type: "post",
                data: { examId: examId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showScoreDetail(data.scoreDetail);
                    } else {
                        showError(data.message || '查询成绩失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示成绩详情
        function showScoreDetail(scoreDetail) {
            let message = `成绩查询结果\n\n`;
            message += `总分：${scoreDetail.totalScore}分\n`;
            message += `听力：${scoreDetail.listening || '-'}分\n`;
            message += `阅读：${scoreDetail.reading || '-'}分\n`;
            message += `写作：${scoreDetail.writing || '-'}分\n`;
            message += `翻译：${scoreDetail.translation || '-'}分\n`;
            message += `及格线：${scoreDetail.passLine || 425}分\n`;
            message += `考试结果：${scoreDetail.totalScore >= (scoreDetail.passLine || 425) ? '通过' : '未通过'}\n`;

            if (scoreDetail.rank) {
                message += `排名：${scoreDetail.rank}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 获取证书
        function getCertificate(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            $.ajax({
                url: "/student/examinationManagement/levelExam/getCertificate",
                type: "post",
                data: { examId: examId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        if (data.certificateUrl) {
                            // 下载证书
                            window.open(data.certificateUrl, '_blank');
                            showSuccess('证书下载成功');
                        } else {
                            showSuccess('证书申请已提交，请等待审核');
                        }
                    } else {
                        showError(data.message || '获取证书失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示考试详情
        function showExamDetail(examId) {
            const exam = allExams.find(e => e.id === examId);
            if (!exam) return;

            $('#modalTitle').text(exam.name + ' - 详细信息');

            let modalContent = `
                <div class="exam-basic-info">
                    <h4>基本信息</h4>
                    <table class="detail-table">
                        <tr><td>考试名称</td><td>${exam.name}</td></tr>
                        <tr><td>考试代码</td><td>${exam.examCode}</td></tr>
                        <tr><td>考试等级</td><td>${exam.level}</td></tr>
                        <tr><td>考试时间</td><td>${formatDate(exam.examDate)}</td></tr>
                        <tr><td>报名状态</td><td>${getRegistrationStatus(exam.registrationStatus)}</td></tr>
                        <tr><td>考试费用</td><td>¥${exam.fee || 0}</td></tr>
                    </table>
                </div>
            `;

            if (exam.description) {
                modalContent += `
                    <div class="exam-description">
                        <h4>考试说明</h4>
                        <p>${exam.description}</p>
                    </div>
                `;
            }

            if (exam.scores && exam.scores.length > 0) {
                modalContent += `
                    <div class="exam-scores-detail">
                        <h4>成绩记录</h4>
                        <table class="detail-table">
                            <thead>
                                <tr>
                                    <th>考试时间</th>
                                    <th>总分</th>
                                    <th>听力</th>
                                    <th>阅读</th>
                                    <th>写作</th>
                                    <th>结果</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                exam.scores.forEach(score => {
                    const result = score.score >= (score.passLine || 425) ? '通过' : '未通过';
                    modalContent += `
                        <tr>
                            <td>${formatDate(score.examDate)}</td>
                            <td>${score.score}</td>
                            <td>${score.listening || '-'}</td>
                            <td>${score.reading || '-'}</td>
                            <td>${score.writing || '-'}</td>
                            <td>${result}</td>
                        </tr>
                    `;
                });

                modalContent += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            $('#modalBody').html(modalContent);
            $('#detailModal').show();
        }

        // 关闭详情模态框
        function closeDetailModal() {
            $('#detailModal').hide();
        }

        // 刷新数据
        function refreshData() {
            loadExamStats();
            loadExamsByType(currentExamType);
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('#examList').hide();
            } else {
                $('#loadingState').hide();
                $('#examList').show();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框背景关闭
        $('#detailModal').click(function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
    </script>
</body>
</html>
