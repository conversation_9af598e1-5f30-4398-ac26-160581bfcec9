<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的${pagetitle}</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 流程管理页面样式 */
        .process-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .process-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .process-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            background: var(--warning-light);
            color: var(--warning-dark);
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-section i {
            color: var(--warning-color);
            margin-right: 8px;
        }
        
        .actions-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .actions-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .actions-title i {
            color: var(--success-color);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-add {
            flex: 1;
            background: var(--success-color);
            color: white;
        }
        
        .btn-return {
            flex: 1;
            background: var(--text-disabled);
            color: white;
        }
        
        .processes-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .processes-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .processes-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .processes-title i {
            color: var(--primary-color);
        }
        
        .process-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .process-item:last-child {
            border-bottom: none;
        }
        
        .process-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .process-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .process-content {
            flex: 1;
        }
        
        .process-title-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
        }
        
        .process-batch {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .process-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-draft {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-pending {
            background: var(--info-light);
            color: var(--info-dark);
        }
        
        .status-reviewing {
            background: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .status-approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-operation {
            flex: 1;
            min-width: 60px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-edit {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .btn-print {
            background: var(--success-color);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        .notice-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .notice-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .notice-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .notice-title {
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .notice-close {
            background: none;
            border: none;
            color: white;
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .notice-body {
            padding: var(--padding-md);
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .notice-footer {
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-continue {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .btn-cancel {
            flex: 1;
            background: var(--text-disabled);
            color: white;
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .process-details {
                grid-template-columns: 1fr;
            }
            
            .operation-buttons {
                flex-direction: column;
            }
            
            .notice-content {
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    <input type="hidden" id="xnxq" name="xnxq" value="${xnxq}"/>
    <input type="hidden" id="tmbh" name="tmbh" value="${tmbh}"/>
    <input type="hidden" id="gclx" name="gclx" value="${gclx}"/>
    <input type="hidden" id="apply_type" name="apply_type" value="${apply_type}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="returnOther();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">我的${pagetitle}</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 流程管理头部 -->
        <div class="process-header">
            <div class="process-title">我的${pagetitle}</div>
            <div class="process-desc">管理和查看流程申请</div>
        </div>
        
        <!-- 错误信息 -->
        <c:if test="${not empty errorMessage}">
            <div class="notice-section">
                <i class="ace-icon fa fa-exclamation-circle"></i>
                ${errorMessage}
            </div>
        </c:if>
        
        <!-- 操作按钮 -->
        <c:if test="${empty errorMessage}">
            <div class="actions-section">
                <div class="actions-title">
                    <i class="ace-icon fa fa-cogs"></i>
                    操作
                </div>
                <div class="action-buttons">
                    <c:if test="${flage}">
                        <button class="btn-mobile btn-add" onclick="updateInfo('add', '');">
                            <i class="ace-icon fa fa-plus"></i>
                            <span>填写${pagetitle}</span>
                        </button>
                    </c:if>
                    <button class="btn-mobile btn-return" onclick="returnOther();">
                        <i class="ace-icon fa fa-reply"></i>
                        <span>返回</span>
                    </button>
                </div>
            </div>
            
            <!-- 流程列表 -->
            <div class="processes-section">
                <div class="processes-header">
                    <div class="processes-title">
                        <i class="ace-icon fa fa-list"></i>
                        流程申请列表
                    </div>
                </div>
                
                <div id="processesList">
                    <!-- 动态加载流程列表 -->
                </div>
                
                <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                    <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreProcesses();">
                        <i class="ace-icon fa fa-plus"></i>
                        <span>加载更多</span>
                    </button>
                </div>
            </div>
        </c:if>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无流程申请数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
        
        <!-- 申请需知模态框 -->
        <div class="notice-modal" id="noticeModal">
            <div class="notice-content">
                <div class="notice-header">
                    <div class="notice-title">申请需知</div>
                    <button class="notice-close" onclick="closeNoticeModal();">
                        <i class="ace-icon fa fa-times"></i>
                    </button>
                </div>
                <div class="notice-body" id="noticeBody">
                    <!-- 动态加载申请需知内容 -->
                </div>
                <div class="notice-footer">
                    <button class="btn-mobile btn-continue" id="continueBtn" disabled>
                        <i class="ace-icon fa fa-arrow-right"></i>
                        <span>继续</span>
                        <span id="countdown"></span>
                    </button>
                    <button class="btn-mobile btn-cancel" onclick="closeNoticeModal();">
                        <i class="ace-icon fa fa-times"></i>
                        <span>关闭</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let processData = [];
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;
        let hasMore = true;
        let countdownInterval = null;

        $(function() {
            initPage();

            // 如果没有错误信息，加载流程列表
            if ('${empty errorMessage}' === 'true') {
                loadProcesses(1, true);
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载更多流程
        function loadMoreProcesses() {
            if (hasMore) {
                loadProcesses(currentPage + 1, false);
            }
        }

        // 加载流程数据
        function loadProcesses(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
            }

            showLoading(true);

            const gclx = $('#gclx').val();
            const xnxq = $('#xnxq').val();
            const tmbh = $('#tmbh').val();

            $.ajax({
                url: "/student/personalManagement/processManagement/search",
                type: "post",
                data: "gclx=" + gclx + "&xnxq=" + xnxq + "&tmbh=" + tmbh + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.records && data.records.length > 0) {
                        if (reset) {
                            processData = data.records;
                        } else {
                            processData = processData.concat(data.records);
                        }

                        totalCount = data.pageContext.totalCount;
                        currentPage = page;
                        hasMore = processData.length < totalCount;

                        renderProcessesList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            processData = [];
                            renderProcessesList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染流程列表
        function renderProcessesList(reset = false) {
            const container = $('#processesList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : processData.length - pageSize;
            const endIndex = processData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (processData[i]) {
                    const itemHtml = createProcessItem(processData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建流程项目HTML
        function createProcessItem(item, index) {
            // 获取状态信息
            const statusInfo = getStatusInfo(item.SHZT);

            // 构建操作按钮
            let operationButtons = '';
            if (item.SHZT == 0 && '${flage}' === 'true') {
                operationButtons = `
                    <button class="btn-operation btn-edit" onclick="updateInfo('edit', '${item.RID}');">
                        <i class="ace-icon fa fa-edit"></i>
                        <span>修改</span>
                    </button>
                    <button class="btn-operation btn-delete" onclick="revokeInfo('${item.RID}');">
                        <i class="ace-icon fa fa-trash"></i>
                        <span>删除</span>
                    </button>
                `;
            } else {
                operationButtons = `
                    <button class="btn-operation btn-view" onclick="updateInfo('look', '${item.RID}');">
                        <i class="ace-icon fa fa-eye"></i>
                        <span>查看</span>
                    </button>
                `;

                if (item.SHZT == 3 && '${schoolCode}' === '100013' && '${gclx}' === '01') {
                    operationButtons += `
                        <button class="btn-operation btn-print" onclick="doPrint('${item.RID}');">
                            <i class="ace-icon fa fa-print"></i>
                            <span>打印开题报告</span>
                        </button>
                    `;
                }
            }

            return `
                <div class="process-item">
                    <div class="process-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="process-index">${index + 1}</div>
                            <div class="process-content">
                                <div class="process-title-text">${item.TMMC || ''}</div>
                                <div class="process-batch">批次：${item.PCMC || ''}</div>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                        </div>
                    </div>

                    <div class="process-details">
                        <div class="detail-item">
                            <span class="detail-label">提交时间</span>
                            <span>${item.TJSJ || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">备注</span>
                            <span>${item.BZ || ''}</span>
                        </div>
                    </div>

                    <div class="operation-buttons">
                        ${operationButtons}
                    </div>
                </div>
            `;
        }

        // 获取状态信息
        function getStatusInfo(shzt) {
            switch (shzt) {
                case 0: return { text: '暂存', class: 'status-draft' };
                case 1: return { text: '待审核', class: 'status-pending' };
                case 2: return { text: '审核中', class: 'status-reviewing' };
                case 3: return { text: '审核通过', class: 'status-approved' };
                case 4: return { text: '审核不通过', class: 'status-rejected' };
                default: return { text: '', class: 'status-pending' };
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && processData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (processData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.processes-section').hide();
            } else {
                $('#emptyState').hide();
                $('.processes-section').show();
            }
        }

        // 更新信息
        function updateInfo(type, rid) {
            if (type !== "add") {
                openEditPage(type, rid);
            } else {
                showLoading(true);

                $.ajax({
                    url: "/student/personalManagement/processManagement/checkaddApply",
                    type: "post",
                    data: { type: $("#gclx").val() },
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        if (data.msg) {
                            showError(data.msg);
                        } else {
                            if (data.sfxyd === "1") {
                                showNoticeModal(data, type, rid);
                            } else {
                                openEditPage(type, rid);
                            }
                        }
                    },
                    error: function(xhr) {
                        showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 显示申请需知模态框
        function showNoticeModal(data, type, rid) {
            $('#noticeBody').html(data.ydnr);
            $('#noticeModal').fadeIn(300);

            let countdown = data.qzydms || 0;
            const continueBtn = $('#continueBtn');
            const countdownSpan = $('#countdown');

            if (countdown > 0) {
                continueBtn.prop('disabled', true);
                countdownSpan.text(`（${countdown}s）`);

                countdownInterval = setInterval(function() {
                    countdown--;
                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        countdownSpan.text('');
                        continueBtn.prop('disabled', false);
                        continueBtn.off('click').on('click', function() {
                            closeNoticeModal();
                            setTimeout(function() {
                                openEditPage(type, rid);
                            }, 500);
                        });
                    } else {
                        countdownSpan.text(`（${countdown}s）`);
                    }
                }, 1000);
            } else {
                continueBtn.prop('disabled', false);
                continueBtn.off('click').on('click', function() {
                    closeNoticeModal();
                    setTimeout(function() {
                        openEditPage(type, rid);
                    }, 500);
                });
            }
        }

        // 关闭申请需知模态框
        function closeNoticeModal() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            $('#noticeModal').fadeOut(300);
        }

        // 打开编辑页面
        function openEditPage(type, rid) {
            const gclx = $("#gclx").val();
            if (parent && parent.addTab) {
                parent.addTab('流程管理', '/student/personalManagement/processManagement/update?type=' + type + '&rid=' + rid + '&gclx=' + gclx);
            } else {
                location.href = "/student/personalManagement/processManagement/update?type=" + type + "&rid=" + rid + "&gclx=" + gclx;
            }
        }

        // 删除流程
        function revokeInfo(rid) {
            if (confirm("是否确认删除？")) {
                showLoading(true);

                const gclx = $("#gclx").val();

                $.ajax({
                    url: "/student/personalManagement/processManagement/doDel",
                    type: "post",
                    data: "rid=" + rid + "&gclx=" + gclx + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result.indexOf("/") != -1) {
                            window.location.href = data.result;
                        } else if (data.result === "ok") {
                            showSuccess("删除成功！", function() {
                                location.reload();
                            });
                        }
                    },
                    error: function(xhr) {
                        showError("删除失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 打印
        function doPrint(sqbh) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/processManagement/print/checkTemplate",
                type: "post",
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data === "ok") {
                        if (parent && parent.addTab) {
                            parent.addTab('打印开题报告', '/student/personalManagement/processManagement/print?sqbh=' + sqbh);
                        } else {
                            window.open("/student/personalManagement/processManagement/print?sqbh=" + sqbh);
                        }
                    } else {
                        showError("当前数据还没有申请模板，请联系管理员！");
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 返回其他页面
        function returnOther() {
            if (parent && parent.closeFrame) {
                parent.closeFrame();
            } else {
                location.href = "/student/personalManagement/lwydqr/index";
            }
        }

        // 刷新数据
        function refreshData() {
            if ('${empty errorMessage}' === 'true') {
                loadProcesses(1, true);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 点击模态框外部关闭
        $(document).on('click', '.notice-modal', function(e) {
            if (e.target === this) {
                closeNoticeModal();
            }
        });
    </script>
</body>
</html>
