<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>网上选题</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 项目选择页面样式 */
        .select-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .select-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .select-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .notice-section {
            margin: var(--margin-sm) var(--margin-md);
            padding: var(--padding-md);
            border-radius: 8px;
            font-size: var(--font-size-small);
            line-height: 1.4;
        }
        
        .notice-success {
            background: var(--success-light);
            color: var(--success-dark);
            border-left: 4px solid var(--success-color);
        }
        
        .notice-warning {
            background: var(--warning-light);
            color: var(--warning-dark);
            border-left: 4px solid var(--warning-color);
        }
        
        .notice-section i {
            margin-right: 8px;
        }
        
        .search-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-title i {
            color: var(--primary-color);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .form-input, .form-select {
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-search {
            flex: 1;
            background: var(--info-color);
            color: white;
        }
        
        .projects-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .projects-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
        }
        
        .projects-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .projects-title i {
            color: var(--success-color);
        }
        
        .project-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .project-item:last-child {
            border-bottom: none;
        }
        
        .project-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .project-index {
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-mini);
            font-weight: 600;
            margin-right: var(--margin-sm);
        }
        
        .project-content {
            flex: 1;
        }
        
        .project-title-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.4;
            cursor: pointer;
        }
        
        .project-title-text:hover {
            color: var(--primary-color);
        }
        
        .project-teacher {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
            cursor: pointer;
        }
        
        .project-teacher:hover {
            color: var(--primary-color);
        }
        
        .project-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .detail-label {
            font-weight: 500;
        }
        
        .operation-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--margin-md);
            flex-wrap: wrap;
        }
        
        .btn-operation {
            flex: 1;
            min-width: 60px;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-view {
            background: var(--info-color);
            color: white;
        }
        
        .btn-add {
            background: var(--success-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
        
        .selected-section {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selected-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .selected-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .selected-title i {
            color: var(--warning-color);
        }
        
        .btn-delete-all {
            background: var(--error-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: var(--font-size-mini);
            cursor: pointer;
        }
        
        .selected-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            position: relative;
        }
        
        .selected-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
        }
        
        .status-confirmed {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .load-more-container {
            padding: var(--padding-md);
            text-align: center;
            border-top: 1px solid var(--divider-color);
        }
        
        .btn-load-more {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--padding-sm) var(--padding-lg);
            border-radius: 6px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }
        
        .btn-load-more:disabled {
            background: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .search-actions {
                flex-direction: column;
            }
            
            .project-details {
                grid-template-columns: 1fr;
            }
            
            .operation-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}"/>
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">网上选题</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 项目选择头部 -->
        <div class="select-header">
            <div class="select-title">网上选题</div>
            <div class="select-desc">选择毕业设计题目</div>
        </div>
        
        <!-- 状态提示 -->
        <c:choose>
            <c:when test="${schoolCode == '100014' && xtmsnums == 'A'}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    当前论文模式为【分配模式】，您不能自主选择题目！<br>
                    <c:forEach items="${sbtmList}" var="tmxx">
                        ${tmxx}
                    </c:forEach>
                </div>
            </c:when>
            <c:when test="${yjby == '0'}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    您不在当前论文批次的学生名单中！
                </div>
            </c:when>
            <c:when test="${pcnum == '0'}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    没有开启的批次！
                </div>
            </c:when>
            <c:when test="${yjby == 'kg_close'}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    学生选题开关已关闭，无法进行选题操作！
                </div>
            </c:when>
            <c:when test="${yjby == 'null'}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    选题时间维护不完整，请确认选题时间！
                </div>
            </c:when>
            <c:when test="${yjby == 'nostart' && qrcount == 0}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    当前不是毕业设计选题时间，请确认选题时间！
                </div>
            </c:when>
            <c:when test="${yjby == 'nostart' && qrcount > 0}">
                <div class="notice-section notice-success">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    您的论文题目是
                    <c:forEach items="${xtList}" var="tmxx">
                        【${tmxx.tmmc}】   ${tmxx.dbzgscjg}
                    </c:forEach>
                </div>
            </c:when>
            <c:when test="${yjby == 'no_xz'}">
                <div class="notice-section notice-warning">
                    <i class="ace-icon fa fa-exclamation-circle"></i>
                    您的学制没有维护，请联系管理员！
                </div>
            </c:when>
            <c:otherwise>
                <!-- 查询条件 -->
                <div class="search-section">
                    <div class="search-title">
                        <i class="ace-icon fa fa-search"></i>
                        查询条件
                    </div>
                    
                    <form id="tmForm" name="tmForm" class="search-form">
                        <div class="form-group">
                            <label class="form-label">院系</label>
                            <select name="xsh" class="form-select">
                                <option value="">全部</option>
                                <cache:query var="jgb" region="code_jgb" fields="jgdm,jgmc" orderby="jgdm asc"/>
                                <c:forEach items="${jgb}" var="jgb">
                                    <option value="${jgb.jgdm}" <c:if test="${jgb.jgdm==xsh}">selected</c:if>>${jgb.jgmc}</option>
                                </c:forEach>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">题目名称</label>
                            <input type="text" name="tmmc" class="form-input" placeholder="请输入题目名称">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">课题类别</label>
                            <select name="ktlbdm" class="form-select">
                                <option value="">全部</option>
                                <cache:query var="ktlbb" fields="ktlbdm,ktlbsm" region="code_ktlbb"/>
                                <c:forEach items="${ktlbb}" var="ktlbb">
                                    <option value="${ktlbb.ktlbdm}">${ktlbb.ktlbsm}</option>
                                </c:forEach>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">教师名</label>
                            <input type="text" name="jsm" class="form-input" placeholder="请输入教师姓名">
                        </div>
                    </form>
                    
                    <div class="search-actions">
                        <button class="btn-mobile btn-search" onclick="searchProjects();">
                            <i class="ace-icon fa fa-search"></i>
                            <span>查询</span>
                        </button>
                    </div>
                </div>
                
                <!-- 题目信息列表 -->
                <div class="projects-section">
                    <div class="projects-header">
                        <div class="projects-title">
                            <i class="ace-icon fa fa-list"></i>
                            题目信息列表
                        </div>
                    </div>
                    
                    <div id="projectsList">
                        <!-- 动态加载项目列表 -->
                    </div>
                    
                    <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                        <button class="btn-load-more" id="loadMoreBtn" onclick="loadMoreProjects();">
                            <i class="ace-icon fa fa-plus"></i>
                            <span>加载更多</span>
                        </button>
                    </div>
                </div>
                
                <!-- 我的选题信息 -->
                <div class="selected-section">
                    <div class="selected-header">
                        <div class="selected-title">
                            <i class="ace-icon fa fa-star"></i>
                            我的选题信息
                        </div>
                        <c:if test="${qrcount == 0}">
                            <button class="btn-delete-all" onclick="deleteSelected();">
                                <i class="ace-icon fa fa-trash"></i>
                                删除
                            </button>
                        </c:if>
                    </div>
                    
                    <div id="selectedList">
                        <!-- 动态加载已选题目列表 -->
                    </div>
                </div>
            </c:otherwise>
        </c:choose>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-text-o"></i>
            <div>暂无题目数据</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let projectData = [];
        let selectedData = [];
        let currentPage = 1;
        let pageSize = 10;
        let totalCount = 0;
        let hasMore = true;
        let searchParams = '';
        let canSelect = '${qrcount}' === '0';

        $(function() {
            initPage();

            // 如果可以选题，加载数据
            if ('${yjby}' !== '0' && '${yjby}' !== 'kg_close' && '${yjby}' !== 'null' && '${pcnum}' !== '0') {
                loadProjects(1, true);
            }

            // 显示消息
            if ('${message}' && '${message}' !== '') {
                showInfo('${message}');
            }
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 搜索项目
        function searchProjects() {
            loadProjects(1, true);
        }

        // 加载更多项目
        function loadMoreProjects() {
            if (hasMore) {
                loadProjects(currentPage + 1, false);
            }
        }

        // 加载项目数据
        function loadProjects(page = 1, reset = false) {
            if (reset) {
                currentPage = 1;
                hasMore = true;
                searchParams = $('#tmForm').serialize();
            }

            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/projectSelect/search",
                type: "post",
                data: searchParams + "&pageNum=" + page + "&pageSize=" + pageSize,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    if (data && data.LwTmxxbPage && data.LwTmxxbPage.records && data.LwTmxxbPage.records.length > 0) {
                        if (reset) {
                            projectData = data.LwTmxxbPage.records;
                        } else {
                            projectData = projectData.concat(data.LwTmxxbPage.records);
                        }

                        totalCount = data.LwTmxxbPage.pageContext.totalCount;
                        currentPage = page;
                        hasMore = projectData.length < totalCount;

                        renderProjectsList(reset);
                        updateLoadMoreButton();
                        showEmptyState(false);
                    } else {
                        if (reset) {
                            projectData = [];
                            renderProjectsList(true);
                        }
                        showEmptyState(true);
                        updateLoadMoreButton();
                    }

                    // 处理已选题目数据
                    if (data && data.xt_xtxx_list) {
                        selectedData = data.xt_xtxx_list;
                        renderSelectedList();
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染项目列表
        function renderProjectsList(reset = false) {
            const container = $('#projectsList');
            if (reset) {
                container.empty();
            }

            const startIndex = reset ? 0 : projectData.length - pageSize;
            const endIndex = projectData.length;

            for (let i = startIndex; i < endIndex; i++) {
                if (projectData[i]) {
                    const itemHtml = createProjectItem(projectData[i], i);
                    container.append(itemHtml);
                }
            }
        }

        // 创建项目项目HTML
        function createProjectItem(item, index) {
            // 构建操作按钮
            let operationButtons = `
                <button class="btn-operation btn-view" onclick="viewProject('${item.zxjxjhh}', '${item.tmbh}');">
                    <i class="ace-icon fa fa-eye"></i>
                    <span>详情</span>
                </button>
                <button class="btn-operation btn-view" onclick="viewAttachment('${item.zxjxjhh}', '${item.tmbh}');">
                    <i class="ace-icon fa fa-file"></i>
                    <span>附件</span>
                </button>
            `;

            if (canSelect) {
                if (item.isSelected === "yes") {
                    operationButtons += `
                        <button class="btn-operation btn-disabled" disabled>
                            <i class="ace-icon fa fa-check"></i>
                            <span>已选</span>
                        </button>
                    `;
                } else {
                    operationButtons += `
                        <button class="btn-operation btn-add" onclick="addProject('${item.zxjxjhh}', '${item.tmbh}');">
                            <i class="ace-icon fa fa-plus"></i>
                            <span>增加</span>
                        </button>
                    `;
                }
            }

            return `
                <div class="project-item">
                    <div class="project-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="project-index">${index + 1}</div>
                            <div class="project-content">
                                <div class="project-title-text" onclick="viewProject('${item.zxjxjhh}', '${item.tmbh}');" title="${item.tmjj || ''}">${item.tmmc || ''}</div>
                                <div class="project-teacher" onclick="showHistoryPaper('${item.jsh}', '${item.jsm}');" title="查询教师历年指导的可供下载论文">指导教师：${item.jsm || ''}</div>
                            </div>
                        </div>
                    </div>

                    <div class="project-details">
                        <div class="detail-item">
                            <span class="detail-label">职称</span>
                            <span>${item.zcsm || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">院系</span>
                            <span>${getCollegeName(item.xsh) || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">题目来源</span>
                            <span>${item.tmlysm || ''}</span>
                        </div>
                        ${('${schoolCode}' !== '100015') ? `
                        <div class="detail-item">
                            <span class="detail-label">课题类别</span>
                            <span>${item.ktlbsm || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">题目难度</span>
                            <span>${item.lwnd || ''}</span>
                        </div>
                        ` : ''}
                        <div class="detail-item">
                            <span class="detail-label">论文工作形式</span>
                            <span>${item.lwgzxssm || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">论文研究方向</span>
                            <span>${item.lwyjfx || ''}</span>
                        </div>
                    </div>

                    <div class="operation-buttons">
                        ${operationButtons}
                    </div>
                </div>
            `;
        }

        // 渲染已选题目列表
        function renderSelectedList() {
            const container = $('#selectedList');
            container.empty();

            if (selectedData && selectedData.length > 0) {
                selectedData.forEach(function(item, index) {
                    const itemHtml = createSelectedItem(item, index);
                    container.append(itemHtml);
                });
            } else {
                container.html(`
                    <div class="empty-state">
                        <i class="ace-icon fa fa-star-o"></i>
                        <div>暂无已选题目</div>
                    </div>
                `);
            }
        }

        // 创建已选题目HTML
        function createSelectedItem(item, index) {
            const statusInfo = getStatusInfo(item.xtztdm, item.xtztsm);
            const isConfirmed = item.xtztsm === "确定";

            return `
                <div class="selected-item">
                    <div class="project-header-info">
                        <div style="display: flex; align-items: flex-start;">
                            <div class="project-index">${index + 1}</div>
                            <div class="project-content">
                                <div class="project-title-text" onclick="viewProject('${item.id.zxjxjhh}', '${item.id.tmbh}');">${item.tmmc_xs || ''}</div>
                                <div class="project-teacher" onclick="showHistoryPaper('${item.jsh}', '${item.jsm}');">指导教师：${item.jsm || ''}</div>
                            </div>
                        </div>
                        <div>
                            <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
                        </div>
                    </div>

                    <div class="project-details">
                        <div class="detail-item">
                            <span class="detail-label">姓名</span>
                            <span>${item.xm || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">年级</span>
                            <span>${item.njmc || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">院系</span>
                            <span>${item.xsm || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">专业</span>
                            <span>${item.zym || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">班级</span>
                            <span>${item.bm || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">答辩资格审核结果</span>
                            <span>${item.dbzgscjg || ''}</span>
                        </div>
                    </div>

                    <div class="operation-buttons">
                        <button class="btn-operation btn-view" onclick="viewProject('${item.id.zxjxjhh}', '${item.id.tmbh}');">
                            <i class="ace-icon fa fa-eye"></i>
                            <span>详情</span>
                        </button>
                        <button class="btn-operation btn-view" onclick="viewAttachment('${item.id.zxjxjhh}', '${item.id.tmbh}');">
                            <i class="ace-icon fa fa-file"></i>
                            <span>附件</span>
                        </button>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" class="ace" name="xtids" value="${item.id.zxjxjhh},${item.id.tmbh}" ${isConfirmed ? 'disabled' : ''}>
                            <span class="lbl"></span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取院系名称
        function getCollegeName(xsh) {
            const option = $(`#tmForm select[name="xsh"] option[value="${xsh}"]`);
            return option.length > 0 ? option.text() : '';
        }

        // 获取状态信息
        function getStatusInfo(xtztdm, xtztsm) {
            switch (xtztdm) {
                case "02": return { text: xtztsm, class: 'status-confirmed' };
                case "03": return { text: xtztsm, class: 'status-rejected' };
                case "01": return { text: xtztsm, class: 'status-pending' };
                default: return { text: xtztsm, class: 'status-pending' };
            }
        }

        // 更新加载更多按钮
        function updateLoadMoreButton() {
            const container = $('#loadMoreContainer');
            const button = $('#loadMoreBtn');

            if (hasMore && projectData.length > 0) {
                container.show();
                button.prop('disabled', false);
                button.find('span').text('加载更多');
            } else if (projectData.length > 0) {
                container.show();
                button.prop('disabled', true);
                button.find('span').text('已加载全部');
            } else {
                container.hide();
            }
        }

        // 显示空状态
        function showEmptyState(show) {
            if (show) {
                $('#emptyState').show();
                $('.projects-section').hide();
            } else {
                $('#emptyState').hide();
                $('.projects-section').show();
            }
        }

        // 添加项目
        function addProject(zxjxjhh, tmbh) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/projectSelect/add",
                type: "post",
                data: "zxjxjhh=" + zxjxjhh + "&tokenValue=" + $("#tokenValue").val() + "&tmbh=" + tmbh,
                dataType: "json",
                success: function(response) {
                    const data = response.data;
                    $("#tokenValue").val(data.token);

                    if (data.result.indexOf("/") != -1) {
                        window.location.href = data.result;
                    } else if (data.result === "success") {
                        showSuccess("保存成功！", function() {
                            loadProjects(1, true);
                        });
                    } else {
                        showError(data.result);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 删除已选题目
        function deleteSelected() {
            const checked = $('input[name="xtids"]:checked');
            if (checked.length === 0) {
                showError("请选择要删除的题目！");
                return;
            }

            if (confirm("确认删除吗？")) {
                showLoading(true);

                const params = checked.serialize();

                $.ajax({
                    url: "/student/personalManagement/projectSelect/delete",
                    type: "post",
                    data: params + "&tokenValue=" + $("#tokenValue").val(),
                    dataType: "json",
                    success: function(response) {
                        const data = response.data;
                        $("#tokenValue").val(data.token);

                        if (data.result === "0") {
                            showSuccess("信息删除成功！", function() {
                                loadProjects(1, true);
                            });
                        } else if (data.result === "1") {
                            showError("信息删除失败！");
                        } else {
                            showError(data.result);
                        }
                    },
                    error: function(xhr) {
                        showError("信息删除失败！");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 查看项目详情
        function viewProject(zxjxjhh, tmbh) {
            if (parent && parent.addTab) {
                parent.addTab('项目详情', '/student/personalManagement/projectSelect/index/' + zxjxjhh + '/' + tmbh + '/view');
            } else {
                window.open("/student/personalManagement/projectSelect/index/" + zxjxjhh + "/" + tmbh + "/view");
            }
        }

        // 查看附件
        function viewAttachment(zxjxjhh, tmbh) {
            if (parent && parent.addTab) {
                parent.addTab('项目附件', '/student/personalManagement/showFile/' + zxjxjhh + '/' + tmbh);
            } else {
                window.open("/student/personalManagement/showFile/" + zxjxjhh + "/" + tmbh);
            }
        }

        // 查看历史论文
        function showHistoryPaper(jsh, jsm) {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/projectSelect/queryHistoryPaper",
                type: "post",
                data: "jsh=" + jsh,
                dataType: "json",
                success: function(response) {
                    if (response && response.length > 0) {
                        let content = `<div style="padding: 16px;"><h4>【${jsm}】历年指导可下载论文</h4><ul>`;
                        response.forEach(function(paper) {
                            content += `<li style="margin-bottom: 8px;"><a href="${paper.url}" target="_blank">${paper.title}</a></li>`;
                        });
                        content += '</ul></div>';

                        showInfo(content);
                    } else {
                        showInfo("暂无历史论文数据");
                    }
                },
                error: function(xhr) {
                    showError("查询历史论文失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 刷新数据
        function refreshData() {
            if ('${yjby}' !== '0' && '${yjby}' !== 'kg_close' && '${yjby}' !== 'null' && '${pcnum}' !== '0') {
                loadProjects(1, true);
            }
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示成功信息
        function showSuccess(message, callback) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message, callback);
            } else {
                alert(message);
                if (callback) callback();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示信息
        function showInfo(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
