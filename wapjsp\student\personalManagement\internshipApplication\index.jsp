<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实习申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 实习申请页面样式 */
        .internship-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .internship-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .type-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .type-item:last-child {
            border-bottom: none;
        }
        
        .type-item:active {
            background: var(--bg-color-active);
        }
        
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .type-icon.practice {
            background: var(--success-color);
        }
        
        .type-icon.graduation {
            background: var(--info-color);
        }
        
        .type-icon.professional {
            background: var(--warning-color);
        }
        
        .type-icon.social {
            background: var(--error-color);
        }
        
        .type-content {
            flex: 1;
        }
        
        .type-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .type-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-limited {
            background: var(--warning-color);
            color: white;
        }
        
        .status-closed {
            background: var(--text-disabled);
            color: white;
        }
        
        .my-applications {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .applications-title {
            display: flex;
            align-items: center;
        }
        
        .applications-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .applications-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .application-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .application-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .application-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .application-item.in-progress {
            border-left: 4px solid var(--info-color);
        }
        
        .application-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .status-in-progress {
            background: var(--info-color);
            color: white;
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .internship-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
        }
        
        .info-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .info-content {
            color: var(--text-secondary);
        }
        
        .internship-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .internship-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .form-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .form-upload:hover {
            border-color: var(--primary-color);
            background: var(--bg-tertiary);
        }
        
        .upload-icon {
            font-size: var(--font-size-h2);
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-list {
            margin-top: var(--margin-sm);
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }
        
        .file-item:last-child {
            margin-bottom: 0;
        }
        
        .file-icon {
            margin-right: var(--margin-sm);
            color: var(--primary-color);
        }
        
        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">实习申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="internship-header">
            <div class="header-title">实习申请</div>
            <div class="header-subtitle">申请各类实习，积累实践经验</div>
        </div>

        <!-- 实习类型 -->
        <div class="internship-types">
            <div class="type-item" onclick="showInternshipForm('practice')">
                <div class="type-icon practice">
                    <i class="ace-icon fa fa-cogs"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">认识实习</div>
                    <div class="type-desc">了解专业相关行业和企业</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showInternshipForm('graduation')">
                <div class="type-icon graduation">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">毕业实习</div>
                    <div class="type-desc">毕业前的综合实践训练</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showInternshipForm('professional')">
                <div class="type-icon professional">
                    <i class="ace-icon fa fa-briefcase"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">专业实习</div>
                    <div class="type-desc">专业技能的实际应用</div>
                </div>
                <div class="type-status status-limited">有限制</div>
            </div>

            <div class="type-item" onclick="showInternshipForm('social')">
                <div class="type-icon social">
                    <i class="ace-icon fa fa-users"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">社会实践</div>
                    <div class="type-desc">参与社会公益和志愿服务</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>
        </div>

        <!-- 我的申请 -->
        <div class="my-applications">
            <div class="applications-header">
                <div class="applications-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的申请</span>
                </div>
                <div class="applications-count" id="applicationsCount">0</div>
            </div>

            <div id="applicationsList">
                <!-- 申请列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-briefcase"></i>
            <div id="emptyMessage">暂无实习申请记录</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 实习申请表单 -->
    <div class="internship-form" id="internshipForm">
        <div class="form-header">
            <div class="form-back" onclick="closeInternshipForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">实习申请</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">实习类型</div>
                    <input type="text" class="form-input" id="internshipType" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业班级</div>
                    <input type="text" class="form-input" id="majorClass" readonly>
                </div>
            </div>

            <!-- 实习单位信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-building"></i>
                    <span>实习单位信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">实习单位名称</div>
                    <input type="text" class="form-input" id="companyName" placeholder="请输入实习单位全称">
                </div>

                <div class="form-group">
                    <div class="form-label required">单位性质</div>
                    <select class="form-input" id="companyType">
                        <option value="">请选择单位性质</option>
                        <option value="state">国有企业</option>
                        <option value="private">民营企业</option>
                        <option value="foreign">外资企业</option>
                        <option value="government">政府机关</option>
                        <option value="institution">事业单位</option>
                        <option value="ngo">非营利组织</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label required">单位地址</div>
                    <textarea class="form-input" id="companyAddress" rows="2" placeholder="请输入详细地址"></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">单位网站</div>
                    <input type="url" class="form-input" id="companyWebsite" placeholder="请输入单位官网地址">
                </div>
            </div>

            <!-- 实习岗位信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-briefcase"></i>
                    <span>实习岗位信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">实习岗位</div>
                    <input type="text" class="form-input" id="position" placeholder="请输入实习岗位名称">
                </div>

                <div class="form-group">
                    <div class="form-label required">岗位职责</div>
                    <textarea class="form-input form-textarea" id="jobDescription"
                              placeholder="请详细描述实习岗位的主要职责和工作内容..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label required">实习时间</div>
                    <div style="display: flex; gap: 8px;">
                        <input type="date" class="form-input" id="startDate" style="flex: 1;" placeholder="开始日期">
                        <input type="date" class="form-input" id="endDate" style="flex: 1;" placeholder="结束日期">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">实习津贴</div>
                    <input type="number" class="form-input" id="salary" placeholder="请输入月实习津贴（元）" min="0">
                </div>
            </div>

            <!-- 指导教师信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user-tie"></i>
                    <span>指导教师信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">校内指导教师</div>
                    <select class="form-input" id="schoolSupervisor">
                        <option value="">请选择校内指导教师</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label required">企业指导教师</div>
                    <input type="text" class="form-input" id="companySupervisor" placeholder="请输入企业指导教师姓名">
                </div>

                <div class="form-group">
                    <div class="form-label required">企业指导教师职务</div>
                    <input type="text" class="form-input" id="supervisorPosition" placeholder="请输入职务">
                </div>

                <div class="form-group">
                    <div class="form-label required">企业指导教师联系方式</div>
                    <input type="tel" class="form-input" id="supervisorPhone" placeholder="请输入联系电话">
                </div>
            </div>

            <!-- 申请说明 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请说明</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">申请理由</div>
                    <textarea class="form-input form-textarea" id="applicationReason"
                              placeholder="请说明申请该实习的理由和目标..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">个人优势</div>
                    <textarea class="form-input form-textarea" id="personalAdvantage"
                              placeholder="请说明您在该实习岗位上的个人优势..."></textarea>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮箱地址</div>
                    <input type="email" class="form-input" id="email" placeholder="请输入邮箱地址">
                </div>

                <div class="form-group">
                    <div class="form-label">紧急联系人</div>
                    <input type="text" class="form-input" id="emergencyContact" placeholder="请输入紧急联系人姓名">
                </div>

                <div class="form-group">
                    <div class="form-label">紧急联系人电话</div>
                    <input type="tel" class="form-input" id="emergencyPhone" placeholder="请输入紧急联系人电话">
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-paperclip"></i>
                    <span>附件材料</span>
                </div>

                <div class="form-group">
                    <div class="form-label">上传附件</div>
                    <div class="form-upload" onclick="selectFiles()">
                        <div class="upload-icon">
                            <i class="ace-icon fa fa-cloud-upload"></i>
                        </div>
                        <div class="upload-text">点击上传实习协议、简历等材料</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                    <div class="file-list" id="fileList">
                        <!-- 文件列表将动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeInternshipForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitInternshipApplication();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let myApplications = [];
        let currentInternshipType = '';
        let uploadedFiles = [];
        let studentInfo = {};
        let schoolSupervisors = [];

        $(function() {
            initPage();
            loadStudentInfo();
            loadSchoolSupervisors();
            loadMyApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFileUpload();
        }

        // 绑定文件上传
        function bindFileUpload() {
            $('#fileInput').change(function() {
                handleFileSelect(this.files);
            });
        }

        // 加载学生信息
        function loadStudentInfo() {
            $.ajax({
                url: "/student/personalManagement/internshipApplication/getStudentInfo",
                type: "post",
                dataType: "json",
                success: function(data) {
                    studentInfo = data || {};
                },
                error: function() {
                    console.log('加载学生信息失败');
                }
            });
        }

        // 加载校内指导教师
        function loadSchoolSupervisors() {
            $.ajax({
                url: "/student/personalManagement/internshipApplication/getSchoolSupervisors",
                type: "post",
                dataType: "json",
                success: function(data) {
                    schoolSupervisors = data.supervisors || [];
                    renderSupervisorOptions();
                },
                error: function() {
                    console.log('加载指导教师失败');
                }
            });
        }

        // 渲染指导教师选项
        function renderSupervisorOptions() {
            const select = $('#schoolSupervisor');
            select.find('option:not(:first)').remove();

            schoolSupervisors.forEach(supervisor => {
                select.append(`<option value="${supervisor.id}">${supervisor.name} - ${supervisor.title}</option>`);
            });
        }

        // 加载我的申请
        function loadMyApplications() {
            showLoading(true);

            $.ajax({
                url: "/student/personalManagement/internshipApplication/getMyApplications",
                type: "post",
                dataType: "json",
                success: function(data) {
                    myApplications = data.applications || [];
                    renderApplicationsList();
                    updateApplicationsCount();
                    showLoading(false);
                },
                error: function() {
                    showError('加载申请列表失败');
                    showLoading(false);
                }
            });
        }

        // 渲染申请列表
        function renderApplicationsList() {
            const container = $('#applicationsList');
            container.empty();

            if (myApplications.length === 0) {
                showEmptyState('暂无实习申请记录');
                return;
            } else {
                hideEmptyState();
            }

            myApplications.forEach(application => {
                const applicationHtml = createApplicationItem(application);
                container.append(applicationHtml);
            });
        }

        // 创建申请项
        function createApplicationItem(application) {
            const statusClass = getStatusClass(application.status);
            const statusText = getStatusText(application.status);

            return `
                <div class="application-item ${statusClass}" onclick="showApplicationDetail('${application.id}')">
                    <div class="application-basic">
                        <div class="application-title">${getInternshipTypeText(application.type)} - ${application.companyName}</div>
                        <div class="application-status status-${statusClass}">${statusText}</div>
                    </div>
                    <div class="application-details">
                        <div class="detail-item">
                            <span>申请时间:</span>
                            <span>${formatDate(application.createTime)}</span>
                        </div>
                        <div class="detail-item">
                            <span>实习岗位:</span>
                            <span>${application.position}</span>
                        </div>
                        <div class="detail-item">
                            <span>实习时间:</span>
                            <span>${formatDate(application.startDate)} - ${formatDate(application.endDate)}</span>
                        </div>
                        <div class="detail-item">
                            <span>校内指导:</span>
                            <span>${application.schoolSupervisorName}</span>
                        </div>
                    </div>
                    <div class="internship-info">
                        <div class="info-title">实习单位</div>
                        <div class="info-content">${application.companyName} - ${application.companyAddress}</div>
                    </div>
                </div>
            `;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'pending';
                case 'approved': return 'approved';
                case 'rejected': return 'rejected';
                case 'in-progress': return 'in-progress';
                default: return 'pending';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待审核';
                case 'approved': return '已通过';
                case 'rejected': return '已拒绝';
                case 'in-progress': return '实习中';
                default: return '未知';
            }
        }

        // 获取实习类型文本
        function getInternshipTypeText(type) {
            switch(type) {
                case 'practice': return '认识实习';
                case 'graduation': return '毕业实习';
                case 'professional': return '专业实习';
                case 'social': return '社会实践';
                default: return '实习';
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString();
        }

        // 显示实习申请表单
        function showInternshipForm(type) {
            currentInternshipType = type;

            // 设置表单标题
            $('#formTitle').text(getInternshipTypeText(type) + '申请');
            $('#internshipType').val(getInternshipTypeText(type));

            // 填充学生信息
            $('#studentId').val(studentInfo.studentId || '');
            $('#studentName').val(studentInfo.name || '');
            $('#majorClass').val((studentInfo.major || '') + ' ' + (studentInfo.className || ''));

            // 清空表单
            resetForm();

            // 显示表单
            $('#internshipForm').addClass('show');
        }

        // 重置表单
        function resetForm() {
            $('#companyName').val('');
            $('#companyType').val('');
            $('#companyAddress').val('');
            $('#companyWebsite').val('');
            $('#position').val('');
            $('#jobDescription').val('');
            $('#startDate').val('');
            $('#endDate').val('');
            $('#salary').val('');
            $('#schoolSupervisor').val('');
            $('#companySupervisor').val('');
            $('#supervisorPosition').val('');
            $('#supervisorPhone').val('');
            $('#applicationReason').val('');
            $('#personalAdvantage').val('');
            $('#contactPhone').val('');
            $('#email').val('');
            $('#emergencyContact').val('');
            $('#emergencyPhone').val('');
            uploadedFiles = [];
            renderFileList();
        }

        // 关闭实习申请表单
        function closeInternshipForm() {
            $('#internshipForm').removeClass('show');
        }

        // 选择文件
        function selectFiles() {
            $('#fileInput').click();
        }

        // 处理文件选择
        function handleFileSelect(files) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (validateFile(file)) {
                    uploadedFiles.push({
                        id: Date.now() + i,
                        file: file,
                        name: file.name,
                        size: file.size
                    });
                }
            }
            renderFileList();
        }

        // 验证文件
        function validateFile(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['application/pdf', 'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'image/jpeg', 'image/jpg', 'image/png'];

            if (file.size > maxSize) {
                showError('文件大小不能超过10MB');
                return false;
            }

            if (!allowedTypes.includes(file.type)) {
                showError('只支持PDF、Word文档和图片格式');
                return false;
            }

            return true;
        }

        // 渲染文件列表
        function renderFileList() {
            const container = $('#fileList');
            container.empty();

            uploadedFiles.forEach(fileItem => {
                const fileHtml = `
                    <div class="file-item">
                        <i class="file-icon ace-icon fa fa-file"></i>
                        <span class="file-name">${fileItem.name}</span>
                        <i class="file-remove ace-icon fa fa-times" onclick="removeFile('${fileItem.id}')"></i>
                    </div>
                `;
                container.append(fileHtml);
            });
        }

        // 移除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id != fileId);
            renderFileList();
        }

        // 保存草稿
        function saveDraft() {
            if (!validateForm(false)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = true;

            submitFormData(formData, '草稿保存成功');
        }

        // 提交实习申请
        function submitInternshipApplication() {
            if (!validateForm(true)) {
                return;
            }

            const formData = collectFormData();
            formData.isDraft = false;

            const message = `确定要提交${getInternshipTypeText(currentInternshipType)}申请吗？\n\n提交后将进入审核流程，请确保信息准确无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        submitFormData(formData, '实习申请提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    submitFormData(formData, '实习申请提交成功');
                }
            }
        }

        // 收集表单数据
        function collectFormData() {
            return {
                internshipType: currentInternshipType,
                companyName: $('#companyName').val(),
                companyType: $('#companyType').val(),
                companyAddress: $('#companyAddress').val(),
                companyWebsite: $('#companyWebsite').val(),
                position: $('#position').val(),
                jobDescription: $('#jobDescription').val(),
                startDate: $('#startDate').val(),
                endDate: $('#endDate').val(),
                salary: $('#salary').val(),
                schoolSupervisor: $('#schoolSupervisor').val(),
                companySupervisor: $('#companySupervisor').val(),
                supervisorPosition: $('#supervisorPosition').val(),
                supervisorPhone: $('#supervisorPhone').val(),
                applicationReason: $('#applicationReason').val(),
                personalAdvantage: $('#personalAdvantage').val(),
                contactPhone: $('#contactPhone').val(),
                email: $('#email').val(),
                emergencyContact: $('#emergencyContact').val(),
                emergencyPhone: $('#emergencyPhone').val(),
                attachments: uploadedFiles
            };
        }

        // 验证表单
        function validateForm(isSubmit) {
            if (!$('#companyName').val().trim()) {
                showError('请填写实习单位名称');
                return false;
            }

            if (!$('#companyType').val()) {
                showError('请选择单位性质');
                return false;
            }

            if (!$('#companyAddress').val().trim()) {
                showError('请填写单位地址');
                return false;
            }

            if (!$('#position').val().trim()) {
                showError('请填写实习岗位');
                return false;
            }

            if (!$('#jobDescription').val().trim()) {
                showError('请填写岗位职责');
                return false;
            }

            if (!$('#startDate').val()) {
                showError('请选择实习开始日期');
                return false;
            }

            if (!$('#endDate').val()) {
                showError('请选择实习结束日期');
                return false;
            }

            if (new Date($('#startDate').val()) >= new Date($('#endDate').val())) {
                showError('结束日期必须晚于开始日期');
                return false;
            }

            if (!$('#schoolSupervisor').val()) {
                showError('请选择校内指导教师');
                return false;
            }

            if (!$('#companySupervisor').val().trim()) {
                showError('请填写企业指导教师');
                return false;
            }

            if (!$('#supervisorPosition').val().trim()) {
                showError('请填写企业指导教师职务');
                return false;
            }

            if (!$('#supervisorPhone').val().trim()) {
                showError('请填写企业指导教师联系方式');
                return false;
            }

            if (!$('#applicationReason').val().trim()) {
                showError('请填写申请理由');
                return false;
            }

            if (!$('#contactPhone').val().trim()) {
                showError('请填写联系电话');
                return false;
            }

            return true;
        }

        // 提交表单数据
        function submitFormData(formData, successMessage) {
            $.ajax({
                url: "/student/personalManagement/internshipApplication/submitInternshipApplication",
                type: "post",
                data: formData,
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        closeInternshipForm();
                        loadMyApplications();
                    } else {
                        showError(data.message || '操作失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 显示申请详情
        function showApplicationDetail(applicationId) {
            const application = myApplications.find(app => app.id === applicationId);
            if (!application) return;

            let message = `实习申请详情\n\n`;
            message += `实习类型：${getInternshipTypeText(application.type)}\n`;
            message += `申请时间：${formatDate(application.createTime)}\n`;
            message += `实习单位：${application.companyName}\n`;
            message += `单位性质：${getCompanyTypeText(application.companyType)}\n`;
            message += `实习岗位：${application.position}\n`;
            message += `实习时间：${formatDate(application.startDate)} - ${formatDate(application.endDate)}\n`;
            message += `校内指导：${application.schoolSupervisorName}\n`;
            message += `企业指导：${application.companySupervisor}\n`;
            message += `申请理由：${application.applicationReason}\n`;
            message += `当前状态：${getStatusText(application.status)}\n`;

            if (application.reviewComment) {
                message += `审核意见：${application.reviewComment}\n`;
            }

            if (application.reviewTime) {
                message += `审核时间：${formatDate(application.reviewTime)}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 获取单位性质文本
        function getCompanyTypeText(type) {
            switch(type) {
                case 'state': return '国有企业';
                case 'private': return '民营企业';
                case 'foreign': return '外资企业';
                case 'government': return '政府机关';
                case 'institution': return '事业单位';
                case 'ngo': return '非营利组织';
                case 'other': return '其他';
                default: return type;
            }
        }

        // 更新申请数量
        function updateApplicationsCount() {
            $('#applicationsCount').text(myApplications.length);
        }

        // 刷新数据
        function refreshData() {
            loadMyApplications();
        }

        // 显示空状态
        function showEmptyState(message) {
            $('#emptyMessage').text(message);
            $('#emptyState').show();
            $('.my-applications').hide();
        }

        // 隐藏空状态
        function hideEmptyState() {
            $('#emptyState').hide();
            $('.my-applications').show();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
                $('.my-applications').hide();
                $('#emptyState').hide();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });

        // 处理触摸滑动关闭表单
        let startX = 0;

        $('#internshipForm').on('touchstart', function(e) {
            startX = e.originalEvent.touches[0].clientX;
        });

        $('#internshipForm').on('touchmove', function(e) {
            if (!startX) return;

            const currentX = e.originalEvent.touches[0].clientX;
            const diffX = currentX - startX;

            // 向右滑动关闭
            if (diffX > 50) {
                closeInternshipForm();
            }
        });

        $('#internshipForm').on('touchend', function() {
            startX = 0;
        });
    </script>
</body>
</html>
