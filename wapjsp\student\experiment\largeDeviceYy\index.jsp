<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>大型设备预约</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 大型设备预约页面样式 */
        .device-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .device-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .device-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .device-info-card {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .info-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-title i {
            color: var(--primary-color);
        }
        
        .device-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
        }
        
        .detail-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .detail-value {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            text-align: right;
            flex: 1;
            margin-left: var(--margin-sm);
        }
        
        .reservations-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .container-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .container-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .container-title i {
            color: var(--primary-color);
        }
        
        .btn-add-reservation {
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-add-reservation:hover {
            background: var(--success-dark);
        }
        
        .reservations-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .reservation-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .reservation-item:last-child {
            border-bottom: none;
        }
        
        .reservation-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary-light);
            color: var(--primary-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-small);
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .reservation-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .reservation-time {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .reservation-status {
            display: flex;
            gap: var(--spacing-sm);
            font-size: var(--font-size-mini);
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .status-badge.pending {
            background: var(--warning-light);
            color: var(--warning-dark);
        }
        
        .status-badge.approved {
            background: var(--success-light);
            color: var(--success-dark);
        }
        
        .status-badge.rejected {
            background: var(--error-light);
            color: var(--error-dark);
        }
        
        .reservation-actions {
            display: flex;
            gap: var(--spacing-sm);
            flex-shrink: 0;
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 6px 8px;
            font-size: var(--font-size-mini);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn.view {
            color: var(--info-color);
            border-color: var(--info-color);
        }
        
        .action-btn.view:hover {
            background: var(--info-light);
        }
        
        .action-btn.edit {
            color: var(--warning-color);
            border-color: var(--warning-color);
        }
        
        .action-btn.edit:hover {
            background: var(--warning-light);
        }
        
        .action-btn.delete {
            color: var(--error-color);
            border-color: var(--error-color);
        }
        
        .action-btn.delete:hover {
            background: var(--error-light);
        }
        
        .action-btn:disabled {
            color: var(--text-disabled);
            border-color: var(--border-primary);
            cursor: not-allowed;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--padding-xl);
            color: var(--text-disabled);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            color: var(--text-disabled);
        }
        
        .empty-state-text {
            font-size: var(--font-size-base);
            margin-bottom: 4px;
        }
        
        .empty-state-desc {
            font-size: var(--font-size-small);
        }
        
        .pagination-container {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pagination-info {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-sm);
        }
        
        .btn-page {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .btn-page:hover {
            background: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-dark);
        }
        
        .btn-page.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .btn-page:disabled {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
            color: var(--text-disabled);
            cursor: not-allowed;
        }
        
        @media (max-width: 480px) {
            .detail-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }
            
            .detail-value {
                text-align: left;
                margin-left: 0;
            }
            
            .reservation-item {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
            
            .reservation-actions {
                align-self: stretch;
                justify-content: space-around;
            }
        }
    </style>
</head>
<body>
    <input type="hidden" id="tokenValue" name="tokenValue" value="${token_in_session}">
    <input type="hidden" id="rwsqId" value="${rwsqId}">
    <input type="hidden" id="sbmc" value="${sbmc}">
    <input type="hidden" id="sbgg" value="${sbgg}">
    <input type="hidden" id="sbxh" value="${sbxh}">
    <input type="hidden" id="cfdd" value="${cfdd}">
    <input type="hidden" id="sbbm" value="${sbbm}">
    
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="goBack();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">大型设备预约</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 大型设备预约头部 -->
        <div class="device-header">
            <div class="device-title">大型设备预约</div>
            <div class="device-desc">管理您的设备预约申请</div>
        </div>
        
        <!-- 设备信息 -->
        <div class="device-info-card">
            <div class="info-title">
                <i class="ace-icon fa fa-cogs"></i>
                设备信息
            </div>
            <div class="device-details">
                <div class="detail-item">
                    <span class="detail-label">设备名称</span>
                    <span class="detail-value">${sbmc}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">规格</span>
                    <span class="detail-value">${sbgg}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">型号</span>
                    <span class="detail-value">${sbxh}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">存放地点</span>
                    <span class="detail-value">${cfdd}</span>
                </div>
            </div>
        </div>
        
        <!-- 预约记录 -->
        <div class="reservations-container">
            <div class="container-header">
                <div class="container-title">
                    <i class="ace-icon fa fa-calendar"></i>
                    预约记录
                </div>
                <button class="btn-add-reservation" onclick="addReservation();">
                    <i class="ace-icon fa fa-plus"></i>
                    新增预约
                </button>
            </div>
            
            <div class="reservations-list" id="reservationsList">
                <!-- 动态加载预约记录 -->
            </div>
        </div>
        
        <!-- 分页容器 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="pagination-buttons" id="paginationButtons"></div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 30;
        let totalCount = 0;

        $(function() {
            initPage();
            loadReservations();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 加载预约记录
        function loadReservations(page = 1) {
            currentPage = page;
            showLoading(true);

            const rwsqId = $('#rwsqId').val();

            $.ajax({
                url: "/student/experiment/dispark/largeDeviceYy/findYyData",
                type: "get",
                data: {
                    pageNum: page,
                    pageSize: pageSize,
                    rwsqId: rwsqId
                },
                dataType: "json",
                success: function(data) {
                    if (data && data.records) {
                        renderReservations(data.records);
                        renderPagination(data.pageContext);
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr) {
                    showError("获取预约记录失败");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 渲染预约记录
        function renderReservations(reservations) {
            const container = $('#reservationsList');

            if (!reservations || reservations.length === 0) {
                showEmptyState();
                return;
            }

            let reservationsHtml = '';

            reservations.forEach((reservation, index) => {
                const serialNumber = (currentPage - 1) * pageSize + 1 + index;

                // 审批状态处理
                const syzxStatus = getStatusBadge(reservation.SYZXSPZT, '实验中心');
                const yxStatus = getStatusBadge(reservation.YXSPZT, '院系');
                const sbcStatus = getStatusBadge(reservation.SBCSPZT, '设备处');

                // 操作按钮
                const canEdit = !reservation.SYZXSPZT;
                const actionsHtml = `
                    <div class="reservation-actions">
                        <button class="action-btn view" onclick="viewReservation('${reservation.SQID}');" title="查看">
                            <i class="ace-icon fa fa-eye"></i>
                        </button>
                        ${canEdit ? `
                            <button class="action-btn edit" onclick="editReservation('${reservation.SQID}');" title="修改">
                                <i class="ace-icon fa fa-edit"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteReservation('${reservation.SQID}');" title="删除">
                                <i class="ace-icon fa fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                `;

                reservationsHtml += `
                    <div class="reservation-item">
                        <div class="reservation-number">${serialNumber}</div>
                        <div class="reservation-info">
                            <div class="reservation-time">${reservation.SQSJ ? reservation.SQSJ.substring(0, 10) : '-'}</div>
                            <div class="reservation-status">
                                ${syzxStatus}
                                ${yxStatus}
                                ${sbcStatus}
                            </div>
                        </div>
                        ${actionsHtml}
                    </div>
                `;
            });

            container.html(reservationsHtml);
        }

        // 获取状态标识
        function getStatusBadge(status, label) {
            if (!status) {
                return `<span class="status-badge pending">${label}:待审批</span>`;
            } else if (status === '1') {
                return `<span class="status-badge approved">${label}:通过</span>`;
            } else {
                return `<span class="status-badge rejected">${label}:未通过</span>`;
            }
        }

        // 显示空状态
        function showEmptyState() {
            const container = $('#reservationsList');
            container.html(`
                <div class="empty-state">
                    <i class="ace-icon fa fa-calendar-times-o"></i>
                    <div class="empty-state-text">您还没有预约过该设备</div>
                    <div class="empty-state-desc">点击"新增预约"开始预约</div>
                </div>
            `);
            $('#paginationContainer').hide();
        }

        // 渲染分页
        function renderPagination(pageContext) {
            if (!pageContext || pageContext.totalCount <= pageSize) {
                $('#paginationContainer').hide();
                return;
            }

            const container = $('#paginationButtons');
            const info = $('#paginationInfo');

            const totalPages = Math.ceil(pageContext.totalCount / pageSize);
            const currentPage = pageContext.pageNum;

            // 更新分页信息
            info.text(`共 ${pageContext.totalCount} 条记录，第 ${currentPage} / ${totalPages} 页`);

            let paginationHtml = '';

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${prevDisabled} onclick="loadReservations(${currentPage - 1});">上一页</button>`;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<button class="btn-page" onclick="loadReservations(1);">1</button>`;
                if (startPage > 2) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHtml += `<button class="btn-page ${activeClass}" onclick="loadReservations(${i});">${i}</button>`;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<span class="btn-page" style="cursor: default;">...</span>`;
                }
                paginationHtml += `<button class="btn-page" onclick="loadReservations(${totalPages});">${totalPages}</button>`;
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            paginationHtml += `<button class="btn-page" ${nextDisabled} onclick="loadReservations(${currentPage + 1});">下一页</button>`;

            container.html(paginationHtml);
            $('#paginationContainer').show();
        }

        // 新增预约
        function addReservation() {
            const rwsqId = $('#rwsqId').val();
            const sbmc = $('#sbmc').val();
            const sbgg = $('#sbgg').val();
            const sbxh = $('#sbxh').val();
            const cfdd = $('#cfdd').val();
            const sbbm = $('#sbbm').val();

            const url = `/student/experiment/dispark/largeDeviceYy/addInfo?rwsqId=${rwsqId}&sbmc=${encodeURIComponent(sbmc)}&sbgg=${encodeURIComponent(sbgg)}&sbxh=${encodeURIComponent(sbxh)}&cfdd=${encodeURIComponent(cfdd)}&sbbm=${encodeURIComponent(sbbm)}`;

            if (parent && parent.addTab) {
                parent.addTab('新增预约', url);
            } else {
                window.location.href = url;
            }
        }

        // 查看预约
        function viewReservation(sqId) {
            const rwsqId = $('#rwsqId').val();
            const sbmc = $('#sbmc').val();
            const sbgg = $('#sbgg').val();
            const sbxh = $('#sbxh').val();
            const cfdd = $('#cfdd').val();
            const sbbm = $('#sbbm').val();

            const url = `/student/experiment/dispark/largeDeviceYy/largeDeviceYysqInfoGet?sqId=${sqId}&sbmc=${encodeURIComponent(sbmc)}&sbgg=${encodeURIComponent(sbgg)}&sbxh=${encodeURIComponent(sbxh)}&cfdd=${encodeURIComponent(cfdd)}&rwsqId=${rwsqId}&sbbm=${encodeURIComponent(sbbm)}&type=1`;

            if (parent && parent.addTab) {
                parent.addTab('查看预约', url);
            } else {
                window.location.href = url;
            }
        }

        // 修改预约
        function editReservation(sqId) {
            const rwsqId = $('#rwsqId').val();
            const sbmc = $('#sbmc').val();
            const sbgg = $('#sbgg').val();
            const sbxh = $('#sbxh').val();
            const cfdd = $('#cfdd').val();
            const sbbm = $('#sbbm').val();

            const url = `/student/experiment/dispark/largeDeviceYy/editInfo?rwsqId=${rwsqId}&sbmc=${encodeURIComponent(sbmc)}&sbgg=${encodeURIComponent(sbgg)}&sbxh=${encodeURIComponent(sbxh)}&cfdd=${encodeURIComponent(cfdd)}&sbbm=${encodeURIComponent(sbbm)}&sqId=${sqId}`;

            if (parent && parent.addTab) {
                parent.addTab('修改预约', url);
            } else {
                window.location.href = url;
            }
        }

        // 删除预约
        function deleteReservation(sqId) {
            if (confirm('删除后将不可恢复，是否依然删除？')) {
                showLoading(true);

                $.ajax({
                    url: "/student/experiment/dispark/largeDeviceYy/removeInfo",
                    type: "post",
                    data: {
                        tokenValue: $('#tokenValue').val(),
                        sqId: sqId
                    },
                    dataType: "json",
                    success: function(data) {
                        $('#tokenValue').val(data.token);

                        if (data.result.indexOf("/") !== -1) {
                            window.location.href = data.result;
                        } else if (data.result === "ok") {
                            showSuccess("删除成功！");
                            loadReservations(currentPage);
                        } else {
                            showError(data.result);
                        }
                    },
                    error: function(xhr) {
                        showError("删除失败");
                    },
                    complete: function() {
                        showLoading(false);
                    }
                });
            }
        }

        // 返回
        function goBack() {
            window.location.href = "/student/experiment/dispark/largeDeviceYy/largeDeviceYysqView";
        }

        // 刷新数据
        function refreshData() {
            loadReservations(currentPage);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
