/* 移动端框架CSS - Mobile Framework */

/* ========== 基础变量 ========== */
:root {
    /* 颜色系统 */
    --primary-color: #007bff;
    --primary-light: #66b3ff;
    --primary-dark: #0056b3;
    
    --success-color: #28a745;
    --success-light: #71dd8a;
    --success-dark: #1e7e34;
    
    --warning-color: #ffc107;
    --warning-light: #ffda6a;
    --warning-dark: #d39e00;
    
    --error-color: #dc3545;
    --error-light: #f1959b;
    --error-dark: #bd2130;
    
    --info-color: #17a2b8;
    --info-light: #7dd3fc;
    --info-dark: #117a8b;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #f1f3f4;
    --bg-color-active: #e9ecef;
    
    /* 文本色 */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-disabled: #adb5bd;
    
    /* 边框色 */
    --border-primary: #dee2e6;
    --border-secondary: #e9ecef;
    --divider-color: #f1f3f4;
    
    /* 字体大小 */
    --font-size-mini: 10px;
    --font-size-small: 12px;
    --font-size-base: 14px;
    --font-size-large: 16px;
    --font-size-h4: 18px;
    --font-size-h3: 20px;
    --font-size-h2: 24px;
    --font-size-h1: 28px;
    
    /* 行高 */
    --line-height-base: 1.5;
    --line-height-large: 1.6;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;
    
    --margin-xs: 4px;
    --margin-sm: 8px;
    --margin-md: 12px;
    --margin-lg: 16px;
    --margin-xl: 24px;
    
    --padding-xs: 4px;
    --padding-sm: 8px;
    --padding-md: 12px;
    --padding-lg: 16px;
    --padding-xl: 24px;
    
    /* 动画 */
    --transition-base: 0.3s ease;
    --transition-fast: 0.15s ease;
    
    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* ========== 基础重置 ========== */
* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ========== 页面容器 ========== */
.page-mobile {
    min-height: 100vh;
    background-color: var(--bg-secondary);
    padding-bottom: env(safe-area-inset-bottom);
}

/* ========== 导航栏 ========== */
.navbar-mobile {
    background: var(--bg-primary);
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--padding-md);
    border-bottom: 1px solid var(--border-primary);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-back {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--primary-color);
    font-size: var(--font-size-large);
}

.navbar-title {
    font-size: var(--font-size-large);
    font-weight: 500;
    color: var(--text-primary);
    text-align: center;
    flex: 1;
}

.navbar-action {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--primary-color);
    font-size: var(--font-size-large);
}

/* ========== 按钮组件 ========== */
.btn-mobile {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: var(--font-size-small);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-base);
    min-height: 36px;
    gap: var(--spacing-xs);
}

.btn-mobile:active {
    transform: scale(0.98);
}

.btn-mobile.flex-1 {
    flex: 1;
}

/* ========== 空状态 ========== */
.empty-state {
    text-align: center;
    padding: 60px var(--padding-md);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    color: var(--text-disabled);
    margin-bottom: var(--margin-md);
    display: block;
}

/* ========== 加载状态 ========== */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px var(--padding-md);
    color: var(--text-secondary);
}

.loading-container i {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: var(--margin-sm);
}

/* ========== 响应式工具类 ========== */
.flex {
    display: flex;
}

.flex-1 {
    flex: 1;
}

.flex-column {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* ========== 间距工具类 ========== */
.m-0 { margin: 0; }
.m-1 { margin: var(--margin-xs); }
.m-2 { margin: var(--margin-sm); }
.m-3 { margin: var(--margin-md); }
.m-4 { margin: var(--margin-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--padding-xs); }
.p-2 { padding: var(--padding-sm); }
.p-3 { padding: var(--padding-md); }
.p-4 { padding: var(--padding-lg); }

/* ========== 触摸优化 ========== */
.touch-action-none {
    touch-action: none;
}

.touch-action-pan-x {
    touch-action: pan-x;
}

.touch-action-pan-y {
    touch-action: pan-y;
}

/* ========== 安全区域适配 ========== */
@supports (padding: max(0px)) {
    .safe-area-inset-top {
        padding-top: max(var(--padding-md), env(safe-area-inset-top));
    }
    
    .safe-area-inset-bottom {
        padding-bottom: max(var(--padding-md), env(safe-area-inset-bottom));
    }
    
    .safe-area-inset-left {
        padding-left: max(var(--padding-md), env(safe-area-inset-left));
    }
    
    .safe-area-inset-right {
        padding-right: max(var(--padding-md), env(safe-area-inset-right));
    }
}

/* ========== 媒体查询 ========== */
@media (max-width: 375px) {
    :root {
        --font-size-base: 13px;
        --padding-md: 10px;
        --margin-md: 10px;
    }
}

@media (min-width: 768px) {
    .page-mobile {
        max-width: 414px;
        margin: 0 auto;
        box-shadow: var(--shadow-lg);
    }
}

/* ========== 滚动条优化 ========== */
::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--text-disabled);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* ========== 选择文本优化 ========== */
::selection {
    background: var(--primary-light);
    color: white;
}

::-moz-selection {
    background: var(--primary-light);
    color: white;
}
