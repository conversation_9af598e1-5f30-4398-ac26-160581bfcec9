<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="cache" uri="http://www.urpSoft.com/cache" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学生证明打印</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 学生证明打印页面样式 */
        .certificate-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-lg);
            text-align: center;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .certificate-title {
            font-size: var(--font-size-h3);
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .certificate-desc {
            font-size: var(--font-size-small);
            opacity: 0.9;
        }
        
        .certificate-selector {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .selector-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .selector-title i {
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-select {
            width: 100%;
            padding: var(--padding-sm);
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-certificate {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-base);
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all var(--transition-base);
        }
        
        .btn-preview {
            background: var(--info-color);
            color: white;
        }
        
        .btn-preview:hover {
            background: var(--info-dark);
            transform: translateY(-1px);
        }
        
        .btn-print {
            background: var(--success-color);
            color: white;
        }
        
        .btn-print:hover {
            background: var(--success-dark);
            transform: translateY(-1px);
        }
        
        .btn-certificate:active {
            transform: translateY(0);
        }
        
        .certificate-preview {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .preview-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .preview-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .preview-title i {
            color: var(--success-color);
        }
        
        .btn-fullscreen {
            background: var(--warning-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: var(--font-size-small);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-base);
        }
        
        .btn-fullscreen:hover {
            background: var(--warning-dark);
        }
        
        .preview-content {
            background: #515558;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-h4);
            position: relative;
        }
        
        .preview-placeholder {
            text-align: center;
            opacity: 0.7;
        }
        
        .preview-placeholder i {
            font-size: 48px;
            margin-bottom: var(--margin-md);
            display: block;
        }
        
        .preview-embed {
            width: 100%;
            height: 400px;
            border: none;
        }
        
        .certificate-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .types-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .types-title i {
            color: var(--warning-color);
        }
        
        .type-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        
        .type-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
            border: 2px solid transparent;
        }
        
        .type-item:hover {
            background: var(--bg-secondary);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .type-item.selected {
            background: var(--primary-light);
            border-color: var(--primary-color);
            color: var(--primary-dark);
        }
        
        .type-icon {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: 8px;
        }
        
        .type-name {
            font-size: var(--font-size-small);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .type-grid {
                grid-template-columns: 1fr;
            }
            
            .preview-content {
                min-height: 300px;
            }
            
            .preview-embed {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">学生证明打印</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>
        
        <!-- 证明打印头部 -->
        <div class="certificate-header">
            <div class="certificate-title">学生证明打印</div>
            <div class="certificate-desc">生成和打印各类学生证明文件</div>
        </div>
        
        <!-- 证明类型选择 -->
        <div class="certificate-selector">
            <div class="selector-title">
                <i class="ace-icon fa fa-search"></i>
                生成条件
            </div>
            
            <div class="form-group">
                <label class="form-label">证明类型</label>
                <select id="dymb" name="dymb" class="form-select">
                    <c:forEach items="${mbs}" var="mb">
                        <option value="${mb[0]}">${mb[1]}</option>
                    </c:forEach>
                </select>
            </div>
            
            <div class="action-buttons">
                <button type="button" class="btn-certificate btn-preview" onclick="preview(0);">
                    <i class="ace-icon fa fa-eye"></i>
                    <span>预览</span>
                </button>
                <button type="button" class="btn-certificate btn-print" onclick="preview(1);">
                    <i class="ace-icon fa fa-print"></i>
                    <span>打印</span>
                </button>
            </div>
        </div>
        
        <!-- 证明预览 -->
        <div class="certificate-preview">
            <div class="preview-header">
                <div class="preview-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    证明预览
                </div>
                <button type="button" class="btn-fullscreen" onclick="openFullscreen();">
                    <i class="ace-icon fa fa-expand"></i>
                    <span>全屏</span>
                </button>
            </div>
            
            <div class="preview-content" id="stu_cjd">
                <div class="preview-placeholder">
                    <i class="ace-icon fa fa-file-text"></i>
                    <div>请选择证明类型并点击预览</div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>生成中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let schoolId = '${schoolId}';

        $(function() {
            initPage();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 预览或打印证明
        function preview(flag) {
            const dymb = $("#dymb").val();
            if (!dymb) {
                showError("请选择证明类型！");
                return;
            }

            // 特殊学校的在读证明需要检查预计毕业日期
            if ((dymb === "Enroll@xsgl@zdzm_cn" || dymb === "Enroll@xsgl@zdzm_en") && schoolId === "100054") {
                checkYjbyrq(flag, dymb);
            } else {
                generateCertificate(flag, dymb);
            }
        }

        // 检查预计毕业日期
        function checkYjbyrq(flag, dymb) {
            showLoading(true);

            $.ajax({
                url: "/student/studentCertificatePrinting/index/checkedYjbyrq",
                cache: false,
                type: "post",
                dataType: "json",
                success: function(data) {
                    if (data.data === "success") {
                        generateCertificate(flag, dymb);
                    } else {
                        showError(data.data);
                    }
                },
                error: function(xhr) {
                    showError("错误代码[" + xhr.readyState + "-" + xhr.status + "]:操作失败！");
                },
                complete: function() {
                    showLoading(false);
                }
            });
        }

        // 生成证明
        function generateCertificate(flag, dymb) {
            if (flag === 1) {
                // 打印模式 - 新窗口打开
                const form = document.createElement("form");
                form.action = "/student/studentCertificatePrinting/index/reports?flag=" + flag + "&printType=" + dymb;
                form.method = "post";
                form.target = "_blank";
                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);
            } else {
                // 预览模式 - 嵌入显示
                showLoading(true);

                const embedUrl = "/student/studentCertificatePrinting/index/reports?flag=" + flag + "&printType=" + dymb;
                const embedHtml = `<embed src="${embedUrl}" width="100%" height="100%" class="preview-embed"></embed>`;

                $("#stu_cjd").html(embedHtml);

                // 监听embed加载完成
                setTimeout(function() {
                    showLoading(false);
                }, 2000);
            }
        }

        // 打开全屏预览
        function openFullscreen() {
            const embed = $("#stu_cjd embed");
            if (embed.length > 0) {
                const embedSrc = embed.attr("src");
                if (embedSrc) {
                    window.open(embedSrc, "_blank", "width=800,height=600,scrollbars=yes,resizable=yes");
                } else {
                    showError("请先预览证明！");
                }
            } else {
                showError("请先预览证明！");
            }
        }

        // 刷新数据
        function refreshData() {
            // 清空预览内容
            $("#stu_cjd").html(`
                <div class="preview-placeholder">
                    <i class="ace-icon fa fa-file-text"></i>
                    <div>请选择证明类型并点击预览</div>
                </div>
            `);

            // 重置选择
            $("#dymb").prop("selectedIndex", 0);
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
