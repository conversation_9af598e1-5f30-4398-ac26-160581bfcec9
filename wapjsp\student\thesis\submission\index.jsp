<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>论文提交</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 论文提交页面样式 */
        .submission-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .submission-status {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .status-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .current-status {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
        }
        
        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-h4);
            margin-right: var(--margin-md);
        }
        
        .status-icon.draft {
            background: var(--warning-color);
        }
        
        .status-icon.submitted {
            background: var(--info-color);
        }
        
        .status-icon.approved {
            background: var(--success-color);
        }
        
        .status-icon.rejected {
            background: var(--error-color);
        }
        
        .status-content {
            flex: 1;
        }
        
        .status-text {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .status-meta {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .badge-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .badge-submitted {
            background: var(--info-color);
            color: white;
        }
        
        .badge-approved {
            background: var(--success-color);
            color: white;
        }
        
        .badge-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .submission-form {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .form-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .file-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-md);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .file-upload:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .file-upload.dragover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }
        
        .upload-icon {
            font-size: var(--font-size-h3);
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
        }
        
        .upload-hint {
            font-size: var(--font-size-mini);
            color: var(--text-disabled);
        }
        
        .file-list {
            margin-top: var(--margin-md);
        }
        
        .file-item {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            margin-bottom: var(--margin-sm);
            display: flex;
            align-items: center;
        }
        
        .file-icon {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: var(--margin-sm);
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-size: var(--font-size-small);
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .file-size {
            font-size: var(--font-size-mini);
            color: var(--text-secondary);
        }
        
        .file-actions {
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .btn-file-action {
            width: 28px;
            height: 28px;
            border-radius: 4px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: var(--font-size-small);
        }
        
        .btn-download {
            background: var(--info-color);
            color: white;
        }
        
        .btn-delete {
            background: var(--error-color);
            color: white;
        }
        
        .submission-history {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .history-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-title {
            display: flex;
            align-items: center;
        }
        
        .history-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .history-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
        }
        
        .history-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-item:active {
            background: var(--bg-color-active);
        }
        
        .history-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .history-version {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .history-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .history-detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--margin-md);
        }
        
        .btn-save {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-submit {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-disabled {
            background: var(--text-disabled);
            color: white;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">论文提交</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="submission-header">
            <div class="header-title">论文提交</div>
            <div class="header-subtitle">提交毕业论文及相关材料</div>
        </div>

        <!-- 提交状态 -->
        <div class="submission-status">
            <div class="status-title">
                <i class="ace-icon fa fa-info-circle"></i>
                <span>提交状态</span>
            </div>

            <div class="current-status" id="currentStatus">
                <!-- 当前状态信息将动态填充 -->
            </div>
        </div>

        <!-- 提交表单 -->
        <div class="submission-form" id="submissionForm">
            <div class="form-title">
                <i class="ace-icon fa fa-upload"></i>
                <span>论文提交</span>
            </div>

            <div class="form-group">
                <div class="form-label">论文标题</div>
                <input type="text" class="form-input" id="thesisTitle" placeholder="请输入论文标题">
            </div>

            <div class="form-group">
                <div class="form-label">论文摘要</div>
                <textarea class="form-input form-textarea" id="thesisAbstract" placeholder="请输入论文摘要"></textarea>
            </div>

            <div class="form-group">
                <div class="form-label">关键词</div>
                <input type="text" class="form-input" id="keywords" placeholder="请输入关键词，用分号分隔">
            </div>

            <div class="form-group">
                <div class="form-label">论文文件</div>
                <div class="file-upload" id="fileUpload" onclick="selectFile('thesis')">
                    <div class="upload-icon">
                        <i class="ace-icon fa fa-cloud-upload"></i>
                    </div>
                    <div class="upload-text">点击上传论文文件</div>
                    <div class="upload-hint">支持PDF、DOC、DOCX格式，大小不超过50MB</div>
                </div>
                <input type="file" id="thesisFile" style="display: none;" accept=".pdf,.doc,.docx">
                <div class="file-list" id="thesisFileList">
                    <!-- 论文文件列表将动态填充 -->
                </div>
            </div>

            <div class="form-group">
                <div class="form-label">附件文件</div>
                <div class="file-upload" id="attachmentUpload" onclick="selectFile('attachment')">
                    <div class="upload-icon">
                        <i class="ace-icon fa fa-paperclip"></i>
                    </div>
                    <div class="upload-text">点击上传附件文件</div>
                    <div class="upload-hint">支持常见文档格式，单个文件不超过20MB</div>
                </div>
                <input type="file" id="attachmentFile" style="display: none;" multiple>
                <div class="file-list" id="attachmentFileList">
                    <!-- 附件文件列表将动态填充 -->
                </div>
            </div>

            <div class="form-group">
                <div class="form-label">提交说明</div>
                <textarea class="form-input form-textarea" id="submitNote" placeholder="请输入提交说明（选填）"></textarea>
            </div>

            <div class="form-actions">
                <button class="btn-mobile btn-save flex-1" onclick="saveDraft();">
                    <i class="ace-icon fa fa-save"></i>
                    <span>保存草稿</span>
                </button>
                <button class="btn-mobile btn-submit flex-1" onclick="submitThesis();">
                    <i class="ace-icon fa fa-paper-plane"></i>
                    <span>正式提交</span>
                </button>
            </div>
        </div>

        <!-- 提交历史 -->
        <div class="submission-history">
            <div class="history-header">
                <div class="history-title">
                    <i class="ace-icon fa fa-history"></i>
                    <span>提交历史</span>
                </div>
                <div class="history-count" id="historyCount">0</div>
            </div>

            <div id="historyItems">
                <!-- 提交历史将动态填充 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSubmission = null;
        let submissionHistory = [];
        let uploadedFiles = {
            thesis: [],
            attachment: []
        };

        $(function() {
            initPage();
            loadSubmissionStatus();
            loadSubmissionHistory();
            bindEvents();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
        }

        // 绑定事件
        function bindEvents() {
            // 文件上传事件
            $('#thesisFile').change(function() {
                handleFileUpload(this, 'thesis');
            });

            $('#attachmentFile').change(function() {
                handleFileUpload(this, 'attachment');
            });

            // 拖拽上传事件
            setupDragAndDrop();
        }

        // 设置拖拽上传
        function setupDragAndDrop() {
            $('.file-upload').on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            $('.file-upload').on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            $('.file-upload').on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');

                const files = e.originalEvent.dataTransfer.files;
                const type = $(this).attr('id') === 'fileUpload' ? 'thesis' : 'attachment';

                for (let file of files) {
                    processFile(file, type);
                }
            });
        }

        // 加载提交状态
        function loadSubmissionStatus() {
            showLoading(true);

            $.ajax({
                url: "/student/thesis/submission/getSubmissionStatus",
                type: "post",
                dataType: "json",
                success: function(data) {
                    currentSubmission = data.submission;
                    renderSubmissionStatus();

                    if (currentSubmission) {
                        fillFormData();
                    }

                    showLoading(false);
                },
                error: function() {
                    showError('加载提交状态失败');
                    showLoading(false);
                }
            });
        }

        // 渲染提交状态
        function renderSubmissionStatus() {
            let statusHtml = '';

            if (currentSubmission) {
                const status = currentSubmission.status || 'draft';
                const statusClass = getStatusClass(status);
                const statusText = getStatusText(status);
                const iconClass = getStatusIconClass(status);

                statusHtml = `
                    <div class="status-icon ${statusClass}">
                        <i class="ace-icon fa ${iconClass}"></i>
                    </div>
                    <div class="status-content">
                        <div class="status-text">${currentSubmission.title || '未命名论文'}</div>
                        <div class="status-meta">
                            提交时间：${formatDate(currentSubmission.submitTime)}<br>
                            最后修改：${formatDate(currentSubmission.updateTime)}<br>
                            版本号：${currentSubmission.version || 'v1.0'}
                        </div>
                    </div>
                    <div class="status-badge badge-${status}">${statusText}</div>
                `;
            } else {
                statusHtml = `
                    <div class="status-icon draft">
                        <i class="ace-icon fa fa-file-text-o"></i>
                    </div>
                    <div class="status-content">
                        <div class="status-text">尚未提交</div>
                        <div class="status-meta">
                            请填写论文信息并上传相关文件后提交
                        </div>
                    </div>
                    <div class="status-badge badge-draft">草稿</div>
                `;
            }

            $('#currentStatus').html(statusHtml);
        }

        // 获取状态样式类
        function getStatusClass(status) {
            return status;
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'draft': return '草稿';
                case 'submitted': return '已提交';
                case 'approved': return '已通过';
                case 'rejected': return '被退回';
                default: return '未知';
            }
        }

        // 获取状态图标类
        function getStatusIconClass(status) {
            switch(status) {
                case 'draft': return 'fa-edit';
                case 'submitted': return 'fa-paper-plane';
                case 'approved': return 'fa-check-circle';
                case 'rejected': return 'fa-times-circle';
                default: return 'fa-file-text';
            }
        }

        // 填充表单数据
        function fillFormData() {
            if (!currentSubmission) return;

            $('#thesisTitle').val(currentSubmission.title || '');
            $('#thesisAbstract').val(currentSubmission.abstract || '');
            $('#keywords').val(currentSubmission.keywords || '');
            $('#submitNote').val(currentSubmission.note || '');

            // 填充文件列表
            if (currentSubmission.files) {
                uploadedFiles.thesis = currentSubmission.files.thesis || [];
                uploadedFiles.attachment = currentSubmission.files.attachment || [];

                renderFileList('thesis');
                renderFileList('attachment');
            }
        }

        // 选择文件
        function selectFile(type) {
            if (type === 'thesis') {
                $('#thesisFile').click();
            } else {
                $('#attachmentFile').click();
            }
        }

        // 处理文件上传
        function handleFileUpload(input, type) {
            const files = input.files;

            for (let file of files) {
                processFile(file, type);
            }

            // 清空input
            input.value = '';
        }

        // 处理文件
        function processFile(file, type) {
            // 验证文件
            if (!validateFile(file, type)) {
                return;
            }

            // 创建文件对象
            const fileObj = {
                id: Date.now() + Math.random(),
                name: file.name,
                size: file.size,
                type: file.type,
                file: file
            };

            // 添加到文件列表
            if (type === 'thesis') {
                // 论文文件只能有一个
                uploadedFiles.thesis = [fileObj];
            } else {
                uploadedFiles.attachment.push(fileObj);
            }

            renderFileList(type);
        }

        // 验证文件
        function validateFile(file, type) {
            if (type === 'thesis') {
                // 论文文件验证
                const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                if (!allowedTypes.includes(file.type)) {
                    showError('论文文件只支持PDF、DOC、DOCX格式');
                    return false;
                }

                if (file.size > 50 * 1024 * 1024) {
                    showError('论文文件大小不能超过50MB');
                    return false;
                }
            } else {
                // 附件文件验证
                if (file.size > 20 * 1024 * 1024) {
                    showError('附件文件大小不能超过20MB');
                    return false;
                }
            }

            return true;
        }

        // 渲染文件列表
        function renderFileList(type) {
            const container = $(`#${type}FileList`);
            const files = uploadedFiles[type];

            container.empty();

            files.forEach(file => {
                const fileHtml = createFileItem(file, type);
                container.append(fileHtml);
            });
        }

        // 创建文件项
        function createFileItem(file, type) {
            return `
                <div class="file-item">
                    <div class="file-icon">
                        <i class="ace-icon fa fa-file"></i>
                    </div>
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                    <div class="file-actions">
                        ${file.url ? `
                            <button class="btn-file-action btn-download" onclick="downloadFile('${file.id}', '${type}');">
                                <i class="ace-icon fa fa-download"></i>
                            </button>
                        ` : ''}
                        <button class="btn-file-action btn-delete" onclick="deleteFile('${file.id}', '${type}');">
                            <i class="ace-icon fa fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 下载文件
        function downloadFile(fileId, type) {
            const file = uploadedFiles[type].find(f => f.id === fileId);
            if (!file || !file.url) return;

            const link = document.createElement('a');
            link.href = file.url;
            link.download = file.name;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 删除文件
        function deleteFile(fileId, type) {
            const index = uploadedFiles[type].findIndex(f => f.id === fileId);
            if (index > -1) {
                uploadedFiles[type].splice(index, 1);
                renderFileList(type);
            }
        }

        // 保存草稿
        function saveDraft() {
            const formData = getFormData();
            formData.status = 'draft';

            if (!validateFormData(formData, false)) {
                return;
            }

            doSaveSubmission(formData, '草稿保存成功');
        }

        // 提交论文
        function submitThesis() {
            const formData = getFormData();
            formData.status = 'submitted';

            if (!validateFormData(formData, true)) {
                return;
            }

            const message = `确定要正式提交论文吗？\n\n论文标题：${formData.title}\n提交后将无法修改，请确认信息无误。`;

            if (typeof urp !== 'undefined' && urp.confirm) {
                urp.confirm(message, function(confirmed) {
                    if (confirmed) {
                        doSaveSubmission(formData, '论文提交成功');
                    }
                });
            } else {
                if (confirm(message)) {
                    doSaveSubmission(formData, '论文提交成功');
                }
            }
        }

        // 获取表单数据
        function getFormData() {
            return {
                title: $('#thesisTitle').val().trim(),
                abstract: $('#thesisAbstract').val().trim(),
                keywords: $('#keywords').val().trim(),
                note: $('#submitNote').val().trim(),
                files: uploadedFiles
            };
        }

        // 验证表单数据
        function validateFormData(formData, isSubmit) {
            if (!formData.title) {
                showError('请输入论文标题');
                return false;
            }

            if (isSubmit) {
                if (!formData.abstract) {
                    showError('请输入论文摘要');
                    return false;
                }

                if (!formData.keywords) {
                    showError('请输入关键词');
                    return false;
                }

                if (uploadedFiles.thesis.length === 0) {
                    showError('请上传论文文件');
                    return false;
                }
            }

            return true;
        }

        // 执行保存提交
        function doSaveSubmission(formData, successMessage) {
            // 这里应该使用FormData来处理文件上传
            const submitData = new FormData();

            submitData.append('title', formData.title);
            submitData.append('abstract', formData.abstract);
            submitData.append('keywords', formData.keywords);
            submitData.append('note', formData.note);
            submitData.append('status', formData.status);

            // 添加论文文件
            uploadedFiles.thesis.forEach((file, index) => {
                if (file.file) {
                    submitData.append(`thesisFile_${index}`, file.file);
                }
            });

            // 添加附件文件
            uploadedFiles.attachment.forEach((file, index) => {
                if (file.file) {
                    submitData.append(`attachmentFile_${index}`, file.file);
                }
            });

            $.ajax({
                url: "/student/thesis/submission/saveSubmission",
                type: "post",
                data: submitData,
                processData: false,
                contentType: false,
                success: function(data) {
                    if (data.success) {
                        showSuccess(successMessage);
                        loadSubmissionStatus(); // 重新加载状态
                        loadSubmissionHistory(); // 重新加载历史
                    } else {
                        showError(data.message || '保存失败');
                    }
                },
                error: function() {
                    showError('网络错误，请重试');
                }
            });
        }

        // 加载提交历史
        function loadSubmissionHistory() {
            $.ajax({
                url: "/student/thesis/submission/getSubmissionHistory",
                type: "post",
                dataType: "json",
                success: function(data) {
                    submissionHistory = data.history || [];
                    renderSubmissionHistory();
                },
                error: function() {
                    console.log('加载提交历史失败');
                }
            });
        }

        // 渲染提交历史
        function renderSubmissionHistory() {
            $('#historyCount').text(submissionHistory.length);

            const container = $('#historyItems');
            container.empty();

            if (submissionHistory.length === 0) {
                container.html(`
                    <div style="padding: 40px; text-align: center; color: var(--text-secondary);">
                        暂无提交记录
                    </div>
                `);
                return;
            }

            submissionHistory.forEach(item => {
                const historyHtml = createHistoryItem(item);
                container.append(historyHtml);
            });
        }

        // 创建历史项
        function createHistoryItem(item) {
            const status = item.status || 'draft';
            const statusClass = getStatusClass(status);
            const statusText = getStatusText(status);

            return `
                <div class="history-item" onclick="showHistoryDetail('${item.id}')">
                    <div class="history-basic">
                        <div class="history-version">${item.version || 'v1.0'} - ${item.title}</div>
                        <div class="history-status badge-${status}">${statusText}</div>
                    </div>
                    <div class="history-details">
                        <div class="history-detail-item">
                            <span>提交时间:</span>
                            <span>${formatDate(item.submitTime)}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>文件数量:</span>
                            <span>${item.fileCount || 0}个</span>
                        </div>
                        <div class="history-detail-item">
                            <span>状态:</span>
                            <span>${statusText}</span>
                        </div>
                        <div class="history-detail-item">
                            <span>审核时间:</span>
                            <span>${formatDate(item.reviewTime)}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示历史详情
        function showHistoryDetail(historyId) {
            const item = submissionHistory.find(h => h.id === historyId);
            if (!item) return;

            let message = `提交详情\n\n`;
            message += `版本：${item.version || 'v1.0'}\n`;
            message += `标题：${item.title}\n`;
            message += `状态：${getStatusText(item.status)}\n`;
            message += `提交时间：${formatDate(item.submitTime)}\n`;

            if (item.reviewTime) {
                message += `审核时间：${formatDate(item.reviewTime)}\n`;
            }

            if (item.reviewComment) {
                message += `审核意见：${item.reviewComment}\n`;
            }

            if (item.note) {
                message += `提交说明：${item.note}\n`;
            }

            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }

        // 刷新数据
        function refreshData() {
            loadSubmissionStatus();
            loadSubmissionHistory();
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                $('#loadingState').show();
            } else {
                $('#loadingState').hide();
            }
        }

        // 显示错误信息
        function showError(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 显示成功信息
        function showSuccess(message) {
            if (typeof urp !== 'undefined' && urp.alert) {
                urp.alert(message);
            } else {
                alert(message);
            }
        }

        // 调整页面高度
        function adjustPageHeight() {
            const windowHeight = $(window).height();
            const navbarHeight = $('.navbar-mobile').outerHeight();
            const containerHeight = windowHeight - navbarHeight;
            $('.page-mobile').css('min-height', containerHeight + 'px');
        }

        // 窗口大小改变时调整布局
        $(window).resize(function() {
            adjustPageHeight();
        });
    </script>
</body>
</html>
