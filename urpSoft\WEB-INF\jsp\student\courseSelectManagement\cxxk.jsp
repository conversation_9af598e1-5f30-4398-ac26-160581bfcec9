<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="pager" uri="http://www.urpSoft.com/pagination"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
	<head>
		<style type="text/css">
			.table-bordered, td, th {
			    border-radius: 0 !important;
			}
			
			.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
			    border: 1px solid #ddd;
			}
			.table {
			    width: 100%;
			    max-width: 100%;
			    margin-bottom: 20px;
			}
			pre code, table {
			    background-color: transparent;
			}
			table {
			    border-collapse: collapse;
			    border-spacing: 0;
			}
			.table>thead>tr {
			    color: #707070;
			    font-weight: 400;
			    background: repeat-x #F2F2F2;
			    background-image: -webkit-linear-gradient(top, #F8F8F8 0, #ECECEC 100%);
			    background-image: -o-linear-gradient(top,#F8F8F8 0,#ECECEC 100%);
			    background-image: linear-gradient(to bottom, #F8F8F8 0, #ECECEC 100%);
			    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff8f8f8', endColorstr='#ffececec', GradientType=0);
			}
			.table.table-bordered>thead>tr>th:first-child {
			    border-left-color: #ddd;
			}
			
			.table>caption+thead>tr:first-child>td, .table>caption+thead>tr:first-child>th, .table>colgroup+thead>tr:first-child>td, .table>colgroup+thead>tr:first-child>th, .table>thead:first-child>tr:first-child>td, .table>thead:first-child>tr:first-child>th {
			    border-top: 0;
			}
			.table.table-bordered>thead>tr>th {
			    vertical-align: middle;
			}
			.table>thead>tr>th:first-child {
			    border-left-color: #F1F1F1;
			}
			.table>thead>tr>th {
			    border-color: #ddd;
			    font-weight: 700;
			}
			.table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
			    border-bottom-width: 2px;
			}
			.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
			    border: 1px solid #ddd;
			}
			.table>thead>tr>th {
			    vertical-align: bottom;
			    border-bottom: 2px solid #ddd;
			}
			.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
			    padding: 8px;
			    line-height: 1.42857143;
			    vertical-align: top;
			    border-top: 1px solid #ddd;
			}
			.table-bordered, td, th {
			    border-radius: 0 !important;
			}
			caption, th {
			    text-align: left;
			}
			.table-striped>tbody>tr:nth-of-type(odd) {
			    background-color: #f9f9f9;
			}
		</style>
		<script src="/js/jQuery/jquery-3.4.1.min.js"></script>
		<script type="text/javascript" src="/assets/layer/layer.js"></script>
		<script type="text/javascript">
			function Urp() {
			};
			
			Urp.prototype = {
				"alert": function (msg, callback) {
					layer.msg(msg);
					if (typeof callback === 'function') {
						setTimeout(callback, 1000);
					}
				},
				"fixedheader": function (tableDivId, divHeight) {
					var h = (divHeight == undefined || divHeight == null) ? '\'450\'' : divHeight;
		
					if ($("#" + tableDivId).length > 0) {
						$("#" + tableDivId).addClass('table-cont');
						$("#" + tableDivId + " table").css({"margin-top": "-2px", "display": "table", "border-collapse": "separate"});
						//$("#"+tableDivId).css({"max-height":h,"overflow":"auto"});
						var tableCont = document.querySelector('#' + tableDivId);
		
						function scrollHandle(e) {
							var scrollTop = this.scrollTop - 1;
							this.querySelector('thead').style.transform = 'translateY(' + scrollTop + 'px)';
						}
		
						tableCont.addEventListener('scroll', scrollHandle);
					}
				},
				"confirm": function (msg, callback) {
					layer.confirm(
						msg,
						{btn: ['确定', '取消'], closeBtn: 0, title: "提示信息"},
						function () {
							if (typeof callback === 'function') {
								layer.closeAll('dialog');
								callback(true);
							}
						},
						function () {
							if (typeof callback === 'function') {
								callback(false);
							}
						}
					);
				}
			};
			urp = new Urp();
		
			var yxkc = "";
			var xxbm = $(parent.document).find("#xxbm").val();
			var xkjdlx = $(parent.document).find("#xkjdlx").val();
			var weekZw = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"];
			
			function queryList(){
				$.ajax({
					url:"/student/courseSelect/relearnCourse/courseList",
					method:"post",
					dataType : "json",
					data : 'fajhh='+$("#fajhh").val(),
					success:function(data){
						yxkc = eval("(" + data["yxkclist"] + ")");
						fillfakcbody(data);
						if("${byscxpdct}" != "1" && "${cxpctfs}" == "any"){
							dealxtsjkc1();
						}
						disabledMainClass();
					},
					error:function(){
						urp.alert("查询课程失败，请稍后再试...");
					}
				});
			}
			
			//校验周次有无冲突
			function checkZC(a,b){
				var ff = false;
				if(a!=undefined && a!="" && b!=undefined && b!="" 
					&& Math.abs(parseInt(a, 2)&parseInt(b, 2))) {
					ff = true;
				}
				
				return ff;
			}
			
			function queryZjJsJl(id, obj) {
				if (id != undefined && $(obj).attr("title") == undefined) {
					$(obj).css("cursor", "wait");
					$.ajax({
						url: "/student/courseSelect/queryTeacherJL",
						method: "post",
						data: {id: id },
						dataType: "json",
						success: function(d) {
							var cont = "负责教师：" + d[0][0] + "\r\n";
							$(obj).attr("title", cont + (d[0][1]==null? "": d[0][1]));
						},
						error: function() {
						},
						complete : function(){
							$(obj).css("cursor", "pointer");
						}
					});
				}
			}
			
			function leaveJs(obj) {
				$(obj).css("cursor", "pointer");
			}
			
			function fillfakcbody(d){
				var rwCxxkList = eval("("+d["rwCxxkList"]+")");
				var kylMap = d["kylMap"];
				$("#cxxkbody").html("");
				//方案课程列表
				if(rwCxxkList.length>0){
					for(var i=0;i<rwCxxkList.length;i++) {
						var cxkccont = "";
						var rwsjdd = rwCxxkList[i].sjdd;
						var rown = rwsjdd.length;
						
						var isShow = true;
						if(xxbm == "100010" && xkjdlx == "005") {
							isShow = false;
							if(rwCxxkList[i].kcsxdm == "001") {
								isShow = true;
							}
						}
						if(xxbm == "100018" && rwCxxkList[i].kcsxdm == "003") {
							isShow = false;
						}
						
						if(isShow) {
							cxkccont += "<tr><td rowspan='"+rown+"'>";
							var bkskyl = kylMap[rwCxxkList[i].zxjxjhh+"_"+rwCxxkList[i].kch+"_"+rwCxxkList[i].kxh];
							
							cxkccont += "<lable"; 
							if(d["pkyl"]== "1" && bkskyl<=0) {
								cxkccont += " style='display:none;' ";
							}
							cxkccont += "><input type='checkbox' \
								id='"+rwCxxkList[i].kch+"_"+rwCxxkList[i].kxh+"_"+rwCxxkList[i].zxjxjhh+"_"
									+rwCxxkList[i].tdkch+"_"+rwCxxkList[i].cxfsdm+"' \
								class='ace ace-checkbox-2' name='kcId' value='"+JSON.stringify(rwCxxkList[i])+"' \
								onclick='setstate(this)'/><span class='lbl'></span></lable></td>";
							<c:if test="${xxbm == '100008'}">
								cxkccont += "<td><label><input type='radio' name='sfzxcx' value='ZX' class='ace'/><span class='lbl'>是</span></label> <label><input type='radio' name='sfzxcx' value='' class='ace'/><span class='lbl'>否</span></label></td>";
							</c:if>
							cxkccont += "<td rowspan='"+rown+"'>"+rwCxxkList[i].kcm+ (rwCxxkList[i].bz!="" ? "<"+rwCxxkList[i].bz+">" : "") +"("+rwCxxkList[i].kch+"_"+rwCxxkList[i].kxh +") </td>";
							cxkccont += "<td rowspan='"+rown+"'>"
								+(rwCxxkList[i].tdkch== null || rwCxxkList[i].tdkch == "" ? "无" : (rwCxxkList[i].tdkcm +"(" + rwCxxkList[i].tdkch + ")"))+"</td>";
							cxkccont += "<td rowspan='"+rown+"'>"+rwCxxkList[i].xf+"</td>";
							cxkccont += "<td rowspan='"+rown+"'>"+rwCxxkList[i].kcsxmc+"</td>";
							if("${xxbm}" == "100006") {
								cxkccont += "<td rowspan='"+rown+"'>"+rwCxxkList[i].kclbmc2+"</td>";
							}
							cxkccont += "<td rowspan='"+rown+"'>"+rwCxxkList[i].kslxmc+"</td>";
							cxkccont += "<td onmouseenter='queryZjJsJl(\""+rwCxxkList[i].zxjxjhh+"_"+rwCxxkList[i].kch+"_"+rwCxxkList[i].kxh+"\", this);'\
								 onmouseleave='leaveJs(this);' style='color:#579EC8;cursor:pointer;' rowspan='"+rown+"'>"+rwCxxkList[i].skjs+"</td>";
							cxkccont += "<td rowspan='"+rown+"'>"+bkskyl+"/<a title='点击查看' href='javascript:void(0);' \
								onclick='viewXkCount(\""+rwCxxkList[i].zxjxjhh+"\",\""+rwCxxkList[i].kch+"\",\""+rwCxxkList[i].kxh+"\", this);'>点击查看</a></td>";
							cxkccont += "<td rowspan='"+rown+"'>"+rwCxxkList[i].xkmssm+"</td>";
							cxkccont += "<td rowspan='"+rown+"'>"+rwCxxkList[i].xkkzsm+"</td>";
							var xkxzsm = rwCxxkList[i].xkxzsm;
							if(rwCxxkList[i].xkxzsm.length>8){
								xkxzsm = "<span style='color:#579EC8;cursor:pointer;' title='"+rwCxxkList[i].xkxzsm+"'>"+rwCxxkList[i].xkxzsm.substring(0,9)+"...</span>";
							}
							cxkccont += "<td rowspan='"+rown+"'>"+xkxzsm+"</td>";
							cxkccont += "<td rowspan='"+rown+"'>"+(rwCxxkList[i].yxkxqxk=="1" ? "是" : "否")+"</td>";
							for(var j=0;j<rown;j++){
								var sjdd = rwsjdd[j];
								if(j>0){
									cxkccont += "<tr>";
								}
								cxkccont += "<td>" + (sjdd.zcsm=="" ? "无" : sjdd.zcsm) + " >> " + 
									(sjdd.skxq=="" ? "无" : weekZw[parseInt(sjdd.skxq)-1]) + " >> " + 
									(sjdd.skjc=="" ? "无" : (sjdd.skjc+(sjdd.cxjc=="" || sjdd.cxjc==null || sjdd.cxjc == "1" ? "" 
																:  "~"+(parseInt(sjdd.cxjc)+parseInt(sjdd.skjc)-1)) + "节")) + "</td>";
								cxkccont += "<td>" + (sjdd.xqm=="" ? "无" : sjdd.xqm) + " >> " + 
									(sjdd.jxlm=="" ? "无" : sjdd.jxlm) + " >> " + 
									(sjdd.jasm=="" ? "无" : sjdd.jasm) + "</td></tr>";
							}
						}
						if(cxkccont != "") {
							if(rwCxxkList[i].zkxh == "") {
								$("#cxxkbody").append(cxkccont);
							} else {
								var zktId = rwCxxkList[i].kch+"_"+rwCxxkList[i].zkxh+"_"+rwCxxkList[i].zxjxjhh;
								var fsTab = zktId + "_fsTab";
								if($("#" + fsTab).length == 0) {
									var pTr = $("[id^=" + zktId + "]").parents("tr");
									var rowspan = $("[id^=" + zktId + "]").parents("td").prop("rowspan");
									
									$("#cxxkbody").children("tr:eq("+ (pTr.index() + rowspan - 1) +")").after("<tr>\
										<td style='border-right: 0; background-color: white;'></td>\
										<td style='border-left: 0; background-color: white;' colspan='"+ ($("#cxxk_tab thead:eq(0) th").length-1) +"'>\
											<table id='"+ fsTab +"' class='table table-hover table-bordered table-striped'>\
												<thead>\
													"+ $("#cxxk_tab thead:eq(0)").html() +" \
												</thead>\
												<tbody>\
												</tbody>\
											</table>\
										</td>\
									</tr>");
									$("#" + fsTab + " th").css("cssText", "background: #bdf2fb !important");
								}
								$("#" + fsTab + " tbody").append(cxkccont);
							}
						}
					}
					
					$("#cxxk_tab").prepend($("#cxxkbody"));
				}
				if($("#cxxkbody tr").length == 0) {
					$("#cxxkbody").html("<tr><td colspan='"+ $("#cxxk_tab thead:eq(0) th").length +"' style='color:red;'>未查询到记录！</td></tr>");
				}
			}
			
			//提交选课
			function xuanze() {
				var idarr = new Array();
				var kcarr = new Array();
				var objs = $("input[name=kcId]:checked");
				
				let pass = true;
				$.each(objs,function(i,v){
					let zx = $(this).parents("tr").find("input[name='sfzxcx']:checked");
					if(xxbm == "100008") {
						if(zx.length == 0) {
							urp.alert("请维护是否自学重修");
							pass = false;
							return false;
						}
					}
					var tval = eval("("+$(this).val()+")");
					var tid = $(this).attr("id");
					if(xxbm == "100008") {
						tid = tid.substring(0, tid.lastIndexOf("_") + 1) + zx.val();
					}
					var isExist = false;
					for(var i=0; i<idarr.length; i++) {
						if(idarr[i]==tid){
							isExist = true;
							break;
						}
					}
					if(!isExist){
						idarr[idarr.length] = tid;
						kcarr[kcarr.length] = tval.kcm + "_" + tval.kxh;
					}
				});
				if(!pass) {
					return "err";
				}
				
				$("#kcIds").val(idarr.join(","));
				var xx = kcarr.join(",");
				var xxa = "";
				for(var i=0; i<xx.length; i++) {
					xxa += xx.charCodeAt(i) + ",";
				}
				$("#kcms").val(xxa);
			}
			
			//重置选课
			function jhqx(){
				$("input[name=kcId]").prop("disabled",false);
				$("input[name=kcId]").prop("checked",false);
			}
			
			function setstate(atom) {
				var ids = atom.id.split("_");
				if($(atom).is(":checked")) {
					$("input[name=kcId][id^="+ids[0]+"_]").not(atom).prop("disabled",true);
				} else {
					$("input[name=kcId][id^="+ids[0]+"_]").not(atom).prop("disabled",false);
				}
				
				var objs = $("input[name=kcId]:checked");
				if("${byscxpdct}" != "1" && "${cxpctfs}" == "any") {
					dealxtsjkc1(objs);
				}
				disabledMainClass(objs);
			}
			
			//处理选课
			function dealkc(atom){
				//同课程不可选,设置时间筛选器状态
				var objs = $("input[name=kcId]:checked");
				
// 				if(xxbm == "100010" && xkjdlx == "005") {
// 	    			if(atom.checked) {
// 				   		var flag = checkCt(objs.not(atom), atom);
// 				   		if(flag == "") {
// 				   			dealxtsjkc1(objs);
// 				   		} else if(flag == "02") {
// 				   			urp.alert("已选非必修课程，不允许选此课程！");
// 				   		} else {
// 		    				urp.confirm("您选择的课程与已选必修课程<br>"+flag+
// 		    					"<br>时间有冲突<span style='color: red;'>（继续选课需承担相应后果）</span>，<br>是否继续？", 
// 		    				function(f) {
// 		    					if(f) {
// 		    						dealxtsjkc1(objs);
// 		    					} else {
// 		    						$(atom).attr("checked", false);
// 		    						var ids = atom.id.split("_");
// 		    						$("input[name=kcId][id^="+ids[0]+"_]").prop("disabled",false);
// 		    					}
// 		    				});
// 				   		}
// 	    			} else {
// 	    				dealxtsjkc1(objs);
// 	    			}
	    			
// 			   	} else {
				   	dealxtsjkc1(objs);
// 			   	}
			}
			
			function disabledMainClass(bxkc) {
				var allkc = $("input[type=checkbox][name=kcId]");
				for(var i=0;i<allkc.length;i++){
					var tempallkcdata = eval("("+allkc[i].value+")");
					var zkxh = tempallkcdata.zkxh;
 					if(zkxh) {
 						$("input[id^='"+ tempallkcdata.kch + "_" + zkxh + "_" + tempallkcdata.zxjxjhh +"']").prop("disabled", true);
 						$("input[id^='"+ tempallkcdata.kch + "_" + zkxh + "_" + tempallkcdata.zxjxjhh +"']").parent().attr("title", "选择附属课堂会连带选择主课堂");
 					}
 					
 					if(tempallkcdata.zkch !="" && bxkc!=undefined && bxkc.length>0){
       					for(var j=0;j<bxkc.length;j++){
       						var tempyxkcdata = eval("("+bxkc[j].value+")");
       						if(tempallkcdata.zkch==tempyxkcdata.zkch){
       							$(allkc[i]).prop("disabled",true);
       						}
       					}
       				}
				}
			}
			
			//选中一门课程时处理其他时间冲突课程
           function dealxtsjkc(bxkc){
       			var allkc = $("input[type=checkbox][name=kcId]").not(bxkc);
       			$(allkc).prop("disabled",false);
       			$(allkc).prop("checked",false);
       			if(allkc!=undefined && allkc.length>0){
       				for(var i=0;i<allkc.length;i++){
   						var tempallkcdata = eval("("+allkc[i].value+")");
		           		var cxxkpdctf = tempallkcdata.cxxkpdctf;
           				if(yxkc!=undefined && yxkc.length>0) {
           					nextkc1:
           					for(var j=0; j<yxkc.length; j++){
           						tempyxkcdata = yxkc[j];
           						var cxxkpdctf1 = tempyxkcdata.cxxkpdctf;
          						if ((cxxkpdctf != null && cxxkpdctf == "1") || (cxxkpdctf1 != null && cxxkpdctf1 == "1")) {
	       							var yxkcsj = tempyxkcdata.timeAndPlaceList;
	       							var allkcsj = tempallkcdata.sjdd;
	       							for(var a=0; a<yxkcsj.length; a++){
	       								for(var b=0; b<allkcsj.length; b++){
	       									if(checkZC(allkcsj[b].skzc,yxkcsj[a].classWeek)){
			           							for(var x=0; x<parseInt(allkcsj[b].cxjc); x++){
			           								for(var y=0; y<parseInt(yxkcsj[a].continuingSession); y++){
			           									if(yxkcsj[a].classDay == allkcsj[b].skxq 
			           										&& (parseInt(yxkcsj[a].classSessions)+y) == (parseInt(allkcsj[b].skjc)+x)){
			           										$(allkc[i]).prop("disabled",true);
			           										break nextkc1;
			           									}
			           								}
			           							}
	       									}
	       								}
	       							}
	       						}
           					}
           				}
		           		
		           		if(bxkc!=undefined && bxkc.length>0){
			         		$(bxkc).prop("checked",true);
           					nextkc:
           					for(var j=0;j<bxkc.length;j++){
           						var tempyxkcdata = eval("("+bxkc[j].value+")");
           						if(tempallkcdata.kch==tempyxkcdata.kch){
           							$(allkc[i]).prop("disabled",true);
           							break nextkc;
           						}else{
           							var cxxkpdctf1 = tempyxkcdata.cxxkpdctf;
          							if ((cxxkpdctf != null && cxxkpdctf == "1") || (cxxkpdctf1 != null && cxxkpdctf1 == "1")) {
	           							var yxkcsj = tempyxkcdata.sjdd;
	           							var allkcsj = tempallkcdata.sjdd;
	           							for(var a=0;a<yxkcsj.length;a++){
	           								for(var b=0;b<allkcsj.length;b++){
	           									if(checkZC(allkcsj[b].skzc,yxkcsj[a].skzc)){
			             							for(var x=0;x<parseInt(allkcsj[b].cxjc);x++){
			             								for(var y=0;y<parseInt(yxkcsj[a].cxjc);y++){
			             									if(yxkcsj[a].skxq==allkcsj[b].skxq && (parseInt(yxkcsj[a].skjc)+y) == (parseInt(allkcsj[b].skjc)+x)){
			             										$(allkc[i]).prop("disabled",true);
			             										break nextkc;
			             									}
			             								}
			             							}
	           									}
	           								}
	           							}
	           						}
           						}
           					}
           				}
           				
           			}
           		}
           	}
			
			//选中一门课程时处理其他时间冲突课程
           function dealxtsjkc1(bxkc){
       			var allkc = $("input[type=checkbox][name=kcId]").not(bxkc);
       			$(allkc).prop("disabled",false);
       			$(allkc).prop("checked",false);
       			if(allkc!=undefined && allkc.length>0){
       				for(var i=0;i<allkc.length;i++){
      					var tempallkcdata = eval("("+allkc[i].value+")");
		           		if(bxkc!=undefined && bxkc.length>0){
			         		$(bxkc).prop("checked",true);
           					nextkc:
           					for(var j=0;j<bxkc.length;j++){
           						var tempyxkcdata = eval("("+bxkc[j].value+")");
           						if(tempallkcdata.kch==tempyxkcdata.kch){
           							$(allkc[i]).prop("disabled",true);
           							break nextkc;
           						}
           					}
           				}
           			}
           		}
           	}
           	
           	function checkCt(bxkc, atom) {
				var tempkcdata = eval("("+atom.value+")");
				var allkcsj = tempkcdata.sjdd;
				
				var flag = "";
				
           		if(yxkc!=undefined && yxkc.length>0 && atom != undefined){
  					for(var j=0; j<yxkc.length; j++){
  						var tempyxkcdata = yxkc[j];
  						
						var yxkcsj = tempyxkcdata.timeAndPlaceList;
						
						for(var a=0; a<yxkcsj.length; a++){
							for(var b=0; b<allkcsj.length; b++){
								if(checkZC(allkcsj[b].skzc,yxkcsj[a].classWeek)){
		  							for(var x=0; x<parseInt(allkcsj[b].cxjc); x++){
		  								for(var y=0; y<parseInt(yxkcsj[a].continuingSession); y++){
		  									if(yxkcsj[a].classDay == allkcsj[b].skxq 
		  										&& (parseInt(yxkcsj[a].classSessions)+y) == (parseInt(allkcsj[b].skjc)+x)){
		  										if(tempyxkcdata.coursePropertiesCode == "001") {
		  											flag = "01" + tempyxkcdata.id.coureNumber + tempyxkcdata.courseName;
			  									} else {
			  										$(atom).click();
		  											$(atom).remove();
		  											flag = "02";
		  											return flag;
			  									}
			  								}
			  							}
									}
								}
							}
						}
   					}
           		}
				
           		if(bxkc!=undefined && bxkc.length>0 && atom != undefined){
					
           			for(var j=0;j<bxkc.length;j++){
  						var tempyxkcdata = eval("("+bxkc[j].value+")");
  						
						var yxkcsj = tempyxkcdata.sjdd;
						for(var a=0;a<yxkcsj.length;a++) {
							for(var b=0;b<allkcsj.length;b++){
								if(checkZC(allkcsj[b].skzc,yxkcsj[a].skzc)){
		 							for(var x=0;x<parseInt(allkcsj[b].cxjc);x++){
		 								for(var y=0;y<parseInt(yxkcsj[a].cxjc);y++){
		 									if(yxkcsj[a].skxq==allkcsj[b].skxq 
		 										&& (parseInt(yxkcsj[a].skjc)+y) == (parseInt(allkcsj[b].skjc)+x)){
		 										if(tempyxkcdata.kcsxdm == "001") {
		 											flag = "01" + tempyxkcdata.kch + tempyxkcdata.kcm;
			 									} else {
			 										$(atom).click();
		  											$(atom).remove();
		  											flag = "02";
		  											return flag;
			 									}
			 								}
			 							}
									}
								}
							}
  						}
					}
				}
				
				return flag;
           	}
           	
           	function resizediv(){
				var iframe = window.parent.document.getElementById("ifra");
				$("#div_kc_tj").css("height", $(iframe).height());
			}
			
			$(function(){
				queryList();
				resizediv();
				urp.fixedheader("div_kc_tj");
			});
			
			function viewXkCount(zxjxjhh, kch, kxh, obj){
				 $.ajax({
	                url: "/student/courseSelect/selectCourse/viewXkCount/"+zxjxjhh+"/"+kch+"/"+kxh,
	                cache: false,
	                type: "post",
	                data: "",
	                dataType: "json",
	                beforeSend: function () {
	                },
	                complete: function () {
	                },
	                success: function (d) {
		                $(obj).html(d);
		                $(obj).attr("title", "点击更新");
	                },
	                error: function (xhr) {
	                    urp.alert("错误代码[" + xhr.readyState + "-" + xhr.status + "]:获取数据失败！");
	                }
				});
			}
		</script>
		<style>
			body{
				background-color:white;
			}
			td, th {
				padding: 4px !important;
			}
			th {
	            white-space: nowrap;
	            border-top:3px solid #ddd !important;
	            background-color:transparent !important;
	        }
		</style>
	</head>
	<body>
		<div class="col-xs-12" id="div_kc_tj" style="overFlow: auto;padding:0;">
			<form action="/student/courseSelect/selectCourses/waitingfor" name="frm" method="POST" target="_parent">
				<input type="hidden" name="dealType" value="6">
				<input type="hidden" id='pxcslx' value="${pxcslx}"/>
				<input type="hidden" id='pxcszdm' value="${pxcszdm}"/>
				<input type="hidden" id='fajhh' name="fajhh" value="${fajhh}"/>
				<input type="hidden" name="kcIds" id="kcIds" value="">
				<input type="hidden" name="kcms" id="kcms" value="">
				<table id="cxxk_tab" class="table table-hover table-bordered table-striped">
					<thead>
						<tr>
							<th>选择</th>
							<c:if test="${xxbm == '100008'}">
							<th>是否自学重修</th>
							</c:if>
							<th>课程</th>
							<th>被替代课程</th>
							<th>学分</th>
							<th>课程属性</th>
							<c:if test="${xxbm == '100006'}">
							<th>课程类别2</th>
							</c:if>
							<th>考试类型</th>
							<th>教师</th>
							<th>课余量/已选人数</th>
							<th>选课模式</th>
							<th>选课控制</th>
							<th>选课限制说明</th>
							<th>允许跨校区选课</th>
							<th>上课时间</th>
							<th>上课地点</th>
						</tr>
					</thead>
					<tbody id="cxxkbody">
						<tr>
							<td colspan="13">
								正在查询重修列表，请稍后
								<img src='/img/icon/pageloading.gif' style='width:28px;height:28px;'/>
							</td>
						</tr>
					</tbody>
				</table>
			</form>
		</div>
	</body>
</html>
